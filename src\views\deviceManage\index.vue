<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <div class="monitor_flex">
        <locateSel
          class="flex_1"
          ref="locateRef"
          @getPosition="getPosition"
          :maxHeight="posHeight"
          @handleQuery="handleQuery"
        />
        <!-- <div class="monitor-position flex_1">
          <div class="monitor-title">安装位置筛选</div>
          <el-scrollbar :max-height="500">
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              show-checkbox
              node-key="id"
              expand-on-click-node
              default-expand-all
              :props="positionProps"
              @check="treeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div> -->

        <div class="flex_2" ref="flexRightBox">
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            @submit.native.prevent
            style="width: 100%"
          >
            <el-form-item prop="deviceCodeAndName">
              <el-input
                v-model="queryParams.deviceCodeAndName"
                placeholder="请输入设备编码/名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="typeId">
              <el-select
                v-model="queryParams.typeId"
                placeholder="请选择设备类型"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="tagId">
              <el-select
                v-model="queryParams.tagId"
                placeholder="请选择设备标签"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tag"
                  :value="item.tagId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="deviceStatus">
              <el-select
                v-model="queryParams.deviceStatus"
                placeholder="请选择设备状态"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="time">
              <el-date-picker
                v-model="queryParams.time"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="入库开始时间"
                end-placeholder="入库结束时间"
                @change="handleQuery"
                @clear="handleQuery"
                style="width: 245px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12 btn-list">
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button type="primary" plain icon="Plus" @click="handleAdd"
                >添加设备</el-button
              >
            </el-col>
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleDelete"
                >批量删除</el-button
              >
            </el-col>
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="warning"
                plain
                icon="Setting"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleRepair"
                >批量报障</el-button
              >
            </el-col>
            <el-col :span="1.5" v-if="!isExternalPark">
              <div style="display: flex; align-items: center">
                <el-upload
                  :action="uploadFileUrl"
                  :headers="headers"
                  :show-file-list="false"
                  :before-upload="beforeUpload"
                  :on-success="handleUploadSuccess"
                >
                  <el-button type="success" plain icon="Download">
                    一键导入
                  </el-button>
                </el-upload>
                <QuestionFilled
                  style="
                    width: 20px;
                    height: 20px;
                    margin-left: 5px;
                    cursor: pointer;
                  "
                  @click="showExport = true"
                />
              </div>
            </el-col>
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="warning"
                plain
                icon="Upload"
                @click="handleExport"
                >一键导出</el-button
              >
            </el-col>
            <el-col
              :span="1.5"
              v-if="!isExternalPark && !useUserStore().isCommon"
            >
              <el-button
                type="success"
                plain
                icon="Download"
                @click="open2 = true"
                >生成设备激活码</el-button
              >
            </el-col>

            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="success"
                plain
                icon="Download"
                @click="handlePrintCode"
                style="background-color: #7728f5; color: white"
                >一键打印设备二维码</el-button
              >
            </el-col>
          </el-row>

          <div>
            <el-table
              ref="ledgerTable"
              v-loading="loading"
              :data="tableData"
              border
              highlight-current-row
              @row-click="rowClick"
              @selection-change="selectionChange"
              @select="onTableSelect"
              @select-all="selectSingleTableAll"
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                width="55"
                align="center"
                fixed="left"
              />
              <el-table-column
                label="设备编码"
                align="center"
                min-width="120px"
                prop="deviceCode"
                show-overflow-tooltip
              />
              <el-table-column
                label="设备名称"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="deviceNameStr"
              />
              <el-table-column
                label="设备类型"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="deviceTypeStr"
              />
              <el-table-column
                label="设备标签"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="tagName"
              />
              <el-table-column
                label="设备能否正常使用"
                align="center"
                min-width="130px"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.isNormal == 1 ? "不能正常使用" : "能正常使用" }}
                </template>
              </el-table-column>
              <el-table-column
                label="设备状态"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
              >
                <template #default="scope">
                  <el-tag
                    v-if="statusObj[scope.row.deviceStatus]"
                    effect="dark"
                    :type="statusObj[scope.row.deviceStatus].type"
                    >{{ statusObj[scope.row.deviceStatus].label }}</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="安装位置"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="installAddress"
              >
                <template #default="{ row }">
                  {{ row.installAddress || "-" }}
                </template>
              </el-table-column>
              <el-table-column
                label="资产端口类别"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="portTypeNameStr"
              />
              <el-table-column
                label="信息资产类别"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="assetsTypeNameStr"
              />
              <el-table-column
                label="信息资产等级"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="levelStr"
              />
              <el-table-column
                label="品牌"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="brandStr"
              />
              <el-table-column
                label="规格型号"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="modelStr"
              />
              <el-table-column
                label="入库时间"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="putTime"
              >
                <template #header>
                  <div
                    style="padding-left: 5px; cursor: pointer"
                    @click="handleSortDate"
                  >
                    入库时间
                    <span class="caret-wrapper">
                      <i
                        class="sort-caret ascending"
                        :class="queryParams.sort == 'asc' ? 'active' : ''"
                      ></i>
                      <i
                        class="sort-caret descending"
                        :class="queryParams.sort == 'desc' ? 'active' : ''"
                      ></i>
                    </span>
                  </div> </template
              ></el-table-column>
              <el-table-column
                label="操作"
                :min-width="280"
                align="center"
                fixed="right"
                class-name="small-padding fixed-width"
              >
                <template #default="scope">
                  <template v-if="!isExternalPark">
                    <el-button
                      link
                      type="primary"
                      icon="Search"
                      @click.stop="handleCheck(scope.row, 0)"
                      >详情</el-button
                    >
                    <el-button
                      link
                      type="warning"
                      icon="Edit"
                      v-if="scope.row.deviceStatus < 3"
                      @click.stop="handleCheck(scope.row, 1)"
                      >编辑</el-button
                    >
                    <el-button
                      link
                      type="danger"
                      icon="Delete"
                      @click.stop="handleDelete(scope.row)"
                      >删除</el-button
                    >
                    <el-button
                      v-if="scope.row.deviceStatus < 3"
                      link
                      type="success"
                      icon="Setting"
                      @click.stop="handleRepair(scope.row)"
                      >报障</el-button
                    >
                  </template>
                  <template v-else>
                    <span>-</span>
                  </template>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.current"
              v-model:limit="queryParams.size"
              auto-scroll
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 填写报障单 -->
    <el-dialog
      class="custom-dialog"
      title="报障单填写"
      v-model="showRepair"
      width="1000px"
      align-center
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-table
        :data="repairList"
        border
        style="margin: 0 auto 40px; width: 96%"
        max-height="300"
      >
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
          show-overflow-tooltip
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="modelStr"
        />
        <el-table-column label="设备图片" align="center" minWidth="120px">
          <template #default="scope">
            <el-image
              v-if="!!scope.row.deviceImg"
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="cover"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="installAddress"
        />
        <el-table-column
          label="物理地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="macAddress"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="logicAddress"
        />
        <el-table-column
          label="IP地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="ipAddress"
        />
        <el-table-column
          label="操作系统"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="osVersion"
        />
        <el-table-column
          label="CPU"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="cpu"
        />
        <el-table-column
          label="品牌"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="brand"
        />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="internalStorage"
        />
        <el-table-column
          label="硬盘"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="disk"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="putTime"
        />
      </el-table>

      <el-form
        ref="repairRef"
        :model="repairForm"
        :rules="repairRules"
        label-width="110px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="报障人" prop="userId">
              <el-select
                v-model="repairForm.userId"
                placeholder="请选择报障人"
                filterable
                clearable
                style="width: 100%"
                @change="repairUserChange"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="报障人联系方式"
              prop="repairPhone"
              label-width="140"
            >
              <el-input
                v-model="repairForm.repairPhone"
                placeholder="请输入报障人联系方式"
                style="width: 94%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障描述" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                placeholder="请输入报障描述"
                style="width: 98%"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="能否正常使用" prop="isNormal">
              <el-select
                v-model="repairForm.isNormal"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="能正常使用" :value="0" />
                <el-option label="不能正常使用" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障附件" prop="annexUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    repairForm.annexUrl = url;
                  }
                "
                :fileSize="10"
                :fileType="['txt', 'doc', 'docx', 'xls', 'xlsx', 'pdf']"
                :type="4"
                :isWorkOrderAnnex="true"
                :limit="1"
                :modelValue="repairForm.annexUrl"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障图片" prop="images">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    repairForm.images = url;
                  }
                "
                :uploadType="4"
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障时间" prop="repairTime">
              <el-date-picker
                type="datetime"
                v-model="repairForm.repairTime"
                placeholder="请选择报障时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled-date="disabledDateFn"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRepairForm" v-throttle
            >确 定</el-button
          >
          <el-button @click="repairCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成激活码 -->
    <el-dialog
      class="custom-dialog"
      top="30vh"
      v-model="open2"
      width="400px"
      :close-on-click-modal="false"
      align-center
    >
      <template #header>
        <div class="dialog-header">
          请选择安装位置
          <el-icon :size="22" style="margin-left: 5px"
            ><QuestionFilled
          /></el-icon>
          <span @click="handleToAdd">没有安装位置？点我新建</span>
        </div>
      </template>
      <div>
        <el-scrollbar height="400px">
          <el-tree
            ref="nodeRef"
            style="max-width: 600px"
            :data="positionNodeList"
            node-key="id"
            :props="positionProps"
            show-checkbox
            check-on-click-node
            :expand-on-click-node="false"
            check-strictly
            default-expand-all
            @check="handleNodeCheck"
          />
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDownload2" v-throttle
            >点击生成激活码</el-button
          >
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入提示 -->
    <el-dialog title="关于导入" align-center v-model="showExport" width="400px">
      <div style="padding: 0 20px">
        如果想一键导入设备信息，请下载当前导入模板，并按照规则填写表格，当前仅支持.xlsx与.xlx格式
        <div
          style="
            color: cornflowerblue;
            text-decoration: underline;
            margin-top: 20px;
            cursor: pointer;
          "
          v-throttle
          @click="handleDownload"
        >
          点击下载导入模板.xlsx
        </div>
      </div>
    </el-dialog>
    <!-- 添加查看二维码的对话框 -->
    <el-dialog
      title="设备二维码"
      v-model="qrCodeDialogVisible"
      width="360px"
      append-to-body
      :align-center="true"
    >
      <div class="qrcode-container">
        <div class="qrcode-wrapper">
          <img :src="currentQrCode" alt="设备二维码" style="max-width: 240px" />
        </div>
      </div>
    </el-dialog>
    <ExportRes
      :data="exportRes"
      :open="open3"
      @setOpen="
        (val) => {
          open3 = val;
        }
      "
    />
    <div id="qrcode" style="display: none"></div>
  </div>
</template>

<script setup name="ledgerManage">
import locateSel from "./components/locateSel.vue";
import imgUpload from "@/components/ImageUpload";
import fileUpload from "@/components/FileUpload";
import ExportRes from "@/components/ExportRes";
import { useRoute, useRouter } from "vue-router";
import {
  deviceAdd,
  deviceDel,
  deviceEdit,
  devicePage,
  deviceInfo,
  deviceRemove,
  deviceExport,
  templateDownload,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { getUserList, getUserByRole } from "@/api/system/user";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import {
  downloadBlob,
  treeToArray,
  treeFindPath,
  timeFormat,
  sendPointRequest,
} from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import { sm2Decrypt } from "@/utils/sm2encrypt";
import { nextTick } from "vue";
import QRCode from "qrcode";
import { generateQRCode, wrapText } from "@/utils/uploadCode2";
import { ElMessage } from "element-plus";
import { debounce } from "@/utils/debounce";
import { checkDeviceCode } from "@/api/deviceControl";
import { deviceStatusObj } from "@/utils/addPoint";
import { onMounted, onBeforeUnmount } from "vue";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
const ledgerRef = ref(null);
const exportRes = ref({});
const repairList = ref([]);
const open = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const activeNum = ref(1);
const loading = ref(false);
const showSearch = ref(true);
const showExport = ref(false);
const showRepair = ref(false);
const positionResult = ref({
  names: [],
  ids: [],
});
const queryPositionResult = ref({
  names: [],
  ids: [],
});
const statusList = ref([
  { label: "正常", value: 0, type: "success" },
  { label: "报障中", value: 2, type: "danger" },
  { label: "维修中", value: 1, type: "danger" },
  { label: "待审核", value: 3, type: "primary" },
  { label: "已报废", value: 4, type: "warning" },
]);
const typeList = ref([]);
const typeList_disabled = ref([]);
const tagList = ref([]);
const positionTreeList = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});
const total = ref(0);
const uploadFileUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/device/importData"
); // 导入文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const smartRules = ref({
  tagIdList: [
    {
      required: true,
      message: "设备标签不能为空",
      trigger: ["blur", "change"],
    },
  ],
  macAddress: [
    { required: true, message: "物理地址不能为空", trigger: "blur" },
  ],
  logicAddress: [
    { required: true, message: "逻辑地址不能为空", trigger: "blur" },
  ],
  osVersion: [
    { required: true, message: "操作系统版本号不能为空", trigger: "blur" },
  ],
  cpu: [{ required: true, message: "CPU类型不能为空", trigger: "blur" }],
  internalStorage: [
    { required: true, message: "设备内存不能为空", trigger: "blur" },
  ],
  ipAddress: [{ required: true, message: "IP地址不能为空", trigger: "blur" }],
  ralayHost: [
    { required: true, message: "边缘服务器ip不能为空", trigger: "blur" },
  ],
  brand: [{ required: true, message: "设备品牌不能为空", trigger: "blur" }],
  disk: [{ required: true, message: "设备硬盘不能为空", trigger: "blur" }],
});
const baseRules = ref({
  deviceName: [
    { required: true, message: "设备名称不能为空", trigger: "blur" },
    { max: 50, message: "设备名称最多输入50个字符", trigger: "blur" },
  ],
  typeId: [{ required: true, message: "设备类型不能为空", trigger: "blur" }],
  deviceCode: [
    { required: true, message: "设备编码不能为空", trigger: "blur" },
  ],
  deviceStatus: [
    {
      required: true,
      message: "设备状态不能为空",
      trigger: ["blur", "change"],
    },
  ],
  deviceImg: [
    {
      required: true,
      message: "设备照片不能为空",
      trigger: ["blur", "change"],
    },
  ],
  installAddressId: [
    {
      required: true,
      message: "安装位置不能为空",
      trigger: ["blur", "change"],
    },
  ],
  model: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  putTime: [
    {
      required: true,
      message: "入库时间不能为空",
      trigger: ["blur", "change"],
    },
  ],
});

const data = reactive({
  treeRef: null,
  isBatch: false,
  addTimer: null,
  addSecond: 0,
  statusObj: {
    0: { label: "正常", type: "success" },
    1: { label: "维修中", type: "danger" },
    2: { label: "报障中", type: "warning" },
    3: { label: "待审核", type: "primary" },
    4: { label: "已报废", type: "info" },
  },
  nodeRef: null,
  positionNodeResultList: [
    {
      names: [],
      ids: [],
    },
  ],
  positionNodeList: [],
  repairRef: null,
  ledgerTable: null,
  userList: [],
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // 保存表格勾选的行数据
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    installAddressId: "",
    positionId: "",
    createTime: "",
  },
  queryParams: {
    current: 1,
    size: 10,
    tenantId: "",
    model: "",
    deviceCodeAndName: "",
    deviceStatus: route.query?.deviceStatus || "",
    // address: "",
    typeId: "",
    tagId: "",
    putTime: "",
    time: null,
    startTime: "",
    endTime: "",
  },
  rules: {},
  repairForm: {
    userId: "",
    type: 1,
    repairName: "",
    repairTime: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: "",
    attachments: [],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报障人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      { required: true, message: "报障人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    isNormal: [
      {
        required: true,
        message: "请选择设备能否正常使用",
        trigger: ["blur", "change"],
      },
    ],
    remark: [
      { required: true, message: "报障描述不能为空", trigger: "blur" },
      { max: 200, message: "报障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        message: "报障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
    repairTime: [
      {
        required: true,
        validator: validateRepairTime,
        trigger: ["blur", "change"],
      },
    ],
  },
});
const {
  treeRef,
  isBatch,
  addTimer,
  addSecond,
  statusObj,
  nodeRef,
  positionNodeResultList,
  positionNodeList,
  ledgerTable,
  repairRef,
  userList,
  queryParams,
  form,
  repairForm,
  repairRules,
  tableData,
  tableAllSelectedId,
} = toRefs(data);

const isExternalPark = ref(false);

function validateRepairTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择报修时间"));
  } else if (new Date(value).getTime() > new Date().getTime()) {
    callback(new Error("报修时间不能选择未来时间"));
  } else {
    callback();
  }
}

// 处理节点勾选事件
const handleNodeCheck = (node, checkedStatus) => {
  // 递归勾选所有子节点
  const checkChildren = (node, checked) => {
    if (node.children) {
      node.children.forEach((child) => {
        nodeRef.value.setChecked(child.id, checked); // 勾选当前子节点
        if (child.children) checkChildren(child, checked); // 递归处理孙节点
      });
    }
  };
  // console.log(node, checkedStatus);
  // 获取当前节点的选中状态
  const isChecked = nodeRef.value.getCheckedKeys().includes(node.id);

  // 当父节点被勾选时，递归勾选所有子节点
  if (isChecked && node.children) {
    checkChildren(node, true);
  }
};

function handleSortDate() {
  if (queryParams.value.sort === "asc") {
    queryParams.value.sort = "desc";
  } else {
    queryParams.value.sort = "asc";
  }
  getList();
}

function handleParkChange(event) {
  if (route.path === "/deviceLedger/ledger") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  initParkState();
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem("deviceManagePage");
  if (savedPage) {
    queryParams.value.current = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem("deviceManagePage");
  }

  if (route.query.deviceStatus !== undefined) {
    queryParams.value.deviceStatus = Number(route.query.deviceStatus);
  }
  getPositionTreeList();
  getList();
  window.addEventListener("parkChange", handleParkChange);

  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - 215;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - 215;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览访问设备台账管理页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});

const handleToAdd = () => {
  router.push("baseInfo/location");
};

const handleCancel = () => {
  data.nodeRef.setCheckedNodes([]);
  open2.value = false;
};

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

const repairUserChange = (val) => {
  const idx = data.userList.findIndex((_) => _.userId == val);
  console.log("报障人更改", val, idx);
  data.repairForm.repairPhone = data.userList[idx].phoneNumber || "";
  data.repairForm.repairName = data.userList[idx].nickName || "";
};

/** 查询用户列表 */
async function getUsers() {
  // await getUserList({ nickName: "", phone: "" }).then((response) => {
  //   console.log("用户列表", response.data);
  //   data.userList = response.data || [];
  // });
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    console.log("拥有角色的用户列表", resp);
    if (resp.data) {
      data.userList = resp.data.records;
    }
  });
}

// 打印设备二维码
const handlePrintCode = debounce(() => {
  let rows = data.tableAllSelectedRow;
  if (rows.length == 0) {
    proxy.$modal.msgWarning("请选择设备");
    return;
  }
  if (data.tableAllSelectedRow.findIndex((_) => _.deviceStatus > 1) != -1) {
    proxy.$modal.msgWarning("待审核/已报废设备不能进行此操作");
    return;
  }
  let obj = rows.map((item, index) => {
    return {
      deviceCode: item.deviceCode,
      deviceName: item.deviceName,
      installAddress: item.installAddress,
    };
  });

  console.log(obj);
  generateQRCode(obj, true, `${form.value.deviceName}-设备二维码.png`);
  sendPointRequest({
    event: "Click",
    eventDescribe: `一键打印设备二维码`,
    content: "",
    num: 1,
  });
}, 250);

const qrCodeDialogVisible = ref(false);
const currentQrCode = ref("");

// 添加生成单个二维码的方法
async function generateSingleQRCode(deviceData) {
  const { deviceCode, deviceName, installAddress } = deviceData;

  try {
    // 显著减小二维码尺寸
    const qrUrl = await QRCode.toDataURL(deviceCode, {
      width: 128, // 调整为更小的尺寸
      margin: 1, // 减小边距
      scale: 4, // 保持清晰度
    });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    return new Promise((resolve) => {
      img.onload = () => {
        const scale = 2;
        canvas.width = (img.width + 60) * scale; // 继续减小边距
        canvas.height = (img.height + 20 + 15 * 3) * scale;

        ctx.scale(scale, scale);
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.fillStyle = "white";
        ctx.fillRect(0, 0, canvas.width / scale, canvas.height / scale);

        const qrX = (canvas.width / scale - img.width) / 2;
        const qrY = 10;

        ctx.drawImage(img, qrX, qrY);

        const prompt = `设备名称：${deviceName}\n设备编码：${deviceCode}\n安装位置：${installAddress}`;
        ctx.fillStyle = "black";
        ctx.font = "bold 11px Arial"; // 稍微调小字体

        const promptLines = wrapText(ctx, prompt, 150); // 减小文本宽度
        promptLines.forEach((line, index) => {
          const textY = qrY + img.height + 15 + 13 * index; // 调整行间距
          const textWidth = ctx.measureText(line).width;
          const xPosition = (canvas.width / scale - textWidth) / 2;
          ctx.fillText(line, xPosition, textY);
        });

        resolve(canvas.toDataURL("image/png", 1.0));
      };
      img.src = qrUrl;
    });
  } catch (error) {
    console.error("Error generating QR code:", error);
    return null;
  }
}

// 查看二维码方法
async function handleViewQrCode() {
  try {
    const deviceData = {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    };

    const qrCodeUrl = await generateSingleQRCode(deviceData);
    if (qrCodeUrl) {
      currentQrCode.value = qrCodeUrl;
      qrCodeDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError("生成二维码失败");
    console.error(error);
  }
}

// 下载二维码方法
function handleDownloadQrCode() {
  const deviceData = [
    {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    },
  ];

  generateQRCode(deviceData);
}

function handlePosition(val) {
  positionResult.value = {
    ids: [],
    names: [],
  };
  positionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = positionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    positionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("positionResult ==> ", positionResult.value);
}

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  // queryParams.value.address = queryPositionResult.value.names.join("-");
  handleQuery();
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

/** 查询台账列表 */
function getList() {
  loading.value = true;
  console.log("查询的参数", data.queryParams);
  devicePage(data.queryParams).then((response) => {
    const { page } = response.data;
    console.log("台账列表", page);
    data.tableData = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tagId,
        portTypeName,
        assetsTypeName,
        level,
      } = cur;
      let arr = tagId.split(","),
        names = [];
      arr.map((item) => {
        let obj = tagList.value.find((_) => _.tagId == item);
        // console.log(obj)
        !!obj ? names.push(obj.tag) : "";
      });
      res.push({
        ...cur,
        tagName: names.join("，") || "-",
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        portTypeNameStr: portTypeName || "-",
        assetsTypeNameStr: assetsTypeName || "-",
        levelStr: level || "-",
      });
      return res;
    }, []);
    total.value = page.total;
    loading.value = false;
    nextTick(() => {
      data.tableData.forEach((item) => {
        if (data.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, data.tableAllSelectedId)
          data.ledgerTable.toggleRowSelection(item, true);
        } else {
          data.ledgerTable.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
    console.log(data.tableData);
  });
  devicePage({
    ...data.queryParams,
    current: 1,
    size: 9999999,
  }).then((res) => {
    // console.log(res)
    data.tableData_all = res.data.page.records;
  });
}

async function getTagList() {
  await getDeviceTag().then((response) => {
    // console.log('tagList', response.data)
    tagList.value = response.data;
  });
}

function getPosition(d) {
  data.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    data.positionNodeList = addAttr(tree);
    positionList.value = treeToArray(response.data);
    console.log("positionNodeList", data.positionNodeList);
    // console.log('positionList ==>', positionList.value)
  });
}

function addAttr(data, num = 0) {
  num++;
  for (var j = 0; j < data.length; j++) {
    // data[j].disabled = num != 3 || data[j].status == 1;
    data[j].disabled = data[j].status == 1;
    // console.log(num, data[j]);
    if (data[j].children.length > 0) {
      addAttr(data[j].children, num);
    }
  }
  return data;
}

function addAttr2(data, num = 0) {
  num++;
  for (var j = 0; j < data.length; j++) {
    // data[j].disabled = num != 3 || data[j].status == 1;
    // console.log(num, data[j]);
    data[j].disabled = data[j].status == 1;
    if (data[j].children.length > 0) {
      addAttr2(data[j].children, num);
    }
  }
  return data;
}

function getTypeList() {
  getDeviceType().then((response) => {
    typeList.value = response.data;
    typeList_disabled.value = response.data.reduce((res, cur) => {
      res.push({
        ...cur,
        disabled: cur.typeName == "智慧大屏",
      });

      return res;
    }, []);
    // console.log('typeList_disabled', typeList_disabled.value)
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
}
/** 表单重置 */
function repairReset() {
  data.repairRef.resetFields();
  data.repairForm = {
    type: 1,
    userId: useUserStore().userId + "",
    repairName: useUserStore().nickName,
    repairPhone: useUserStore().phonenumber,
    repairTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    deviceCodeList: [],
    remark: "",
    resource: "管理后台",
    channel: "管理员主动报障",
    images: "",
  };
  // console.log("当前报障人信息", data.repairForm);
}
/** 搜索按钮操作 */
function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.positionIds = d?._checkList;
  }
  queryParams.value.current = 1;
  queryParams.value.startTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[0]
    : "";
  queryParams.value.endTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[1]
    : "";
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.startTime = "";
  queryParams.value.endTime = "";
  delete queryParams.value.sort;
  proxy.resetForm("queryRef");
  queryParams.value.positionIds = [];
  queryParams.value.size = 10;
  proxy.$refs.locateRef.resetQuery();
  // handleQuery();
  // proxy.$refs.treeRef.setCheckedNodes([]);
  // handleQueryPosition();
}
// 根据类型动态修改校验规则
function handleChangeType(val) {
  let name = "";
  typeList.value.forEach((item) => {
    if (item.id == val) name = item.typeName;
  });
  data.rules = {};
  if (name == "智慧大屏") {
    if (baseRules.value.deviceCode[1]) delete baseRules.value.deviceCode[1];
    Object.assign(data.rules, baseRules.value, smartRules.value);
  } else {
    baseRules.value.deviceCode[1] = {
      pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
      message: '设备编码不能以"ZHDP"开头',
      trigger: "blur",
    };
    Object.assign(data.rules, baseRules.value);
  }
  ledgerRef.value.clearValidate();
}
/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      data.ledgerTable.setCurrentRow(null);
      data.ledgerTable.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.deviceId);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      data.ledgerTable.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    data.ledgerTable.setCurrentRow(row);
    data.ledgerTable.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      data.tableAllSelectedId.push(item.deviceId);
      data.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.deviceId);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = data.tableData;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        data.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 新增设备操作 */
function handleAdd() {
  router.push("addDevice");
}
/** 查看按钮操作 */
function handleCheck(row, type) {
  // 保存当前页码到localStorage
  localStorage.setItem("deviceManagePage", queryParams.value.current);

  sendPointRequest({
    event: "Click",
    eventDescribe: "点击设备详情进行查看",
    content: deviceStatusObj[row.deviceStatus].status,
    num: 1,
  });
  router.push({
    path: "deviceInfo",
    query: {
      id: row.deviceId,
      type,
      from: 0,
    },
  });
}
/*
 */
function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    if (valid) {
      data.repairForm.deviceCodeList =
        repairList.value.map((item) => item.deviceCode) || [];
      data.repairForm.images = data.repairForm.images.split(",") || "";
      console.log("repairList.value", repairList.value);
      const deviceStatusList = [
        ...new Set(repairList.value.map((item) => item.deviceStatus)),
      ];
      console.log(data.isBatch, "data.isBatch");
      deviceRepair(data.repairForm).then((res) => {
        sendPointRequest({
          event: "Click",
          eventDescribe: `点击${data.isBatch ? "批量" : ""}报障`,
          content: data.isBatch
            ? (() => {
                return deviceStatusList
                  .map((status) => deviceStatusObj[status].status)
                  .join(",");
              })()
            : deviceStatusObj[repairList.value[0].deviceStatus].status,
          num: 1,
        });
        proxy.$modal.msgSuccess("报障成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

function handleRepair(row) {
  if (tableAllSelectedId.value.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  if (data.tableAllSelectedRow.findIndex((_) => _.deviceStatus > 2) != -1) {
    proxy.$modal.msgWarning("待审核/已报废设备不能进行此操作");
    return;
  }

  data.isBatch = !row.deviceId;

  showRepair.value = true;
  repairList.value = row.deviceId ? [row] : data.tableAllSelectedRow;
  console.log("用户信息", useUserStore());
  nextTick(() => repairReset());
}

/** 删除按钮操作 */
function handleDelete(row) {
  const deviceIds = row.deviceId || tableAllSelectedId.value;
  const deviceNames = [];
  if (tableAllSelectedId.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }

  if (Array.isArray(deviceIds)) {
    deviceIds.map((item) =>
      deviceNames.push(
        data.tableAllSelectedRow.find((_) => _.deviceId == item).deviceName
      )
    );
  } else {
    deviceNames.push(row.deviceName);
  }

  const deviceStatusList = [
    ...new Set(data.tableAllSelectedRow.map((item) => item.deviceStatus)),
  ];

  proxy.$modal
    .confirm(
      `是否确认${!row.deviceId ? "批量" : ""}删除设备名称为【${deviceNames.join(
        "、"
      )}】的数据项？`
    )
    .then(async function () {
      if (typeof deviceIds === "object") {
        sendPointRequest({
          event: "Click",
          eventDescribe: `点击${!row.deviceId ? "批量" : ""}删除`,
          content: !row.deviceId
            ? (() => {
                return deviceStatusList
                  .map((status) => deviceStatusObj[status].status)
                  .join(",");
              })()
            : deviceStatusObj[row.deviceStatus].status,
          num: 1,
        });
        await deviceRemove({ idList: deviceIds });
      } else {
        await deviceDel({ id: deviceIds });
      }
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
      data.tableAllSelectedId = [];
      data.tableAllSelectedRow = [];
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  deviceExport(queryParams.value).then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "设备清单");
    sendPointRequest({
      event: "Click",
      eventDescribe: `点击一键导出`,
      content: "",
      num: 1,
    });
  });
}

const beforeUpload = (file) => {
  const Xls = file.name.split(".");
  if (Xls[1] !== "xls" && Xls[1] !== "xlsx") {
    proxy.$modal.msgError("请上传excel格式的文件!");
    return false;
  } else if (file.size / 1024 / 1024 > 250) {
    proxy.$modal.msgError("请上传250M以下的文件!");
    return false;
  }
  return true;
};

const handleUploadSuccess = (res, file) => {
  if (res.code === 200) {
    sendPointRequest({
      event: "Click",
      eventDescribe: `点击一键导入`,
      content: "",
      num: 1,
    });
    getList();
    exportRes.value = JSON.parse(sm2Decrypt(res.data));
    console.log("导入返回的数据", exportRes.value);
    let { errorCount } = exportRes.value;
    if (!errorCount) {
      proxy.$modal.msgSuccess("导入成功");
    } else {
      open3.value = true;
    }
  } else {
    proxy.$modal.msgError(res.msg);
  }
};

function handleDownload() {
  // templateDownload({ modelName: "import_device" }).then((response) => {
  //   downloadBlob(response, "application/vnd.ms-excel", "设备导入模板");
  // });
  templateDownload().then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "设备导入模板");
  });
}

function handleDownload2() {
  let checkList = data.nodeRef.getCheckedNodes();
  if (checkList.length < 1) {
    proxy.$modal.msgWarning("请选择安装位置");
    return;
  }
  console.log("checklist", checkList);
  handlePositionNode(checkList);
  const positionList = data.positionNodeResultList.map((item) => {
    return {
      position: item.names.join("-"),
      positionId: item.ids.join(","),
    };
  });
  console.log("positionList", { positionList });
  proxy.$modal.loading();
  checkDeviceCode({ positionList })
    .then((res) => {
      exportDeviceCode({ positionList })
        .then((response) => {
          proxy.$modal.closeLoading();
          console.log("blob", response);
          downloadBlob(response, "application/vnd.ms-excel", "设备激活码");
          sendPointRequest({
            event: "Click",
            eventDescribe: `点击生成设备激活码`,
            content: "",
            num: 1,
          });
          proxy.$modal.msgSuccess("操作成功");
          handleCancel();
        })
        .catch((err) => {
          proxy.$modal.closeLoading();
        });
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  sendPointRequest({
    event: "Click",
    eventDescribe: "点击安装位置筛选框",
    content: "",
    num: 1,
  });
  handleQuery();
}

function handlePositionNode(checkList) {
  data.positionNodeResultList = [{ ids: [], names: [] }];
  for (let i = 0; i < checkList.length; i++) {
    data.positionNodeResultList[i] = {
      ids: [],
      names: [],
    };

    data.positionNodeResultList[i].ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == checkList[i].id
    );
    const arr = data.positionNodeResultList[i].ids;
    for (let j = 0; j < arr.length; j++) {
      data.positionNodeResultList[i].names[j] = positionList.value.find(
        (node) => node.id == arr[j]
      ).name;
    }
  }
  console.log("positionResultNodeList ==> ", data.positionNodeResultList);
}

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

getTagList();
getTypeList();
getUsers();
getList();
</script>
<style scoped lang="scss">
.ascending.active {
  border-bottom-color: #4095e5;
}
.descending.active {
  border-top-color: #4095e5;
}
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;
  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}
.qrcode-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0; /* 继续减小内边距 */
}

.qrcode-wrapper {
  text-align: center;
  width: fit-content;
  max-width: 100%;
}

.qrcode-wrapper img {
  width: auto;
  max-width: 100%;
  height: auto;
}
.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}
.monitor_flex {
  display: flex;
  align-items: flex-start;
  gap: 0 20px;
  width: 100%;
  min-height: calc(100vh - 215px);
  .flex_1 {
    width: 18%;
    height: 100%;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }
  .flex_2 {
    // flex: 1;
    width: 82%;
    min-width: 510px;
    // .search-list {
    //   flex-wrap: wrap;
    //   display: flex;
    //   flex-wrap: wrap;
    // }
  }
}
</style>
