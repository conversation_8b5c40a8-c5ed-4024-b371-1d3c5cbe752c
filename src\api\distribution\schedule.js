import request from "@/utils/request";

// 新增工作内容 /system/schoolWork/addWorkContent
export function addWorkContent(data) {
  return request({
    url: "/system/schoolWork/addWorkContent",
    method: "post",
    data,
  });
}

// 查询工作内容列表 /system/schoolWork/listWorkContent
export function listWorkContent(params) {
  return request({
    url: "/system/schoolWork/listWorkContent",
    method: "get",
    params,
  });
}

// 更改工作内容 /system/schoolWork/updateWorkContent
export function updateWorkContent(data) {
  return request({
    url: "/system/schoolWork/updateWorkContent",
    method: "put",
    data,
  });
}

// 删除工作内容 /system/schoolWork/delWorkContent
export function delWorkContent(params) {
  return request({
    url: "/system/schoolWork/delWorkContent",
    method: "delete",
    params,
  });
}

// 查询排班列表 /system/schoolWork/listWork
export function listWork(params) {
  return request({
    url: "/system/schoolWork/listWork",
    method: "get",
    params,
  });
}

// 新增工作排班 /system/schoolWork/addWork
export function addWork(data) {
  return request({
    url: "/system/schoolWork/addWork",
    method: "post",
    data,
  });
}

// 更改工作排班 /system/schoolWork/updateWork
export function updateWork(data) {
  return request({
    url: "/system/schoolWork/updateWork",
    method: "put",
    data,
  });
}

// 获取打卡规则 /system/schoolWork/getClockRule
export function getClockRule() {
  return request({
    url: "/system/schoolWork/getClockRule",
    method: "get",
  });
}

// 设置打卡规则 /system/schoolWork/setClockRule
export function setClockRule(data) {
  return request({
    url: "/system/schoolWork/setClockRule",
    method: "post",
    data,
  });
}


