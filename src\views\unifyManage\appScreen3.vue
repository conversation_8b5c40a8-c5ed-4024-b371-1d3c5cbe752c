<template>
  <div class="unify">
    <div class="unify-main" :class="{ maintain: curBtn == 1 }">
      <div class="unify-main_header">
        <div class="unify-main_header-btns">
          <div
            class="unify-main_header-btns_btn"
            :class="[
              curBtn == index ? 'active' : '',
              index > 1 ? 'right-btn' : 'left-btn',
            ]"
            v-for="(item, index) in headerBtns"
            :key="index"
            @click="curBtn = index"
          >
            {{ item }}
          </div>
        </div>
        <div class="unify-main_header-date">
          <div class="left-date">
            {{ proxy.parseTime(curTime, "{h}:{i}:{s}") }}
          </div>
          <div class="right-date">
            {{ proxy.parseTime(curTime, "{y}.{m}.{d}") }}
          </div>
          <div
            class="weather"
            v-if="!!weatherImg"
            :class="weatherName == 'cloud' ? 'cloud' : ''"
          >
            <img :src="weatherImg" />
          </div>
        </div>
      </div>
      <div :class="curBtn != 2 ? 'unify-main_chart' : ''">
        <page1 v-if="curBtn == 0" />
        <page2 v-if="curBtn == 1" />
        <page3 v-if="curBtn == 2" />
      </div>
      <div class="footer"></div>
    </div>
  </div>
</template>
    
    <script setup name="unifyIndex">
import page1 from "./component/app/page1-2.vue";
import page2 from "./component/app/page2-2.vue";
import page3 from "./component/app/page3.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  weatherName: "sun",
  weatherImg:
    "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/sun.png",
  headerBtns: ["资产数据大屏", "运维数据大屏", "集控孪生大屏"],
  curBtn: 0,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
});
const { weatherName, weatherImg, headerBtns, curBtn, timer, timer2, curTime } =
  toRefs(state);

function getWeather() {
  fetch(
    "https://restapi.amap.com/v3/weather/weatherInfo?city=440100&key=f9b3588a8a4599df337256b96611e824",
    {
      method: "GET",
    }
  )
    .then((response) => {
      if (response.ok) {
        return response.json();
      }
    })
    .then((res) => {
      console.log(res, "天气信息");
      if (res.lives && res.lives.length > 0) {
        let weather = res.lives[0].weather;
        let name =
          weather.indexOf("雨") != -1
            ? "rain"
            : weather.indexOf("阴") != -1 || weather.indexOf("云") != -1
            ? "cloud"
            : "sun";
        state.weatherName = name;
        state.weatherImg = `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/${name}.png`;
      }
    });
}

onMounted(() => {
  document.addEventListener("selectstart", function (e) {
    e.preventDefault();
  });
  getWeather();
  state.timer = setInterval(() => {
    state.curTime = new Date().getTime();
  }, 1000);
  state.timer2 = setInterval(() => {
    getWeather();
  }, 3600000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
  document.removeEventListener("selectstart", function (e) {
    e.preventDefault();
  });
});
</script>
    
    <style lang="scss" scoped>
@font-face {
  font-family: "HYYaKuHeiW";
  src: url("@/assets/fontFamily/HYYaKuHeiW.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "ZhengQingKeHuangYouTi";
  src: url("@/assets/fontFamily/ZhengQingKeHuangYouTi-1.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
.unify {
  width: 100%;
  height: 100%;
  background-color: #020d20;
  // border: 1px solid red;
  color: #b0c0e6;
  overflow-y: hidden;

  &-main {
    width: 100vw;
    height: 56vw;
    // border: 1px solid red;
    position: relative;
    // background: url("@/assets/screen/bg.png");
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/bg.png");
    background-size: 100% 100%;

    &.maintain {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/bg.png");
      background-size: 100% 100%;
    }

    &::before {
      content: "";
      position: absolute;
      top: 0;
      left: 0;
      height: 100%;
      width: 21.8vw;
      z-index: 1;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/frame_left.png");
      background-size: 100% 100%;
    }

    &::after {
      content: "";
      position: absolute;
      top: 0;
      right: 0;
      height: 100%;
      width: 21.5vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/frame_right.png");
      background-size: 100% 100%;
    }

    .footer {
      position: absolute;
      bottom: 0;
      width: 98%;
      left: 1%;
      height: 1.8vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/frame_bottom.png");
      background-size: 100% 100%;
    }

    &_header {
      z-index: 11;
      position: relative;
      width: 100%;
      height: 13.5vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/title2.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 0.5vw;

      &-title {
        display: flex;
        padding-top: 0.3vw;
        border: 1px solid red;
        &::before {
          content: "";
          // border: 1px solid red;
          width: 12vw;
          height: 4vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/header.png");
          background-size: 100% 100%;
        }
      }

      &-btns {
        position: absolute;
        display: flex;
        width: 100%;
        // border: 1px solid red;
        bottom: 8.8vw;
        font-size: 0.85vw;
        font-family: "HYYaKuHeiW";

        &_btn {
          // border: 1px solid red;
          color: #d4deff;
          cursor: pointer;
          text-align: center;
          width: 8.2vw;
          height: 3.3vw;
          line-height: 4vw;
          position: relative;
          overflow: hidden;

          &.left-btn {
            left: 22vw;
            padding-left: 0.3vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/header-leftBtn.png");
            background-size: 100% 100%;
            &.active {
              color: #fff;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/header-leftBtn_checked.png");
              background-size: 100% 100%;
            }
          }

          &.right-btn {
            left: 45.1vw;
            padding-right: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/header-rightBtn.png");
            background-size: 100% 100%;
            &.active {
              color: #fff;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/header-rightBtn_checked.png");
              background-size: 100% 100%;
            }
          }

          &:hover {
            // transform: translateY(-2px);
          }
        }
      }

      &-date {
        position: absolute;
        left: 2.5vw;
        bottom: 8.5vw;
        text-align: right;
        color: #cdd6e3;
        .left-date {
          font-size: 1.5vw;
        }
        .right-date {
          font-size: 0.85vw;
        }
        .weather {
          position: absolute;
          left: 8vw;
          bottom: 0;
          // border: 1px solid red;
          img {
            width: 2.5vw;
            height: 2.5vw;
          }
          &.cloud img {
            width: 2.8vw;
          }
          &::before {
            content: "";
            position: absolute;
            top: 0.3vw;
            left: -1vw;
            width: 0.1vw;
            height: 2vw;
            background-color: #394f63;
          }
        }
      }
    }

    &_chart {
      position: relative;
      padding: 0 2.5vw;
      z-index: 11;
    }
  }
}
</style>