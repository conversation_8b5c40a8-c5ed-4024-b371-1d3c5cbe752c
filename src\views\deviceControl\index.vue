<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <div class="header-tip">
            <span>{{ route.meta.title }}</span>
            <div class="header-tip_timer">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="设备状态刷新倒计时"
                placement="top"
              >
                <img
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/refreshTimer.png"
                />
              </el-tooltip>
              <el-countdown
                v-if="showRefresh"
                title=""
                :value="refreshCountdown"
                format="mm:ss"
                @finish="refreshData"
              />
            </div>
          </div>
          <el-button v-if="fromWorkbench" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <div
        class="monitor"
        :class="{ hasControlBtn: tableAllSelectedId.length > 0 }"
        :style="{ paddingBottom: isAllQuery ? '20px' : '0px' }"
      >
        <locateSel
          class="monitor-position"
          ref="locateRef"
          @getPosition="getPosition"
          :maxHeight="posHeight"
          @handleQuery="handleQuery"
        />
        <div class="monitor-list" ref="flexRightBox">
          <div class="monitor-statics">
            <div class="monitor-statics_item" @click="handleAllQuery(0)">
              <div class="info">
                智能终端总数
                <div>
                  <div class="info-num">
                    <span>{{ ledgerTotal || 0 }}</span
                    >台
                    <div v-if="useUserStore().isCommon">
                      / <span>{{ pointNum || 0 }}</span
                      >台
                    </div>
                    <el-button
                      v-if="useUserStore().isCommon"
                      color="#626aef"
                      size="small"
                      style="margin-left: 10px"
                      @click.stop="handlePoint"
                      >点位到期时间详情</el-button
                    >
                  </div>
                  <div class="expireDay" v-if="useUserStore().isCommon">
                    距离最近到期时间仅剩{{ expireDay || 0 }}天，请注意续费！
                  </div>
                </div>
              </div>
            </div>
            <div class="monitor-statics_item" @click="handleAllQuery(1)">
              <div class="info">
                <div class="tag">运行状态异常终端总数</div>
                <div class="info-num">
                  <span>{{ ledgerErrorTotal }}</span
                  >台
                </div>
              </div>
            </div>
          </div>
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            @submit.native.prevent
          >
            <el-form-item label="" prop="deviceCode">
              <el-input
                v-model="queryParams.deviceCode"
                placeholder="请输入设备编号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="deviceName">
              <el-input
                v-model="queryParams.deviceName"
                placeholder="请输入设备名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="deviceStatus">
              <el-select
                v-model="queryParams.deviceStatus"
                placeholder="请选择设备状态"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择运行状态"
                @change="handleRunStatus"
                multiple
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in runList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                  :disabled="item.disabled"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="12" class="mb12 btn-list">
            <el-col :span="4.5">
              <el-select
                v-model="operationParams.remoteSend"
                placeholder="请选择远程传输操作"
                @change="handleOpeartionRemoteSend"
                :disabled="tableAllSelectedId.length < 1"
                style="width: 180px"
              >
                <el-option
                  v-for="item in remoteSendList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="4.5">
              <el-select
                v-model="operationParams.screenSettings"
                placeholder="请选择大屏设置操作"
                style="width: 180px"
                @change="handleScreenSettings"
                :disabled="tableAllSelectedId.length < 1"
              >
                <el-option
                  v-for="item in screenSettingsList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Setting"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleRepair"
                >批量报修</el-button
              >
            </el-col>
            <el-col
              :span="1.5"
              v-if="!isExternalPark && !useUserStore().isCommon"
            >
              <el-button
                type="success"
                plain
                icon="Download"
                @click="open2 = true"
                >生成设备激活码</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                plain
                icon="Connection"
                type="primary"
                @click="router.push('/teacherCenter/teacher')"
                >设备使用人员管理</el-button
              >
            </el-col>
          </el-row>

          <el-table
            ref="ledgerTable"
            v-loading="loading"
            :data="ledgerList"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
              fixed="left"
            />
            <el-table-column
              label="运行状态"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div
                  v-if="runList[scope.row.runStatusList[0]]"
                  style="
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;
                    justify-content: center;
                  "
                >
                  <el-tag
                    v-for="(item, index) in scope.row.runStatusList"
                    :key="index"
                    effect="dark"
                    :type="runList[item]?.type || 'primary'"
                    >{{ runList[item]?.label || "-" }}</el-tag
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="设备编码"
              align="center"
              show-overflow-tooltip
              minWidth="150px"
              prop="deviceCodeStr"
            />
            <el-table-column
              label="设备名称"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="deviceNameStr"
            />
            <el-table-column
              label="设备类型"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="deviceTypeStr"
            />
            <el-table-column
              label="状态"
              align="center"
              show-overflow-tooltip
              minWidth="100px"
            >
              <template #default="scope">
                <el-tag
                  v-if="statusObj[scope.row.deviceStatus]"
                  effect="dark"
                  :type="statusObj[scope.row.deviceStatus].type"
                  >{{ statusObj[scope.row.deviceStatus].label }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="安装位置"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
            >
              <template #default="scope">
                <span>{{ scope.row.installAddress || "-" }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="设备图片"
              align="center"
              show-overflow-tooltip
              minWidth="110px"
            >
              <template #default="scope">
                <el-image
                  v-if="!!scope.row.deviceImg"
                  style="
                    width: 50px;
                    height: 30px;
                    display: block;
                    margin: 0 auto;
                  "
                  :src="scope.row.deviceImg"
                  fit="contain"
                />
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="180"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <div
                  style="
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: center;
                    margin-bottom: 10px;
                  "
                >
                  <el-tooltip content="详情" placement="top">
                    <el-button
                      link
                      type="success"
                      icon="Search"
                      @click.stop="handleCheck(scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="关机" placement="top">
                    <el-button
                      link
                      type="danger"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SwitchButton"
                      @click.stop="handleBatchRemote(0, 0, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="重启" placement="top">
                    <el-button
                      link
                      type="primary"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="RefreshRight"
                      @click.stop="handleBatchRemote(0, 1, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="定时关机" placement="top">
                    <el-button
                      link
                      type="warning"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="AlarmClock"
                      @click.stop="
                        isBatch = false;
                        handleFixed(scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="开启WIFI" placement="top">
                    <el-button
                      link
                      type="success"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SortUp"
                      @click.stop="handleBatchWifi(0, 1, scope.row)"
                    ></el-button>
                  </el-tooltip>
                </div>
                <div
                  style="
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: center;
                  "
                >
                  <el-tooltip content="关闭WIFI" placement="top">
                    <el-button
                      link
                      type="info"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SortDown"
                      @click.stop="handleBatchWifi(0, 0, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="清除缓存" placement="top">
                    <el-button
                      link
                      type="danger"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Brush"
                      @click.stop="
                        isBatch = false;
                        handleBatchClear(0, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="锁屏" placement="top">
                    <el-button
                      link
                      type="warning"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Lock"
                      @click.stop="
                        isBatch = false;
                        handleBatchLock(0, 0, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="解锁" placement="top">
                    <el-button
                      link
                      type="primary"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Unlock"
                      @click.stop="
                        isBatch = false;
                        handleBatchLock(0, 1, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="报修" placement="top">
                    <el-button
                      link
                      type="success"
                      icon="Setting"
                      :disabled="scope.row.deviceStatus > 2"
                      @click.stop="handleRepair(scope.row)"
                    ></el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            class="pagination-container"
            v-show="total > 0 && !data.isAllQuery"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            :pageSizes="[5, 10, 20, 30, 50]"
            @pagination="getList"
          />
        </div>
      </div>
      <!-- <div class="monitor-bottom" v-show="tableAllSelectedId.length > 0">
        <div class="monitor-btns">
          <el-button
            v-for="item in remoteList"
            :key="item.value"
            :type="item.type"
            :icon="item.icon"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleOpeartionRemote(item.value)"
            >{{ item.label }}</el-button
          >
        </div>
      </div> -->
      <control-btns
        v-if="tableAllSelectedId.length > 0"
        ref="controlBtnRef"
        :tableAllSelectedId="tableAllSelectedId"
        :tableAllSelectedRow="tableAllSelectedRow"
      />
    </el-card>

    <!-- 定时关机 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form ref="fixRef" :model="fixForm" :rules="rules" label-width="100px">
        <el-form-item label="关机时间" prop="time">
          <el-date-picker
            v-model="fixForm.time"
            type="datetime"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            placeholder="请选择关机时间"
            :disabled-date="disabledDateFn"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="checkFixTime" v-throttle
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写报修单 -->
    <el-dialog
      class="custom-dialog"
      title="报修单填写"
      v-model="showRepair"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
      align-center
    >
      <el-table
        :data="repairList"
        border
        style="margin: 0 auto 40px; width: 96%"
        max-height="300"
      >
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceCode"
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="modelStr"
        />
        <el-table-column
          label="设备图片"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-image
              v-if="!!scope.row.deviceImg"
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="contain"
            />
            <span v-else>-</span>
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="installAddress"
        />
        <el-table-column
          label="物理地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="macAddress"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="logicAddress"
        />
        <el-table-column
          label="IP地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="ipAddress"
        />
        <el-table-column
          label="操作系统"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="osVersion"
        />
        <el-table-column
          label="CPU"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="cpu"
        />
        <el-table-column
          label="品牌"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="brand"
        />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="internalStorage"
        />
        <el-table-column
          label="硬盘"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="disk"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="putTime"
        />
      </el-table>

      <el-form
        ref="repairRef"
        :model="repairForm"
        :rules="repairRules"
        label-width="110px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="报障人" prop="userId">
              <el-select
                v-model="repairForm.userId"
                placeholder="请选择报障人"
                filterable
                clearable
                style="width: 100%"
                @change="repairUserChange"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="报障人联系方式"
              prop="repairPhone"
              label-width="140"
            >
              <el-input
                v-model="repairForm.repairPhone"
                placeholder="请输入报障人联系方式"
                style="width: 94%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障描述" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                placeholder="请输入报障描述"
                style="width: 98%"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="能否正常使用" prop="isNormal">
              <el-select
                v-model="repairForm.isNormal"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="能正常使用" :value="0" />
                <el-option label="不能正常使用" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障附件" prop="annexUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    repairForm.annexUrl = url;
                  }
                "
                :fileSize="10"
                :fileType="['txt', 'doc', 'docx', 'xls', 'xlsx', 'pdf']"
                :type="4"
                :isWorkOrderAnnex="true"
                :limit="1"
                :modelValue="repairForm.annexUrl"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障图片" prop="images">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    repairForm.images = url;
                  }
                "
                :uploadType="4"
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障时间" prop="repairTime">
              <el-date-picker
                type="datetime"
                v-model="repairForm.repairTime"
                placeholder="请选择报障时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled-date="disabledDateFn_repair"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRepairForm" v-throttle
            >确 定</el-button
          >
          <el-button @click="repairCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成激活码 -->
    <el-dialog
      class="custom-dialog"
      v-model="open2"
      width="400px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      @close="handleCancel"
      align-center
    >
      <template #header>
        <div class="dialog-header">
          请选择安装位置
          <el-icon :size="22" style="margin-left: 5px"
            ><QuestionFilled
          /></el-icon>
          <span @click="handleToAdd">没有安装位置？点我新建</span>
        </div>
      </template>
      <div>
        <el-scrollbar height="400px">
          <el-tree
            ref="nodeRef"
            style="max-width: 600px"
            :data="positionNodeList"
            node-key="id"
            :props="positionProps"
            show-checkbox
            check-on-click-node
            :expand-on-click-node="false"
            check-strictly
            default-expand-all
            @check="handleNodeCheck"
          />
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDownload2" v-throttle
            >点击生成激活码</el-button
          >
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发布消息 -->
    <el-dialog
      class="custom-dialog"
      top="30vh"
      title="发布消息"
      v-model="messageOpen"
      width="600px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      align-center
      @close="handleCancelMessage"
    >
      <el-form :model="messageForm" ref="messageRef" :rules="messageRules">
        <el-form-item label="消息类型" prop="isTop">
          <el-radio-group v-model="messageForm.isTop">
            <el-radio label="日常通知（非应用置顶）" :value="false" />
            <el-radio label="霸屏展示（应用置顶）" :value="true" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否定时通知" prop="isNotice">
          <el-switch v-model="messageForm.isNotice"></el-switch>
          <el-date-picker
            v-if="messageForm.isNotice"
            v-model="messageForm.noticeTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetime"
            :disabled-date="disabledDateFn"
            clearable
            style="width: 250px; margin-left: 20px"
            placeholder="请选择定时通知时间"
          />
        </el-form-item>
        <el-form-item label="持续时间" prop="lastTimeNum">
          <el-input-number
            v-model="messageForm.lastTimeNum"
            :min="1"
            :max="9999"
            :value-on-clear="1"
            style="width: 160px; margin-right: 10px"
            @change="timeChange"
            @keydown="SendEventTwo"
          ></el-input-number>
          <el-radio-group
            v-model="messageForm.lastTimeUnit"
            @change="timeChange"
          >
            <el-radio label="秒" :value="1" />
            <el-radio label="分钟" :value="2" />
            <el-radio label="小时" :value="3" />
            <el-radio label="天" :value="4" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="轮播秒数" prop="second">
          <el-input-number
            v-model="messageForm.second"
            :min="1"
            :max="limitSecond"
            @keydown="SendEventTwo"
            :value-on-clear="1"
            style="width: 160px; margin-right: 10px"
          ></el-input-number
          >秒
        </el-form-item>
        <el-form-item label="消息内容" prop="message">
          <el-input
            ref="msgTextareaRef"
            type="textarea"
            v-model="messageForm.message"
            maxlength="50"
            placeholder="请输入消息内容"
            @keydown.enter.native="SendEventOne"
            @paste.native="handlePaste"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleMessage" v-throttle
            >发布</el-button
          >
          <el-button @click="handleCancelMessage">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件分发 -->
    <fileSend
      :fileOpen="fileOpen"
      :sendCodeList="sendCodeList"
      @cancel="fileOpen = false"
    />

    <!-- 点位到期时间详情 -->
    <el-dialog
      class="custom-dialog"
      title="点位到期时间详情"
      v-model="pointVisible"
      width="800px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      align-center
    >
      <el-table :data="pointList" border v-loading="loadingPoint">
        <el-table-column
          label="序号"
          prop="pointIndex"
          align="center"
          min-width="55"
        />
        <el-table-column
          label="点位数量"
          prop="pointNum"
          align="center"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="购买日期"
          prop="buyTime"
          align="center"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="点位时长"
          prop="duration"
          align="center"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="到期时间"
          prop="expireTime"
          align="center"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          prop="remark"
          align="center"
          min-width="150"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        class="pagination-container"
        v-show="pointTotal > 0"
        :total="pointTotal"
        v-model:page="pointQueryParams.current"
        v-model:limit="pointQueryParams.size"
        :background="false"
        :autoScroll="false"
        @pagination="getPointList"
        layout="total,prev,pager,next"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="pointVisible = false">关闭</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisibleImg" width="900">
      <img
        :src="dialogImageUrl"
        alt="Preview Image"
        style="
          display: block;
          max-width: 100%;
          margin: 0 auto;
          object-fit: contain;
          height: 600px;
        "
      />
    </el-dialog>

    <!-- 音量控制对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="volumeDialogVisible"
      :title="`调节音量（已选择${data.tableAllSelectedRow.length}台设备）`"
      width="510px"
      align-center
      :close-on-click-modal="false"
      :close-on-click-escape="false"
    >
      <div class="volume-dialog">
        <div class="volume-tip">
          提示：默认对PC通道音量生效，如安卓通道音量为0，则终端音量为0
        </div>
        <div class="volume-control">
          <span class="volume-label">终端音量：</span>
          <el-slider v-model="data.volumeForm.value" :min="0" :max="100" />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleVolumeCommand" v-throttle
            >发送指令</el-button
          >
          <el-button @click="volumeDialogVisible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 频道切换对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="data.channelDialogVisible"
      title="通道切换"
      width="600px"
      align-center
      :close-on-click-modal="false"
      :close-on-click-escape="false"
    >
      <div class="channel-dialog">
        <div class="channel-grid">
          <div
            v-for="channel in data.channelList"
            :key="channel.name"
            class="channel-item"
            :class="{ active: data.selectedChannel === channel.name }"
            @click="data.selectedChannel = channel.name"
          >
            <div class="channel-icon">
              <component :is="channel.icon" />
            </div>
            <div class="channel-name">{{ channel.name }}</div>
          </div>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button type="primary" @click="handleChannelCommand" v-throttle
            >发送指令</el-button
          >
          <el-button @click="data.channelDialogVisible = false">返回</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 倒计日对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="`倒计日功能 (已选择${data.tableAllSelectedRow.length}台设备)`"
      v-model="data.countdownDialogVisible"
      width="500px"
      @close="resetCountdownForm"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
    >
      <div class="countdown-dialog">
        <div class="countdown-options">
          <el-radio-group v-model="countdownForm.enabled">
            <el-radio :value="true">开启倒计日</el-radio>
            <el-radio :value="false">关闭倒计日</el-radio>
          </el-radio-group>
        </div>

        <div class="countdown-tip" v-if="countdownForm.enabled">
          开启倒计日页面后，桌面会显示倒计日卡片，倒计日结束后桌面卡片自动关闭
        </div>

        <div class="countdown-preview" v-if="countdownForm.enabled">
          <div class="preview-container">
            <div class="preview-header">
              <span>倒计日</span>
              <div class="header-border"></div>
            </div>
            <div class="preview-content">
              <div class="preview-title">
                距离{{ countdownForm.eventName || "事件" }}还有
              </div>
              <div class="countdown-row">
                <span class="preview-number">{{ calculateDays }}</span>
                <span class="preview-unit">天</span>
              </div>
              <div class="preview-date" v-if="countdownForm.targetDate">
                目标日期: {{ formatTargetDate }}
              </div>
            </div>
          </div>
        </div>

        <el-form
          :model="countdownForm"
          label-width="80px"
          v-if="countdownForm.enabled"
        >
          <el-form-item label="事件名称">
            <el-input
              v-model.trim="countdownForm.eventName"
              placeholder="请输入"
              maxlength="20"
              show-word-limit
            />
          </el-form-item>

          <el-form-item label="目标日期">
            <el-date-picker
              v-model="countdownForm.targetDate"
              type="date"
              placeholder="请选择目标日期"
              :disabled="!countdownForm.enabled"
              :disabledDate="disabledCountdownDate"
              value-format="YYYY-MM-DD"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleCountdownCommand" v-throttle>
            发送指令
          </el-button>
          <el-button @click="countdownDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Monitor">
import controlBtns from "../deviceControl/components/controlBtns.vue";
import fileSend from "../deviceControl/components/fileSend.vue";
import locateSel from "../deviceManage/components/locateSel.vue";
import imgUpload from "@/components/ImageUpload";
import fileUpload from "@/components/FileUpload";
import {
  ref,
  onUnmounted,
  onBeforeUnmount,
  onActivated,
  onMounted,
  nextTick,
  onDeactivated,
  markRaw,
  computed,
} from "vue";
import { ElLoading } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import {
  devicePage,
  deviceInfo,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import { getUserList, getUserByRole } from "@/api/system/user";
import { getDeviceType } from "@/api/mediaTeach/type";
import { tenantTree as getTenantTree } from "@/api/park";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import {
  treeToArray,
  treeFindPath,
  timeFormat,
  downloadBlob,
  isValidWindowsAbsolutePath,
  isValidRelativePath,
  sendPointRequest,
  sendPointRequestBatch,
} from "@/utils";
import axios from "axios";
import useUserStore from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import {
  addSchoolDeviceLog,
  deviceStartByWOL,
  deviceCtl,
  deviceCtlMqtt,
  checkDeviceCode,
  sendMessage,
  lockScreenRule,
  lockScreenRuleBatch,
  getLockScreenRule,
  downScreenImg,
  deleteScreenImg,
  sendMessageMqtt,
  getPointInfo,
  getLatestExpirationInfo,
  getPointInfoDetail,
  countdown,
  volumeSize,
} from "@/api/deviceControl";
import { ElMessage, genFileId } from "element-plus";
import WindowsIcon from "@/icons/WindowsIcon.vue";
import AndroidIcon from "@/icons/AndroidIcon.vue";
import HdmiIcon from "@/icons/HdmiIcon.vue";
import VgaIcon from "@/icons/VgaIcon.vue";
import AvIcon from "@/icons/AvIcon.vue";
import TvIcon from "@/icons/TvIcon.vue";
import TypeCIcon from "@/icons/TypeCIcon.vue";

const router = useRouter();
axios.defaults.timeout = 5 * 60 * 1000;

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
function getUrlIp(ip, url = "") {
  let str = "http";
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1?.replace("s", "")?.replace("http://", "");
  } else {
    ip1 = `http://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2?.replace("s", "")?.replace("http://", "");
  } else {
    ip2 = `http://` + ip2;
  }
  return { ip1, ip2, url1, url2 };
}
const route = useRoute();
const { proxy } = getCurrentInstance();
// 添加一个响应式变量来记录是否从工作台跳转而来
const fromWorkbench = ref(false);
const format = (percentage) => (percentage === 100 ? "" : `${percentage}%`);
const ledgerTable = ref(null);
const delHeight = ref(260);
const isRalay = ref(false);
const ledgerList = ref([]);
const repairList = ref([]);
const open = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const showRepair = ref(false);
const isBatch = ref(false);
const ips = ref([]);
const statusList = ref([
  { label: "正常", value: 0, type: "success" },
  { label: "报障中", value: 2, type: "danger" },
  { label: "维修中", value: 1, type: "danger" },
  // { label: "待审核", value: 3, type: "primary" },
  // { label: "已报废", value: 4, type: "warning" },
]);
const runList = ref([
  { label: "关机", value: 0, disabled: false, type: "info" },
  { label: "运行良好", value: 1, disabled: false, type: "success" },
  { label: "网速过慢", value: 2, disabled: false, type: "warning" },
  { label: "GPU过高", value: 3, disabled: false, type: "danger" },
  { label: "磁盘占用率过高", value: 4, disabled: false, type: "danger" },
  { label: "CPU过高", value: 5, disabled: false, type: "danger" },
  { label: "内存过高", value: 6, disabled: false, type: "danger" },
]);
const tagList = ref([]);
const positionTreeList = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});
const queryPositionResult = ref({
  names: [],
  ids: [],
});
const typeList = ref([]);
const total = ref(0);
const title = ref("");
const data = reactive({
  showRefresh: false,
  controlBtnRef: null,
  sendCodeList: [],
  refreshCountdown: new Date().getTime() + 5 * 60 * 1000,
  lockRuleDialogRef: null,
  curRow: {},
  refreshTimer: null,
  msgTextareaRef: null,
  deviceTreeData: [],
  defaultProps: {
    children: "children",
    label: "label",
  },
  loadingPoint: false,
  addTimer: null,
  addSecond: 0,
  pointTotal: 0,
  pointQueryParams: {
    current: 1,
    size: 5,
  },
  pointVisible: false,
  fileList: [],
  pointList: [{}],
  expireDay: 0,
  pointNum: 0,
  hasScreenImg: false,
  isImgChange: false,
  dialogImageUrl: "",
  dialogVisibleImg: false,
  isAllQuery: false,
  ruleOpen: false,
  lockRuleForm: {
    num: 10,
    unit: 1,
    timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
    timeList: ["08:00:00", "18:00:00"],
    lockImg: 0,
    lockUrl: "",
    isLock: false,
    isAutoLock: false,
    isDisable: false,
    selectedDevices: "",
    deviceIds: [],
    deviceCodes: [],
  },
  lockRuleRef: null,
  limitSecond: 1,
  fixRef: null,
  abortController: null,
  uploading: false,
  progress: 0,
  installOpen: false,
  installRef: null,
  installTitle: "软件安装",
  uploadRef: null,
  fileRef: null,
  messageRef: null,
  fileOpen: false,
  messageOpen: false,
  messageForm: {
    isTop: false,
    isNotice: false,
    noticeTime: "",
    lastTimeNum: 1,
    lastTimeUnit: 1,
    second: 1,
    message: "",
  },
  positionNodeResultList: [
    {
      names: [],
      ids: [],
    },
  ],
  positionNodeList: [],
  nodeRef: null,
  remoteList: [
    { icon: "SwitchButton", type: "danger", label: "远程关机", value: 0 },
    { icon: "Monitor", type: "success", label: "远程开机", value: 1 },
    { icon: "RefreshRight", type: "primary", label: "远程重启", value: 2 },
    { icon: "AlarmClock", type: "warning", label: "远程定时关机", value: 3 },
    { icon: "SortUp", type: "success", label: "远程开启wifi", value: 4 },
    { icon: "SortDown", type: "info", label: "远程关闭wifi", value: 5 },
    { icon: "Brush", type: "danger", label: "远程清除缓存", value: 6 },
    { icon: "Lock", type: "warning", label: "锁屏", value: 7 },
    { icon: "Unlock", type: "primary", label: "解锁", value: 8 },
  ],
  remoteSendList: [
    { label: "发布消息", value: 0 },
    { label: "文件分发", value: 1 },
  ],
  screenSettingsList: [
    // 新增大屏设置选项列表
    { label: "调节音量", value: 0 },
    // { label: "频道切换", value: 1 }, // 暂时隐藏
    { label: "倒计日功能", value: 2 },
  ],
  operationParams: {
    remote: "",
    remoteSend: "",
    screenSettings: "", // 新增大屏设置选项
  },
  userList: [],
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // ��存表格勾选的行数��
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  ledgerTotal: 0,
  ledgerErrorTotal: 0,
  statusObj: {
    0: { label: "正常", type: "success" },
    1: { label: "维修中", type: "danger" },
    2: { label: "报障中", type: "warning" },
    3: { label: "待审核", type: "primary" },
    4: { label: "已报废", type: "info" },
  },
  fixForm: {
    deviceCode: "",
    time: "",
  },
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  },
  queryParams: {
    smartScreen: 1,
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 5,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: "1",
    deviceStatus: "",
    positionIds: [],
    putTime: "",
    status: [],
  },
  rules: {
    time: [
      {
        required: true,
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
  },
  repairForm: {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  },
  messageRules: {
    message: [
      {
        required: true,
        message: "消息内容不能为空",
        trigger: "blur",
      },
    ],
    isNotice: [
      {
        validator: validateNotice,
        trigger: ["change", "blur"],
      },
    ],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报修人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      {
        required: true,
        message: "报修人联系方式不能为空",
        trigger: ["blur", "change"],
      },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    isNormal: [
      {
        required: true,
        message: "请选择设备能否正常使用",
        trigger: ["blur", "change"],
      },
    ],
    remark: [
      { required: true, message: "故障描述不能为空", trigger: "blur" },
      { max: 200, message: "故障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        message: "故障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
    repairTime: [
      {
        required: true,
        validator: validateRepairTime,
        trigger: ["blur", "change"],
      },
    ],
  },
  volumeDialogVisible: false, // 新增音量控制对话框显示状态
  volumeValue: 50, // 新增音量值
  channelDialogVisible: false, // 频道切换对话框显示状态
  selectedChannel: "", // 当前选中的频道
  channelList: [
    { name: "PC", icon: markRaw(WindowsIcon) },
    { name: "Android", icon: markRaw(AndroidIcon) },
    { name: "HDMI1", icon: markRaw(HdmiIcon) },
    { name: "HDMI2", icon: markRaw(HdmiIcon) },
    { name: "HDMI3", icon: markRaw(HdmiIcon) },
    { name: "HDMI4", icon: markRaw(HdmiIcon) },
    { name: "VGA1", icon: markRaw(VgaIcon) },
    { name: "VGA2", icon: markRaw(VgaIcon) },
    { name: "VGA3", icon: markRaw(VgaIcon) },
    { name: "AV", icon: markRaw(AvIcon) },
    { name: "ATV", icon: markRaw(TvIcon) },
    { name: "DTV", icon: markRaw(TvIcon) },
    { name: "TYPE-C1", icon: markRaw(TypeCIcon) },
    { name: "TYPE-C2", icon: markRaw(TypeCIcon) },
  ],
  countdownDialogVisible: false, // 倒计日对话框显示状态
  countdownForm: {
    enabled: true, // 是否开启倒计日
    eventName: "", // 事件名称
    targetDate: "", // 目标日期
  },
  volumeForm: {
    value: 20, // 默认音量值
  },
  lockRuleExpandedKeys: ref([1, 2]),
  lockRuleDeviceTreeRef: ref(null),
});

const {
  showRefresh,
  controlBtnRef,
  tableAllSelectedRow,
  sendCodeList,
  refreshCountdown,
  msgTextareaRef,
  loadingPoint,
  addTimer,
  addSecond,
  pointQueryParams,
  pointTotal,
  pointVisible,
  pointList,
  expireDay,
  pointNum,
  dialogImageUrl,
  dialogVisibleImg,
  isAllQuery,
  limitSecond,
  fixRef,
  messageRules,
  fileOpen,
  messageRef,
  messageForm,
  messageOpen,
  userList,
  positionNodeList,
  positionNodeResultList,
  nodeRef,
  operationParams,
  remoteList,
  remoteSendList,
  screenSettingsList,
  statusObj,
  queryParams,
  form,
  rules,
  fixForm,
  tableAllSelectedId,
  repairForm,
  repairRules,
  ledgerErrorTotal,
  ledgerTotal,
  volumeDialogVisible,
  countdownDialogVisible,
  countdownForm,
  deviceTreeData,
} = toRefs(data);

function validateRepairTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择报修时间"));
  } else if (new Date(value).getTime() > new Date().getTime()) {
    callback(new Error("报修时间不能选择未来时间"));
  } else {
    callback();
  }
}

function refreshData() {
  getList();
  refreshCountdown.value = new Date().getTime() + 5 * 60 * 1000;
}

const handleRunStatus = (val) => {
  if (val.length > 0 && val[0] === 0) {
    runList.value = runList.value.map((item) => {
      item.disabled = item.value !== 0;
      return item;
    });
  } else if (val.length > 0 && val[0] === 1) {
    runList.value = runList.value.map((item) => {
      item.disabled = item.value !== 1;
      return item;
    });
  } else if (val.length > 0 && val[0] > 1) {
    runList.value = runList.value.map((item) => {
      item.disabled = item.value < 2;
      return item;
    });
  } else {
    runList.value = runList.value.map((item) => {
      item.disabled = false;
      return item;
    });
  }
  console.log(val, runList.value);

  handleQuery();
};

const isExternalPark = ref(false);

function SendEventTwo(e) {
  if (e.key === "." || e.key === "," || e.key === "-" || e.key === "+") {
    e.preventDefault();
  }
}

// 处理节点勾选事件
const handleNodeCheck = (node, checkedStatus) => {
  // 递归勾选所有子节点
  const checkChildren = (node, checked) => {
    if (node.children) {
      node.children.forEach((child) => {
        nodeRef.value.setChecked(child.id, checked); // 勾选当前子节点
        if (child.children) checkChildren(child, checked); // 递归处理孙节点
      });
    }
  };
  // console.log(node, checkedStatus);
  // 获取当前节点的选中状态
  const isChecked = nodeRef.value.getCheckedKeys().includes(node.id);

  // 当父节点被勾选时，递归勾选所有子节点
  if (isChecked && node.children) {
    checkChildren(node, true);
  }
};

function handlePaste(event) {
  event.preventDefault();
  // 获取粘贴板内容并移除换行符
  const pasteData = (event.clipboardData || window.clipboardData).getData(
    "text"
  );
  const cleanText = pasteData?.replace(/[\r\n]/g, "");

  // 插入处理后的文本
  insertTextAtCursor(cleanText);
}

// 在光标位置插入文本
const insertTextAtCursor = (text) => {
  const inputElement = document.activeElement;
  const startPos = inputElement.selectionStart;
  const endPos = inputElement.selectionEnd;

  // 计算新值（考虑当前选择区域）
  const newValue =
    messageForm.value.message.substring(0, startPos) +
    text +
    messageForm.value.message.substring(endPos);

  // 确保不超过50字符
  if (newValue.length <= 50) {
    messageForm.value.message = newValue;

    // 更新光标位置（放在 nextTick 中确保 DOM 更新）
    setTimeout(() => {
      inputElement.setSelectionRange(
        startPos + text.length,
        startPos + text.length
      );
    }, 0);
  } else {
    // 超过长度时截取有效部分
    const allowedLength =
      50 - messageForm.value.message.length + (endPos - startPos);
    const partialText = text.substring(0, allowedLength);

    messageForm.value.message =
      messageForm.value.message.substring(0, startPos) +
      partialText +
      messageForm.value.message.substring(endPos);

    setTimeout(() => {
      inputElement.setSelectionRange(
        startPos + partialText.length,
        startPos + partialText.length
      );
    }, 0);
  }
};

async function getPointList() {
  try {
    loadingPoint.value = true;
    await getPointInfoDetail(pointQueryParams.value)
      .then((res) => {
        console.log(res, "点位信息");
        if (res.code == 200) {
          const { records, total } = res.data;
          const { current, size } = pointQueryParams.value;
          pointTotal.value = total || 0;
          pointList.value =
            records?.map((item, index) => {
              let duration =
                Math.ceil(
                  (new Date(item.expireTime).getTime() -
                    new Date(item.buyTime).getTime()) /
                    (1000 * 60 * 60 * 24)
                ) || 0;
              return {
                pointIndex: index + 1 + (current * size - size),
                pointNum: item.pointNum || 0,
                buyTime: item.buyTime || "-",
                remark: item.remark || "-",
                duration: duration < 0 ? "已过期" : duration + "天",
                expireTime: item.expireTime || "-",
              };
            }) || [];
        }
      })
      .finally(() => (loadingPoint.value = false));
  } catch (error) {
    console.log(error);
    loadingPoint.value = false;
  }
}

async function handlePoint() {
  try {
    pointQueryParams.value.current = 1;
    await getPointList();
    pointVisible.value = true;
  } catch {}
}

function handleParkChange(event) {
  if (route.path === "/deviceLedger/ledger") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

function SendEventOne(event) {
  // console.log(messageForm.value.message);
  if (event.key === "Enter" || event.keyCode === 13) {
    event.preventDefault(); // 打印这个为undefined
    return false;
  }
}

const timeChange = () => {
  let second =
    [1, 60, 3600, 86400][messageForm.value.lastTimeUnit - 1] *
    messageForm.value.lastTimeNum;
  // console.log(
  //   messageForm.value.lastTimeUnit,
  //   second,
  //   messageForm.value.lastTimeNum
  // );
  limitSecond.value = second > 9999 ? 9999 : second;
};

function validateTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择定时关机时间"));
  } else if (new Date(value).getTime() < new Date().getTime()) {
    callback(new Error("定时关机时间不能小于当前时间"));
  } else {
    callback();
  }
}

function validateNotice(rule, value, callback) {
  if (value && !data.messageForm.noticeTime) {
    callback(new Error("请选择定时通知时间"));
  } else if (
    value &&
    new Date(data.messageForm.noticeTime).getTime() < new Date().getTime()
  ) {
    callback(new Error("定时通知时间不能小于当前时间"));
  } else {
    callback();
  }
}

function handleBatchFile() {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  sendCodeList.value = tableAllSelectedRow.value.map((item) => item.deviceCode);
  data.fileOpen = true;
  // console.log(sendCodeList.value, tableAllSelectedRow.value, "sendCodeList");
}

function handleBatchMessage() {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  data.messageOpen = true;
}

function handleBatchInstall(type) {}

function handleMessage() {
  data.messageRef.validate(async (valid) => {
    if (valid) {
      proxy.$modal.loading();
      let arr = [],
        arrMqtt = [],
        flag = false,
        mqttFlag = false,
        msg = "",
        mqttMsg = "";
      data.tableAllSelectedRow.map((item) => {
        !item.isMqtt
          ? arr.push(item.deviceCode)
          : arrMqtt.push(item.deviceCode);
      });
      let d = {
        ...data.messageForm,
      };
      if (arr.length) {
        d.deviceCodeList = arr;
        if (!d.isNotice) delete d.noticeTime;
        console.log("发布消息传参", d);
        await sendMessage(d).then((response) => {
          // console.log("发布消息", response);
          if (response.code == 200) {
            flag = true;
            msg = response.data || "";
          }
        });
      }
      if (arrMqtt.length) {
        d.deviceCodeList = arrMqtt;
        if (!d.isNotice) delete d.noticeTime;
        console.log("发布消息传参mqtt", d);
        await sendMessageMqtt(d).then((response) => {
          // console.log("发布消息", response);
          if (response.code == 200) {
            mqttFlag = true;
            mqttMsg = response.data || "";
          }
        });
      }

      if (flag || mqttFlag) {
        messageResponse(msg, mqttMsg);
        handleCancelMessage();
      } else {
        proxy.$modal.msgSuccess("操作失败");
      }

      proxy.$modal.closeLoading();
    }
  });
}

// 发布消息弹出成功失败条数及失败设备
function messageResponse(msg, mqttMsg) {
  // console.log(msg, mqttMsg);
  try {
    let total = 0,
      successNum = 0,
      failNum = 0,
      codeList = [];
    if (msg) {
      let arr = msg?.split("，");
      // console.log(arr[0], arr[1], arr[2], arr[3]);
      total += arr[0]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      successNum += arr[1]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      failNum += arr[2]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      codeList = [...codeList, ...(arr[3]?.split(": ")[1]?.split("；") || [])];
      // console.log(
      //   total,
      //   successNum,
      //   failNum,
      //   codeList,
      //   arr[3]?.split(": ")[1]?.split("；")
      // );
    }
    if (mqttMsg) {
      let arr = mqttMsg?.split(", ");
      total += arr[0]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      successNum += arr[1]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      failNum += arr[2]?.split(": ")[1]?.replace("条", "") * 1 || 0;
      codeList = [...codeList, ...(arr[3]?.split(": ")[1]?.split("；") || [])];
      // console.log(
      //   total,
      //   successNum,
      //   failNum,
      //   codeList,
      //   arr[3]?.split(": ")[1]?.split("；")
      // );
    }
    proxy.$modal.msgSuccess(
      `消息共发送：${total}条，成功：${successNum}条，失败：${failNum}条${
        codeList.length > 0 ? `，消息发送失败设备：${codeList.join("；")}` : ""
      }`
    );
  } catch (e) {
    console.log("捕获错误信息", e);
    proxy.$modal.msgSuccess("定时指令发送成功");
  }
}

function handleCancelMessage() {
  proxy.resetForm("messageRef");
  data.messageForm.lastTimeUnit = 1;
  data.messageForm.noticeTime = "";
  data.messageOpen = false;
}

/** 查询用户列表 */
async function getUsers() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    console.log("拥有角色的用户列表", resp);
    if (resp.data) {
      data.userList = resp.data.records;
    }
  });
}

const handleToAdd = () => {
  router.push("/deviceLedger/baseInfo/location");
};

const handleCancel = () => {
  data.nodeRef.setCheckedNodes([]);
  open2.value = false;
};

function handleDownload2() {
  let checkList = data.nodeRef.getCheckedNodes();
  if (checkList.length == 0) {
    proxy.$modal.msgWarning("请选择安装位置");
    return;
  }
  positionNodeResultList.value = [];
  handlePositionNode(checkList);
  // console.log(positionNodeResultList.value, "data.positionNodeResultList");
  const positionList = positionNodeResultList.value.map((item) => {
    return {
      position: item.names.join("-"),
      positionId: item.ids.join(","),
    };
  });
  // console.log("positionList", positionList);

  proxy.$modal.loading();
  checkDeviceCode({ positionList })
    .then((res) => {
      exportDeviceCode({ positionList })
        .then((response) => {
          proxy.$modal.closeLoading();
          // console.log("blob", response);
          downloadBlob(response, "application/vnd.ms-excel", "设备激活码");
          sendPointRequest({
            event: "Click",
            eventDescribe: "点击生成激活码",
            content: "",
            num: 1,
          });
          proxy.$modal.msgSuccess("操作成功");
          handleCancel();
        })
        .catch((err) => {
          proxy.$modal.closeLoading();
        });
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

function handlePositionNode(checkList) {
  positionNodeResultList.value = [{ ids: [], names: [] }];
  for (let i = 0; i < checkList.length; i++) {
    positionNodeResultList.value[i] = {
      ids: [],
      names: [],
    };

    positionNodeResultList.value[i].ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == checkList[i].id
    );
    const arr = positionNodeResultList.value[i].ids;
    for (let j = 0; j < arr.length; j++) {
      positionNodeResultList.value[i].names[j] = positionList.value.find(
        (node) => node.id == arr[j]
      ).name;
    }
  }
  // console.log("positionResultNodeList ==> ", positionNodeResultList.value);
}

const repairUserChange = (val) => {
  const idx = data.userList.findIndex((_) => _.userId == val);
  // console.log("报障人更改", val, idx);
  data.repairForm.repairPhone = data.userList[idx].phoneNumber || "";
  data.repairForm.repairName = data.userList[idx].nickName || "";
};

// 限制报修日期
const disabledDateFn_repair = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

// 限制日期
const disabledDateFn = (date) => {
  //   const today = formatDate(new Date().getTime())
  if (date.getTime() + 86400000 < new Date().getTime()) {
    // console.log('date', date, date.getTime(), (date.getTime() + 86400000), new Date().getTime())
    return true;
  }
  return false;
};

const checkFixTime = () => {
  fixRef.value.validate((valid) => {
    if (valid) {
      const time = fixForm.value.time?.replace("S", " ")?.replace("Z", "");
      if (new Date(time).getTime() < new Date().getTime()) {
        proxy.$modal.msgWarning("选择的时间不能小于当前时间");
        return;
      }
      isBatch.value
        ? handleBatchRemote(1, 3)
        : handleRemote(fixForm.value, 3, "time");
    }
  });
};

function handleOpeartionRemote(val) {
  // console.log("远程操作", val);
  switch (val) {
    // 批量远程关机
    case 0:
      handleBatchRemote(1, 0);
      break;
    //批量远程开机
    case 1:
      handleBatchRemote(2, 0);
      break;
    // 批量远程重启
    case 2:
      handleBatchRemote(1, 1);
      break;
    // 批量远程定时关机
    case 3:
      handleBatchFixed();
      break;
    // 批量远程开启WIFI
    case 4:
      handleBatchWifi(1, 1);
      break;
    // 批量远程关闭WIFI
    case 5:
      handleBatchWifi(1, 0);
      break;
    // 批量远程清除缓存
    case 6:
      handleBatchClear(1);
      break;
    // 批量远程锁屏
    case 7:
      handleBatchLock(1, 0);
      break;
    // 批量远程解锁
    case 8:
      handleBatchLock(1, 1);
      break;
    default:
      break;
  }
  data.operationParams.remote = "";
}

function handleOpeartionRemoteSend(val) {
  // console.log("远程传输操作", val);
  switch (val) {
    // 批量远程发布消息
    case 0:
      handleBatchMessage();
      break;
    //批量远程文件分发
    case 1:
      handleBatchFile();
      break;
    // 批量远程软件安装
    case 2:
      handleBatchInstall(1);
      break;
    // 批量远程软件卸载
    case 3:
      handleBatchInstall(0);
      break;
    default:
      break;
  }
  data.operationParams.remoteSend = "";
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

async function getTagList() {
  try {
    await getDeviceTag().then((response) => {
      tagList.value = response.data;
    });
  } catch (e) {
    console.log(e, "获取标签失败");
  }
}

async function getOptions() {
  try {
    await getDeviceType().then((response) => {
      typeList.value = response.data.reduce((res, cur) => {
        if (cur.typeName == "智慧大屏") queryParams.value.typeId = cur.id;
        res.push({
          ...cur,
        });
        return res;
      }, []);
    });
  } catch (e) {
    console.log(e, "获取设备类型失败");
  }
}

function getPosition(d) {
  data.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

async function getPositionTreeList() {
  try {
    await getPositionTree({}).then((response) => {
      let tree = JSON.parse(JSON.stringify(response.data));
      positionTreeList.value = response.data;
      positionList.value = treeToArray(response.data);
      data.positionNodeList = addAttr(tree);
      // console.log("positionList ==>", positionList.value);
    });
  } catch (e) {
    console.log(e, "获取位置树失败");
  }
}

function addAttr(data, num = 0) {
  num++;
  for (var j = 0; j < data.length; j++) {
    // data[j].disabled = num != 3 || data[j].status == 1;
    data[j].disabled = data[j].status == 1;
    // console.log(num, data[j]);
    if (data[j].children.length > 0) {
      addAttr(data[j].children, num);
    }
  }
  return data;
}

function handleRepair(row) {
  isBatch.value = !row.deviceId;
  if (tableAllSelectedId.value.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  if (data.tableAllSelectedRow.findIndex((_) => _.deviceStatus > 2) != -1) {
    proxy.$modal.msgWarning("待审核/已报废设备不能进行此操作");
    return;
  }
  repairReset();
  repairList.value = row.deviceId ? [row] : data.tableAllSelectedRow;
  getUsers();
  // console.log(repairList.value)
  showRepair.value = true;
}

/** 查询设备列表 */
function getList() {
  console.log(queryParams.value, "queryParams.value");
  data.isAllQuery = false;
  loading.value = true;
  devicePage(queryParams.value).then((response) => {
    console.log(response, "管控");

    const { page, abnormalTerminal, normalTerminal } = response.data;
    ledgerList.value = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        isOff,
        runStatus,
        ralayHost,
        deviceCode,
        ipAddress,
        logicAddress,
        macAddress,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tenantName,
      } = cur;
      res.push({
        ...cur,
        status: !!isOff ? 0 : !!runStatus ? 2 : 1,
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        deviceCodeStr: deviceCode || "-",
        ipAddressStr: ipAddress || "-",
        ralayHostStr: ralayHost || "-",
        logicAddressStr: logicAddress || "-",
        macAddressStr: macAddress || "-",
        tenantNameStr: tenantName || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        runStatusList: runStatus.filter((_) => _ < 7),
      });
      return res;
    }, []);
    // console.log(ledgerList.value);
    console.log(
      "筛选条件参数，当前页台账列表数据",
      queryParams.value,
      ledgerList.value
    );
    total.value = page.total;
    ledgerErrorTotal.value = abnormalTerminal;
    ledgerTotal.value = normalTerminal;
    loading.value = false;
    nextTick(() => {
      ledgerList.value.forEach((item) => {
        if (data.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, data.tableAllSelectedId)
          ledgerTable.value?.toggleRowSelection(item, true);
        } else {
          ledgerTable.value?.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
  });
  devicePage({
    ...queryParams.value,
    current: 1,
    size: 9999999,
    typeId: 1,
    smartScreen: 1,
    abnormalInterruptionChannel: queryParams.value.abnormalInterruptionChannel,
  }).then((res) => {
    console.log("所有台账数据", res);
    data.tableData_all = res.data.page.records;
    console.log(data.tableData_all);
    deviceTreeData.value = buildTree(res.data.page.records);
  });
}

// 构建树形结构函数
function buildTree(data) {
  const root = { label: "全选", id: 0, children: [] };
  let nextId = 1;
  const nodeMap = new Map();

  // 按层级深度排序（浅→深）
  data.sort((a, b) => {
    const aDepth = a.installAddress ? a.installAddress.split("-").length : 0;
    const bDepth = b.installAddress ? b.installAddress.split("-").length : 0;
    return aDepth - bDepth;
  });

  // 第一步：创建所有分支节点
  data.forEach((item) => {
    if (!item.installAddress) return;

    const parts = item.installAddress.split("-");
    let currentPath = "";
    let parentNode = root;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      currentPath = currentPath ? `${currentPath}-${part}` : part;

      if (!nodeMap.has(currentPath)) {
        const newNode = {
          label: part,
          id: nextId++,
          children: [],
        };

        parentNode.children.push(newNode);
        nodeMap.set(currentPath, newNode);
        parentNode = newNode;
      } else {
        parentNode = nodeMap.get(currentPath);
      }
    }
  });

  // 第二步：添加数据节点
  data.forEach((item) => {
    if (!item.installAddress) {
      root.children.push({
        label: item.deviceCode.toString(),
        id: nextId++,
        children: [],
      });
      return;
    }

    const parts = item.installAddress.split("-");
    const fullPath = item.installAddress;
    const parentPath = parts.slice(0, -1).join("-");

    // 查找父节点
    const parentNode =
      nodeMap.get(parentPath) || nodeMap.get(fullPath)
        ? nodeMap.get(fullPath)
        : root;

    // 检查是否已存在相同数据节点
    const exists = parentNode.children.some((child) =>
      child.label.includes(item.deviceCode.toString())
    );

    if (!exists) {
      const isLeaf = !nodeMap.has(fullPath);
      const leafLabel = isLeaf
        ? `${parts[parts.length - 1]}-${item.deviceCode}`
        : item.deviceCode.toString();

      parentNode.children.unshift({
        label: leafLabel,
        id: nextId++,
        children: [],
      });
    }
  });

  return [root];
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  open3.value = false;
  reset();
}
/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  fixForm.value = {
    ipAddress: "",
    time: null,
  };
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "1",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
  proxy.resetForm("fixRef");
}
/** 表单重置 */
function repairReset() {
  proxy.resetForm("repairRef");
  repairForm.value = {
    type: 1,
    userId: useUserStore().userId + "",
    repairName: useUserStore().nickName,
    repairPhone: useUserStore().phonenumber,
    repairTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    deviceCodeList: [],
    remark: "",
    resource: "管理后台",
    channel: "管理员主动报障",
    images: "",
  };
}
function handleCheck(row) {
  let sendData = {
    userEvents: [
      {
        event: "Click",
        eventDescribe: "点击单个设备操作集控按钮",
        content: 0,
        num: 1,
        deviceCode: row.deviceCode,
      },
    ],
  };
  // console.log(sendData, "单个埋点");
  sendPointRequestBatch(sendData);
  router.push({
    path: "/deviceLedger/deviceInfo",
    query: {
      id: row.deviceId,
      type: 0,
      from: 1,
    },
  });
}
/** 搜索按钮操作 */
function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.positionIds = d?._checkList;
  }
  queryParams.value.current = 1;
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 0;
  getList();
}

/** 全部搜索按钮操作 */
function handleAllQuery(val) {
  loading.value = true;
  proxy.resetForm("queryRef");
  data.isAllQuery = true;
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  devicePage({
    current: 1,
    size: 9999999,
    typeId: 1,
    smartScreen: 1,
    abnormalInterruptionChannel: val,
  })
    .then((resp) => {
      // console.log("所有台账数据", resp);
      ledgerList.value = resp.data.page.records.reduce((res, cur) => {
        let {
          deviceName,
          deviceType,
          isOff,
          runStatus,
          ralayHost,
          deviceCode,
          ipAddress,
          logicAddress,
          macAddress,
          model,
          osVersion,
          cpu,
          internalStorage,
          disk,
          brand,
          tenantName,
        } = cur;
        res.push({
          ...cur,
          status: !!isOff ? 0 : !!runStatus ? 2 : 1,
          deviceNameStr: deviceName || "-",
          deviceTypeStr: deviceType || "-",
          deviceCodeStr: deviceCode || "-",
          ipAddressStr: ipAddress || "-",
          ralayHostStr: ralayHost || "-",
          logicAddressStr: logicAddress || "-",
          macAddressStr: macAddress || "-",
          tenantNameStr: tenantName || "-",
          modelStr: model || "-",
          osVersionStr: osVersion || "-",
          cpuStr: cpu || "-",
          internalStorageStr: internalStorage || "-",
          diskStr: disk || "-",
          brandStr: brand || "-",
          runStatusList: runStatus.filter((_) => _ < 7),
        });
        return res;
      }, []);
      if (val) {
        ledgerErrorTotal.value = ledgerList.value.length;
      } else {
        ledgerTotal.value = ledgerList.value.length;
      }
    })
    .finally(() => (loading.value = false));
}

/** 异常搜索按钮操作 */
function handleErrorQuery() {
  // resetQuery()
  proxy.resetForm("queryRef");
  queryParams.value.current = 1;
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.size = 5;
  queryParams.value.positionIds = [];
  proxy.$refs.locateRef.resetQuery();
}

/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      ledgerTable.value.setCurrentRow(null);
      ledgerTable.value.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.deviceId);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      ledgerTable.value.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    ledgerTable.value.setCurrentRow(row);
    ledgerTable.value.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    if (valid) {
      repairForm.value.deviceCodeList =
        repairList.value.map((item) => item.deviceCode) || [];
      repairForm.value.images = repairForm.value.images.split(",") || "";
      deviceRepair(repairForm.value).then((res) => {
        let sendData = {
          userEvents: repairForm.value.deviceCodeList.map((item) => {
            return {
              event: "Click",
              eventDescribe: `${
                !!isBatch.value ? "点击批量报障" : "点击单个设备操作集控按钮"
              }`,
              content: `${!!isBatch.value ? "" : 9}`,
              num: 1,
              deviceCode: item,
            };
          }),
        };
        // console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        proxy.$modal.msgSuccess("报障成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      data.tableAllSelectedId.push(item.deviceId);
      data.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.deviceId);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = ledgerList.value;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        data.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 批量操作前的检测 */
const checkBatch = () => {
  // console.log(data.tableAllSelectedRow, "data.tableAllSelectedRow");
  let newArr = [];
  data.tableAllSelectedId.map((item) => {
    data.tableData_all.map((item2) => {
      if (item2.deviceId == item) {
        newArr.push(item2);
      }
    });
  });
  let arr = [],
    flag = false;
  ips.value = [];
  let narr = [],
    recoverArr = [];
  newArr.map((item) => {
    if (item.runStatus.indexOf(0) != -1) {
      // 关机
      narr.push(item.deviceName);
      flag = true;
    } else if (item.winStatus > 1) {
      recoverArr.push(item.deviceName);
    } else {
      arr.push(item.deviceName);
      ips.value.push(item.ipAddress);
    }
  });
  return { arr, narr, flag, recoverArr };
};

/** 批量定时关机按钮操作 */
const handleBatchFixed = () => {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  isBatch.value = true;
  title.value = "批量定时关机";
  open.value = true;
};

const handleFixed = (row) => {
  isBatch.value = false;
  title.value = "定时关机";
  fixForm.value.isMqtt = row.isMqtt;
  fixForm.value.ipAddress = row.ipAddress;
  fixForm.value.ralayHost = row.ralayHost;
  fixForm.value.deviceCode = row.deviceCode;
  open.value = true;
};

/** 批量重启/关闭/定时关闭按钮操作 */
const handleBatchRemote = async (isBatch, type, row) => {
  const { arr, narr, flag, recoverArr } = checkBatch();

  if (isBatch == 2) {
    if (arr.length > 0 || recoverArr.length > 0) {
      proxy.$modal.msgWarning(
        "设备存在开机状态，无法进行此操作"
      );
      return;
    }
    proxy.$modal
      .confirm(`是否确认批量开机设备名称为【${narr.join("、")}】的设备？`)
      .then(() => {
        proxy.$modal
          .confirm("请确认设备是否支持远程开机")
          .then(async () => {
            let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
            let sendData = {
              userEvents: rows.map((item) => {
                return {
                  event: "Click",
                  eventDescribe: "选中设备后点击下方悬浮按钮集控",
                  content: 0,
                  num: 1,
                  deviceCode: item.deviceCode,
                };
              }),
            };
            // console.log(sendData, "批量埋点");
            sendPointRequestBatch(sendData);
            addSchoolDeviceLog({
              logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
              logContent: `远程开机`, // 日志内容
              deviceNum: rows.length, // 操作设备数量
            });
            let success = [],
              fail = [];
            for (let j = 0; j < rows.length; j++) {
              try {
                proxy.$modal.loading();
                await deviceStartByWOL({ deviceCode: rows[j].deviceCode })
                  .then((res) => {
                    console.log(res);
                    success.push(rows[j].deviceCode);
                  })
                  .catch(() => {
                    fail.push(rows[j].deviceCode);
                  })
                  .finally(() => {
                    proxy.$modal.closeLoading();
                  });
              } catch (error) {}
            }
            proxy.$modal.msgSuccess(
              fail.length == 0
                ? "操作成功"
                : `开机指令共发送${rows.length}条，成功${
                    success.length
                  }条，失败${fail.length}条，指令发送失败设备：${fail.join(
                    "、"
                  )}`
            );
          })
          .catch((err) => {});
      })
      .catch((err) => {});
    return;
  }

  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }

  if (type !== 3) {
    if (!!isBatch) {
      proxy.$modal
        .confirm(
          `是否确认批量${!!type ? "重启" : "关闭"}设备名称为【${arr.join(
            "、"
          )}】的设备？`
        )
        .then(async function () {
          let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
          let sendData = {
            userEvents: rows.map((item) => {
              return {
                event: "Click",
                eventDescribe: "选中设备后点击下方悬浮按钮集控",
                content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
                num: 1,
                deviceCode: item.deviceCode,
              };
            }),
          };
          console.log(sendData, "批量埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
            logContent: `远程${
              type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
            }`, // 日志内容
            deviceNum: rows.length, // 操作设备数量
          });
          for (let j = 0; j < rows.length; j++) {
            try {
              handleRemote(
                {
                  ipAddress: rows[j].ipAddress,
                  time: type === 3 ? fixForm.value.time : null,
                  deviceCode: rows[j].deviceCode,
                  isMqtt: rows[j].isMqtt,
                },
                type
              );
            } catch (error) {}
          }
        })
        .catch(() => {});
    } else {
      proxy.$modal
        .confirm(
          `是否确认${!!type ? "重启" : "关闭"}设备名称为【
            ${row.deviceName}】的设备？`
        )
        .then(async function () {
          try {
            let sendData = {
              userEvents: [
                {
                  event: "Click",
                  eventDescribe: "点击单个设备操作集控按钮",
                  content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
                  num: 1,
                  deviceCode: row.deviceCode,
                },
              ],
            };
            console.log(sendData, "单个埋点");
            sendPointRequestBatch(sendData);
            addSchoolDeviceLog({
              logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: row.deviceCode, // 操作设备编号
              logContent: `远程${
                type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
              }`, // 日志内容
              deviceNum: 1, // 操作设备数量
            });
            handleRemote(row, type);
          } catch (error) {}
        })
        .catch(() => {});
    }
  } else {
    // 批量定时关机
    let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
    let sendData = {
      userEvents: rows.map((item) => {
        return {
          event: "Click",
          eventDescribe: "选中设备后点击下方悬浮按钮集控",
          content: 3,
          num: 1,
          deviceCode: item.deviceCode,
        };
      }),
    };
    console.log(sendData, "批量埋点");
    sendPointRequestBatch(sendData);
    addSchoolDeviceLog({
      logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
      deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
      logContent: `远程定时关机`, // 日志内容
      deviceNum: rows.length, // 操作设备数量
    });
    for (let j = 0; j < rows.length; j++) {
      try {
        handleRemote(
          {
            ipAddress: rows[j].ipAddress,
            time: type === 3 ? fixForm.value.time : null,
            deviceCode: rows[j].deviceCode,
            isMqtt: rows[j].isMqtt,
          },
          type
        );
      } catch (error) {}
    }
  }
  cancel();
};

/** 批量解锁/锁屏 type: 0锁屏 1解锁*/
const handleBatchLock = (isBatch, type, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  if (!!isBatch) {
    proxy.$modal
      .confirm(
        `是否确认批量对设备名称为【${arr.join("、")}】的设备进行${
          type == 0 ? "锁屏" : "解锁"
        }？`
      )
      .then(function () {
        let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
        let sendData = {
          userEvents: rows.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: `${type == 0 ? 7 : 8}`,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        for (let j = 0; j < rows.length; j++) {
          if (type == 1) {
            addSchoolDeviceLog({
              logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: rows[j].deviceCode, // 操作设备编号
              logContent: `设备后台解锁`, // 日志内容
              deviceNum: 1, // 操作设备数量
            });
          }
          try {
            handleLock(
              {
                ipAddress: rows[j].ipAddress,
                deviceCode: rows[j].deviceCode,
                isMqtt: rows[j].isMqtt,
              },
              type
            );
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    proxy.$modal
      .confirm(
        `是否确认对设备名称为【${row.deviceName}】的设备进行${
          type == 0 ? "锁屏" : "解锁"
        }？`
      )
      .then(function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: `${type == 0 ? 7 : 8}`,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `设备后台${type == 0 ? "锁屏" : "解锁"}`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleLock(row, type);
        } catch (error) {}
      })
      .catch(() => {});
  }
};

/** 批量清除缓存 */
const handleBatchClear = (isBatch, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  if (!!isBatch) {
    proxy.$modal
      .confirm(
        "是否确认批量清除设备名称为【" + arr.join("、") + "】的设备缓存？"
      )
      .then(async function () {
        let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
        let sendData = {
          userEvents: rows.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: 6,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
          logContent: `远程清除缓存`, // 日志内容
          deviceNum: rows.length, // 操作设备数量
        });
        for (let j = 0; j < rows.length; j++) {
          try {
            handleClear({
              ipAddress: rows[j].ipAddress,
              deviceCode: rows[j].deviceCode,
              isMqtt: rows[j].isMqtt,
            });
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    proxy.$modal
      .confirm("是否确认清除设备名称为【" + row.deviceName + "】的设备缓存？")
      .then(async function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: 6,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `远程清除缓存`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleClear(row);
        } catch (error) {}
      })
      .catch(() => {});
  }
};

/** 批量开启/关闭 Wifi */
const handleBatchWifi = (isBatch, type, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  if (!!isBatch) {
    proxy.$modal
      .confirm(
        `是否确认批量${!!type ? "开启" : "关闭"}设备名称为【${arr.join(
          "、"
        )}】的设备wifi？`
      )
      .then(async function () {
        let rows = JSON.parse(JSON.stringify(data.tableAllSelectedRow));
        let sendData = {
          userEvents: rows.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: `${!!type ? 4 : 5}`,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
          logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
          deviceNum: rows.length, // 操作设备数量
        });
        for (let j = 0; j < rows.length; j++) {
          try {
            handleWifi(
              {
                ipAddress: rows[j].ipAddress,
                deviceCode: rows[j].deviceCode,
                isMqtt: rows[j].isMqtt,
              },
              type
            );
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    proxy.$modal
      .confirm(
        `是否确认${!!type ? "开启" : "关闭"}设备名称为【${
          row.deviceName
        }】的设备wifi？`
      )
      .then(async function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: `${!!type ? 4 : 5}`,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleWifi(row, type);
        } catch (error) {}
      })
      .catch(() => {});
  }
};

/** 解锁/锁屏 type: 0锁屏 1解锁*/
const handleLock = async (row, type) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleClear = async (row) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri: (isRalay.value ? ip2 : ip1) + `/api/Cockpit/ClearMemory`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/ClearMemory`,
      deviceCodeList: row.deviceCode,
    };
    console.log("传参", obj);
    res = await deviceCtlMqtt(obj);
  } else {
    console.log("传参", obj);
    res = await deviceCtl(obj);
  }
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleWifi = async (row, type) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleRemote = async (row, type, isTime) => {
  if (!!isTime) {
    // 操作单个设备的定时关机
    console.log(row, "row");
    try {
      let sendData = {
        userEvents: [
          {
            event: "Click",
            eventDescribe: "点击单个设备操作集控按钮",
            content: 3,
            num: 1,
            deviceCode: row.deviceCode,
          },
        ],
      };
      console.log(sendData, "单个埋点");
      sendPointRequestBatch(sendData);
      let res = await addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: row.deviceCode, // 操作设备编号
        logContent: `远程定时关机`, // 日志内容
        deviceNum: 1,
      });
      console.log(res, "操作记录");
    } catch (error) {}
  }

  proxy.$modal.loading();

  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let data = { cmd: type === 3 ? 0 : type };
  if (type == 3) {
    data.time = row.time;
  } else {
    delete data.time;
  }
  let obj = {
    method: "post",
    uri: (isRalay.value ? ip2 : ip1) + `/api/Cockpit/Control`,
    content: JSON.stringify(data),
  };
  console.log("关机/重启/定时关机传参", obj);
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/Control`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();

  if (!isBatch.value) {
    cancel();
  }
};

async function getPoint() {
  try {
    await getPointInfo({}).then((res) => {
      console.log("机构点位信息", res);
      if (res.code == 200) {
        data.pointNum = res.data.total || 0;
      }
    });
  } catch (e) {
    console.log(e, "获取点位信息失败");
  }
  try {
    await getLatestExpirationInfo({ current: 1, size: 100 }).then((res) => {
      console.log("机构过期信息", res);
      if (res.code == 200) {
        expireDay.value = res.data.latestExpirationDays || 0;
      }
    });
  } catch (e) {
    console.log(e, "获取机构过期信息失败");
  }
}

// 添加 handleBack 方法
function handleBack() {
  fromWorkbench.value = false;
  proxy.$tab.closeOpenPage("/work");
}

// 新增处理大屏设置操作的函数
function handleScreenSettings(val) {
  console.log("大屏设置操作", val);
  switch (val) {
    // 调节音量
    case 0:
      handleVolumeControl();
      break;
    // 频道切换
    case 1:
      handleChannelSwitch();
      break;
    // 倒计日功能
    case 2:
      handleCountdown();
      break;
    default:
      break;
  }
  data.operationParams.screenSettings = "";
}

function handleVolumeControl() {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  volumeDialogVisible.value = true;
}

function handleChannelSwitch() {
  data.channelDialogVisible = true;
}

function handleCountdown() {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  data.countdownDialogVisible = true;
}

// 新增发送音量调节指令函数
async function handleVolumeCommand() {
  try {
    // 检查是否有选中的设备
    if (data.tableAllSelectedRow.length === 0) {
      proxy.$modal.msgWarning("请至少选择一个设备");
      return;
    }

    proxy.$modal.loading();
    const params = {
      deviceCodeList: data.tableAllSelectedRow.map((item) => item.deviceCode),
      volumeSize: data.volumeForm.value,
    };

    console.log("音量调节参数:", params);
    const res = await volumeSize(params);

    if (res.code === 200) {
      proxy.$modal.msgSuccess("音量调节指令发送成功");
      volumeDialogVisible.value = false;
    } else {
      // proxy.$modal.msgError(res.msg || "操作失败");
    }
  } catch (error) {
    // console.error("音量调节失败:", error);
    // proxy.$modal.msgError("操作失败");
  } finally {
    proxy.$modal.closeLoading();
  }
}

// 新增发送频道切换指令函数
function handleChannelCommand() {
  if (!data.selectedChannel) {
    proxy.$modal.msgWarning("请选择要切换的频道");
    return;
  }
  // TODO: 实现发送频道切换指令的逻辑
  proxy.$modal.msgSuccess("频道切换指令已发送");
  data.channelDialogVisible = false;
}

// 处理倒计日指令
async function handleCountdownCommand() {
  // 如果是开启倒计日，需要验证必填字段
  if (data.countdownForm.enabled) {
    if (!data.countdownForm.eventName) {
      proxy.$modal.msgWarning("请输入事件名称");
      return;
    }
    if (!data.countdownForm.targetDate) {
      proxy.$modal.msgWarning("请选择目标日期");
      return;
    }
  }
  /*  */
  try {
    proxy.$modal.loading();
    const params = {
      openShutdown: data.countdownForm.enabled ? "1" : "0",
      deviceCodeList: data.tableAllSelectedRow.map((item) => item.deviceCode),
    };

    // 只有在开启倒计日时才添加事件名称和时间
    if (data.countdownForm.enabled) {
      params.eventName = data.countdownForm.eventName;
      params.eventTime = data.countdownForm.targetDate;
    }

    console.log(params, "倒计日功能传递的参数");
    // return;
    const res = await countdown(params);

    if (res.code === 200) {
      proxy.$modal.msgSuccess(
        data.countdownForm.enabled ? "倒计日指令发送成功" : "倒计日已关闭"
      );
      data.countdownDialogVisible = false;
      resetCountdownForm();
    } else {
      // proxy.$modal.msgError(res.msg || "操作失败");
    }
  } catch (error) {
    // console.error("操作失败:", error);
    // proxy.$modal.msgError("操作失败");
  } finally {
    proxy.$modal.closeLoading();
  }
}

// 重置倒计日表单
function resetCountdownForm() {
  data.countdownForm = {
    enabled: true,
    eventName: "",
    targetDate: "",
  };
}

// 计算剩余天数
const calculateDays = computed(() => {
  if (!data.countdownForm.targetDate) return 0;

  const targetDate = new Date(data.countdownForm.targetDate);
  const today = new Date();

  // 将两个日期都设置为当天的0点0分0秒
  targetDate.setHours(0, 0, 0, 0);
  today.setHours(0, 0, 0, 0);

  if (targetDate.getTime() === today.getTime()) {
    return 0;
  }

  const diffTime = targetDate.getTime() - today.getTime();
  const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));

  return diffDays > 0 ? diffDays : 0;
});

// 格式化目标日期显示
const formatTargetDate = computed(() => {
  if (!data.countdownForm.targetDate) return "";

  const date = new Date(data.countdownForm.targetDate);
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");

  return `${year}年${month}月${day}日`;
});

// 限制倒计日目标日期选择范围
const disabledCountdownDate = (time) => {
  const today = new Date();
  const fiveYearsLater = new Date();
  fiveYearsLater.setFullYear(today.getFullYear() + 5);

  // 禁用今天之前的日期和5年后的日期
  return (
    time.getTime() < today.getTime() - 8.64e7 ||
    time.getTime() > fiveYearsLater.getTime()
  );
};

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});

onActivated(async () => {
  controlBtnRef.value?.getControl();
  console.log("激活");
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  refreshCountdown.value = new Date().getTime() + 5 * 60 * 1000;
  if (useUserStore().isCommon) {
    await getPoint();
  }
  // 检查是否需要触发异常设备查询
  if (route.query.triggerAbnormal) {
    // 设置标记，表示是从工作台跳转而来
    fromWorkbench.value = true;

    // 清除查询参数以避免刷新时再次触发
    router.replace({ path: route.path });

    // 调用处理异常设备查询的函数
    handleErrorQuery();
  } else if (!fromWorkbench.value) {
    getList();
  }
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef?.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef?.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
  await getOptions();
  await getPositionTreeList();
  await getTagList();
  initParkState();
  showRefresh.value = true;
});

// 在离开页面时重置状态
onDeactivated(() => {
  // console.log("离开页面", addTimer.value);
  // 重置fromWorkbench状态，确保再次进入时不显示返回按钮
  fromWorkbench.value = false;

  // 重置查询参数，确保下次进入时不会自动触发异常查询
  queryParams.value.abnormalInterruptionChannel = 0;
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览设备集控页面",
    content: "",
    num: addSecond.value,
  });

  clearInterval(addTimer.value);
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
  showRefresh.value = false;
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览设备集控页面",
    content: "",
    num: addSecond.value,
  });

  clearInterval(addTimer.value);
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
  showRefresh.value = false;
});

onMounted(async () => {
  showRefresh.value = true;
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  refreshCountdown.value = new Date().getTime() + 5 * 60 * 1000;
  if (useUserStore().isCommon) {
    await getPoint();
  }
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem("deviceManagePage");
  if (savedPage) {
    queryParams.value.current = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem("deviceManagePage");
  }

  if (route.query.deviceStatus !== undefined) {
    queryParams.value.deviceStatus = Number(route.query.deviceStatus);
  }
  getList();
  window.addEventListener("parkChange", handleParkChange);
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef?.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef?.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
  await getOptions();
  await getPositionTreeList();
  await getTagList();
  initParkState();
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 20px 0 10px 10px !important;
}
.filePath {
  :deep(.el-form-item__label) {
    padding-right: 0 !important;
  }
}
.header-tip {
  display: flex;
  align-items: center;
  gap: 0 20px;

  img {
    width: 20px;
    height: 20px;
  }
  &_timer {
    display: flex;
    align-items: center;
    gap: 0 5px;
  }
}

.uploadImg {
  :deep(.el-upload__tip) {
    margin-top: 0;
  }
  :deep(.el-upload--picture-card) {
    width: 90px !important;
    height: 35px;
    border: none;
    background: none;
  }
  :deep(.el-upload-list--picture-card) {
    align-items: center;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    margin-bottom: 0;
    justify-content: center;
  }
}
.pagination-container {
  height: auto;
  margin-top: 15px;
  padding: 10px 20px 0 !important;
  margin-bottom: 0;
}
.monitor {
  display: flex;
  align-items: flex-start;
  gap: 0 10px;
  margin-bottom: 15px;
  padding: 0 20px 0 10px;
  &.hasControlBtn {
    height: calc(100vh - 260px);
    overflow-y: scroll;
    overflow-x: hidden;
  }
  &-bottom {
    position: fixed;
    width: 100%;
    display: flex;
    justify-content: center;
    bottom: 35px;
    left: 0;
    z-index: 2001;
    pointer-events: none;
  }
  &-btns {
    // border: 1px solid red;
    pointer-events: auto;
    display: flex;
    max-width: 90%;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px 0;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  &-title {
    font-size: 16px;
    padding: 10px 20px;
  }
  &-position {
    width: 18%;
    margin-right: 10px;
    // border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }
  &-list {
    width: 82%;
    min-width: 400px;
  }
}
.el-progress {
  width: 200px;
  :deep(.el-progress__text) {
    min-width: 35px;
  }
}
.fileclass {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }
}
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;
  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}
.monitor-statics {
  // border: 1px solid #d4d4d4;
  display: flex;
  margin-bottom: 20px;
  font-size: 16px;
  gap: 0 20px;
  &_item {
    // border: 1px solid red;
    color: rgba(0, 0, 0, 0.4);
    padding: 1.5vw 2vw;
    flex: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0 10px;
    cursor: pointer;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/control-bg.png");
    background-size: 100% 100%;

    .info {
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      gap: 15px;
      &-num {
        display: flex;
        align-items: center;
        letter-spacing: 0.05vw;
        flex-wrap: wrap;
      }
      span {
        font-size: 26px;
        color: #4095e5;
      }
      .expireDay {
        color: #ed5953;
        font-size: 12px;
        font-weight: bold;
        padding-top: 5px;
      }
    }

    &::after {
      content: "";
      // width: 5vw;
      // height: 5vw;
      width: 70px;
      height: 70px;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/control-normal.png");
      background-size: 100% 100%;
    }

    &:last-child {
      span {
        color: #ed5953;
      }
      .tag {
        display: flex;
        align-items: center;
        line-height: 1;
        gap: 0 5px;
        &::after {
          content: "去处理 >";
          white-space: nowrap;
          display: inline-block;
          background-color: #ed5953;
          padding: 3px 7px;
          border-radius: 2vw;
          font-size: 8px;
          color: #fff;
        }
      }
      &::after {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/control-abnormal.png");
        background-size: 100% 100%;
      }
    }
  }
}

.volume-control {
  padding: 20px 0;

  .volume-label {
    margin-bottom: 10px;
  }

  :deep(.el-slider) {
    width: 100%;
  }
}

.volume-dialog {
  padding: 20px;

  .volume-tip {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
  }

  .volume-control {
    display: flex;
    align-items: center;
    gap: 20px;

    .volume-label {
      white-space: nowrap;
    }

    :deep(.el-slider) {
      flex: 1;
    }
  }
}

.channel-dialog {
  padding: 20px;

  .channel-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 20px;

    .channel-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 10px;
      cursor: pointer;

      .channel-icon {
        width: 60px;
        height: 60px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 50%;
        border: 1px solid #1d2122;
        transition: all 0.3s;

        svg {
          width: 30px;
          height: 30px;
          fill: currentColor;
        }

        &:hover {
          border-color: #409eff;
          svg {
            color: #409eff;
          }
        }
      }

      &.active .channel-icon {
        border-color: #409eff;
        background-color: #ecf5ff;
        svg {
          color: #409eff;
        }
      }

      .channel-name {
        font-size: 14px;
        color: #606266;
      }
    }
  }
}

.countdown-dialog {
  // padding: 20px;

  .countdown-options {
    margin-bottom: 20px;
  }

  .countdown-tip {
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
  }

  .countdown-preview {
    margin-bottom: 20px;
    text-align: center;

    .preview-image {
      max-width: 100%;
      height: auto;
      border: 1px solid #eee;
      border-radius: 4px;
    }
  }

  :deep(.el-form-item) {
    margin-bottom: 20px;

    &:last-child {
      margin-bottom: 0;
    }
  }
}

.countdown-preview {
  margin-bottom: 20px;

  .preview-container {
    width: 100%;
    height: 200px;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    border-radius: 8px;
    position: relative;
    color: white;
  }

  .preview-header {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    font-size: 16px;
    opacity: 0.9;
    text-align: left;
  }

  .header-border {
    position: absolute;
    left: 0;
    right: 0;
    bottom: -5px; // 调整边框位置
    height: 1px;
    background-color: rgba(255, 255, 255, 0.3);
  }

  .preview-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 100%;
    text-align: center;
  }

  .preview-title {
    font-size: 16px;
    margin-bottom: 15px;
    margin-top: 20px;
    opacity: 0.9;
  }

  .countdown-row {
    display: flex;
    align-items: baseline;
    justify-content: center;
    margin-bottom: 15px;
    color: white;
  }

  .preview-number {
    font-size: 64px;
    font-weight: bold;
    line-height: 1;
    color: white;
  }

  .preview-unit {
    font-size: 20px;
    margin-left: 5px;
    color: white;
  }

  .preview-date {
    font-size: 14px;
    opacity: 0.8;
    color: white;
  }
}

.lock-rule-dialog {
  :deep(.el-dialog__body) {
    padding: 20px;
  }
}

.select-label {
  color: #606266;
  margin: 12px 0 8px;
}

.select-container {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.tree-select-container {
  margin-top: 8px;
}

.tree-select-label {
  margin-bottom: 8px;
  font-weight: 700;
  padding-left: 120px;
}

.tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.mb-3 {
  margin-bottom: 12px;
  font-weight: 700;
}

.mt-3 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 25px;
}
.mt-4 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 10px;
}

.tree-container {
  :deep(.el-tree-node__content) {
    height: auto;
  }
  :deep(.el-tree-node__label) {
    white-space: nowrap;
  }
}

.select-device-container {
  min-height: 32px;
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  max-height: 100px;
  overflow-y: auto;

  .device-tag {
    margin: 2px 4px;
  }

  .placeholder-text {
    color: #999;
    line-height: 24px;
  }
}
</style>
