<template>
    <div class="citymain">
        <div class="item item2">
            <el-icon size="2vw" color="#79bbff">
                <component is="DataAnalysis"></component>
            </el-icon>
            <div style="text-align: center;">
                <span>{{ deviceRatio }}%</span><br />
                多媒体教学率
            </div>
        </div>
        <div class="item item10">
            <Echarts @setFontSize="setFontSize" id="qqsbyxzt" width="100%" height="100%" style="float: left;"
                :fullOptions="qqsbyxztOption" :loading="false" />
        </div>
        <div class="item item2" v-for="(item, index) in deviceConfig" :key="index" @click="getInfo(index)">
            <el-icon size="2vw" :color="configIcon[index].color">
                <component :is="configIcon[index].name"></component>
            </el-icon>
            <div style="text-align: center;">
                <span>{{ item.value }}</span><br />
                {{ item.label }}
            </div>
        </div>
        <div class="item item2">
            <el-icon size="2vw" color="#fab6b6">
                <component is="Connection"></component>
            </el-icon>
            <div style="text-align: center;">
                <span>{{ deviceTeachTime }}小时</span><br />
                多媒体教学时长
            </div>
        </div>
        <div class="item item3">
            <Echarts @setFontSize="setFontSize" id="yysyl" width="25%" height="100%" style="float: left;"
                :fullOptions="yysylOption" :loading="false" />
            <Echarts @setFontSize="setFontSize" id="sex" width="25%" height="100%" style="float: left;"
                :fullOptions="sexOption" :loading="false" />
            <Echarts @setFontSize="setFontSize" id="grade" width="25%" height="100%" style="float: left;"
                :fullOptions="gradeOption" :loading="false" />
            <Echarts @setFontSize="setFontSize" id="major" width="25%" height="100%" style="float: left;"
                :fullOptions="majorOption" :loading="false" />
        </div>
        <div class="item item4">
            <div class="yyflzb" @click="open1 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="yyflzb" width="100%" height="100%" style="float: left;"
                :fullOptions="yyflzbOption" :loading="false" />
        </div>
        <div class="item item5">
            <div class="yyrjtop" @click="open2 = true">详情</div>
            <Echarts @setFontSize="setFontSize" class="row-opt opt3" id="yyrjtop" width="100%" height="100%"
                style="float: left;" :fullOptions="yyrjtopOption" :loading="false" />
        </div>
        <div class="item item6">
            <div class="sbjhsc" @click="open3 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="sbjhsc" width="100%" height="100%" :fullOptions="sbjhscOption"
                :loading="false" />
        </div>
        <div class="item item7">
            <div class="xxbjdk" @click="open4 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="xxbjdk" width="100%" height="100%" :fullOptions="xxbjdkOption"
                :loading="false" />
        </div>
        <div class="item item8">
            <div class="qqsbsysc" @click="open5 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="qqsbsysc" width="100%" height="100%" :fullOptions="qqsbsyscOption"
                :loading="false" />
        </div>
        <el-dialog v-model="open1" title="应用分类占比" width="600px">
            <el-table :data="tableData1" style="width: 100%" border max-height="72vh">
                <el-table-column prop="type" label="应用分类" />
                <el-table-column prop="num" label="使用次数" />
                <el-table-column prop="software" label="最高使用率软件" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open2" title="应用软件Top20" width="600px">
            <el-table :data="tableData2" style="width: 100%" border max-height="72vh">
                <el-table-column type="index" label="排名" width="100" />
                <el-table-column prop="appName" label="软件名称" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open3" title="全市近7天多媒体教学时长" width="600px">
            <el-table :data="tableData3" style="width: 100%" border max-height="72vh">
                <el-table-column prop="name" label="区名" />
                <el-table-column prop="date" label="教学日期" />
                <el-table-column prop="dur" label="多媒体教学时长" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open4" title="各区平均带宽" width="600px">
            <el-table :data="tableData4" style="width: 100%" border max-height="72vh">
                <el-table-column prop="tenantName" label="区名" />
                <el-table-column prop="network" label="平均带宽" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open5" title="月度各区设备使用时长" width="600px">
            <el-table :data="tableData5" style="width: 100%" border max-height="72vh">
                <el-table-column prop="tenantName" label="区名" />
                <el-table-column prop="runTime" label="设备使用时长" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open6" title="设备数量详情" width="600px">
            <el-table :data="tableData6" style="width: 100%" border max-height="72vh">
                <el-table-column prop="小学" label="小学" />
                <el-table-column prop="初中" label="初中" />
                <el-table-column prop="高中" label="高中" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Index">
import Echarts from '@/components/Echarts/index.vue';
import { onMounted, reactive, ref, getCurrentInstance } from 'vue';
import { queSoftwareRatio, queStudentNum, queDeviceRatio, queSubjectRatio, queCityDeviceInfo, queTenantDeviceTime, queTenantDeviceWeekRunTime, queTenantNetwork, queTeacharSexRatio, queTeacherAgeRatio, queDeviceTenantRatio } from '@/api/city'
import { queAppUseNum } from '@/api/mediaTeach/ledger'
import { transformSize } from '@/utils'
import { sm2Decrypt } from '@/utils/sm2encrypt'

// console.log(JSON.parse(sm2Decrypt('040f71145b8f210c6978b05fee11304994876c5e8451c4d5516def009ad9391c0b4da49280b5fc63ecc1fffb8a4143fed8903d672df6f5292cff175642b52ae03a1404dcdd13d7b2a588f525b341668c5d4ed30e0d82c8a48a1ba4208d144678bcae27309786d279dbc64ee6e3d4c41568751f698eb8cae314b1f18330d694c151121e662bc052bd3ce6a31ea487951ada9aa2e895119c45af48e06aee00aacb2f43e62f85b068d9434551f0d1684f77276d871a3122626dd8c6b2fd6ac8')))

const { proxy } = getCurrentInstance()

const open1 = ref(false)
const tableData1 = ref([
    { type: '社交', num: 36, software: '微信' },
    { type: '浏览器', num: 10, software: '360浏览器' },
    { type: '办公', num: 23, software: 'PPT' }
])
const open2 = ref(false)
const tableData2 = ref([
    { name: '微信' },
    { name: '谷歌浏览器' },
    { name: 'PPT' }
])
const open3 = ref(false)
const tableData3 = ref([
    { name: '白云区', date: '2023-11-28', dur: '11h' },
    { name: '白云区', date: '2023-11-27', dur: '10h' },
    { name: '白云区', date: '2023-11-26', dur: '9h' },
    { name: '白云区', date: '2023-11-25', dur: '11h' },
    { name: '白云区', date: '2023-11-24', dur: '6h' },
    { name: '白云区', date: '2023-11-23', dur: '7h' },
    { name: '白云区', date: '2023-11-22', dur: '8h' },
    { name: '海珠区', date: '2023-11-28', dur: '10h' },
    { name: '海珠区', date: '2023-11-27', dur: '9h' },
    { name: '海珠区', date: '2023-11-26', dur: '11h' },
    { name: '海珠区', date: '2023-11-25', dur: '10h' },
    { name: '海珠区', date: '2023-11-24', dur: '6h' },
    { name: '海珠区', date: '2023-11-23', dur: '7h' },
    { name: '海珠区', date: '2023-11-22', dur: '8h' },
])
const open4 = ref(false)
const tableData4 = ref([
    { tenantName: '白云区', network: '36m/s' },
    { tenantName: '番禺区', network: '10m/s' },
    { tenantName: '海珠区', network: '23m/s' }
])
const open5 = ref(false)
const tableData5 = ref([
    { tenantName: '白云区', runTime: '36h' },
    { tenantName: '番禺区', runTime: '10h' },
    { tenantName: '海珠区', runTime: '23h' }
])

const open6 = ref(false)
const tableData6 = ref([
    { '小学': 20, '初中': 36, '高中': 40 },
])

let deviceRatio = ref(0)
let deviceTeachTime = ref(0)

let deviceConfig = ref([
    { label: '学校数量', value: '1000' },
    { label: '教师数量', value: '56' },
    { label: '学生数量', value: '4881' },
    { label: '设备数量', value: '1000' },
])
const configIcon = [
    { name: 'Histogram', color: '#5470c6' },
    { name: 'Avatar', color: '#91cc75' },
    { name: 'User', color: '#fac858' },
    { name: 'Menu', color: '#ee6666' }
]

/** 全市设备运行状态 */
let qqsbyxztData = [
    { value: 50, name: '运行良好' },
    { value: 30, name: '运行较差' },
    { value: 20, name: '未运行' },
]
/** 多媒体教学应用使用率 */
let yysylData = [
    { value: 100, name: '3-12岁' },
    { value: 140, name: '13-22岁' },
    { value: 230, name: '23-32岁' }
]
let sexData = [
    { value: 100, name: '男性' },
    { value: 150, name: '女性' }
]
let gradeData = [
    { value: 100, name: '小学' },
    { value: 150, name: '初中' },
    { value: 109, name: '高中' }
]
let majorData = [
    { value: 30, name: '语文' },
    { value: 34, name: '数学' },
    { value: 36, name: '英语' }
]
/** 学校平均带宽 */
let xxbjdkXData = ['能力小学', '文艺中学']
let xxbjdkSData = [130, 100]
// let xxbjdkXData = ['学校1', '学校2', '学校3', '学校4', '学校5']
// let xxbjdkSData = [100, 140, 230, 100, 130]

/** 近7天设备交互时长 */
let sbjhscLData = ['学校1', '学校2', '学校3', '学校4', '学校5']
let sbjhscXData = ['9.15', '9.16', '9.17', '9.18', '9.19', '9.20', '9.21']
let sbjhscSData = [
    {
        name: '学校1',
        type: 'line',
        stack: 'Total',
        data: [120, 132, 101, 134, 90, 230, 210]
    },
    {
        name: '学校2',
        type: 'line',
        stack: 'Total',
        data: [220, 182, 191, 234, 290, 330, 310]
    },
    {
        name: '学校3',
        type: 'line',
        stack: 'Total',
        data: [150, 232, 201, 154, 190, 330, 410]
    },
    {
        name: '学校4',
        type: 'line',
        stack: 'Total',
        data: [320, 332, 301, 334, 390, 330, 320]
    },
    {
        name: '学校5',
        type: 'line',
        stack: 'Total',
        data: [820, 932, 901, 934, 1290, 1330, 1320]
    }
]

/** 月度全区设备使用时长 */
let qqsbsyscXData = ['能力小学', '文艺中学']
let qqsbsyscSData = [100, 140]
// let qqsbsyscXData = ['学校1', '学校2', '学校3', '学校4', '学校5']
// let qqsbsyscSData = [100, 140, 230, 100, 130]

/** top5 */
let yyrjtopYData = ['其它', '谷歌浏览器', '微信', 'QQ', '腾讯课堂']
let yyrjtopSData = [100, 100, 130, 140, 230]

let yyflzbData = [
    { value: 20, name: '社交' },
    { value: 10, name: '学习' },
    { value: 10, name: '办公' },
    { value: 60, name: '其它' }
]

function getInfo(index) {
    if (index === 3) {
        open6.value = true
    }
}

/** 注入 */
function injectOption() {
    proxy.qqsbyxzt = qqsbyxztOption
    proxy.yysyl = yysylOption
    proxy.sex = sexOption
    proxy.grade = gradeOption
    proxy.major = majorOption
    proxy.xxbjdk = xxbjdkOption
    proxy.sbjhsc = sbjhscOption
    proxy.qqsbsysc = qqsbsyscOption
    proxy.yyrjtop = yyrjtopOption
    proxy.yyflzb = yyflzbOption
}

const qqsbyxztOption = reactive({
    options: {
        title: {
            text: '全市设备运行状态',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            // orient: 'vertical',
            // right: '2%',
            bottom: '1%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '占比',
                type: 'pie',
                radius: ['35%', '48%'],
                center: ['50%', '45%'],
                color: ['#91cd77', '#ef6567', '#75bedc', '#f9c956', '#5470c6'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 10,
                    borderColor: '#fff',
                    borderWidth: 2
                },
                label: {
                    show: false,
                    position: 'center'
                },
                labelLine: {
                    show: false
                },
                data: []
            }
        ]
    },
    init: false
})
const yysylOption = reactive({
    options: {
        title: {
            text: '多媒体设备使用率',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1.5%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '年龄占比',
                type: 'pie',
                radius: '40%',
                center: ['50%', '45%'],
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
const sexOption = reactive({
    options: {
        title: {
            textStyle: {
                fontSize: transformSize(22)
            },
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '性别占比',
                type: 'pie',
                radius: '40%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                center: ['50%', '45%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
const gradeOption = reactive({
    options: {
        title: {
            textStyle: {
                fontSize: transformSize(22)
            },
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '年级占比',
                type: 'pie',
                radius: '40%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                center: ['50%', '45%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
const majorOption = reactive({
    options: {
        title: {
            textStyle: {
                fontSize: transformSize(22)
            },
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '学科占比',
                type: 'pie',
                radius: '40%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                center: ['50%', '45%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
const xxbjdkOption = reactive({
    options: {
        title: {
            text: '各区平均带宽',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '10%',
            right: '5%',
            bottom: '13%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'bar',
                barMaxWidth: '40%',
                itemStyle: {
                    color: '#fac858'
                }
            }
        ]
    },
    init: false
})
const sbjhscOption = reactive({
    options: {
        title: {
            text: '全市近7天多媒体教学时长',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '0.5%'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            show: false
            // textStyle: {
            //     color: '#fff'
            // },
            // top: '2%',
            // right: '1%',
            // data: []
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: []
    },
    init: false
})
const qqsbsyscOption = reactive({
    options: {
        title: {
            text: '月度各区设备使用时长',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '10%',
            right: '5%',
            bottom: '13%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'bar',
                barMaxWidth: '40%'
            }
        ]
    },
    init: false
})
/** 应用软件top5 */
const yyrjtopOption = reactive({
    options: {
        title: {
            text: '应用软件Top5',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '5%',
            right: '10%',
            bottom: '5%',
            height: '75%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLabel: {
                show: false
                // color: '#fff'
            },
            splitLine: { show: false },
        },
        yAxis: {
            type: 'category',
            axisLabel: {
                show: false,
                color: '#fff'
            },
            data: []
        },
        series: [
            {
                name: '使用次数',
                type: 'bar',
                barMaxWidth: '40%',
                data: [],
                itemStyle: {
                    color: '#fac858'
                }
            },
        ]
    },
    init: false
})
const yyflzbOption = reactive({
    options: {
        title: {
            text: '应用分类占比',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            show: false,
            left: 'center',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '数量占比',
                type: 'pie',
                radius: '40%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                center: ['50%', '55%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})

let opt1 = qqsbyxztOption.options
let opt2 = yysylOption.options
let opt3 = xxbjdkOption.options
let opt4 = sbjhscOption.options
let opt5 = qqsbsyscOption.options
let opt6 = sexOption.options
let opt7 = gradeOption.options
let opt8 = majorOption.options
let opt9 = yyrjtopOption.options
let opt10 = yyflzbOption.options

injectOption()

function setDatas() {
    opt1.series[0].data = qqsbyxztData

    opt2.series[0].data = yysylData

    opt3.xAxis.data = xxbjdkXData
    opt3.series[0].data = xxbjdkSData

    opt4.legend.data = sbjhscLData
    opt4.xAxis.data = sbjhscXData
    opt4.series = sbjhscSData

    opt5.xAxis.data = qqsbsyscXData
    opt5.series[0].data = qqsbsyscSData

    opt6.series[0].data = sexData

    opt7.series[0].data = gradeData

    opt8.series[0].data = majorData

    opt9.yAxis.data = yyrjtopYData
    opt9.series[0].data = yyrjtopSData

    opt10.series[0].data = yyflzbData
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(22)
}

function getDatas() {
    queDeviceRatio().then(response => {
        let { ratio, useTime } = response.data
        deviceTeachTime.value = useTime
        deviceRatio.value = (ratio * 1).toFixed(2) || 0
    })
    queStudentNum().then(response => {
        deviceConfig.value[2].value = response.data || 0
    })
    queSoftwareRatio().then(response => {
        if (response.data.ratioList.length > 0) {
            yyflzbData = []
            response.data.ratioList.map(item => {
                yyflzbData.push({
                    name: item.type,
                    value: (item.ratio * 100).toFixed(2) || 0
                })
            })
            opt10.series[0].data = yyflzbData
            tableData1.value = response.data.ratioList.reduce((res, cur) => {
                res.push({
                    ...cur,
                    software: cur.software || '-'
                })
                return res
            }, [])
        }
    })
    queCityDeviceInfo().then(response => {
        qqsbyxztData = []
        let { tenantNum, teacherNum, deviceNum, offPercentum, runPercentum, fineRation } = response.data
        deviceConfig.value[0].value = tenantNum || 0
        deviceConfig.value[1].value = teacherNum || 0
        deviceConfig.value[3].value = deviceNum || 0
        qqsbyxztData.push({
            name: '运行良好', value: (fineRation * 100).toFixed(2) || 0
        }, {
            name: '运行较差', value: ((1 - fineRation) * 100).toFixed(2) || 0
        }, {
            name: '未运行', value: (offPercentum * 100).toFixed(2) || 0
        })
        opt1.series[0].data = qqsbyxztData
    })
    queTenantNetwork().then(response => {
        if (response.data.length > 0) {
            xxbjdkXData = [], xxbjdkSData = []
            response.data.map((item, index) => {
                xxbjdkXData[index] = item.tenantName
                xxbjdkSData[index] = item.network || 0
            })
            opt3.xAxis.data = xxbjdkXData
            opt3.series[0].data = xxbjdkSData
            tableData4.value = response.data
        }
    })
    queTenantDeviceTime().then(response => {
        if (response.data.length > 0) {
            qqsbsyscXData = [], qqsbsyscSData = []
            response.data.map((item, index) => {
                qqsbsyscXData[index] = item.tenantName
                qqsbsyscSData[index] = item.runTime || 0
            })
            opt5.xAxis.data = qqsbsyscXData
            opt5.series[0].data = qqsbsyscSData
            tableData5.value = response.data.reduce((res, cur) => {
                res.push({
                    ...cur,
                    runTime: cur.runTime * 1 + 'h'
                })
                return res
            }, [])
        }
    })
    queTenantDeviceWeekRunTime().then(response => {
        if (response.data.runInfoList.length > 0) {

            sbjhscLData = [], sbjhscXData = [], sbjhscSData = []
            response.data.runInfoList[0].infoList.map((item, index) => {
                sbjhscLData[index] = item.tenantName
                sbjhscSData.push({
                    name: item.tenantName,
                    type: 'line',
                    stack: `Total${index}`,
                    data: []
                })
            })
            response.data.runInfoList.map((item, index) => {
                sbjhscXData[index] = item.time
                item.infoList.map((item2, index2) => {
                    sbjhscSData[index2].data[index] = item2.runTime || 0
                })
            })
            opt4.legend.data = sbjhscLData
            opt4.xAxis.data = sbjhscXData
            opt4.series = sbjhscSData
            console.log(sbjhscXData)
            console.log(sbjhscSData)
            tableData3.value = []
            sbjhscSData.map(item => {
                item.data.map((val, index) => {
                    tableData3.value.push({
                        name: item.name,
                        date: sbjhscXData[index],
                        dur: val
                    })
                })
            })

        }
    })
    queTeacherAgeRatio().then(response => {
        if (response.data.ageRatioList.length > 0) {
            yysylData = []
            response.data.ageRatioList.map(item => {
                yysylData.push({
                    name: item.age + '岁',
                    value: (item.ratio * 100).toFixed(2) || 0
                })
            })
            opt2.series[0].data = yysylData
        }
    })
    queTeacharSexRatio().then(response => {
        sexData = []
        let { boyRatio, girlRatio } = response.data
        sexData.push({
            name: '男性', value: (boyRatio * 100).toFixed(2) || 0
        }, {
            name: '女性', value: (girlRatio * 100).toFixed(2) || 0
        })
        opt6.series[0].data = sexData
    })
    queSubjectRatio().then(response => {
        if (response.data.ratioList.length > 0) {
            majorData = []
            response.data.ratioList.map(item => {
                majorData.push({
                    name: item.subject,
                    value: (item.ration * 100).toFixed(2) || 0
                })
            })
            opt8.series[0].data = majorData
        }
    })
    queDeviceTenantRatio().then(response => {
        if (response.data.ratioList.length > 0) {
            gradeData = []
            response.data.ratioList.map(item => {
                gradeData.push({
                    name: item.level,
                    value: (item.ratio * 100).toFixed(2) || 0
                })
                tableData6.value[0][item.level] = item.num
            })
            opt7.series[0].data = gradeData
        }
    })
    queAppUseNum().then(response => {
        if (response.data.length > 0) {
            yyrjtopYData = [], yyrjtopSData = []
            response.data.slice(0, 5).reverse().map((item, index) => {
                yyrjtopYData[index] = item.appName
                yyrjtopSData[index] = item.num
            })
            opt9.yAxis.data = yyrjtopYData
            opt9.series[0].data = yyrjtopSData
            tableData2.value = response.data
        }
    })
}

onMounted(() => {
    setDatas()
    getDatas()
})

</script>

<style lang="scss" scoped>
.citymain {

    background-color: #0f0c43;
    color: #fff;
    padding: 2.5vh 1vw 1.5vh;
    display: grid;
    grid-template-columns: repeat(6, 15.7%);
    grid-template-rows: 12vh 12vh 12vh 12vh 33vh;
    grid-gap: 1.8vh 1vw;

    .item {
        border: 1px solid #fff;
    }

    .item2 {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1vw;

        .el-icon {
            font-size: 2.5vw !important;
            margin-right: 1.5vw;
        }

        span {
            display: inline-block;
            max-width: 8vw;
            word-break: break-all;
            font-size: 1.5vw;
            font-weight: bold;
        }
    }

    .item4 {
        grid-row-start: span 2;
        grid-column-start: 1;
        grid-column-end: 2;
        position: relative;
    }

    .yyflzb,
    .yyrjtop,
    .sbjhsc,
    .xxbjdk,
    .qqsbsysc {
        position: absolute;
        cursor: pointer;
        right: 3%;
        top: 3%;
        font-size: .8vw;
        padding: .5vh 1vw;
        background-color: #a9abae;
        z-index: 9;
    }

    .sbjhsc,
    .xxbjdk,
    .qqsbsysc {
        right: 2%;
    }

    // .item9 {
    //     grid-row-start: span 1.5;
    //     grid-column-start: 1;
    //     grid-column-end: 2;
    // }

    .item5 {
        grid-row-start: span 2;
        grid-column-start: 2;
        grid-column-end: 3;
        position: relative;
    }

    .item3 {
        grid-row-start: span 3;
        grid-column-start: 3;
        grid-column-end: 7;
    }

    .item6 {
        grid-column-start: 1;
        grid-column-end: 3;
        position: relative;
    }

    .item7 {
        grid-column-start: 3;
        grid-column-end: 5;
        position: relative;
    }

    .item8 {
        grid-column-start: 5;
        grid-column-end: 7;
        position: relative;
    }

    .item10 {
        grid-row-start: span 2;
    }

}
</style>
