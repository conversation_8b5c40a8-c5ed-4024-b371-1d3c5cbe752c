<template>
  <div class="operation app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">人员管理</div>
      </template>

      <!-- 搜索区域 -->
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        class="search-list"
      >
        <el-form-item prop="nameOrNumber">
          <el-input
            v-model.trim="queryParams.nameOrNumber"
            placeholder="请输入工号/姓名"
            clearable
            @keyup.enter="handleQuery"
            style="width: 200px"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="phone">
          <el-input
            v-model.trim="queryParams.phone"
            placeholder="请输入手机号"
            clearable
            @keyup.enter="handleQuery"
            style="width: 200px"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="deptId">
          <el-tree-select
            v-model="queryParams.deptId"
            placeholder="请选择所属部门"
            clearable
            style="width: 200px"
            @change="handleQuery"
            :data="deptList"
            :props="{
              value: 'deptId',
              label: 'deptName',
              children: 'children',
            }"
            check-strictly
            filterable
          >
          </el-tree-select>
        </el-form-item>
        <el-form-item prop="sex">
          <el-select
            v-model="queryParams.sex"
            placeholder="请选择性别"
            clearable
            style="width: 200px"
            @change="handleQuery"
          >
            <el-option
              v-for="item in typeList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区 -->
      <div class="mb12">
        <el-button plain icon="Plus" type="primary" @click="handleAdd"
          >新建人员</el-button
        >
        <el-button plain icon="CirclePlus" type="primary" @click="handleJoin"
          >选择人员</el-button
        >
        <el-button
          plain
          icon="Delete"
          type="danger"
          :disabled="disableBatchDelete"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
        <el-button plain icon="Upload" type="warning" @click="handleBatchImport"
          >批量导入</el-button
        >
        <el-button
          plain
          icon="Download"
          type="success"
          @click="handleBatchExport"
          >批量导出</el-button
        >
      </div>

      <!-- 原有的表格部分 -->
      <el-table
        ref="tableRef"
        v-loading="loading"
        :data="tableList"
        border
        row-key="userId"
        :reserve-selection="true"
        @selection-change="handleSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column
          label="工号"
          align="center"
          minWidth="140px"
          prop="workId"
        />
        <el-table-column
          label="姓名"
          align="center"
          minWidth="120px"
          prop="nickName"
          show-overflow-tooltip
        />
        <!-- <el-table-column
          label="所属机构"
          align="center"
          minWidth="120px"
          prop="institutionName"
        /> -->
        <el-table-column
          label="所属部门"
          align="center"
          minWidth="120px"
          prop="deptName"
          show-overflow-tooltip
        />
        <el-table-column
          label="岗位"
          align="center"
          minWidth="120px"
          prop="postName"
        />
        <el-table-column
          label="手机号码"
          align="center"
          minWidth="120px"
          prop="phoneNumber"
        />
        <el-table-column
          label="邮箱"
          align="center"
          minWidth="180px"
          prop="email"
        />
        <el-table-column
          label="性别"
          align="center"
          minWidth="120px"
          prop="sex"
        />
        <el-table-column label="状态" align="center" minWidth="100px">
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.status]"
              effect="dark"
              :type="statusList[scope.row.status].type"
              >{{ statusList[scope.row.status].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="备注"
          align="center"
          minWidth="120px"
          prop="remark"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          align="center"
          minWidth="160px"
          prop="createTime"
        />
        <el-table-column align="center" minWidth="140px">
          <template #header>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0 5px;
              "
            >
              是否绑定微信
              <el-tooltip
                class="box-item"
                effect="dark"
                content="没有绑定微信则将收不到通知信息"
                placement="top"
              >
                <el-icon :size="16"><QuestionFilled /></el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="scope">
            {{ scope.row.isBindWx }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="120"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="View"
              @click.stop="handleView(scope)"
              >查看</el-button
            >
            <!-- <el-button
              link
              type="primary"
              icon="Edit"
              @click.stop="handleEdit(scope)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.stop="handleDel(scope)"
              >删除</el-button
            > -->
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="tableList.length > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />

      <!-- 新增用户/选择运维人员弹窗 -->
      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        :title="title"
        :width="title == '新建人员' ? 1200 : 1000"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        align-center
        @close="handleCancel"
      >
        <el-form
          v-if="title == '新建人员'"
          ref="memberRef"
          :model="formInfo"
          :rules="rules"
          label-width="auto"
          class="member-form"
        >
          <el-table :data="formInfo.tableList" border>
            <el-table-column min-width="180" label="工号" prop="workId">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.workId`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model.trim="scope.row.workId"
                    placeholder="请输入工号"
                    style="width: 100%"
                    maxlength="20"
                    show-word-limit
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="180" label="姓名" prop="nickName">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.nickName`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model.trim="scope.row.nickName"
                    placeholder="请输入姓名"
                    style="width: 100%"
                    maxlength="25"
                    show-word-limit
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="180" label="所属部门" prop="deptId">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.deptId`"
                  label=""
                >
                  <el-tree-select
                    v-model="scope.row.deptId"
                    :data="deptList"
                    :props="{
                      value: 'deptId',
                      label: 'deptName',
                      children: 'children',
                      disabled: 'disabled',
                    }"
                    placeholder="请选择所属部门"
                    check-strictly
                    clearable
                    filterable
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="160" label="岗位" prop="postId">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.postId`"
                  label=""
                >
                  <el-select
                    v-model="scope.row.postId"
                    placeholder="请选择岗位"
                    clearable
                    filterable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in postList"
                      :key="item.postId"
                      :label="item.postName"
                      :value="item.postId"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <!-- <el-table-column label="身份证号" prop="idCard" min-width="200">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.idCard`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model="scope.row.idCard"
                    placeholder="请输入身份证号"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column> -->
            <el-table-column
              min-width="160"
              label="手机号码"
              prop="phoneNumber"
            >
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.phoneNumber`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model.trim.number="scope.row.phoneNumber"
                    placeholder="请输入手机号码"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <!-- <el-table-column min-width="200" label="用户姓名" prop="nickname">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.nickname`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model="scope.row.nickname"
                    placeholder="请输入用户姓名"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column> -->
            <el-table-column min-width="160" label="邮箱" prop="email">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.email`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model="scope.row.email"
                    placeholder="请输入邮箱"
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="150" label="性别" prop="sex">
              <template #default="scope">
                <el-form-item :prop="`tableList.${scope.$index}.sex`" label="">
                  <el-select
                    v-model="scope.row.sex"
                    placeholder="请选择性别"
                    clearable
                    filterable
                    style="width: 100%"
                  >
                    <el-option
                      v-for="item in typeList"
                      :key="item.value"
                      :label="item.label"
                      :value="item.value"
                    >
                    </el-option>
                  </el-select>
                </el-form-item>
              </template>
            </el-table-column>
            <el-table-column min-width="200" label="备注" prop="remark">
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.remark`"
                  label=""
                >
                  <el-input
                    clearable
                    v-model="scope.row.remark"
                    placeholder="请输入备注"
                    style="width: 90%"
                    maxlength="200"
                    show-word-limit
                  />
                </el-form-item>
              </template>
            </el-table-column>
            <!-- <el-table-column
              min-width="200"
              label="所属机构"
              prop="institutionId"
            >
              <template #default="scope">
                <el-form-item
                  :prop="`tableList.${scope.$index}.institutionId`"
                  label=""
                >
                  <el-tree-select
                    v-model="scope.row.institutionId"
                    :data="institutionTree"
                    :props="{
                      value: 'number',
                      label: 'name',
                      children: 'children',
                    }"
                    value-key="number"
                    placeholder="选择所属机构"
                    check-strictly
                    style="width: 100%"
                  />
                </el-form-item>
              </template>
            </el-table-column> -->

            <el-table-column
              min-width="120"
              fixed="right"
              label="操作"
              align="center"
            >
              <template #default="scope">
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click="handleRepairDel(scope.$index)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <div class="dialog-add" @click="handleAddUser">+ 新增一行</div>
        </el-form>

        <div v-else>
          <el-form
            :model="queryParams_dialog"
            ref="memberRef"
            :inline="true"
            class="search-list"
          >
            <el-form-item prop="nameOrNumber">
              <el-input
                v-model.trim="queryParams_dialog.numberAndName"
                placeholder="请输入工号/姓名"
                clearable
                @keyup.enter="handleRepairQuery"
                style="width: 200px"
                @change="handleRepairQuery"
              />
            </el-form-item>
            <el-form-item prop="sex">
              <el-select
                v-model="queryParams_dialog.sex"
                placeholder="请选择性别"
                clearable
                style="width: 200px"
                @change="handleRepairQuery"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleRepairQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetRepairQuery"
                >重置</el-button
              >
            </el-form-item>
          </el-form>

          <el-table
            :data="tableList_dialog"
            border
            v-loading="loading_dialog"
            @selection-change="handleSelectionChange"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column
              label="工号"
              prop="workId"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="姓名"
              prop="name"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="性别"
              prop="sex"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="所属机构"
              prop="number"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="所属部门"
              prop="deptName"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="岗位"
              prop="postName"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="手机号码"
              prop="phoneNumber"
              min-width="100"
              show-overflow-tooltip
            />
          </el-table>

          <pagination
            v-show="total_dialog > 0"
            :total="total_dialog"
            v-model:page="queryParams_dialog.current"
            v-model:limit="queryParams_dialog.pageSize"
            @pagination="getRepairList"
            layout="total,prev,pager,next"
            :autoScroll="false"
            :background="false"
          />
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit">{{
              title == "新建人员" ? "新建" : "新增"
            }}</el-button>
          </div>
        </template>
      </el-dialog>

      <!-- 修改导入对话框 -->
      <el-dialog
        class="custom-dialog"
        v-model="importDialogVisible"
        title="批量导入"
        :width="importResult.show ? '720px' : '400px'"
        :close-on-click-modal="false"
        align-center
      >
        <div class="import-result" v-if="importResult.show">
          <div class="result-header">
            <span class="result-title">本次导入：</span>
            <span class="success">成功：{{ importResult.success }}条</span>
            <span class="error">失败：{{ importResult.error }}条</span>
          </div>
          <div class="error-reason" v-if="importResult.errorReason">
            <div style="line-height: 1.8">失败原因如下：</div>
            <div>{{ importResult.errorReason }}</div>
          </div>
          <div class="warn-reason" v-if="importResult.warnReason">
            <div>提示：</div>
            <div>{{ importResult.warnReason }}</div>
          </div>
        </div>
        <div class="import-steps" v-else>
          <div class="step">
            <div class="step-title">第一步：下载模板</div>
            <el-button type="primary" @click="downloadTemplate"
              >点击下载</el-button
            >
          </div>
          <div class="step">
            <div class="step-title">第二步：选择部门</div>
            <el-tree-select
              v-model="selectedDeptId"
              :data="deptTreeList_disabled"
              :props="{
                value: 'deptId',
                label: 'deptName',
                children: 'children',
                disabled: 'disabled',
              }"
              placeholder="请选择部门"
              check-strictly
              clearable
              filterable
              style="width: 100%"
            />
          </div>
          <div class="step">
            <div class="step-title">第三步：填写信息后上传</div>
            <el-upload
              ref="uploadRef"
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :show-file-list="true"
              accept=".xlsx,.xls"
              :limit="1"
              :on-exceed="handleExceed"
              :before-remove="
                () => {
                  importFile = null;
                  return true;
                }
              "
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleClose">{{
              importResult.show ? "关闭" : "返回"
            }}</el-button>
            <el-button
              v-if="!importResult.show"
              type="primary"
              @click="handleImport"
              >导入</el-button
            >
            <el-button v-else type="primary" @click="handleRetry"
              >重新上传</el-button
            >
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="operation">
import { useRoute } from "vue-router";
import { tranListToTreeData, timeFormat, downloadBlob } from "@/utils";
// import * as XLSX from 'xlsx'
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  watch,
  computed,
  onMounted,
  nextTick,
} from "vue";
import {
  getMaintainList,
  getUserToMaintainList,
  addMaintainInUserList,
  addMaintainList,
  delMaintain,
  downloadMaintainImportTemplate,
  importMaintain,
  exportMaintain,
} from "@/api/distribution/member";
import { listTree } from "@/api/system/distribution";
import { listPost } from "@/api/system/post_new";
import { ElMessageBox } from "element-plus";
import { debounce } from "@/utils/debounce";

const route = useRoute();
const { proxy } = getCurrentInstance();
const memberRef = ref(null);
const queryRef = ref(null);
const state = reactive({
  title: "新建人员",
  dialogVisible: false,
  total: 1,
  total_dialog: 0,
  loading: false,
  loading_dialog: false,
  formInfo: {
    tableList: [
      {
        nickName: "",
        deptId: "",
        postId: "",
        phoneNumber: "",
        email: "",
        sex: "",
        remark: "",
      },
    ],
  },
  tableList_all: [],
  tableList: [],
  tableList_dialog: [
    {
      nickname: "",
      number: "",
      name: "",
      institutionId: "",
      institutionName: "",
      deptId: "",
      deptName: "",
      postId: "",
      postName: "",
      status: 0,
      sex: "",
      phoneNumber: "",
      email: "",
    },
  ],
  userList: [],
  typeList: [
    { label: "男", value: "0" },
    { label: "女", value: "1" },
  ],
  statusList: [
    { label: "启用", value: 0, type: "success" },
    { label: "停用", value: 1, type: "danger" },
  ],
  institutionTree: [],
  postList: [],
  deptList: [],
  /* scheduleList: [
    {
      name: "张三",
      field1: "执勤",
      field2: "轮休",
      field3: "执勤",
      field4: "轮休",
      field5: "执勤",
      field6: "轮休",
      field7: "执勤",
    },
  ], */
  queryParams: {
    current: 1,
    size: 10,
    nameOrNumber: "",
    phone: "",
    deptId: "",
    sex: "",
    status: "",
  },
  queryParams_dialog: {
    numberAndName: "",
    current: 1,
    pageSize: 5,
    sex: "",
  },
  rules: {
    "tableList.0.nickName": [
      { required: true, message: "用户姓名不能为空", trigger: "blur" },
      {
        pattern: /^[\u4e00-\u9fa5]{1,25}$/,
        message: "用户姓名只能输入中文汉字",
        trigger: ["blur", "change"],
      },
    ],
    "tableList.0.deptId": [
      {
        required: true,
        message: "所属部门不能为空",
        trigger: ["blur", "change"],
      },
    ],
    "tableList.0.phoneNumber": [
      { required: true, message: "手机号码不能为空", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    "tableList.0.workId": [
      {
        pattern: /^[A-Za-z0-9]/,
        message: "请输入数字+英文组合",
        trigger: ["blur"],
      },
    ],
    "tableList.0.email": [
      {
        type: "email",
        message: "请输入正确的邮箱地址",
        trigger: ["blur", "change"],
      },
    ],
  },
  selectedRows: [], // 当前页选中的行
  allSelectedRows: new Map(), // 存储所有选中的行，使用 Map 避免重复
  importDialogVisible: false,
  importFile: null,
  importResult: {
    show: false,
    success: 0,
    error: 0,
    errorReason: "",
  },
  exportDialogVisible: false,
  selectedDeptId: "",
  deptTreeList_disabled: [],
});

const {
  deptTreeList_disabled,
  rules,
  userList,
  title,
  formInfo,
  deptList,
  institutionTree,
  postList,
  tableList,
  tableList_dialog,
  typeList,
  queryParams,
  queryParams_dialog,
  dialogVisible,
  statusList,
  loading,
  total,
  total_dialog,
  selectedRows,
  importDialogVisible,
  importResult,
  exportDialogVisible,
  loading_dialog,
  selectedDeptId,
} = toRefs(state);

// 新增计算属性
const disableBatchDelete = computed(() => {
  return state.selectedRows.length === 0;
});

// 修改验证规则生成函数
function generateRules(index) {
  return {
    [`tableList.${index}.nickName`]: [
      { required: true, message: `用户姓名不能为空`, trigger: "blur" },
      {
        pattern: /^[\u4e00-\u9fa5]{1,25}$/,
        message: `用户姓名只能输入中文汉字`,
        trigger: ["blur", "change"],
      },
    ],
    [`tableList.${index}.deptId`]: [
      {
        required: true,
        message: `所属部门不能为空`,
        trigger: ["blur", "change"],
      },
    ],
    [`tableList.${index}.phoneNumber`]: [
      { required: true, message: `手机号码不能为空`, trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: `请输入正确的手机号码`,
        trigger: "blur",
      },
    ],
    [`tableList.${index}.workId`]: [
      {
        pattern: /^[A-Za-z0-9]/,
        message: `请输入数字+英文组合`,
        trigger: ["blur"],
      },
    ],
    [`tableList.${index}.email`]: [
      {
        type: "email",
        message: `请输入正确的邮箱地址`,
        trigger: ["blur", "change"],
      },
    ],
  };
}

// 添加节流函数
const throttle = (fn, delay = 1000) => {
  let timer = null;
  let isExecuting = false; // 添加执行标志位

  return async function (...args) {
    if (timer || isExecuting) return; // 如果正在执行或等待中，直接返回

    isExecuting = true; // 设置执行标志
    timer = setTimeout(() => {
      timer = null;
    }, delay);

    try {
      await fn.apply(this, args);
    } finally {
      isExecuting = false; // 确保执行完成后重置标志位
    }
  };
};

// 使用防抖包装handleSubmit函数
const handleSubmit = throttle(async () => {
  if (title.value === "新建人员") {
    // 手动触发所有字段的验证
    const validatePromises = state.formInfo.tableList.map((_, index) => {
      return new Promise((resolve) => {
        memberRef.value.validateField(
          [
            `tableList.${index}.nickName`,
            `tableList.${index}.deptId`,
            `tableList.${index}.phoneNumber`,
            `tableList.${index}.workId`,
            `tableList.${index}.email`,
          ],
          (valid) => {
            resolve(valid);
          }
        );
      });
    });

    try {
      const results = await Promise.all(validatePromises);
      const hasError = results.some((valid) => !valid);

      if (!hasError) {
        // 所有行验证通过，构造提交数据
        const submitData = formInfo.value.tableList.map((item) => ({
          nickName: item.nickName,
          deptId: item.deptId,
          postId: item.postId,
          phoneNumber: item.phoneNumber,
          email: item.email,
          sex: item.sex,
          remark: item.remark,
          workId: item.workId,
        }));
        console.log("新建人员提交的参数：", submitData);

        proxy.$modal.loading();
        // 调用添加维护人员API
        const res = await addMaintainList(submitData);
        console.log("批量新建运维人员结果", res);
        if (res.code === 200) {
          dialogVisible.value = false;
          // 格式化返回信息，使用 HTML 标签进行换行
          let message = res.data.includes("失败原因")
            ? res.data.replace("失败原因：", "<br>失败原因：")
            : res.data;

          message = message.includes("教职工中心")
            ? message.replace("教职工中心", "<br>教职工中心")
            : message;
          // 使用 ElMessageBox 显示返回数据
          ElMessageBox.alert(message, "新建结果", {
            confirmButtonText: "确定",
            type: "info",
            dangerouslyUseHTMLString: true,
            callback: () => {
              getList();
            },
          });
        } else {
          // proxy.$modal.msgError(res.msg || "新增失败");
        }
      } else {
        proxy.$modal.msgError("请检查所有行的必填项和格式是否正确");
      }
    } catch (error) {
      console.log(error);
      // proxy.$modal.msgError("新增失败");
    } finally {
      proxy.$modal.closeLoading();
    }
  } else {
    // 处理选择人员的逻辑
    const selectedUserIds = selectedRows.value.map((row) => row.userId);

    if (selectedUserIds.length === 0) {
      proxy.$modal.msgWarning("请至少选择一名人员");
      return;
    }

    const params = {
      userIdList: selectedUserIds,
    };

    try {
      const res = await addMaintainInUserList(params);
      console.log(res, "选择人员后返回的参数");
      if (res.code === 200) {
        if (!!res.data) {
          console.log(res.data, "教职工检测失败");
          ElMessageBox.alert(res.data, "系统提示", {
            type: "warning",
            confirmButtonText: "确认",
            callback: (action) => {},
          });
        }
        proxy.$modal.msgSuccess("添加成功");
        dialogVisible.value = false;
        getList();
      } else {
        // proxy.$modal.msgError(res.msg || "添加失败");
      }
    } catch (error) {
      // proxy.$modal.msgError("添加失败");
    }
  }
}, 2500); // 设置2.5秒的节流时间

// 修改handleAddUser函数
function handleAddUser() {
  const newIndex = state.formInfo.tableList.length;
  state.formInfo.tableList.push({
    nickName: "",
    deptId: "",
    postId: "",
    phoneNumber: "",
    email: "",
    sex: "",
    remark: "",
    workId: "",
  });

  // 更新所有行的验证规则
  const newRules = {};
  state.formInfo.tableList.forEach((_, idx) => {
    Object.assign(newRules, generateRules(idx));
  });
  rules.value = newRules;

  // 强制下一个tick更新验证规则
  nextTick(() => {
    memberRef.value?.clearValidate();
  });
}

function handleRepairDel(index) {
  state.formInfo.tableList.splice(index, 1);

  // 重新生成所有行的验证规则
  const newRules = {};
  state.formInfo.tableList.forEach((_, idx) => {
    Object.assign(newRules, generateRules(idx));
  });
  rules.value = newRules;

  // 确保至少保留一行数据
  if (state.formInfo.tableList.length === 0) {
    handleAddUser();
  }
}

function handleAdd() {
  state.title = "新建人员";
  state.formInfo = {
    tableList: [
      {
        nickName: "",
        deptId: "",
        postId: "",
        phoneNumber: "",
        email: "",
        sex: "",
        remark: "",
      },
    ],
  };
  state.dialogVisible = true;
}

function handleJoin() {
  state.title = "请选择运维人员";
  state.dialogVisible = true;
  // 重置查询条件
  state.queryParams_dialog = {
    numberAndName: "",
    current: 1,
    pageSize: 5,
    sex: "",
  };
  // 调用获取运维人员列表接口
  getRepairList();
}

function handleRepairQuery() {
  state.queryParams_dialog.current = 1;
  getRepairList();
}

function resetRepairQuery() {
  state.queryParams_dialog = {
    numberAndName: "",
    current: 1,
    pageSize: 5,
    sex: "",
  };
  getRepairList();
}

function handleEdit({ row, $index }) {
  state.curIdx = $index;
  state.title = "编辑人员";
  // 判断该行数据的任务状态，已完成则弹窗里的任务状态项为禁用
  state.formInfo = JSON.parse(JSON.stringify(row));
  state.dialogVisible = true;
}

function handleCancel() {
  memberRef.value.resetFields();
  state.dialogVisible = false;
}

function handleDel({ row, $index }) {
  proxy.$modal.confirm(`确定要删除姓名为${row.name}的人员信息？`).then(() => {
    state.tableList_all.splice($index, 1);
    state.scheduleList.splice($index, 1);

    setTimeout(() => {
      proxy.$modal.msgSuccess("操作成功");
      getList();
    }, 500);
  });
}

function handleQuery() {
  state.queryParams.current = 1;
  getList();
}

function resetQuery() {
  queryRef.value?.resetFields();
  state.queryParams = {
    current: 1,
    size: 10,
    nameOrNumber: "",
    phone: "",
    deptId: "",
    sex: "",
    status: "",
  };
  // 清空所有选中状态
  state.allSelectedRows.clear();
  state.selectedRows = [];
  handleQuery();
}

function handleSelectionChange(selection) {
  state.selectedRows = selection;

  // 更新 allSelectedRows
  const currentPageRows = state.tableList;

  // 先移除当前页所有行的选中状态
  currentPageRows.forEach((row) => {
    if (!selection.find((selected) => selected.userId === row.userId)) {
      state.allSelectedRows.delete(row.userId);
    }
  });

  // 添加当前选中的行
  selection.forEach((row) => {
    state.allSelectedRows.set(row.userId, row);
  });
}

function getRepairList() {
  state.loading_dialog = true;
  const params = {
    current: state.queryParams_dialog.current,
    size: state.queryParams_dialog.pageSize,
    nickNameAndWorkId: state.queryParams_dialog.numberAndName,
    sex: state.queryParams_dialog.sex,
  };
  console.log("params:", params);

  getUserToMaintainList(params)
    .then((res) => {
      if (res.code === 200) {
        console.log("选择人员res.data:", res.data);

        state.tableList_dialog = res.data.records.map((item) => ({
          number: item.workId || item.userCode,
          name: item.nickName,
          sex: item.sex == "0" ? "男" : "女",
          institutionName: item.deptName || "-",
          workId: item.workId || "-",
          deptName: item.deptName || "-",
          postName: item.postName || "-",
          phoneNumber: item.phoneNumber || "-",
          userId: item.userId,
          userCode: item.userCode,
          email: item.email || "-",
          deptId: item.deptId,
          postId: item.postId,
          status: item.status,
          isBindWx: item.isBindWx,
          createTime: item.createTime,
        }));

        state.total_dialog = res.data.total;
        state.queryParams_dialog.pageSize = res.data.size;
        state.queryParams_dialog.current = res.data.current;
      } else {
        state.tableList_dialog = [];
        state.total_dialog = 0;
        // proxy.$modal.msgError(res.msg || "获取数据失败");
      }
    })
    .catch((error) => {
      console.error("Failed to fetch user list:", error);
      state.tableList_dialog = [];
      state.total_dialog = 0;
      // proxy.$modal.msgError("获取人员列表失败");
    })
    .finally(() => {
      state.loading_dialog = false;
    });
}

function getList() {
  state.loading = true;
  const params = {
    current: state.queryParams.current,
    size: state.queryParams.size,
    nickNameAndWorkId: state.queryParams.nameOrNumber,
    phoneNumber: state.queryParams.phone,
    deptId: state.queryParams.deptId,
    sex: state.queryParams.sex,
    status: state.queryParams.status,
  };
  console.log("params:", params);

  getMaintainList(params)
    .then((res) => {
      if (res.code === 200 && res.data && res.data.records) {
        console.log("res.data:", res.data);

        state.tableList = res.data.records.map((item) => ({
          ...item,
          sex: item.sex === "0" ? "男" : item.sex === "1" ? "女" : "-",
          status: parseInt(item.status),
          workId: item.workId || "-",
          phoneNumber: item.phoneNumber || "-",
          institutionName: item.deptName || "-",
          email: item.email || "-",
          createTime: item.createTime || "-",
          nickName: item.nickName || "-",
          isBindWx: item.isBindWx || "-",
          postName: item.postName || "-",
          remark: item.remark || "-",
        }));
        state.total = res.data.total;
      } else {
        state.tableList = [];
        state.total = 0;
        // proxy.$modal.msgError(res.msg || "获取数据格式异常");
      }
    })
    .catch((error) => {
      state.tableList = [];
      state.total = 0;
      // proxy.$modal.msgError("获取人员列表失败");
    })
    .finally(() => {
      state.loading = false;
    });
}

function handleBatchDelete() {
  const allSelectedCount = state.allSelectedRows.size;

  if (allSelectedCount === 0) {
    proxy.$modal.msgWarning("请至少选择一条记录");
    return;
  }

  proxy.$modal
    .confirm(`确定批量删除选中的${allSelectedCount}条数据？`)
    .then(() => {
      const userIdList = Array.from(state.allSelectedRows.keys());

      delMaintain({ userIdList })
        .then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("批量删除成功");
            // 清空选中状态
            state.allSelectedRows.clear();
            state.selectedRows = [];
            // 刷新列表
            getList();
          } else {
            proxy.$modal.msgError(res.msg || "删除失败");
          }
        })
        .catch((error) => {
          console.error("Failed to delete:", error);
          // proxy.$modal.msgError("删除失败");
        });
    });
}

function handleBatchImport() {
  state.importDialogVisible = true;
  // 重置导入结果
  state.importResult = {
    show: false,
    success: 0,
    error: 0,
    errorReason: "",
  };
  // 重置其他相关状态
  state.importFile = null;
  state.selectedDeptId = "";

  // 加载部门数据
  getDeptList();
}

function downloadTemplate() {
  downloadMaintainImportTemplate()
    .then((res) => {
      if (res instanceof Blob) {
        const blob = res;
        const objUrl = window.URL.createObjectURL(blob);

        const a = document.createElement("a");
        a.setAttribute("href", objUrl);
        a.setAttribute("download", "运维人员信息模板.xls");
        a.click();
        window.URL.revokeObjectURL(objUrl);

        proxy.$modal.msgSuccess("模板下载成功");
      } else {
        proxy.$modal.msgError("模板下载失败");
      }
    })
    .catch(() => {
      proxy.$modal.msgError("模板下载失败");
    });
}

// 使用节流包装handleImport函数
const handleImport = throttle(async () => {
  if (!state.selectedDeptId) {
    proxy.$modal.msgWarning("请先选择部门");
    return;
  }

  if (!state.importFile) {
    proxy.$modal.msgWarning("请先选择要导入的文件");
    return;
  }

  const formData = new FormData();
  formData.append("file", state.importFile.raw);
  formData.append("deptId", state.selectedDeptId);

  try {
    proxy.$modal.loading();
    const res = await importMaintain(formData);
    if (res.code === 200) {
      console.log("导入结果:", res);
      state.importResult = {
        show: true,
        success: res.data?.successCount || 0,
        error: res.data?.errorCount || 0,
        errorReason: formatErrorReason(res.data?.errorInfoList),
        warnReason: res.data?.successMsg,
      };
      if (res.data?.successCount > 0) {
        getList();
      }
    }
  } catch (error) {
    console.error("Import failed:", error);
  } finally {
    proxy.$modal.closeLoading();
  }
}, 2500);

function handleClose() {
  state.importDialogVisible = false;
  state.importResult.show = false;
  state.importFile = null;
  state.selectedDeptId = "";
  // 清空上传文件列表
  uploadRef.value?.clearFiles();
}

function handleRetry() {
  state.importResult.show = false;
  state.importFile = null;
  state.selectedDeptId = "";
}

function handleBatchExport() {
  const allSelectedCount = state.allSelectedRows.size;

  proxy.$modal
    .confirm(
      `确定批量导出${
        allSelectedCount > 0 ? `选中的${allSelectedCount}条数据` : "所有数据"
      }?`
    )
    .then(() => {
      const params = {};

      // 如果有选中行，添加 userIdStr 参数
      if (allSelectedCount > 0) {
        params.userIdStr = Array.from(state.allSelectedRows.keys()).join(",");
      }
      console.log("params:", params);
      exportMaintain(params)
        .then((res) => {
          if (res instanceof Blob) {
            const blob = res;
            const filename = `运维人员信息_${timeFormat(
              new Date().getTime(),
              "yyyy-mm-dd"
            )}.xlsx`;

            const link = document.createElement("a");
            link.href = window.URL.createObjectURL(blob);
            link.download = filename;
            link.click();
            window.URL.revokeObjectURL(link.href);

            proxy.$modal.msgSuccess("导出成功");
          } else {
            proxy.$modal.msgError("导出失败：返回格式错误");
          }
        })
        .catch((error) => {
          proxy.$modal.msgError("导出失败");
        });
    });
}

function handleView({ row }) {
  proxy.$router.push({
    path: "/distribution/memberInfo",
    query: {
      id: row.userId,
      url: route.fullPath,
    },
  });
}

function getDeptList() {
  /* let params = {
    deptName: '',
    status: '0'
  } */
  return listTree().then((res) => {
    if (res.code === 200) {
      console.log("获取部门列表:", res.data);

      state.deptList = res.data;
      state.deptTreeList_disabled = addAttr(res.data);
    }
    return res;
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status === "1"; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

function getPostList() {
  const params = {
    pageNum: 1,
    pageSize: 999999, // 获取所有岗位
    status: "0",
  };
  return listPost(params).then((res) => {
    if (res.code === 200) {
      postList.value = res.data.rows;
      console.log("获取岗位列表:", postList.value);
    }
    return res;
  });
}

// 限制上传一个文件，重新选择文件替换原来的文件
const handleExceed = (files) => {
  uploadRef.value.clearFiles();
  nextTick(() => {
    uploadRef.value.handleStart(files[0]);
  });
};

function handleFileChange(file) {
  if (!file) {
    proxy.$modal.msgError("请选择文件");
    return false;
  }

  // 验证文件类型
  const isExcel =
    file.raw.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.raw.type === "application/vnd.ms-excel";
  if (!isExcel) {
    proxy.$modal.msgError("只能上传 Excel 文件!");
    return false;
  }

  state.importFile = file;
  return true;
}

function formatErrorReason(errorList) {
  if (!errorList || errorList.length === 0) return "";

  return errorList
    .map((error) => {
      return `第${error.row}行: ${error.cause}`;
    })
    .join("\n");
}

// 添加上传组件的引用
const uploadRef = ref(null);

onMounted(() => {
  getList();
  getDeptList();
  getPostList();
});
</script>

<style lang="scss" scoped>
.dialog-add {
  text-align: center;
  font-size: 16px;
  height: 50px;
  line-height: 50px;
  color: #4095e5;
  cursor: pointer;
  transition: all 0.5s;
  &:hover {
    background-color: #f6fbff;
  }
}
// 添加导入导出相关样式
.import-steps {
  padding: 20px 0;

  .step {
    margin-bottom: 20px;

    &-title {
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}

.import-result {
  padding: 20px;
  text-align: center;

  .result-header {
    display: flex;
    align-items: center;
    // justify-content: center;
    margin-bottom: 15px;

    .result-title {
      margin-right: 10px;
      font-weight: bold;
    }

    .success {
      color: #67c23a;
      margin-right: 10px;
    }

    .error {
      color: #f56c6c;
    }
  }

  .error-reason {
    text-align: left;
    color: #f56c6c;
    font-size: 14px;
    margin-top: 15px;
    padding: 10px;
    background-color: #fef0f0;
    border-radius: 4px;

    div:last-child {
      line-height: 1.8;
      white-space: pre-line;
    }
  }

  .warn-reason {
    text-align: left;
    color: #e6a23c;
    font-size: 14px;
    margin-top: 15px;
    padding: 10px;
    background-color: #fdf6ec;
    border-radius: 4px;

    div:last-child {
      white-space: pre-line;
    }
  }
}

.member-form {
  .el-table {
    max-height: calc(90vh - 250px); // 为表格设置最大高度
    overflow-y: auto; // 启用垂直滚动
  }
}

// 优化滚动条样式（可选）
:deep(.el-dialog__body)::-webkit-scrollbar,
.member-form .el-table::-webkit-scrollbar {
  width: 6px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-thumb,
.member-form .el-table::-webkit-scrollbar-thumb {
  background-color: #dcdfe6;
  border-radius: 3px;
}

:deep(.el-dialog__body)::-webkit-scrollbar-track,
.member-form .el-table::-webkit-scrollbar-track {
  background-color: #f5f7fa;
}
</style>
