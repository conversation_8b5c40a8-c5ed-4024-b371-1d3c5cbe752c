<template>
  <div class="fileInfo">
    <div class="fileInfo-header">
      <div
        class="fileInfo-header_item"
        v-for="(item, index) in activeList"
        :key="index"
        :class="[active == index ? 'active' : '', `item${index + 1}`]"
      >
        {{ item }}
      </div>
    </div>

    <div v-show="active == 0" class="fileInfo-main">
      <el-form ref="formRef" :model="form" :rules="rules">
        <el-descriptions title="" border>
          <el-descriptions-item
            v-if="!!templateConfig.projectStatus"
            label-class-name="label-width"
            class-name="value-width"
            label="项目信息"
          >
            <el-form-item
              label=""
              prop="projectName"
              v-if="!!templateConfig.projectStatus"
            >
              <el-input
                placeholder="请选择项目信息"
                readonly
                v-model="form.projectName"
                :disabled="!props.isAdd"
                style="width: 250px"
                @click="handleShowDialog('请选择项目信息')"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="!!templateConfig.relationStatus"
            label-class-name="label-width"
            class-name="value-width"
            label="干系人信息"
          >
            <el-form-item
              label=""
              prop="stakeholderName"
              v-if="!!templateConfig.relationStatus"
            >
              <el-input
                placeholder="请先选择项目信息"
                disabled
                v-model="form.stakeholderName"
                style="width: 250px"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="!!templateConfig.assetStatus"
            label-class-name="label-width"
            class-name="value-width"
            label="资产信息"
          >
            <el-form-item
              label=""
              prop="deviceName"
              v-if="!!templateConfig.assetStatus"
            >
              <el-input
                placeholder="请选择资产信息"
                readonly
                v-model="form.deviceName"
                :disabled="!props.isAdd"
                style="width: 250px"
                @click="handleShowDialog('请选择资产信息')"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="!!templateConfig.workOrderStatus"
            label-class-name="label-width"
            class-name="value-width"
            label="运维工单信息"
          >
            <el-form-item
              label=""
              prop="workOrderCode"
              v-if="!!templateConfig.workOrderStatus"
            >
              <el-input
                placeholder="请选择运维工单"
                readonly
                :disabled="!props.isAdd"
                v-model="form.workOrderCode"
                style="width: 250px"
                @click="handleShowDialog('请选择运维工单信息')"
              />
            </el-form-item>
          </el-descriptions-item>
          <!-- <el-descriptions-item
            v-if="!!templateConfig.statisticsStatus"
            label-class-name="label-width"
            class-name="value-width"
            label="统计分析信息"
          >
            <el-form-item label="" prop="templateType">
              <el-input
                placeholder="请选择"
                readonly
                v-model="form.templateName"
                style="width: 250px"
              />
            </el-form-item>
          </el-descriptions-item> -->
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="文档名称"
          >
            <el-form-item
              prop="documentName"
              label=""
              required
              :inline-message="false"
              :rules="[
                {
                  required: true,
                  message: '文档名称不能为空',
                  trigger: 'blur',
                },
              ]"
            >
              <el-input
                v-model="form.documentName"
                :disabled="props.type == 1 && !props.isAdd"
                maxlength="50"
                placeholder="请输入文档名称"
                style="width: 100%"
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>

    <CanvasEditor
      v-show="active == 1"
      ref="canvasEditor"
      :parentContent="parentContent"
      :view="view"
      :type="props.type"
      @save-content="handleSaveCanvasEditorContent"
    />

    <div class="fileInfo-footer">
      <el-button v-if="active == 0" type="primary" @click="handleNext">{{
        props.type == 1 && !props.isAdd ? "查看文档内容" : "下一步"
      }}</el-button>
      <el-button v-if="active == 1" type="primary" @click="active = 0">{{
        props.type == 1 && !props.isAdd ? "查看文档基本信息" : "上一步"
      }}</el-button>
      <el-button
        v-if="active == 1 && (props.type == 2 || props.isAdd)"
        type="success"
        @click="handleSave"
        v-throttle
        >{{ props.isAdd ? "完成创建" : "保存" }}</el-button
      >
      <el-button @click="handleBack">返回</el-button>
    </div>

    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="800"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancel"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
        @submit.native.prevent
      >
        <el-form-item prop="deviceCode" v-if="title == '请选择资产信息'">
          <el-input
            v-model="queryParams.deviceCode"
            placeholder="请输入设备编码"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="deviceName" v-if="title == '请选择资产信息'">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label=""
          prop="workOrderCode"
          v-if="title == '请选择运维工单信息'"
        >
          <el-input
            v-model="queryParams.workOrderCode"
            placeholder="请输入工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item
          label=""
          prop="positionId"
          v-if="title == '请选择资产信息'"
        >
          <el-tree-select
            v-model="queryParams.positionId"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            check-strictly
            style="width: 200px"
            clearable
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item
          v-if="title == '请选择项目信息'"
          label=""
          prop="searchKey"
        >
          <el-input
            placeholder="请输入编号/名称/项目负责人"
            clearable
            style="width: 200px"
            v-model="queryParams.searchKey"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="tableList"
        border
        @row-click="rowSingleClick"
        v-loading="loading"
      >
        <el-table-column align="center" width="70">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="(val) => handleSingleChange(val, scope.$index)"
              @click.native.stop=""
            />
          </template>
        </el-table-column>
        <template v-if="title == '请选择资产信息'">
          <el-table-column
            label="设备编码"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">{{ row.deviceCode || "-" }}</template>
          </el-table-column>
          <el-table-column
            label="设备名称"
            prop="deviceName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">{{ row.deviceName || "-" }}</template>
          </el-table-column>
          <el-table-column
            label="设备类型"
            prop="deviceType"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">{{ row.deviceType || "-" }}</template>
          </el-table-column>
          <el-table-column
            label="安装位置"
            prop="installAddressStr"
            min-width="120"
          >
            <template #default="{ row }">{{
              row.installAddress || "-"
            }}</template>
          </el-table-column>
        </template>
        <template v-if="title == '请选择运维工单信息'">
          <el-table-column
            label="工单编号"
            prop="workOrderCode"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="报障人姓名"
            prop="repairName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="报障时间"
            prop="submittedTime"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="报障来源"
            prop="channel"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
        </template>
        <template v-if="title == '请选择项目信息'">
          <el-table-column
            label="项目编号"
            prop="projectNo"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="项目名称"
            prop="projectName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="项目负责人"
            prop="responsibleNameStr"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="项目归属部门"
            prop="projectDeptName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
        </template>
        <template v-if="title == '请选择干系人信息'">
          <el-table-column
            label="工号"
            prop="number"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="姓名"
            prop="name"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属机构"
            prop="type"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="所属部门"
            prop="name"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="性别"
            prop="name"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column label="操作" min-width="100" align="center">
            <template #default="scope"> </template>
          </el-table-column>
        </template>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="
          queryParams[title == '请选择资产信息' ? 'current' : 'pageNum']
        "
        v-model:limit="
          queryParams[title == '请选择资产信息' ? 'size' : 'pageSize']
        "
        @pagination="getList"
        :background="false"
        :autoScroll="false"
        layout="total,prev,pager,next"
      />
      <template #footer>
        <el-button @click.stop="handleCancel">返回</el-button>
        <el-button type="primary" @click.stop="handleConfirm" v-throttle
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, getCurrentInstance, toRefs, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import CanvasEditor from "@/views/canvas-editor/index.vue";
import { getSchoolTemplateDetail } from "@/api/fileMamage/template";
import {
  getSchoolDocumentDetail,
  addSchoolDocument,
  editSchoolDocument,
  checkDocumentNameUnique,
} from "@/api/fileMamage/file";
import { deviceMaintenanceList } from "@/api/mediaTeach/trouble";
import { devicePage } from "@/api/mediaTeach/ledger";
import { getPositionTree } from "@/api/mediaTeach/position";
import { getSchoolProjectPage } from "@/api/order";

const { proxy } = getCurrentInstance();
const formRef = ref(null);
const { nickName, roles } = useUserStore(); // 获取当前登录的用户信息
const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  templateId: {
    type: String,
    default: "",
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
  type: {
    type: String,
    default: "1",
  },
  fromTemp: {
    type: String,
    default: "0",
  },
});
const active = ref(0);
const title = ref("请选择资产信息");
const activeList = ref([
  props.type == 1 && !props.isAdd ? "文档基本信息" : "基本信息填写",
  props.type == 1 && !props.isAdd ? "文档内容" : "内容编辑",
]);
const dialogVisible = ref(false);
const queryRef = ref(null);
const templateConfig = ref({
  assetStatus: 0,
  projectStatus: 0,
  relationStatus: 0,
  workOrderStatus: 0,
  statisticsStatus: 0,
});

const form = ref({
  isShowStakeholder: 0,
  projectName: "",
  projectId: "",
  documentName: "",
  documentContent: "",
  templateId: props.templateId || "",
  templateTypeId: "",
  troubleId: "",
  workOrderCode: "",
  deviceId: "",
});
const state = reactive({
  loading: false,
  total: 0,
  tableList: [],
  queryParams: {
    pageSize: 5,
    pageNum: 1,
    current: 1,
    size: 5,
    type: 2,
    troubleStatusList: [0, 1, 2, 3, 4],
    deviceCode: "",
    workOrderCode: "",
    numberAndNameAndUser: "",
    deviceName: "",
    positionId: "",
  },
  positionTreeList: [],
  curDevice: {},
  curTask: {},
  curProject: {},
  positionResult: {
    names: [],
    ids: [],
  },
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  rules: {
    projectName: [
      {
        required: true,
        message: "项目信息不能为空",
        trigger: ["blur", "change"],
      },
    ],
    stakeholderName: [
      {
        required: true,
        message: "干系人信息不能为空",
        trigger: ["blur", "change"],
      },
    ],
    deviceName: [
      {
        required: true,
        message: "资产信息不能为空",
        trigger: ["blur", "change"],
      },
    ],
    workOrderCode: [
      {
        required: true,
        message: "运维工单信息不能为空",
        trigger: ["blur", "change"],
      },
    ],
    documentName: [
      {
        required: true,
        validator: validName,
        trigger: ["blur"],
      },
    ],
  },
});
const {
  loading,
  total,
  positionTreeList,
  rules,
  tableList,
  queryParams,
  positionTreeLis,
  curDevice,
  curProject,
  curTask,
  positionResult,
  positionProps,
} = toRefs(state);
// 存放父组件传递的数据
const parentContent = ref(undefined);
// 存放子组件数据
const content = ref(undefined);
// 标识符
const view = ref(undefined);
// 组件引用
const canvasEditor = ref(null);

const route = useRoute();

async function checkName() {
  let flag = true;
  let obj = {
    documentName: form.value.documentName,
    documentId: "",
  };
  // if (props.type == 2) {
  //   obj.documentId = props.id,
  // } else {
  //   delete obj.documentId
  // }
  if (props.type == 2) {
    obj.documentId = props.id;
  } else {
    delete obj.documentId;
  }
  let res = await checkDocumentNameUnique(obj);
  flag = res.data;
  console.log(!flag);
  return !flag;
}

async function validName(rules, value, callback) {
  console.log(rules, value);
  if (value == "") {
    callback(new Error("文档名称不能为空"));
  } else if (await checkName(value)) {
    callback(new Error("文档名称已存在"));
  } else {
    callback();
  }
}

// 初始化数据
onMounted(() => {
  if (props.id) {
    getFileContent();
  } else if (props.templateId) {
    getTemplateContent();
  } else {
    templateConfig.value = {
      assetStatus: 1,
      projectStatus: 1,
      relationStatus: 1,
      workOrderStatus: 1,
      statisticsStatus: 0,
    };
    getEditorContent();
  }
});

async function getPositionTreeList() {
  await getPositionTree({}).then((response) => {
    state.positionTreeList = response.data;
  });
}

// 确保 getEditorContent 方法正确初始化编辑器内容
function getEditorContent() {
  let obj = {
    main: [],
  };

  parentContent.value = JSON.parse(JSON.stringify(obj));
}

/** 弹窗搜索按钮操作 */
function handleQuery() {
  state.queryParams.current = 1;
  state.queryParams.pageNum = 1;
  getList();
}

/** 弹窗重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.curDevice = {};
  state.curProject = {};
  state.curTask = {};
  handleQuery();
}

function clearCheck() {
  state.tableList = state.tableList.map((item) => {
    item.checked = false;
    return item;
  });
}

function handleSingleChange(val, index) {
  console.log(val, index, state.tableList);
  if (title.value == "请选择项目信息") {
    if (val) {
      clearCheck();
      state.tableList[index].checked = true;
      state.curProject = JSON.parse(JSON.stringify(state.tableList[index]));
      console.log("check", val);
    } else {
      state.curProject = {};
    }
  }
  if (title.value == "请选择运维工单信息") {
    if (val) {
      clearCheck();
      state.tableList[index].checked = true;
      state.curTask = JSON.parse(JSON.stringify(state.tableList[index]));
      console.log("check", val);
    } else {
      state.curTask = {};
    }
  }
  if (title.value == "请选择资产信息") {
    if (val) {
      clearCheck();
      state.tableList[index].checked = true;
      state.curDevice = JSON.parse(JSON.stringify(state.tableList[index]));
      console.log("check", val);
    } else {
      state.curDevice = {};
    }
  }
}

function rowSingleClick(row) {
  console.log("点击单行", state.tableList, row);
  if (title.value == "请选择项目信息") {
    const idx = state.tableList.findIndex((_) => _.projectId == row.projectId);
    if (row.checked) {
      state.tableList[idx].checked = false;
      state.curProject = {};
    } else {
      clearCheck();
      state.tableList[idx].checked = true;
      state.curProject = JSON.parse(JSON.stringify(row));
    }
  }
  if (title.value == "请选择运维工单信息") {
    const idx = state.tableList.findIndex((_) => _.troubleId == row.troubleId);
    if (row.checked) {
      state.tableList[idx].checked = false;
      state.curTask = {};
    } else {
      clearCheck();
      state.tableList[idx].checked = true;
      state.curTask = JSON.parse(JSON.stringify(row));
    }
  }
  if (title.value == "请选择资产信息") {
    const idx = state.tableList.findIndex((_) => _.deviceId == row.deviceId);
    if (row.checked) {
      state.tableList[idx].checked = false;
      state.curDevice = {};
    } else {
      clearCheck();
      state.tableList[idx].checked = true;
      state.curDevice = JSON.parse(JSON.stringify(row));
    }
  }
}

function getTemplateContent(id) {
  getSchoolTemplateDetail(id ? id : props.templateId).then((resp) => {
    console.log("模板详情", resp);
    if (resp.data?.templateContent) {
      parentContent.value = JSON.parse(resp.data.templateContent);
    }
    const {
      assetStatus = 0,
      projectStatus = 0,
      relationStatus = 0,
      workOrderStatus = 0,
      statisticsStatus = 0,
      templateTypeId = "",
    } = resp.data;
    form.value.templateTypeId = templateTypeId;
    form.value.isShowStakeholder = relationStatus;
    templateConfig.value = {
      assetStatus,
      projectStatus,
      relationStatus,
      workOrderStatus,
      statisticsStatus,
    };
  });
}

function getFileContent() {
  getSchoolDocumentDetail(props.id).then((resp) => {
    if (resp.data) {
      const {
        documentId,
        documentName,
        projectId,
        projectName,
        deviceId,
        deviceName,
        troubleId,
        troubleNo,
        stakeholderName,
        documentContent,
        templateId,
      } = resp.data;
      form.value = {
        ...form.value,
        documentName,
        projectId,
        projectName,
        deviceId,
        deviceName,
        troubleId,
        troubleNo,
        stakeholderName,
        documentContent,
        documentId,
        workOrderCode: troubleNo,
      };
      if (documentContent) {
        parentContent.value = JSON.parse(documentContent);
      }
      if (templateId) {
        getTemplateContent(templateId);
      }
    }
    console.log("文档详情", resp);
  });
}

function handleShowDialog(val) {
  title.value = val;
  dialogVisible.value = true;
  handleQuery();
}

function handleCancel() {
  resetQuery()
  dialogVisible.value = false;
  state.curDevice = {};
  state.curProject = {};
  state.curTask = {};
}

function handleConfirm() {
  if (title.value == "请选择项目信息") {
    if (!state.curProject.projectId) {
      proxy.$modal.msgWarning("请选择项目信息");
      return;
    }
    const {
      stakeholderNameStr = "",
      projectId = "",
      projectName,
    } = state.curProject;
    form.value.projectId = projectId;
    form.value.projectName = projectName;
    form.value.stakeholderName = stakeholderNameStr;
  }
  if (title.value == "请选择运维工单信息") {
    if (!state.curTask.troubleId) {
      proxy.$modal.msgWarning("请选择运维工单信息");
      return;
    }
    const { troubleId = "", workOrderCode = "" } = state.curTask;
    form.value.troubleId = troubleId;
    form.value.workOrderCode = workOrderCode;
  }
  if (title.value == "请选择资产信息") {
    if (!state.curDevice.deviceId) {
      proxy.$modal.msgWarning("请选择资产信息");
      return;
    }
    const { deviceId = "", deviceName = "" } = state.curDevice;
    form.value.deviceId = deviceId;
    form.value.deviceName = deviceName;
  }
  dialogVisible.value = false;
}

function handleNext() {
  if (props.type == 1) {
    active.value = 1;
    return;
  }
  formRef.value.validate((valid) => {
    if (valid) {
      active.value = 1;
    }
  });
}

function handleBack() {
  if (route.query.from === "changeRecord") {
    proxy.$tab.closeOpenPage("/system/changeRecord");
  } else {
    proxy.$tab.closeOpenPage(
      props.fromTemp == "1"
        ? "/fileManage/templateManage/fileTemplate"
        : "/fileManage/file"
    );
  }
}

function getList() {
  state.loading = true;
  clearCheck();
  if (title.value == "请选择项目信息") {
    getSchoolProjectPage(state.queryParams)
      .then((resp) => {
        console.log("项目列表", resp);
        if (resp.data) {
          const { rows = [], total = 0 } = resp.data;
          state.tableList = rows.map((item) => {
            const {
              sysResponsibleName,
              responsibleName,
              sysStakeholderName,
              stakeholderName,
            } = item;
            item.responsibleNameStr = (sysResponsibleName?.split("、") || [])
              .concat(responsibleName?.split("、") || [])
              .filter((_) => !!_)
              .join("、");
            item.stakeholderNameStr = (sysStakeholderName?.split("、") || [])
              .concat(stakeholderName?.split("、") || [])
              .filter((_) => !!_)
              .join("、");
            item.checked = state.curProject.projectId == item.projectId;
            return item;
          });
          state.total = total;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
  if (title.value == "请选择运维工单信息") {
    deviceMaintenanceList(state.queryParams)
      .then((resp) => {
        console.log("工单列表", resp);
        if (resp.data) {
          const { maintenanceWebListVOList = [], total = 0 } = resp.data;
          state.tableList = maintenanceWebListVOList.map((item) => {
            item.checked = state.curTask.troubleId == item.troubleId;
            return item;
          });
          state.total = total;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
  if (title.value == "请选择资产信息") {
    getPositionTreeList();
    const { positionId } = state.queryParams;
    devicePage({
      ...state.queryParams,
      positionIds: positionId ? [positionId] : null,
    })
      .then((resp) => {
        console.log("设备列表", resp);
        if (resp.data) {
          const { records = [], total = 0 } = resp.data.page;
          state.tableList = records.map((item) => {
            item.checked = state.curDevice.deviceId == item.deviceId;
            return item;
          });
          state.total = total;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
}

function handleSave() {
  formRef.value.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      canvasEditor.value.saveContent();
      form.value.isShowStakeholder = templateConfig.value.relationStatus;
      form.value.source = "文档管理";
      if (props.isAdd) {
        addSchoolDocument(form.value)
          .then((resp) => {
            proxy.$modal.msgSuccess("操作成功");
            proxy.$modal.closeLoading();
            handleBack();
          })
          .catch(() => proxy.$modal.closeLoading());
      } else {
        delete form.value.templateId;
        delete form.value.templateTypeId;
        console.log(form.value, "修改");
        editSchoolDocument(form.value)
          .then((resp) => {
            proxy.$modal.msgSuccess("操作成功");
            proxy.$modal.closeLoading();
            handleBack();
          })
          .catch(() => proxy.$modal.closeLoading());
      }
    }
  });
}

// 处理子组件传递的数据
const handleSaveCanvasEditorContent = (data) => {
  console.log("从子组件接收到的数据:", data);
  // 将data数据转换为 json 格式的数据, 方便入库处理
  form.value.documentContent = JSON.stringify(data.data);
  console.log("转换后的数据 content 为: ", form.value.documentContent);
};
</script>

<style lang="scss" scoped>
.fileInfo {
  width: 100%;
  background-color: #fff;
  padding: 0 20px;

  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }

    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }

  &-header {
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 2vw;
    gap: 0 10vw;
    font-size: 14px;

    &_item {
      // border: 1px solid red;
      display: flex;
      align-items: center;
      gap: 0 10px;

      &::before {
        content: "1";
        width: 2.5vw;
        height: 2.5vw;
        display: inline-block;
        text-align: center;
        line-height: 2.5vw;
        background-color: #4095e5;
        color: #fff;
        border-radius: 50%;
      }

      &.item2::before {
        content: "2";
      }

      &.active {
        color: #4095e5;
      }
    }
  }

  &-main {
    min-height: 550px;
  }

  &-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 0 20px;
  }
}
</style>