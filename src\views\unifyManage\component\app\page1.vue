<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <div class="block-title">资产覆盖率</div>
        <div class="battery">
          <span>{{ objData.assetCoverageStatistics.coverage }}</span>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">部门资产总数</div>
        <div class="table block">
          <div class="table-row opt-title">
            <div class="table-row_name table-row_head">部门名称</div>
            <div class="table-row_name table-row_head">部门人数</div>
            <div class="table-row_name table-row_head">资产总数</div>
            <div class="table-row_name table-row_head">操作</div>
          </div>
          <div
            class="table-row data"
            v-for="item in objData.assetCoverageStatistics.assetCoverageRanking"
            :key="item.deptName"
          >
            <div class="table-row_name">{{ item.deptName }}</div>
            <div class="table-row_name">
              {{ item.peopleNum > 99999 ? "99999+" : item.peopleNum }}
            </div>
            <div class="table-row_name">
              {{ item.assetsNum > 99999 ? "99999+" : item.assetsNum }}
            </div>
            <div
              class="table-row_name"
              style="cursor: pointer; color: #4095e5"
              @click="handleCheck(item)"
            >
              点击查看
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block4">
        <div class="block-title">信息资产类型占比</div>
        <div class="block echart-item">
          <Echarts
            id="pieData"
            width="100%"
            height="100%"
            :fullOptions="pieOption"
          />
        </div>
      </div>
    </div>
    <div class="col col2">
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list"
          :key="index"
        >
          <div>
            <span>{{ item.num }}</span
            >个
          </div>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="col2-block2">
        <div
          class="item-opt2"
          :class="[`opt${index + 1}`, index % 2 == 0 ? 'left' : 'right']"
          v-for="(item, index) in optList6"
          :key="index"
        >
          <div>{{ item.name }}</div>
          <span>{{ item.number }}</span>
        </div>
      </div>
      <div class="col2-block3">
        <div class="block-title long">排行榜</div>
        <div class="flex">
          <div class="table2">
            <div class="subtitle">备件使用排行榜</div>
            <div class="table-row opt-title">
              <div class="table-row_number table-row_head">序号</div>
              <div class="table-row_type table-row_head">备件名称</div>
              <div class="table-row_count table-row_head">使用量</div>
            </div>
            <div
              class="table-data"
              @mouseenter="setAnimate(false, 1)"
              @mouseleave="setAnimate(true, 1)"
            >
              <div :class="{ 'table-data_inner': animate1 }">
                <div
                  class="table-row data"
                  v-for="(item, index) in objData.sparepartsUseStatistics"
                  :key="index"
                  :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
                >
                  <div class="table-row_number">{{ item.rank }}</div>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="String(item.sparepartsName)"
                    placement="bottom"
                    :enterable="false"
                    :hide-after="0"
                  >
                    <div class="table-row_type">{{ item.sparepartsName }}</div>
                  </el-tooltip>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="String(item.userNum)"
                    placement="bottom"
                    :enterable="false"
                    :hide-after="0"
                  >
                    <div class="table-row_count">
                      {{ item.userNum > 99999 ? "99999+" : item.userNum }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="table2">
            <div class="subtitle">设备备件消耗排行榜</div>
            <div class="table-row opt-title">
              <div class="table-row_number table-row_head">序号</div>
              <div class="table-row_type table-row_head">设备名称</div>
              <div class="table-row_count table-row_head">使用量</div>
            </div>
            <div
              class="table-data"
              @mouseenter="setAnimate(false, 2)"
              @mouseleave="setAnimate(true, 2)"
            >
              <div :class="{ 'table-data_inner': animate2 }">
                <div
                  class="table-row data"
                  v-for="(
                    item, index
                  ) in objData.sparepartsConsumptionStatistics"
                  :key="index"
                  :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
                >
                  <div class="table-row_number">{{ item.rank }}</div>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="String(item.deviceName)"
                    placement="bottom"
                    :enterable="false"
                    :hide-after="0"
                  >
                    <div class="table-row_type">{{ item.deviceName }}</div>
                  </el-tooltip>
                  <el-tooltip
                    class="box-item"
                    effect="dark"
                    :content="String(item.consumptionNum)"
                    placement="bottom"
                    :enterable="false"
                    :hide-after="0"
                  >
                    <div class="table-row_count">
                      {{
                        item.consumptionNum > 99999
                          ? "99999+"
                          : item.consumptionNum
                      }}
                    </div>
                  </el-tooltip>
                </div>
              </div>
            </div>
          </div>
          <div class="block echart-item">
            <div class="subtitle">应用使用时长Top5</div>
            <Echarts
              id="barData2"
              width="100%"
              height="10vw"
              :fullOptions="barOption2"
            />
          </div>
          <div class="block echart-item">
            <div class="subtitle">信息资产类别Top5</div>
            <Echarts
              id="barData"
              width="100%"
              height="10vw"
              :fullOptions="barOption"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1" :class="curAssetsTab == 0 ? 'left' : 'right'">
        <div class="col3-block1_left" @click="curAssetsTab = 0"></div>
        <div class="col3-block1_right" @click="curAssetsTab = 1"></div>
      </div>
      <div class="col3-block2" v-show="curAssetsTab == 0">
        <div class="flex">
          <div class="block">
            <div class="subtitle">使用年限占比</div>
            <div style="position: relative">
              <img
                class="pieData6-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie2-bg.png"
              />
              <Echarts
                ref="pieData6Ref"
                id="pieData6"
                class="echart"
                width="100%"
                height="6vw"
                :fullOptions="pieOption6"
              />
              <div class="pieData6-list">
                <div
                  class="pieData6-item"
                  v-for="(item, index) in pieOption6.options.series[0].data"
                  :data-index="index"
                >
                  <div class="pieData6-name">
                    <div
                      :style="{
                        backgroundColor: item.itemStyle.color,
                        border: `1px solid ${item.itemStyle.borderColor}`,
                      }"
                      class="item-dot"
                    >
                      <i
                        :style="{
                          backgroundColor: item.itemStyle.borderColor,
                        }"
                      ></i>
                    </div>
                    {{ item.name }}
                  </div>
                  <div class="pieData6-count">
                    {{ item.value > 999 ? "999+" : item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block">
            <div class="subtitle">硬盘大小占比</div>
            <div style="position: relative">
              <img
                class="pieData10-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
              />
              <Echarts
                ref="pieData10Ref"
                id="pieData10"
                class="echart"
                width="100%"
                height="6vw"
                :fullOptions="pieOption10"
              />
              <div class="pieData10-list">
                <div
                  class="pieData10-item"
                  v-for="(item, index) in pieOption10.options.series[0].data"
                  :data-index="index"
                >
                  <div
                    class="pieData10-bar"
                    :style="{ backgroundColor: item.itemStyle.bgColor2 }"
                  >
                    <div
                      class="pieData10-subBar"
                      :style="{ backgroundColor: item.itemStyle.bgColor }"
                    ></div>
                  </div>
                  <div>
                    <div class="pieData10-name">
                      {{ item.name }}
                    </div>
                    <div class="pieData10-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <!-- <div class="block">
            <div class="subtitle">标签使用频率</div>
            <Echarts
              id="barData3"
              width="100%"
              height="6vw"
              :fullOptions="barOption3"
            />
          </div> -->
          <div class="block">
            <div class="subtitle">设备类型占比</div>
            <Echarts
              id="pieData8"
              width="100%"
              height="10vw"
              :fullOptions="pieOption8"
            />
          </div>
          <div class="block">
            <div class="subtitle">端口类型占比</div>
            <Echarts
              id="pieData9"
              width="100%"
              height="10vw"
              :fullOptions="pieOption9"
            />
          </div>
        </div>
        <div class="flex">
          <div class="block">
            <div class="subtitle">内存占比</div>
            <div style="position: relative">
              <img
                class="pieData11-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie4-bg.png"
              />
              <Echarts
                ref="pieData11Ref"
                id="pieData11"
                class="echart"
                width="100%"
                height="6vw"
                :fullOptions="pieOption11"
              />
              <div class="pieData11-list">
                <div
                  class="pieData11-item"
                  v-for="(item, index) in pieOption11.options.series[0].data"
                  :data-index="index"
                >
                  <div class="pieData11-name">
                    <div
                      :style="{
                        backgroundColor: item.itemStyle.bgColor,
                      }"
                      class="item-dot"
                    ></div>
                    {{ item.name }}
                  </div>
                  <div class="pieData11-count">
                    {{ item.value > 999 ? "999+" : item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block">
            <div class="subtitle">故障时长占比</div>
            <div style="position: relative">
              <img
                class="pieData7-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
              />
              <Echarts
                ref="pieData7Ref"
                id="pieData7"
                class="echart"
                width="100%"
                height="6vw"
                :fullOptions="pieOption7"
              />
              <div class="pieData7-list">
                <div
                  class="pieData7-item"
                  v-for="(item, index) in pieOption7.options.series[0].data"
                  :data-index="index"
                >
                  <div
                    class="pieData7-bar"
                    :style="{ backgroundColor: item.itemStyle.bgColor2 }"
                  >
                    <div
                      class="pieData7-subBar"
                      :style="{ backgroundColor: item.itemStyle.bgColor }"
                    ></div>
                  </div>
                  <div>
                    <div class="pieData7-name">
                      {{ item.name }}
                    </div>
                    <div class="pieData7-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="block">
            <div class="subtitle">品牌占比</div>
            <Echarts
              id="pieData12"
              width="100%"
              height="10vw"
              :fullOptions="pieOption12"
            />
          </div>
          <div class="block">
            <div class="subtitle">信息资产类别等级占比</div>
            <Echarts
              id="pieData13"
              width="100%"
              height="10vw"
              :fullOptions="pieOption13"
            />
          </div>
        </div>
      </div>
      <div class="col3-block2" v-show="curAssetsTab == 1">
        <div class="flex">
          <div class="block">
            <div class="subtitle">软件库分类占比</div>
            <Echarts
              id="pieData2"
              width="100%"
              height="6vw"
              :fullOptions="pieOption2"
            />
          </div>
          <div class="block">
            <div class="subtitle">软件许可证到期时间占比</div>
            <Echarts
              id="pieData3"
              width="100%"
              height="6vw"
              :fullOptions="pieOption3"
            />
          </div>
        </div>
        <div class="flex">
          <div class="block">
            <div class="subtitle">系统版本占比（正版）</div>
            <Echarts
              id="barData4"
              width="100%"
              height="10vw"
              :fullOptions="pieOption15"
            />
          </div>
          <div class="block">
            <div class="subtitle">软件使用状况</div>
            <Echarts
              id="pieData4"
              width="100%"
              height="10vw"
              :fullOptions="pieOption4"
            />
          </div>
        </div>
        <!-- <div class="flex">
          <div class="block">
            <div class="subtitle">网络资产状况</div>
            <Echarts
              id="pieData5"
              width="100%"
              height="5.7vw"
              :fullOptions="pieOption5"
            />
          </div>
          <div class="block block2">
            <div class="subtitle">共入库</div>
            <div class="address"><span>6</span>个网站地址</div>
          </div>
        </div>
        <div class="flex">
          <div class="block">
            <div class="subtitle range">
              数据信息资产状况
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  :class="curRange1 == 2 ? 'active' : ''"
                  @click="handleRange(0, 2)"
                >
                  月
                </div>
                <div
                  class="range-tabs_item"
                  :class="curRange1 == 3 ? 'active' : ''"
                  @click="handleRange(0, 3)"
                >
                  年
                </div>
              </div>
            </div>
            <Echarts
              id="lineData"
              width="100%"
              height="5.7vw"
              :fullOptions="lineOption"
            />
          </div>
          <div class="block block-right">
            <div class="item-opt center">
              {{ optList1[0].title }}
              <div class="item-opt_row">
                <span>{{ optList1[0].total || 0 }}</span>
                条数据
              </div>
            </div>
            <div class="item-opt">
              {{ optList1[1].title }}
              <div class="item-opt_row">
                <span>{{ optList1[1].tip }}</span>
                <div
                  :class="
                    optList1[1].num > 0
                      ? 'up'
                      : optList1[1].num < 0
                      ? 'low'
                      : 'none'
                  "
                >
                  {{ optList1[1].ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="block">
            <div class="subtitle range">
              知识产权资产状况
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  :class="curRange2 == 2 ? 'active' : ''"
                  @click="handleRange(1, 2)"
                >
                  月
                </div>
                <div
                  class="range-tabs_item"
                  :class="curRange2 == 3 ? 'active' : ''"
                  @click="handleRange(1, 3)"
                >
                  年
                </div>
              </div>
            </div>
            <Echarts
              id="lineData2"
              width="100%"
              height="5.7vw"
              :fullOptions="lineOption2"
            />
          </div>
          <div class="block block-right">
            <div
              class="item-opt"
              v-for="(item, index) in optList2"
              :key="index"
            >
              {{ item.title }}
              <div class="item-opt_row">
                <span>{{ item.tip }}</span>
                <div
                  :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'"
                >
                  {{ item.ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div> -->
      </div>
      <div class="col3-mask" v-show="curAssetsTab == 1">
        待开放中，敬请期待...
      </div>
      <div class="col3-block3">
        <div class="flex">
          <div class="network up">
            平均上行网速<br />
            <span>{{ networkList[0].speed }}</span>
          </div>
          <div class="network up">
            峰值上行网速<br />
            <span>{{ networkList[0].maxSpeed }}</span>
          </div>
        </div>
        <div class="flex">
          <div class="network low">
            平均下行网速<br />
            <span>{{ networkList[1].speed }}</span>
          </div>
          <div class="network low">
            峰值下行网速<br />
            <span>{{ networkList[1].maxSpeed }}</span>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      title="查看详情"
      v-model="dialogVisible"
      align-center
      width="500"
    >
      <el-descriptions title="" border :column="1">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门名称"
        >
          {{ deptInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门人数"
        >
          {{ deptInfo.peopleNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="资产总数"
        >
          {{ deptInfo.assetsNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门负责人"
        >
          {{ deptInfo.leaderName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>
  
  <script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getSchoolStatistics,
  getWorkOrderStatistics,
  getAssetsStatistics,
  getMaintainCountStatistics,
  getWorkOrderTimeStatistics,
  getKnowledgeStatistics,
} from "@/api/unify";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  pieData10Ref: null,
  pieData11Ref: null,
  pieData7Ref: null,
  pieData6Ref: null,
  scrolltimer1: null,
  scrolltimer2: null,
  animate1: false,
  animate2: false,
  curRange1: 2,
  curRange2: 2,
  networkList: [
    { speed: "746kb/s", maxSpeed: "1362kb/s" },
    { speed: "52Mb/s", maxSpeed: "114Mb/s" },
  ],
  curAssetsTab: 0,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  pieData11Ref,
  pieData10Ref,
  pieData7Ref,
  pieData6Ref,
  scrolltimer1,
  scrolltimer2,
  animate1,
  animate2,
  curRange1,
  curRange2,
  networkList,
  curAssetsTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  curTime,
  timer,
  timer2,
} = toRefs(state);

const col2_block1_list = ref([
  { name: "硬件资产", num: 0 },
  { name: "软件资产", num: 4 },
  { name: "数据信息资产", num: 0 },
  { name: "网络资产", num: 6 },
  { name: "知识产权", num: 5 },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "开机率", num: "0", unit: "%" },
  { name: "报废数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "0", unit: "人" },
  { name: "今日执勤", num: "0", unit: "人" },
  { name: "今日轮休", num: "0", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "知识库", tip: "", total: 0, ratio: "0%", num: 0 },
  { title: "对比上个月", tip: "个数同比", total: 0, ratio: "0", num: 0 },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "知识产权个数同比",
    total: 0,
    ratio: "-50.00%",
    num: -50,
  },
  {
    title: "本年度对比上年度",
    tip: "知识产权个数同比",
    total: 5,
    ratio: "300.00%",
    num: 300,
  },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  { title: "今日工单平均处理时间", total: 0, ratio: "0%", num: 0 },
  { title: "合计工单平均处理时间", total: 0, fq: "0天/次" },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const optList6 = ref([
  { name: "数据信息资产", number: "01" },
  { name: "网络资产", number: "02" },
  { name: "硬件资产", number: "03" },
  { name: "知识产权资产", number: "04" },
  { name: "软件资产", number: "05" },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//应用使用时长TOP5柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "15%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        formatter: "{value} h",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//标签使用频率饼图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}：${relVal.value}`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//系统版本占比饼图
const pieOption15 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: 10,
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

//资产类别占比饼图
const pieOption = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "65%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "70%",
              lineHeight: 20,
            },
            b: {
              color: "#6889de",
              fontSize: "60%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const pieOption2 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "操作系统", value: 51 },
          { name: "教学软件", value: 23 },
          { name: "其他软件", value: 9 },
        ],
      },
    ],
  },
});

// 软件到期时间饼图（假数据）
const pieOption3 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "5年以上", value: 34 },
          { name: "3-5年", value: 63 },
          { name: "1-3年", value: 72 },
          { name: "1年以内", value: 46 },
        ],
      },
    ],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 1 },
          { name: "教育平台", value: 3 },
          { name: "办公平台", value: 2 },
        ],
      },
    ],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 10,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 8,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "一年以内",
            value: 0,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "1-3年",
            value: 0,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "3-5年",
            value: 0,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
          {
            name: "5年以上",
            value: 0,
            itemStyle: { color: "rgba(255,255,0,0.3)", borderColor: "#ffff00" },
          },
        ],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["55%", "60%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 9,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 7,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "1天以内",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "1-5天",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "5-10天",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "10天以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieOption8 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产端口类型占比饼图
const pieOption9 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["55%", "60%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 9,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 7,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "128G以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "256G",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "512G",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "1TB以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 10,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 8,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "8G以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "8G",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "16G",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "32G",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "64G以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieOption12 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          // formatter: (params) => {
          //   const { name, ratio } = params.data;
          //   return `{b|${name}}\n{d|${ratio}}`;
          // },
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 信息资产类别等级占比
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
        },
        lineStyle: {
          color: "#009bab",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        rotate: 0,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计2
const lineOption4 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [42],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//运维考勤打卡情况
const lineOption5 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

function setAnimate(flag, type) {
  if (flag) {
    play(type);
  } else {
    setTimeout(() => {
      state[`animate${type}`] = false;
    }, 1000);
    window.clearInterval(state[`scrolltimer${type}`]);
  }
}

function play(type) {
  if (
    objData.value[
      type == 1 ? "sparepartsUseStatistics" : "sparepartsConsumptionStatistics"
    ].length >= 5
  ) {
    state[`scrolltimer${type}`] = setInterval(() => {
      let obj = JSON.parse(
        JSON.stringify(
          objData.value[
            type == 1
              ? "sparepartsUseStatistics"
              : "sparepartsConsumptionStatistics"
          ][0]
        )
      );
      objData.value[
        type == 1
          ? "sparepartsUseStatistics"
          : "sparepartsConsumptionStatistics"
      ].push(obj);
      state[`animate${type}`] = true;
      setTimeout(() => {
        objData.value[
          type == 1
            ? "sparepartsUseStatistics"
            : "sparepartsConsumptionStatistics"
        ].shift();

        state[`animate${type}`] = false;
      }, 1000);
    }, 2000);
  }
}

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getKnowledgeStatistics({ range: state.curRange1 }).then((res) => {
      console.log("知识库统计", res);
      const { knowledgeBaseStatistics } = res.data;
      if (knowledgeBaseStatistics) {
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
          (item) => item.num
        );
      }
    });
  }
  if (type == 1) {
    state.curRange2 = tab;
    lineOption2.value.options.xAxis.data =
      tab == 2
        ? [
            "2024-09",
            "2024-10",
            "2024-11",
            "2024-12",
            "2025-01",
            "2025-02",
            "2025-03",
          ]
        : ["2019", "2020", "2021", "2022", "2023", "2024", "2025"];
    lineOption2.value.options.series[0].data =
      tab == 2 ? [0, 0, 0, 1, 0, 2, 1] : [0, 0, 0, 0, 0, 1, 4];
  }
}

function handleCheck(item) {
  state.deptInfo = {
    ...item,
  };
  state.dialogVisible = true;
}

//获取数据
function getData() {
  handleRange(0, 2);
  handleRange(1, 2);
  getSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        assetsTotal,
        restTotal,
        maintenanceTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
      } = res.data;
      col2_block1_list.value[0].num = deviceTotal;

      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;

      window.clearInterval(state.scrolltimer1);
      window.clearInterval(state.scrolltimer2);
      objData.value = {
        ...objData.value,
        sparepartsUseStatistics:
          res.data.sparepartsUseStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || [], // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || [], // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };
      play(1);
      play(2);

      // 设置部门名称假数据
      // objData.value.assetCoverageStatistics.assetCoverageRanking[0].deptName =
      //   "教务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[1].deptName =
      //   "德育处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[2].deptName =
      //   "总务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[3].deptName =
      //   "学校办公室";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[4].deptName =
      //   "保卫处";

      // lineOption2.value.options.xAxis.data = [
      //   "2024-10",
      //   "2024-11",
      //   "2024-12",
      //   "2025-01",
      //   "2025-02",
      //   "2025-03",
      // ];
      // lineOption2.value.options.series[0].data = [0, 0, 0, 0, 5, 0];

      if (deviceAppUserStatistics?.appUseTime) {
        // 软件使用时长柱状图赋值
        barOption2.value.options.xAxis.data =
          deviceAppUserStatistics.appUseTime.map((item) => item.month);
        barOption2.value.options.series[0].data =
          deviceAppUserStatistics.appUseTime.map((item) => item.timeTotal);
      }

      if (deviceAppUserStatistics?.appUseType) {
        // 软件使用占比饼图赋值
        pieOption4.value.options.series[0].data =
          deviceAppUserStatistics.appUseType.map((item) => {
            return {
              name: item.appType,
              value: item.num,
            };
          });
      }

      if (assetsTypeStatistics) {
        // 资产类别占比柱状图赋值
        barOption.value.options.xAxis.data = assetsTypeStatistics.map(
          (item) => item.assetsTypeName
        );
        barOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => item.num
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => {
            return {
              name: item.assetsTypeName,
              value: item.num,
            };
          }
        );

        // 假数据
        // pieOption.value.options.series[0].data[0].name = "计算机";
        // pieOption.value.options.series[0].data[1].name = "工作站";
        // pieOption.value.options.series[0].data[2].name = "服务器";
        // pieOption.value.options.series[0].data[3].name = "磁盘";
        // pieOption.value.options.series[0].data[4].name = "电灯泡";

        // barOption.value.options.xAxis.data[0] = "计算机";
        // barOption.value.options.xAxis.data[1] = "工作站";
        // barOption.value.options.xAxis.data[2] = "服务器";
        // barOption.value.options.xAxis.data[3] = "磁盘";
        // barOption.value.options.xAxis.data[4] = "电灯泡";
      }
      // console.log(pieOption.value, barOption.value, "资产类别111");

      // if (knowledgeBaseStatistics) {
      //   lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
      //     (item) => item.statisticsTime
      //   );
      //   lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
      //     (item) => item.num
      //   );
      // }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];

        col2_block1_list.value[2].num = knowledgeBaseYear;

        optList1.value[0].total = knowledgeBaseYear;
        optList1.value[1].ratio = knowledgeBaseMonthRise;
        optList1.value[1].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;

        // optList1.value[1].total = knowledgeBaseYear;
        // optList1.value[1].ratio = knowledgeBaseYearRise;
        // optList1.value[1].num =
        //   knowledgeBaseYearRise?.replace("%", "") * 1 || 0;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsFaultBrandProportion,
      assetsBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
      assetsLevelProportion,
    } = res.data;

    // 标签使用频率
    if (assetsTagUseProportion) {
      // const arr = ["小学部", "初中部", "高中部", "易消耗品", "维护过的物品"];
      // pieOption14.value.options.series[0].data = assetsTagUseProportion?.map(
      //   (item, index) => {
      //     return {
      //       name: arr[index],
      //       value: item.num,
      //     };
      //   }
      // );
      // 资产类别占比柱状图赋值
      // barOption3.value.options.xAxis.data =
      //   assetsTagUseProportion?.map(
      //     (item, index) => arr[index] || item.assetsTagName
      //   ) || [];
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map((item, index) => item.assetsTagName) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];
    }

    // console.log(barOption3.value, "barOption3.value");

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      pieOption15.value.options.series[0].data =
        assetsSystemVersionProportion?.map((item, index) => {
          return {
            name: item.assetsSystemVersionName,
            value: item.num,
          };
        });
      // 资产类别占比柱状图赋值
      // barOption4.value.options.xAxis.data = assetsSystemVersionProportion.map(
      //   (item) => item.assetsSystemVersionName
      // );
      // barOption4.value.options.series[0].data =
      //   assetsSystemVersionProportion.map((item) => item.num);
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data.map((item) => {
        let obj = deviceTroubleProportion.find((_) => _.timeRange == item.name);
        item.value = obj?.num || 0;
      });
      // pieOption7.value.options.series[0].data =
      //   deviceTroubleProportion?.map((item) => {
      //     return {
      //       name: item.timeRange,
      //       value: item.num,
      //     };
      //   }) || [];
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data.map((item) => {
        let obj = hardwareAssetsUseProportion.find(
          (_) => _.timeRange == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      pieOption8.value.options.series[0].data =
        assetsTypeProportion?.map((item) => {
          return {
            name: item.assetsTypeName,
            value: item.num,
          };
        }) || [];
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      pieOption9.value.options.series[0].data =
        assetsPortProportion?.map((item) => {
          return {
            name: item.assetsPortName,
            value: item.num,
          };
        }) || [];
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data.map((item) => {
        let obj = assetsHardDiskProportion.find(
          (_) => _.assetsHardDiskName == item.name
        );
        item.value = obj?.num || 0;
      });
      // pieOption10.value.options.series[0].data =
      //   assetsHardDiskProportion?.map((item) => {
      //     return {
      //       name: item.assetsHardDiskName,
      //       value: item.num,
      //     };
      //   }) || [];
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data.map((item) => {
        let obj = assetsMemoryProportion.find(
          (_) => _.assetsMemoryName == item.name
        );
        item.value = obj?.num || 0;
      });
      // pieOption11.value.options.series[0].data =
      //   assetsMemoryProportion?.map((item) => {
      //     return {
      //       name: item.assetsMemoryName,
      //       value: item.num,
      //     };
      //   }) || [];
    }

    // 故障资产品牌占比
    if (assetsBrandProportion) {
      pieOption12.value.options.series[0].data =
        assetsBrandProportion?.map((item) => {
          return {
            name: item.assetsBrandName,
            value: item.num,
            ratio: item.proportion,
          };
        }) || [];
    }

    // 信息资产类别等级占比
    if (assetsLevelProportion) {
      pieOption13.value.options.series[0].data =
        assetsLevelProportion?.map((item) => {
          return {
            name: item.assetsLevelName || "",
            value: item.num,
          };
        }) || [];
    }
  });
}

function getOrderData() {
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList3.value[0].total = processingWorkOrderMonth;
      optList3.value[0].ratio = processingWorkOrderMonthRise;
      optList3.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList3.value[1].total = processingWorkOrderYear;
      optList3.value[1].ratio = processingWorkOrderYearRise;
      optList3.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
      } = workOrderProcrssingTime;
      optList4.value[0].total = todayAverageProcessingTime || 0;
      optList4.value[0].ratio = riseOfProcessingTime || "0%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;

      // optList4.value[1].total = (totalAverageProcessingTime.replace('分钟', '') * 1 / 1440).toFixed(1) + '天';
      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
    }
  });
}

const initPieFunc = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: 0, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: 0,
        dataIndex: index,
      });
    });
  });
};

onMounted(() => {
  proxy.$modal.loading();
  getData();
  getOrderData();
  getAssetsData();
  initPieFunc("pieData6");
  initPieFunc("pieData10");
  initPieFunc("pieData7");
  initPieFunc("pieData11");
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  window.clearInterval(state.scrolltimer1);
  window.clearInterval(state.scrolltimer2);
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
  
  <style lang="scss" scoped>
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #b0c0e6;
  padding: 0.2vw 0vw 0.2vw 0.5vw;
  font-size: 0.7vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.4vw;
      color: #4095e5;
      &_item {
        border: 1px solid rgba(255, 255, 255, 0);
        padding: 0.05vw 0.3vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/range-bg.png");
          background-size: 100% 100%;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}
.unify-main_charts {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  // overflow: hidden;

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-pie_bg.png");
      background-size: 100% 100%;

      &-title {
        font-size: 0.8vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1vw;
        margin-top: 1vw;
        color: #fff;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/title.png");
        background-size: 100% 100%;
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/title-long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.5vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          padding-right: 0.2vw;
          color: #c7d9f9;
        }

        div {
          padding-left: 0.8vw;
        }

        .up {
          color: #2dcd5e;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          color: #fe005d;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 23vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;
      padding: 1vw 0 0;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        height: 6vw;
        width: 100%;
        text-align: center;
        line-height: 4.5vw;
        font-size: 1.5vw;
        color: #fff;
      }
    }

    &-block3 {
      .table {
        // height: 10vw;
        font-size: 0.63vw;
        padding: 0.2vw 0vw 0.5vw;
        color: #fff;
        // margin-top: 0.5vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/block-bg.png");
        background-size: 100% 100%;

        .opt-title {
          margin-bottom: 0.7vw;
          font-size: 0.73vw;
        }

        &.block {
          min-height: 11.9vw;
        }

        &-row {
          display: flex;
          padding: 0.34vw 0;
          text-align: center;
          margin-top: 0.3vw;

          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    &-block4 {
      //   border: 1px solid red;
      .echart-item {
        height: 11vw;
        margin-top: 0.7vw;
        flex: 1;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/block-bg.png");
        background-size: 100% 100%;
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 51vw;
    &-block1 {
      padding-top: 1vw;
      width: 100%;
      // border: 1px solid red;
      display: flex;
      justify-content: center;
      font-size: 0.9vw;
      color: #5470c6;
      gap: 0 1vw;
      &_item {
        // border: 1px solid red;
        width: 8.2vw;
        padding: 0.6vw 1vw 0.1vw;
        line-height: 1.2vw;
        letter-spacing: 0.1vw;
        background: url("@/assets/screen/top_data_frame.png");
        background-size: 100% 100%;
        span {
          font-size: 1.5vw;
          color: #fff;
          display: inline-block;
          padding-right: 0.2vw;
        }
      }
    }

    &-block2 {
      // border: 1px solid red;
      height: 30.3vw;
      position: relative;
      // border: 1px solid red;
      width: 37vw;
      margin: 1.5vw auto;
      background: url("@/assets/screen/middle_banner.png");
      background-size: 100% 100%;

      .item-opt2 {
        width: 9.5vw;
        height: 1.8vw;
        position: absolute;
        cursor: pointer;
        font-size: 0.7vw;

        div {
          position: absolute;
          // border: 1px solid red;
          width: 7.5vw;
          // height: 1.8vw;
          text-align: center;
          line-height: 1.8vw;
        }

        span {
          position: absolute;
          right: 0.4vw;
          top: 0.5vw;
        }

        &.right {
          background: url("@/assets/screen/middle_frame_right.png");
          background-size: 100% 100%;

          div {
            right: 0;
          }

          span {
            left: 0.4vw;
          }
        }

        &.left {
          background: url("@/assets/screen/middle_frame_left.png");
          background-size: 100% 100%;
        }

        &.opt1 {
          // display: none;
          // border: 1px solid red;
          top: -0.5vw;
          left: -4vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_left_top_line.png");
            background-size: 100% 100%;
            width: 4vw;
            height: 3vw;
            right: -3.9vw;
            top: -0.4vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_left_sel.png");
            background-size: 100% 100%;

            &::before {
              right: -4.6vw;
              top: -1.1vw;
              width: 5vw;
              height: 4vw;
              background: url("@/assets/screen/middle_left_top_line_sel.png");
              background-size: 100% 100%;
            }
          }
        }

        &.opt2 {
          right: -2.5vw;
          bottom: 0vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_right_bottom_line.png");
            background-size: 100% 100%;
            width: 4vw;
            height: 1.8vw;
            left: -3.9vw;
            top: -1.5vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_right_sel.png");
            background-size: 100% 100%;

            &::before {
              position: absolute;
              content: "";
              background: url("@/assets/screen/middle_right_bottom_line_sel.png");
              background-size: 100% 100%;
              width: 5.5vw;
              height: 4vw;
              left: -5.5vw;
              top: -3.4vw;
            }
          }

          // display: none;
          // border: 1px solid red;
          // right: 2.5vw;
          // top: -3.2vw;

          // &::before {
          //   position: absolute;
          //   content: "";
          //   background: url("@/assets/screen/middle_right_top_line.png");
          //   background-size: 100% 100%;
          //   width: 4vw;
          //   height: 3vw;
          //   left: -3.9vw;
          //   top: -0.6vw;
          // }

          // &.active {
          //   background: url("@/assets/screen/middle_frame_right_sel.png");
          //   background-size: 100% 100%;

          //   &::before {
          //     background: url("@/assets/screen/middle_right_top_line_sel.png");
          //     background-size: 100% 100%;
          //     width: 5vw;
          //     height: 4vw;
          //     left: -4.7vw;
          //     top: -1vw;
          //   }
          // }
        }

        &.opt3 {
          // border: 1px solid red;
          top: 6.5vw;
          left: -5vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_left_middle_line.png");
            background-size: 100% 100%;
            width: 3vw;
            height: 4vw;
            right: -0.7vw;
            top: 1.4vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_left_sel.png");
            background-size: 100% 100%;

            &::before {
              position: absolute;
              content: "";
              background: url("@/assets/screen/middle_left_middle_line_sel.png");
              background-size: 100% 100%;
              width: 4vw;
              height: 5vw;
              right: -1.1vw;
              top: 1.6vw;
            }
          }
        }

        &.opt4 {
          // border: 1px solid red;
          right: -4.5vw;
          bottom: 12.5vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_right_middle_line.png");
            background-size: 100% 100%;
            width: 3vw;
            height: 4vw;
            left: 3vw;
            top: -4vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_right_sel.png");
            background-size: 100% 100%;

            &::before {
              position: absolute;
              content: "";
              background: url("@/assets/screen/middle_right_middle_line_sel.png");
              background-size: 100% 100%;
              width: 4vw;
              height: 5vw;
              left: 3vw;
              top: -4.6vw;
            }
          }
        }

        &.opt5 {
          // border: 1px solid red;
          bottom: -0.5vw;
          left: -5vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_left_bottom_line.png");
            background-size: 100% 100%;
            width: 4vw;
            height: 3vw;
            right: -4vw;
            top: -1vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_left_sel.png");
            background-size: 100% 100%;

            &::before {
              position: absolute;
              content: "";
              background: url("@/assets/screen/middle_left_bottom_line_sel.png");
              background-size: 100% 100%;
              width: 5vw;
              height: 4vw;
              right: -4.6vw;
              top: -0.9vw;
            }
          }
        }

        &.opt6 {
          // border: 1px solid red;
          right: -2vw;
          bottom: -0.5vw;

          &::before {
            position: absolute;
            content: "";
            background: url("@/assets/screen/middle_right_bottom_line.png");
            background-size: 100% 100%;
            width: 4vw;
            height: 1.8vw;
            left: -3.9vw;
            top: -1.5vw;
          }

          &.active {
            background: url("@/assets/screen/middle_frame_right_sel.png");
            background-size: 100% 100%;

            &::before {
              position: absolute;
              content: "";
              background: url("@/assets/screen/middle_right_bottom_line_sel.png");
              background-size: 100% 100%;
              width: 5.5vw;
              height: 4vw;
              left: -5.5vw;
              top: -3.4vw;
            }
          }
        }
      }
    }

    &-block3 {
      .echart-item {
        height: 11.3vw;
        margin-top: 0.2vw;
        flex: 1;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
      }
      .table2 {
        flex: 1;
        height: 11.5vw;
        // border: 1px solid red;
        padding: 0.2vw 0;
        font-size: 0.6vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;

        .table-row {
          display: flex;
          height: 1.2vw;
          line-height: 1.2vw;
          text-align: center;
          // border: 1px solid red;
          margin-bottom: 0.4vw;
          // &.data:hover {
          //   background-color: #061d47;
          // }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 8vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .table-data {
          overflow: hidden;
          // border: 1px solid red;
          height: 7.6vw;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -1.6vw;
          }
          .odd {
            // background-color: rgba(22, 88, 255, 0.15);
            background-color: rgba(63, 86, 237, 0.35);
          }
          .even {
            background-color: rgba(53, 206, 217, 0.35);
          }
        }

        .opt-title {
          margin-top: 0.1vw;
          height: 1.3vw;
          line-height: 1.3vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/tableHead-bg.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block4 {
      .grid-item {
        display: flex;
        flex-direction: column;

        .block {
          margin-top: 0.5vw;
          height: 6.8vw;
          padding: 0.5vw 0.2vw;

          &-right {
            font-size: 0.6vw !important;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
        }
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 24vw;
    height: 100%;

    .subtitle {
      font-size: 0.6vw;
      padding-top: 0.3vw;
    }

    .block {
      background: none;
      // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
      // background-size: 100% 100%;
    }

    &-block1 {
      margin-top: -0.3vw;
      // border: 1px solid red;
      height: 2vw;
      cursor: pointer;
      display: flex;
      &.left {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/assetsTab_left.png");
        background-size: 100% 100%;
      }
      &.right {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/assetsTab_right.png");
        background-size: 100% 100%;
      }

      &_left,
      &_right {
        flex: 1;
        height: 2vw;
        // border: 1px solid yellow;
      }
    }

    &-block2 {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pies-bg.png");
      background-size: 100% 100%;
      .flex {
        gap: 0 0.5vw;
      }
      .block {
        padding: 0vw 0.2vw;
        margin-top: 0.5vw;
      }

      .block2 {
        background: url("@/assets/screen/app/address-bg.png");
        background-size: 100% 100%;
      }

      .address {
        // border: 1px solid red;
        height: 4vw;
        line-height: 4vw;
        font-size: 0.9vw;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          color: #4095e5;
          font-size: 1.1vw;
          letter-spacing: 0.2vw;
        }
      }
    }

    &-mask {
      margin-top: 1vw;
      width: 100%;
      height: 18.5vw;
      line-height: 18.5vw;
      text-align: center;
      color: #fff;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/masking.png");
      background-size: 100% 100%;
    }

    &-block3 {
      margin-top: 0.5vw;
      padding: 0.7vw 0 0.6vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/network-bg.png");
      background-size: 100% 100%;
      .flex {
        gap: 0;
        margin-bottom: 0.2vw;
      }
      .network {
        position: relative;
        color: #4f5760;
        font-size: 0.8vw;
        line-height: 1.5vw;
        flex: 1;
        padding: 0.5vw 1vw 0.5vw 4vw;
        span {
          font-size: 1vw;
          color: #fff;
          display: inline-block;
          padding-right: 0.2vw;
        }
        &::before {
          position: absolute;
          content: "";
          left: 1vw;
          width: 2.5vw;
          height: 2.5vw;
        }
        &.up::before {
          background: url("@/assets/screen/admin/network_up.png");
          background-size: 100% 100%;
        }

        &.low::before {
          background: url("@/assets/screen/admin/network_low.png");
          background-size: 100% 100%;
        }
      }
    }

    .pieData6,
    .pieData11 {
      &-list {
        width: 6vw;
        height: 90%;
        margin-top: 2.5%;
        position: absolute;
        top: 0;
        right: 0;
        font-size: 0.6vw;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
      }
      &-item {
        cursor: pointer;
        // border: 1px solid red;
        display: flex;
        justify-content: space-between;
        height: 1.1vw;
        line-height: 1.1vw;
        border-width: 1px;
        border-style: solid;
        padding: 0 0.2vw;
        border-image-source: radial-gradient(
          50% 50% at 50% 100%,
          #c7d6ff4d 0%,
          #c7d3ff00 100%
        );
        border-image-slice: 1;
        background: radial-gradient(
          100% 175.73% at 0% 50%,
          #77adff40 0%,
          #002cc700 100%
        );
      }
      &-name {
        display: flex;
        align-items: center;
        gap: 0 0.2vw;
        .item-dot {
          width: 0.4vw;
          height: 0.4vw;
          display: flex;
          align-items: center;
          justify-content: center;
          i {
            display: flex;
            width: 0.15vw;
            height: 0.15vw;
          }
        }
      }
      &-count {
        color: #fff;
      }
      &-bg {
        position: absolute;
        top: 0.9vw;
        left: 0.32vw;
        width: 4.3vw;
        height: 4.3vw;
      }
    }
    .pieData11 {
      &-list {
        margin-top: 0%;
        gap: 0.05vw 0;
      }
      &-item {
        padding: 0 0.3vw;
        border-width: 1px;
        border-style: solid;
        border-image-source: linear-gradient(
          0deg,
          #c7d6ff4d 0%,
          #c7d6ff00 28.9%
        );
        border-image-slice: 1;
        box-sizing: border-box;
        background: radial-gradient(
          50% 71.05% at 50% 100%,
          #a3cdff40 0%,
          #0020c700 100%
        );
      }
      &-name {
        .item-dot {
          width: 0.4vw;
          height: 0.4vw;
          border-radius: 50%;
          margin-right: 0.1vw;
        }
      }
    }
    .pieData10,
    .pieData7 {
      &-list {
        height: 90%;
        margin-top: 7.5%;
        position: absolute;
        top: 0;
        right: 0;
        font-size: 0.6vw;
        display: flex;
        justify-content: space-between;
        width: 6vw;
        flex-direction: row;
        flex-wrap: wrap;
      }
      &-item {
        cursor: pointer;
        display: flex;
        width: 3vw;
        height: 1.5vw;
        text-align: left;
      }
      &-bar {
        width: 0.1vw;
        height: 100%;
        margin-right: 0.25vw;
      }
      &-subBar {
        width: 0.1vw;
        height: 0.7vw;
      }
      &-name {
        display: flex;
        align-items: center;
        gap: 0 0.2vw;
        font-size: 0.5vw;
      }
      &-count {
        color: #fff;
        margin-top: 0.2vw;
      }
      &-bg {
        position: absolute;
        left: 0.29vw;
        top: 0.85vw;
        width: 4.3vw;
        height: 4.3vw;
      }
    }
    .pieData8 {
      &-list {
        // border: 1px solid red;
        width: 100%;
        height: 3vw;
        overflow-y: scroll;
        margin-top: 2.5%;
        position: absolute;
        top: 6.5vw;
        right: 0;
        font-size: 0.6vw;
        display: flex;
        flex-wrap: wrap;
        gap: 0.3vw 1vw;
        padding: 0 0.5vw;
      }
      &-item {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      &-name {
        display: flex;
        align-items: center;
        gap: 0 0.2vw;
        .item-dot {
          width: 0.4vw;
          height: 0.4vw;
          display: flex;
          align-items: center;
          justify-content: center;
          i {
            display: flex;
            width: 0.15vw;
            height: 0.15vw;
          }
        }
      }
      &-count {
        margin-left: 1vw;
        color: #fff;
      }
      &-bg {
        position: absolute;
        top: 0.2vw;
        left: 0.2vw;
        width: 10vw;
        height: 6vw;
      }
    }
  }
}
</style>