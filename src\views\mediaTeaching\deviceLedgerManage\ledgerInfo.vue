<template>
  <div class="ledgerInfo app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form :model="errors">
        <el-descriptions title="" border column="2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备名称"
          >
            <span v-if="!isEdit"> {{ errors.deviceName }} </span>
            <el-form-item v-else label="" prop="deviceName">
              <el-input
                placeholder="请输入"
                v-model="errors.deviceName"
                style="width: 100%"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备类型"
          >
            <span v-if="!isEdit">{{ errors.typeName }}</span>
            <el-form-item v-else label="" prop="typeId">
              <el-select
                v-model="errors.typeId"
                placeholder="请选择类别"
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备标签"
          >
            <span v-if="!isEdit">{{ errors.tagName }}</span>
            <el-form-item v-else label="" prop="tagId">
              <el-select
                v-model="errors.tagId"
                placeholder="请选择设备标签"
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备编码"
          >
            {{ errors.deviceCode }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="安装位置"
          >
            <span v-if="!isEdit">{{ errors.installAddress }}</span>
            <el-form-item label="" prop="installAddressId" v-else>
              <el-tree-select
                v-model="errors.installAddressId"
                :props="positionProps"
                :data="positionTreeList_disabled"
                placeholder="请选择安装位置"
                :render-after-expand="false"
                clearable
                style="width: 100%"
                @change="handlePosition"
              >
              </el-tree-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="资产端口类别"
          >
            <span v-if="!isEdit">{{ errors.portType }}</span>
            <el-form-item v-else label="" prop="portType">
              <el-select
                v-model="errors.portType"
                placeholder="请选择资产端口类别"
                :disabled="readonly"
                style="width: 100%"
              >
                <el-option
                  v-for="item in portTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产等级"
          >
            <span v-if="!isEdit">{{ errors.assetLevel }}</span>
            <el-form-item v-else label="" prop="assetLevel">
              <el-select
                v-model="errors.assetLevel"
                placeholder="请选择信息资产等级"
                style="width: 100%"
              >
                <el-option
                  v-for="item in assetLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产类别"
          >
            <span v-if="!isEdit">{{ errors.assetType }}</span>
            <el-form-item v-else label="" prop="assetType">
              <el-select
                v-model="errors.assetType"
                placeholder="请选择信息资产类别"
                :disabled="readonly"
                style="width: 100%"
              >
                <el-option
                  v-for="item in assetTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="品牌"
          >
            <span v-if="!isEdit">{{ errors.brand }}</span>
            <el-form-item label="" prop="brand" v-else>
              <el-input
                v-model="errors.brand"
                placeholder="请输入设备品牌名称"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="规格型号"
          >
            <span v-if="!isEdit">{{ errors.model }}</span>
            <el-form-item label="" prop="model" v-else>
              <el-input v-model="errors.model" placeholder="请输入规格型号" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="物理地址"
          >
            <span v-if="!isEdit">{{ errors.macAddress }}</span>
            <el-form-item label="" prop="macAddress" v-else>
              <el-input
                v-model="errors.macAddress"
                placeholder="请输入物理地址"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="逻辑地址"
          >
            <span v-if="!isEdit">{{ errors.logicAddress }}</span>
            <el-form-item label="" prop="logicAddress" v-else>
              <el-input
                v-model="form.logicAddress"
                placeholder="请输入逻辑地址"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="IP地址"
          >
            <span v-if="!isEdit">{{ errors.ipAddress }}</span>
            <el-form-item label="" prop="ipAddress" v-else>
              <el-input v-model="errors.ipAddress" placeholder="请输入IP地址" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="内存"
          >
            <span v-if="!isEdit">{{ errors.internalStorage }}</span>
            <el-form-item label="" prop="internalStorage" v-else>
              <el-input
                v-model="form.internalStorage"
                placeholder="请输入设备内存"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="CPU"
          >
            <span v-if="!isEdit">{{ errors.cpu }}</span>
            <el-form-item label="" prop="cpu" v-else>
              <el-input v-model="errors.cpu" placeholder="请输入CPU型号" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="操作系统"
          >
            <span v-if="!isEdit">{{ errors.osVersion }}</span>
            <el-form-item label="" prop="osVersion" v-else>
              <el-input
                v-model="errors.osVersion"
                placeholder="请输入操作系统版本号"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="硬盘"
          >
            <span v-if="!isEdit">{{ errors.disk }}</span>
            <el-form-item label="" prop="disk" v-else>
              <el-input v-model="errors.disk" placeholder="请输入设备硬盘" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="入库时间"
          >
            <span v-if="!isEdit">{{ errors.putTime }}</span>
            <el-form-item label="" prop="putTime" v-else>
              <el-date-picker
                v-model="form.putTime"
                format="YYYY-MM-DD"
                :disabled-date="disabledDateFn"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                type="date"
                placeholder="请选择入库时间"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="端口"
          >
            <span v-if="!isEdit">{{ errors.portNumber }}</span>
            <el-form-item label="" prop="portNumber" v-else>
              <el-input v-model="errors.portNumber" placeholder="请输入端口" />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="线缆"
          >
            <span v-if="!isEdit">{{ errors.cableInfo }}</span>
            <el-form-item label="" prop="cableInfo" v-else>
              <el-input
                v-model="errors.cableInfo"
                placeholder="请输入线缆信息"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="边缘服务器"
          >
            <span v-if="!isEdit">{{ errors.ralayHost }}</span>
            <el-form-item label="" prop="ralayHost" v-else>
              <el-input
                v-model="errors.ralayHost"
                placeholder="请输入边缘服务器的ip地址"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备图片"
          >
            <el-image
              v-if="!isEdit"
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="errors.deviceImg"
              fit="contain"
            />

            <el-form-item label="" prop="deviceImg" v-else>
              <imgUpload
                @update:modelValue="(url) => setImgUrl(url, 'deviceImg')"
                :limit="1"
                :modelValue="form.deviceImg"
                :isShowTip="isEdit"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备二维码"
          >
            <el-button type="primary" @click="handleViewQrCode">
              查看二维码
            </el-button>
            <a
              class="download-link ml-4"
              @click="handleDownloadQrCode"
              style="color: red"
            >
              下载
            </a>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备状态"
          >
            {{ errors.deviceStatus }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <el-button type="danger" style="float: right; margin: 10px 0"
        >报废设备</el-button
      >
      <div class="ledgerInfo-tit" style="margin-top: 40px">关联配置</div>
      <el-form :model="errors" label-width="100">
        <el-form-item label="关联设备">
          <el-input v-model="errors.deviceStr" style="width: 250px" />
        </el-form-item>
        <el-form-item label="关联人员">
          <el-input v-model="errors.deviceStr" style="width: 250px" />
        </el-form-item>
      </el-form>

      <div class="ledgerInfo-btns">
        <el-button type="primary" @click="handleSubmit">{{
          isEdit ? "点击提交" : "编辑"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <!-- 备件表格 -->
      <div class="ledgerInfo-tit">备件使用情况</div>
      <el-table :data="ledgerList" border>
        <el-table-column
          label="备件编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="备件名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="单据编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="单据来源"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="消耗量"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceType"
        />
        <el-table-column
          label="操作"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTag"
        />
      </el-table>
      <pagination
        v-show="ledgerList.length > 0"
        :total="ledgerList.length"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />

      <div class="ledgerInfo-record">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role} 于 ${item.time} ${item.type}了${item.spareName}的现库存`
          }}
        </div>
      </div>
    </el-card>

    <!-- 选择设备/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      :width="title == '请选择关联备件' ? 600 : 800"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item>
          <el-input
            v-model="queryParams.faultDesc"
            placeholder="请输入备件编号/名称搜索"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-if="title == '请选择关联备件'" :data="ledgerList" border>
        <el-table-column type="selection" align="center" width="70" />
        <el-table-column
          label="备件编号"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="备件名称"
          prop="number"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="备件类别"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
      </el-table>

      <el-table v-if="title != '请选择关联备件'" :data="ledgerList" border>
        <el-table-column type="selection" align="center" width="70">
          <template #default="scope">
            <el-checkbox v-model="scope.row.checked" />
          </template>
        </el-table-column>
        <el-table-column
          label="工号"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="姓名"
          prop="number"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="性别"
          prop="number"
          show-overflow-tooltip
          min-width="90"
        />
        <el-table-column
          label="所属部门"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="岗位"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
    
<script setup>
import { ref, onMounted } from "vue";
import imgUpload from "@/components/ImageUpload";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute();

const uploadRef = ref(null);
const isEdit = ref(false);
const dialogVisible = ref(false);
const ledgerList = ref([]);
const recordList = ref([
  {
    spareName: "备件1",
    type: "修改",
    name: "张三",
    role: "管理员",
    time: "2024-11-20 21:49:11",
  },
]);
const queryParams = ref({
  current: 1,
  size: 5,
});
const errors = ref({}); // 改为对象形式存储每个字段的错误信息

// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  file: null,
});

const handleSelect = (type) => {
  if (type) {
    title.value = "请选择关联人员";
  } else {
    title.value = "请选择关联备件";
  }
  dialogVisible.value = true;
};

// 文件上传前的验证
const beforeUpload = (file) => {
  const allowedTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
  ];
  const allowedExtensions = ["doc", "docx", "xls", "xlsx", "txt"];
  const extension = file.name.split(".").pop().toLowerCase();
  const isValidType =
    allowedTypes.includes(file.type) || allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  errors.value.file = "";

  if (!isValidType) {
    errors.value.file = "只能上传doc、docx、xls、xlsx、txt格式的文件！";
    return false;
  }
  if (!isLt20M) {
    errors.value.file = "文件大小不能超过20MB！";
    return false;
  }
  return true;
};

// 文件改变时的处理
const handleFileChange = (file) => {
  if (file) {
    form.value.file = file.raw;
    errors.value.file = ""; // 清除文件错误提示
  }
};

// 返回
const handleCancel = () => {
  router.back();
};

// 验证表单
const validateForm = () => {
  errors.value = {}; // 清空之前的错误信息

  if (!form.value.id) {
    errors.value.id = "请输入预案编号";
  }
  if (!form.value.name) {
    errors.value.name = "请输入预案名称";
  }
  if (!form.value.type) {
    errors.value.type = "请选择预案类型";
  }
  if (!form.value.content) {
    errors.value.content = "请输入预案内容";
  }
  if (!form.value.file) {
    errors.value.file = "请上传预案文件";
  }

  return Object.keys(errors.value).length === 0;
};

// 提交
const handleSubmit = async () => {
  if (!isEdit.value) {
    isEdit.value = true;
    return;
  }
  if (!validateForm()) return;

  try {
    // 获取当前本地存储的数据
    let plans = JSON.parse(localStorage.getItem("emergencyPlans") || "[]");

    // 添加新预案
    const newPlan = {
      ...form.value,
      file: form.value.file
        ? {
            name: form.value.file.name,
            size: form.value.file.size,
            type: form.value.file.type,
          }
        : null, // 只保存文件的基本信息
      createTime: new Date().toISOString(),
      updateTime: isEdit.value ? new Date().toISOString() : null, // 只有编辑时才设置修改时间
    };

    if (isEdit.value) {
      // 编辑模式：更新现有预案
      const index = plans.findIndex((item) => item.id === form.value.id);
      if (index > -1) {
        plans[index] = newPlan;
      }
    } else {
      // 新建模式：添加新预案
      plans.push(newPlan);
    }

    // 保存到localStorage
    localStorage.setItem("emergencyPlans", JSON.stringify(plans));

    ElMessage.success("保存成功");
    router.push("/emergencyManage/operation");
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

// 初始化
onMounted(() => {
  // 如果是编辑模式，获取路由参数中的数据
  if (route.query.id) {
    isEdit.value = true;
    const plans = JSON.parse(localStorage.getItem("emergencyPlans") || "[]");
    const currentPlan = plans.find((item) => item.id === route.query.id);
    if (currentPlan) {
      form.value = { ...currentPlan };
    }
  }
});
</script>

<style lang="scss" scoped>
.ledgerInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
</style>