import request from "@/utils/request";

// 批量增加备件明细
export function addSparePartsTrouble(data) {
  return request({
    url: "/system/spareParts/deviceTrouble/add",
    method: "post",
    data,
  });
}

// 备件类别列表
export function sparePartsCategoryPage(params) {
  return request({
    url: "/system/sparePartsCategory/page",
    method: "get",
    params,
  });
}

// 下拉选择的备件类别列表
export function sparePartsCategoryList() {
  return request({
    url: `/system/sparePartsCategory/list`,
    method: "get",
  });
}

// 添加备件类别
export function addSparePartsCategory(data) {
  return request({
    url: "/system/sparePartsCategory/add",
    method: "post",
    data,
  });
}

// 修改备件类别
export function updateSparePartsCategory(data) {
  return request({
    url: "/system/sparePartsCategory/update",
    method: "put",
    data,
  });
}

// 批量删除备件类别
export function delSparePartsCategory(params) {
  return request({
    url: `/system/sparePartsCategory/delete`,
    method: "delete",
    params,
  });
}

// 备件类别详情
export function sparePartsCategoryInfo(id) {
  return request({
    url: `/system/sparePartsCategory/${id}`,
    method: "get",
  });
}

// 备件列表
export function sparePartsPage(params) {
  return request({
    url: "/system/spareParts/page",
    method: "get",
    params,
  });
}

// 添加备件
export function addSpareParts(data) {
  return request({
    url: "/system/spareParts/add",
    method: "post",
    data,
  });
}

// 修改备件
export function updateSpareParts(data) {
  return request({
    url: "/system/spareParts/update",
    method: "put",
    data,
  });
}

// 批量删除备件
export function delSpareParts(params) {
  return request({
    url: `/system/spareParts/delete`,
    method: "delete",
    params,
  });
}

// 批量/新增备件明细
export function addSparePartsDetails(data) {
  return request({
    url: "/system/spareParts/details/add",
    method: "post",
    data,
  });
}

// 删除备件明细
export function delSparePartsInfo(id) {
  return request({
    url: `/system/spareParts/delete/${id}`,
    method: "delete",
  });
}

// 备件详情
export function sparePartsInfo(id) {
  return request({
    url: `/system/spareParts/${id}`,
    method: "get",
  });
}

// 出入库记录
export function sparePartsEntryAndRecordPage(params) {
  return request({
    url: "/system/spareParts/entryAndRecord/page",
    method: "get",
    params,
  });
}

// 关联工单记录
export function sparePartsWorkOrderPage(params) {
  return request({
    url: "/system/spareParts/workOrder/page",
    method: "get",
    params,
  });
}

// 关联应急事件记录
export function sparePartsEmergencyPage(params) {
  return request({
    url: "/system/spareParts/emergency/page",
    method: "get",
    params,
  });
}
