<!-- components/emergency/RelatedPersonnelTable.vue -->
<template>
  <div class="related-personnel">
    <div class="taskInfo-tit">关联人员</div>
    <!-- 操作区域插槽 -->
    <div v-if="$slots.operations" class="table-operations">
      <slot name="operations" />
    </div>
    <el-table
      :data="personnel"
      border
      style="width: 100%; margin-top: 10px"
    >
      <el-table-column prop="workId" label="工号" min-width="180" />
      <el-table-column prop="name" label="姓名" min-width="180" show-overflow-tooltip/>
      <!-- <el-table-column prop="department" label="部门" width="180" /> -->
      <el-table-column prop="relateTime" label="关联时间" min-width="180">
        <template #default="scope">
          {{ formatDate(scope.row.relateTime) }}
        </template>
      </el-table-column>
      <!-- 添加报告列 -->
      <el-table-column prop="reportFileName" label="报告" min-width="180">
        <template #default="scope">
          <template v-if="scope.row.reportFileName">
            <el-button 
              type="primary" 
              link
              @click="downloadFile(scope.row.reportFileUrl, scope.row.reportFileName)"
            >
              {{ scope.row.reportFileName }}
            </el-button>
          </template>
          <span v-else class="no-report">未上传</span>
        </template>
      </el-table-column>
      <!-- 操作列 -->
      <!-- <el-table-column label="操作" width="220" align="center">
        <template #default="scope">
          <el-button
            v-if="!scope.row.reportFileName"
            link
            type="primary"
            @click="handleNotify(scope.row)"
          >
            一键通知
          </el-button>
          <span v-else-if="scope.row.notifyTime">已通知</span>
          <span v-else>-</span>
        </template>
      </el-table-column> -->
    </el-table>

    <!-- 添加通知确认对话框 -->
    <el-dialog
      v-model="notifyDialogVisible"
      title="通知确认"
      width="500"
      :close-on-click-modal="false"
    >
      <div class="notify-content">
        <p>是否确认通知该人员？</p>
        <div class="personnel-info" v-if="currentNotifyPerson">
          <p>{{ currentNotifyPerson.name }}</p>
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="notifyDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmNotify">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { ElMessage } from 'element-plus'
import { schoolPlanWorkOrderNotice } from '@/api/emergency'

const props = defineProps({
  personnel: {
    type: Array,
    required: true
  },
  readonly: {
    type: Boolean,
    default: false
  },
  showNotifyButton: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['refresh'])

// 通知相关的响应式变量
const notifyDialogVisible = ref(false)
const currentNotifyPerson = ref(null)

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 处理通知
const handleNotify = (row) => {
  currentNotifyPerson.value = row;
  notifyDialogVisible.value = true;
};

// 确认通知
const confirmNotify = async () => {
  try {
    if (!currentNotifyPerson.value?.workOrderId) {
      ElMessage.error('无效的工单ID');
      return;
    }

    const res = await schoolPlanWorkOrderNotice({
      planWorkOrderId: currentNotifyPerson.value.workOrderId
    });
    
    if (res.code === 200) {
      ElMessage.success('通知发送成功');
      notifyDialogVisible.value = false;
      currentNotifyPerson.value = null;
      emit('refresh'); // 触发刷新
    } else {
      ElMessage.error(res.msg || '通知发送失败');
    }
  } catch (error) {
    console.error("通知发送失败:", error);
    ElMessage.error("通知发送失败");
  }
};

// 添加下载文件方法
const downloadFile = (fileUrl, fileName) => {
  if (!fileUrl) {
    ElMessage.warning('文件不存在')
    return
  }

  try {
    // 确保使用HTTPS链接
    let downloadUrl = fileUrl
    if (downloadUrl.startsWith('http:')) {
      downloadUrl = downloadUrl.replace('http:', 'https:')
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = downloadUrl
    link.setAttribute('download', fileName || '未命名文件')
    link.setAttribute('target', '_blank')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}
</script>

<style scoped lang="scss">
.related-personnel {
  .taskInfo-tit {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0;
  }

  .table-operations {
    margin-bottom: 10px;
  }

  .no-report {
    color: #909399;
  }
}

.notify-content {
  padding: 20px;
  
  .personnel-info {
    margin-top: 10px;
    padding: 10px;
    background-color: #f5f7fa;
    border-radius: 4px;
  }
}
</style>