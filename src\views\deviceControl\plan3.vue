<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>
      <el-radio-group v-model="active" style="margin-bottom: 15px">
        <el-radio-button value="1">消息定时计划</el-radio-button>
        <el-radio-button value="2">设备开关机设置</el-radio-button>
        <el-radio-button value="3">无操作锁定设置</el-radio-button>
        <el-radio-button value="4">设备自动解锁</el-radio-button>
        <el-radio-button value="5">锁屏设置</el-radio-button>
      </el-radio-group>

      <msgPlan v-if="active == '1'" />
      <deviceSwitch v-if="active == '2'" />
      <deviceLock v-if="active == '3'" />
      <deviceUnlock v-if="active == '4'" />
      <lockScreen v-if="active == '5'" />
    </el-card>
  </div>
</template>

<script setup name="recording">
import msgPlan from "../deviceControl/components/plan/msgPlan.vue";
import deviceUnlock from "../deviceControl/components/plan/deviceUnlock.vue";
import deviceLock from "../deviceControl/components/plan/deviceLock.vue";
import deviceSwitch from "../deviceControl/components/plan/deviceSwitch.vue";
import lockScreen from "../deviceControl/components/plan/lockScreen.vue";

import { useRoute } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";

const route = useRoute();
const { proxy } = getCurrentInstance();
const state = reactive({
  active: "1",
  loading: false,
  statusList: [
    { label: "待处理", value: 0, type: "danger" },
    { label: "已处理", value: 1, type: "success" },
  ],
  queryParams: {
    current: 1,
    size: 10,
  },
});

const { active, queryParams, statusList, loading } = toRefs(state);

function handleDel({ row, $index }) {}

function getList() {}

getList();
</script>