<template>
  <div class="projectInfo app-container">
    <el-card shadow="never" class="page-card" v-loading="loading">
      <template #header>
        <div class="card-header page-header">项目信息详情</div>
      </template>
      <el-form ref="projectRef" :model="projectInfo" :rules="rules">
        <el-descriptions title="" border>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目编号"
          >
            <span>{{ projectInfo.projectNo }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目名称"
          >
            <span v-if="!isEdit">{{ projectInfo.projectName }}</span>
            <el-form-item prop="projectName" label="" v-else>
              <el-input
                v-model="projectInfo.projectName"
                placeholder="请输入项目名称"
                style="width: 250px"
                clearable
                maxlength="30"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目负责人"
          >
            <span v-if="!isEdit">{{ projectInfo.responsibleNameStr }}</span>
            <el-form-item prop="responsibleId" label="" v-else>
              <el-select
                v-model="projectInfo.responsibleId"
                placeholder="请选择项目负责人"
                style="width: 250px"
                filterable
                clearable
                multiple
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                />
              </el-select>
              <el-input
                v-model="projectInfo.responsibleName"
                placeholder="请输入其他项目负责人"
                style="width: 250px; margin-top: 10px"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目干系人"
          >
            <span v-if="!isEdit">{{ projectInfo.stakeholderNameStr }}</span>
            <el-form-item prop="stakeholderId" label="" v-else>
              <el-select
                v-model="projectInfo.stakeholderId"
                placeholder="请选择项目干系人"
                style="width: 250px"
                filterable
                multiple
                clearable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                />
              </el-select>
              <el-input
                v-model="projectInfo.stakeholderName"
                placeholder="请输入其他项目干系人"
                style="width: 250px; margin-top: 10px"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目参与人"
          >
            <span v-if="!isEdit">{{ projectInfo.partakeNameStr }}</span>
            <el-form-item prop="partakeId" label="" v-else>
              <el-select
                v-model="projectInfo.partakeId"
                placeholder="请选择项目参与人"
                style="width: 250px"
                filterable
                multiple
                clearable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                />
              </el-select>
              <el-input
                v-model="projectInfo.partakeName"
                placeholder="请输入其他项目参与人"
                style="width: 250px; margin-top: 10px"
                maxlength="50"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目开始时间"
          >
            <span v-if="!isEdit">{{ projectInfo.startTime }}</span>
            <el-form-item prop="startTime" label="" v-else>
              <el-date-picker
                v-model="projectInfo.startTime"
                type="datetime"
                clearable
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择项目开始时间"
                style="width: 250px"
                @change="projectInfo.endTime = null"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目结束时间"
          >
            <span v-if="!isEdit">{{ projectInfo.endTime }}</span>
            <el-form-item prop="endTime" label="" v-else>
              <el-date-picker
                v-model="projectInfo.endTime"
                type="datetime"
                clearable
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择项目结束时间"
                :disabled-date="disabledDate"
                :disabled="!projectInfo.startTime"
                @change="handleEndTimeChange"
                style="width: 250px"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目金额"
          >
            <span v-if="!isEdit">{{ projectInfo.projectAmount }}元</span>
            <el-form-item prop="projectAmount" label="" v-else>
              <el-input-number
                v-model="projectInfo.projectAmount"
                :min="0"
                :value-on-clear="0"
                style="width: 170px"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目归属部门"
          >
            <span v-if="!isEdit">{{ projectInfo.projectDeptName }}</span>
            <el-form-item prop="projectDeptId" label="" v-else>
              <el-tree-select
                v-model="projectInfo.projectDeptId"
                :props="deptProps"
                :data="deptList"
                placeholder="请选择项目归属部门"
                check-strictly
                :render-after-expand="false"
                :expand-on-click-node="false"
                style="width: 250px"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="甲方"
          >
            <span v-if="!isEdit">{{ projectInfo.partyA }}</span>
            <el-form-item prop="partyA" label="" v-else>
              <el-input
                placeholder="请输入甲方名称"
                v-model="projectInfo.partyA"
                style="width: 250px"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="乙方"
          >
            <span v-if="!isEdit">{{ projectInfo.partyB }}</span>
            <el-form-item prop="partyB" label="" v-else>
              <el-input
                placeholder="请输入乙方名称"
                v-model="projectInfo.partyB"
                style="width: 250px"
                clearable
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="开放小程序搜索"
          >
            <span v-if="!isEdit">{{
              projectInfo.isOpen ? "开放" : "不开放"
            }}</span>
            <el-form-item prop="isOpen" label="" v-else>
              <el-radio-group v-model="projectInfo.isOpen">
                <el-radio label="不开放" :value="0" />
                <el-radio label="开放" :value="1" />
              </el-radio-group>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <div class="projectInfo-btns">
        <el-button type="primary" @click="handleEdit">{{
          !isEdit ? "编辑" : "保存"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <div class="projectInfo-record" v-if="recordList.length > 0">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role || ""} 于 ${item.time} ${item.type}了${
              item.spareName
            }的现库存`
          }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { updateSchoolProject, schoolProjectInfo } from "@/api/order";
import { getUserByRole } from "@/api/system/user";
import { listTree } from "@/api/system/distribution";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  projectRef: null,
  loading: false,
  isEdit: false,
  recordList: [],
  userList: [],
  deptList: [],
  projectInfo: {
    projectNo: "",
    projectName: "",
    responsibleId: [],
    responsibleName: "",
    sysResponsibleName: "",
    stakeholderId: [],
    stakeholderName: "",
    sysStakeholderName: "",
    partakeId: [],
    partakeName: "",
    sysPartakeName: "",
    startTime: "",
    endTime: "",
    projectAmount: 0,
    projectDeptId: "",
    projectDeptName: "",
    partyA: "",
    partyB: "",
    isOpen: 0,
  },
  deptProps: {
    label: "deptName",
    children: "children",
    value: "deptId",
  },
  rules: {
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "blur" },
    ],
    projectAmount: [
      {
        validator: validateAmount,
        trigger: ["blur", "change"],
      },
    ],
    responsibleId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 0),
        trigger: ["blur", "change"],
      },
    ],
    stakeholderId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 1),
        trigger: ["blur", "change"],
      },
    ],
    partakeId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 2),
        trigger: ["blur", "change"],
      },
    ],
    startTime: [
      {
        required: true,
        message: "项目开始时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
    projectDeptId: [
      {
        required: true,
        message: "归属部门不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  rules,
  projectRef,
  deptProps,
  loading,
  recordList,
  projectInfo,
  userList,
  deptList,
  isEdit,
} = toRefs(state);

const disabledDate = (date) => {
  if (date.getTime() < new Date(state.projectInfo.startTime).getTime()) {
    return true;
  }
  return false;
};

const handleEndTimeChange = (val) => {
  console.log("项目结束时间", val);
  if (
    new Date(state.projectInfo.startTime).getTime() >= new Date(val).getTime()
  ) {
    proxy.$modal.msgError("结束时间不能小于等于开始时间");
    state.projectInfo.endTime = null;
  }
};

function validateAmount(rule, value, callback) {
  if (value > 999999999) {
    callback(new Error("项目金额不能大于999999999"));
  } else {
    callback();
  }
}

const validateName = (rule, value, callback, index) => {
  let arr = [
    { label: "项目负责人", prop: "responsibleName" },
    { label: "项目干系人", prop: "stakeholderName" },
    { label: "项目参与人", prop: "partakeName" },
  ];
  if (state.projectInfo[arr[index].prop] === "" && value.length == 0) {
    callback(new Error(`${arr[index].label}不能为空`));
  } else {
    callback();
  }
};

function handleEdit() {
  if (state.isEdit) {
    proxy.$modal.loading();
    state.projectInfo.responsibleId = state.projectInfo.responsibleId.join(",");
    state.projectInfo.stakeholderId = state.projectInfo.stakeholderId.join(",");
    state.projectInfo.partakeId = state.projectInfo.partakeId.join(",");
    updateSchoolProject(state.projectInfo)
      .then((res) => {
        getData();
        proxy.$modal.msgSuccess("修改成功");
        if (route.query.type == 0) state.isEdit = !state.isEdit;
        proxy.$modal.closeLoading();
      })
      .catch(() => proxy.$modal.closeLoading());
  } else {
    state.isEdit = !state.isEdit;
  }
}

function handleBack() {
  // proxy.$tab.closeOpenPage("project");
  router.go(-1);
  proxy.$tab.closeOpenPage();
}

/** 获取运维人员列表 */
function getUserList() {
  getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["maintain", "maintainManage"],
  }).then((resp) => {
    console.log("运维人员", resp);
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

/** 获取部门列表 */
function getDeptList() {
  listTree({ status: 0 }).then((resp) => {
    console.log("部门树", resp);
    if (resp.data) {
      state.deptList = resp.data;
    }
  });
}

async function getData() {
  if (route.query.id) {
    state.loading = true;
    await schoolProjectInfo(route.query.id)
      .then((resp) => {
        console.log("项目信息", resp);
        if (resp.data) {
          state.projectInfo = resp.data;
          const {
            responsibleId,
            responsibleName,
            sysResponsibleName,
            stakeholderId,
            stakeholderName,
            sysStakeholderName,
            partakeId,
            partakeName,
            sysPartakeName,
          } = state.projectInfo;
          state.projectInfo = {
            ...state.projectInfo,
            partakeNameStr: (sysPartakeName?.split("、") || [])
              .concat(partakeName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
            responsibleNameStr: (sysResponsibleName?.split("、") || [])
              .concat(responsibleName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
            stakeholderNameStr: (sysStakeholderName?.split("、") || [])
              .concat(stakeholderName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
            responsibleId: responsibleId?.split(",").filter((_) => !!_),
            stakeholderId: stakeholderId?.split(",").filter((_) => !!_),
            partakeId: partakeId?.split(",").filter((_) => !!_),
          };
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
}

onMounted(() => {
  state.isEdit = route.query.type == 1;
  getData();
  getDeptList();
  getUserList();
});
</script>

<style lang="scss" scoped>
.projectInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-btns {
    padding: 50px 0;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }

  &-record {
    font-size: 14px;
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
      font-size: 16px;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
</style>