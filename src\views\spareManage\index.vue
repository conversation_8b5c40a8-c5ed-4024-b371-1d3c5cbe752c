<template>
  <div class="spare app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <!-- 默认显示列表操作记录的第一条数据 -->
          {{ route.meta.title }}
        </div>
      </template>
      <div class="spare-main">
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="codeAndName">
            <el-input
              v-model="queryParams.codeAndName"
              placeholder="请输入备件编号/名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="typeId">
            <el-select
              v-model="queryParams.typeId"
              placeholder="请选择备件类别"
              @change="handleQuery"
              clearable
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="item in typeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="mb12">
          <el-button plain type="primary" icon="Plus" @click="handleAdd"
            >新建备件</el-button
          >
          <el-button
            plain
            type="success"
            icon="Tickets"
            @click="handleDialog(1)"
            :disabled="tableAllSelectedId.length < 1"
            >批量关联工单</el-button
          >
          <el-button
            plain
            type="warning"
            icon="Suitcase"
            @click="handleDialog(2)"
            :disabled="tableAllSelectedId.length < 1"
            >批量关联应急事件</el-button
          >
          <el-button
            plain
            type="danger"
            icon="Delete"
            @click="handleBatchDel"
            :disabled="tableAllSelectedId.length < 1"
            >批量删除</el-button
          >
        </div>

        <div class="spare-main_table">
          <el-table
            ref="spareTableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
          >
            <el-table-column
              label=""
              type="selection"
              align="center"
              minWidth="60px"
              fixed="left"
            />
            <el-table-column
              label="备件编号"
              align="center"
              minWidth="100px"
              prop="code"
              show-overflow-tooltip
            />
            <el-table-column
              label="备件名称"
              align="center"
              minWidth="120px"
              prop="name"
              show-overflow-tooltip
            />
            <el-table-column
              label="备件类别"
              align="center"
              minWidth="100px"
              prop="typeName"
              show-overflow-tooltip
            />
            <el-table-column
              label="规格型号"
              align="center"
              minWidth="120px"
              prop="specifications"
              show-overflow-tooltip
            />
            <el-table-column
              label="备件品牌"
              align="center"
              minWidth="120px"
              prop="brand"
              show-overflow-tooltip
            />
            <el-table-column
              label="颜色分类"
              align="center"
              minWidth="120px"
              prop="color"
              show-overflow-tooltip
            />
            <el-table-column
              label="现有库存"
              align="center"
              minWidth="100px"
              prop="stock"
              show-overflow-tooltip
            />
            <el-table-column
              label="总消耗量"
              align="center"
              minWidth="100px"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.consumption || 0 }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="300"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="success"
                  icon="Search"
                  @click.stop="handleCheck(scope.row, 0)"
                  >查看</el-button
                >
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleCheck(scope.row, 1)"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="warning"
                  icon="Download"
                  @click.stop="handleDialog(0, scope.row)"
                  >出入库</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 新增备件信息弹窗 -->
    <el-dialog
      class="custom-dialog"
      title="添加备件"
      v-model="dialogVisible"
      width="500px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancel"
    >
      <el-form ref="spareRef" :model="spareInfo" :rules="rules">
        <el-form-item prop="name" label="备件名称">
          <el-input
            v-model="spareInfo.name"
            style="width: 250px"
            placeholder="请输入备件名称"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item prop="typeId" label="备件类别">
          <el-select
            v-model="spareInfo.typeId"
            placeholder="请选择备件类型"
            clearable
            filterable
            style="width: 250px"
          >
            <el-option
              v-for="item in typeList_disabled"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item prop="specifications" label="规格型号">
          <el-input
            v-model="spareInfo.specifications"
            style="width: 250px"
            placeholder="请输入规格型号"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item prop="brand" label="备件品牌">
          <el-input
            v-model="spareInfo.brand"
            style="width: 250px"
            placeholder="请输入备件品牌"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item prop="color" label="颜色分类">
          <el-input
            v-model="spareInfo.color"
            style="width: 250px"
            placeholder="请输入颜色分类"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item prop="stock" label="现有库存">
          <el-input-number
            v-model="spareInfo.stock"
            :value-on-clear="0"
            :min="0"
            :max="999999999"
          />
        </el-form-item>
        <el-form-item prop="consumption" label="总消耗量">
          <el-input-number
            v-model="spareInfo.consumption"
            :min="0"
            :max="999999999"
            :value-on-clear="0"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click.stop="handleCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleSubmit" v-throttle
          >确定</el-button
        >
      </template>
    </el-dialog>

    <!-- 添加出入库/关联工单/应急事件记录 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisibleRela"
      width="700"
      @close="handleCancelRela"
    >
      <el-form :model="form" ref="dialogFormRef" :rules="relaRules">
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="form.type"
            style="width: 250px"
            :disabled="isBatch"
            placeholder="请选择类型"
            @change="form.source = ''"
          >
            <el-option label="出库" :value="0" />
            <el-option label="入库" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="单据来源" prop="source">
          <el-select
            v-model="form.source"
            style="width: 250px"
            :placeholder="form.type === '' ? '请先选择类型' : '请选择单据来源'"
            :disabled="isBatch || form.type === ''"
            @change="
              showRelaTable = false;
              dialogFormRef.clearValidate('associatedCode');
            "
          >
            <el-option v-if="form.type === 1" label="采购" :value="0" />
            <el-option v-if="form.type === 0" label="关联工单" :value="1" />
            <el-option v-if="form.type === 0" label="应急事件" :value="2" />
          </el-select>
        </el-form-item>
        <div v-if="form.type == 1 && form.source == 0">
          <el-form-item label="入库数量" prop="num">
            <el-input-number
              v-model="form.num"
              :min="1"
              :max="999999999"
              style="width: 160px"
            />
          </el-form-item>
        </div>
        <div v-if="form.type == 0 && form.source > 0">
          <el-form-item
            label="关联单据编号"
            prop="associatedCode"
            :rules="{
              required: true,
              message: '关联单据编号不能为空',
              trigger: 'change',
            }"
          >
            <el-input
              v-model="form.associatedCode"
              placeholder="请选择关联单据"
              readonly
              @click="handleShowTable"
              style="width: 250px"
            />
          </el-form-item>

          <div v-if="showRelaTable">
            <el-form
              class="search-list"
              :model="queryParamsRela"
              ref="queryRelaRef"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item label="" prop="code">
                <el-input
                  v-model="queryParamsRela.code"
                  placeholder="请输入单据编号"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQueryRela"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQueryRela"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQueryRela"
                  >重置</el-button
                >
              </el-form-item>
            </el-form>
            <el-table
              ref="dialogTableRef"
              v-loading="loadingDialog"
              :data="tableListRela"
              border
              @row-click="rowClickRela"
            >
              <el-table-column label="" align="center" width="80">
                <template #default="scope">
                  <el-checkbox
                    v-model="scope.row.checked"
                    @change="(val) => handleSingleChange(val, scope)"
                    @click.native.stop=""
                  />
                </template>
              </el-table-column>
              <el-table-column label="单据编号" prop="code" min-width="100" />
              <el-table-column
                label="创建时间"
                prop="createTime"
                min-width="100"
              />
              <el-table-column label="消耗量" align="center">
                <template #default="scope">
                  <el-input-number
                    size="small"
                    v-model="scope.row.num"
                    :disabled="!scope.row.checked"
                    :min="1"
                    :max="99999"
                    @click.native.stop=""
                  />
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="totalRela > 0"
              :total="totalRela"
              v-model:page="queryParamsRela.pageNum"
              v-model:limit="queryParamsRela.pageSize"
              @pagination="getRelateList"
              :background="false"
              layout="total,prev,pager,next"
            />
            <div
              v-show="!!dialogSelectedRow.code"
              style="display: flex; justify-content: center; margin-top: 20px"
            >
              <el-button
                icon="Connection"
                plain
                type="primary"
                @click="handleRelate"
                >关联该条单据</el-button
              >
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <el-button @click.stop="handleCancelRela">取消</el-button>
        <el-button type="primary" @click.stop="handleConfirmRela" v-throttle
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="spare">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import { findIndexInObejctArr } from "@/utils";
import { schoolPlanWorkOrderList } from "@/api/emergency";
import { deviceMaintenanceList } from "@/api/mediaTeach/trouble";
import { useRouter, useRoute } from "vue-router";
import {
  sparePartsPage,
  delSpareParts,
  addSpareParts,
  addSparePartsDetails,
  sparePartsCategoryList,
  sparePartsCategoryPage,
} from "@/api/spare";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const state = reactive({
  isBatch: false,
  dialogFormRef: null,
  spareTableRef: null,
  queryRelaRef: null,
  spareRef: null,
  dialogVisible: false,
  dialogVisibleRela: false,
  loading: false,
  loadingDialog: false,
  showRelaTable: false,
  total: 0,
  totalRela: 0,
  title: "",
  tableList_all: [],
  tableList: [],
  tableListRela: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  typeList: [],
  typeList_disabled: [],
  // 列表条件筛选字段
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    codeAndName: "",
    typeId: "",
  },
  queryParamsRela: {
    pageNum: 1,
    pageSize: 5,
    code: "",
    type: 2,
    troubleStatusList: [2, 3],
  },
  dialogSelectedRow: {},
  form: {},
  // 详情弹窗的某条数据详情
  spareInfo: {},
  // 列表增删改的操作记录
  recordList: [],
  rules: {
    name: [{ required: true, message: "备件名称不能为空", trigger: "blur" }],
    typeId: [
      { required: true, message: "备件类别不能为空", trigger: "change" },
    ],
    specifications: [
      { required: true, message: "规格型号不能为空", trigger: "blur" },
    ],
    brand: [{ required: true, message: "备件品牌不能为空", trigger: "blur" }],
    color: [{ required: true, message: "颜色分类不能为空", trigger: "blur" }],
  },
  relaRules: {
    type: [
      {
        required: true,
        message: "类型不能为空",
        trigger: "blur",
      },
    ],
    source: [
      {
        required: true,
        message: "单据来源不能为空",
        trigger: "blur",
      },
    ],
    associatedCode: [
      {
        required: true,
        message: "单据编号不能为空",
        trigger: "blur",
      },
    ],
  },
});

const {
  typeList_disabled,
  relaRules,
  isBatch,
  showRelaTable,
  dialogFormRef,
  spareTableRef,
  queryRelaRef,
  spareRef,
  rules,
  title,
  loading,
  loadingDialog,
  total,
  totalRela,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableList,
  tableListRela,
  form,
  dialogSelectedRow,
  queryParamsRela,
  tableList_all,
  tableRadio,
  spareInfo,
  recordList,
  typeList,
  dialogVisible,
  dialogVisibleRela,
  queryParams,
} = toRefs(state);

// 弹窗确认按钮
const handleConfirmRela = () => {
  state.dialogFormRef.validate((valid) => {
    if (valid) {
      const { type, num, source, associatedCode, associatedId, id } =
        state.form;
      let obj = {
        sparePartsIdList: !!id ? [id] : state.tableAllSelectedId,
        type,
        num,
        resourceType: "",
        associatedCode: "",
        associatedId: "",
        resource: ["采购", "关联工单", "应急事件"][source],
      };
      if (state.form.source == 0) {
        obj.resourceType = 3;
      }
      if (state.form.source == 1) {
        obj.resourceType = 1;
        obj.associatedCode = associatedCode;
        obj.associatedId = associatedId;
      }
      if (state.form.source == 2) {
        obj.resourceType = 2;
        obj.associatedCode = associatedCode;
        obj.associatedId = associatedId;
      }
      console.log("obj", obj, state.form);
      addSparePartsDetails(obj).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          handleCancelRela();
        }
      });
    }
  });
};

// 打开弹窗
const handleDialog = (val, item) => {
  if (val == 0) {
    state.isBatch = false;
    state.title = "新增出入库记录";
    state.form = {
      id: item.id,
      type: "",
      source: "",
      num: 0,
    };
  }
  if (val == 1) {
    state.isBatch = true;
    state.title = "新增关联工单";
    state.form = {
      id: "",
      type: 0,
      source: 1,
      associatedCode: "",
      num: 0,
    };
  }
  if (val == 2) {
    state.isBatch = true;
    state.title = "新增应急事件";
    state.form = {
      id: "",
      type: 0,
      source: 2,
      associatedCode: "",
      num: 0,
    };
  }
  state.dialogVisibleRela = true;
};

// 弹窗关联单据按钮
function handleRelate() {
  const { code, id, num } = state.dialogSelectedRow;
  state.form.associatedCode = code;
  state.form.associatedId = id;
  state.form.num = num;
  state.showRelaTable = false;
  // console.log(state.dialogSelectedRow, state.form)
}

// 点击显示关联单据表格
const handleShowTable = () => {
  state.showRelaTable = true;
  state.dialogSelectedRow = {};
  state.form.associatedCode = "";
  state.queryParamsRela.code = "";
  nextTick(() => resetQueryRela());
};

function handleSingleChange(val, scope) {
  console.log(val, scope, state.tableListRela);
  if (val) {
    clearCheck();
    scope.row.checked = true;
    state.dialogSelectedRow = scope.row;
    console.log("check", val);
  } else {
    state.dialogSelectedRow = {};
  }
}

function clearCheck() {
  state.tableListRela = state.tableListRela.map((item) => {
    item.checked = false;
    return item;
  });
}

function rowClickRela(row) {
  console.log("rowClick", row);
  const idx = state.tableListRela.findIndex((_) => _.code == row.code);
  if (row.checked) {
    state.tableListRela[idx].checked = false;
    state.dialogSelectedRow = {};
  } else {
    clearCheck();
    state.tableListRela[idx].checked = true;
    state.dialogSelectedRow = row;
  }
}

// 获取关联单据表格数据/单据表分页
function getRelateList() {
  state.loadingDialog = true;
  clearCheck();
  if (state.form.source == 1) {
    state.queryParamsRela.workOrderCode = state.queryParamsRela.code;
    console.log("queryParamsRela", state.queryParamsRela);
    deviceMaintenanceList(state.queryParamsRela).then((response) => {
      if (response.code == 200) {
        console.log("工单列表", response);
        const { total, maintenanceWebListVOList } = response.data || {};
        state.tableListRela = (maintenanceWebListVOList || []).reduce(
          (res, cur) => {
            res.push({
              id: cur.troubleId,
              code: cur.workOrderCode,
              createTime: cur.submittedTime,
              num: 1,
              checked: state.dialogSelectedRow.code == cur.workOrderCode,
            });
            return res;
          },
          []
        );
        state.totalRela = total || 0;
        state.loadingDialog = false;
      }
    });
  }
  if (state.form.source == 2) {
    state.queryParamsRela.workOrderCode = state.queryParamsRela.code;
    console.log("queryParamsRela", state.queryParamsRela);
    schoolPlanWorkOrderList(state.queryParamsRela).then((response) => {
      console.log("应急事件列表", response);
      if (response.code == 200) {
        const { total, records } = response.data || {};
        state.tableListRela = (records || []).reduce((res, cur) => {
          res.push({
            id: cur.id,
            code: cur.code,
            createTime: cur.createTime,
            num: 1,
            checked: state.dialogSelectedRow.code == cur.code,
          });
          return res;
        }, []);
        state.totalRela = total || 0;
        state.loadingDialog = false;
      }
    });
  }
}

/** 弹窗搜索按钮操作 */
function handleQueryRela() {
  state.queryParamsRela.pageNum = 1;
  getRelateList();
}

/** 弹窗重置按钮操作 */
function resetQueryRela() {
  proxy.resetForm("queryRelaRef");
  state.dialogSelectedRow = {};
  handleQueryRela();
}

/** 列表的详情按钮 */
function handleCheck(item, val) {
  console.log(item);
  router.push(`/spareManage/spareInfo?id=${item.id}&type=${val}`);
}

async function handleAdd() {
  await sparePartsCategoryPage({
    pageNum: 1,
    pageSize: 999999,
    status: 1,
  }).then((response) => {
    if (response.data) {
      state.typeList_disabled = response.data.records || [];
      console.log(response, state.typeList_disabled);
    }
  });
  state.spareInfo = {
    stock: 0,
    name: "",
    brand: "",
    consumption: 0,
    typeId: "",
    specifications: "",
    color: "",
  };
  state.dialogVisible = true;
}

function handleDel(item, index) {
  proxy.$modal
    .confirm(`确定要删除编号为${item.code}的备件信息？`)
    .then(async function () {
      await delSpareParts({ ids: item.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

// 批量删除操作
function handleBatchDel() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一个点位");
    return;
  } else {
    proxy.$modal
      .confirm("确定批量删除？")
      .then(async function () {
        await delSpareParts({ ids: state.tableAllSelectedId.join(",") });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
}

/** 弹窗的确认按钮 */
function handleSubmit() {
  state.spareRef.validate((valid) => {
    if (valid) {
      state.spareInfo.typeName = state.typeList.find(
        (_) => _.id == state.spareInfo.typeId
      ).name;
      addSpareParts(state.spareInfo).then((response) => {
        proxy.$modal.msgSuccess("新增成功");
        getList();
        handleCancel();
      });
    }
  });
}

function handleCancelRela() {
  state.dialogFormRef.resetFields();
  state.dialogSelectedRow = {};
  state.showRelaTable = false;
  state.dialogVisibleRela = false;
}

/** 弹窗的取消按钮 */
function handleCancel() {
  state.spareRef.resetFields();
  state.dialogVisible = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  state.queryParams.pageSize = 10;
  handleQuery();
}

function getList() {
  state.loading = true;
  sparePartsPage(state.queryParams).then((response) => {
    if (response.code == 200) {
      console.log("备件", response);
      const { records, total } = response.data;
      state.tableList = records || [];
      state.total = total || 0;
      state.loading = false;
      nextTick(() => {
        state.tableList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.id) > -1) {
            state.spareTableRef.toggleRowSelection(item, true);
          } else {
            state.spareTableRef.toggleRowSelection(item, false);
          }
        });
      });
    }
  });
  sparePartsPage({ ...state.queryParams, pageNum: 1, pageSize: 999999 }).then(
    (response) => {
      if (response.code == 200) {
        const { records } = response.data;
        state.tableList_all = records || [];
      }
    }
  );
  sparePartsCategoryList().then((response) => {
    if (response.code == 200) {
      console.log(response);
      state.typeList = response.data || [];
    }
  });
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "id"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.spareTableRef.setCurrentRow(null);
      state.spareTableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.id);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.spareTableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.spareTableRef.setCurrentRow(row);
    state.spareTableRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.id) === -1) {
      state.tableAllSelectedId.push(item.id);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.id);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.id) === -1) {
        state.tableAllSelectedId.push(item.id); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

onMounted(() => {
  getList();
});
</script>

