import request from "@/utils/request";

// 删除巡检计划
export function deleteInspectionPlan(params) {
  return request({
    url: `/system/schoolInspectionPersonnel/delete`,
    method: "delete",
    params
  });
}

// 删除巡检计划人员
export function deleteInspection(data) {
  return request({
    url: "/system/user/deleteInspection",
    method: "post",
    data,
  });
}

// 查询运维人员列表 /system/user/listMaintain
export function getMaintainList(params) {
  return request({
    url: "/system/user/listMaintain",
    method: "get",
    params,
  });
}

// 查询运维人员列表2 /system/user/role/list
export function getRoleList(data) {
  return request({
    url: "/system/user/role/list",
    method: "post",
    data,
  });
}

// 查询选择要加入运维人员的用户列表 /system/user/listUserToMaintain
export function getUserToMaintainList(params) {
  return request({
    url: "/system/user/listUserToMaintain",
    method: "get",
    params,
  });
}

// 选择人员到运维人员列表 /system/user/addMaintainInUserList
export function addMaintainInUserList(data) {
  return request({
    url: "/system/user/addMaintainInUserList",
    method: "post",
    data,
  });
}

// 新建人员 /system/user/addMaintainList
export function addMaintainList(data) {
  return request({
    url: "/system/user/addMaintainList",
    method: "post",
    data,
  });
}

// 批量删除 /system/user/delMaintain
export function delMaintain(data) {
  return request({
    url: "/system/user/delMaintain",
    method: "post",
    data,
  });
}

// 下载运维人员导入模板 /system/user/download/importMaintain-template
export function downloadMaintainImportTemplate() {
  return request({
    url: "/system/user/download/importMaintain-template",
    method: "get",
    responseType: "blob",
  });
}

// 批量导入 /system/user/importMaintain
export function importMaintain(data) {
  return request({
    url: "/system/user/importMaintain",
    method: "post",
    data,
  });
}

// 批量导出 /system/user/exportMaintain
export function exportMaintain(params) {
  return request({
    url: "/system/user/exportMaintain",
    method: "post",
    responseType: "blob",
    params,
  });
}

// 查询运维人员详情 /system/user/getMaintainInfo/116
export function getMaintainInfo(id) {
  return request({
    url: `/system/user/getMaintainInfo/${id}`,
    method: "get",
  });
}

// 查询打卡记录列表 /system/schoolWork/listClock
export function listClock(params) {
  return request({
    url: "/system/schoolWork/listClock",
    method: "get",
    params,
  });
}
