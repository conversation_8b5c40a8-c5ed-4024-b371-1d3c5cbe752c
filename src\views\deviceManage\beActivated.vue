<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <div class="monitor">
        <div style="margin-bottom: 10px; font-size: 16px">
          机构点位：{{ currentPointNum }} / {{ pointNum || 0 }}
        </div>
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
          style="width: 100%"
        >
          <el-form-item prop="machineCode">
            <el-input
              v-model="queryParams.machineCode"
              placeholder="请输入设备机器码"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="time">
            <el-date-picker
              v-model="queryParams.time"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              start-placeholder="安装开始日期"
              end-placeholder="安装结束日期"
              :disabled-date="disabledDate"
              @change="handleQuery"
              @clear="handleQuery"
              style="width: 300px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb12" style="gap: 10px 0">
          <el-col :span="1.5" v-if="!isExternalPark">
            <el-button
              type="success"
              plain
              icon="Finished"
              :disabled="tableAllSelectedId.length < 1"
              @click="handleBatchActivate"
              >批量激活</el-button
            >
          </el-col>
        </el-row>

        <div>
          <el-table
            ref="ledgerTable"
            v-loading="loading"
            :data="tableData"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
              fixed="left"
            />
            <el-table-column
              label="设备机器码"
              align="center"
              prop="machineCode"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              label="安装时间"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="createTime"
            >
              <template #header>
                <div
                  style="padding-left: 5px; cursor: pointer"
                  @click="handleSortDate"
                >
                  安装时间
                  <span class="caret-wrapper">
                    <i
                      class="sort-caret ascending"
                      :class="queryParams.sort == 'asc' ? 'active' : ''"
                    ></i>
                    <i
                      class="sort-caret descending"
                      :class="queryParams.sort == 'desc' ? 'active' : ''"
                    ></i>
                  </span>
                </div> </template
            ></el-table-column>
            <el-table-column
              label="操作"
              align="center"
              width="200"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  icon="Delete"
                  type="danger"
                  @click.stop="handleDelete(scope.row)"
                  >删除</el-button
                >
                <el-button
                  link
                  icon="Check"
                  type="success"
                  @click.stop="handleActivate(scope.row)"
                  >激活</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加安装位置选择对话框 -->
    <el-dialog
      class="custom-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      title="请选择设备安装位置（可跳过）"
      v-model="installDialogVisible"
      width="650px"
      append-to-body
      align-center
      @close="handleInstallCancel"
    >
      <el-form :model="form" ref="formRef">
        <el-table :data="form.selectedDevices" border max-height="70vh">
          <el-table-column
            prop="machineCode"
            label="设备机器码"
            align="center"
            minWidth="150"
          />
          <el-table-column
            prop="createTime"
            label="安装时间"
            align="center"
            minWidth="140"
          />
          <el-table-column
            prop="installAddress"
            label="安装位置"
            align="center"
            minWidth="200"
          >
            <template #default="scope">
              <el-form-item
                label=""
                :prop="`selectedDevices.${scope.$index}.installAddressId`"
                :rules="[
                  {
                    required: true,
                    message: '请选择安装位置',
                    trigger: ['blur', 'change'],
                  },
                ]"
              >
                <el-tree-select
                  v-model="scope.row.installAddressId"
                  :props="positionProps"
                  :data="positionTreeList_disabled"
                  placeholder="请选择安装位置"
                  :render-after-expand="false"
                  clearable
                  check-strictly
                  style="width: 100%"
                  @change="(val) => handlePosition(val, scope.row)"
                />
              </el-form-item>
            </template>
          </el-table-column>
        </el-table>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleInstallNext"
            >下一步</el-button
          >
          <el-button type="info" @click="handleInstallSkip">跳过</el-button>
          <el-button @click="handleInstallCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改确认激活对话框 -->
    <el-dialog
      :close-on-click-modal="false"
      v-model="confirmDialogVisible"
      :title="`确认激活${form.selectedDevices.length > 1 ? '这些' : '此'}设备?`"
      width="400px"
      append-to-body
      align-center
    >
      <div style="padding: 10px 0; display: flex; gap: 0 50px">
        <div>本次激活点位数：{{ form.selectedDevices.length }}</div>
        <div>当前剩余点位数：{{ data.pointNum - data.currentPointNum }}</div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="handleConfirmActivate"
            :loading="activatingLoading"
            >激活</el-button
          >
          <el-button @click="handleConfirmCancel">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加激活成功对话框 -->
    <el-dialog
      class="success-dialog"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      v-model="successDialogVisible"
      width="400px"
      append-to-body
      align-center
    >
      <template #default>
        <div class="success-content">
          <el-icon
            class="success-icon"
            :color="isSuccess ? '#67C23A' : '#f56c6c'"
            :size="50"
          >
            <CircleCheckFilled v-if="isSuccess" />
            <CircleCloseFilled v-if="!isSuccess" />
          </el-icon>
          <div
            class="success-text"
            :style="{ color: isSuccess ? '#67C23A' : '#f56c6c' }"
          >
            {{ isSuccess ? "激活成功!" : "点位不足，请购买点位！" }}
          </div>
        </div>
      </template>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleToPath" v-if="!isSuccess">
            查看点位
          </el-button>
          <el-button
            type="primary"
            @click="handleToPath"
            v-if="isSuccess && isShowTip"
          >
            点我去分配安装位置
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="ledgerManage">
import imgUpload from "@/components/ImageUpload";
import fileUpload from "@/components/FileUpload";
import ExportRes from "@/components/ExportRes";
import { useRoute, useRouter } from "vue-router";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import { downloadBlob, treeToArray, treeFindPath, timeFormat } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import { sm2Decrypt } from "@/utils/sm2encrypt";
import { nextTick } from "vue";
import { ElMessage } from "element-plus";
import { debounce } from "@/utils/debounce";
import {
  deviceMachineList,
  getPointInfo,
  deviceMachineActivate,
  deleteMachineCode,
} from "@/api/deviceControl";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const data = reactive({
  isShowTip: true,
  isSkip: false,
  currentPointNum: 0,
  pointNum: 0, // 点位上限数
  isSuccess: false,
  formRef: null,
  form: {
    selectedDevices: [],
  },
  positionList: [],
  positionTreeList: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  isExternalPark: false,
  installDialogVisible: false,
  confirmDialogVisible: false,
  activatingLoading: false,
  successDialogVisible: false,
  positionTreeList_disabled: [],
  total: 0,
  loading: false,
  nodeRef: null,
  positionNodeResultList: [
    {
      names: [],
      ids: [],
    },
  ],
  positionNodeList: [],
  repairRef: null,
  ledgerTable: null,
  userList: [],
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // 保存表格勾选的行数据
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  queryParams: {
    current: 1,
    size: 10,
  },
  rules: {},
});
const {
  isShowTip,
  currentPointNum,
  isSkip,
  pointNum,
  isSuccess,
  rules,
  formRef,
  form,
  positionList,
  positionTreeList,
  positionProps,
  isExternalPark,
  installDialogVisible,
  confirmDialogVisible,
  activatingLoading,
  successDialogVisible,
  positionTreeList_disabled,
  total,
  loading,
  nodeRef,
  positionNodeResultList,
  positionNodeList,
  ledgerTable,
  repairRef,
  userList,
  queryParams,
  repairForm,
  repairRules,
  tableData,
  tableAllSelectedId,
} = toRefs(data);

function handleSortDate(val) {
  if (queryParams.value.sort === "asc") {
    queryParams.value.sort = "desc";
  } else {
    queryParams.value.sort = "asc";
  }
  getList();
}

function disabledDate(time) {
  return time.getTime() > Date.now();
}

async function checkPoint(flag = false) {
  await getPointInfo({}).then((res) => {
    console.log("机构点位", res);
    if (res.code == 200) {
      const { total, current, efftiveCount } = res.data;
      currentPointNum.value = current;
      pointNum.value = total || 0;
      isSuccess.value = flag
        ? (efftiveCount || 0) > current
        : tableAllSelectedId.value.length <= (efftiveCount || 0) - current;
    }
  });
}

function handleToPath() {
  router.push(
    isSuccess.value ? `/deviceLedger/ledger` : `/deviceControl/monitor`
  );
}

function handleParkChange(event) {
  if (route.path === "/deviceLedger/ledger") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

onMounted(() => {
  initParkState();
  getPositionTreeList();
  getList();
  window.addEventListener("parkChange", handleParkChange);
});

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].machineCode === obj.machineCode) {
      return i;
    }
  }
  return -1;
}

/** 查询台账列表 */
function getList() {
  loading.value = true;
  getPointInfo({}).then((res) => {
    console.log("机构点位", res);
    if (res.code == 200) {
      const { total, current } = res.data;
      currentPointNum.value = current;
      pointNum.value = total || 0;
    }
  });
  deviceMachineList(data.queryParams)
    .then((res) => {
      if (res.code == 200) {
        const { records, total } = res.data;
        data.tableData =
          records.map((item) => {
            let arr = item.positionId?.split(",") || [];
            item.installAddress = item.installAddress || "";
            item.installAddressId = arr.length > 0 ? arr[arr.length - 1] * 1 : "";
            item.positionId = item.positionId || "";
            return item;
          }) || [];
        data.total = total || 0;
        console.log(data.tableData, "列表数据");
        nextTick(() => {
          data.tableData.forEach((item) => {
            if (data.tableAllSelectedId.indexOf(item.machineCode) > -1) {
              data.ledgerTable.toggleRowSelection(item, true);
            } else {
              data.ledgerTable.toggleRowSelection(item, false);
            }
          });
        });
      }
    })
    .finally(() => (loading.value = false));
  deviceMachineList({
    ...data.queryParams,
    current: 1,
    size: 9999999,
  }).then((res) => {
    console.log(res);
    data.tableData_all = res.data?.records || [];
  });
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    positionTreeList_disabled.value = addAttr(tree);
    positionList.value = treeToArray(response.data);
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加disabled属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  queryParams.value.beginTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[0]
    : "";
  queryParams.value.endTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[1]
    : "";
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.beginTime = "";
  queryParams.value.endTime = "";
  delete queryParams.value.sort;
  proxy.resetForm("queryRef");
  queryParams.value.size = 10;
  handleQuery();
}

/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      data.ledgerTable.setCurrentRow(null);
      data.ledgerTable.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.machineCode);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      data.ledgerTable.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    data.ledgerTable.setCurrentRow(row);
    data.ledgerTable.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.machineCode) === -1) {
      data.tableAllSelectedId.push(item.machineCode);
      data.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.machineCode);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = data.tableData;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.machineCode === a[0].machineCode) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.machineCode) === -1) {
        data.tableAllSelectedId.push(item.machineCode); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

/** 批量激活按钮操作 */
async function handleBatchActivate() {
  await checkPoint();

  if (tableAllSelectedId.value.length === 0) {
    proxy.$modal.msgError("请选择要激活的设备");
    return;
  }

  if (isSuccess.value) {
    form.value.selectedDevices = [
      ...data.tableAllSelectedRow.map((item) => {
        // item.installAddressId = "";
        // item.installAddress = "";
        // item.positionId = "";
        return item;
      }),
    ];
    installDialogVisible.value = true;
  } else {
    successDialogVisible.value = true;
  }
  // console.log(form.value.selectedDevices, "form.value.selectedDevices");
}

// 添加对话框按钮处理函数
function handleInstallCancel() {
  installDialogVisible.value = false;
  // form.value.selectedDevices = [];
}

// 处理跳过按钮
function handleInstallSkip() {
  installDialogVisible.value = false;
  confirmDialogVisible.value = true;
  isSkip.value = true;
  // console.log(form.value.selectedDevices, "form.value.selectedDevices");
}
// 处理下一步按钮
function handleInstallNext() {
  formRef.value.validate((valid) => {
    if (valid) {
      installDialogVisible.value = false;
      confirmDialogVisible.value = true;
      isSkip.value = false;
      // console.log(form.value.selectedDevices, "form.value.selectedDevices");
    }
  });
}

// 添加对应的方法
async function handleActivate(row) {
  console.log("激活设备", row);
  // row.installAddressId = "";
  // row.installAddress = "";
  // row.positionId = "";
  await checkPoint(true);
  if (isSuccess.value) {
    form.value.selectedDevices = [row];
    installDialogVisible.value = true;
  } else {
    successDialogVisible.value = true;
  }
}

function handleDelete(row) {
  console.log("删除设备", row);

  proxy.$modal
    .confirm(`是否确认删除机器码为"${row.machineCode}"的设备？`, {
      title: "提示",
      type: "warning",
    })
    .then(() => {
      deleteMachineCode({ machineCode: row.machineCode })
        .then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess("删除成功");
            getList();
          }
        })
        .catch((error) => {
          proxy.$modal.msgError("删除失败");
        });
    })
    .catch(() => {});
}

// 确认激活的处理函数
async function handleConfirmActivate() {
  activatingLoading.value = true;
  confirmDialogVisible.value = false;

  try {
    proxy.$modal.loading();
    // TODO: 替换实际的激活API调用
    let flag = false;
    let obj = {
      machineCodeDeviceVoList: form.value.selectedDevices.map((item) => {
        console.log(item);
        if (!item.positionId) flag = true;
        let obj = {
          machineCode: item.machineCode,
          positionId: item.positionId || "9999",
          position: item.installAddress || "默认位置",
        };
        // if (!isSkip.value) {
        //   obj.positionId = item.positionId;
        //   obj.position = item.installAddress;
        // }
        return obj;
      }),
    };
    console.log("激活传参", obj);
    const res = await deviceMachineActivate(obj);
    isShowTip.value = flag;
    // 显示成功对话框
    successDialogVisible.value = true;
    // // 重置状态
    form.value.selectedDevices = [];
    getList(); // 刷新列表
  } catch (error) {
    proxy.$modal.msgError("激活失败");
  } finally {
    proxy.$modal.closeLoading();
    activatingLoading.value = false;
  }
}

function handleConfirmCancel() {
  confirmDialogVisible.value = false;
  form.value.selectedDevices = [];
}

function handlePosition(val, row) {
  const positionResult = {
    ids: [],
    names: [],
  };
  positionResult.ids = treeFindPath(positionTreeList.value, (d) => d.id == val);
  const arr = positionResult.ids;
  for (let i = 0; i < arr.length; i++) {
    positionResult.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  row.installAddress = positionResult.names.join("-");
  row.installAddressId = val;
  row.positionId = positionResult.ids.join(",");
  console.log("positionResult ==> ", positionResult);
}
</script>
<style scoped lang="scss">
.ascending.active {
  border-bottom-color: #4095e5;
}
.descending.active {
  border-top-color: #4095e5;
}
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;
  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}

.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}

.dialog-footer {
  display: flex;
  justify-content: center;
  gap: 20px;
}

.success-dialog {
  :deep(.el-dialog__header) {
    display: none;
  }

  :deep(.el-dialog__body) {
    padding: 30px 20px;
  }
}

.success-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 20px;
}

.success-text {
  font-size: 20px;
  color: #67c23a;
}
</style>
