<!-- components/emergency/OrderBaseInfo.vue -->
<template>
  <div class="order-base-info">
    <!-- 工单详情表格 -->
    <div class="taskInfo-tit">工单详情</div>
    <table class="info-table">
      <tbody>
        <tr>
          <td class="label">应急工单编号</td>
          <td class="content">{{ workOrderData.orderId }}</td>
          <td class="label">应急预案</td>
          <td class="content">
            <slot name="emergencyPlan">
              {{ workOrderData.emergencyPlan || "-" }}
            </slot>
          </td>
        </tr>
        <tr>
          <td class="label">应急工单类别</td>
          <td class="content">
            {{ getOrderTypeName(workOrderData.orderType) }}
          </td>
          <td class="label">工单状态</td>
          <td class="content">{{ getStatusName(workOrderData.status) }}</td>
        </tr>
        <tr>
          <td class="label">应急工单描述</td>
          <td class="content description-cell" colspan="3">{{ workOrderData.description }}</td>
        </tr>
        <tr v-if="showReport">
          <td class="label">处理报告</td>
          <td class="content">
            <slot name="report">
              <template v-if="workOrderData?.reports?.length > 0">
                <div v-for="(report, index) in workOrderData.reports" :key="index" class="report-item">
                  <el-button 
                    type="primary" 
                    link
                    @click="downloadFile(report.fileUrl, report.fileName)"
                  >
                    {{ report.fileName }}
                  </el-button>
                </div>
              </template>
              <span v-else>-</span>
            </slot>
          </td>
          <td class="label">上传人</td>
          <td class="content">{{ workOrderData?.uploaderName || '-' }}</td>
        </tr>
        <tr v-if="showReport">
          <td class="label">应急事件等级</td>
          <td class="content" colspan="6">
            <el-select
              v-model="workOrderData.level"
              disabled
              style="width: 20%"
              placeholder="-"
            >
              <el-option
                v-for="item in levelOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup>
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'

const props = defineProps({
  workOrderData: {
    type: Object,
    default: () => ({
      reports: [],
      uploaderName: '-',
      level: '-'
    })
  },
  showReport: {
    type: Boolean,
    default: false
  },
  readonly: {
    type: Boolean,
    default: false
  },
  isProcessing: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:workOrderData', 'uploadReport'])

// 上传相关配置
const uploadUrl = '/api/upload' // 替换为实际的上传接口地址

// 上传前验证
const beforeUpload = (file) => {
  const isValidType = file.type === 'application/pdf' || 
                     file.type === 'application/msword' || 
                     file.type === 'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
  if (!isValidType) {
    ElMessage.error('只能上传PDF或Word文档!')
    return false
  }
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB!')
    return false
  }
  return true
}

// 上传成功回调
const handleUploadSuccess = (response) => {
  if (response.code === 200) {
    const newWorkOrderData = {
      ...props.workOrderData,
      report: response.data.fileName,
      reportFileUrl: response.data.fileUrl
    }
    emit('update:workOrderData', newWorkOrderData)
    ElMessage.success('处理报告上传成功')
  } else {
    ElMessage.error(response.msg || '上传失败')
  }
}

// 获取工单类型名称
const getOrderTypeName = (type) => {
  const typeMap = {
    equipment: "设备故障",
    system: "系统异常",
    safety: "安全事故",
    other: "其他",
  };
  return typeMap[type] || type;
};

// 获取工单状态名称
const getStatusName = (status) => {
  const statusMap = {
    pending: "待上传",
    waiting_upload: "待上传",
    waiting_archive: "待归档",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 添加删除报告的处理函数
const handleDeleteReport = () => {
  emit('update:work-order-data', {
    ...props.workOrderData,
    report: '-',
    reportFileUrl: ''
  })
}

// 添加下载文件方法
const downloadFile = (fileUrl, fileName) => {
  if (!fileUrl) {
    ElMessage.warning('文件不存在')
    return
  }

  try {
    // 确保使用HTTPS链接
    let downloadUrl = fileUrl
    if (downloadUrl.startsWith('http:')) {
      downloadUrl = downloadUrl.replace('http:', 'https:')
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement('a')
    link.style.display = 'none'
    link.href = downloadUrl
    link.setAttribute('download', fileName || '未命名文件')
    link.setAttribute('target', '_blank')
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
  } catch (error) {
    console.error('下载文件失败:', error)
    ElMessage.error('下载文件失败')
  }
}

// 添加等级选项常量
const levelOptions = [
  {
    label: '非常紧急',
    value: '非常紧急'
  },
  {
    label: '紧急',
    value: '紧急'
  },
  {
    label: '一般',
    value: '一般'
  },
  {
    label: '轻松',
    value: '轻松'
  }
];
</script>

<style scoped lang="scss">
.order-base-info {
  .taskInfo-tit {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;

      &.description-cell {
        white-space: pre-wrap;
        word-wrap: break-word;
        word-break: break-all;
        line-height: 1.5;
        min-height: 45px;
        height: auto;
        max-height: none;
      }
    }
  }
}

.report-upload {
  display: inline-block;
}

.delete-icon {
  margin-left: 8px;
  color: #909399;
  cursor: pointer;
  font-size: 14px;
  vertical-align: middle;

  &:hover {
    color: #f56c6c;
  }
}

.report-item {
  margin: 5px 0;
  &:first-child {
    margin-top: 0;
  }
  &:last-child {
    margin-bottom: 0;
  }
}
</style>