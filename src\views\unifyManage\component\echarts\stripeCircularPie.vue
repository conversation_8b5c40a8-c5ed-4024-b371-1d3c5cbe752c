<template>
  <div class="pieData">
    <div class="pieData-chart">
      <img
        class="pieData-bg"
        src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie6-bg.png"
      />
      <Echarts
        :ref="props.id + 'Ref'"
        :id="props.id"
        class="echart"
        width="100%"
        height="6vw"
        :fullOptions="props.option"
      />
    </div>
    <div class="pieData-list">
      <div class="addr" v-if="props.showNum">
        共入库<span>{{ props.num || 0 }}</span
        >个网站地址
      </div>
      <div
        class="pieData-item"
        :class="[`${props.id}-item`]"
        v-for="(item, index) in props.option.options.series[0].data"
        :data-index="index"
        :key="index"
      >
        <div class="pieData-name">
          <div
            :style="{
              backgroundColor: item.itemStyle.color,
              border: `1px solid ${item.itemStyle.borderColor}`,
            }"
            class="item-dot"
          >
            <i
              :style="{
                backgroundColor: item.itemStyle.borderColor,
              }"
            ></i>
          </div>
          {{ item.name }}
        </div>
        <div class="pieData-count">
          {{ item.value > 999 ? "999+" : item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Echarts from "@/components/Echarts/index.vue";
import { toRefs } from "vue";
import { fitChartSize, downloadBlob, timeFormat } from "@/utils";

const props = defineProps({
  num: {
    type: Number,
    default: 0,
  },
  showNum: {
    type: Boolean,
    default: false,
  },
  id: {
    type: String,
    default: "pieData",
  },
  option: {
    type: Object,
    default: {
      options: {
        series: [
          {
            type: "pie",
            radius: ["50%", "65%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            emphasis: {
              scaleSize: 3,
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            label: {
              show: false,
              position: "center",
              formatter: "{d|{d}%}\n{b|{b}}",
              rich: {
                d: {
                  color: "#fff",
                  fontSize: fitChartSize(14),
                  lineHeight: 14,
                  fontWeight: "bold",
                },
                b: {
                  color: "#D4EDFF",
                  fontSize: fitChartSize(12),
                },
              },
            },
            labelLine: {
              show: false,
              length: 2,
              length2: 5,
              lineStyle: {
                color: "rgba(81, 131, 255, .2)",
              },
            },
            data: [
              {
                name: "一年以内",
                value: 0,
                itemStyle: {
                  color: "rgba(0,164,255,0.3)",
                  borderColor: "#00a4ff",
                },
              },
              {
                name: "1-3年",
                value: 0,
                itemStyle: {
                  color: "rgba(0,204,3,0.3)",
                  borderColor: "#00cc03",
                },
              },
              {
                name: "3-5年",
                value: 0,
                itemStyle: {
                  color: "rgba(255,85,1,0.3)",
                  borderColor: "#ff5501",
                },
              },
              {
                name: "5年以上",
                value: 0,
                itemStyle: {
                  color: "rgba(255,255,0,0.3)",
                  borderColor: "#ffff00",
                },
              },
            ],
          },
        ],
      },
    },
  },
});

const state = reactive({
  pieData1Ref: null,
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData5Ref: null,
  pieData6Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData14Ref: null,
  pieData15Ref: null,
});
const {
  pieData1Ref,
  pieData2Ref,
  pieData3Ref,
  pieData4Ref,
  pieData5Ref,
  pieData6Ref,
  pieData7Ref,
  pieData8Ref,
  pieData9Ref,
  pieData10Ref,
  pieData11Ref,
  pieData12Ref,
  pieData13Ref,
  pieData14Ref,
  pieData15Ref,
} = toRefs(state);

const instance = getCurrentInstance();

const initPieFunc = () => {
  let timer = null;
  function waitForRef() {
    clearTimeout(timer);
    const echarts3DRef = instance?.proxy?.$refs[props.id + "Ref"];
    if (echarts3DRef) {
      document.querySelectorAll(`.${props.id}-item`).forEach((div) => {
        div.addEventListener("mouseover", function (e) {
          const index = parseInt(this.dataset.index); // 获取对应的数据索引
          echarts3DRef.dispatchAction({
            type: "highlight", // 触发高亮动作
            seriesIndex: 0, // 系列索引（第一个饼图）
            dataIndex: index, // 数据项索引
          });
        });

        div.addEventListener("mouseout", function () {
          const index = parseInt(this.dataset.index);
          echarts3DRef.dispatchAction({
            type: "downplay", // 取消高亮
            seriesIndex: 0,
            dataIndex: index,
          });
        });
      });
    } else {
      timer = setTimeout(waitForRef, 100);
    }
  }
  nextTick(waitForRef);
};

onBeforeUnmount(() => {
  if (state[`${props.id}Ref`]) {
    state[`${props.id}Ref`].unMountFunc();
  }
});

defineExpose({
  initPieFunc,
});
</script>

<style lang="scss" scoped>
.pieData {
  display: flex;
  align-items: center;
  margin-top: 0.5vw;
  &-chart {
    position: relative;
    flex: 1;
  }
  &-bg {
    position: absolute;
    top: 0.95vw;
    left: 0.82vw;
    width: 4.1vw;
    height: 4.1vw;
  }

  &-list {
    flex: 1;
    font-size: 0.55vw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.2vw 0;
    .addr {
      padding: 0.4vw 0;
      text-align: center;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/admin/addr-bg.png");
      background-size: 100% 100%;
      font-size: 0.45vw;
      span {
        color: #0bf9fe;
        font-size: 0.55vw;
        padding: 0 0.1vw;
      }
    }
  }
  &-item {
    cursor: pointer;
    // border: 1px solid red;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 1vw;
    line-height: 1.1vw;
    border-width: 1px;
    border-style: solid;
    padding: 0.4vw 0.4vw;
    border-image-source: radial-gradient(
      50% 50% at 50% 100%,
      #c7d6ff4d 0%,
      #c7d3ff00 100%
    );
    border-image-slice: 1;
    background: radial-gradient(
      100% 175.73% at 0% 50%,
      #77adff40 0%,
      #002cc700 100%
    );
    border-right: none;
  }
  &-name {
    display: flex;
    align-items: center;
    gap: 0 0.2vw;
    color: #d4deffff;
    .item-dot {
      width: 0.4vw;
      height: 0.4vw;
      display: flex;
      align-items: center;
      justify-content: center;
      i {
        display: flex;
        width: 0.1vw;
        height: 0.1vw;
      }
    }
  }
}
</style>