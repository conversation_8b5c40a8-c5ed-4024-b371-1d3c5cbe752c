<template>
  <div class="consult app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">咨询单</div>
      </template>
      <div class="consult-main">
        <div class="consult-main_search search-list">
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="consultNo">
              <el-input
                v-model="queryParams.consultNo"
                placeholder="请输入咨询单编号"
                style="width: 200px"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="responsible">
              <el-input
                v-model="queryParams.responsible"
                placeholder="请输入项目负责人"
                style="width: 200px"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="deptId">
              <el-tree-select
                v-model="queryParams.deptId"
                :props="deptProps"
                :data="deptList"
                placeholder="请选择项目归属部门"
                check-strictly
                :render-after-expand="false"
                :expand-on-click-node="false"
                style="width: 200px"
                clearable
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>

        <div class="consult-main_table">
          <el-table ref="tableRef" v-loading="loading" :data="tableList" border>
            <el-table-column
              label="咨询单编号"
              align="center"
              minWidth="120px"
              prop="consultNo"
              show-overflow-tooltip
            />
            <el-table-column
              label="咨询单类别"
              align="center"
              minWidth="120px"
              prop="consultTypeName"
              show-overflow-tooltip
            />

            <el-table-column
              label="咨询内容"
              align="center"
              minWidth="180px"
              prop="consultContent"
              show-overflow-tooltip
            />
            <el-table-column
              label="归属项目"
              align="center"
              minWidth="150px"
              prop="projectName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.projectName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="归属部门"
              align="center"
              minWidth="120px"
              prop="deptName"
              show-overflow-tooltip
            />
            <el-table-column
              label="项目负责人"
              align="center"
              minWidth="100px"
              prop="responsibleName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.responsibleName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              minWidth="160px"
              prop="createdTime"
              show-overflow-tooltip
            />
            <el-table-column
              label="回复时间"
              align="center"
              minWidth="160px"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.updatedTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" minWidth="100px">
              <template #default="scope">
                <el-tag
                  effect="dark"
                  :type="statusList[scope.row.replyStatus].type"
                  >{{ statusList[scope.row.replyStatus].label }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="230"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  icon="Search"
                  type="primary"
                  @click="handleCheck(scope.row, 0)"
                  >查看</el-button
                >
                <el-button
                  v-if="scope.row.replyStatus == 0"
                  link
                  icon="Edit"
                  type="warning"
                  @click="handleCheck(scope.row, 1)"
                  >处理</el-button
                >
                <el-button
                  v-if="scope.row.replyStatus == 0"
                  link
                  icon="Delete"
                  type="danger"
                  @click="handleDel(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="consult">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import { useRouter } from "vue-router";
import { getConsultPage, getConsultTypeList, delConsult } from "@/api/order";
import { getUserByRole } from "@/api/system/user";
import { listTree } from "@/api/system/distribution";

const { proxy } = getCurrentInstance();
const router = useRouter();
const consultRef = ref(null);
const userStore = useUserStore(); // 获取当前登录的用户信息
const state = reactive({
  total: 0,
  curConsultIndex: -1,
  isDisabled: false,
  loading: false,
  dialogVisible: false,
  // 根据条件筛选后显示的数据
  tableList: [],
  deptList: [],
  userList: [],
  typeList: [
    { label: "技术支持", value: "技术支持" },
    { label: "物流查询", value: "物流查询" },
    { label: "财务咨询", value: "财务咨询" },
    { label: "售后服务", value: "售后服务" },
  ],
  statusList: [
    { label: "未回复", value: "未回复", type: "danger" },
    { label: "已回复", value: "已回复", type: "success" },
  ],
  // 列表条件筛选字段
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    consultNo: "",
    deptId: "",
    responsible: "",
  },
  // 详情弹窗的某条数据详情
  consultInfo: {
    consultContent: "",
    consultNo: "",
    consultTime: "",
    consultType: "",
    consultSubmit: "",
    answerStatus: "",
  },
  deptProps: {
    label: "deptName",
    children: "children",
    value: "deptId",
  },
});

const {
  total,
  deptList,
  deptProps,
  userList,
  curConsultIndex,
  isDisabled,
  tableList_all,
  consultInfo,
  recordList,
  tableList,
  typeList,
  statusList,
  dialogVisible,
  queryParams,
  loading,
} = toRefs(state);

/** 获取运维人员列表 */
function getUserList() {
  getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["maintain", "maintainManage"],
  }).then((resp) => {
    console.log("运维人员", resp);
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

/** 获取部门列表 */
function getDeptList() {
  listTree().then((resp) => {
    console.log("部门树", resp);
    if (resp.data) {
      state.deptList = resp.data;
    }
  });
}

/** 列表的详情按钮 */
function handleCheck(item, type) {
  router.push({
    path: "consultInfo",
    query: { id: item.consultId, type },
  });
}

function handleDel(item) {
  proxy.$modal
    .confirm(`确定删除编号为${item.consultNo}的咨询单信息？`)
    .then(() => {
      delConsult({ consultId: item.consultId }).then((resp) => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      });
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

function getList() {
  state.loading = true;
  getConsultPage(state.queryParams)
    .then((resp) => {
      if (resp.data) {
        const { rows = [], total = 0 } = resp.data;
        state.tableList = rows;
        state.total = total;
        state.loading = false;
      }
    })
    .catch(() => (state.loading = false));
}

onMounted(() => {
  getUserList();
  getList();
  getDeptList();
});
</script>

<style lang="scss" scoped>
.consult {
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ddd;
    margin-right: 0;
    padding-bottom: 20px;
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    .left {
      padding-left: 3.5vw;
      position: relative;
      color: #8d8d8d;
      &::before {
        position: absolute;
        top: 0;
        left: 0;
        width: 3.3vw;
        height: 3.3vw;
        content: "";
        background: url("@/assets/icons/icon_bigFile.png");
        background-size: 100% 100%;
      }
      div {
        color: #333;
        font-size: 1.3vw;
      }
    }
    .right {
      text-align: center;
      div {
        font-weight: bold;
        font-size: 1.3vw;
        color: #4faaff;
        margin-bottom: 0.2vw;
      }
    }
  }
  &-main {
    &_record {
      .title {
        padding: 2vw 0 0.3vw;
        font-weight: bold;
      }
      &-item {
        margin-top: 0.5vw;
      }
    }
  }
  .dialog-header {
    border-bottom: 1px solid #ddd;
  }
}
</style>