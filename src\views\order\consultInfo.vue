<template>
  <div class="consultInfo app-container">
    <el-card shadow="never" class="page-card" v-loading="loading">
      <template #header>
        <div class="card-header page-header">咨询单详情</div>
      </template>
      <el-descriptions title="" border>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="咨询单编号"
        >
          {{ consultInfo.consultNo }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="咨询单类别"
        >
          <span>{{ consultInfo.consultTypeName }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="归属部门"
        >
          <span>{{ consultInfo.deptName || '-' }}</span>
        </el-descriptions-item>

        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="归属项目"
        >
          <span>{{ consultInfo.projectName || '-' }}</span>
        </el-descriptions-item>

        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          <span>{{ consultInfo.createdTime }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="处理时间"
        >
          <span>{{ consultInfo.replyTime || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          :span="3"
          label-class-name="label-width"
          class-name="value-width"
          label="项目负责人"
        >
          <span>{{ consultInfo.responsibleName || '-' }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="咨询内容"
        >
          <span>{{ consultInfo.consultContent }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <el-form
        ref="consultRef"
        class="consultInfo-form"
        :model="consultInfo"
        :rules="rules"
        v-show="isEdit || consultInfo.replyStatus == 1"
      >
        <el-form-item label="回复内容" prop="replyContent">
          <editor
            v-model="consultInfo.replyContent"
            :min-height="200"
            type="url"
            @update:modelValue="contentChange"
            @blur="consultRef.validateField('replyContent')"
            :isEdit="isEdit && !isHandled"
          />
          <div
            v-show="isEdit"
            style="position: absolute; bottom: -30px; right: 10px"
          >
            共<span :style="{ color: contentTotal > 500 ? '#f56c6c' : '' }">{{
              ` ${contentTotal} `
            }}</span
            >字
          </div>
        </el-form-item>
      </el-form>

      <div class="consultInfo-btns">
        <el-button type="primary" @click="handleEdit" v-if="!isHandled" v-throttle>{{
          !isEdit ? "处理" : "回复"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import { getConsultInfo, updateConsult } from "@/api/order";

const { proxy } = getCurrentInstance();
const route = useRoute();

const state = reactive({
  contentTotal: 0,
  isHandled: false,
  loading: false,
  consultRef: null,
  isEdit: false,
  userList: [{ userName: "张三", id: 1 }],
  deptList: [{ deptName: "部门1", id: 1 }],
  consultInfo: {
    consultId: "",
    consultNo: "",
    consultType: 1,
    consultTypeName: "",
    consultContent: "",
    projectId: "",
    projectName: "",
    responsibleName: "",
    deptId: "",
    deptName: "",
    replyStatus: 0,
    replyBy: "",
    replyByName: "",
    replyTime: "",
    replyContent: "",
    isDelete: 1,
    createdTime: "",
    replyTime: "",
  },
  rules: {
    replyContent: [
      {
        required: true,
        message: "回复内容不能为空",
        trigger: "blur",
      },
    ],
  },
});

const {
  isHandled,
  loading,
  contentTotal,
  rules,
  consultRef,
  consultInfo,
  userList,
  deptList,
  isEdit,
} = toRefs(state);

// 统计活动内容纯文本字数
function contentChange(val) {
  // console.log("纯文本", val);
  // infoForm.value.content = infoForm.value.content.subString(0, 500)
  contentTotal.value = val
    .replace(/<[^>]+>/g, "", "")
    .replace(/\s/g, "").length;
}

function handleEdit() {
  if (state.isEdit) {
    state.consultRef.validate((valid) => {
      if (valid) {
        proxy.$modal.loading();
        updateConsult(state.consultInfo)
          .then((res) => {
            proxy.$modal.msgSuccess("回复成功");
            state.isEdit = !state.isEdit;
            getData();
            proxy.$modal.closeLoading();
          })
          .catch(() => proxy.$modal.closeLoading());
      }
    });
  } else {
    state.isEdit = !state.isEdit;
  }
}

function getData() {
  if (route.query.id) {
    state.loading = true;
    getConsultInfo(route.query.id)
      .then((resp) => {
        console.log("咨询单详情", resp);
        if (resp.data) {
          state.consultInfo = resp.data;
          state.isHandled = resp.data.replyStatus == 1;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
}

function handleBack() {
  proxy.$tab.closeOpenPage("consult");
}

onMounted(() => {
  state.isEdit = route.query.type == 1;
  getData();
});
</script>

<style lang="scss" scoped>
.consultInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-form {
    margin-top: 20px;
  }
  &-btns {
    padding: 50px 0;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}
</style>