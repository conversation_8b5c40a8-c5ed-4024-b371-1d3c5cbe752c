<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="fromWorkbench" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <div
        class="monitor"
        :style="{ paddingBottom: isAllQuery ? '20px' : '0px' }"
      >
        <div class="monitor-position">
          <div class="monitor-title">安装位置筛选</div>
          <el-scrollbar :max-height="500">
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              show-checkbox
              node-key="id"
              expand-on-click-node
              default-expand-all
              :props="positionProps"
              @check="treeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div>
        <div class="monitor-list">
          <div class="monitor-statics">
            <div class="monitor-statics_item" @click="handleAllQuery(0)">
              <div>智能终端总数</div>
              {{ ledgerTotal }}台
            </div>
            <div class="monitor-statics_item" @click="handleAllQuery(1)">
              <div>异常终端总数（点击处理）</div>
              {{ ledgerErrorTotal }}台
            </div>
          </div>
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            @submit.native.prevent
          >
            <el-form-item label="" prop="deviceCode">
              <el-input
                v-model="queryParams.deviceCode"
                placeholder="请输入设备编号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="deviceName">
              <el-input
                v-model="queryParams.deviceName"
                placeholder="请输入设备名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="deviceStatus">
              <el-select
                v-model="queryParams.deviceStatus"
                placeholder="请选择设备状态"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择运行状态"
                @change="handleQuery"
                multiple
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in runList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="12" class="mb12">
            <el-col :span="4.5">
              <el-select
                v-model="operationParams.remoteSend"
                placeholder="请选择远程传输操作"
                @change="handleOpeartionRemoteSend"
                :disabled="tableAllSelectedId.length < 1"
                style="width: 200px"
              >
                <el-option
                  v-for="item in remoteSendList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-col>
            <el-col :span="1.5">
              <el-button
                color="#626aef"
                plain
                icon="Setting"
                @click="handleLockRule"
                >锁屏规则设置</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Setting"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleRepair"
                >批量报修</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Download"
                @click="open2 = true"
                >生成设备激活码</el-button
              >
            </el-col>
          </el-row>

          <el-table
            ref="ledgerTable"
            v-loading="loading"
            :data="ledgerList"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
          >
            <el-table-column
              type="selection"
              width="55"
              align="center"
              fixed="left"
            />
            <el-table-column
              label="运行状态"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div
                  v-if="runList[scope.row.runStatusList[0]]"
                  style="
                    display: flex;
                    flex-wrap: wrap;
                    gap: 5px;
                    justify-content: center;
                  "
                >
                  <el-tag
                    v-for="(item, index) in scope.row.runStatusList"
                    :key="index"
                    effect="dark"
                    :type="runList[item].type"
                    >{{ runList[item].label }}</el-tag
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="设备编码"
              align="center"
              show-overflow-tooltip
              minWidth="150px"
              prop="deviceCodeStr"
            />
            <el-table-column
              label="设备名称"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="deviceNameStr"
            />
            <el-table-column
              label="设备类型"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="deviceTypeStr"
            />
            <el-table-column
              label="状态"
              align="center"
              show-overflow-tooltip
              minWidth="100px"
            >
              <template #default="scope">
                <el-tag
                  v-if="statusObj[scope.row.deviceStatus]"
                  effect="dark"
                  :type="statusObj[scope.row.deviceStatus].type"
                  >{{ statusObj[scope.row.deviceStatus].label }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="安装位置"
              align="center"
              show-overflow-tooltip
              minWidth="120px"
              prop="installAddress"
            />
            <el-table-column
              label="设备图片"
              align="center"
              show-overflow-tooltip
              minWidth="110px"
            >
              <template #default="scope">
                <el-image
                  style="
                    width: 50px;
                    height: 30px;
                    display: block;
                    margin: 0 auto;
                  "
                  :src="scope.row.deviceImg"
                  fit="contain"
                />
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="180"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <div
                  style="
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: center;
                    margin-bottom: 10px;
                  "
                >
                  <el-tooltip content="详情" placement="top">
                    <el-button
                      link
                      type="success"
                      icon="Search"
                      @click.stop="handleCheck(scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="关机" placement="top">
                    <el-button
                      link
                      type="danger"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SwitchButton"
                      @click.stop="handleBatchRemote(0, 0, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="重启" placement="top">
                    <el-button
                      link
                      type="primary"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="RefreshRight"
                      @click.stop="handleBatchRemote(0, 1, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="定时关机" placement="top">
                    <el-button
                      link
                      type="warning"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="AlarmClock"
                      @click.stop="
                        isBatch = false;
                        handleFixed(scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="开启WIFI" placement="top">
                    <el-button
                      link
                      type="success"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SortUp"
                      @click.stop="handleBatchWifi(0, 1, scope.row)"
                    ></el-button>
                  </el-tooltip>
                </div>
                <div
                  style="
                    display: flex;
                    flex-wrap: nowrap;
                    justify-content: center;
                  "
                >
                  <el-tooltip content="关闭WIFI" placement="top">
                    <el-button
                      link
                      type="info"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="SortDown"
                      @click.stop="handleBatchWifi(0, 0, scope.row)"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="清除缓存" placement="top">
                    <el-button
                      link
                      type="danger"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Brush"
                      @click.stop="
                        isBatch = false;
                        handleBatchClear(0, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="锁屏" placement="top">
                    <el-button
                      link
                      type="warning"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Lock"
                      @click.stop="
                        isBatch = false;
                        handleBatchLock(0, 1, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="解锁" placement="top">
                    <el-button
                      link
                      type="primary"
                      :disabled="scope.row.runStatus[0] == 0"
                      icon="Unlock"
                      @click.stop="
                        isBatch = false;
                        handleBatchLock(0, 0, scope.row);
                      "
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip content="报修" placement="top">
                    <el-button
                      link
                      type="success"
                      icon="Setting"
                      :disabled="scope.row.deviceStatus > 2"
                      @click.stop="handleRepair(scope.row)"
                    ></el-button>
                  </el-tooltip>
                </div>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            class="pagination-container"
            v-show="total > 0 && !isAllQuery"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            :pageSizes="[5, 10, 20, 30, 50]"
            @pagination="getList"
          />
        </div>
      </div>
      <div class="monitor-bottom">
        <div class="monitor-btns">
          <el-button
            v-for="item in remoteList"
            :key="item.value"
            :type="item.type"
            :icon="item.icon"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleOpeartionRemote(item.value)"
            >{{ item.label }}</el-button
          >
        </div>
      </div>
    </el-card>

    <!-- 定时关机 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="fixRef" :model="fixForm" :rules="rules" label-width="100px">
        <el-form-item label="关机时间" prop="time">
          <el-date-picker
            v-model="fixForm.time"
            type="datetime"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            placeholder="请选择关机时间"
            :disabled-date="disabledDateFn"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="checkFixTime" v-throttle
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写报修单 -->
    <el-dialog
      class="custom-dialog"
      title="报修单填写"
      v-model="showRepair"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
      align-center
    >
      <el-table
        :data="repairList"
        border
        style="margin: 0 auto 40px; width: 96%"
        max-height="300"
      >
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceCode"
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="modelStr"
        />
        <el-table-column
          label="设备图片"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="contain"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="installAddress"
        />
        <el-table-column
          label="物理地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="macAddress"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="logicAddress"
        />
        <el-table-column
          label="IP地址"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="ipAddress"
        />
        <el-table-column
          label="操作系统"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="osVersion"
        />
        <el-table-column
          label="CPU"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="cpu"
        />
        <el-table-column
          label="品牌"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="brand"
        />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="internalStorage"
        />
        <el-table-column
          label="硬盘"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="disk"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
          prop="putTime"
        />
      </el-table>

      <el-form
        ref="repairRef"
        :model="repairForm"
        :rules="repairRules"
        label-width="100px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="报障人" prop="userId">
              <el-select
                v-model="repairForm.userId"
                placeholder="请选择报障人"
                filterable
                clearable
                style="width: 100%"
                @change="repairUserChange"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="报障人联系方式"
              prop="repairPhone"
              label-width="140"
            >
              <el-input
                v-model="repairForm.repairPhone"
                placeholder="请输入报障人联系方式"
                style="width: 94%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障描述" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                placeholder="请输入报障描述"
                style="width: 98%"
                maxlength="200"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="能否正常使用" prop="isNormal">
              <el-select
                v-model="repairForm.isNormal"
                filterable
                clearable
                style="width: 100%"
              >
                <el-option label="能正常使用" :value="0" />
                <el-option label="不能正常使用" :value="1" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障附件" prop="annexUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    repairForm.annexUrl = url;
                  }
                "
                :fileSize="10"
                :fileType="['txt', 'doc', 'docx', 'xls', 'xlsx', 'pdf']"
                :type="4"
                :isWorkOrderAnnex="true"
                :limit="1"
                :modelValue="repairForm.annexUrl"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="报障图片" prop="images">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    repairForm.images = url;
                  }
                "
                :uploadType="4"
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报障时间" prop="repairTime">
              <el-date-picker
                type="datetime"
                v-model="repairForm.repairTime"
                placeholder="请选择报障时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                :disabled-date="disabledDateFn"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRepairForm" v-throttle
            >确 定</el-button
          >
          <el-button @click="repairCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 生成激活码 -->
    <el-dialog
      class="custom-dialog"
      top="30vh"
      v-model="open2"
      width="400px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      @close="handleCancel"
      align-center
    >
      <template #header>
        <div class="dialog-header">
          请选择安装位置
          <el-icon :size="22" style="margin-left: 5px"
            ><QuestionFilled
          /></el-icon>
          <span @click="handleToAdd">没有安装位置？点我新建</span>
        </div>
      </template>
      <div>
        <el-scrollbar height="400px">
          <el-tree
            ref="nodeRef"
            style="max-width: 600px"
            :data="positionNodeList"
            node-key="id"
            :props="positionProps"
            show-checkbox
            check-on-click-node
            expand-on-click-node
            check-strictly
            default-expand-all
          />
        </el-scrollbar>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDownload" v-throttle
            >点击生成激活码</el-button
          >
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 发布消息 -->
    <el-dialog
      class="custom-dialog"
      top="30vh"
      title="发布消息"
      v-model="messageOpen"
      width="600px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      align-center
      @close="handleCancelMessage"
    >
      <el-form :model="messageForm" ref="messageRef" :rules="messageRules">
        <el-form-item label="消息类型" prop="isTop">
          <el-radio-group v-model="messageForm.isTop">
            <el-radio label="日常通知（非应用置顶）" :value="false" />
            <el-radio label="霸屏展示（应用置顶）" :value="true" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="是否定时通知" prop="isNotice">
          <el-switch v-model="messageForm.isNotice"></el-switch>
          <el-date-picker
            v-if="messageForm.isNotice"
            v-model="messageForm.noticeTime"
            value-format="YYYY-MM-DD HH:mm:ss"
            format="YYYY-MM-DD HH:mm:ss"
            type="datetime"
            :disabled-date="disabledDateFn"
            clearable
            style="width: 250px; margin-left: 20px"
            placeholder="请选择定时通知时间"
          />
        </el-form-item>
        <el-form-item label="持续时间" prop="lastTimeNum">
          <el-input-number
            v-model="messageForm.lastTimeNum"
            :min="1"
            :max="9999"
            :value-on-clear="1"
            style="width: 160px; margin-right: 10px"
            @change="timeChange"
          ></el-input-number>
          <el-radio-group
            v-model="messageForm.lastTimeUnit"
            @change="timeChange"
          >
            <el-radio label="秒" :value="1" />
            <el-radio label="分钟" :value="2" />
            <el-radio label="小时" :value="3" />
            <el-radio label="天" :value="4" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="轮播秒数" prop="second">
          <el-input-number
            v-model="messageForm.second"
            :min="1"
            :max="limitSecond"
            :value-on-clear="1"
            style="width: 160px; margin-right: 10px"
          ></el-input-number
          >秒
        </el-form-item>
        <el-form-item label="消息内容" prop="message">
          <el-input
            type="textarea"
            v-model="messageForm.message"
            maxlength="50"
            placeholder="请输入消息内容"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleMessage" v-throttle
            >发布</el-button
          >
          <el-button @click="handleCancelMessage">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 文件分发 -->
    <el-dialog
      class="custom-dialog"
      top="30vh"
      title="文件分发"
      v-model="fileOpen"
      width="500px"
      :close-on-click-modal="false"
      :close-on-click-escape="false"
      align-center
      @close="handleFileSendCancel"
    >
      <el-form
        v-loading="uploading"
        :model="fileForm"
        ref="fileRef"
        :rules="fileRules"
      >
        <el-form-item label="文件说明" prop="fileDescription">
          <el-input
            v-model="fileForm.fileDescription"
            placeholder="请输入文件说明"
            type="textarea"
            :rows="1"
            autocomplete="off"
            maxlength="50"
            clearable
          />
        </el-form-item>
        <el-form-item label="选择文件" prop="uploadName" class="fileclass">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :headers="headers"
            action="#"
            :limit="1"
            :auto-upload="false"
            :before-upload="beforeUpload"
            :on-change="changeFile"
            :on-exceed="handleExceed"
            :show-file-list="false"
            :http-request="httpRequestFn"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
          </el-upload>
          <div>{{ fileForm.uploadName }}</div>
        </el-form-item>
        <el-form-item
          label="文件指定传输位置"
          prop="filePath"
          label-position="top"
        >
          <el-radio-group
            v-model="fileForm.type"
            @change="fileRef.validateField('filePath')"
          >
            <el-radio label="桌面" :value="1" />
            <el-radio label="系统盘" :value="2" />
            <el-radio label="自由路径" :value="0" />
          </el-radio-group>
          <el-input
            v-if="fileForm.type < 1"
            v-model="fileForm.filePath"
            placeholder="请输入系统传输绝对路径，如 C:\filePath\file123"
            autocomplete="off"
            style="width: 100%"
            type="textarea"
            :rows="1"
            maxlength="50"
          />
          <el-input
            v-else
            v-model="fileForm.filePath"
            placeholder="若有追加路径，请输入追加的路径参数，如 filePath\file123"
            autocomplete="off"
            style="width: 100%"
            type="textarea"
            :rows="1"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div
          style="
            display: flex;
            justify-content: space-between;
            align-items: center;
          "
        >
          <div>
            <div v-if="uploading">
              <el-progress
                :percentage="progress"
                :status="progress == 100 ? 'success' : ''"
                :format="format"
              />
            </div>
          </div>
          <div class="dialog-footer">
            <el-button
              type="primary"
              @click="handleFileSend"
              :disabled="uploading"
              v-throttle
              >{{
                uploading
                  ? progress < 100
                    ? "上传中..."
                    : "发送中..."
                  : "发送"
              }}</el-button
            >
            <el-button v-if="uploading" @click="handleFileSendCancel"
              >终止</el-button
            >
            <el-button @click="handleCancelFile" v-if="!uploading"
              >返回</el-button
            >
          </div>
        </div>
      </template>
    </el-dialog>

    <!-- 锁屏规则设置-->
    <el-dialog
      class="custom-dialog"
      title="锁屏规则设置"
      v-model="ruleOpen"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-form :model="lockRuleForm" ref="lockRuleRef" label-position="top">
        <el-form-item label="自动锁定规则设置" prop="lockTime">
          <div style="display: flex; align-items: center; gap: 0 10px">
            检测到设备无操作
            <el-input-number
              v-model="lockRuleForm.lockTime"
              :max="99999"
              :min="1"
            />
            <el-select v-model="lockRuleForm.unit" style="width: 100px">
              <el-option label="秒" :value="0" />
              <el-option label="分钟" :value="1" />
              <el-option label="小时" :value="2" />
              <el-option label="天" :value="3" />
            </el-select>
            后自动锁定
          </div>
        </el-form-item>
        <el-form-item label="自动解锁规则设置" prop="time">
          <el-time-picker
            v-model="lockRuleForm.time"
            is-range
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            format="HH:mm:ss"
            value-format="HH:mm:ss"
            style="margin-bottom: 5px"
          />
          此时间段内所有设备不会被自动锁定，但可被手动锁定
        </el-form-item>
        <el-form-item label="锁定屏保图片" prop="lockImg">
          <div style="width: 100%">
            <el-radio-group v-model="lockRuleForm.lockImg">
              <el-radio label="默认" :value="0" />
              <el-radio label="自定义" :value="1" />
            </el-radio-group>
          </div>
          <imgUpload
            v-if="lockRuleForm.lockImg == 1"
            @update:modelValue="
              (url) => {
                lockRuleForm.images = url;
              }
            "
            :imgWidth="3840"
            :uploadType="4"
            :limit="1"
            :modelValue="lockRuleForm.images"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleRule">保存</el-button>
          <el-button @click="handleCancelRule">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
  
  <script setup name="Monitor">
import imgUpload from "@/components/ImageUpload";
import fileUpload from "@/components/FileUpload";
import {
  ref,
  onBeforeUnmount,
  onActivated,
  onMounted,
  nextTick,
  onDeactivated,
} from "vue";
import { ElLoading } from "element-plus";
import { useRoute, useRouter } from "vue-router";
import {
  devicePage,
  deviceInfo,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import { getUserList, getUserByRole } from "@/api/system/user";
import { getDeviceType } from "@/api/mediaTeach/type";
import { tenantTree as getTenantTree } from "@/api/park";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import {
  treeToArray,
  treeFindPath,
  timeFormat,
  downloadBlob,
  isValidWindowsAbsolutePath,
  isValidRelativePath,
} from "@/utils";
import {
  checkSupportsCrypto,
  generateRequestId,
  getCurrentTime,
} from "@/utils/sliceUpload";
import axios from "axios";
import useUserStore from "@/store/modules/user";
import { getToken } from "@/utils/auth";
import {
  addSchoolDeviceLog,
  deviceStartByWOL,
  deviceCtlMqtt,
  checkDeviceCode,
  sendMessage,
} from "@/api/deviceControl";
import { ElMessage, genFileId } from "element-plus";

const router = useRouter();
axios.defaults.timeout = 5 * 60 * 1000;

const route = useRoute();
const { proxy } = getCurrentInstance();
// 添加一个响应式变量来记录是否从工作台跳转而来
const fromWorkbench = ref(false);
const format = (percentage) => (percentage === 100 ? "" : `${percentage}%`);
const BASEURL = import.meta.env.VITE_APP_BASE_API;
const state = reactive({
  ledgerTable: null,
  ledgerList: [],
  repairList: [],
  loadingPage: null,
  open: false,
  open2: false,
  open3: false,
  loading: false,
  showSearch: true,
  showRepair: false,
  isBatch: false,
  ips: [],
  statusList: [
    { label: "正常", value: 0, type: "success" },
    { label: "报障中", value: 2, type: "danger" },
    { label: "维修中", value: 1, type: "danger" },
  ],
  runList: [
    { label: "关机", value: 0, type: "info" },
    { label: "运行良好", value: 1, type: "success" },
    { label: "网速过慢", value: 2, type: "warning" },
    { label: "GPU过高", value: 3, type: "danger" },
    { label: "磁盘占用率过高", value: 4, type: "danger" },
    { label: "CPU过高", value: 5, type: "danger" },
    { label: "内存过高", value: 6, type: "danger" },
  ],
  tagList: [],
  positionTreeList: [],
  positionList: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
  },
  queryPositionResult: {
    names: [],
    ids: [],
  },
  typeList: [],
  tenantTree: [],
  title: "",
  total: 0,
  actionUrl: "",
  headers: {
    Authorization: "Bearer " + getToken(),
    "Content-Type": "multipart/form-data",
  },
  headers2: { Authorization: "Bearer " + getToken() },
  isAllQuery: false,
  ruleOpen: false,
  lockRuleForm: {
    lockTime: 1,
    unit: "秒",
    time: [],
    lockImg: 0,
  },
  lockRuleRef: null,
  limitSecond: 1,
  fixRef: null,
  abortController: null,
  uploading: false,
  progress: 0,
  installOpen: false,
  installRef: null,
  installTitle: "软件安装",
  uploadRef: null,
  fileRef: null,
  messageRef: null,
  fileOpen: false,
  messageOpen: false,
  messageForm: {
    isTop: false,
    isNotice: false,
    noticeTime: "",
    lastTimeNum: 1,
    lastTimeUnit: 1,
    second: 1,
    message: "",
  },
  fileForm: {
    fileDescription: "",
    uploadName: "",
    filePath: "",
    type: 0,
  },
  positionNodeResultList: [
    {
      names: [],
      ids: [],
    },
  ],
  positionNodeList: [],
  nodeRef: null,
  remoteList: [
    { icon: "SwitchButton", type: "danger", label: "远程关机", value: 0 },
    { icon: "Monitor", type: "success", label: "远程开机", value: 1 },
    { icon: "RefreshRight", type: "primary", label: "远程重启", value: 2 },
    { icon: "AlarmClock", type: "warning", label: "远程定时关机", value: 3 },
    { icon: "SortUp", type: "success", label: "远程开启wifi", value: 4 },
    { icon: "SortDown", type: "info", label: "远程关闭wifi", value: 5 },
    { icon: "Brush", type: "danger", label: "远程清除缓存", value: 6 },
    { icon: "Lock", type: "warning", label: "锁屏", value: 7 },
    { icon: "Unlock", type: "primary", label: "解锁", value: 8 },
  ],
  remoteSendList: [
    { label: "发布消息", value: 0 },
    { label: "文件分发", value: 1 },
  ],
  operationParams: {
    remote: "",
    remoteSend: "",
  },
  userList: [],
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // ��存表格勾选的行数��
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  ledgerTotal: 0,
  ledgerErrorTotal: 0,
  statusObj: {
    0: {
      label: "正常",
      type: "success",
    },
    1: {
      label: "维修中",
      type: "danger",
    },
    2: {
      label: "报障中",
      type: "warning",
    },
    3: {
      label: "待审核",
      type: "primary",
    },
    4: {
      label: "已报废",
      type: "info",
    },
  },
  fixForm: {
    deviceCode: "",
    time: "",
  },
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  },
  queryParams: {
    smartScreen: 1,
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 5,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: "",
    deviceStatus: "",
    positionIds: [],
    putTime: "",
    status: [],
  },
  rules: {
    time: [
      {
        required: true,
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
  },
  repairForm: {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  },
  messageRules: {
    message: [
      {
        required: true,
        message: "消息内容不能为空",
        trigger: "blur",
      },
    ],
    isNotice: [
      {
        validator: validateNotice,
        trigger: ["change", "blur"],
      },
    ],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报修人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      { required: true, message: "报修人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    isNormal: [
      {
        required: true,
        message: "请选择设备能否正常使用",
        trigger: ["blur", "change"],
      },
    ],
    remark: [
      { required: true, message: "故障描述不能为空", trigger: "blur" },
      { max: 200, message: "故障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        message: "故障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
  },
  fileRules: {
    filePath: [
      {
        validator: validatePath,
        trigger: "blur",
      },
    ],
    fileDescription: [
      { required: true, message: "请输入文件说明", trigger: "blur" },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
        message: "请输入中英文字符串",
        trigger: "blur",
      },
      { min: 1, max: 20, message: "长度不超过20位", trigger: "blur" },
    ],
    uploadName: [
      { required: true, message: "请选择上传文件", trigger: "change" },
    ],
  },
});

const {
  ledgerTable,
  ledgerList,
  repairList,
  loadingPage,
  open,
  open2,
  open3,
  loading,
  showSearch,
  showRepair,
  isBatch,
  ips,
  statusList,
  runList,
  tagList,
  positionTreeList,
  positionList,
  positionProps,
  queryPositionResult,
  typeList,
  tenantTree,
  title,
  total,
  actionUrl,
  headers,
  headers2,
  isAllQuery,
  ruleOpen,
  lockRuleForm,
  lockRuleRef,
  limitSecond,
  fixRef,
  messageRules,
  abortController,
  uploading,
  progress,
  fileRules,
  installOpen,
  installTitle,
  installRef,
  uploadRef,
  fileRef,
  fileForm,
  fileOpen,
  messageRef,
  messageForm,
  messageOpen,
  userList,
  positionNodeList,
  positionNodeResultList,
  nodeRef,
  operationParams,
  remoteList,
  remoteSendList,
  statusObj,
  queryParams,
  form,
  rules,
  fixForm,
  tableAllSelectedId,
  repairForm,
  repairRules,
  ledgerErrorTotal,
  ledgerTotal,
} = toRefs(state);

// 检测浏览器是否支持 crypto.subtle.digest
checkSupportsCrypto();

function handleLockRule() {
  ruleOpen.value = true;
  proxy.resetForm("lockRuleRef");
}

// 提交锁屏规则
function handleRule() {}

// 取消锁屏规则
function handleCancelRule() {
  lockRuleRef.value.resetFields();
  ruleOpen.value = false;
}

const timeChange = () => {
  let second =
    [1, 60, 3600, 86400][messageForm.value.lastTimeUnit - 1] *
    messageForm.value.lastTimeNum;
  console.log(
    messageForm.value.lastTimeUnit,
    second,
    messageForm.value.lastTimeNum
  );
  limitSecond.value = second > 9999 ? 9999 : second;
};

// 表单验证文件传输位置
function validatePath(rule, value, callback) {
  if (fileForm.value.type < 1 && value == "") {
    callback(new Error("请输入文件指定传输位置"));
  } else if (fileForm.value.type < 1 && !isValidWindowsAbsolutePath(value)) {
    callback(new Error("请输入正确的系统文件绝对路径"));
  } else if (value && fileForm.value.type > 0 && !isValidRelativePath(value)) {
    callback(new Error("请输入正确的系统文件相对路径"));
  } else {
    callback();
  }
}

// 表单验证定时关机时间
function validateTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择定时关机时间"));
  } else if (new Date(value).getTime() < new Date().getTime()) {
    callback(new Error("定时关机时间不能小于当前时间"));
  } else {
    callback();
  }
}

// 表单验证发布消息通知时间
function validateNotice(rule, value, callback) {
  if (value && !state.messageForm.noticeTime) {
    callback(new Error("请选择定时通知时间"));
  } else if (
    new Date(state.messageForm.noticeTime).getTime() < new Date().getTime()
  ) {
    callback(new Error("定时通知时间不能小于当前时间"));
  } else {
    callback();
  }
}

// 确定上传
const handleFileSend = () => {
  if (!uploading.value) {
    state.fileRef.validate((valid) => {
      console.log(valid);
      if (valid) {
        actionUrl.value = BASEURL + `/system/schoolFile/releaseBigFileSlice`;
        state.uploadRef.submit();
      }
    });
  }
};

// 取消上传
const handleFileSendCancel = () => {
  if (abortController.value) abortController.value.abort();
  state.uploading = false;
  state.progress = 0;
};

const handleCancelFile = () => {
  proxy.resetForm("fileRef");
  fileForm.value.type = 0;
  handleFileSendCancel();
  state.fileOpen = false;
};

// 覆盖文件
const handleExceed = (files) => {
  state.uploadRef.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  state.uploadRef.handleStart(file);
  state.fileForm.uploadName = file.name;
};

// 文件发生变化
const changeFile = (file, files) => {
  console.log(file, files, "change");

  if (file.status == "ready") {
    state.fileForm.uploadName = file.name;
    state.fileRef.validateField("uploadName");
  }
};

// 文件上传之前
const beforeUpload = (file) => {
  console.log(file, "before");

  const fileName = file.name;
  const fileExtension = fileName
    .slice(((fileName.lastIndexOf(".") - 1) >>> 0) + 2)
    .toLowerCase();

  const isZipOrRar =
    title.value != "上传文件" &&
    (fileExtension === "zip" || fileExtension === "rar");
  const isSizeLimitForNewVersion =
    title.value != "上传文件" && file.size / 1024 / 1024 < 1130; // 1GB
  const isWithinSizeLimit =
    title.value === "上传文件" && file.size / 1024 / 1024 < 1130; // 1GB

  if (title.value != "上传文件") {
    if (!isSizeLimitForNewVersion) {
      ElMessage.error("上传文件大小不能超过 1GB!");
      state.fileRef.resetFields();
      return false;
    }
  }

  if (title.value === "上传文件" && !isWithinSizeLimit) {
    ElMessage.error("上传文件大小不能超过 1GB!");
    state.fileRef.resetFields();
    return false;
  }

  // 清除之前的文件，确保只保留一个文件
  state.uploadRef.clearFiles();

  return true;
};

// 自定义上传
const httpRequestFn = (options) => {
  console.log(options, "自定义上传");

  uploading.value = true;
  progress.value = 0;
  abortController.value = new AbortController();

  // 生成 requestId
  generateRequestId(options.file, 1024 * 1024, function (requestId) {
    try {
      console.log(`生成的 requestId 是: ${requestId}`);

      const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
      let start = 0;
      console.log("文件大小：" + options.file.size);
      let end = Math.min(chunkSize, options.file.size);

      const uploadChunk = (chunk, chunkNumber, totalChunks) => {
        const formData = new FormData();
        formData.append("file", new Blob([chunk]));
        formData.append("fileDescription", state.fileForm.fileDescription);
        formData.append(
          "deviceCodeList",
          JSON.stringify(
            state.tableAllSelectedRow.map((item) => item.deviceCode)
          )
        );
        formData.append("filePath", state.fileForm.filePath);
        formData.append("type", "1");
        formData.append("chunkNumber", chunkNumber);
        formData.append("totalChunks", totalChunks);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        ); // 使用文件名和大小作为标识符
        formData.append("requestId", requestId); // 添加生成的 requestId
        formData.append("originalFileName", options.file.name); // 添加原始文件名

        // 如果是最后一个分片，则添加一个标志
        if (chunkNumber === totalChunks) {
          formData.append("isLastChunk", "true");
        }

        fetch(actionUrl.value, {
          // 更新为你提供的URL
          method: "POST",
          body: formData,
          headers: headers2.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              // 打印当前时间和分片上传成功信息
              if (chunkNumber === totalChunks) {
                proxy.$modal.alert("发送成功", "提示");
                progress.value = 100;
              } else {
                progress.value = Math.round((chunkNumber / totalChunks) * 100);
              }
              console.log(
                `[${getCurrentTime()}] 分片 ${chunkNumber} 已成功上传`
              );
              processNextChunk(chunkNumber + 1);
            } else {
              console.error("分片上传失败:", response.statusText);
              proxy.$modal.alert(
                `${progress.value < 100 ? "上传" : "发送"}失败`,
                "提示"
              );
            }
          })
          .catch((error) => {
            console.error("用户手动取消", error);
            proxy.$modal.alert(
              `${progress.value < 100 ? "上传" : "发送"}失败`,
              "提示"
            );
          })
          .finally(() => {
            if (chunkNumber === totalChunks) {
              handleCancelFile();
            }
          });
      };

      const processNextChunk = (chunkNumber) => {
        if (start < options.file.size) {
          const nextChunk = options.file.slice(start, end);
          uploadChunk(
            nextChunk,
            chunkNumber,
            Math.ceil(options.file.size / chunkSize)
          );
          start = end;
          end = Math.min(options.file.size, start + chunkSize);
        } else {
          console.log("所有分片已上传完毕");
        }
      };

      // 开始处理第一个分片
      processNextChunk(1);
    } catch {}
  });
};

function handleBatchFile() {
  const { flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  state.fileOpen = true;
}

function handleBatchMessage() {
  const { flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  state.messageOpen = true;
}

function handleMessage() {
  state.messageRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      let d = {
        ...state.messageForm,
        deviceCodeList: state.tableAllSelectedRow.map(
          (item) => item.deviceCode
        ),
      };
      if (!d.isNotice) delete d.noticeTime;
      console.log("发布消息传参", d);
      sendMessage(d)
        .then((response) => {
          console.log("发布消息", response);
          proxy.$modal.msgSuccess("发布成功");
          proxy.$modal.closeLoading();
          handleCancelMessage();
        })
        .catch(() => proxy.$modal.closeLoading());
    }
  });
}

function handleCancelMessage() {
  proxy.resetForm("messageRef");
  state.messageForm.lastTimeUnit = 1;
  state.messageForm.noticeTime = "";
  state.messageOpen = false;
}

/** 查询用户列表 */
async function getUsers() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    console.log("拥有角色的用户列表", resp);
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

const handleToAdd = () => {
  router.push("/deviceLedger/baseInfo/location");
};

const handleCancel = () => {
  state.nodeRef.setCheckedNodes([]);
  open2.value = false;
};

function handleDownload() {
  let checkList = state.nodeRef.getCheckedNodes();
  if (checkList.length == 0) {
    proxy.$modal.msgWarning("请选择安装位置");
    return;
  }
  positionNodeResultList.value = [];
  handlePositionNode(checkList);
  console.log(positionNodeResultList.value, "positionNodeResultList");
  const positionList = positionNodeResultList.value.map((item) => {
    return {
      position: item.names.join("-"),
      positionId: item.ids.join(","),
    };
  });

  proxy.$modal.loading();
  checkDeviceCode({ positionList })
    .then((res) => {
      exportDeviceCode({ positionList })
        .then((response) => {
          proxy.$modal.closeLoading();
          // console.log("blob", response);
          downloadBlob(response, "application/vnd.ms-excel", "设备激活码");
          proxy.$modal.msgSuccess("操作成功");
          handleCancel();
        })
        .catch((err) => {
          proxy.$modal.closeLoading();
        });
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

function handlePositionNode(checkList) {
  positionNodeResultList.value = [{ ids: [], names: [] }];
  for (let i = 0; i < checkList.length; i++) {
    positionNodeResultList.value[i] = {
      ids: [],
      names: [],
    };

    positionNodeResultList.value[i].ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == checkList[i].id
    );
    const arr = positionNodeResultList.value[i].ids;
    for (let j = 0; j < arr.length; j++) {
      positionNodeResultList.value[i].names[j] = positionList.value.find(
        (node) => node.id == arr[j]
      ).name;
    }
  }
  console.log("positionResultNodeList ==> ", positionNodeResultList.value);
}

const repairUserChange = (val) => {
  const idx = state.userList.findIndex((_) => _.userId == val);
  state.repairForm.repairPhone = state.userList[idx].phoneNumber || "";
  state.repairForm.repairName = state.userList[idx].nickName || "";
};

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() + 86400000 < new Date().getTime()) {
    return true;
  }
  return false;
};

// 校验定时关机的时间
const checkFixTime = () => {
  fixRef.value.validate((valid) => {
    if (valid) {
      const time = fixForm.value.time.replace("S", " ").replace("Z", "");
      if (new Date(time).getTime() < new Date().getTime()) {
        proxy.$modal.msgWarning("选择的时间不能小于当前时间");
        return;
      }
      handleBatchRemote(isBatch.value, 2, fixForm.value);
    }
  });
};

function handleOpeartionRemote(val) {
  console.log("远程操作", val);
  switch (val) {
    // 批量远程关机
    case 0:
      handleBatchRemote(1, 0);
      break;
    //批量远程开机
    case 1:
      handleBatchPowerOn();
      break;
    // 批量远程重启
    case 2:
      handleBatchRemote(1, 1);
      break;
    // 批量远程定时关机
    case 3:
      handleBatchFixed();
      break;
    // 批量远程开启WIFI
    case 4:
      handleBatchWifi(1, 1);
      break;
    // 批量远程关闭WIFI
    case 5:
      handleBatchWifi(1, 0);
      break;
    // 批量远程清除缓存
    case 6:
      handleBatchClear(1);
      break;
    // 批量远程锁屏
    case 7:
      handleBatchLock(1, 1);
      break;
    // 批量远程解锁
    case 8:
      handleBatchLock(1, 0);
      break;
    default:
      break;
  }
  state.operationParams.remote = "";
}

function handleOpeartionRemoteSend(val) {
  console.log("远程传输操作", val);
  switch (val) {
    // 批量远程发布消息
    case 0:
      handleBatchMessage();
      break;
    //批量远程文件分发
    case 1:
      handleBatchFile();
      break;
    default:
      break;
  }
  state.operationParams.remoteSend = "";
}

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  handleQuery();
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

function getTagList() {
  getDeviceTag().then((response) => {
    tagList.value = response.data;
  });
}

async function getOptions() {
  await getDeviceType().then((response) => {
    typeList.value = response.data.reduce((res, cur) => {
      if (cur.typeName == "智慧大屏") queryParams.value.typeId = cur.id;
      res.push({
        ...cur,
      });
      return res;
    }, []);
  });
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    positionList.value = treeToArray(response.data);
    state.positionNodeList = addAttr(tree);
    console.log("positionList ==>", positionList.value);
  });
}

function addAttr(data, num = 0) {
  num++;
  for (var j = 0; j < state.length; j++) {
    data[j].disabled = num != 3 || data[j].status == 1;
    // console.log(num, data[j]);
    if (data[j].children.length > 0) {
      addAttr(data[j].children, num);
    }
  }
  return data;
}

function handleRepair(row) {
  if (tableAllSelectedId.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  repairReset();
  repairList.value = row.deviceId ? [row] : state.tableAllSelectedRow;
  getUsers();
  // console.log(repairList.value)
  showRepair.value = true;
}

/** 查询设备列表 */
function getList() {
  console.log(queryParams.value, "queryParams.value");
  state.isAllQuery = false;
  loading.value = true;
  devicePage(queryParams.value).then((response) => {
    console.log(response, "管控");

    const { page, abnormalTerminal, normalTerminal } = response.data;
    ledgerList.value = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        isOff,
        runStatus,
        ralayHost,
        deviceCode,
        ipAddress,
        logicAddress,
        macAddress,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tenantName,
      } = cur;
      res.push({
        ...cur,
        status: !!isOff ? 0 : !!runStatus ? 2 : 1,
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        deviceCodeStr: deviceCode || "-",
        ipAddressStr: ipAddress || "-",
        ralayHostStr: ralayHost || "-",
        logicAddressStr: logicAddress || "-",
        macAddressStr: macAddress || "-",
        tenantNameStr: tenantName || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        runStatusList: runStatus,
      });
      return res;
    }, []);
    // console.log(ledgerList.value)
    console.log(
      "筛选条件参数，当前页台账列表数据",
      queryParams.value,
      ledgerList.value
    );
    total.value = page.total;
    // total.value = ledgerTotal.value = page.total
    ledgerErrorTotal.value = abnormalTerminal;
    ledgerTotal.value = normalTerminal;
    loading.value = false;
    nextTick(() => {
      ledgerList.value.forEach((item) => {
        if (state.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, state.tableAllSelectedId)
          ledgerTable.value?.toggleRowSelection(item, true);
        } else {
          ledgerTable.value?.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
  });
  devicePage({
    ...queryParams.value,
    current: 1,
    size: 9999999,
    typeId: 1,
    smartScreen: 1,
    abnormalInterruptionChannel: queryParams.value.abnormalInterruptionChannel,
  }).then((res) => {
    console.log("所有台账数据", res);
    state.tableData_all = res.data.page.records;
    console.log(state.tableData_all);
  });
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  open3.value = false;
  reset();
}

/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  fixForm.value = {
    ipAddress: "",
    time: null,
  };
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "1",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
  proxy.resetForm("fixRef");
}

/** 表单重置 */
function repairReset() {
  proxy.resetForm("repairRef");
  repairForm.value = {
    type: 1,
    userId: useUserStore().userId + "",
    repairName: useUserStore().nickName,
    repairPhone: useUserStore().phonenumber,
    repairTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    deviceCodeList: [],
    remark: "",
    resource: "管理后台",
    channel: "管理员主动报障",
    images: "",
  };
}

function handleCheck(row) {
  router.push({
    path: "/deviceLedger/deviceInfo",
    query: {
      id: row.deviceId,
      type: 0,
      from: 1,
    },
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 0;
  getList();
}

/** 全部搜索按钮操作 */
function handleAllQuery(val) {
  loading.value = true;
  proxy.resetForm("queryRef");
  state.isAllQuery = true;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  devicePage({
    current: 1,
    size: 9999999,
    typeId: 1,
    // smartScreen: 0,
    abnormalInterruptionChannel: val,
  })
    .then((resp) => {
      console.log("所有台账数据", resp);
      ledgerList.value = resp.data.page.records.reduce((res, cur) => {
        let {
          deviceName,
          deviceType,
          isOff,
          runStatus,
          ralayHost,
          deviceCode,
          ipAddress,
          logicAddress,
          macAddress,
          model,
          osVersion,
          cpu,
          internalStorage,
          disk,
          brand,
          tenantName,
        } = cur;
        res.push({
          ...cur,
          status: !!isOff ? 0 : !!runStatus ? 2 : 1,
          deviceNameStr: deviceName || "-",
          deviceTypeStr: deviceType || "-",
          deviceCodeStr: deviceCode || "-",
          ipAddressStr: ipAddress || "-",
          ralayHostStr: ralayHost || "-",
          logicAddressStr: logicAddress || "-",
          macAddressStr: macAddress || "-",
          tenantNameStr: tenantName || "-",
          modelStr: model || "-",
          osVersionStr: osVersion || "-",
          cpuStr: cpu || "-",
          internalStorageStr: internalStorage || "-",
          diskStr: disk || "-",
          brandStr: brand || "-",
          runStatusList: runStatus,
        });
        return res;
      }, []);
      if (val) {
        ledgerErrorTotal.value = ledgerList.value.length;
      } else {
        ledgerTotal.value = ledgerList.value.length;
      }
    })
    .finally(() => (loading.value = false));
}

/** 异常搜索按钮操作 */
function handleErrorQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.current = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.positionIds = [];
  proxy.$refs.treeRef.setCheckedNodes([]);
  handleQueryPosition();
}

/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      ledgerTable.value.setCurrentRow(null);
      ledgerTable.value.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.deviceId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      ledgerTable.value.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    ledgerTable.value.setCurrentRow(row);
    ledgerTable.value.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    if (valid) {
      repairForm.value.deviceCodeList =
        repairList.value.map((item) => item.deviceCode) || [];
      repairForm.value.images = repairForm.value.images.split(",") || "";
      deviceRepair(repairForm.value).then((res) => {
        proxy.$modal.msgSuccess("报障成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  handleQuery();
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      state.tableAllSelectedId.push(item.deviceId);
      state.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.deviceId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = ledgerList.value;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableData_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        state.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', state.tableData_all)
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 批量操作前的检测 */
const checkBatch = () => {
  console.log(state.tableAllSelectedRow, "tableAllSelectedRow");

  let arr = [],
    flag = false;
  ips.value = [];
  let narr = [];
  state.tableAllSelectedRow.map((item) => {
    if (item.runStatus.indexOf(0) != -1) {
      // 关机
      narr.push(item.deviceName);
      flag = true;
    } else {
      arr.push(item.deviceName);
      ips.value.push(item.ipAddress);
    }
  });
  return { arr, narr, flag };
};

/** 批量定时关机按钮操作 */
const handleBatchFixed = () => {
  const { flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  isBatch.value = true;
  title.value = "批量定时关机";
  open.value = true;
};

const handleFixed = (row) => {
  isBatch.value = false;
  title.value = "定时关机";
  fixForm.value.ipAddress = row.ipAddress;
  fixForm.value.ralayHost = row.ralayHost;
  fixForm.value.deviceCode = row.deviceCode;
  fixForm.value.deviceName = row.deviceName;
  open.value = true;
};

// 添加操作日志
function addDeviceLog(obj) {
  console.log("添加操作日志传参", obj);
  addSchoolDeviceLog(obj);
}

// 远程操作集中处理函数
function monitorProcessing(
  confirmText,
  logContent,
  uri,
  method,
  flag,
  isBatch,
  deviceCode,
  content = ""
) {
  if (flag) {
    proxy.$modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  proxy.$modal
    .confirm(confirmText)
    .then(async function () {
      let rows = proxy.$refs.ledgerTable.getSelectionRows();

      if (!!isBatch) {
        for (let j = 0; j < rows.length; j++) {
          addDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows[j].deviceCode, // 操作设备编号
            logContent, // 日志内容
          });
        }
      } else {
        addDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode, // 操作设备编号
          logContent, // 日志内容
        });
      }

      proxy.$modal.loading();

      let obj = {
        method,
        uri,
        content,
        deviceCodeList: !!isBatch
          ? rows.map((item) => item.deviceCode).join(",")
          : deviceCode,
      };
      console.log("集控操作传参", obj);
      deviceCtlMqtt(obj)
        .then((res) => {
          proxy.$modal.msgSuccess("操作成功");
        })
        .finally((e) => {
          proxy.$modal.closeLoading();
        });
    })
    .catch(() => {});
}

/** 批量重启/关闭/定时关闭按钮操作  type: 0关机   1重启  2定时关机 */
const handleBatchRemote = async (isBatch, type, row) => {
  const { arr, flag } = checkBatch();
  let d = { cmd: type == 2 ? 0 : type * 1 };
  // 如果是定时关机则要传 time
  if (type == 2) {
    d.time = row.time;
  } else {
    delete d.time;
  }
  monitorProcessing(
    `是否确认${!!isBatch ? "批量" : ""}对设备名称为【${
      !!isBatch ? arr.join("、") : row.deviceName
    }】的设备进行${type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"}？`,
    `远程${type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"}`,
    `/api/Cockpit/Control`,
    "post",
    flag,
    isBatch,
    row?.deviceCode,
    JSON.stringify(d)
  );
  cancel();
};

/** 批量开机 */
const handleBatchPowerOn = () => {
  const { arr, narr } = checkBatch();
  if (arr.length > 0) {
    proxy.$modal.msgWarning("设备存在开机状态，无法进行此操作");
    return;
  }
  proxy.$modal
    .confirm(`是否确认批量对设备名称为【${narr.join("、")}】的设备进行开机？`)
    .then(() => {
      let rows = proxy.$refs.ledgerTable.getSelectionRows();
      for (let j = 0; j < rows.length; j++) {
        try {
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows[j].deviceCode, // 操作设备编号
            logContent: `远程开机`, // 日志内容
          });
        } catch (error) {}

        proxy.$modal.loading();

        deviceStartByWOL({ deviceCode: rows[j].deviceCode })
          .then((res) => {
            console.log(res);
          })
          .finally((err) => {
            proxy.$modal.closeLoading();
          });
      }
    })
    .catch((err) => {});
};

/** 批量锁屏/解锁  type: 1锁屏   0解锁 */
const handleBatchLock = (isBatch, type, row) => {
  const { arr, flag } = checkBatch();
  monitorProcessing(
    `是否确认${!!isBatch ? "批量" : ""}对设备名称为【${
      !!isBatch ? arr.join("、") : row.deviceName
    }】的设备进行${!!type ? "锁屏" : "解锁"}？`,
    `远程${!!type ? "锁屏" : "解锁"}`,
    `/api/Cockpit/${!!type ? "StratLockForm" : "StopLockForm"}`,
    "get",
    flag,
    isBatch,
    row?.deviceCode
  );
};

/** 批量清除缓存 */
const handleBatchClear = (isBatch, row) => {
  const { arr, flag } = checkBatch();
  monitorProcessing(
    `是否确认${!!isBatch ? "批量" : ""}清除设备名称为【${
      !!isBatch ? arr.join("、") : row.deviceName
    }】的设备缓存？`,
    `远程清除缓存`,
    `/api/Cockpit/ClearMemory`,
    "get",
    flag,
    isBatch,
    row?.deviceCode
  );
};

/** 批量开启/关闭 Wifi type: 1开启   0关闭 */
const handleBatchWifi = (isBatch, type, row) => {
  const { arr, flag } = checkBatch();
  monitorProcessing(
    `是否确认${!!isBatch ? "批量" : ""}${!!type ? "开启" : "关闭"}设备名称为【${
      !!isBatch ? arr.join("、") : row.deviceName
    }】的设备wifi？`,
    `远程${!!type ? "开启" : "关闭"}WIFI`,
    `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
    "get",
    flag,
    isBatch,
    row?.deviceCode
  );
};

onMounted(async () => {
  await getOptions();
  await getPositionTreeList();
  await getTagList();
  getList();
});

onActivated(() => {
  console.log("激活");

  // 检查是否需要触发异常设备查询
  if (route.query.triggerAbnormal) {
    // 设置标记，表示是从工作台跳转而来
    fromWorkbench.value = true;

    // 清除查询参数以避免刷新时再次触发
    router.replace({ path: route.path });

    // 调用处理异常设备查询的函数
    handleErrorQuery();
  } else if (!fromWorkbench.value) {
    getList();
  }
});

// 在离开页面时重置状态
onDeactivated(() => {
  // 重置fromWorkbench状态，确保再次进入时不显示返回按钮
  fromWorkbench.value = false;

  // 重置查询参数，确保下次进入时不会自动触发异常查询
  queryParams.value.abnormalInterruptionChannel = 0;
});

// 添加 handleBack 方法
function handleBack() {
  fromWorkbench.value = false;
  proxy.$tab.closeOpenPage("/work");
}
</script>
  
  <style lang="scss" scoped>
.pagination-container {
  height: auto;
  margin-top: 15px;
}
.monitor {
  display: flex;
  gap: 0 10px;
  // position: relative;
  &-bottom {
    position: fixed;
    width: 100%;
    display: flex;
    justify-content: center;
    bottom: 35px;
    left: 0;
    z-index: 1002;
  }
  &-btns {
    // border: 1px solid red;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px 0;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
  &-title {
    font-size: 16px;
    padding: 10px 20px;
  }
  &-position {
    width: 18%;
    margin-right: 10px;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }
  &-list {
    width: 82%;
  }
}
.el-progress {
  width: 200px;
  :deep(.el-progress__text) {
    min-width: 35px;
  }
}
.fileclass {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }
}
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;
  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}
.monitor-statics {
  border: 1px solid #d4d4d4;
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  font-size: 16px;
  &_item {
    padding: 40px;
    flex: 1;
    display: flex;
    justify-content: center;
    cursor: pointer;
    div {
      margin-right: 20px;
    }
    &:last-child {
      border-left: 1px solid #d4d4d4;
      div {
        color: #c73528;
      }
    }
  }
}
</style>
  