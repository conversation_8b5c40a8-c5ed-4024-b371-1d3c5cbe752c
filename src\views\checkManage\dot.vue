<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="pointCode">
          <el-input
            v-model="queryParams.pointCode"
            placeholder="请输入点位编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入点位名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建点位</el-button
          >
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleBatchDel"
            :disabled="tableAllSelectedId.length < 1"
            >批量删除</el-button
          >
        </el-col>
      </el-row>

      <el-table
        ref="dotTable"
        v-loading="loading"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column type="selection" label="" width="80" align="center" />
        <el-table-column
          label="点位编号"
          align="center"
          minWidth="120px"
          prop="pointCode"
          show-overflow-tooltip
        />
        <el-table-column
          label="点位名称"
          align="center"
          minWidth="120px"
          prop="name"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检指标"
          align="center"
          minWidth="120px"
          prop="indicatorsName"
          show-overflow-tooltip
        />
        <el-table-column
          label="点位备注"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.remark || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="250"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.stop="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="success"
              icon="Download"
              @click.stop="handleCode(scope.row)"
              >下载二维码</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.stop="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <div id="qrcode" v-show="false"></div>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="typeRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item v-if="form.id" label="点位编号" prop="typeNumber">
          <el-input v-model="form.pointCode" disabled placeholder="-" />
        </el-form-item>
        <el-form-item label="点位名称" prop="name">
          <el-input
            v-model="form.name"
            maxlength="10"
            :readonly="readonly"
            placeholder="请输入点位名称"
          />
        </el-form-item>
        <el-form-item label="巡检指标" prop="indicatorsId">
          <el-select v-model="form.indicatorsId" clearable filterable>
            <el-option
              v-for="item in pointList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="点位备注" prop="remark">
          <el-input
            v-model="form.remark"
            maxlength="100"
            type="textarea"
            :readonly="readonly"
            placeholder="请输入类别描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-throttle>{{
            form.id ? "保存" : "新建"
          }}</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { generateQRCode } from "@/utils/uploadCode";
import { useRoute } from "vue-router";
import { findIndexInObejctArr } from "@/utils";
import {
  schoolPointInfo,
  addSchoolPoint,
  updateSchoolPoint,
  delSchoolPoint,
  schoolPointList,
  schoolIndicatorsList,
} from "@/api/check";

const route = useRoute();
const { proxy } = getCurrentInstance();

const state = reactive({
  dotTable: null,
  open: false,
  loading: false,
  readonly: false,
  total: 0,
  title: "",
  pointList: [],
  tableList_all: [],
  tableList: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: "",
  },
  rules: {
    name: [
      { required: true, message: "点位名称不能为空", trigger: "blur" },
      { max: 10, message: "点位名称最多输入10个字符", trigger: "blur" },
    ],
    indicatorsId: [
      { required: true, message: "巡检指标不能为空", trigger: "change" },
    ],
    remark: [
      { max: 200, message: "点位描述最多输入200个字符", trigger: "blur" },
    ],
  },
});

const {
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  dotTable,
  open,
  loading,
  readonly,
  total,
  title,
  queryParams,
  form,
  rules,
  tableList,
  pointList,
} = toRefs(state);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

async function getPointList() {
  await schoolIndicatorsList({ pageNum: 1, pageSize: 999999 }).then(
    (response) => {
      if (response.code == 200) {
        const { records } = response.data;
        console.log(records);
        state.pointList = records;
      }
    }
  );
}

/** 查询类别列表 */
function getList() {
  state.loading = true;
  schoolPointList(state.queryParams).then((response) => {
    console.log("dot", response);
    const { records, total } = response.data;
    state.tableList = records || [];
    state.total = total || 0;
    state.loading = false;
    nextTick(() => {
      state.tableList.forEach((item) => {
        if (state.tableAllSelectedId.indexOf(item.id) > -1) {
          state.dotTable.toggleRowSelection(item, true);
        } else {
          state.dotTable.toggleRowSelection(item, false);
        }
      });
    });
  });
  schoolPointList({ ...state.queryParams,pageNum: 1, pageSize: 999999 }).then((response) => {
    const { records } = response.data;
    state.tableList_all = records;
  });
}
/** 取消按钮 */
function cancel() {
  state.open = false;
  reset();
}
/** 表单重置 */
function reset() {
  state.form = {
    id: "",
    name: "",
    remark: "",
    createTime: "",
  };
  proxy.resetForm("typeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  getPointList();
  reset();
  state.open = true;
  state.title = "新建点位";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  getPointList();
  reset();
  state.title = "编辑点位";
  state.form = JSON.parse(JSON.stringify(row));
  state.open = true;
}
/** 查看按钮操作 */
let timer = null;
function handleCode(row) {
  console.log(row);
  clearTimeout(timer);
  state.form = JSON.parse(JSON.stringify(row));
  timer = setTimeout(() => {
    proxy.$modal.loading("正在下载...");
    let dataArr = [
      {
        id: row.id,
        name: `${row.name}`,
        name2: `${row.indicatorsName || '-'}`,
      },
    ];
    generateQRCode(dataArr, false, `${row.name}-点位二维码.png`);
    // generateQRCode(`${row.id}`, row.name, true);
    setTimeout(() => {
      //   autoPicture("codeImg", { width: 300, height: 350 });
      proxy.$modal.closeLoading();
    }, 150);
  }, 150);
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeRef"].validate((valid) => {
    if (valid) {
      state.form.indicatorsName = state.pointList.find(
        (_) => _.id == state.form.indicatorsId
      ).name;

      if (!!state.form.id) {
        updateSchoolPoint(state.form).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.open = false;
        });
      } else {
        addSchoolPoint(state.form).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          state.open = false;
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除点位名称为"' + row.name + '"的数据项？')
    .then(async function () {
      await delSchoolPoint({ ids: row.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
// 批量删除操作
function handleBatchDel() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一个点位");
    return;
  } else {
    proxy.$modal
      .confirm("确定批量删除？")
      .then(async function () {
        await delSchoolPoint({ ids: state.tableAllSelectedId.join(",") });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "id"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.dotTable.setCurrentRow(null);
      state.dotTable.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.id);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.dotTable.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.dotTable.setCurrentRow(row);
    state.dotTable.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.id) === -1) {
      state.tableAllSelectedId.push(item.id);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.id);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.id) === -1) {
        state.tableAllSelectedId.push(item.id); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

getList();
</script>

<style lang="scss" scoped>
#codeImg {
  // border: 1px solid red;
  width: 300px;
  height: 350px;
  display: flex;
  flex-direction: column;
  justify-content: space-evenly;
  align-items: center;
  font-size: 16px;
  white-space: pre-wrap;
  background-color: #fff;
}
</style>
