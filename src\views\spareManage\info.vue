<template>
  <div class="spareInfo app-container">
    <el-card shadow="never" class="page-card" v-loading="loadingPage">
      <template #header>
        <div class="card-header page-header">
          <!-- 默认显示列表操作记录的第一条数据 -->
          <div class="left">备件详情</div>
          <div class="right" v-if="!isDevice">
            <el-button @click="handleBack">返回列表</el-button>
            <el-button
              v-throttle
              type="primary"
              @click="isEdit ? handleSave() : handleEdit()"
              >{{ isEdit ? "保存" : "编辑" }}表单</el-button
            >
          </div>
          <div class="right" v-else>
            <el-button @click="handleBack">返回</el-button>
          </div>
        </div>
      </template>

      <el-form
        ref="spareRef"
        :model="spareInfo"
        :rules="rules"
        style="margin-bottom: 15px"
      >
        <el-descriptions title="备件基本信息" border>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="备件编号"
          >
            <span>{{ spareInfo.code }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="备件名称"
          >
            <span v-if="!isEdit">{{ spareInfo.name }}</span>
            <el-form-item v-else label="" prop="name">
              <el-input
                placeholder="请输入"
                v-model="spareInfo.name"
                style="width: 250px"
                maxlength="10"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="备件类别"
          >
            <span v-if="!isEdit">{{ spareInfo.typeName }}</span>
            <el-form-item v-else label="" prop="typeId">
              <el-select
                v-model="spareInfo.typeId"
                placeholder="请选择类别"
                style="width: 250px"
                @change="handleChangeSpareType"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="规格型号"
          >
            <span v-if="!isEdit">{{ spareInfo.specifications }}</span>
            <el-form-item v-else label="" prop="specifications">
              <el-input
                placeholder="请输入"
                v-model="spareInfo.specifications"
                style="width: 250px"
                maxlength="10"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="备件品牌"
          >
            <span v-if="!isEdit">{{ spareInfo.brand }}</span>
            <el-form-item v-else label="" prop="brand">
              <el-input
                placeholder="请输入"
                v-model="spareInfo.brand"
                style="width: 250px"
                maxlength="10"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="颜色分类"
          >
            <span v-if="!isEdit">{{ spareInfo.color }}</span>
            <el-form-item v-else label="" prop="color">
              <el-input
                placeholder="请输入"
                v-model="spareInfo.color"
                style="width: 250px"
                maxlength="10"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="现有库存"
          >
            <span v-if="!isEdit">{{ spareInfo.stock }}</span>
            <el-form-item v-else label="" prop="stock">
              <el-input-number
                v-model="spareInfo.stock"
                :min="0"
                :max="999999999"
                :value-on-clear="0"
                @change="getExpendSum"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="总消耗量"
          >
            <span v-if="!isEdit">{{ spareInfo.consumption }}</span>
            <el-form-item v-else label="" 
                  prop="consumption"                 
                  :rules="{
                  required:true,
                  validator: (rule, value, callback) =>
                  validConsume(rule, value, callback, index),
                  trigger: 'change',
                  }">
              <el-input-number
                v-model="spareInfo.consumption"
                :min="0"
                :max="999999999"
                @change="getConSume"
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <div class="spareInfo-title">
        出入库记录
        <el-button
          v-if="!isDevice"
          plain
          type="primary"
          icon="Plus"
          size="small"
          @click="handleDialog(0)"
          >新增出入库记录</el-button
        >
      </div>
      <div class="spareInfo-main2">
        <el-table :data="crkList" border v-loading="loadingCrk">
          <el-table-column
            label="单据编号"
            prop="code"
            align="center"
            min-width="90"
            show-overflow-tooltip
          />
          <el-table-column
            label="类型"
            align="center"
            min-width="100"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ !!row.type ? "入库" : "出库" }}
            </template>
          </el-table-column>
          <el-table-column
            label="单据来源"
            prop="resource"
            align="center"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="关联单据编号"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.associatedCode || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="数量"
            prop="num"
            align="center"
            min-width="90"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ Math.abs(row.num) || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="创建时间"
            prop="createTime"
            align="center"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            align="center"
            min-width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="primary"
                v-if="!!scope.row.associatedCode"
                v-throttle
                link
                @click="handleDetail(scope, 0)"
                >查看详情</el-button
              >
              <el-button type="danger" link @click="handleDelete(scope, 0)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="crkTotal > 0"
          :total="crkTotal"
          v-model:page="queryParamsCrk.pageNum"
          v-model:limit="queryParamsCrk.pageSize"
          @pagination="getList(0)"
          :background="false"
          :autoScroll="false"
          layout="total,prev,pager,next"
        />
      </div>

      <div class="spareInfo-title">
        关联工单记录
        <el-button
          v-if="!isDevice"
          plain
          type="primary"
          icon="Plus"
          size="small"
          @click="handleDialog(1)"
          >新增关联工单</el-button
        >
      </div>
      <div class="spareInfo-main2">
        <el-table :data="taskList" border v-loading="loadingTask">
          <el-table-column
            label="单据编号"
            prop="code"
            align="center"
            min-width="90"
            show-overflow-tooltip
          />
          <el-table-column
            label="单据来源"
            prop="resource"
            align="center"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="关联单据编号"
            prop="associatedCode"
            align="center"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="数量"
            prop="num"
            align="center"
            min-width="90"
            show-overflow-tooltip
          />
          <el-table-column
            label="创建时间"
            prop="createTime"
            align="center"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            label="操作"
            align="center"
            min-width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleDetail(scope, 1)"
                v-throttle
                >查看详情</el-button
              >
              <el-button type="danger" link @click="handleDelete(scope, 1)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="taskTotal > 0"
          :total="taskTotal"
          :background="false"
          v-model:page="queryParamsTask.pageNum"
          v-model:limit="queryParamsTask.pageSize"
          @pagination="getList(1)"
          :autoScroll="false"
          layout="total,prev,pager,next"
        />
      </div>
      <div class="spareInfo-title">
        关联应急事件
        <el-button
          v-if="!isDevice"
          plain
          type="primary"
          icon="Plus"
          size="small"
          @click="handleDialog(2)"
          >新增应急事件</el-button
        >
      </div>
      <div class="spareInfo-main2">
        <el-table :data="eventList" border v-loading="loadingEvent">
          <el-table-column
            label="单据编号"
            prop="code"
            align="center"
            min-width="90"
          />
          <el-table-column
            label="单据来源"
            prop="resource"
            align="center"
            min-width="100"
          />
          <el-table-column
            label="关联单据编号"
            prop="associatedCode"
            align="center"
            min-width="120"
          />
          <el-table-column
            label="数量"
            prop="num"
            align="center"
            min-width="90"
          />
          <el-table-column
            label="创建时间"
            prop="createTime"
            align="center"
            min-width="150"
          />
          <el-table-column
            label="操作"
            align="center"
            min-width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                type="primary"
                link
                @click="handleDetail(scope, 2)"
                v-throttle
                >查看详情</el-button
              >
              <el-button type="danger" link @click="handleDelete(scope, 2)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="eventTotal > 0"
          :total="eventTotal"
          :background="false"
          :autoScroll="false"
          v-model:page="queryParamsEvent.pageNum"
          v-model:limit="queryParamsEvent.pageSize"
          @pagination="getList(2)"
          layout="total,prev,pager,next"
        />
      </div>

      <div class="spareInfo-record" v-if="operationLogList.length > 1">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in operationLogList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role || ""} 于 ${item.time} ${item.type}了${
              item.spareName
            }的现库存`
          }}
        </div>
      </div>
    </el-card>

    <!-- 添加出入库/关联工单/应急事件记录 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="700"
      @close="handleCancel"
    >
      <el-form :model="form" ref="dialogFormRef">
        <el-form-item label="类型" prop="type">
          <el-select
            v-model="form.type"
            style="width: 250px"
            placeholder="请选择类型"
            :disabled="title != '新增出入库记录'"
            @change="form.source = ''"
          >
            <el-option label="出库" :value="0" />
            <el-option label="入库" :value="1" />
          </el-select>
        </el-form-item>
        <el-form-item label="单据来源" prop="source">
          <el-select
            v-model="form.source"
            style="width: 250px"
            :placeholder="form.type === '' ? '请先选择类型' : '请选择单据来源'"
            :disabled="form.type === '' || title != '新增出入库记录'"
            @change="
              showRelaTable = false;
              dialogFormRef.clearValidate('associatedCode');
            "
          >
            <el-option v-if="form.type === 1" label="采购" :value="0" />
            <el-option v-if="form.type === 0" label="关联工单" :value="1" />
            <el-option v-if="form.type === 0" label="应急事件" :value="2" />
          </el-select>
        </el-form-item>
        <div v-if="form.type == 1 && form.source == 0">
          <el-form-item label="入库数量" prop="num">
            <el-input-number v-model="form.num" :min="1" :max="99999" />
          </el-form-item>
        </div>
        <div v-if="form.type == 0 && form.source > 0">
          <el-form-item
            label="关联单据编号"
            prop="associatedCode"
            :rules="{
              required: true,
              message: '关联单据编号不能为空',
              trigger: 'change',
            }"
          >
            <el-input
              v-model="form.associatedCode"
              placeholder="请选择关联单据"
              readonly
              @click="handleShowTable"
              style="width: 250px"
            />
          </el-form-item>

          <div v-if="showRelaTable">
            <el-form
              class="search-list"
              :model="queryParams"
              ref="queryRef"
              :inline="true"
              @submit.native.prevent
            >
              <el-form-item label="" prop="code">
                <el-input
                  v-model="queryParams.code"
                  placeholder="请输入单据编号"
                  clearable
                  style="width: 200px"
                  @keyup.enter="handleQuery"
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery"
                  >搜索</el-button
                >
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
              </el-form-item>
            </el-form>
            <el-table
              ref="dialogTableRef"
              v-loading="loadingDialog"
              :data="tableList"
              border
              @row-click="rowClick"
            >
              <el-table-column label="" align="center" width="80">
                <template #default="scope">
                  <el-checkbox
                    v-model="scope.row.checked"
                    @change="(val) => handleSingleChange(val, scope)"
                    @click.native.stop=""
                  />
                </template>
              </el-table-column>
              <el-table-column label="单据编号" prop="code" min-width="100" />
              <el-table-column
                label="创建时间"
                prop="createTime"
                min-width="100"
              />
              <el-table-column label="消耗量" align="center">
                <template #default="scope">
                  <el-input-number
                    size="small"
                    v-model="scope.row.num"
                    :disabled="!scope.row.checked"
                    :min="1"
                    :max="999999999"
                    :value-on-clear="1"
                    @click.native.stop=""
                  />
                </template>
              </el-table-column>
            </el-table>
            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.pageNum"
              v-model:limit="queryParams.pageSize"
              @pagination="getRelateList"
              :background="false"
              :autoScroll="false"
              layout="total,prev,pager,next"
            />
            <div
              v-show="!!dialogSelectedRow.code"
              style="display: flex; justify-content: center; margin-top: 20px"
            >
              <el-button
                icon="Connection"
                plain
                type="primary"
                @click="handleRelate"
                >关联该条单据</el-button
              >
            </div>
          </div>
        </div>
      </el-form>
      <template #footer>
        <el-button @click.stop="handleCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleConfirm" v-throttle
          >新建</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="spareInfo">
import { ref, getCurrentInstance, onMounted, reactive, toRefs } from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  sparePartsInfo,
  addSparePartsDetails,
  delSparePartsInfo,
  sparePartsEntryAndRecordPage,
  sparePartsCategoryPage,
  sparePartsWorkOrderPage,
  sparePartsEmergencyPage,
  updateSpareParts,
} from "@/api/spare";
import { deviceMaintenanceList, repairInfo } from "@/api/mediaTeach/trouble";
import { schoolPlanWorkOrderList } from "@/api/emergency";
import { nextTick } from "process";
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const isDevice = ref(!!route.query.deviceId);
const isChangeReocrd = ref(!!route.query.changeId);

const state = reactive({
  loadingPage: false,
  loadingCrk: false,
  loadingTask: false,
  loadingEvent: false,
  title: "添加关联工单",
  dialogFormRef: null,
  spareRef: null,
  queryRef: null,
  isEdit: false,
  initStock: 0,
  initConsumption: 0,
  loadingDialog: false,
  dialogVisible: false,
  showRelaTable: false,
  dialogSelectedRow: {},
  total: 0,
  crkTotal: 0,
  taskTotal: 0,
  eventTotal: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    code: "",
    type: 2,
    troubleStatusList: [2, 3],
  },
  queryParamsCrk: {
    pageNum: 1,
    pageSize: 5,
  },
  queryParamsTask: {
    pageNum: 1,
    pageSize: 5,
  },
  queryParamsEvent: {
    pageNum: 1,
    pageSize: 5,
  },
  form: {},
  spareInfo: {},
  crkList: [],
  taskList: [],
  eventList: [],
  tableList: [],
  typeList: [],
  operationLogList: [],
  statusObj: {
    0: {
      type: "primary",
      name: "待认领",
      path: "/taskManage/taskCenter/claimInfo",
    },
    1: {
      type: "danger",
      name: "待处理",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
    2: {
      type: "warning",
      name: "待评价",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    3: {
      type: "success",
      name: "已完成",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    4: {
      type: "info",
      name: "已挂起",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
  },
  rules: {
    name: [{ required: true, message: "备件名称不能为空", trigger: "blur" }],
    typeId: [
      { required: true, message: "备件类别不能为空", trigger: "change" },
    ],
    specifications: [
      { required: true, message: "规格型号不能为空", trigger: "blur" },
    ],
    brand: [{ required: true, message: "备件品牌不能为空", trigger: "blur" }],
    color: [{ required: true, message: "颜色分类不能为空", trigger: "blur" }],
  },
});
const {
  statusObj,
  loadingCrk,
  loadingEvent,
  loadingTask,
  loadingPage,
  queryRef,
  title,
  initStock,
  dialogFormRef,
  loadingDialog,
  spareRef,
  isEdit,
  dialogVisible,
  crkList,
  taskList,
  eventList,
  showRelaTable,
  dialogSelectedRow,
  total,
  crkTotal,
  taskTotal,
  eventTotal,
  queryParams,
  queryParamsCrk,
  queryParamsTask,
  queryParamsEvent,
  form,
  tableList,
  spareInfo,
  typeList,
  operationLogList,
  rules,
} = toRefs(state);

const handleChangeSpareType = (val) => {
  console.log(state.typeList.find((_) => _.id == val));
  state.spareInfo.typeName = state.typeList.find((_) => _.id == val).name;
};

// 弹窗确认按钮
const handleConfirm = () => {
  state.dialogFormRef.validate((valid) => {
    if (valid) {
      const { type, num, source, associatedCode, associatedId } = state.form;
      let obj = {
        sparePartsIdList: [route.query.id],
        type,
        num,
        resourceType: "",
        associatedCode: "",
        associatedId: "",
        resource: ["采购", "关联工单", "应急事件"][source],
      };
      if (state.form.source == 0) {
        obj.resourceType = 3;
      }
      if (state.form.source == 1) {
        obj.resourceType = 1;
        obj.associatedCode = associatedCode;
        obj.associatedId = associatedId;
      }
      if (state.form.source == 2) {
        obj.resourceType = 2;
        obj.associatedCode = associatedCode;
        obj.associatedId = associatedId;
      }
      console.log("obj", obj);
      addSparePartsDetails(obj).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("新增成功");
          getData();
          handleCancel();
        }
      });
    }
  });
};

// 打开弹窗
const handleDialog = (val) => {
  if (val == 0) {
    state.title = "新增出入库记录";
    state.form = {
      type: "",
      source: "",
      num: 0,
    };
  }
  if (val == 1) {
    state.title = "新增关联工单";
    state.form = {
      type: 0,
      source: 1,
      associatedCode: "",
      num: 0,
    };
  }
  if (val == 2) {
    state.title = "新增应急事件";
    state.form = {
      type: 0,
      source: 2,
      associatedCode: "",
      num: 0,
    };
  }
  state.dialogVisible = true;
};

// 点击显示关联单据表格
const handleShowTable = () => {
  state.showRelaTable = true;
  state.dialogSelectedRow = {};
  state.form.associatedCode = "";
  state.queryParams.code = "";
  nextTick(() => resetQuery());
};

function handleSingleChange(val, scope) {
  console.log(val, scope, state.tableList);
  if (val) {
    clearCheck();
    scope.row.checked = true;
    state.dialogSelectedRow = scope.row;
    console.log("check", val);
  } else {
    state.dialogSelectedRow = {};
  }
}

function clearCheck() {
  state.tableList = state.tableList.map((item) => {
    item.checked = false;
    return item;
  });
}

function rowClick(row) {
  console.log("rowClick", row);
  const idx = state.tableList.findIndex((_) => _.code == row.code);
  if (row.checked) {
    state.tableList[idx].checked = false;
    state.dialogSelectedRow = {};
  } else {
    clearCheck();
    state.tableList[idx].checked = true;
    state.dialogSelectedRow = row;
  }
}

// 编辑表单
function handleEdit() {
  state.initStock = state.spareInfo.stock;
  state.initConsumption = state.spareInfo.consumption;
  state.isEdit = true;
}

//  保存表单
function handleSave() {
  state.spareRef.validate(async (valid) => {
    if (valid) {
      proxy.$modal.loading();
      updateSpareParts(state.spareInfo)
        .then((response) => {
          proxy.$modal.msgSuccess("保存成功");
          proxy.$modal.closeLoading();
          getData();
          state.isEdit = false;
        })
        .catch(() => proxy.$modal.closeLoading());
    }
  });
}

// 现库存判断
function getExpendSum(val) {
  if (val < state.initStock) {
    state.spareInfo.consumption =
      state.initConsumption + (state.initStock - val);
  } else {
    state.spareInfo.consumption = state.initConsumption;
  }
}
// 总消耗量判断
function getConSume(val){
  const consume = val - state.initConsumption //获取增加的消耗量
  console.log(consume,'consume');
  if(consume <= state.initStock){
    state.spareInfo.stock = state.initStock - consume
  }else{
    spareRef.value.validateField('consumption');
  }
}

// 弹窗取消按钮
function handleCancel() {
  state.dialogFormRef.resetFields();
  state.dialogSelectedRow = {};
  state.dialogVisible = false;
  state.showRelaTable = false;
}

// 弹窗关联单据按钮
function handleRelate() {
  const { code, id, num } = state.dialogSelectedRow;
  state.form.associatedCode = code;
  state.form.associatedId = id;
  state.form.num = num;
  state.showRelaTable = false;
}

// 页面表格列表数据获取/分页
async function getList(val) {
  if (val == 0) {
    state.loadingCrk = true;
    await sparePartsEntryAndRecordPage({
      ...state.queryParamsCrk,
      id: route.query.id,
    })
      .then((response) => {
        console.log("出入库记录", response.data);
        if (response.code == 200) {
          const { records, total } = response.data;
          state.crkList = records || [];
          state.crkTotal = total || 0;
        }
        state.loadingCrk = false;
      })
      .catch(() => (state.loadingCrk = false));
  }
  if (val == 1) {
    state.loadingTask = true;
    await sparePartsWorkOrderPage({
      ...state.queryParamsTask,
      id: route.query.id,
    })
      .then((response) => {
        console.log("关联工单记录", response.data);
        if (response.code == 200) {
          const { records, total } = response.data;
          state.taskList = records || [];
          state.taskTotal = total || 0;
        }
        state.loadingTask = false;
      })
      .catch(() => (state.loadingTask = false));
  }
  if (val == 2) {
    state.loadingEvent = true;
    await sparePartsEmergencyPage({
      ...state.queryParamsEvent,
      id: route.query.id,
    })
      .then((response) => {
        console.log("应急事件记录", response.data);
        if (response.code == 200) {
          const { records, total } = response.data;
          state.eventList = records || [];
          state.eventTotal = total || 0;
        }
        state.loadingEvent = false;
      })
      .catch(() => (state.loadingEvent = false));
  }
}

// 获取关联单据表格数据/单据表分页
function getRelateList() {
  state.loadingDialog = true;
  if (state.form.source == 1) {
    state.queryParams.workOrderCode = state.queryParams.code;
    console.log("queryParams", state.queryParams);
    deviceMaintenanceList(state.queryParams).then((response) => {
      if (response.code == 200) {
        console.log("工单列表", response);
        const { total, maintenanceWebListVOList } = response.data || {};
        state.tableList = (maintenanceWebListVOList || []).reduce(
          (res, cur) => {
            res.push({
              id: cur.troubleId,
              code: cur.workOrderCode,
              createTime: cur.submittedTime,
              num: 1,
              checked: state.dialogSelectedRow.code == cur.workOrderCode,
            });
            return res;
          },
          []
        );
        state.total = total || 0;
        state.loadingDialog = false;
      }
    });
  }
  if (state.form.source == 2) {
    state.queryParams.workOrderCode = state.queryParams.code;
    console.log("queryParams", state.queryParams);
    schoolPlanWorkOrderList(state.queryParams).then((response) => {
      console.log("应急事件列表", response);
      if (response.code == 200) {
        const { total, records } = response.data || {};
        state.tableList = (records || []).reduce((res, cur) => {
          res.push({
            id: cur.id,
            code: cur.code,
            createTime: cur.createTime,
            num: 1,
            checked: state.dialogSelectedRow.code == cur.code,
          });
          return res;
        }, []);
        state.total = total || 0;
        state.loadingDialog = false;
      }
    });
  }
}

/** 弹窗搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getRelateList();
}

/** 弹窗重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  dialogSelectedRow.value = {};
  handleQuery();
}

// 页面表格删除操作
function handleDelete({ row, $index }, val) {
  let name = ["出入库记录", "关联工单", "应急事件"][val];
  proxy.$modal
    .confirm(`确定要删除单据编号为${row.code}的${name}？`)
    .then(() => {
      proxy.$modal.loading();
      delSparePartsInfo(row.id)
        .then((resp) => {
          if (resp.code == 200) {
            proxy.$modal.msgSuccess("删除成功");
            proxy.$modal.closeLoading();
            getData();
          }
        })
        .catch(() => proxy.$modal.closeLoading());
    });
}

// 页面表格查看详情操作
function handleDetail({ row, $index }, val) {
  if (row.resource == "关联工单") {
    repairInfo({ troubleId: row.associatedId, type: 1 }).then((res) => {
      console.log(res);
      router.push({
        path: state.statusObj[res.data?.repairVO?.status].path,
        query: {
          id: row.associatedId,
          spareId: route.query.id,
        },
      });
    });
  }
  if (row.resource == "关联应急" || row.resource == "应急事件") {
    router.push({
      path: "/emergencyManage/workOrderDetail",
      query: {
        id: row.associatedId,
        spareId: route.query.id,
      },
    });
  }
}

function handleBack() {
  if (isDevice.value) {
    proxy.$tab.closeOpenPage(
      `/deviceLedger/deviceInfo?id=${route.query.deviceId}`
    );
  } else if (isChangeReocrd.value) {
    proxy.$tab.closeOpenPage("/system/changeRecord");
  } else {
    proxy.$tab.closeOpenPage("/spareManage/spare");
  }
  // router.go(-1);
  // proxy.$tab.closeOpenPage();
}

async function getData() {
  state.loadingPage = true;

  await sparePartsInfo(route.query.id)
    .then((response) => {
      if (response.code == 200) {
        console.log(response);
        state.spareInfo = response.data;
        state.loadingPage = false;
      }
    })
    .catch(() => (state.loadingPage = false));

  await sparePartsCategoryPage({
    pageNum: 1,
    pageSize: 999999,
    status: 1,
  }).then((response) => {
    if (response.data) {
      console.log(response, state.spareInfo);
      state.typeList = response.data.records;
      const idx = state.typeList.findIndex(
        (_) => _.id == state.spareInfo.typeId
      );
      if (idx == -1) {
        state.typeList.push({
          id: state.spareInfo.typeId,
          name: state.spareInfo.typeName,
          disabled: true,
        });
      }
    }
  });
  getList(0);
  getList(1);
  getList(2);
}
/* 校验总消耗量 */
function validConsume(rule, value, callback, index){
  if(value < state.initConsumption){
    callback(new Error("总消耗量不能小于原来的量"));
  }
  else if(value - state.initConsumption> state.initStock){
    callback(new Error("现有库存不足以支撑消耗"));
  }
  else {
    callback();
  }
};

onMounted(async () => {
  await getData();
  if (route.query.type == 1) {
    handleEdit();
  }
});
</script>

<style lang="scss" scoped>
.spareInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  .pagination-container {
    padding: 0 !important;
    margin-top: 15px;
    :deep(.el-pagination) {
      justify-content: center;
    }
  }
  &-title {
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-weight: bold;
    margin-bottom: 15px;
  }
  &-main {
    padding: 10px 0 20px 30px;
  }
  &-main2 {
    padding: 0 20px 20px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
</style>
