<!-- views/emergency/workOrder.vue -->
<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>应急工单中心</span>
        </div>
      </template>
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item>
          <el-input
            v-model.trim="queryParams.workOrderCode"
            placeholder="请输入应急工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-input
            v-model.trim="queryParams.peopleName"
            placeholder="请输入关联人员"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-select
            v-model="queryParams.status"
            style="width: 200px"
            placeholder="请选择工单状态"
            clearable
            @change="handleSearch"
          >
            <el-option label="待上传" value="0" />
            <el-option label="已归档" value="1" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-button
        class="mb12"
        plain
        icon="Plus"
        type="primary"
        @click="dialogVisible = true"
        >创建应急工单</el-button
      >
      <el-table v-loading="loading" :data="tableData" border>
        <el-table-column
          prop="orderId"
          label="应急工单编号"
          show-overflow-tooltip
          min-width="110"
        />
        <!-- <el-table-column
          prop="orderType"
          label="应急工单类别"
          show-overflow-tooltip
          min-width="110"
        >
          <template #default="scope">
            {{ getOrderTypeName(scope.row.orderType) }}
          </template>
        </el-table-column> -->
        <el-table-column
          prop="description"
          label="应急工单描述"
          show-overflow-tooltip
          min-width="150"
        />
        <el-table-column
          prop="relatedPerson"
          label="关联人员"
          show-overflow-tooltip
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.relatedPerson || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="relatedParts"
          label="关联备件"
          show-overflow-tooltip
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.relatedParts || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="emergencyPlan"
          label="应急预案"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.emergencyPlan || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="report"
          label="处理报告"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="scope">
            {{ scope.row.reportFileName || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          prop="status"
          label="工单状态"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ getStatusName(scope.row.status) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="createTime"
          label="创建时间"
          show-overflow-tooltip
          min-width="150"
        >
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
        <el-table-column
          prop="handleTime"
          label="处理时间"
          show-overflow-tooltip
          min-width="150"
        >
          <template #default="scope">
            {{ scope.row.handleTime ? formatDate(scope.row.handleTime) : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          show-overflow-tooltip
          min-width="300"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <!-- 查看按钮：始终显示 -->
            <el-button
              link
              type="primary"
              @click="handleView(scope.row)"
            >
              查看
            </el-button>

            <!-- 以下按钮仅在当前记录的 permissions 为 1 时显示 -->
            <template v-if="scope.row.permissions === 1">
              <!-- 处理按钮：待处理状态显示 -->
              <el-button
                v-if="scope.row.status === 'pending'"
                link
                type="primary"
                @click="handleProcess(scope.row)"
              >
                处理
              </el-button>

              <!-- 归档按钮：待处理状态显示 -->
              <el-button
                v-if="scope.row.status === 'pending'"
                link
                type="primary"
                @click="handleArchive(scope.row)"
              >
                归档
              </el-button>

              <!-- 上传报告按钮：待处理和待上传状态显示，且reportFileName为空时显示 -->
              <el-button
                v-if="
                  ['pending', 'waiting_upload'].includes(scope.row.status) &&
                  !scope.row.reportFileName
                "
                link
                type="primary"
                @click="handleUpload(scope.row)"
              >
                上传报告
              </el-button>

              <!-- 一键通知按钮：只在工单状态为待处理且未通知过且有关联人员时显示 -->
              <el-button
                v-if="scope.row.status === 'pending' && !scope.row.notifyTime && scope.row.relatedPerson !== null"
                link
                type="primary"
                @click="handleNotify(scope.row)"
              >
                一键通知
              </el-button>
            </template>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加通知确认对话框 -->
    <el-dialog
      v-model="notifyDialogVisible"
      title="通知确认"
      width="500"
      :close-on-click-modal="false"
    >
      <div class="notify-content">
        <p>是否确认通知以下关联人员？</p>
        <div class="personnel-list" v-if="currentNotifyPersonnel.length > 0">
          <p v-for="(person, index) in currentNotifyPersonnel" :key="index">
            {{ person.name }}
          </p>
        </div>
        <div v-else class="no-personnel">
          <el-alert
            title="当前工单暂无关联人员"
            type="warning"
            :closable="false"
          />
        </div>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="notifyDialogVisible = false">取消</el-button>
          <el-button
            type="primary"
            @click="confirmNotify"
            :disabled="currentNotifyPersonnel.length === 0"
          >
            确定
          </el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 新建工单对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="dialogVisible"
      title="请选择应急预案"
      width="600"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-table 
        :data="caseList" 
        border
        ref="caseTableRef"
        @row-click="handleRowClick"
      >
        <el-table-column align="center" width="55">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="(val) => handleChangeCheck(val, scope.$index)"
              @click.native.stop
            />
          </template>
        </el-table-column>
        <el-table-column
          label="预案编号"
          prop="code"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          label="预案名称"
          prop="name"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="预案内容"
          prop="content"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          min-width="150"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ formatDate(scope.row.createTime) }}
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="caseTotal > 0"
        :total="caseTotal"
        v-model:page="caseQueryParams.pageNum"
        v-model:limit="caseQueryParams.pageSize"
        @pagination="getCaseList"
        :background="false"
        :autoScroll="false"
        layout="total,prev,pager,next"
      />
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="handleSubmit">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传报告对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传报告"
      width="400"
      :close-on-click-modal="false"
    >
      <div class="upload-content">
        <el-upload
          class="upload-demo"
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".doc,.docx,.xls,.xlsx"
        >
          <el-button type="primary">点击上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只允许上传word、excel格式文件，大小不超过20M
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpload">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="emergencyTaskCenter">
import { ref, onMounted, watch, reactive, toRefs } from "vue";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { debounce } from "@/utils";
import { schoolPlanList, schoolPlanWorkOrderList, uploadSchoolPlanWorkOrder, schoolPlanWorkOrderNotice } from "@/api/emergency";

const router = useRouter();
const route = useRoute();

// 统一的状态管理
const state = reactive({
  dialogVisible: false,
  tableData: [],
  total: 0,
  caseList: [],
  caseTotal: 0,
  formRef: null,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    workOrderCode: '',
    peopleName: '',
    status: ''
  },
  loading: false,
  form: {
    orderId: "",
    orderType: "",
    description: "",
  },
  // 通知相关
  notifyDialogVisible: false,
  currentNotifyWorkOrder: null,
  currentNotifyPersonnel: [],
  // 应急预案相关
  caseQueryParams: {
    pageNum: 1,
    pageSize: 5
  },
  caseTableRef: null,
  selectedCase: null,
  // 上传相关
  uploadDialogVisible: false,
  currentUploadRow: null,
  fileList: [],
  // 添加新的响应式变量
  isAllSelected: ref(false),
  isIndeterminate: ref(false),
  permissions: 0, // 默认为0，表示只有查看权限
});

// 解构需要的响应式状态
const {
  dialogVisible,
  tableData,
  total,
  caseList,
  caseTotal,
  formRef,
  queryParams,
  loading,
  form,
  notifyDialogVisible,
  currentNotifyWorkOrder,
  currentNotifyPersonnel,
  caseQueryParams,
  caseTableRef,
  selectedCase,
  uploadDialogVisible,
  currentUploadRow,
  fileList,
  isAllSelected,
  isIndeterminate,
  permissions,
} = toRefs(state);

const getList = async () => {
  try {
    state.loading = true;
    const res = await schoolPlanWorkOrderList(state.queryParams);
    if (res.code === 200) {
      state.loading = false;
      console.log('应急工单',res.data.records);
      
      state.tableData = res.data.records.map(item => ({
        id: item.id,
        orderId: item.code,
        description: item.remark,
        relatedPerson: item.nickName,
        relatedParts: item.sparePartsName,
        emergencyPlan: item.planName || '-',
        report: item.reportFileName,
        status: item.status == 0 ? 'pending' : 
               item.status == 1 ? 'archived' : 'pending',
        createTime: item.createTime,
        handleTime: item.processingTime,
        reportFileName: item.reportFileName,
        notifyTime: null,
        permissions: item.permissions,
      }));
      state.total = res.data.total;
    }
  } catch (error) {
    state.loading = false;
    // console.error("获取数据失败:", error);
    // ElMessage.error("获取数据失败");
  }
};

const handleAdd = () => {
  router.push("addTask");
};

// 获取工单状态名称
const getStatusName = (status) => {
  const statusMap = {
    pending: "待上传",
    archived: "已归档"
  };
  return statusMap[status] || status;
};

// 获取工单类别名称
const getOrderTypeName = (type) => {
  const typeMap = {
    equipment: "设备故障",
    system: "系统异常",
    safety: "安全事故",
    other: "其他",
  };
  return typeMap[type] || type;
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 获取表格数据
const getTableData = () => {
  try {
    const workOrderStr = localStorage.getItem("emergencyWorkOrders");
    if (workOrderStr) {
      state.tableData = JSON.parse(workOrderStr);
    }
  } catch (error) {
    // console.error("获取数据失败:", error);
    // ElMessage.error("获取数据失败");
  }
};

// 检查工单编号是否存在
const checkOrderIdExists = (orderId) => {
  const workOrders = JSON.parse(
    localStorage.getItem("emergencyWorkOrders") || "[]"
  );
  return workOrders.some((order) => order.orderId === orderId);
};

// 提交表单
const handleSubmit = () => {
  if (!state.selectedCase) {
    ElMessage.warning('请选择一个应急预案');
    return;
  }
  router.push({
    path: '/emergencyManage/addTask',
    query: {
      planId: state.selectedCase.id
    }
  });
};

// 查看工单
const handleView = (row) => {
  // 保存当前页码到localStorage
  localStorage.setItem('emergencyTaskCenterPage', queryParams.value.pageNum);
  
  router.push({
    path: '/emergencyManage/workOrderDetail',
    query: { id: row.id }
  });
};

// 处理工单
const handleProcess = (row) => {
  // 保存当前页码到localStorage
  localStorage.setItem('emergencyTaskCenterPage', queryParams.value.pageNum);
  
  router.push({
    path: "/emergencyManage/handle",
    query: {
      id: row.id,
      mode: "process",
    },
  });
};

const handleQuery = () => {
  /* getSchoolPlanWorkOrderList(queryParams).then((res) => {
    if (res.code === 200) {
      tableData.value = res.data.rows;
      total.value = res.data.total;
    }
  }); */
};

// 上传报告
const handleUpload = (row) => {
  currentUploadRow.value = row;
  fileList.value = [];
  uploadDialogVisible.value = true;
};

// 一键通知处理函数
const handleNotify = (row) => {
  currentNotifyWorkOrder.value = row;
  // 如果有关联人员，显示通知对话框
  if (row.relatedPerson) {
    currentNotifyPersonnel.value = [{
      name: row.relatedPerson,
      // department: "-"
    }];
    notifyDialogVisible.value = true;
  } else {
    ElMessage.warning('该工单没有关联人员');
  }
};

// 确认通知
const confirmNotify = async () => {
  try {
    const res = await schoolPlanWorkOrderNotice({
      planWorkOrderId: currentNotifyWorkOrder.value.id
    });
    
    if (res.code === 200) {
      ElMessage.success('通知发送成功');
      notifyDialogVisible.value = false;
      currentNotifyWorkOrder.value = null;
      currentNotifyPersonnel.value = [];
      getList(); // 刷新表格数据
    } else {
      ElMessage.error(res.msg || '通知发送失败');
    }
  } catch (error) {
    console.error("通知发送失败:", error);
    ElMessage.error("通知发送失败");
  }
};


const handleSearch = debounce(() => {
  state.queryParams.pageNum = 1;
  getList();
}, 300);


const resetQuery = debounce(() => {
  state.queryParams = {
    pageNum: 1,
    pageSize: 10,
    workOrderCode: '',
    peopleName: '',
    status: ''
  };
  getList();
},300);

// 获取应急预案列表
const getCaseList = () => {
  schoolPlanList(state.caseQueryParams)
    .then(res => {
      if (res.code === 200) {
        state.caseList = res.data.records.map(item => ({
          ...item,
          checked: false // 添加 checked 属性
        }));
        state.caseTotal = res.data.total;
      }
    })
    .catch(error => {
      console.error("获取应急预案列表失败:", error);
      ElMessage.error("获取应急预案列表失败");
    });
};

// 处理单个选择变更
const handleChangeCheck = (val, index) => {
  if (val) {
    // 清除其他选中项
    state.caseList = state.caseList.map((item, idx) => {
      item.checked = idx === index;
      return item;
    });
  }
  // 更新选中的预案
  state.selectedCase = val ? state.caseList[index] : null;
};

// 行点击处理
const handleRowClick = (row) => {
  const idx = state.caseList.findIndex(item => item.id === row.id);
  if (idx > -1) {
    state.caseList[idx].checked = !state.caseList[idx].checked;
    if (state.caseList[idx].checked) {
      // 如果选中，清除其他选中项
      state.caseList.forEach((item, index) => {
        if (index !== idx) {
          item.checked = false;
        }
      });
    }
    // 更新选中的预案
    state.selectedCase = state.caseList[idx].checked ? state.caseList[idx] : null;
  }
};

// 修改对话框关闭时的处理
watch(() => dialogVisible.value, (newVal) => {
  if (newVal) {
    getCaseList();
  } else {
    state.caseList = state.caseList.map(item => {
      item.checked = false;
      return item;
    });
    state.selectedCase = null;
  }
});

// 上传相关的响应式变量
const uploadAction = import.meta.env.VITE_APP_BASE_API + '/emergency/schoolPlan/uploadSchoolPlanWorkOrder'; 

// 修改上传前的验证函数
const beforeUpload = (file) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split('.').pop();
  const allowedExtensions = ['doc', 'docx', 'xls', 'xlsx'];
  
  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error('只能上传Word或Excel格式的文件!');
    return false;
  }
  if (!isLt20M) {
    ElMessage.error('文件大小不能超过20MB!');
    return false;
  }
  return true;
};

// 修改文件改变处理函数
const handleFileChange = (file, uploadFileList) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split('.').pop();
  const allowedExtensions = ['doc', 'docx', 'xls', 'xlsx'];
  
  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error('只能上传Word或Excel格式的文件!');
    fileList.value = uploadFileList.filter(f => 
      allowedExtensions.includes(f.name.toLowerCase().split('.').pop())
    );
    return false;
  }
  if (!isLt20M) {
    ElMessage.error('文件大小不能超过20MB!');
    fileList.value = uploadFileList.filter(f => 
      f.size / 1024 / 1024 < 20
    );
    return false;
  }

  fileList.value = uploadFileList;
};

// 修改确认上传函数
const confirmUpload = async () => {
  if (!state.fileList.length) {
    ElMessage.warning('请先上传文件');
    return;
  }

  try {
    // 先检查是否已存在记录
    const checkParams = {
      planWorkOrderId: currentUploadRow.value.id
    };
    
    const formData = new FormData();
    formData.append('file', state.fileList[0].raw);
    formData.append('planWorkOrderId', currentUploadRow.value.id);
    formData.append('type', '5');
    // 添加更新标识
    // formData.append('isUpdate', 'true');

    const res = await uploadSchoolPlanWorkOrder(formData);
    
    if (res.code === 200) {
      ElMessage.success('报告上传成功');
      uploadDialogVisible.value = false;
      state.fileList = []; // 清空文件列表
      getList(); // 刷新列表
    } else {
      ElMessage.error(res.msg || '上传失败');
    }
  } catch (error) {
    // console.error('上传失败:', error);
    // ElMessage.error('上传失败，可能是由于记录已存在');
  }
};

// 归档工单
const handleArchive = (row) => {
  // 保存当前页码到localStorage
  localStorage.setItem('emergencyTaskCenterPage', queryParams.value.pageNum);
  
  router.push({
    path: '/emergencyManage/archive',
    query: {
      id: row.id
    }
  });
};

// 添加路由参数监听
watch(
  () => route.query,
  (query) => {
    if (query.pageNum) {
      queryParams.pageNum = Number(query.pageNum);
      getList();
    }
  },
  { immediate: true }
);

onMounted(() => {
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem('emergencyTaskCenterPage');
  if (savedPage) {
    queryParams.value.pageNum = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem('emergencyTaskCenterPage');
  }
  getList();
});
</script>

<style lang="scss" scoped>
// 添加单选样式
:deep(.el-table-column--selection .cell) {
  padding-right: 14px;
  padding-left: 14px;
}

.el-table {
  :deep(.current-row) {
    background-color: var(--el-table-row-hover-bg-color) !important;
  }
}

.upload-content {
  padding: 20px;
  
  .el-upload__tip {
    margin-top: 10px;
    color: #909399;
  }
}
</style>