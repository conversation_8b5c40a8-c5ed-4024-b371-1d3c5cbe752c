import { ElMessage } from "element-plus";
import modal from "@/plugins/modal";
import { sendPointRequestBatch } from "@/utils";
import {
  deviceStartByWOL,
  addSchoolDeviceLog,
  deviceCtl,
  deviceCtlMqtt,
} from "@/api/deviceControl";

/** 批量操作前的检测 */
export const checkBatch = (props) => {
  let newArr = [];
  props.tableAllSelectedId?.map((item) => {
    props.tableAllSelectedRow?.map((item2) => {
      if (item2.deviceId == item) {
        newArr?.push(item2);
      }
    });
  });
  let arr = [],
    flag = false;
  let narr = [],
    recoverArr = [];
  newArr?.map((item) => {
    if (item.runStatus?.indexOf(0) != -1) {
      // 关机
      narr?.push(item.deviceName);
      flag = true;
    } else if (item.winStatus > 1) {
      recoverArr?.push(item.deviceName);
    } else {
      arr?.push(item.deviceName);
    }
  });
  return { arr, narr, flag, recoverArr };
};

export function getUrlIp(ip, url = "") {
  let str = "http";
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `http://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2.replace("s", "").replace("http://", "");
  } else {
    ip2 = `http://` + ip2;
  }
  return { ip1, ip2, url1, url2 };
}

/** 解锁/锁屏 type: 0锁屏 1解锁*/
export const handleLock = async (row, type) => {
  try {
    modal.loading();
    let { ip1 } = getUrlIp(row.ipAddress);
    let obj = {
      method: "get",
      uri: ip1 + `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
      content: "",
    };
    let res = null;
    if (row.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
        deviceCodeList: row.deviceCode,
      };
      res = await deviceCtlMqtt(obj);
    } else {
      res = await deviceCtl(obj);
    }
    console.log("传参", obj);
    if (res.code == 200) {
      ElMessage.closeAll();
      modal.msgSuccess("操作成功");
    }
    modal.closeLoading();
  } catch (e) {
    modal.closeLoading();
  }
};

export const handleClear = async (row) => {
  try {
    modal.loading();
    let { ip1 } = getUrlIp(row.ipAddress);
    let obj = {
      method: "get",
      uri: ip1 + `/api/Cockpit/ClearMemory`,
      content: "",
    };
    let res = null;
    if (row.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/ClearMemory`,
        deviceCodeList: row.deviceCode,
      };
      console.log("传参", obj);
      res = await deviceCtlMqtt(obj);
    } else {
      console.log("传参", obj);
      res = await deviceCtl(obj);
    }
    if (res.code == 200) {
      ElMessage.closeAll();
      modal.msgSuccess("操作成功");
    }
    modal.closeLoading();
  } catch (e) {
    console.log(e);
    modal.closeLoading();
  }
};

export const handleWifi = async (row, type) => {
  try {
    modal.loading();
    let { ip1 } = getUrlIp(row.ipAddress);
    let obj = {
      method: "get",
      uri: ip1 + `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
      content: "",
    };
    let res = null;
    if (row.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
        deviceCodeList: row.deviceCode,
      };
      res = await deviceCtlMqtt(obj);
    } else {
      res = await deviceCtl(obj);
    }
    console.log("传参", obj);
    if (res.code == 200) {
      ElMessage.closeAll();
      modal.msgSuccess("操作成功");
    }
    modal.closeLoading();
  } catch (e) {
    console.log(e);
    modal.closeLoading();
  }
};

export const handleRemote = async (row, type, isTime) => {
  if (!!isTime) {
    // 操作单个设备的定时关机
    try {
      let sendData = {
        userEvents: [
          {
            event: "Click",
            eventDescribe: "点击单个设备操作集控按钮",
            content: 3,
            num: 1,
            deviceCode: row.deviceCode,
          },
        ],
      };
      console.log(sendData, "单个埋点");
      sendPointRequestBatch(sendData);
      let res = await addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: row.deviceCode, // 操作设备编号
        logContent: `远程定时关机`, // 日志内容
        deviceNum: 1,
      });
      console.log(res, "操作记录");
    } catch (error) {}
  }

  try {
    modal.loading();

    let { ip1 } = getUrlIp(row.ipAddress);
    let data = { cmd: type === 3 ? 0 : type };
    if (type == 3) {
      data.time = row.time;
    } else {
      delete data.time;
    }
    let obj = {
      method: "post",
      uri: ip1 + `/api/Cockpit/Control`,
      content: JSON.stringify(data),
    };
    console.log("关机/重启/定时关机传参", obj);
    let res = null;
    if (row.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/Control`,
        deviceCodeList: row.deviceCode,
      };
      res = await deviceCtlMqtt(obj);
    } else {
      res = await deviceCtl(obj);
    }
    console.log("传参", obj);
    if (res.code == 200) {
      ElMessage.closeAll();
      modal.msgSuccess("操作成功");
    }
    modal.closeLoading();
  } catch (e) {
    console.log(e);
    modal.closeLoading();
  }
};

/** 批量开机按钮操作 */
export const handleBatchStart = async (props = {}) => {
  if (props?.tableAllSelectedId?.length < 1) {
    modal.msgWarning("请选择设备");
    return;
  }

  const { arr, narr, flag, recoverArr } = checkBatch(props);

  if (arr.length > 0 || recoverArr.length > 0) {
    modal.msgWarning("设备存在开机状态，无法进行此操作");
    return;
  }
  modal
    .confirm(`是否确认批量开机设备名称为【${narr?.join("、")}】的设备？`)
    .then(() => {
      modal
        .confirm("请确认设备是否支持远程开机")
        .then(async () => {
          let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
          let sendData = {
            userEvents: rows?.map((item) => {
              return {
                event: "Click",
                eventDescribe: "选中设备后点击下方悬浮按钮集控",
                content: 0,
                num: 1,
                deviceCode: item.deviceCode,
              };
            }),
          };
          // console.log(sendData, "批量埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
            logContent: `远程开机`, // 日志内容
            deviceNum: rows.length, // 操作设备数量
          });
          let success = [],
            fail = [];
          for (let j = 0; j < rows.length; j++) {
            try {
              modal.loading();
              await deviceStartByWOL({ deviceCode: rows[j].deviceCode })
                .then((res) => {
                  console.log(res);
                  success?.push(rows[j].deviceCode);
                })
                .catch(() => {
                  fail?.push(rows[j].deviceCode);
                })
                .finally(() => {
                  modal.closeLoading();
                });
            } catch (error) {
              console.log(error);
              modal.closeLoading();
            }
          }
          modal.msgSuccess(
            fail.length == 0
              ? "操作成功"
              : `开机指令共发送${rows.length}条，成功${success.length}条，失败${
                  fail.length
                }条，指令发送失败设备：${fail?.join("、")}`
          );
        })
        .catch((err) => {});
    })
    .catch((err) => {});
};

/** 批量定时关机提交数据 */
export const handleBatchClockOff = async (props = {}) => {
  // 批量定时关机
  let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
  let sendData = {
    userEvents: rows?.map((item) => {
      return {
        event: "Click",
        eventDescribe: "选中设备后点击下方悬浮按钮集控",
        content: 3,
        num: 1,
        deviceCode: item.deviceCode,
      };
    }),
  };
  console.log(sendData, "批量埋点");
  sendPointRequestBatch(sendData);
  addSchoolDeviceLog({
    logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
    deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
    logContent: `远程定时关机`, // 日志内容
    deviceNum: rows.length, // 操作设备数量
  });
  for (let j = 0; j < rows.length; j++) {
    try {
      handleRemote(
        {
          ipAddress: rows[j].ipAddress,
          time: type === 3 ? fixForm.value.time : null,
          deviceCode: rows[j].deviceCode,
          isMqtt: rows[j].isMqtt,
        },
        type
      );
    } catch (error) {}
  }
};

/** 批量清除缓存 */
export const handleBatchClear = (isBatch, row = {}, props = {}) => {
  if (props.tableAllSelectedId?.length < 1) {
    modal.msgWarning("请选择设备");
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch(props);
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }

  if (!!isBatch) {
    modal
      .confirm(
        "是否确认批量清除设备名称为【" + arr?.join("、") + "】的设备缓存？"
      )
      .then(async function () {
        let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
        let sendData = {
          userEvents: rows?.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: 6,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
          logContent: `远程清除缓存`, // 日志内容
          deviceNum: rows.length, // 操作设备数量
        });
        for (let j = 0; j < rows.length; j++) {
          try {
            handleClear({
              ipAddress: rows[j].ipAddress,
              deviceCode: rows[j].deviceCode,
              isMqtt: rows[j].isMqtt,
            });
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    modal
      .confirm("是否确认清除设备名称为【" + row.deviceName + "】的设备缓存？")
      .then(async function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: 6,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `远程清除缓存`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleClear(row);
        } catch (error) {}
      })
      .catch(() => {});
  }
};

/** 批量开启/关闭 Wifi */
export const handleBatchWifi = (isBatch, type, row = {}, props = {}) => {
  if (props.tableAllSelectedId?.length < 1) {
    modal.msgWarning("请选择设备");
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch(props);
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }

  if (!!isBatch) {
    modal
      .confirm(
        `是否确认批量${!!type ? "开启" : "关闭"}设备名称为【${arr?.join(
          "、"
        )}】的设备wifi？`
      )
      .then(async function () {
        let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
        let sendData = {
          userEvents: rows?.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: `${!!type ? 4 : 5}`,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
          logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
          deviceNum: rows.length, // 操作设备数量
        });
        for (let j = 0; j < rows.length; j++) {
          try {
            handleWifi(
              {
                ipAddress: rows[j].ipAddress,
                deviceCode: rows[j].deviceCode,
                isMqtt: rows[j].isMqtt,
              },
              type
            );
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    modal
      .confirm(
        `是否确认${!!type ? "开启" : "关闭"}设备名称为【${
          row.deviceName
        }】的设备wifi？`
      )
      .then(async function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: `${!!type ? 4 : 5}`,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleWifi(row, type);
        } catch (error) {}
      })
      .catch(() => {});
  }
};

/** 批量重启/关闭/定时关闭按钮操作 */
export const handleBatchRemote = async (
  isBatch,
  type,
  row = {},
  props = {}
) => {
  if (props.tableAllSelectedId?.length < 1) {
    modal.msgWarning("请选择设备");
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, narr, flag, recoverArr } = checkBatch(props);

  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }

  if (type !== 3) {
    if (!!isBatch) {
      modal
        .confirm(
          `是否确认批量${!!type ? "重启" : "关闭"}设备名称为【${arr?.join(
            "、"
          )}】的设备？`
        )
        .then(async function () {
          let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
          let sendData = {
            userEvents: rows?.map((item) => {
              return {
                event: "Click",
                eventDescribe: "选中设备后点击下方悬浮按钮集控",
                content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
                num: 1,
                deviceCode: item.deviceCode,
              };
            }),
          };
          console.log(sendData, "批量埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
            logContent: `远程${
              type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
            }`, // 日志内容
            deviceNum: rows.length, // 操作设备数量
          });
          for (let j = 0; j < rows.length; j++) {
            try {
              handleRemote(
                {
                  ipAddress: rows[j].ipAddress,
                  time: type === 3 ? fixForm.value.time : null,
                  deviceCode: rows[j].deviceCode,
                  isMqtt: rows[j].isMqtt,
                },
                type
              );
            } catch (error) {}
          }
        })
        .catch(() => {});
    } else {
      modal
        .confirm(
          `是否确认${!!type ? "重启" : "关闭"}设备名称为【
            ${row.deviceName}】的设备？`
        )
        .then(async function () {
          try {
            let sendData = {
              userEvents: [
                {
                  event: "Click",
                  eventDescribe: "点击单个设备操作集控按钮",
                  content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
                  num: 1,
                  deviceCode: row.deviceCode,
                },
              ],
            };
            console.log(sendData, "单个埋点");
            sendPointRequestBatch(sendData);
            addSchoolDeviceLog({
              logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: row.deviceCode, // 操作设备编号
              logContent: `远程${
                type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
              }`, // 日志内容
              deviceNum: 1, // 操作设备数量
            });
            handleRemote(row, type);
          } catch (error) {}
        })
        .catch(() => {});
    }
  } else {
    // 批量定时关机
    let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
    let sendData = {
      userEvents: rows?.map((item) => {
        return {
          event: "Click",
          eventDescribe: "选中设备后点击下方悬浮按钮集控",
          content: 3,
          num: 1,
          deviceCode: item.deviceCode,
        };
      }),
    };
    console.log(sendData, "批量埋点");
    sendPointRequestBatch(sendData);
    addSchoolDeviceLog({
      logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
      deviceCode: rows?.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
      logContent: `远程定时关机`, // 日志内容
      deviceNum: rows.length, // 操作设备数量
    });
    for (let j = 0; j < rows.length; j++) {
      try {
        handleRemote(
          {
            ipAddress: rows[j].ipAddress,
            time: type === 3 ? props.time : null,
            deviceCode: rows[j].deviceCode,
            isMqtt: rows[j].isMqtt,
          },
          type
        );
      } catch (error) {}
    }
  }
};

/** 批量解锁/锁屏 type: 0锁屏 1解锁*/
export const handleBatchLock = (isBatch, type, row = {}, props = {}) => {
  if (props.tableAllSelectedId?.length < 1) {
    modal.msgWarning("请选择设备");
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch(props);
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  if (!!isBatch) {
    modal
      .confirm(
        `是否确认批量对设备名称为【${arr?.join("、")}】的设备进行${
          type == 0 ? "锁屏" : "解锁"
        }？`
      )
      .then(function () {
        let rows = JSON.parse(JSON.stringify(props.tableAllSelectedRow));
        let sendData = {
          userEvents: rows?.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: `${type == 0 ? 7 : 8}`,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        for (let j = 0; j < rows.length; j++) {
          if (type == 1) {
            addSchoolDeviceLog({
              logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: rows[j].deviceCode, // 操作设备编号
              logContent: `设备后台解锁`, // 日志内容
              deviceNum: 1, // 操作设备数量
            });
          }
          try {
            handleLock(
              {
                ipAddress: rows[j].ipAddress,
                deviceCode: rows[j].deviceCode,
                isMqtt: rows[j].isMqtt,
              },
              type
            );
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    modal
      .confirm(
        `是否确认对设备名称为【${row.deviceName}】的设备进行${
          type == 0 ? "锁屏" : "解锁"
        }？`
      )
      .then(function () {
        try {
          let sendData = {
            userEvents: [
              {
                event: "Click",
                eventDescribe: "点击单个设备操作集控按钮",
                content: `${type == 0 ? 7 : 8}`,
                num: 1,
                deviceCode: row.deviceCode,
              },
            ],
          };
          console.log(sendData, "单个埋点");
          sendPointRequestBatch(sendData);
          addSchoolDeviceLog({
            logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: row.deviceCode, // 操作设备编号
            logContent: `设备后台${type == 0 ? "锁屏" : "解锁"}`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
          handleLock(row, type);
        } catch (error) {}
      })
      .catch(() => {});
  }
};
