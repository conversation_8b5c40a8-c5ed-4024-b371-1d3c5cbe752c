<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <div class="echarts-box">
                <div class="item row">
                    <Echarts @setFontSize="setFontSize" id="glxsbsl" width="100%" height="100%" :fullOptions="glxsbslOption"
                        :loading="false" />
                </div>
                <div class="item row" style="padding: 8vh 3vw 2vh">
                    <div class="title">各教室设备配备</div>
                    <el-scrollbar height="33vh" style="height: 100%;" always>
                        <div class="row-opt">
                            <div class="class" v-for="(item, index) in deviceEquip" :key="index">
                                <div class="block" :style="{ backgroundColor: '#a2ef4d' }">
                                </div>
                                {{ item.name }}
                            </div>
                        </div>
                    </el-scrollbar>
                </div>
                <div class="item item3">
                    <div class="title">班均出口带宽</div>
                    <div class="item3-opt opt1">
                        <div class="icon">
                            <Monitor />
                        </div>
                        <div class="info">
                            <div class="name">班均出口带宽</div>
                            <div class="value">{{ deviceConfig.network }}</div>
                        </div>
                    </div>
                    <div class="item3-opt opt2">
                        <div class="icon">
                            <Top />
                        </div>
                        <div class="info">
                            <div class="name">上行网速</div>
                            <div class="value">{{ deviceConfig.networkUpload }}</div>
                        </div>
                    </div>
                    <div class="item3-opt opt3">
                        <div class="icon">
                            <Bottom />
                        </div>
                        <div class="info">
                            <div class="name">下行网速</div>
                            <div class="value">{{ deviceConfig.networkDownload }}</div>
                        </div>
                    </div>
                </div>
                <div class="item">
                    <Echarts @setFontSize="setFontSize" @getClickIdx="getClickIdx" id="zblyl" width="100%" height="100%"
                        :fullOptions="zblylOption" :loading="false" />
                </div>
                <div class="item">
                    <Echarts @setFontSize="setFontSize" id="zbppfb" width="100%" height="100%" :fullOptions="zbppfbOption"
                        :loading="false" />
                </div>
            </div>
        </el-card>
        <el-dialog v-model="open" :title="`每日班级设备利用率详情（${name}）`" width="600px" style="height: 50vh;">
            <Echarts @setFontSize="setFontSize" id="bjzblyl" width="100%" height="35vh" :fullOptions="bjzblylOption"
                :loading="false" />
        </el-dialog>
    </div>
</template>

<script setup name="ledgerManage">
import Echarts from '@/components/Echarts/index.vue'
import { useRoute } from 'vue-router'
import { transformSize } from '@/utils'
import { onMounted } from 'vue';
import { queDeviceRate, queDeviceGradeRate, queNetwork, queBrandRation, queDeviceAddress, queTypeRation } from '@/api/mediaTeach/media'

const route = useRoute()
const { proxy } = getCurrentInstance();

let name = ref('')
let open = ref(false)

let deviceEquip = ref([
    { name: '一年级-1班' },
    { name: '一年级-2班' },
    { name: '一年级-3班' },
    { name: '一年级-4班' },
    { name: '一年级-5班' },
    { name: '二年级-1班' },
])

let deviceConfig = ref({
    network: '68.5M/s',
    networkDownload: '0.8kbps',
    networkUpload: '1.3kbps'
})

let glxsbslSData = [
    { name: '触控一体机', value: 1 },
]
let zbqkSData = [
    { name: '触控一体机', value: 1 },
    { name: '电子班牌', value: 5 },
]
let zbppfbSData = [
    { name: '其它', value: 1 },
]
let bjjspblSData = [
    { name: '配备率', value: 4 },
    { name: '未配备率', value: 8 },
]
let gnbmpblSData = [
    { name: '配备率', value: 2 },
    { name: '未配备率', value: 0 },
]
let zblylYData = ['4班', '3班', '2班', '1班']
let zblylSData = [1, 3, 1, 5]

let bjzblylYData = ['4班', '3班', '2班', '1班']
let bjzblylSData = [1, 3, 1, 5]

let zbyxscXData = ['电子班牌', '触控一体机', '投影仪', '其它']
let zbyxscSData = [
    {
        name: '时长',
        data: [10, 50, 20, 10],
        type: 'line',
    },
]

//各类型设备数量
const glxsbslOption = reactive({
    options: {
        title: {
            text: '各类型设备数量',
            textStyle: {
                fontSize: transformSize(25),
            },
            left: '2%',
            top: '4%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            bottom: '5%'
        },
        series: [
            {
                name: '各类型设备数量',
                type: 'pie',
                radius: ['45%', '60%'],
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
//装备情况
const zbqkOption = reactive({
    options: {
        title: {
            text: '装备情况',
            textStyle: {
                fontSize: transformSize(25),
            },
            left: '2%',
            top: '5%'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: '装备情况',
                type: 'pie',
                radius: '50%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
//装备品牌分布
const zbppfbOption = reactive({
    options: {
        title: {
            text: '装备品牌分布',
            textStyle: {
                fontSize: transformSize(25),
            },
            left: '2%',
            top: '5%'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: '分布比例',
                type: 'pie',
                radius: '50%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
//班级教室配备率
const bjjspblOption = reactive({
    options: {
        title: {
            text: '班级教室配备率',
            textStyle: {
                fontSize: transformSize(25),
            },
            left: '2%',
            top: '5%'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: '班级教室配备率',
                type: 'pie',
                color: ['#91cc75', '#e8e8e8'],
                radius: ['40%', '55%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
//功能部门配备率
const gnbmpblOption = reactive({
    options: {
        title: {
            text: '功能部门配备率',
            textStyle: {
                fontSize: transformSize(25),
            },
            left: '2%',
            top: '5%'
        },
        tooltip: {
            trigger: 'item'
        },
        series: [
            {
                name: '功能部门配备率',
                type: 'pie',
                color: ['#54709c', '#e8e8e8'],
                radius: ['40%', '55%'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
//年级设备利用率
const zblylOption = reactive({
    options: {
        title: {
            text: '每日设备利用率',
            textStyle: {
                fontSize: transformSize(25)
            },
            top: '5%',
            left: '2%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '2%',
            right: '10%',
            bottom: '5%',
            height: '75%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            splitLine: { show: false },
        },
        yAxis: {
            type: 'category',
            data: []
        },
        series: [
            {
                name: '使用次数',
                type: 'bar',
                barWidth: '40%',
                data: []
            },
        ]
    },
    init: false
})
//班级设备利用率
const bjzblylOption = reactive({
    options: {
        title: {
            text: '',
            textStyle: {
                fontSize: transformSize(25)
            },
            top: '5%',
            left: '2%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '2%',
            right: '10%',
            bottom: '5%',
            height: '75%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            splitLine: { show: false },
        },
        yAxis: {
            type: 'category',
            data: []
        },
        series: [
            {
                name: '使用次数',
                type: 'bar',
                barWidth: '40%',
                data: []
            },
        ]
    },
    init: false
})
//装备运行时长
const zbyxscOption = reactive({
    options: {
        title: {
            text: '装备运行时长',
            textStyle: {
                fontSize: transformSize(25)
            },
            top: '5%',
            left: '2%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            bottom: '15%',
            left: '12%',
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        series: []
    },
    init: false
})

let opt1 = glxsbslOption.options
let opt2 = zbqkOption.options
let opt3 = zbppfbOption.options
let opt4 = bjjspblOption.options
let opt5 = gnbmpblOption.options
let opt6 = zblylOption.options
let opt7 = zbyxscOption.options
let opt8 = bjzblylOption.options

injectOption()

function getClickIdx(idx) {
    name.value = zblylYData[idx]
    // console.log(idx, name.value)
    queDeviceRate({ grade: name.value }).then(response => {
        console.log(response.data)
        if (response.data.length > 0) {
            bjzblylYData = [], bjzblylSData = []
            response.data.map(item => {
                bjzblylYData.push(item.grade)
                bjzblylSData.push((item.ration * 1).toFixed(2))
            })
            opt8.yAxis.data = bjzblylYData
            opt8.series[0].data = bjzblylSData
            open.value = true
        }
    })
}

function injectOption() {
    proxy.glxsbsl = glxsbslOption
    proxy.zbqk = zbqkOption
    proxy.zbppfb = zbppfbOption
    proxy.bjjspbl = bjjspblOption
    proxy.gnbmpbl = gnbmpblOption
    proxy.zblyl = zblylOption
    proxy.zbyxsc = zbyxscOption
    proxy.bjzblyl = bjzblylOption
}

function setDatas() {
    opt1.series[0].data = glxsbslSData
    opt2.series[0].data = zbqkSData
    opt3.series[0].data = zbppfbSData
    opt4.series[0].data = bjjspblSData
    opt5.series[0].data = gnbmpblSData
    opt6.yAxis.data = zblylYData
    opt6.series[0].data = zblylSData
    opt7.xAxis.data = zbyxscXData
    opt7.series = zbyxscSData
    opt8.yAxis.data = bjzblylYData
    opt8.series[0].data = bjzblylSData
}

function getDatas() {
    queTypeRation().then(response => {
        if (response.data.nameRationList.length > 0) {
            glxsbslSData = []
            response.data.nameRationList.map(item => {
                glxsbslSData.push({
                    name: item.name,
                    value: (item.ration * 1).toFixed(2) || 0
                })
            })
            opt1.series[0].data = glxsbslSData
        }
    })
    queDeviceAddress().then(response => {
        if (response.data.length > 0) {
            deviceEquip.value = []
            response.data.map(item => {
                item.classList.map(opt => {
                    deviceEquip.value.push({
                        name: `${item.grade}-${opt}`
                    })
                })
            })
        }
    })
    queNetwork().then(response => {
        let { network, networkDownload, networkUpload } = response.data
        deviceConfig.value = {
            network: network + 'M/s',
            networkDownload: networkDownload + 'kbps',
            networkUpload: networkUpload + 'kbps'
        }
    })
    queBrandRation().then(response => {
        if (response.data.brandRationList.length > 0) {
            zbppfbSData = []
            response.data.brandRationList.map(item => {
                zbppfbSData.push({
                    name: item.brand,
                    value: (item.ration * 1).toFixed(2) || 0
                })
            })
            opt3.series[0].data = zbppfbSData
        }
    })
    queDeviceGradeRate().then(response => {
        console.log(response.data)
        if (response.data.length > 0) {
            zblylYData = [], zblylSData = []
            response.data.map(item => {
                zblylYData.push(item.grade)
                zblylSData.push((item.ration * 1).toFixed(2) || 0)
            })
            opt6.yAxis.data = zblylYData
            opt6.series[0].data = zblylSData
        }
    })
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(25)
}

onMounted(() => {
    setDatas()
    getDatas()
})

</script>

<style lang="scss" scoped>
.echarts-box {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 50vh 50vh 40vh;
    grid-gap: 2vh 1vw;

    .item {
        border-radius: .5vw;
        border: 1px solid #e8e8e8;
    }

    .item3 {
        padding: 4vh 3vw 2vh;
        display: flex;
        flex-direction: column;
        justify-content: flex-end;
        position: relative;

        .title {
            position: absolute;
            left: 3%;
            top: 4%;
            font-size: 1.2vw;
            font-weight: bold;
        }

        .opt1>.icon {
            background-color: #91cc75;
        }

        .opt2>.icon {
            background-color: #ee6666;
        }

        .opt3>.icon {
            background-color: #4095e5;
        }
    }

    .item3-opt {
        display: flex;
        border: 1px solid #e8e8e8;
        margin-top: 2vh;

        .icon {
            width: 33%;
            height: 100%;
            display: flex;
            padding: 0 2vw;
            color: #fff;
        }

        .info {
            width: 60%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;
            padding: 1vh 0;

            .name,
            .value {
                font-size: 1.2vw;
            }

            .value {
                margin-top: 0.8vh;
                font-weight: bold;
            }
        }
    }

    .row {
        grid-column-start: 1;
        grid-column-end: 4;
        position: relative;
        // padding: 8vh 3vw 2vh;

        .title {
            position: absolute;
            font-weight: bold;
            font-size: 1.3vw;
            left: 2%;
            top: 4%;
        }

        .legend {
            display: flex;
            margin-top: 3vh;
            gap: 0 1vw;

            div {
                position: relative;
                padding-left: 1.3vw;
            }

            div::before {
                position: absolute;
                top: .25vw;
                left: 0;
                content: '';
                width: 1vw;
                height: 1vw;
            }

            div:first-child::before {
                background-color: #a2ef4d;
            }

            div:last-child::before {
                background-color: #bebebe;
            }
        }

        &-opt {
            margin-top: 1vh;
            display: flex;
            flex-wrap: wrap;
            gap: 1.5vh 2vw;

            .grade {
                width: 5vw;
            }

            .class {
                width: 5.5vw;
                text-align: center;

                .block {
                    width: 100%;
                    height: 3vh;
                }
            }
        }
    }
}
</style>
