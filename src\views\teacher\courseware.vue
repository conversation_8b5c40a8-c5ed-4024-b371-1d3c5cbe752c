<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ "我的班级" || route.meta.title }}
        </div>
      </template>

      <div class="monitor_flex">
        <locateSel
          class="monitor-position flex_1"
          ref="locateRef"
          :isTeacher="1"
          @getPosition="getPosition"
          :maxHeight="posHeight"
          @handleQuery="handleQuery"
        />
        <div class="flex_2" ref="flexRightBox" v-loading="loading">
          <el-row :gutter="12" class="mb12 btn-list">
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="FolderOpened"
                @click="handleBatchFile(1)"
                :disabled="tableAllSelectedId.length < 1"
                >课件分发</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Lock"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleBatchLock(1, 0)"
                >批量锁定</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Unlock"
                @click="handleBatchLock(1, 1)"
                :disabled="tableAllSelectedId.length < 1"
                >批量解锁</el-button
              >
            </el-col>
          </el-row>
          <div class="recording-main">
            <div class="online-filter" v-if="tableList.length > 0">
              <el-checkbox v-model="queryParams.isFlag" @change="offChange">
                <div class="online-filter-content">只看在线设备</div>
              </el-checkbox>
              <el-checkbox
                class="checkAll"
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >
                <div class="online-filter-content">全选</div>
              </el-checkbox>
            </div>
            <div class="recording-main_grid">
              <p class="normal" v-if="total == 0">暂无数据</p>
              <el-row :gutter="10" style="gap: 5px 0" v-if="total > 0">
                <el-col
                  :span="8"
                  v-for="(item, index) in tableList"
                  :key="index"
                >
                  <div class="screen-wrapper">
                    <div
                      class="screen-card"
                      style="cursor: pointer"
                      v-throttle
                      @click="handleCheckItem(item, index)"
                    >
                      <div class="screen-checked" @click.native.stop>
                        <el-checkbox
                          label=""
                          v-model="item.checked"
                          @change="
                            (val) => {
                              handleCheckChange(val, item);
                            }
                          "
                          @click.native.stop
                        />
                      </div>
                      <div v-if="item.statusCode == 2" class="img"></div>
                      <img
                        v-else
                        :src="item.statusCode == 2 ? '' : item.deviceImg"
                        :alt="item.statusCode == 2 ? '设备离线' : '暂无图片'"
                      />

                      <div class="screen-title">
                        <div class="left">
                          <!-- <div>{{ item.deviceCode || "-" }}</div> -->
                          <div>{{ item.deviceName || "-" }}</div>
                          <div
                            class="installation"
                            :title="item.installAddress"
                          >
                            {{ item.installAddress || "默认位置" }}
                          </div>
                        </div>
                        <div class="right">
                          <el-tooltip content="课件上传" placement="top">
                            <el-button
                              link
                              type="warning"
                              class="icon-btn"
                              :disabled="item.statusCode == 2"
                              icon="FolderOpened"
                              @click.native.stop="handleBatchFile(0, item)"
                            ></el-button>
                          </el-tooltip>
                          <el-tooltip content="锁屏" placement="top">
                            <el-button
                              link
                              type="danger"
                              class="icon-btn"
                              :disabled="item.statusCode == 2"
                              icon="Lock"
                              @click.native.stop="handleBatchLock(0, 0, item)"
                            ></el-button>
                          </el-tooltip>
                          <el-tooltip content="解锁" placement="top">
                            <el-button
                              link
                              type="success"
                              class="icon-btn"
                              :disabled="item.statusCode == 2"
                              icon="Unlock"
                              @click.native.stop="handleBatchLock(0, 1, item)"
                            ></el-button>
                          </el-tooltip>
                        </div>
                      </div>
                      <div
                        class="screen-status"
                        :class="
                          item.statusCode == 0
                            ? 'online'
                            : item.statusCode == 1
                            ? 'badline'
                            : 'offline'
                        "
                      >
                        {{ statusCodeObj[item.statusCode]?.label || "-" }}
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-pagination
              v-show="total > 0"
              :total="total"
              v-model:current-page="queryParams.pageNum"
              v-model:page-size="queryParams.pageSize"
              @current-change="getList"
              layout="total, prev, pager, next"
              background
              class="custom-pagination"
            />
          </div>
        </div>
      </div>
    </el-card>

    <!-- 文件分发 -->
    <fileSend
      :fileOpen="fileOpen"
      :sendCodeList="sendCodeList"
      @cancel="fileOpen = false"
    />
  </div>
</template>

<script setup name="recording">
import fileSend from "../deviceControl/components/fileSend.vue";
import locateSel from "../deviceManage/components/locateSel.vue";
import { useRoute, useRouter } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onBeforeUnmount,
  onMounted,
} from "vue";
import {
  treeToArray,
  getBlob,
  downloadBlob2,
  downloadHttp,
  sendPointRequest,
  isValidWindowsAbsolutePath,
  isValidRelativePath,
  sendPointRequestBatch,
} from "@/utils";
import { getDevicePermissions } from "@/api/teacher";
import { devicePage } from "@/api/mediaTeach/ledger";
import {
  getSchoolScreenshotList,
  addSchoolScreenshot,
  addByMqtt,
  deviceCtl,
  deviceStartByWOL,
  deviceCtlMqtt,
  addSchoolDeviceLog,
} from "@/api/deviceControl";
import { nextTick } from "process";
import { ElMessage, genFileId } from "element-plus";

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const positionList = ref([]);
const state = reactive({
  sendCodeList: [],
  loading: false,
  isBatch: false,
  abortController: null,
  uploadRef: null,
  uploading: false,
  progress: 0,
  fileForm: {
    codeList: [],
    fileDescription: "",
    uploadName: "",
    filePath: "",
    type: 0,
  },
  fileOpen: false,
  positionNodeList: [],
  statusCodeObj: {
    0: {
      label: "设备解锁",
    },
    1: {
      label: "设备锁定",
    },
    2: {
      label: "设备离线",
    },
  },
  fileRef: null,
  isRalay: false,
  checkAll: false,
  isIndeterminate: false,
  open: false,
  title: "",
  tableAllSelectedRow: [],
  tableAllSelectedId: [],
  refreshTimer: null,
  addTimer: null,
  addSecond: 0,
  rect: null,
  screenDom: null,
  demo: null,
  hasInitVideo: false,
  jmuxer: null,
  isProd: import.meta.env.VITE_APP_BASE_API == "/prod-api",
  desktopLoading: false,
  dialogVisible: false,
  total: 0,
  loading: false,
  ips: [],
  tableData_all: [],
  tableList: [],
  typeList: [],
  statusList: [
    { label: "待处理", value: 0, type: "danger" },
    { label: "已处理", value: 1, type: "success" },
  ],
  queryParams: {
    isFlag: false,
    region: [],
    pageNum: 1,
    pageSize: 9,
  },
  onlyOnlineDevices: false,
  currentDevice: {
    isMqtt: 0,
    deviceName: "",
    screenshot: "",
  },
  curRow: {},
});

const {
  sendCodeList,
  loading,
  fileForm,
  fileOpen,
  statusCodeObj,
  isRalay,
  ips,
  tableData_all,
  checkAll,
  isIndeterminate,
  open,
  tableAllSelectedId,
  tableAllSelectedRow,
  refreshTimer,
  addTimer,
  addSecond,
  hasInitVideo,
  tableList,
  queryParams,
  total,
} = toRefs(state);

function getUrlIp(ip, url = "") {
  let str = "http";
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1?.includes(str)) {
    url1 = url1?.replace("s", "")?.replace("http://", "");
  } else {
    ip1 = `http://` + ip1;
  }
  if (ip2?.includes(str)) {
    url2 = url2?.replace("s", "")?.replace("http://", "");
  } else {
    ip2 = `http://` + ip2;
  }
  return { ip1, ip2, url1, url2 };
}

function handleBatchFile(isBatch, row = {}) {
  // isBatch: 1 批量   0 单个
  if (!!isBatch) {
    const { flag, recoverArr } = checkBatch();
    if (flag || recoverArr.length > 0) {
      proxy.$modal.msgWarning(
        "设备存在离线状态，无法进行此操作"
      );
      return;
    }
  } else if (row.statusCode == 2 || row.winStatus > 1) {
    proxy.$modal.msgWarning(
      "设备存在离线状态，无法进行此操作"
    );
    return;
  }
  state.sendCodeList = isBatch
    ? state.tableAllSelectedRow.map((item) => item.deviceCode)
    : [row.deviceCode];
  console.log(state.sendCodeList, state.tableAllSelectedRow, "sendCodeList");
  state.fileOpen = true;
}

const handleCheckAllChange = (val) => {
  console.log(val, "全选");
  isIndeterminate.value = false;
  tableList.value = tableList.value.map((item) => {
    item.checked = val;
    return item;
  });
  tableAllSelectedId.value = val
    ? state.tableData_all.map((item) => item.deviceId)
    : [];
  tableAllSelectedRow.value = val ? state.tableData_all : [];
  console.log(
    tableAllSelectedId.value,
    tableAllSelectedRow.value,
    tableList.value,
    "全选"
  );
};

const handleCheckItem = (item, index) => {
  tableList.value[index].checked = !item.checked;
  handleCheckChange(tableList.value[index].checked, item);
  console.log("触发handleCheckItem", item, index);
};

const handleCheckChange = (val, row) => {
  console.log("触发handleCheckChange");
  let idx = tableAllSelectedId.value.findIndex((item) => item == row.deviceId);
  idx == -1
    ? tableAllSelectedId.value.push(row.deviceId)
    : tableAllSelectedId.value.splice(idx, 1);
  idx == -1
    ? tableAllSelectedRow.value.push(row)
    : tableAllSelectedRow.value.splice(idx, 1);
  console.log(val, row, "单独选");

  let total = tableAllSelectedId.value.length;
  checkAll.value = total == tableData_all.value.length;
  isIndeterminate.value = total > 0 && total < tableData_all.value.length;
};

/** 批量操作前的检测 */
const checkBatch = () => {
  let newArr = [];
  console.log(
    state.tableAllSelectedId,
    state.tableData_all,
    "批量操作前的检测"
  );
  state.tableAllSelectedId.map((item) => {
    state.tableData_all.map((item2) => {
      if (item2.deviceId == item) {
        newArr.push(item2);
      }
    });
  });
  let arr = [],
    flag = false;
  ips.value = [];
  let narr = [],
    recoverArr = [];
  newArr.map((item) => {
    if (item.statusCode == 2) {
      // 关机
      narr.push(item.deviceName);
      flag = true;
    } else if (item.winStatus > 1) {
      recoverArr.push(item.deviceName);
    } else {
      arr.push(item.deviceName);
      ips.value.push(item.ipAddress);
    }
  });
  return { arr, narr, flag, recoverArr };
};

/** 批量解锁/锁屏 type: 0锁屏 1解锁*/
const handleBatchLock = (isBatch, type, row) => {
  if (isBatch == 0 && (row.winStatus > 1 || row.statusCode == 2)) {
    proxy.$modal.msgWarning(
      "设备存在离线状态，无法进行此操作"
    );
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在离线状态，无法进行此操作"
    );
    return;
  }
  proxy.$modal
    .confirm(
      `是否确认${isBatch == 1 ? "批量" : ""}对设备名称为【${
        isBatch == 1 ? arr.join("、") : row.deviceName
      }】的设备进行${type == 0 ? "锁屏" : "解锁"}？`
    )
    .then(async function () {
      if (isBatch == 0) {
        await handleLock(
          {
            ipAddress: row.ipAddress,
            deviceCode: row.deviceCode,
            isMqtt: row.isMqtt,
          },
          type
        );
        return;
      }
      let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
      let sendData = {
        userEvents: rows.map((item) => {
          return {
            event: "Click",
            eventDescribe: "选中设备后点击下方悬浮按钮集控",
            content: `${type == 0 ? 7 : 8}`,
            num: 1,
            deviceCode: item.deviceCode,
          };
        }),
      };
      console.log(sendData, "批量埋点");
      //   sendPointRequestBatch(sendData);
      for (let j = 0; j < rows.length; j++) {
        if (type == 1) {
          addSchoolDeviceLog({
            logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows[j].deviceCode, // 操作设备编号
            logContent: `设备后台解锁`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
        }
        try {
          await handleLock(
            {
              ipAddress: rows[j].ipAddress,
              deviceCode: rows[j].deviceCode,
              isMqtt: rows[j].isMqtt,
            },
            type
          );
        } catch (error) {}
      }
    })
    .then(() => {
      console.log("执行完成");
      getList();
    })
    .catch(() => {});
};

/** 解锁/锁屏 type: 0锁屏 1解锁*/
const handleLock = async (row, type) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost || "");
  let obj = {
    method: "get",
    uri:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

function offChange(e) {
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  getList();
}

function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.region = d?._checkList;
  }
  queryParams.value.pageNum = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

function getList(flag = true) {
  let obj = {
    ...queryParams.value,
  };
  if (flag) loading.value = true;
  console.log("查询传参", obj);
  getDevicePermissions(obj)
    .then((res) => {
      console.log(res, "课件上传");
      const { records, total } = res.data;
      console.log(records, total, "total");
      tableList.value = records || [];
      tableList.value.forEach((item) => {
        if (!!item.screenshotFile) {
          item.screenshotFile = "data:image/gif;base64," + item.screenshotFile;
        }
      });
      state.total = total || 0;
      nextTick(() => {
        tableList.value.forEach((item) => {
          if (tableAllSelectedId.value.indexOf(item.deviceId) > -1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        });
      });
      console.log(total, "total");
    })
    .finally((err) => {
      loading.value = false;
    });

  getDevicePermissions({
    ...obj,
    pageNum: 1,
    pageSize: 9999999,
  }).then((res) => {
    console.log("所有台账数据", res);
    const { records } = res.data;
    tableData_all.value = records || [];
    let total = tableAllSelectedId.value.length;
    checkAll.value = total == tableData_all.value.length;
    isIndeterminate.value = total > 0 && total < tableData_all.value.length;
    console.log(tableData_all.value);
  });
}

function getPosition(d) {
  state.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

onMounted(() => {
  refreshTimer.value = setInterval(() => {
    getList(false);
  }, 60000);
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  getList();
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - 215;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - 215;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览大屏巡检页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  clearInterval(refreshTimer.value);
  if (hasInitVideo.value) handleCloseWs2();
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});
</script>

<style lang="scss" scoped>
.monitor {
  &-bottom {
    position: fixed;
    width: 100%;
    display: flex;
    justify-content: center;
    bottom: 35px;
    left: 0;
    z-index: 2001;
    pointer-events: none;
  }
  &-btns {
    pointer-events: auto;
    // border: 1px solid red;
    display: flex;
    max-width: 90%;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px 0;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

.el-progress {
  width: 200px;
  :deep(.el-progress__text) {
    min-width: 35px;
  }
}

.fileclass {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }
}

.icon-btn {
  margin-left: 0;
  :deep(.el-icon) {
    font-size: 1.5vw !important;
  }
}

.recording-main {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;

  .normal {
    font-size: 18px;
    min-height: calc(100vh - 300px);
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .online-filter {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;

    .online-filter-content {
      display: flex;
      align-items: center;
      padding-left: 5px;
      color: #606266;
    }

    :deep(.el-checkbox__label) {
      padding-left: 0;
    }

    :deep(.el-checkbox__inner) {
      border-radius: 50%;
    }

    .checkAll {
      :deep(.el-checkbox__inner) {
        border-radius: 2px;
      }
    }
  }

  &_grid {
    margin-bottom: 20px;
    min-height: 380px;
  }

  .screen-wrapper {
    background-color: #e5e6ea;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .screen-card {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    // height: 180px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .screen-checked {
      position: absolute;
      top: 0;
      left: 0;
      // border: 1px solid red;
      padding-top: 0.4vw;
      padding-left: 0.3vw;
      .el-checkbox {
        height: auto;
      }
      :deep(.el-checkbox__inner) {
        width: 1.5vw;
        height: 1.5vw;
        &::after {
          width: 0.5vw;
          height: 1vw;
          left: 0.45vw;
          top: 0vw;
        }
      }
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .img {
      width: 100%;
      height: 100%;
      background-color: #333;
    }

    .screen-title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.3vw 0.5vw 0;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      font-size: 0.8vw;
      line-height: 1.2vw;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      display: flex;
      align-items: center;
      justify-content: space-between;
      gap: 0 5%;
      .left {
        width: 65%;
        div {
          text-overflow: ellipsis;
          overflow: hidden;
        }
      }
      .right {
        width: 30%;
        font-size: 1.5vw;
        display: flex;
        justify-content: space-between;
      }
    }
    .screen-status {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0.4vw 0.8vw;
      color: #fff;
      border-radius: 4px;
      background-color: #81b337;
      &.offline {
        background-color: #bd3124;
      }
      &.badline {
        background-color: #e99d42;
      }
    }
  }

  .custom-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination__total) {
      display: none;
    }

    :deep(.el-pager li) {
      background-color: #f4f4f5;
      border: none;

      &.is-active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}

.view-switch {
  margin-bottom: 10px;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  //   background-color: black;
  //   border-color: black;
}

.dialog-content {
  .screenshot-area {
    text-align: center;
    // border: 1px solid red;
    margin-bottom: 20px;
    overflow-x: scroll;

    img {
      object-fit: contain;
    }
  }

  .operation-area {
    text-align: center;
    margin-bottom: 20px;

    .scissors-icon {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      padding: 8px 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #e4e7ed;
      }
    }
  }
}

:deep(.screenshot-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .screenshot-placeholder {
    background-color: #c4c4c5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .screenshot-success-message {
    /* display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px; */
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 20px;
  }
}

.dialog_img {
  width: 100%;
}

.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}
.monitor_flex {
  display: flex;
  gap: 10px;
  align-items: flex-start;
  min-height: calc(100vh - 215px);
  .flex_1 {
    width: 18%;
    margin-right: 10px;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }

  .flex_2 {
    width: 82%;
  }
  .installation {
    display: inline-block;
    // width: 13.5vw;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
