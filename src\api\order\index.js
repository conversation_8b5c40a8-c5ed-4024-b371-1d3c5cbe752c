import request from "@/utils/request";

// 删除任务单信息
export function delProjectTask(params) {
  return request({
    url: "/system/schoolProjectTask",
    method: "delete",
    params,
  });
}

// 修改任务单信息
export function updateProjectTask(data) {
  return request({
    url: "/system/schoolProjectTask",
    method: "put",
    data,
  });
}

// 查询任务单详情
export function projectTaskInfo(id) {
  return request({
    url: `/system/schoolProjectTask/${id}`,
    method: "get",
  });
}

// 新增任务单信息
export function addProjectTask(data) {
  return request({
    url: "/system/schoolProjectTask",
    method: "post",
    data,
  });
}

// 获取任务单列表
export function getProjectTaskList(params) {
  return request({
    url: "/system/schoolProjectTask",
    method: "get",
    params,
  });
}


// 删除咨询单信息
export function delConsult(params) {
    return request({
      url: "/system/consult",
      method: "delete",
      params,
    });
  }
  
  // 修改咨询单信息
  export function updateConsult(data) {
    return request({
      url: "/system/consult",
      method: "put",
      data,
    });
  }

// 查询咨询单详情
export function getConsultInfo(id) {
    return request({
      url: `/system/consult/${id}`,
      method: "get",
    });
  }
  

// 查询咨询单列表
export function getConsultPage(params) {
  return request({
    url: "/system/consult",
    method: "get",
    params,
  });
}

// 新增咨询单信息
export function addConsult(data) {
  return request({
    url: "/system/consult",
    method: "post",
    data,
  });
}

// 获取咨询单类别列表
export function getConsultTypeList(params) {
  return request({
    url: "/system/consult/getConsultTypeList",
    method: "get",
    params,
  });
}

// 删除项目信息
export function delSchoolProject(params) {
  return request({
    url: "/system/schoolProject",
    method: "delete",
    params,
  });
}

// 修改项目信息
export function updateSchoolProject(data) {
  return request({
    url: "/system/schoolProject",
    method: "put",
    data,
  });
}

// 查询项目信息列表
export function getSchoolProjectPage(params) {
  return request({
    url: "/system/schoolProject",
    method: "get",
    params,
  });
}

// 新增项目信息
export function addSchoolProject(data) {
  return request({
    url: "/system/schoolProject",
    method: "post",
    data,
  });
}

// 查询项目信息详情
export function schoolProjectInfo(id) {
  return request({
    url: `/system/schoolProject/${id}`,
    method: "get",
  });
}
