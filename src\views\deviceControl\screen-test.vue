<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <!-- <el-button @click="handleBack"
              >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
            > -->
        </div>
      </template>

      <div class="monitor_flex">
        <div class="monitor-position flex_1">
          <div class="monitor-title">安装位置筛选</div>
          <el-scrollbar :max-height="500">
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              show-checkbox
              node-key="id"
              expand-on-click-node
              default-expand-all
              :props="positionProps"
              @check="treeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div>
        <div class="flex_2">
          <!-- <div
            id="demo"
            style="
              width: 150px;
              height: 100px;
              /* border: 1px solid red; */
              overflow-x: scroll;
              overflow-y: hidden;
            "
          >
            <div style="width: 400px; height: 100px"></div>
          </div>
          <el-button @click="getScorllLeft">获取滚动距离</el-button> -->
          <div class="view-switch">
            <el-radio-group v-model="currentView" @change="viewChange">
              <el-radio-button value="1" label="realtime"
                >设备实时桌面</el-radio-button
              >
              <el-radio-button value="2" label="gallery"
                >截图图库</el-radio-button
              >
            </el-radio-group>
          </div>
          <div v-if="currentView == '1'" class="recording-main">
            <div class="online-filter">
              <el-checkbox v-model="queryParams.isOff" @change="offChange">
                <div class="online-filter-content">只看在线设备</div>
              </el-checkbox>
            </div>
            <div class="recording-main_grid">
              <p class="normal" v-if="total == 0">暂无数据</p>
              <el-row :gutter="10" style="gap: 5px 0" v-else>
                <el-col
                  :span="8"
                  v-for="(item, index) in tableList"
                  :key="index"
                >
                  <div class="screen-wrapper">
                    <div
                      class="screen-card"
                      @click="
                        item.runStatus[0] == 0 ? '' : handleDesktop2(item)
                      "
                      style="cursor: pointer"
                      v-throttle
                    >
                      <div v-if="item.runStatus[0] == 0" class="img"></div>
                      <img
                        v-else
                        :src="item.runStatus[0] == 0 ? '' : item.screenshotUrl"
                        :alt="item.runStatus[0] == 0 ? '设备离线' : '暂无图片'"
                      />
                      <div class="screen-title">{{ item.deviceName }}</div>
                      <div
                        class="screen-status"
                        :class="item.runStatus[0] == 0 ? 'offline' : 'online'"
                      >
                        {{ item.runStatus[0] == 0 ? "设备离线" : "设备在线" }}
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-pagination
              v-show="total > 0"
              :total="total"
              v-model:current-page="queryParams.current"
              v-model:page-size="queryParams.size"
              @current-change="getList"
              layout="total, prev, pager, next"
              background
              class="custom-pagination"
            />
          </div>
          <div v-else class="gallery-main recording-main">
            <div class="gallery-grid">
              <div class="recording-main_grid">
                <p class="normal" v-if="stotal == 0">暂无数据</p>
                <el-row :gutter="20" v-else>
                  <el-col
                    :span="8"
                    v-for="(item, index) in galleryList"
                    :key="index"
                  >
                    <div class="gallery-item">
                      <div
                        class="gallery-image"
                        v-throttle
                        @click="checkImg(item.imageLocalUrl)"
                      >
                        <img :src="item.imageLocalUrl" alt="暂无图片" />
                      </div>
                      <div
                        v-if="item.imageLocalUrl"
                        class="download-icon"
                        v-throttle
                        @click="downloadHttp(item.imageLocalUrl)"
                      >
                        <el-icon><Download /></el-icon>
                      </div>
                      <div
                        v-else-if="item.imageFile"
                        class="download-icon"
                        v-throttle
                        @click="
                          downloadBlob2(
                            getBlob(item.imageFile),
                            `${item.deviceCode}-截屏图片`
                          )
                        "
                      >
                        <el-icon><Download /></el-icon>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <el-pagination
                v-show="stotal > 0"
                :total="stotal"
                v-model:current-page="shotParams.pageNum"
                v-model:page-size="shotParams.pageSize"
                @current-change="getImgList"
                layout="total, prev, pager, next"
                background
                class="custom-pagination"
              />
            </div>
          </div>
        </div>
      </div>

      <!-- 实时桌面 -->
      <el-dialog
        v-model="dialogVisible"
        :title="`设备名称: ${currentDevice.deviceName || 'XXXXXXXX'}`"
        append-to-body
        @close="handleCloseWs2"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="90%"
        align-center
        
      >
        <div class="dialog-content">
          <div class="operation-area">
            <div class="scissors-icon" @click="handleScreenshot" v-throttle>
              <el-icon><Scissor /></el-icon> 截图
            </div>
          </div>
          <div class="screenshot-area" v-loading="desktopLoading" ref="screenDom">
            <!-- <img id="player" style="width: 100%; height: 70vh" /> -->
            <video
              id="videoPlayer"
              style="height: 75vh; outline: none"
              autoplay
              controlsList="nodownload nofullscreen noplaybackrate nopictureinpicture"
              disablePictureInPicture
              disableRemotePlayback
              tabindex="0"
            ></video>
          </div>
        </div>
      </el-dialog>

      <el-dialog
        v-model="screenshotDialogVisible"
        :show-close="false"
        :modal="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="400px"
        class="screenshot-dialog"
      >
        <div class="screenshot-placeholder">
          <div class="screenshot-success-message">
            <!-- <el-icon class="success-icon" color="#67C23A"><CircleCheckFilled /></el-icon> -->
            <span>已截图，保存至截图图库中</span>
          </div>
        </div>
      </el-dialog>

      <!-- 查看截图 -->
      <el-dialog
        v-model="imgDialogVisible"
        :show-close="false"
        :modal="true"
        width="80%"
        align-center
        class="screenshot-dialog"
      >
        <img :src="imgUrl" alt="" class="dialog_img" />
      </el-dialog>
    </el-card>
  </div>
</template>
  
  <script setup name="recording">
// import JMuxer from "jmuxer";
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  computed,
  watch,
  onBeforeUnmount,
  onMounted,
} from "vue";
import { Scissor, CircleCheckFilled, Download } from "@element-plus/icons-vue";
import { createWebSocket, closeSock, sendSock } from "@/utils/socket";
import {
  treeToArray,
  treeFindPath,
  timeFormat,
  getBlob,
  downloadBlob2,
  downloadHttp,
} from "@/utils";
import keyToVKMap from "@/utils/keyToVKMap";
import { getPositionTree } from "@/api/mediaTeach/position";
import {
  devicePage,
  deviceInfo,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import {
  getSchoolScreenshotList,
  addSchoolScreenshot,
  addByMqtt,
} from "@/api/deviceControl";
import useUserStore from "@/store/modules/user";
import { webSocketStore } from "@/store/modules/webSocket";
import { getToken } from "@/utils/auth";
import elementResizeDetectorMaker from "element-resize-detector";
import { nextTick } from "process";

const webSocket = webSocketStore();
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
});
const route = useRoute();
const { proxy } = getCurrentInstance();
const positionList = ref([]);
const positionTreeList = ref([]);
const videoElement = ref(null);
const erd = elementResizeDetectorMaker();
const state = reactive({
  screenDom: null,
  rect: null,
  demo: null,
  hasInitVideo: false,
  jmuxer: null,
  isProd: import.meta.env.VITE_APP_BASE_API == "/prod-api",
  desktopLoading: false,
  dialogVisible: false,
  total: 1,
  loading: false,
  tableList_all: [],
  tableList: [],
  typeList: [],
  statusList: [
    { label: "待处理", value: 0, type: "danger" },
    { label: "已处理", value: 1, type: "success" },
  ],
  queryParams: {
    current: 1,
    size: 9,
    typeId: "1",
    positionIds: [],
    smartScreen: 1,
    isDesktop: 1,
  },
  imgUrl: "",
  imgDialogVisible: false,
  currentView: "1",
  galleryList: [],
  onlyOnlineDevices: false,
  stotal: 0,
  shotParams: {
    pageNum: 1,
    pageSize: 9,
    typeId: "1",
    positionIds: [],
  },
  currentDevice: {
    isMqtt: 0,
    deviceName: "",
    screenshot: "",
  },
  screenshotDialogVisible: false,
  pic: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAnCAYAAACrDdDdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAABBSURBVGhD7c8BDQAwEMSg+Td908GnOODtsHKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKm7QNz9WRsCGQFWQAAAABJRU5ErkJggg==",
});

const {
  screenDom,
  rect,
  demo,
  hasInitVideo,
  jmuxer,
  isProd,
  pic,
  desktopLoading,
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  statusList,
  loading,
  total,
  stotal,
  shotParams,
  currentView,
  galleryList,
  onlyOnlineDevices,
  currentDevice,
  imgDialogVisible,
  screenshotDialogVisible,
  imgUrl,
} = toRefs(state);

// const keyToVKMap = {
//   0: 0x30,
//   1: 0x31,
//   2: 0x32,
//   3: 0x33,
//   4: 0x34,
//   5: 0x35,
//   6: 0x36,
//   7: 0x37,
//   8: 0x38,
//   9: 0x39,
//   'Backspace': 0x08,
// };

function getVKCode(key) {
  return keyToVKMap[key] || 0; // 如果未找到对应的键码，返回 0
}

const global_callback2 = (nalUnit, err = false) => {
  console.log("websocket的回调函数收到服务器信息：", nalUnit);
  desktopLoading.value = false;
  if (err) {
    proxy.$modal.msgError(nalUnit);
    proxy.$modal.closeLoading();
    state.dialogVisible = false;
  } else if (nalUnit == "已断开") {
    proxy.$modal.closeLoading();
    state.dialogVisible = false;
  } else {
    // 喂给JMuxer
    if (state.jmuxer) {
      state.jmuxer.feed({
        video: nalUnit,
        duration: 0,
        // 实时模式设为0
      });
      if (videoElement.value.videoWidth && !state.hasInitVideo) {
        state.hasInitVideo = true;
        nextTick(() => {
          console.log("第二次初始化");
          initVideoPlayer();
        });
      }
    }
  }
};

const global_callback = (msg, err = false) => {
  console.log("websocket的回调函数收到服务器信息：", msg);
  desktopLoading.value = false;
  if (err) {
    proxy.$modal.msgError(msg);
    proxy.$modal.closeLoading();
    state.dialogVisible = false;
  } else {
    if (typeof msg !== "string") {
      const data = new Blob([msg], { type: "image/png" });
      const img = new Image();
      img.src = window.URL.createObjectURL(data);
      const player = document.getElementById("player");
      img.onload = function () {
        player.src = img.src;
        console.log("图片链接", player.src);
      };
      webSocket.addMsg(msg);
      console.log("图片链接", img.src);
    } else {
      const player = document.getElementById("player");
      player.src = state.pic;
      state.dialogVisible = false;
      console.log("图片链接", player.src);
    }
  }
};

function handleDesktop2(row) {
  console.log("clicked device:", row, state.jmuxer);
  state.currentDevice = row || {
    isMqtt: 0,
    deviceName: "",
    deviceCode: "",
    screenshot: "",
  };
  proxy.$modal.loading();
  let { url1, ws } = getUrlIp(row.ipAddress);
  // let uri = `ws://*************:5566/ClientHub`;
  let uri = `wss://maintainapp.gzwinteam.com/wsflow/ServerHub?corpId=${
    isProd.value ? useUserStore().corpId : useUserStore().corpId || "00000123"
  }&${
    row.isMqtt ? `deviceCode=${row.deviceCode}` : `addr=ws://${url1}/ServerHub`
  }`;

  // let uri = `${window.location.protocol == "https:" ? "wss" : "ws"}://${
  //   window.location.host
  // }/wsflow/ServerHub?corpId=${
  //   isProd.value ? useUserStore().corpId : useUserStore().corpId || "00000123"
  // }&${
  //   row.isMqtt ? `deviceCode=${row.deviceCode}` : `addr=ws://${url1}/ServerHub`
  // }`;

  createWebSocket(global_callback2, uri).then((res) => {
    proxy.$modal.closeLoading();
    state.dialogVisible = true;
    desktopLoading.value = true;
    nextTick(() => {
      //初始化JMuxer
      state.jmuxer = new JMuxer({
        node: "videoPlayer",
        mode: "video",
        flushingTime: 0,
        //封装间隔（毫秒）
        fps: 30,
        // 需与实际帧率一致
        debug: true,
      });
      console.log("第一次初始化");
      videoElement.value = document.getElementById("videoPlayer");
      window.addEventListener("resize", cancelDebounce);
      erd.listenTo(screenDom.value, () => {
        cancelDebounce();
      });
    });
  });
}

function handleDesktop(row) {
  console.log("clicked device:", row);
  state.currentDevice = row || {
    deviceName: "",
    deviceCode: "",
    screenshot: "",
  };
  proxy.$modal.loading();
  let { url1, ws } = getUrlIp(row.ipAddress);
  // let uri = `${ws}://${url1}/ServerHub?token=${getToken()}`;
  let uri = `wss://maintainapp${
    isProd.value ? "" : "test"
  }.gzwinteam.com/wsflow/ServerHub?corpId=${
    isProd.value ? useUserStore().corpId : useUserStore().corpId || "00000123"
  }&addr=ws://${url1}/ServerHub`;
  createWebSocket(global_callback, uri).then((res) => {
    proxy.$modal.closeLoading();
    state.dialogVisible = true;
    desktopLoading.value = true;
  });
}

async function handleCloseWs2() {
  await closeSock();
  hasInitVideo.value = false;
  window.removeEventListener("resize", cancelDebounce);
  if (state.jmuxer) state.jmuxer.destroy();
}

async function handleCloseWs() {
  await closeSock();
  const player = document.getElementById("player");
  player.src = state.pic;
}

function getUrlIp(ip, url = "") {
  let str = "http",
    ws = "ws",
    href = window.location.href.split(":");
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `${href[0]}://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2.replace("s", "").replace("http://", "");
  } else {
    ip2 = `${href[0]}://` + ip2;
  }
  ws = href[0] == "https" ? "ws" : "ws";
  console.log("网站协议", href[0], ws, href[0] == "https");
  return { ip1, ip2, url1, url2, ws };
}

const getCurrentList = computed(() => {
  return currentView.value === "realtime" ? tableList.value : galleryList.value;
});

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

// 查看图片
function checkImg(url) {
  imgUrl.value = url;
  imgDialogVisible.value = true;
}

function handleDel({ row, $index }) {
  proxy.$modal.confirm(`确定要删除编号为${row.number}的录音信息？`).then(() => {
    state.tableList_all.splice($index, 1);
    // 同步更新完tableList_all后更新localStorage， 新增、删除、修改操作都需要做这一步，若页面有变更记录则同步更新变更记录列表
    // localStorage.setItem('WHYWPT_SPARELIST_ALL', JSON.stringify(state.tableList_all))

    setTimeout(() => {
      proxy.$modal.msgSuccess("操作成功");
      getList();
    }, 500);
  });
}

function offChange(e) {
  console.log(e);
  getList();
}

function getList() {
  let obj = {
    ...queryParams.value,
  };
  obj.isOff ? (obj.isOff = 0) : delete obj?.isOff;
  console.log(obj, "设备实时桌面");
  proxy.$modal.loading();
  devicePage(obj)
    .then((res) => {
      const { page, abnormalTerminal } = res.data;
      proxy.$modal.closeLoading();
      console.log(res, "大屏巡检");
      tableList.value = page.records;
      tableList.value.forEach((item) => {
        if (!!item.screenshotFile) {
          item.screenshotFile = "data:image/gif;base64," + item.screenshotFile;
        }
      });
      total.value = page.total;
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

// 获取截图列表
function getImgList() {
  console.log(shotParams.value, "shotParams");

  proxy.$modal.loading();
  getSchoolScreenshotList(shotParams.value)
    .then((res) => {
      proxy.$modal.closeLoading();
      galleryList.value = res.data.rows;
      galleryList.value.forEach((item) => {
        if (!!item.imageFile) {
          item.imageFile = "data:image/gif;base64," + item.imageFile;
        }
      });
      stotal.value = res.data.total;
      console.log(res, "截图");
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

function viewChange(e) {
  console.log(e);
  if (e == 1) {
    getList();
  } else {
    getImgList();
  }
}

function handleView(item) {
  // 处理查看设备详情的逻辑
  console.log("查看设备:", item);
}

function handleDialogClose() {
  state.currentDevice = {
    isMqtt: 0,
    deviceName: "",
    screenshot: "",
  };
  state.dialogVisible = false;
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  shotParams.value.positionIds = obj.checkedKeys;
  getList();
}

function handleScreenshot() {
  console.log(state.currentDevice.deviceCode);
  console.log({ deviceCode: state.currentDevice.deviceCode }, "截图");
  proxy.$modal.loading();
  if (state.currentDevice.isMqtt) {
    addByMqtt({ deviceCode: state.currentDevice.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
      })
      .finally(() => proxy.$modal.closeLoading());
  } else {
    addSchoolScreenshot({ deviceCode: state.currentDevice.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
      })
      .finally(() => proxy.$modal.closeLoading());
  }
}

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  queryParams.value.address = queryPositionResult.value.names.join("-");
}
// handleQueryPosition()

function getPositionTreeList() {
  getPositionTree({})
    .then((response) => {
      let tree = JSON.parse(JSON.stringify(response.data));
      positionTreeList.value = response.data;
      positionList.value = treeToArray(response.data);
      // data.positionNodeList = addAttr(tree);
      // console.log("positionList ==>", positionList.value);
    })
    .catch((err) => {});
}

function initVideoPlayer() {
  // console.log("初始化视频播放器");
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  rect.value = videoElement.value.getBoundingClientRect(); // 获取元素的边界框信息
  //   const elWidth = rect.width; // 元素的宽度
  //   const elHeight = rect.height; // 元素的高度
  //   console.log(
  //     "设备分辨率、盒子宽高",
  //     videoWidth,
  //     videoHeight,
  //     rect.value.width,
  //     elHeight
  //   );

  videoElement.value.addEventListener("mousemove", (event) => {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    let x = event.clientX - rect.value.left + screenDom.value.scrollLeft;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标移动:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.videoWidth,
      `滚动条偏移量：${screenDom.value.scrollLeft}`
    );
    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X:
            (((videoWidth / rect.value.width) * x) /
              videoElement.value.videoWidth) *
            65535,
          Y:
            (((videoHeight / rect.value.height) * y) /
              videoElement.value.videoHeight) *
            65535,
        },
        Input: 2,
        Delta: 0,
        Key: 0,
      })
    );
  });
  videoElement.value.addEventListener("mousedown", (event) => {
    videoElement.value.focus(); // 设置焦点到video元素
    const button = event.button;
    let x = event.clientX - rect.value.left;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标按下:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.offsetWidth,
      videoElement.value.videoWidth
    );
    let Input = {
      Button: button,
      MousePoint: {
        X:
          (((videoWidth / rect.value.width) * x) /
            videoElement.value.videoWidth) *
          65535,
        Y:
          (((videoHeight / rect.value.height) * y) /
            videoElement.value.videoHeight) *
          65535,
      },
      Input: 1,
      Delta: 0,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标点击信息到服务器
    sendSock(JSON.stringify(Input));
  });
  videoElement.value.addEventListener("mouseup", (event) => {
    videoElement.value.focus(); // 设置焦点到video元素
    const button = event.button;
    let x = event.clientX - rect.value.left;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标抬起:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.offsetWidth,
      videoElement.value.videoWidth
    );
    let Input = {
      Button: button,
      MousePoint: {
        X:
          (((videoWidth / rect.value.width) * x) /
            videoElement.value.videoWidth) *
          65535,
        Y:
          (((videoHeight / rect.value.height) * y) /
            videoElement.value.videoHeight) *
          65535,
      },
      Input: 0,
      Delta: 0,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标点击信息到服务器
    sendSock(JSON.stringify(Input));
  });
  videoElement.value.addEventListener("wheel", (event) => {
    console.log("鼠标滑轮:", event.deltaY);
    let wheelInput = {
      Button: 1,
      MousePoint: {
        X: 0,
        Y: 0,
      },
      Input: 3,
      Delta: event.deltaY,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标滑轮信息到服务器
    sendSock(JSON.stringify(wheelInput));
  });
  videoElement.value.addEventListener("keydown", (event) => {
    const vkCode = getVKCode(event.key);
    console.log("键盘按下:", event.key, "VK Code:", vkCode);
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X: 0,
          Y: 0,
        },
        Input: 4,
        Delta: 0,
        Key: vkCode,
      })
    );
  });
  videoElement.value.addEventListener("keyup", (event) => {
    const vkCode = getVKCode(event.key);
    console.log("键盘按下:", event.key, "VK Code:", vkCode);
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X: 0,
          Y: 0,
        },
        Input: 5,
        Delta: 0,
        Key: vkCode,
      })
    );
  });
  videoElement.value.addEventListener("contextmenu", (event) => {
    console.log("右键菜单被阻止", event);
    let x = event.clientX - rect.value.left;
    let y = event.clientY - rect.value.top;
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送右键菜单信息到服务器
    sendSock(
      JSON.stringify({
        type: "contextmenu",
        // x: event.clientX,
        // y: event.clientY,
        x: (videoWidth / rect.value.width) * x,
        y: (videoHeight / rect.value.height) * y,
      })
    );
  });
  window.onload = () => {
    videoElement.value.focus();
  };
  // 禁止暂停播放
  videoElement.value.addEventListener("pause", () => {
    videoElement.value.play(); // 强制继续播放
  });
}

function getScorllLeft() {
  const rect = demo.value.getBoundingClientRect(); // 获取元素的边界框信息
  console.log(Math.ceil(demo.value.scrollLeft), demo.value.scrollWidth, rect);
}

const resizeHandler = () => {
    rect.value = videoElement.value.getBoundingClientRect(); // 重新获取元素的边界框信息
    console.log('video盒子大小发生改变', rect.value)
}

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};

const cancelDebounce = debounce(resizeHandler, 50);

onMounted(() => {
  //   demo.value = document.getElementById("demo");

  //   document.addEventListener("keydown", (event) => {
  //     const vkCode = getVKCode(event.key);
  //     console.log("键盘按下:", event.key, "VK Code:", vkCode);
  //   });
  getPositionTreeList();
  getList();
});

onBeforeUnmount(() => {
  if (hasInitVideo.value) handleCloseWs2();
});
</script>
  
  <style lang="scss" scoped>
.recording-main {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;

  .normal {
    font-size: 18px;
    min-height: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .online-filter {
    margin-bottom: 20px;

    .online-filter-content {
      display: flex;
      align-items: center;
      padding-left: 5px;
      color: #606266;
    }

    :deep(.el-checkbox__label) {
      padding-left: 0;
    }

    :deep(.el-checkbox__inner) {
      border-radius: 50%;
    }
  }

  &_grid {
    margin-bottom: 20px;
    min-height: 380px;
  }

  .screen-wrapper {
    background-color: #e5e6ea;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .screen-card {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    // height: 180px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .img {
      width: 100%;
      height: 100%;
      background-color: #333;
    }

    .screen-title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.5vw 1vw;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      font-size: 14px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .screen-status {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0.4vw 0.8vw;
      color: #fff;
      border-radius: 4px;
      background-color: #81b337;
      &.offline {
        background-color: #bd3124;
      }
    }
  }

  .custom-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination__total) {
      display: none;
    }

    :deep(.el-pager li) {
      background-color: #f4f4f5;
      border: none;

      &.is-active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}

.view-switch {
  margin-bottom: 10px;
}

.gallery-card {
  margin-bottom: 20px;

  .gallery-image {
    position: relative;
    height: 200px;
    overflow: hidden;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .gallery-title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 8px;
      background: rgba(0, 0, 0, 0.6);
      color: #fff;
    }
  }

  .gallery-info {
    padding: 12px;

    .info-item {
      margin-bottom: 8px;

      .label {
        color: #666;
        margin-right: 8px;
      }
    }
  }
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  //   background-color: black;
  //   border-color: black;
}

.dialog-content {
  .screenshot-area {
    text-align: center;
    // border: 1px solid red;
    margin-bottom: 20px;
    overflow-x: scroll;

    img {
      object-fit: contain;
    }
  }

  .operation-area {
    text-align: center;
    margin-bottom: 20px;

    .scissors-icon {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      padding: 8px 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #e4e7ed;
      }
    }
  }
}

:deep(.screenshot-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .screenshot-placeholder {
    background-color: #c4c4c5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .screenshot-success-message {
    /* display: flex;
          align-items: center;
          justify-content: center;
          gap: 10px; */
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 20px;
  }
}

.dialog_img {
  width: 100%;
}

.gallery-main {
  .gallery-grid {
    // padding: 20px;

    .gallery-item {
      position: relative;
      background-color: #f5f5f5;
      border-radius: 4px;
      margin-bottom: 40px;
      cursor: pointer;

      .gallery-image {
        width: 100%;
        aspect-ratio: 16/9;
        display: flex;
        align-items: center;
        justify-content: center;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }

      .download-icon {
        position: absolute;
        bottom: -35px;
        right: -5px;
        cursor: pointer;
        background-color: #fff;
        padding: 8px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        z-index: 1;

        &:hover {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}
.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}
.monitor_flex {
  display: flex;
  gap: 10px;
  .flex_1 {
    width: 18%;
    margin-right: 10px;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }

  .flex_2 {
    width: 82%;
  }
}
</style>
  