<template>
  <div class="work">
    <div class="work-title">工作台</div>
    <div class="work-main">
      <div class="work-left">
        <div class="work-header">
          <div class="work-header_left">
            {{ getCurrentTime().time }}好，{{ userStore.nickName }}！
          </div>
          <div class="work-header_right">
            {{ getCurrentTime().str }}
          </div>
        </div>
        <div class="work-block">
          <div class="work-block_tit sbgl">设备概览</div>
          <div class="work-block_info">
            <div
              class="work-block_info-item"
              style="cursor: pointer"
              v-for="(item, index) in infoList"
              @click="deviceItem(item)"
            >
              <img :src="item.icon" />
              <div>
                当前{{ item.status }}设备台数<br />
                <span :style="{ color: item.color }">{{ item.num }}台</span>
              </div>
            </div>
          </div>
        </div>
        <div class="work-block block2">
          <div class="work-block_tit kjrk">快捷入口</div>
          <el-tabs v-model="activeName">
            <el-tab-pane
              v-for="(item, index) in tabRouteList"
              :label="item.meta.title"
              :name="index"
              :key="item.name"
            >
              <div class="work-block_tabs">
                <div
                  class="work-block_tabs-item"
                  v-for="(obj, idx) in item.children"
                  :key="idx"
                  @click="
                    handleRouter(obj.path, item.path, obj, item.meta.title)
                  "
                >
                  <img
                    src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbtzgl.png"
                  />
                  {{ obj.meta.title }}
                </div>
              </div>
            </el-tab-pane>
          </el-tabs>
        </div>
      </div>
      <div class="work-right">
        <div class="work-block" style="margin-top: 0">
          <!-- <div class="work-block_tit dbrw">今日天气</div> -->
          <div>
            <!-- 天气：晴 -->
            <div>
              {{ getCurrentTime().weekday }}
              <span id="timeId">{{ timeShow }}</span>
            </div>
          </div>
        </div>
        <div class="work-block task-block">
          <div class="work-block_tit dbrw">
            待办任务
            <div class="filter-container">
              <el-select
                v-model="taskFilter"
                placeholder="全部"
                size="small"
                style="width: 95px"
                @change="filterTasks"
              >
                <el-option label="全部" value="all" />
                <el-option
                  v-for="item in taskTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>
          <div class="work-block_task">
            <div
              class="work-block_task-item"
              v-for="item in taskList"
              :key="item.name"
              @click="handleTab(item)"
            >
              <div class="left">
                <div>
                  <img :src="item.icon" />
                  {{ item.name }}
                </div>
                <el-progress :percentage="item.progress" />
              </div>
              <div class="right">点击查看详情</div>
            </div>
          </div>
        </div>
        <div class="work-block">
          <div class="work-block_tit cygn">
            常用功能
            <el-button
              type="primary"
              size="small"
              @click="isEdit ? submitBlock() : (isEdit = true)"
              >{{ isEdit ? "保存" : "编辑" }}</el-button
            >
          </div>
          <div v-if="!isEdit">
            <div
              class="work-block_use"
              v-if="useList.length != 0"
              v-loading="loadingUse"
            >
              <div
                class="work-block_use-item"
                v-for="(item, index) in useList"
                :key="index"
                @click="routerPath(item)"
              >
                <img
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbtzgl.png"
                />
                <span style="font-size: 0.7vw">{{
                  item?.meta?.title || item.title
                }}</span>
              </div>
            </div>
            <div
              v-else
              style="
                min-height: 110px;
                display: flex;
                align-items: center;
                justify-content: center;
              "
            >
              <span>暂无常用功能</span>
            </div>
          </div>
          <div class="work-block_use2" v-if="isEdit" v-loading="editUseLoading">
            <div
              v-for="(item, index) in editUseList"
              :key="index"
              style="width: 47%"
            >
              <div
                class="work-block_use2-item2"
                v-if="item?.title || item?.meta?.title"
              >
                <img
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbtzgl.png"
                />
                <span style="font-size: 0.7vw">{{
                  item?.title || item?.meta?.title
                }}</span>
                <el-icon @click="handleDelIcon(index)"><Close /></el-icon>
              </div>
              <div class="work-block_use2-item" v-else>
                <el-tree-select
                  v-model="item.title"
                  :data="editUseTree"
                  :props="treeDefaultProps"
                  :render-after-expand="false"
                  style="width: 100%"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="quick-jump-button" @click="handleQuickJump">
        <img
          src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/setting.png"
          alt="设置"
          style="height: 82px; width: 82px"
        />
      </div>
    </div>
  </div>
</template>

<script setup name="Work">
import ServiceCounter from "@/components/ServiceCounter";
import {
  onMounted,
  onActivated,
  onDeactivated,
  reactive,
  ref,
  toRefs,
  computed,
  onBeforeUnmount,
} from "vue";
import { useRouter } from "vue-router";
import useUserStore from "@/store/modules/user";
import { getRouters } from "@/api/menu";
import { workStatistics, addSchoolUserUseFunctionBatch } from "@/api/service";
import {
  getSchoolUserUseFunction,
  addSchoolUserUseFunction,
  deleteUserUse,
} from "@/api/approval";
import { sendPointRequest, treeToArray, treeFindPath } from "@/utils";
import { menuItemEmits } from "element-plus";

const tabRouteList = ref([]);
const router = useRouter();
const userStore = useUserStore();
const isShow = ref(false);
const timeShow = ref("");
const treeDefaultProps = ref({
  children: "children",
  value: "title",
  label: (d) => {
    return d.meta.title;
  },
  disabled: (d) => {
    return (
      state.editUseList.findIndex((item) => item.title == d.title) != -1 &&
      (d.children?.length == 0 || !d.children)
    );
  },
});

const state = reactive({
  editUseLoading: false,
  loadingUse: false,
  initUseList: [],
  singleRoute: [],
  editUseTree: [],
  editUseList: [{ title: "" }, { title: "" }, { title: "" }, { title: "" }],
  isEdit: false,
  activeName: 0,
  stayTimer: null,
  stayTime: 0,
  infoList: [
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-info_icon1.png",
      status: "正常",
      num: 20,
      color: "#0082fa",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-info_icon2.png",
      status: "维修",
      num: 20,
      color: "#ffa33a",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-info_icon3.png",
      status: "异常",
      num: 20,
      color: "#fe0e00",
    },
  ],
  useList: [
    // {
    //   path: "/deviceLedger/ledger",
    //   name: "设备台账管理",
    //   icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbtzgl.png",
    // },
    // {
    //   path: "/deviceControl/monitor",
    //   name: "设备管控",
    //   icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbgk.png",
    // },
  ],
  taskList: [
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_dclgd.png",
      name: "待处理工单",
      progress: 23,
      path: "/taskManage/taskCenter/todo",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_yjsj.png",
      name: "应急事件",
      progress: 85,
      path: "/emergencyManage/TaskCenter",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_ts.png",
      name: "投诉",
      progress: 70,
      path: "/repair/repairIndex",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_xj.png",
      name: "巡检",
      progress: 23,
      path: "/checkManage/plan",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_zxd.png",
      name: "咨询单",
      progress: 23,
      path: "/order/consult",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_rwd.png",
      name: "任务单",
      progress: 23,
      path: "/order/orderTask",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_xxtz.png",
      name: "消息通知",
      progress: 23,
      path: "/service",
    },
    {
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_spd.png",
      name: "审批单",
      progress: 23,
      path: "/audit",
    },
  ],
  routeList: [
    {
      name: "设备台账管理",
      pathList: [
        {
          path: "/deviceLedger/baseInfo/assetsType",
          name: "信息资产维护",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/xxzcwh.png",
        },
        {
          path: "/deviceLedger/baseInfo/portType",
          name: "资产端口类别",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/zcdkwh.png",
        },
        {
          path: "/deviceLedger/baseInfo/type",
          name: "设备类型管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbsygl.png",
        },
        {
          path: "/deviceLedger/baseInfo/tag",
          name: "设备标签管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbbqgl.png",
        },
        {
          path: "/deviceLedger/baseInfo/location",
          name: "安装位置管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/azwzgl.png",
        },
        {
          path: "/deviceLedger/ledger",
          name: "设备台账管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbtzgl.png",
        },
        {
          path: "/deviceLedger/deviceUseManage",
          name: "设备使用管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbsygl.png",
        },
      ],
    },
    {
      name: "设备集控",
      pathList: [
        {
          path: "/deviceControl/monitor",
          name: "设备管控",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/sbgk.png",
        },
        {
          path: "/deviceControl/screen",
          name: "大屏巡检",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/dpxj.png",
        },
        {
          path: "/deviceControl/log",
          name: "操作日志",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/czrz.png",
        },
        {
          path: "/deviceControl/recover",
          name: "冰点还原",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/bdhy.png",
        },
        {
          path: "/deviceControl/install",
          name: "软件安装",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/rjaz.png",
        },
      ],
    },
    {
      name: "工单管理",
      pathList: [
        {
          path: "/taskManage/daily",
          name: "运维日报",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/ywrb.png",
        },
        {
          path: "/taskManage/taskCenter/todo",
          name: "待处理工单",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/dclgd.png",
        },
        {
          path: "/taskManage/taskCenter/handled",
          name: "已处理工单",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/yclgd.png",
        },
      ],
    },
    {
      name: "应急管理",
      pathList: [
        {
          path: "/emergencyManage/operation",
          name: "应急预案",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/yjya.png",
        },
        {
          path: "/emergencyManage/TaskCenter",
          name: "应急工单中心",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/yjgdzx.png",
        },
      ],
    },
    {
      name: "备件管理",
      pathList: [
        {
          path: "/spareManage/spareType",
          name: "备件类别",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/bjlb.png",
        },
        {
          path: "/spareManage/spare",
          name: "备件台账",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/bjtz.png",
        },
      ],
    },
    {
      name: "投诉处理",
      pathList: [
        {
          path: "/repair/repairIndex",
          name: "投诉处理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/tscl.png",
        },
      ],
    },
    {
      name: "项目管理",
      pathList: [
        {
          path: "/order/project",
          name: "项目信息管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/xmxxgl.png",
        },
        {
          path: "/order/consult",
          name: "咨询单",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/zxd.png",
        },
        {
          path: "/order/orderTask",
          name: "任务单",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/rwd.png",
        },
      ],
    },
    {
      name: "知识库管理",
      pathList: [
        {
          path: "/knowledgeManage/knowledge",
          name: "知识库管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/zskgl.png",
        },
      ],
    },
    {
      name: "文档管理",
      pathList: [
        {
          path: "/fileManage/templateManage/templateType",
          name: "模板类别管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/mblbgl.png",
        },
        {
          path: "/fileManage/templateManage/fileTemplate",
          name: "模板库",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/mbk.png",
        },
        {
          path: "/fileManage/file",
          name: "文档管理",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/wdgl.png",
        },
      ],
    },
    {
      name: "服务台",
      pathList: [
        {
          path: "/recording",
          name: "电话录音查询",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/dhlycx.png",
        },
        {
          path: "/satisfaction",
          name: "满意度评价",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/mydpj.png",
        },
        {
          path: "/visit",
          name: "工作台回访",
          icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/gzthf.png",
        },
      ],
    },
  ],
  serviceObj: {
    path: "",
    title: "服务台",
    meta: {
      title: "服务台",
      icon: "service",
    },
    children: [
      {
        path: "service",
        name: "Service",
        title: "服务台",
        meta: {
          title: "服务台",
          icon: "service",
        },
      },
      {
        path: "satisfaction",
        name: "Satisfaction",
        title: "满意度评价",
        meta: {
          title: "满意度评价",
          icon: "satisfaction",
        },
      },
      {
        path: "recording",
        name: "Recording",
        title: "电话录音查询",
        meta: {
          title: "电话录音查询",
          icon: "recording",
        },
      },
      {
        path: "visit",
        name: "Visit",
        title: "工单回访",
        meta: {
          title: "工单回访",
          icon: "visit",
        },
      },
    ],
  },
  screenObj: {
    path: "",
    title: "一屏统管",
    meta: {
      title: "一屏统管",
      icon: "screen",
    },
    children: [
      {
        path: "unifyManage",
        name: "UnifyManage",
        title: "一屏统管",
        meta: {
          title: "一屏统管",
          icon: "screen",
        },
      },
    ],
  },
});

const {
  editUseLoading,
  loadingUse,
  initUseList,
  screenObj,
  singleRoute,
  serviceObj,
  editUseTree,
  editUseList,
  isEdit,
  activeName,
  infoList,
  useList,
  taskList,
  routeList,
  stayTimer,
  stayTime,
} = toRefs(state);

// 添加任务筛选相关的数据
const taskFilter = ref("all");
const originalTaskList = ref([]);

// 修改任务类型选项，确保包含所有8种任务类型
const taskTypeOptions = computed(() => {
  return [
    { label: "待处理工单", value: "待处理工单" },
    { label: "应急事件", value: "应急事件" },
    { label: "投诉", value: "投诉" },
    { label: "巡检", value: "巡检" },
    { label: "咨询单", value: "咨询单" },
    { label: "任务单", value: "任务单" },
    { label: "消息通知", value: "消息通知" },
    { label: "审批单", value: "审批单" },
  ];
});

const handleDelIcon = (index) => {
  editUseList.value.splice(index, 1);
  if (editUseList.value.length < 4) {
    editUseList.value.push({ title: "" });
  }
};

const submitBlock = () => {
  console.log(editUseTree.value, editUseList.value, "editUseTree");
  let arr = treeToArray(editUseTree.value);
  const d = {
    deleteIds:
      initUseList.value.map((item) => ({
        menuId: item.menuId,
      })) || [],
  };
  console.log("删除传参", d);
  editUseLoading.value = true;
  deleteUserUse(d)
    .then(async (res) => {
      let d = editUseList.value.map((item) => {
        if (item.title) {
          return {
            userId: userStore.userId,
            resource: "user",
            menuId: arr.find((i) => i.title == item.title).menuId,
            num: 1,
          };
        }
      });
      console.log("批量新增传参", d);
      try {
        await addSchoolUserUseFunctionBatch(d);
      } catch (e) {
        console.log("addSchoolUserUseFunction新增失败", e);
      }
      isEdit.value = false;
      getUseData();
    })
    .finally(() => {
      editUseLoading.value = false;
    });
};

// 修改筛选任务的方法，确保动画从0开始
const filterTasks = () => {
  // 根据筛选条件获取目标任务列表
  let targetTasks = [];
  if (taskFilter.value === "all") {
    targetTasks = JSON.parse(JSON.stringify(originalTaskList.value));
  } else {
    targetTasks = originalTaskList.value.filter(
      (task) => task.name === taskFilter.value
    );
  }

  // 先立即将任务列表更新为目标任务，但进度值设为0
  taskList.value = targetTasks.map((task) => ({
    ...task,
    progress: 0,
  }));

  // 使用setTimeout让DOM有时间更新
  setTimeout(() => {
    // 逐步增加进度值，创建动画效果
    const finalTasks = JSON.parse(JSON.stringify(targetTasks));
    const duration = 1000; // 动画持续时间（毫秒）
    const steps = 20; // 动画步数
    const interval = duration / steps;

    let step = 0;
    const timer = setInterval(() => {
      step++;

      // 计算当前进度百分比
      const progress = step / steps;

      // 更新每个任务的进度
      taskList.value.forEach((task, index) => {
        const targetProgress = finalTasks[index].progress;
        task.progress = Math.round(targetProgress * progress);
      });

      // 动画结束
      if (step >= steps) {
        clearInterval(timer);

        // 设置最终进度值
        taskList.value.forEach((task, index) => {
          task.progress = finalTasks[index].progress;
        });
      }
    }, interval);
  }, 50);
};

// 在组件挂载时启动计时器
onMounted(() => {
  state.stayTime = 0;
  state.stayTimer = setInterval(() => {
    state.stayTime++;
  }, 1000);
  getData();
  getRouteList();
  // 保存原始任务列表
  originalTaskList.value = JSON.parse(JSON.stringify(state.taskList));
});

// 在组件激活时更新原始任务列表
onActivated(() => {
  state.stayTime = 0;
  state.stayTimer = setInterval(() => {
    state.stayTime++;
  }, 1000);
  getData();
  getRouteList();
  getUseData();
  // 重新保存原始任务列表
  originalTaskList.value = JSON.parse(JSON.stringify(state.taskList));
  // 如果有筛选条件，重新应用筛选
  if (taskFilter.value !== "all") {
    filterTasks();
  }
});

onDeactivated(() => {
  console.log("组件被停用");
  console.log("停用时的停留时间：", state.stayTime);

  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览工作台",
    content: "",
    num: state.stayTime,
  });
  clearInterval(state.stayTimer);
});

onBeforeUnmount(() => {
  console.log("组件即将销毁");
  console.log("销毁时的停留时间：", state.stayTime);

  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览访问工作台",
    content: "",
    num: state.stayTime,
  });
  clearInterval(state.stayTimer);
});

function getCurrentTime() {
  const date = new Date();
  const year = date.getFullYear();
  const month = date.getMonth() + 1; // 月份从0开始，需+1
  const day = date.getDate();
  const hours = date.getHours();

  const weekdays = [
    "星期日",
    "星期一",
    "星期二",
    "星期三",
    "星期四",
    "星期五",
    "星期六",
  ];
  const weekday = weekdays[date.getDay()]; // 获取星期几

  // 判断时间段
  let period;
  if (hours >= 0 && hours < 6) {
    period = "凌晨";
  } else if (hours < 12) {
    period = "上午";
  } else if (hours < 14) {
    period = "中午";
  } else if (hours < 18) {
    period = "下午";
  } else if (hours < 20) {
    period = "傍晚";
  } else {
    period = "晚上";
  }

  const formattedMonth = month.toString().padStart(2, "0"); // 月份补零
  const formattedDay = day.toString().padStart(2, "0"); // 日期补零

  return {
    str: `今天是${year}年${formattedMonth}月${formattedDay}日，欢迎进入工作台，开始一天的工作吧！`,
    time: `${period}`,
    weekday,
  };
}

function getCurrentHMS() {
  const date = new Date();
  return {
    hours: date.getHours().toString().padStart(2, "0"), // 时
    minutes: date.getMinutes().toString().padStart(2, "0"), // 分
    seconds: date.getSeconds().toString().padStart(2, "0"), // 秒
  };
}

function startDynamicDisplay() {
  // 若需更新网页显示，可操作DOM：
  setInterval(() => {
    const time = getCurrentHMS();
    timeShow.value = `${time.hours}:${time.minutes}:${time.seconds}`;
  }, 1000);
}
startDynamicDisplay();

const routerPath = (item) => {
  console.log(item);
  sendPointRequest({
    event: "Click",
    eventDescribe: "点击常用功能",
    content: "",
    num: 1,
  });
  if (item.children && item.children.length > 0) {
    if (item.children[0].children && item.children[0].children.length > 0) {
      router.push({
        path: `${item.path}/${item.children[0].path}/${item.children[0].children[0].path}`,
      });
      return;
    }
    router.push({
      path: `${item.path == "/" ? "" : item.path}/${item.children[0].path}`,
    });
  } else {
    let arr = treeFindPath(
      editUseTree.value,
      (d) => d.menuId == item.menuId,
      [],
      "path"
    );
    console.log(arr);
    if (arr.length > 1) {
      router.push({
        path: `${arr.join("/")}`,
      });
    }
  }
};

function getUseData() {
  loadingUse.value = true;
  getSchoolUserUseFunction({ userId: userStore.userId })
    .then((res) => {
      useList.value =
        res.data.map((item) => {
          item.title =
            item.path == "/"
              ? item.children[0]?.meta?.title
              : item?.meta?.title;
          return item;
        }) || [];
      useList.value = useList.value.slice(0, 4);
      initUseList.value = JSON.parse(JSON.stringify(useList.value));
      console.log(initUseList.value, "initUseList");
      useList.value[0] && (editUseList.value[0] = useList.value[0]);
      useList.value[1] && (editUseList.value[1] = useList.value[1]);
      useList.value[2] && (editUseList.value[2] = useList.value[2]);
      useList.value[3] && (editUseList.value[3] = useList.value[3]);
      console.log(useList.value, "常用");
    })
    .catch(() => {})
    .finally(() => {
      loadingUse.value = false;
    });
}
getUseData();

function handleTab(item) {
  console.log(item);
  if (item.path) {
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击待办任务",
      content: `点击待办任务查看详情的${item.name}`,
      num: 1,
    });
    router.push(item.path);
  }
}

function getData() {
  workStatistics()
    .then((res) => {
      if (res.code === 200) {
        // 更新设备概览数据
        infoList.value[0].num = res.data.normalDeviceNum;
        infoList.value[1].num = res.data.maintenanceDeviceNum;
        infoList.value[2].num = res.data.abnormalDeviceNum;

        // 创建一个新的任务列表，使用接口返回的数据
        const newTaskList = [
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_dclgd.png",
            name: "待处理工单",
            progress: res.data.waitProcessingStatisticsVO
              ?.waitProcessingWorkOrderRatio
              ? parseFloat(
                  res.data.waitProcessingStatisticsVO
                    .waitProcessingWorkOrderRatio
                )
              : 0,
            path: "/taskManage/taskCenter/todo",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_yjsj.png",
            name: "应急事件",
            progress: res.data.emergencyEventResponseStatisticsVO
              ?.emergencyEventResponseRatio
              ? parseFloat(
                  res.data.emergencyEventResponseStatisticsVO
                    .emergencyEventResponseRatio
                )
              : 0,
            path: "/emergencyManage/TaskCenter",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_ts.png",
            name: "投诉",
            progress: res.data.complaintsStatisticsVO?.complaintRatio
              ? parseFloat(res.data.complaintsStatisticsVO.complaintRatio)
              : 0,
            path: "/repair/repairIndex",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_xj.png",
            name: "巡检",
            progress: res.data.inspectionStatisticsVO?.inspectionRatio
              ? parseFloat(res.data.inspectionStatisticsVO.inspectionRatio)
              : 0,
            path: "/checkManage/plan",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_zxd.png",
            name: "咨询单",
            progress: res.data.consultationStatisticsVO?.consultationRatio
              ? parseFloat(res.data.consultationStatisticsVO.consultationRatio)
              : 0,
            path: "/order/consult",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_rwd.png",
            name: "任务单",
            progress: res.data.taskStatisticsVO?.taskRatio
              ? parseFloat(res.data.taskStatisticsVO.taskRatio)
              : 0,
            path: "/order/orderTask",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_xxtz.png",
            name: "消息通知",
            progress: res.data.messageStatisticsVO?.messageRatio
              ? parseFloat(res.data.messageStatisticsVO.messageRatio)
              : 0,
            path: "/service",
          },
          {
            icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/icon_spd.png",
            name: "审批单",
            progress: res.data.approvalStatisticsVO?.approvalRatio
              ? parseFloat(res.data.approvalStatisticsVO.approvalRatio)
              : 0,
            path: "/audit",
          },
        ];

        // 更新任务列表
        state.taskList = newTaskList;

        // 保存原始任务列表
        originalTaskList.value = JSON.parse(JSON.stringify(newTaskList));

        // 如果有筛选条件，重新应用筛选
        if (taskFilter.value !== "all") {
          filterTasks();
        }
      }
    })
    .catch((err) => {
      console.error("获取工作台数据失败:", err);
    });
}

const deviceItem = (item) => {
  console.log(item);
  sendPointRequest({
    event: "Click",
    eventDescribe: "点击设备概览跳转",
    content: "",
    num: 1,
  });
  if (item.status === "异常") {
    // 跳转到设备管控页面并触发异常终端查询
    router.push({
      path: "/deviceControl/monitor",
      query: {
        triggerAbnormal: true,
      },
    });
  } else {
    // 跳转到设备管理页面
    router.push({
      path: "/deviceLedger/ledger",
      query: {
        deviceStatus:
          item.status == "正常"
            ? 0
            : item.status == "维修"
            ? 1
            : item.status == "",
        type: "workbench",
      },
    });
  }
};

const handleQuickJump = () => {
  //快速跳转至表单审批
  router.push({ path: "/workflow/orderFlow" });
};

//设置获取路由的方法
const getRouteList = async () => {
  const res = await getRouters();
  console.log(res, "菜单路由");
  tabRouteList.value = filterHiddenItems(res.data);
  singleRoute.value = tabRouteList.value.filter((item) => item.path == "/");
  tabRouteList.value = tabRouteList.value.filter((item) => item.path != "/");
  console.log(tabRouteList.value, singleRoute.value, "tabRouteList");
  try {
    screenObj.value.children[0].menuId = singleRoute.value.find(
      (_) => _.menuId && _.title == screenObj.value.title
    ).menuId;
    // console.log(serviceObj.value, screenObj.value, "serviceObj,screenObj");

    serviceObj.value.children.map((item) => {
      item.menuId = singleRoute.value.find(
        (_) => _.menuId && _.title == item.title
      ).menuId;
    });
    // console.log(serviceObj.value, screenObj.value, "serviceObj,screenObj");
  } catch (error) {
    console.error("获取菜单路由失败:", error);
  }
  tabRouteList.value.push(serviceObj.value);
  editUseTree.value = JSON.parse(JSON.stringify(tabRouteList.value));
  tabRouteList.value.push(screenObj.value);
  editUseTree.value.push(screenObj.value.children[0]);
};

// 过滤所有hidden为false的方法
function filterHiddenItems(items) {
  return items.filter((item) => {
    item.title = item.meta?.title || item.children[0].meta?.title;
    // 如果 hidden 为 true，则过滤掉该对象
    if (
      item.title == "工作台" ||
      (item.hidden == true &&
        serviceObj.value.children.findIndex((_) => _.title == item.title) == -1)
    ) {
      return false;
    }
    // 如果有 children 数组，则递归过滤
    if (item.children && item.children.length > 0) {
      item.children = filterHiddenItems(item.children);
    }
    // 保留 hidden 为 false 的对象
    return true;
  });
}
// 路由跳转方法
const handleRouter = (child, fath, obj, title) => {
  console.log(child, fath, obj, title);
  sendPointRequest({
    event: "Click",
    eventDescribe: "点击快捷入口",
    content: `点击快捷入口的${title}`,
    num: 1,
  });
  addSchoolUserUseFunction({
    userId: userStore.userId,
    menuId: obj.parentId,
    num: 1,
  })
    .then(() => {})
    .catch(() => {});
  if (obj.children && obj.children.length > 0) {
    router.push({ path: `${fath}/${child}/${obj.children[0].path}` });
    return;
  }
  router.push({ path: `${fath}/${child}` });
};
</script>

<style lang="scss" scoped>
.quick-jump-button {
  position: absolute;
  right: 30%;
  bottom: 10%;
  color: #fff;
  padding: 10px 20px;
  border-radius: 5px;
  cursor: pointer;
  // box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
  z-index: 1000;
  transition: background-color 0.3s;
  // 圆形
  border-radius: 50%;
  padding: 0%;
  border-radius: 50%;
  padding: 0%;
}

.work {
  // border: 1px solid red;
  background-color: #f3f3f3;
  padding: 20px;

  &-title {
    // border: 1px solid red;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    font-size: 24px;
    color: #4095e5;
    font-weight: bold;
    gap: 0 10px;

    &::before {
      content: "";
      display: inline-block;
      width: 50px;
      height: 35px;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-title.png");
      background-size: 100% 100%;
    }
  }

  &-main {
    display: flex;
    position: relative;
    // border: 1px solid red;
  }

  &-header {
    display: flex;
    height: 70px;
    line-height: 70px;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/header-bg.png");
    background-size: 100% 100%;
    color: #fff;

    &::before {
      content: "";
      width: 110px;
      height: 70px;
      display: inline-block;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/header-banner.png");
      background-size: 100% 100%;
    }

    &_left {
      font-size: 20px;
      font-weight: bold;
      margin: 0 80px 0 30px;
      letter-spacing: 1px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }

    &_right {
      font-size: 16px;
      letter-spacing: 1px;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
  }

  &-left {
    width: 75%;
    margin-right: 15px;
  }

  &-right {
    width: 24%;
  }

  &-block {
    background-color: #fff;
    border-radius: 10px;
    margin-top: 15px;
    padding: 15px;

    &_tit {
      display: flex;
      align-items: center;
      gap: 0 10px;
      font-size: 16px;
      font-weight: bold;
      margin-bottom: 10px;

      .filter-container {
        padding-bottom: 0;
        margin-left: auto;
      }
    }

    &_info {
      display: flex;
      justify-content: space-between;
      padding: 10px 20px 0;

      &-item {
        display: flex;
        align-items: center;
        gap: 0 20px;
        background: white;

        img {
          width: 100px;
          height: 100px;
        }

        div {
          font-size: 16px;
          text-align: center;

          span {
            font-size: 24px;
            font-weight: bold;
            margin-top: 5px;
            display: inline-block;
          }
        }
      }
    }

    &_tabs {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;

      &-item {
        display: flex;
        align-items: center;
        gap: 0 10px;
        border-radius: 7px;
        padding: 10px;
        width: 190px;
        cursor: pointer;
        background-color: #f3f3f3;
        font-size: 0.7vw;

        img {
          width: 50px;
          height: 50px;
        }
      }
    }

    &_task {
      &-item {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 8px 0;
        border-top: 1px solid #ddd;
        font-size: 14px;

        &:last-child {
          padding-bottom: 0;
        }

        .left {
          width: 70%;

          div {
            display: flex;
            align-items: center;
            gap: 0 5px;
          }

          img {
            width: 15px;
            height: 15px;
          }
        }

        .right {
          color: #ffa33a;
          cursor: pointer;
        }
      }
    }

    &_use {
      display: flex;
      border-top: 1px solid #ddd;
      padding-top: 1vw;
      gap: 0.8vw;
      flex-wrap: wrap;
      min-height: 110px;

      &-item {
        background-color: #4095e5;
        color: #fff;
        display: flex;
        align-items: center;
        width: 47%;
        height: 45px;
        border-radius: 5px;
        font-size: 0.8vw;
        cursor: pointer;
        padding-right: 5px;

        img {
          width: 3vw;
          height: 3vw;
        }
      }
    }

    &_use2 {
      display: flex;
      min-height: 110px;
      border-top: 1px solid #ddd;
      padding-top: 1vw;
      gap: 0.8vw;
      flex-wrap: wrap;

      &-item2 {
        background-color: #4095e5;
        color: #fff;
        display: flex;
        align-items: center;
        width: 100%;
        border-radius: 5px;
        font-size: 0.8vw;
        padding-right: 5px;
        position: relative;
        img {
          width: 3vw;
          height: 3vw;
        }
        .el-icon {
          position: absolute;
          top: 0.3vw;
          right: 0.3vw;
          cursor: pointer;
          &:hover {
            color: #333;
          }
        }
      }
      &-item {
        width: 100%;
        font-size: 0.8vw;
        cursor: pointer;
        padding-right: 20px;
      }
    }

    &.block2 {
      min-height: 450px;
    }

    .sbgl::before {
      width: 25px;
      height: 25px;
      content: "";
      display: inline-block;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-info_title.png");
      background-size: 100% 100%;
    }

    .kjrk::before {
      width: 25px;
      height: 25px;
      content: "";
      display: inline-block;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-block_title1.png");
      background-size: 100% 100%;
    }

    .dbrw::before {
      width: 25px;
      height: 25px;
      content: "";
      display: inline-block;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-block_title2.png");
      background-size: 100% 100%;
    }

    .cygn {
      display: flex;
      justify-content: space-between;
      align-items: center;
      position: relative;
      padding-left: 30px;
      &::before {
        position: absolute;
        width: 25px;
        height: 25px;
        left: 0;
        content: "";
        display: inline-block;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/work-block_title3.png");
        background-size: 100% 100%;
      }
    }
  }
}

.iconmain {
  img {
    width: 1vw;
    height: 1vw;
  }
}

.task-block {
  min-height: 465px;
  display: flex;
  flex-direction: column;

  .work-block_task {
    flex-grow: 1;
  }
}
</style>
