<template>
  <div class="unify-main_charts1">
    <!-- <div class="map" id="map" v-loading="loading"></div> -->
    <!-- <div class="map">
      <div class="model"></div>
    </div> -->
    <div class="address">
      <div class="address-list">
        <div
          class="address-item"
          v-for="(item, index) in addressList"
          :key="index"
          @click="curAddress = index"
        >
          <div class="address-item_label">{{ item.name }}</div>
          <div class="address-sub" v-if="curAddress == index">
            <div
              class="address-subitem"
              :class="opt.checked ? 'active' : ''"
              v-for="(opt, idx) in addressList[curAddress].children"
              :key="idx"
              @click.stop="handleCheck(opt)"
            >
              {{ opt.name }}
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="dialog1">
      <div class="title">设备名称</div>
      <div class="main">
        <div class="row">设备状态：开机/关机</div>
        <div class="row">锁定状态：解锁/锁定</div>
        <div class="row">WIFI：开启/关闭</div>
        <div class="row">安装位置：教学楼-A栋-101</div>
        <div class="row">运行状态：运行良好/CPU占用过高...</div>
      </div>
    </div>
    <div class="dialog2">
      <div class="title">选中设备信息</div>
      <div class="main">
        <div class="row">设备编号：xxxxx</div>
        <div class="row">设备名称：xxxxx</div>
        <div class="row">设备类型：xxxxx</div>
        <div class="row">设备状态：xxxxx</div>
        <div class="row">设备安装位置：xxxxx</div>
        <div class="row">运行状态：xxxxx</div>
      </div>
    </div>
    <div class="control">
      <div class="control-btns">
        <div
          class="control-item"
          v-for="item in controlBtns"
          :key="item.value"
          @click="handleOpeartionRemote(item.value)"
        >
          {{ item.label }}
        </div>
      </div>
      <div class="control-bg"></div>
    </div>
  </div>
</template>
    
    <script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  findPosition,
  getPositionTree,
  positionInfo,
} from "@/api/mediaTeach/position.js";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  loading: true,
  curAddress: -1,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  controlBtns: [
    { label: "远程关机", value: 0 },
    { label: "远程开机", value: 1 },
    { label: "远程重启", value: 2 },
    { label: "定时关机", value: 3 },
    { label: "清除缓存", value: 4 },
    { label: "开启WIFI", value: 5 },
    { label: "关闭WIFI", value: 6 },
    { label: "解锁", value: 7 },
    { label: "锁定", value: 8 },
  ],
  addressList: [
    {
      id: 1,
      name: "教学楼A",
      children: [
        // {
        //   name: "1f",
        //   checked: false,
        // },
        // {
        //   name: "2f",
        //   checked: false,
        // },
      ],
    },
    {
      id: 2,
      name: "教学楼B",
      children: [
        // {
        //   name: "1-1f",
        //   checked: false,
        // },
        // {
        //   name: "2-2f",
        //   checked: false,
        // },
      ],
    },
    {
      id: 3,
      name: "综合楼",
      children: [
        // {
        //   name: "1f",
        //   checked: false,
        // },
        // {
        //   name: "2f",
        //   checked: false,
        // },
      ],
    },
  ],
});

const {
  loading,
  curAddress,
  addressList,
  controlBtns,
  chartsDOM,
  curTime,
  timer,
  timer2,
} = toRefs(state);

const handleCheck = (opt) => {
  for (let i = 0; i < addressList.value.length; i++) {
    for (let j = 0; j < addressList.value[i].children.length; j++) {
      addressList.value[i].children[j].checked = false;
    }
  }
  opt.checked = true;
};

// 加载腾讯地图API
function loadTencentMap() {
  return new Promise((resolve, reject) => {
    if (window.TMap) {
      resolve(window.TMap);
      return;
    }

    window.init = function () {
      // 确保所有服务都加载完成
      const checkService = setInterval(() => {
        if (window.TMap && window.TMap.service) {
          clearInterval(checkService);
          resolve(window.TMap);
        }
      }, 100);
    };

    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=T7GBZ-CWSW5-HXVIJ-IOR44-YLMQ5-ZCBE7&libraries=service&callback=init`;
    document.head.appendChild(script);

    script.onerror = () => reject(new Error("Failed to load TMap script"));
  });
}

function handleOpeartionRemote(val) {
  console.log("远程操作", val);
  proxy.$modal.msgWarning("请选择设备");
  // switch (val) {
  //   // 批量远程关机
  //   case 0:
  //     handleBatchRemote(1, 0);
  //     break;
  //   //批量远程开机
  //   case 1:
  //     handleBatchRemote(2, 0);
  //     break;
  //   // 批量远程重启
  //   case 2:
  //     handleBatchRemote(1, 1);
  //     break;
  //   // 批量远程定时关机
  //   case 3:
  //     handleBatchFixed();
  //     break;
  //   // 批量远程开启WIFI
  //   case 4:
  //     handleBatchWifi(1, 1);
  //     break;
  //   // 批量远程关闭WIFI
  //   case 5:
  //     handleBatchWifi(1, 0);
  //     break;
  //   // 批量远程清除缓存
  //   case 6:
  //     handleBatchClear(1);
  //     break;
  //   // 批量远程锁屏
  //   case 7:
  //     handleBatchLock(1, 0);
  //     break;
  //   // 批量远程解锁
  //   case 8:
  //     handleBatchLock(1, 1);
  //     break;
  //   default:
  //     break;
  // }
  // data.operationParams.remote = "";
}

function getTree() {
  proxy.$modal.loading();
  getPositionTree({})
    .then((res) => {
      console.log("安装位置树状", res);
      if (res.data) {
        state.addressList = res.data.map((item) => ({
          id: item.id,
          name: item.name,
          children: item.children || [],
        }));
        if (state.addressList.length > 0) {
          state.curAddress = 0;
          state.addressList[0].children.length > 0
            ? (state.addressList[0].children[0].checked = true)
            : "";
        }
      }
    })
    .finally(() => proxy.$modal.closeLoading());
}

onMounted(async () => {
  getTree();
  // await loadTencentMap();
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
    
    <style lang="scss" scoped>
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}

.dialog1 {
  position: absolute;
  top: 8.5vw;
  left: 27vw;
  // border: 1px solid red;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/dialog-bg1.png");
  background-size: 100% 100%;
  color: #fff;
  .title {
    padding: 0.4vw 0.8vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/dialog-title1.png");
    background-size: 100% 100%;
  }
  .main {
    padding: 0.4vw 0.8vw 0.8vw;
    display: flex;
    flex-direction: column;
    gap: 0.3vw;
  }
  &::after {
    content: "";
    position: absolute;
    right: -2.4vw;
    bottom: -4vw;
    width: 3vw;
    height: 4.5vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/dialog-line.png");
    background-size: 100% 100%;
  }
}
.dialog2 {
  position: absolute;
  top: 5vw;
  right: 2.5vw;
  width: 25vw;
  height: 40vw;
  padding-top: 2vw;
  padding-left: .8vw;
  // border: 1px solid red;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/dialog-bg2.png");
  background-size: 100% 100%;
  color: #fff;
  .title {
    width: 10vw;
    padding: 0.3vw 0.8vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/dialog-title2.png");
    background-size: 100% 100%;
  }
  .main {
    padding: 0.4vw 0.4vw;
    display: flex;
    flex-direction: column;
    gap: 0.5vw;
  }
}

.unify-main_charts1 {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  height: 56vw;
  margin-top: -13vw;
  gap: 0 1vw;
  overflow-x: hidden;
  position: relative;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/control-bg.png");
  background-size: 100% 100%;
  .map {
    // border: 1px solid yellow;
    width: 100%;
    height: 45vw;
    // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/bg.png");
    // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/control-bg.png");
    background-size: 100% 100%;
    .model {
      // border: 1px solid yellow;
      margin: 15vw auto 0;
      width: 70vw;
      height: 24vw;
      // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/model.png");
      background-size: 100% 100%;
    }
    // border: 1px solid yellow;
    // :deep(.el-loading-mask) {
    //   background: none !important;
    // }
  }
  .address {
    top: 9.5vw;
    left: 4.5vw;
    position: absolute;
    width: 0.01vw;
    background-color: #45576f;
    min-height: 38vw;
    z-index: 11;
    // border: 1px solid yellow;
    &::before {
      position: absolute;
      content: "";
      top: -3.5vw;
      left: -2vw;
      width: 4vw;
      height: 4vw;
      // border: 1px solid yellow;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/tree-head.png");
      background-size: 100% 100%;
    }
    &::after {
      position: absolute;
      content: "";
      bottom: -0.5vw;
      left: -0.5vw;
      width: 1vw;
      height: 1vw;
      // border: 1px solid yellow;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/tree-back.png");
      background-size: 100% 100%;
    }
    &-list {
      position: absolute;
      width: 10vw;
      // border: 1px solid yellow;
      top: 2vw;
      left: 1vw;
      display: flex;
      flex-direction: column;
      gap: 1.5vw 0;
      min-height: 36vw;
    }
    &-item {
      color: #fff;
      text-align: center;
      cursor: pointer;
      &_label {
        position: relative;
        padding: 0.5vw 0;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/tree-tab.png");
        background-size: 100% 100%;
        &::before {
          position: absolute;
          content: "";
          bottom: -0.9vw;
          left: -1.6vw;
          width: 1vw;
          height: 1vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/tree-node.png");
          background-size: 100% 100%;
        }
        &::after {
          content: "";
          position: absolute;
          bottom: -0.4vw;
          left: -0.6vw;
          width: 11vw;
          height: 0.01vw;
          background-color: #1a7aff;
        }
      }
    }
    &-sub {
      padding-top: 2vw;
      text-align: left;
      display: flex;
      flex-direction: column;
      gap: 1vw 0;
    }
    &-subitem {
      // border: 1px solid red;
      padding: 0.5vw;
      color: #7e8fa6;
      cursor: pointer;
      &.active {
        color: #fff;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/tree-tab2.png");
        background-size: 100% 100%;
      }
    }
  }
  .control {
    position: absolute;
    width: 100%;
    bottom: 0;
    &-btns {
      display: flex;
      justify-content: center;
      gap: 0 2vw;
    }
    &-bg {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/control-bg.png");
      background-size: 100% 100%;
      // border: 1px solid red;
      width: 100%;
      height: 3vw;
    }
    &-item {
      // border: 1px solid red;
      cursor: pointer;
      width: 7.5vw;
      text-align: center;
      color: #fff;
      height: 2.8vw;
      font-size: 1.1vw;
      line-height: 2.5vw;
      font-family: "黑体";
      font-weight: bold;
      z-index: 12;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/control/control-btn.png");
      background-size: 100% 100%;
    }
  }
}
</style>