<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="templateTypeNo">
          <el-input
            v-model="queryParams.templateTypeNo"
            placeholder="请输入类别编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="templateTypeName">
          <el-input
            v-model="queryParams.templateTypeName"
            placeholder="请输入类别名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="templateTypeStatus">
          <el-select
            v-model="queryParams.templateTypeStatus"
            placeholder="请选择状态"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建类别</el-button
          >
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleBatchDel"
            :disabled="tableAllSelectedId.length < 1"
            >批量删除类别</el-button
          >
        </el-col>
      </el-row>

      <el-table
        ref="typeTable"
        v-loading="loading"
        :data="typeList"
        border
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column type="selection" label="" width="80" align="center" />
        <el-table-column
          label="类别编号"
          align="center"
          minWidth="120px"
          prop="templateTypeNo"
          show-overflow-tooltip
        />
        <el-table-column
          label="类别名称"
          align="center"
          minWidth="120px"
          prop="templateTypeName"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              :style="{
                color: scope.row.templateTypeNo == 'MBLB000001' ? 'red' : '',
              }"
              >{{ scope.row.templateTypeName || "-" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="类别描述"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.templateTypeDescribe || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="类别状态" align="center" minWidth="120px">
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.templateTypeStatus]"
              effect="dark"
              :type="statusList[scope.row.templateTypeStatus].type"
              >{{ statusList[scope.row.templateTypeStatus].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          minWidth="120px"
          prop="createdTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.createdTime }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="修改时间"
          align="center"
          minWidth="120px"
          prop="updatedTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.updatedTime || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="230"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="success"
              icon="Search"
              @click.stop="handleCheck(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.stop="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.templateTypeNo != 'MBLB000001'"
              link
              type="danger"
              icon="Delete"
              @click.stop="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="typeRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent
      >
        <el-form-item
          v-if="form.templateTypeId"
          label="类别编号"
          prop="templateTypeNo"
        >
          <el-input v-model="form.templateTypeNo" disabled placeholder="-" />
        </el-form-item>
        <el-form-item label="类别名称" prop="templateTypeName">
          <el-input
            v-model="form.templateTypeName"
            :readonly="readonly"
            :disabled="
              form.templateTypeName == '初始类别' && !!form.templateTypeId
            "
            placeholder="请输入类别名称"
          />
        </el-form-item>
        <el-form-item label="类别描述" prop="templateTypeDescribe">
          <el-input
            v-model="form.templateTypeDescribe"
            type="textarea"
            :readonly="readonly"
            placeholder="请输入类别描述"
          />
        </el-form-item>
        <el-form-item label="状态" prop="templateTypeStatus">
          <el-radio-group
            v-model="form.templateTypeStatus"
            :disabled="
              readonly ||
              (form.templateTypeName == '初始类别' && !!form.templateTypeId)
            "
          >
            <el-radio
              v-for="item in statusList"
              :key="item.value"
              :label="item.value"
              :value="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <template v-if="readonly">
            <el-button type="primary" @click="switchToEdit" v-throttle
              >修 改</el-button
            >
            <el-button @click="cancel">关 闭</el-button>
          </template>
          <template v-else>
            <el-button type="primary" @click="submitForm" v-throttle
              >确 定</el-button
            >
            <el-button @click="cancel">取 消</el-button>
          </template>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import {
  getSchoolTemplateTypeList,
  getSchoolTemplateTypeDetail,
  addSchoolTemplateType,
  editSchoolTemplateType,
  deleteSchoolTemplateType,
  deleteBatchSchoolTemplateType,
} from "@/api/fileMamage/templateCategory";
import { findIndexInObejctArr } from "@/utils";

const route = useRoute();
const { proxy } = getCurrentInstance();

const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const readonly = ref(false);
const ids = ref([]);
const statusList = ref([
  { label: "启用", value: 0, type: "success" },
  { label: "停用", value: 1, type: "danger" },
]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const state = reactive({
  typeTable: null,
  typeList: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  typeList_all: [],
  delList: [],
  form: {},
  queryParams: {
    templateTypeNo: "",
    templateTypeName: "",
    templateTypeStatus: "",
    pageNum: 1,
    pageSize: 10,
  },
  rules: {
    templateTypeName: [
      { required: true, message: "类别名称不能为空", trigger: "blur" },
      { max: 20, message: "类别名称最多输入20个字符", trigger: "blur" },
    ],
    templateTypeDescribe: [
      { max: 200, message: "类别描述最多输入200个字符", trigger: "blur" },
    ],
  },
});

const {
  typeTable,
  tableRadio,
  typeList,
  tableAllSelectedId,
  tableAllSelectedRow,
  typeList_all,
  queryParams,
  form,
  rules,
  delList,
} = toRefs(state);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

/** 查询类别列表 */
function getList() {
  loading.value = true;
  console.log(queryParams.value, "查询参数");
  getSchoolTemplateTypeList(queryParams.value).then((response) => {
    console.log("type", response);
    typeList.value = response.data.rows;
    total.value = response.data.total;
    loading.value = false;
    nextTick(() => {
      state.typeList.forEach((item) => {
        if (state.tableAllSelectedId.indexOf(item.templateTypeId) > -1) {
          state.typeTable.toggleRowSelection(item, true);
        } else {
          state.typeTable.toggleRowSelection(item, false);
        }
      });
    });
  });
  getSchoolTemplateTypeList({
    ...queryParams.value,
    pageNum: 1,
    pageSize: 999999,
  }).then((response) => {
    const { rows } = response.data;
    state.typeList_all = rows || [];
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    templateTypeId: undefined,
    templateTypeNo: "",
    templateTypeName: "",
    templateTypeDescribe: "",
    templateTypeStatus: 0,
  };
  proxy.resetForm("typeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  state.queryParams.pageSize = 10;
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  readonly.value = false;
  open.value = true;
  title.value = "添加类别";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  readonly.value = false;
  title.value = "修改类别";
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}
/** 查看按钮操作 */
function handleCheck(row) {
  reset();
  readonly.value = true;
  title.value = "查看类别";
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeRef"].validate((valid) => {
    if (valid) {
      console.log("修改传参", form.value);
      delete form.value.createdTime;
      delete form.value.updatedTime;
      if (form.value.templateTypeId) {
        editSchoolTemplateType(form.value).then(() => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          open.value = false;
        });
      } else {
        addSchoolTemplateType(form.value).then(() => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          open.value = false;
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除类别名称为"' + row.templateTypeName + '"的数据项？')
    .then(function () {
      return deleteSchoolTemplateType({ templateTypeId: row.templateTypeId });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

/** 从查看切换到编辑状态 */
function switchToEdit() {
  readonly.value = false;
  title.value = "修改类别";
}

/** 批量删除按钮操作 */
function handleBatchDel() {
  if (
    state.tableAllSelectedRow.findIndex(
      (_) => _.templateTypeName == "初始类别"
    ) != -1
  ) {
    proxy.$modal.msgWarning("初始类别不能被删除");
    return;
  }
  proxy.$modal
    .confirm("确认批量删除？")
    .then(function () {
      return deleteBatchSchoolTemplateType(state.tableAllSelectedId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("批量删除成功");
    })
    .catch(() => {});
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "templateTypeId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.typeTable.setCurrentRow(null);
      state.typeTable.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.templateTypeId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.typeTable.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.typeTable.setCurrentRow(row);
    state.typeTable.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.templateTypeId) === -1) {
      state.tableAllSelectedId.push(item.templateTypeId);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.templateTypeId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.typeList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.templateTypeId === a[0].templateTypeId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.typeList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.templateTypeId) === -1) {
        state.tableAllSelectedId.push(item.templateTypeId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

getList();
</script>
