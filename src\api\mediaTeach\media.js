import request from '@/utils/request'

// 查询每日班级设备利用率
export function queDeviceRate(data) {
    return request({
        url: '/system/device/queDeviceRate',
        method: 'post',
        data
    })
}

// 查询每日年级设备利用率
export function queDeviceGradeRate(parmas) {
    return request({
        url: '/system/device/queDeviceGradeRate',
        method: 'get',
        parmas
    })
}

// 查询设备当月平均宽带
export function queNetwork(data) {
    return request({
        url: '/system/deviceLog/queNetwork',
        method: 'post',
        data
    })
}

// 查询设备品牌比例
export function queBrandRation(params) {
    return request({
        url: '/system/device/queBrandRation',
        method: 'get',
        params
    })
}

// 查询设备安装位置分布
export function queDeviceAddress(params) {
    return request({
        url: '/system/device/queDeviceAddress',
        method: 'get',
        params
    })
}

// 查询设备类型比例
export function queTypeRation(params) {
    return request({
        url: '/system/device/queTypeRation',
        method: 'get',
        params
    })
}