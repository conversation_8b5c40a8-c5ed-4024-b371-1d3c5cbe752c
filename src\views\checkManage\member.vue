<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>巡检人员</span>
        </div>
      </template>
      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新增巡检人员</el-button
          >
        </el-col>
      </el-row>
      <el-table v-loading="loading" :data="tableList" border>
        <el-table-column
          label="人员编号"
          min-width="120"
          align="center"
          show-overflow-tooltip
          prop="userName"
        />
        <el-table-column
          label="工号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="workId"
        >
          <template #default="{ row }">
            {{ row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="姓名"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="nickName"
        />
        <el-table-column
          label="身份证"
          align="center"
          show-overflow-tooltip
          minWidth="180px"
          prop="idCard"
        >
          <template #default="{ row }">
            {{ row.idCard || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="联系方式"
          align="center"
          show-overflow-tooltip
          min-width="120px"
          prop="phoneNumber"
        />
        <el-table-column
          label="出生日期"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="birthday"
        >
          <template #default="{ row }">
            {{ row.birthday || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="正面照"
          min-width="150"
          align="center"
          show-overflow-tooltip
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="{ row }">
            <el-image
              v-if="row.photos"
              style="width: 50px; height: 50px; display: block; margin: 0 auto"
              :src="row.photos"
              :preview-src-list="[row.photos]"
              fit="contain"
              preview-teleported
            />
            <span v-else>暂无照片</span>
          </template>
        </el-table-column>
        <el-table-column align="center" show-overflow-tooltip minWidth="100px">
          <template #header>
            <div
              style="
                display: flex;
                align-items: center;
                justify-content: center;
                gap: 0 5px;
              "
            >
              是否绑定微信
              <el-tooltip
                class="box-item"
                effect="dark"
                content="没有绑定微信则将收不到通知信息"
                placement="top"
              >
                <el-icon :size="16">
                  <QuestionFilled />
                </el-icon>
              </el-tooltip>
            </div>
          </template>
          <template #default="{ row }">
            {{ row.openId ? "是" : "否" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="100"
          align="center"
          fixed="right"
        >
          <template #default="scope">
            <el-button link type="danger" @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 新增巡检人员对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="dialogVisible"
      title="新增巡检人员"
      width="1000px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <template #header>
        <div class="dialog-header">
          请选择巡检人员
          <el-icon :size="22" style="margin-left: 5px">
            <QuestionFilled />
          </el-icon>
          <span @click="handleToAdd">未找到人员信息？点我去新增</span>
        </div>
      </template>
      <el-table
        v-loading="loadingMember"
        :data="memberList"
        border
        ref="memberRef"
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column type="selection" align="center" min-width="50" />
        <el-table-column
          label="工号"
          align="center"
          prop="number"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="nickName"
          min-width="120"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="性别"
          align="center"
          prop="sex"
          min-width="100"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{
              scope.row.sex == "0" ? "男" : scope.row.sex == "1" ? "女" : "未知"
            }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          prop="deptName"
          min-width="120"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="身份证"
          align="center"
          prop="idCard"
          min-width="180"
        >
          <template #default="{ row, $index }">
            <div
              :class="{
                'el-form-item is-error': row.idCard && row.idCardErr === false,
              }"
              style="margin-bottom: 0"
            >
              <el-input
                @click.stop
                placeholder="请输入"
                size="small"
                v-model.trim="row.idCard"
                maxlength="18"
                clearable
                @blur="handleBlur(row)"
                @clear="handleIdCardClear(row)"
              />
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="出生日期"
          align="center"
          prop="deptName"
          min-width="150"
        >
          <template #default="{ row, $index }">
            <el-date-picker
              style="width: 120px"
              v-model="row.birthday"
              @click.stop
              type="date"
              placeholder="请选择"
              size="small"
              :disabled-date="disabledDate"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="birthChange(row)"
              @clear="handleClear(row)"
            />
          </template>
        </el-table-column>
        <el-table-column label="正面照" align="center" min-width="130">
          <template #default="{ row, $index }">
            <imgUpload
              @update:modelValue="(url) => photoChange(row, url)"
              :limit="1"
              :modelValue="row.photos"
              :fileType="['png', 'jpg', 'jpeg']"
              :isShowTip="false"
              className="w100"
              :styleName="{ width: '100px', height: '100px' }"
            />
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="totalMember > 0"
        :total="totalMember"
        v-model:page="queryParamsMember.pageNum"
        v-model:limit="queryParamsMember.pageSize"
        @pagination="getMembers"
        :background="false"
        :autoScroll="false"
        layout="total,prev,pager,next"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取消</el-button>
          <el-button type="primary" @click="submitForm" v-throttle
            >新增</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="checkMember">
import { ref, reactive, onMounted, toRefs, getCurrentInstance } from "vue";
import {
  userInspectionList,
  addInspection,
  delInspection,
  inspectionPage,
} from "@/api/check";
import { nextTick } from "process";
import { getMaintainList, getRoleList } from "@/api/distribution/member";
import { findIndexInObejctArr } from "@/utils";
import { useRouter } from "vue-router";
import { editInspectionUser } from "@/api/deviceControl";
import { validateIDCard } from "@/utils/idCard";
import imgUpload from "@/components/ImageUpload";

const router = useRouter();
const { proxy } = getCurrentInstance();
const state = reactive({
  totalMember: 0,
  loadingMember: false,
  memberRef: null,
  tableList: [],
  memberList: [],
  memberList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  loading: false,
  dialogVisible: false,
  total: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    roleKey: ["inspection"],
  },
  queryParamsMember: {
    pageNum: 1,
    pageSize: 3,
    roleKey: ["maintain"],
    unInspection: 1,
  },
  formRef: null,
  form: {},
});
const {
  queryParamsMember,
  totalMember,
  loadingMember,
  memberRef,
  memberList_all,
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableList,
  memberList,
  total,
  queryParams,
  loading,
  dialogVisible,
  formRef,
  form,
} = toRefs(state);

const disabledDate = (date) => {
  return new Date() < date;
};

// 跳转运维人员管理页
const handleToAdd = () => {
  router.push({
    path: "/distribution/memberManage",
  });
};

const handleIdCardClear = (data) => {
  editInspectionUser({
    userId: data.userId,
    idCard: data.idCard || "",
    birthday: data.birthday || "",
    photos: data.photos,
  }).then((res) => {
    console.log(res);
  });
};

const handleBlur = (data) => {
  console.log(data);
  console.log({
    userId: data.userId,
    idCard: data.idCard,
    birthday: data.birthday || "",
  });
  if (!data.idCard) return;
  console.log(validateIDCard(data.idCard));
  if (validateIDCard(data.idCard)) {
    data.idCardErr = true; // 验证通过
    editInspectionUser({
      userId: data.userId,
      idCard: data.idCard || "",
      birthday: data.birthday || "",
      photos: data.photos,
    }).then((res) => {
      console.log(res);
    });
  } else {
    data.idCardErr = false; // 验证不通过
  }
};

const handleClear = (data) => {
  editInspectionUser({
    userId: data.userId,
    idCard: data.idCard || "",
    birthday: "",
    photos: data.photos,
  });
};

const birthChange = (data) => {
  if (!data.birthday) return;

  editInspectionUser({
    userId: data.userId,
    idCard: data.idCard || "",
    birthday: data.birthday || "",
    photos: data.photos,
  }).then((res) => {
    console.log(res);
  });
};

const photoChange = (data, url) => {
  console.log(data, url);
  const params = {
    userId: data.userId,
    idCard: data.idCard || "",
    birthday: data.birthday || "",
    photos: url,
  };
  console.log(params, "修改图片传递的参数");

  editInspectionUser(params).then((res) => {
    console.log(res);
    getMembers();
  });
};

const getList = () => {
  state.loading = true;
  console.log(state.queryParams, "state.queryParams");

  inspectionPage(state.queryParams)
    .then((response) => {
      console.log("member", response);
      const { records, total } = response.data;
      state.tableList = records || [];
      state.total = total || 0;
      state.loading = false;
    })
    .catch(() => {
      state.loading = false;
    });
};

// const dialogOpen = () => {
//   getMembers();
// };
const getMembers = () => {
  console.log(state.queryParamsMember, "state.queryParamsMember");

  state.loadingMember = true;
  getRoleList(state.queryParamsMember)
    .then((res) => {
      console.log("运维人员列表", res.data);
      if (res.data && res.data.records) {
        state.memberList = res.data.records || [];
        state.totalMember = res.data.total || 0;
        nextTick(() => {
          state.memberList.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item.userId) > -1) {
              state.memberRef.toggleRowSelection(item, true);
            } else {
              state.memberRef.toggleRowSelection(item, false);
            }
          });
        });
      }
    })
    .finally(() => {
      state.loadingMember = false;
    });
  getRoleList({
    ...state.queryParamsMember,
    pageNum: 1,
    pageSize: 999999,
  }).then((response) => {
    console.log(response);
    // state.memberList = response.data.records || [];
    const { records } = response.data;
    state.memberList_all = records;
  });
};

// 新增按钮点击事件
const handleAdd = () => {
  getMembers();
  state.dialogVisible = true;
};

// 提交表单
const submitForm = () => {
  console.log(state.form);
  // let rows = proxy.$refs.memberRef.getSelectionRows();
  // console.log(rows);
  console.log(tableAllSelectedRow.value);
  console.log({
    userIds: tableAllSelectedId.value,
  });
  if (tableAllSelectedId.value.length == 0) {
    proxy.$modal.msgError("请选择巡检人员");
    return;
  }

  const hasInvalidIdCard = tableAllSelectedRow.value.some((row) => {
    if (row.idCard && !validateIDCard(row.idCard)) {
      proxy.$modal.msgError("身份证格式不正确");
      return true;
    }
    return false;
  });

  if (hasInvalidIdCard) {
    return;
  }

  addInspection({
    userIds: tableAllSelectedId.value,
  })
    .then((response) => {
      console.log(response);

      state.queryParamsMember.pageNum = 1;
      proxy.$modal.msgSuccess("新增成功");
      tableAllSelectedId.value = [];
      tableAllSelectedRow.value = [];
      getList();
      dialogVisible.value = false;
    })
    .catch(() => {});

  // state.formRef.validate((valid) => {
  // if (valid) {
  // addSchoolPoint(state.form).then((response) => {
  //   proxy.$modal.msgSuccess("新增成功");
  //   getList();
  //   state.open = false;
  // });
  // }
  // });
};

// 取消按钮
const cancel = () => {
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  dialogVisible.value = false;
};

// 删除操作
const handleDelete = (row) => {
  proxy.$modal
    .confirm('是否确认删除姓名为"' + row.nickName + '"的巡检人员？')
    .then(async function () {
      await delInspection({ userIds: [row.userId] });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
};

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "userId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.memberRef.setCurrentRow(null);
      state.memberRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.userId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.memberRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.memberRef.setCurrentRow(row);
    state.memberRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
      state.tableAllSelectedId.push(item.userId);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.userId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.memberList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.userId === a[0].userId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.memberList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
        state.tableAllSelectedId.push(item.userId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;

  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}

.avatar-uploader {
  :deep(.el-upload) {
    border: 1px dashed #d9d9d9;
    border-radius: 6px;
    cursor: pointer;
    position: relative;
    overflow: hidden;
    transition: var(--el-transition-duration-fast);

    &:hover {
      border-color: var(--el-color-primary);
    }
  }
}

.avatar-uploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  text-align: center;
  line-height: 178px;
}

.avatar {
  width: 178px;
  height: 178px;
  display: block;
}

.el-upload__tip {
  margin-top: 8px;
  color: #666;
  font-size: 12px;
}

:deep(.el-upload-list--picture-card .el-upload-list__item) {
  width: 100px;
  height: 100px;
}
</style>
