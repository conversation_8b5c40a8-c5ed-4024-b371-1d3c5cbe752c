<template>
  <div class="templateInfo">
    <templateDetail :isAdd="false" :id="route.query.id" />

    <div class="fileInfo-record" v-if="recordList.length > 0">
      <div class="title">变更信息</div>
      <div
        class="spare-main_record-item"
        v-for="(item, index) in recordList.slice(0, 5)"
        :key="index"
      >
        {{
          `${item.name} ${item.role} 于 ${item.time} ${item.type}了${item.spareName}的现库存`
        }}
      </div>
    </div>
  </div>
</template>

<script setup>
import templateDetail from "./components/templateDetail.vue";
import { ref, onMounted, getCurrentInstance, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const { nickName, roles } = useUserStore(); // 获取当前登录的用户信息

// 存放父组件传递的数据
const parentContent = ref(undefined);
// 存放子组件数据
const content = ref(undefined);
// 标识符
const view = ref(undefined);
// 组件引用
const canvasEditor = ref(null);

const recordList = ref([]);


// 初始化数据
onMounted(() => {
  
});

function handleBack() {
  proxy.$tab.closeOpenPage("/fileManage/fileTemplate");
}

function handleSave() {
 
}


</script>

<style lang="scss" scoped>
.templateInfo {
  width: 100%;
  
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
</style>
