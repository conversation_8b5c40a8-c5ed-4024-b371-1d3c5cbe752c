<template>
  <el-dialog
    class="custom-dialog"
    top="30vh"
    title="文件分发"
    v-model="open"
    width="500px"
    :close-on-click-modal="false"
    :close-on-click-escape="false"
    align-center
    @close="handleCancelFile"
  >
    <el-form
      v-loading="uploading"
      :model="fileForm"
      ref="fileRef"
      :rules="fileRules"
    >
      <el-form-item label="文件说明" prop="fileDescription">
        <el-input
          v-model="fileForm.fileDescription"
          placeholder="请输入文件说明"
          type="textarea"
          :rows="1"
          autocomplete="off"
          maxlength="50"
          clearable
        />
      </el-form-item>
      <el-form-item label="选择文件" prop="uploadName" class="fileclass">
        <el-upload
          ref="uploadRef"
          class="upload-demo"
          action="#"
          :limit="1"
          :auto-upload="false"
          :on-change="changeFile"
          :on-exceed="handleExceed"
          :show-file-list="false"
          :http-request="httpRequestFn"
        >
          <template #trigger>
            <el-button type="primary">选择文件</el-button>
          </template>
        </el-upload>
        <div>{{ fileForm.uploadName }}</div>
      </el-form-item>
      <el-form-item label="文件指定传输位置" prop="type" label-position="top">
        <el-radio-group
          v-model="fileForm.type"
          @change="(val) => changeType(val, 'type')"
        >
          <el-radio-button label="桌面" :value="1" />
          <el-radio-button label="自由路径" :value="3" />
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="fileForm.type == 3"
        label=""
        prop="type2"
        label-position="top"
        style="margin-top: -20px"
      >
        <el-radio-group
          v-model="fileForm.type2"
          @change="(val) => changeType(val, 'type2')"
        >
          <el-radio label="C盘" :value="2" />
          <el-radio label="指定位置" :value="0" />
        </el-radio-group>
      </el-form-item>
      <div
        v-show="fileForm.type == 3 && fileForm.type2 == 2"
        style="color: #f56c6c; margin-top: -20px"
      >
        注：选择C盘则默认C盘根目录，无法修改子路径
      </div>
      <el-form-item
        class="filePath"
        v-if="fileForm.type == 3 && fileForm.type2 == 0"
        prop="filePath"
        label-position="left"
        style="margin-top: -20px"
      >
        <template #label></template>
        <el-input
          v-model="fileForm.filePath"
          placeholder="请输入系统传输绝对路径，如 C:\filePath\file123"
          autocomplete="off"
          style="width: 100%"
          type="textarea"
          :rows="1"
          maxlength="50"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div
        style="
          display: flex;
          justify-content: space-between;
          align-items: center;
        "
      >
        <div>
          <div v-if="uploading">
            <el-progress
              :percentage="progress"
              :status="progress == 100 ? 'success' : ''"
              :format="format"
            />
          </div>
        </div>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="handleFileSend"
            :disabled="uploading"
            v-throttle
            >{{
              uploading ? (progress < 100 ? "上传中..." : "发送中...") : "发送"
            }}</el-button
          >
          <el-button v-if="uploading" @click="handleFileSendCancel"
            >终止</el-button
          >
          <el-button @click="handleCancelFile" v-if="!uploading"
            >返回</el-button
          >
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import {
  checkSupportsCrypto,
  generateRequestId,
  getCurrentTime,
} from "@/utils/sliceUpload";
import { getToken } from "@/utils/auth";
import {
  ref,
  computed,
  onMounted,
  getCurrentInstance,
  watch,
  defineProps,
  toRefs,
} from "vue";
import { ElMessage, genFileId } from "element-plus";
import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";
import { isValidWindowsAbsolutePath, isIPv4 } from "@/utils";

const emits = defineEmits(["cancel"]);
const { proxy } = getCurrentInstance();
const props = defineProps({
  fileOpen: {
    type: Boolean,
    default: false,
  },
  sendCodeList: {
    type: Array,
    default: [],
  },
});

watch(
  () => props.fileOpen,
  (val) => {
    if (val) {
      state.open = true;
    }
  }
);

onMounted(() => {
  // 检测浏览器是否支持 crypto.subtle.digest
  checkSupportsCrypto();
});

const headers = ref({ Authorization: "Bearer " + getToken() });
const actionUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/schoolFile/releaseBigFileSlice"
);
const format = (percentage) => (percentage === 100 ? "" : `${percentage}%`);
const state = reactive({
  abortController: null,
  uploadRef: null,
  fileRef: null,
  uploading: false,
  progress: 0,
  open: false,
  fileList: [],
  fileForm: {
    fileDescription: "",
    uploadName: "",
    filePath: "",
    type: 0,
    type2: -1,
  },
  fileRules: {
    fileDescription: [
      { required: true, message: "请输入文件说明", trigger: "blur" },
      {
        pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
        message: "请输入中英文字符串",
        trigger: "blur",
      },
      { min: 1, max: 20, message: "长度不超过20位", trigger: "blur" },
    ],
    uploadName: [
      { required: true, message: "请选择上传文件", trigger: "change" },
    ],
    type: [
      {
        required: true,
        type: "number",
        min: 1,
        max: 3,
        message: "请选择文件指定传输位置",
        trigger: "change",
      },
    ],
    type2: [
      {
        required: true,
        type: "number",
        min: 0,
        max: 2,
        message: "请选择路径",
        trigger: "change",
      },
    ],
    filePath: [
      {
        required: true,
        validator: validatePath,
        trigger: "blur",
      },
    ],
  },
});

const {
  abortController,
  uploadRef,
  fileList,
  fileRef,
  uploading,
  progress,
  open,
  fileForm,
  fileRules,
} = toRefs(state);

function validatePath(rule, value, callback) {
  if (value == "") {
    callback(new Error("请输入文件指定传输位置"));
  } else if (!isValidWindowsAbsolutePath(value)) {
    callback(new Error("请输入正确的系统文件绝对路径"));
  } else {
    callback();
  }
}

// 确定上传
const handleFileSend = () => {
  if (!uploading.value) {
    fileRef.value.validate((valid) => {
      console.log(valid);
      if (valid) {
        console.log(actionUrl.value);
        uploadRef.value.submit();
      }
    });
  }
};

function changeType(val, label) {
  if (label == "type" && val == 1) {
    fileForm.value.type2 = -1;
    fileForm.value.filePath = "";
  } else if (label == "type2" && val == 2) {
    fileForm.value.filePath = "";
  }
}

// 自定义上传
const httpRequestFn = (options) => {
  console.log(options, "自定义上传");

  uploading.value = true;
  progress.value = 0;
  abortController.value = new AbortController();

  // 生成 requestId
  generateRequestId(options.file, 1024 * 1024, function (requestId) {
    try {
      console.log(`生成的 requestId 是: ${requestId}`);

      const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
      let start = 0;
      console.log("文件大小：" + options.file.size);
      let end = Math.min(chunkSize, options.file.size);

      // 上传空文件的函数
      const uploadEmptyFile = (requestId) => {
        const formData = new FormData();
        formData.append("file", new Blob([])); // 空文件
        formData.append("fileDescription", fileForm.value.fileDescription);
        formData.append("deviceCodeList", JSON.stringify(props.sendCodeList));
        formData.append(
          "type",
          fileForm.value.type == 3 ? fileForm.value.type2 : fileForm.value.type
        );
        formData.append(
          "filePath",
          fileForm.value.type == 3 && fileForm.value.type2 == 0
            ? fileForm.value.filePath
            : ""
        );
        formData.append("chunkNumber", 1);
        formData.append("totalChunks", 1);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        );
        formData.append("requestId", requestId);
        formData.append("originalFileName", options.file.name);
        formData.append("isLastChunk", "true"); // 空文件只有一个分片

        fetch(actionUrl.value, {
          method: "POST",
          body: formData,
          headers: headers2.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              progress.value = 100;
              console.log(`[${getCurrentTime()}] 空文件上传成功`);
              return response.json();
            } else {
              console.error("空文件上传失败:", response.statusText);
            }
          })
          .then((res) => {
            console.log(res);
            if (res && res.data) {
              if (res.code == 200) {
                let data = sm2Decrypt(res.data);
                console.log(data);
                if (data == "分发成功") {
                  proxy.$modal.alert(`发送成功`, "提示");
                } else {
                  proxy.$modal.alert(`发送失败`, "提示");
                }
              } else {
                proxy.$modal.alert(`发送失败`, "提示");
              }
              handleCancelFile();
            }
          });
      };

      // 对空文件的特殊处理
      if (options.file.size === 0) {
        console.log("检测到空文件，直接上传...");
        uploadEmptyFile(requestId);
        return;
      }

      const uploadChunk = (chunk, chunkNumber, totalChunks) => {
        const formData = new FormData();
        formData.append("file", new Blob([chunk]));
        formData.append("fileDescription", fileForm.value.fileDescription);
        formData.append("deviceCodeList", JSON.stringify(props.sendCodeList));
        formData.append(
          "type",
          fileForm.value.type == 3 ? fileForm.value.type2 : fileForm.value.type
        );
        formData.append(
          "filePath",
          fileForm.value.type == 3 && fileForm.value.type2 == 0
            ? fileForm.value.filePath
            : ""
        );
        formData.append("chunkNumber", chunkNumber);
        formData.append("totalChunks", totalChunks);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        ); // 使用文件名和大小作为标识符
        formData.append("requestId", requestId); // 添加生成的 requestId
        formData.append("originalFileName", options.file.name); // 添加原始文件名

        // 如果是最后一个分片，则添加一个标志
        if (chunkNumber === totalChunks) {
          formData.append("isLastChunk", "true");
        }

        fetch(actionUrl.value, {
          // 更新为你提供的URL
          method: "POST",
          body: formData,
          headers: headers.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              // 打印当前时间和分片上传成功信息
              if (chunkNumber === totalChunks) {
                progress.value = 100;
                return response.json();
              } else {
                progress.value = Math.round((chunkNumber / totalChunks) * 100);
              }
              console.log(
                `[${getCurrentTime()}] 分片 ${chunkNumber} 已成功上传`
              );
              processNextChunk(chunkNumber + 1);
            } else {
              console.error("分片上传失败:", response.statusText);
              proxy.$modal.alert(`发送失败`, "提示");
            }
          })
          .then((res) => {
            console.log(res);
            if (res && res.data) {
              if (res.code == 200) {
                let data = sm2Decrypt(res.data);
                console.log(data);
                if (data == "分发成功") {
                  proxy.$modal.alert(`发送成功`, "提示");
                } else {
                  proxy.$modal.alert(`发送失败`, "提示");
                }
              } else {
                proxy.$modal.alert(`发送失败`, "提示");
              }
              handleCancelFile();
            }
          });
      };

      const processNextChunk = (chunkNumber) => {
        if (start < options.file.size) {
          const nextChunk = options.file.slice(start, end);
          uploadChunk(
            nextChunk,
            chunkNumber,
            Math.ceil(options.file.size / chunkSize)
          );
          start = end;
          end = Math.min(options.file.size, start + chunkSize);
        } else {
          console.log("所有分片已上传完毕");
        }
      };

      // 开始处理第一个分片
      processNextChunk(1);
    } catch {
      proxy.$modal.alert(
        `${progress.value < 100 ? "上传" : "发送"}失败`,
        "提示"
      );
      handleFileSendCancel();
    }
  });
};

// 覆盖文件
const handleExceed = (files) => {
  uploadRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadRef.value.handleStart(file);
  fileForm.value.uploadName = file.name;
};

// 文件发生变化
const changeFile = (file, files) => {
  console.log(file, files, "change");

  if (file.status == "ready") {
    const isRela = isIPv4(window.location.hostname);
    if (file.size > (isRela ? 1130 : 5) * 1024 * 1024) {
      proxy.$modal.msgWarning(`文件大小不超过${isRela ? "1GB" : "5MB"}`);
      fileList.value = [];
      return;
    }

    fileList.value = [file];
    fileForm.value.uploadName = file.name;
    state.fileRef.validateField("uploadName");
  }
  return false;
};

// 取消上传
const handleFileSendCancel = () => {
  if (abortController.value) abortController.value.abort();
  uploading.value = false;
  progress.value = 0;
};

const handleCancelFile = () => {
  proxy.resetForm("fileRef");
  fileForm.value.type = 0;
  handleFileSendCancel();
  state.open = false;
  fileList.value = [];
  emits("cancel");
};
</script>

<style lang="scss" scoped>
.filePath {
  :deep(.el-form-item__label) {
    padding-right: 0 !important;
  }
}
.el-progress {
  width: 200px;
  :deep(.el-progress__text) {
    min-width: 35px;
  }
}

.fileclass {
  :deep(.el-form-item__content) {
    flex-direction: column;
    align-items: flex-start;
  }
}
</style>