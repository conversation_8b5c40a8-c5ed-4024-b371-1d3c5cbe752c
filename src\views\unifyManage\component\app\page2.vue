<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <div class="block-title">资产覆盖率</div>
        <div class="battery">
          <span>{{ objData.assetCoverageStatistics.coverage }}</span>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">信息资产状况</div>
        <div class="subtitle">信息资产类别占比</div>
        <div class="block block2">
          <Echarts
            id="pieData"
            width="100%"
            height="11.5vw"
            :fullOptions="pieOption"
          />
        </div>
        <div class="subtitle">信息资产类别TOP5</div>
        <div class="block block2">
          <Echarts
            id="barData"
            width="100%"
            height="11.5vw"
            :fullOptions="barOption"
          />
        </div>
      </div>
    </div>

    <div class="col col2">
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list"
          :key="index"
        >
          <div class="card">
            <div class="card-top">{{ item.name }}</div>
            <div class="card-center">{{ item.num }}</div>
            <div class="card-bottom">
              {{ item.increaseNum > 0 ? `+${item.ratio}` : item.ratio }}
            </div>
          </div>
          <div class="tip" v-if="index < 2">
            与昨日{{
              item.increaseNum == 0
                ? "相同"
                : item.increaseNum > 0
                ? `相比多出${item.increaseNum}个`
                : `相比少于${Math.abs(item.increaseNum)}个`
            }}
          </div>
        </div>
      </div>
      <div class="col2-block2">
        <div class="col2-block2_banner"></div>
      </div>
      <div class="col2-block3">
        <div class="flex">
          <div style="flex: 1">
            <div class="block-title">部门资产总数</div>
            <div class="table block">
              <div class="table-row opt-title">
                <div class="table-row_name table-row_head">部门名称</div>
                <div class="table-row_name table-row_head">部门人数</div>
                <div class="table-row_name table-row_head">资产总数</div>
                <div class="table-row_name table-row_head">操作</div>
              </div>
              <div
                class="table-row data"
                v-for="item in objData.assetCoverageStatistics
                  .assetCoverageRanking"
                :key="item.deptName"
              >
                <div class="table-row_name">{{ item.deptName }}</div>
                <div class="table-row_name">
                  {{ item.peopleNum > 99999 ? "99999+" : item.peopleNum }}
                </div>
                <div class="table-row_name">
                  {{ item.assetsNum > 99999 ? "99999+" : item.assetsNum }}
                </div>
                <div
                  class="table-row_name"
                  style="cursor: pointer; color: #4095e5"
                  @click="handleCheck(item)"
                >
                  点击查看
                </div>
              </div>
            </div>
          </div>
          <div style="flex: 1">
            <div class="block-title">今日巡检人员</div>
            <div class="table block">
              <div class="table-row opt-title">
                <div class="table-row_count table-row_head">人员名称</div>
                <div class="table-row_count table-row_head">巡检点位</div>
                <div class="table-row_count table-row_head">完成情况</div>
              </div>
              <div
                class="table-row data"
                v-for="item in checkList"
                :key="item.indicatorsId"
              >
                <div class="table-row_count">{{ item.name }}</div>
                <div class="table-row_count">
                  {{
                    item.prointCodeList.length > 99999
                      ? "99999+"
                      : item.prointCodeList.length
                  }}
                </div>
                <div class="table-row_count">
                  {{
                    item.prointCodeList.length - item.uncheckPointNum > 99999
                      ? "99999+"
                      : item.prointCodeList.length - item.uncheckPointNum
                  }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1">
        <div
          class="col3-block1_item"
          :class="[`item${index + 1}`]"
          v-for="(item, index) in col3_block1_list"
          :key="item.name"
        >
          <div class="left">
            {{ item.name }}
            <div>
              <span>{{ item.day }}</span
              >天 <span>{{ item.hour }}</span
              >小时 <span>{{ item.minute }}</span
              >分钟
            </div>
          </div>
          <div class="right" @click="checkOrder(item.id, index)" v-if="item.id">
            点击查看
          </div>
        </div>
      </div>

      <div class="col3-block2">
        <div class="col3-block2_tabs">
          <div
            class="left"
            :class="curRangeTab == 1 ? 'active' : ''"
            @click="
              curRangeTab = 1;
              getClockData();
            "
          >
            周
          </div>
          <div
            class="center"
            :class="curRangeTab == 2 ? 'active' : ''"
            @click="
              curRangeTab = 2;
              getClockData();
            "
          >
            月
          </div>
          <div
            class="right"
            :class="curRangeTab == 3 ? 'active' : ''"
            @click="
              curRangeTab = 3;
              getClockData();
            "
          >
            年
          </div>
        </div>
        <div
          class="col3-block2_item"
          :class="[`item${index + 1}`]"
          v-for="(item, index) in col3_block2_list"
          :key="item.name"
        >
          <div class="left">
            {{ item.name }}
            <div>
              <span>{{ item.num }}</span
              >{{ item.unit }}
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block3">
        <div class="block-title">运维考勤打卡情况</div>
        <div class="flex">
          <div class="block">
            <div class="subtitle range">
              <i></i>
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="curRange1 == item.value ? 'active' : ''"
                  @click="handleRange(0, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <Echarts
              id="lineData5"
              width="100%"
              height="5.7vw"
              :fullOptions="lineOption5"
            />
          </div>
          <div class="block block-right">
            <div class="item-opt center" v-for="item in optList5">
              {{ item.title }}
              <div class="item-opt_row">
                <span>{{ item.total || 0 }}</span>
                人
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block4">
        <div class="block-title">收到工单数/处理工单数</div>
        <div class="flex">
          <div class="block">
            <div class="subtitle range">
              <i></i>
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="curRange2 == item.value ? 'active' : ''"
                  @click="handleRange(1, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <Echarts
              id="lineData3"
              width="100%"
              height="5.7vw"
              :fullOptions="lineOption3"
            />
          </div>
          <div class="block block-right">
            <div
              class="item-opt"
              v-for="(item, index) in optList2"
              :key="index"
            >
              {{ item.title }}
              <div class="item-opt_row">
                <span>{{ item.tip }}</span>
                <div
                  :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'"
                >
                  {{ item.ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block5">
        <div class="block-title">工单平均处理时间</div>
        <div class="flex">
          <div class="block">
            <div class="subtitle range">
              <i></i>
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="curRange3 == item.value ? 'active' : ''"
                  @click="handleRange(2, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <Echarts
              id="lineData4"
              width="100%"
              height="5.7vw"
              :fullOptions="lineOption4"
            />
          </div>
          <div class="block block-right">
            <div
              class="item-opt"
              v-for="(item, index) in optList4"
              :key="index"
            >
              {{ item.title }}<br />
              <div class="center">
                {{
                  index == 0
                    ? `与昨日${
                        item.increase == 0
                          ? "相同"
                          : item.increase > 0
                          ? `相比增加${item.increase}分钟`
                          : `相比减少${Math.abs(item.increase)}分钟`
                      }`
                    : `平均 ${item.fq.replace(" 天/次", "")} 天处理完一次工单`
                }}
              </div>
              <div class="item-opt_row">
                同比
                <span>{{ item.total || "0分钟" }}</span>
                <div
                  :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'"
                >
                  {{ item.ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      title="查看详情"
      v-model="dialogVisible"
      align-center
      width="500"
    >
      <el-descriptions title="" border :column="1">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门名称"
        >
          {{ deptInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门人数"
        >
          {{ deptInfo.peopleNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="资产总数"
        >
          {{ deptInfo.assetsNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门负责人"
        >
          {{ deptInfo.leaderName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <el-dialog
      class="custom-dialog"
      title="工单详情"
      v-model="dialogVisibleOrder"
      align-center
      width="1050"
    >
      <el-scrollbar max-height="500px">
        <OrderDetail
          :key="curOrderId"
          :showComplaintsBtn="false"
          :troubleId="curOrderId"
          :isHandled="true"
        />
      </el-scrollbar>
      <template #footer>
        <div style="display: flex; justify-content: center">
          <el-pagination
            layout="prev, pager, next"
            v-if="idsList.length > 0"
            :page-size="1"
            :total="idsList.length"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </el-dialog>
  </div>
</template>
    
    <script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import OrderDetail from "@/views/taskCenter/components/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getClockOffsetStatistics,
  getSchoolStatistics,
  getWorkOrderStatistics,
  getAssetsStatistics,
  getMaintainCountStatistics,
  getWorkOrderTimeStatistics,
  getWorkOrderTime,
} from "@/api/unify";
import { schoolInspectionList } from "@/api/check";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  curOrderId: "",
  dialogVisibleOrder: false,
  idsList: [],
  minList: [],
  maxList: [],
  checkList: [],
  rangeList: [
    { label: "周", value: 1 },
    { label: "月", value: 2 },
    { label: "年", value: 3 },
  ],
  curRange1: 1,
  curRange2: 1,
  curRange3: 1,
  curRangeTab: 1,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  minList,
  maxList,
  idsList,
  curOrderId,
  dialogVisibleOrder,
  checkList,
  rangeList,
  curRange1,
  curRange2,
  curRange3,
  curRangeTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  curTime,
  timer,
  timer2,
} = toRefs(state);

const col3_block1_list = ref([
  {
    name: "今日最长处理时间",
    id: "",
    day: 0,
    hour: 0,
    minute: 0,
  },
  {
    name: "今日最短处理时间",
    id: "",
    day: 0,
    hour: 0,
    minute: 0,
  },
]);

const col3_block2_list = ref([
  {
    name: "考勤时间打卡偏差值",
    num: 0.026,
    unit: "h",
  },
  {
    name: "考勤位置打卡偏差值",
    num: 0.025,
    unit: "km",
  },
]);

const col2_block1_list = ref([
  { name: "当前工单量", num: 5936, ratio: "19.2%", increaseNum: 96 },
  { name: "今日新增工单量", num: 5936, ratio: "19.2%", increaseNum: 96 },
  { name: "报障总次数", num: 5936, ratio: "553", increaseNum: 553 },
  { name: "当前报障率", num: "35%", ratio: "9.2%", increaseNum: 9.2 },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "开机率", num: "0", unit: "%" },
  { name: "报废数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "0", unit: "人" },
  { name: "今日执勤", num: "0", unit: "人" },
  { name: "今日轮休", num: "0", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "今日应到运维人员", total: 0, ratio: "0%", num: 0 },
  { title: "实到运维人员", total: 0, ratio: "0", num: 0 },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "处理工单数同比",
    total: 0,
    ratio: "-100%",
    num: -100,
  },
  {
    title: "本年度对比上年度",
    tip: "处理工单数同比",
    total: 5,
    ratio: "0%",
    num: 0,
  },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  {
    title: "今日工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    increase: 15,
  },
  {
    title: "合计工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    fq: "2.45",
  },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "10%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//应用使用时长TOP5柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "15%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        formatter: "{value} h",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//标签使用频率饼图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//系统版本占比饼图
const pieOption15 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: 10,
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

//资产类别占比饼图
const pieOption = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "70%",
              lineHeight: 20,
            },
            b: {
              color: "#6889de",
              fontSize: "60%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const pieOption2 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "操作系统", value: 51 },
          { name: "教学软件", value: 23 },
          { name: "其他软件", value: 9 },
        ],
      },
    ],
  },
});

// 软件到期时间饼图（假数据）
const pieOption3 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "5年以上", value: 34 },
          { name: "3-5年", value: 63 },
          { name: "1-3年", value: 72 },
          { name: "1年以内", value: 46 },
        ],
      },
    ],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 1 },
          { name: "教育平台", value: 3 },
          { name: "办公平台", value: 2 },
        ],
      },
    ],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieOption8 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产端口类型占比饼图
const pieOption9 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieOption12 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内故障资产使用年限占比饼图
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
        },
        lineStyle: {
          color: "#009bab",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        rotate: 0,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计2
const lineOption4 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "20%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [42],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//运维考勤打卡情况
const lineOption5 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

function handleCurrentChange(number) {
  state.curOrderId = state.idsList[number - 1];
}

// 查看工单详情
function checkOrder(id, idx) {
  state.idsList = idx ? state.minList : state.maxList;
  state.curOrderId = state.idsList[0];
  state.dialogVisibleOrder = true;
}

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getWorkOrderLine1();
  }
  if (type == 1) {
    state.curRange2 = tab;
    getWorkOrderLine2();
  }
  if (type == 2) {
    state.curRange3 = tab;
    getWorkOrderLine3();
  }
}

function handleCheck(item) {
  state.deptInfo = {
    ...item,
  };
  state.dialogVisible = true;
}

//获取数据
function getData() {
  schoolInspectionList({ pageNum: 1, pageSize: 5 }).then((res) => {
    console.log("巡检记录列表", res);
    if (res.data) {
      state.checkList = res.data.records || [];
    }
  });
  getSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        assetsTotal,
        restTotal,
        maintenanceTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
      } = res.data;
      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;

      objData.value = {
        ...objData.value,
        sparepartsUseStatistics: res.data.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      // 设置部门名称假数据
      // objData.value.assetCoverageStatistics.assetCoverageRanking[0].deptName =
      //   "教务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[1].deptName =
      //   "德育处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[2].deptName =
      //   "总务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[3].deptName =
      //   "学校办公室";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[4].deptName =
      //   "保卫处";

      // lineOption2.value.options.xAxis.data = [
      //   "2024-10",
      //   "2024-11",
      //   "2024-12",
      //   "2025-01",
      //   "2025-02",
      //   "2025-03",
      // ];
      // lineOption2.value.options.series[0].data = [0, 0, 0, 0, 5, 0];

      if (deviceAppUserStatistics?.appUseTime) {
        // 软件使用时长柱状图赋值
        barOption2.value.options.xAxis.data =
          deviceAppUserStatistics.appUseTime.map((item) => item.month);
        barOption2.value.options.series[0].data =
          deviceAppUserStatistics.appUseTime.map((item) => item.timeTotal);
      }

      if (deviceAppUserStatistics?.appUseType) {
        // 软件使用占比饼图赋值
        pieOption4.value.options.series[0].data =
          deviceAppUserStatistics.appUseType.map((item) => {
            return {
              name: item.appType,
              value: item.num,
            };
          });
      }

      if (assetsTypeStatistics) {
        // 资产类别占比柱状图赋值
        barOption.value.options.xAxis.data = assetsTypeStatistics.map(
          (item) => item.assetsTypeName
        );
        barOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => item.num
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => {
            return {
              name: item.assetsTypeName,
              value: item.num,
            };
          }
        );

        // 假数据
        // pieOption.value.options.series[0].data[0].name = "计算机";
        // pieOption.value.options.series[0].data[1].name = "工作站";
        // pieOption.value.options.series[0].data[2].name = "服务器";
        // pieOption.value.options.series[0].data[3].name = "磁盘";
        // pieOption.value.options.series[0].data[4].name = "电灯泡";

        // barOption.value.options.xAxis.data[0] = "计算机";
        // barOption.value.options.xAxis.data[1] = "工作站";
        // barOption.value.options.xAxis.data[2] = "服务器";
        // barOption.value.options.xAxis.data[3] = "磁盘";
        // barOption.value.options.xAxis.data[4] = "电灯泡";
      }
      console.log(pieOption.value, barOption.value, "资产类别111");

      if (knowledgeBaseStatistics) {
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
          (item) => item.num
        );
      }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];
        optList1.value[0].total = knowledgeBaseMonth;
        optList1.value[0].ratio = knowledgeBaseMonthRise;
        optList1.value[0].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;

        optList1.value[1].total = knowledgeBaseYear;
        optList1.value[1].ratio = knowledgeBaseYearRise;
        optList1.value[1].num =
          knowledgeBaseYearRise?.replace("%", "") * 1 || 0;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsFaultBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
    } = res.data;

    // 标签使用频率
    if (assetsTagUseProportion) {
      const arr = ["小学部", "初中部", "高中部", "易消耗品", "维护过的物品"];
      // pieOption14.value.options.series[0].data = assetsTagUseProportion?.map(
      //   (item, index) => {
      //     return {
      //       name: arr[index],
      //       value: item.num,
      //     };
      //   }
      // );
      // 资产类别占比柱状图赋值
      // barOption3.value.options.xAxis.data =
      //   assetsTagUseProportion?.map(
      //     (item, index) => arr[index] || item.assetsTagName
      //   ) || [];
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map((item, index) => item.assetsTagName) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];
    }

    // console.log(barOption3.value, "barOption3.value");

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      pieOption15.value.options.series[0].data =
        assetsSystemVersionProportion?.map((item, index) => {
          return {
            name: item.assetsSystemVersionName,
            value: item.num,
          };
        });
      // 资产类别占比柱状图赋值
      // barOption4.value.options.xAxis.data = assetsSystemVersionProportion.map(
      //   (item) => item.assetsSystemVersionName
      // );
      // barOption4.value.options.series[0].data =
      //   assetsSystemVersionProportion.map((item) => item.num);
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data =
        deviceTroubleProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data =
        hardwareAssetsUseProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      pieOption8.value.options.series[0].data =
        assetsTypeProportion?.map((item) => {
          return {
            name: item.assetsTypeName,
            value: item.num,
          };
        }) || [];
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      pieOption9.value.options.series[0].data =
        assetsPortProportion?.map((item) => {
          return {
            name: item.assetsPortName,
            value: item.num,
          };
        }) || [];
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data =
        assetsHardDiskProportion?.map((item) => {
          return {
            name: item.assetsHardDiskName,
            value: item.num,
          };
        }) || [];
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data =
        assetsMemoryProportion?.map((item) => {
          return {
            name: item.assetsMemoryName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产品牌占比
    if (assetsFaultBrandProportion) {
      pieOption12.value.options.series[0].data =
        assetsFaultBrandProportion?.map((item) => {
          return {
            name: item.assetsFaultBrandName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产使用年限占比
    if (assetsFaultUseYearProportion) {
      pieOption13.value.options.series[0].data =
        assetsFaultUseYearProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }
  });
}

function getWorkOrderLine1() {
  getMaintainCountStatistics({ range: state.curRange1 }).then((res) => {
    console.log(res, "运维统计");
    let { clockStatistics, shouldClockCountToday, clockedCountToday } =
      res.data;
    optList5.value[0].total = shouldClockCountToday;
    optList5.value[1].total = clockedCountToday;
    if (clockStatistics) {
      lineOption5.value.options.xAxis.data =
        clockStatistics?.map((item) => item.dateLabel) || [];
      lineOption5.value.options.series[0].data =
        clockStatistics?.map((item) => item.shouldClockUsers) || [];
      lineOption5.value.options.series[1].data =
        clockStatistics?.map((item) => item.clockedUsers) || [];
    }
  });
}

function getWorkOrderLine2() {
  getWorkOrderTimeStatistics({ range: state.curRange2 }).then((res) => {
    console.log(res, "收到工单数/处理工单数统计");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      lineOption3.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption3.value.options.series[0].data =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption3.value.options.series[1].data =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
    }
  });
}

function getWorkOrderLine3() {
  getWorkOrderTime({ range: state.curRange3 }).then((res) => {
    console.log(res, "工单平均处理时间统计");
    let { workOrderTimeStatistics } = res.data;
    if (workOrderTimeStatistics) {
      lineOption4.value.options.xAxis.data =
        workOrderTimeStatistics?.map((item) => item.statisticsTime) || [];
      lineOption4.value.options.series[0].data =
        workOrderTimeStatistics?.map((item) => item.avgProcessingTime) || [];
    }
  });
}

function getClockData() {
  getClockOffsetStatistics({ range: state.curRangeTab }).then((res) => {
    console.log(res, "考勤统计");
    if (res.data) {
      const { totalTimeOffset, totalPlaceOffset } = res.data;
      col3_block2_list.value[0].num = Math.abs(totalTimeOffset);
      col3_block2_list.value[1].num = Math.abs(totalPlaceOffset);
    }
  });
}

// 根据分钟获取 天、小时、分钟 的对象
function getDay_Hour_Minute(minute) {
  minute = Math.abs(minute);
  let obj = { day: 0, hour: 0, minute: 0 };
  if (minute < 24 * 60) {
    obj.day = 0;
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  } else {
    obj.day = Math.floor(minute / (24 * 60));
    minute = minute % (24 * 60);
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  }
  return obj;
}

function getOrderData() {
  getClockData();
  getWorkOrderLine1();
  getWorkOrderLine2();
  getWorkOrderLine3();
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let {
        workOrderTotal,
        workOrderYesterdayTotal,
        workOrderTotalIncrease,
        workOrderTodayAddTotal,
        workOrderYesterdayAddTotal,
      } = workOrderProportion;

      col2_block1_list.value[0].num = workOrderTotal;
      col2_block1_list.value[0].ratio = workOrderTotalIncrease;
      col2_block1_list.value[0].increaseNum =
        workOrderTotal - workOrderYesterdayTotal;

      col2_block1_list.value[1].num = workOrderTodayAddTotal;
      col2_block1_list.value[1].ratio = workOrderYesterdayAddTotal
        ? (
            ((workOrderTodayAddTotal - workOrderYesterdayAddTotal) /
              workOrderYesterdayAddTotal) *
              100 || 0
          ).toFixed(2) + "%"
        : "0.00%";
      col2_block1_list.value[1].increaseNum =
        workOrderTodayAddTotal - workOrderYesterdayAddTotal;

      col2_block1_list.value[2].num = workOrderTotal;
      col2_block1_list.value[2].ratio =
        workOrderTotal - workOrderYesterdayTotal;
      col2_block1_list.value[2].increaseNum =
        workOrderTotal - workOrderYesterdayTotal;

      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      col2_block1_list.value[3].num = deviceFaultTotalIncrease;
      col2_block1_list.value[3].ratio =
        (
          (deviceFaultTotalIncrease?.replace("%", "") * 1 || 0) -
          (deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 || 0)
        ).toFixed(2) + "%";
      col2_block1_list.value[3].increaseNum =
        deviceFaultTotal - deviceFaultYesterdayTotal;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList2.value[0].total = processingWorkOrderMonth;
      optList2.value[0].ratio = processingWorkOrderMonthRise;
      optList2.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList2.value[1].total = processingWorkOrderYear;
      optList2.value[1].ratio = processingWorkOrderYearRise;
      optList2.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        allMaxProcessingTime,
        allMinProcessingTime,
        maxTroubleId,
        minTroubleId,
        totalMaxProcessingTime,
        totalMinProcessingTime,
        yesterdayAllMaxProcessingTime,
        yesterdayAverageProcessingTime,
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
        yesterdayTotalAverageProcessingTime,
      } = workOrderProcrssingTime;

      Object.assign(
        col3_block1_list.value[0],
        getDay_Hour_Minute(totalMaxProcessingTime || 0)
      );
      state.maxList = maxTroubleId || [];
      col3_block1_list.value[0].id = state.maxList[0] || "";
      Object.assign(
        col3_block1_list.value[1],
        getDay_Hour_Minute(totalMinProcessingTime || 0)
      );
      state.minList = minTroubleId || [];
      col3_block1_list.value[1].id = state.minList[0] || "";

      optList4.value[0].total = optList4.value[1].total =
        Math.floor(todayAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[0].ratio = riseOfProcessingTime || "0.00%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;
      optList4.value[0].increase = Math.floor(
        (todayAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
          (yesterdayAverageProcessingTime?.replace("分钟", "") * 1 || 0)
      );

      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
      optList4.value[1].ratio =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (
              (((totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) /
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) *
              100
            ).toFixed(2) + "%"
          : "0.00%";
      optList4.value[1].num =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
            (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0)
          : 0;
    }
  });
}

onMounted(() => {
  proxy.$modal.loading();
  getData();
  getOrderData();
  getAssetsData();
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
    
    <style lang="scss" scoped>
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #b0c0e6;
  padding: 0.2vw 0vw 0.2vw 0.5vw;
  font-size: 0.7vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.4vw;
      color: #4095e5;
      &_item {
        border: 1px solid rgba(255, 255, 255, 0);
        padding: 0.05vw 0.3vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/range-bg.png");
          background-size: 100% 100%;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}
.unify-main_charts {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  margin-top: -8vw;
  overflow: hidden;

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-pie_bg.png");
      background-size: 100% 100%;

      &-title {
        font-size: 0.8vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1vw;
        margin-top: 1vw;
        color: #fff;
        background: url("@/assets/screen/app/block-title.png");
        background-size: 100% 100%;
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/block-title_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.5vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          // padding-right: 0.2vw;
          color: #c7d9f9;
        }

        div {
          padding-left: 0.5vw;
        }

        .up {
          color: #2dcd5e;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0;
            width: 0.5vw;
            height: 0.58vw;
            background: url("@/assets/screen/arrow_up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          color: #fe005d;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0.05vw;
            left: 0.05vw;
            width: 0.5vw;
            height: 0.58vw;
            background: url("@/assets/screen/arrow_low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 21vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        height: 5.5vw;
        width: 100%;
        text-align: center;
        line-height: 4.5vw;
        font-size: 1.5vw;
        color: #fff;
      }
    }

    &-block3 {
      //   border: 1px solid red;
      .subtitle {
        padding: 0.57vw;
      }
      .block2 {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 55vw;
    &-block1 {
      width: 100%;
      // border: 1px solid red;
      display: flex;
      justify-content: center;
      font-size: 0.8vw;
      color: #fff;
      gap: 0 4vw;
      &_item {
        // border: 1px solid red;
        display: flex;
        align-items: flex-end;
        letter-spacing: 0.1vw;
        gap: 0 0.5vw;
        .card {
          // border: 1px solid red;
          font-size: 0.4vw;
          width: 4.5vw;
          padding: 0.3vw 0.5vw 0.4vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/progress-bg.png");
          background-size: 100% 100%;
          &-top {
            // border: 1px solid red;
            text-align: center;
            color: #8495b6;
          }
          &-center {
            // border: 1px solid red;
            width: 3.5vw;
            height: 3.5vw;
            text-align: center;
            line-height: 3.5vw;
            font-size: 0.8vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/progress.png");
            background-size: 100% 100%;
            margin: 0.2vw auto;
          }
          &-bottom {
            // border: 1px solid red;
            text-align: center;
            padding: 0.2vw;
            font-size: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/progress-num_bg.png");
            background-size: 100% 100%;
          }
        }
      }
    }

    &-block2 {
      // border: 1px solid red;
      height: 28.8vw;
      position: relative;
      width: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      &_banner {
        // border: 1px solid red;
        width: 48vw;
        height: 26vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/banner.png");
        background-size: 100% 100%;
      }
    }

    &-block3 {
      .table {
        font-size: 0.65vw;
        padding: 0.2vw 0vw 0.5vw;
        margin-top: 0.5vw;

        .opt-title {
          margin-bottom: 0.5vw;
        }

        &.block {
          min-height: 11.7vw;
        }

        &-row {
          display: flex;
          padding: 0.3vw 0;
          text-align: center;
          margin-top: 0.3vw;

          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 6.5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 9vw;
          }
        }
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 21vw;
    height: 100%;

    .subtitle {
      font-size: 0.6vw;
      padding-top: 0.3vw;
    }

    .block {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
      background-size: 100% 100%;
      &-title {
        margin-bottom: 0.4vw;
      }
      &-right {
        display: flex;
        flex-direction: column;
        gap: 0.2vw 0;
      }
    }

    &-block1 {
      display: flex;
      flex-direction: column;
      gap: 0.5vw 0;
      &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.65vw;
        // border: 1px solid red;
        padding: 0.5vw 1vw 0.5vw 4.5vw;
        position: relative;
        &::before {
          // border: 1px solid red;
          content: "";
          width: 2.6vw;
          height: 2.6vw;
          position: absolute;
          left: 1vw;
          top: 0.5vw;
        }
        .left {
          line-height: 1.3vw;
          span {
            font-size: 0.9vw;
          }
        }
        .right {
          text-decoration: underline;
          font-size: 0.75vw;
          cursor: pointer;
        }
        &.item1 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/blue-bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon1.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #4095e5;
          }
        }
        &.item2 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/orange-bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon3.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #fea41d;
          }
        }
      }
    }

    &-block2 {
      display: flex;
      flex-direction: column;
      gap: 0.5vw 0;
      margin-top: 0.95vw;
      &_tabs {
        // border: 1px solid red;
        height: 3vw;
        cursor: pointer;
        display: flex;
        align-items: center;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0.4vw;
        gap: 0 0.5vw;

        .left,
        .center,
        .right {
          flex: 1;
          height: 1.5vw;
          line-height: 1.5vw;
          font-size: 0.9vw;
          text-align: center;
          background-color: #000;
          &.active {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-item.png");
            background-size: 100% 100%;
          }
        }
      }

      &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.65vw;
        // border: 1px solid red;
        padding: 0.5vw 1vw 0.5vw 4.5vw;
        position: relative;
        &::before {
          // border: 1px solid red;
          content: "";
          width: 2.6vw;
          height: 2.6vw;
          position: absolute;
          left: 1vw;
          top: 0.5vw;
        }
        .left {
          line-height: 1.3vw;
          span {
            font-size: 0.9vw;
            padding-right: 0.2vw;
            letter-spacing: 0.05vw;
          }
        }
        .right {
          text-decoration: underline;
          font-size: 0.75vw;
          cursor: pointer;
        }
        &.item1 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-blue_bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon1.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #4095e5;
          }
        }
        &.item2 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-green_bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon2.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #0bfeb4;
          }
        }
      }
    }

    &-block5 {
      .item-opt {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/trend-bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0.5vw;
        &_row {
          margin: 0;
          padding: 0;
        }
      }
      .center {
        margin: 0.2vw auto;
        color: #4095e5;
        text-align: left;
      }
    }
  }
}
</style>