<!-- views/emergency/workOrderHandle.vue -->
<template>
  <div class="taskInfo">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <!-- 预案信息 -->
      <el-descriptions title="预案信息" border :column="3" v-if="planInfo">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案编号"
        >
          {{ planInfo?.code || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案类型"
        >
          {{ planInfo?.type || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案文件"
        >
          <el-button
            v-if="planInfo?.fileUrl"
            type="primary"
            @click="downloadPlanFile"
          >
            点击查看
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案名称"
        >
          {{ planInfo?.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          :span="2"
          label="预案内容"
        >
          {{ planInfo?.content || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="修改时间"
        >
          {{ planInfo?.updateTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ planInfo?.createTime || "-" }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 工单基础信息 -->
      <order-base-info
        :work-order-data="workOrderData"
        :show-report="true"
        :readonly="false"
        :is-processing="isProcessing"
        @update:work-order-data="workOrderData = $event"
        @uploadReport="uploadDialogVisible = true"
      >
        <!-- 自定义应急预案插槽 -->
        <template #emergencyPlan>
          {{ workOrderData.emergencyPlan || "-" }}
        </template>
        <!-- 修改处理报告插槽 -->
        <template #report>
          <div class="report-container">
            <div class="upload-btn-wrapper">
              <el-button type="primary" @click="uploadDialogVisible = true">
                上传报告
              </el-button>
            </div>
            <div class="report-list">
              <template
                v-if="workOrderData.reports && workOrderData.reports.length > 0"
              >
                <div
                  v-for="(report, index) in workOrderData.reports"
                  :key="index"
                  class="report-item"
                >
                  <el-button
                    type="primary"
                    link
                    @click="downloadFile(report.fileUrl, report.fileName)"
                  >
                    {{ report.fileName }}
                  </el-button>
                </div>
              </template>
              <span v-else>-</span>
            </div>
          </div>
        </template>
      </order-base-info>

      <!-- 关联备件表格 -->
      <related-parts-table v-model:parts="relatedParts" :readonly="true">
      </related-parts-table>

      <!-- 关联人员表格 -->
      <related-personnel-table
        :personnel="relatedPersonnel"
        :readonly="workOrderData.status === 'archived'"
        :show-notify-button="workOrderData.status === 'pending'"
        @refresh="getWorkOrderData"
      >
      </related-personnel-table>

      <!-- 处理按钮区域 -->
      <div class="taskInfo-btns">
        <template v-if="!isProcessing">
          <el-button type="primary" @click="handleArchive">归档</el-button>
          <el-button @click="goBack">返回</el-button>
        </template>
      </div>
    </el-card>
    <!-- 选择应急预案对话框 -->
    <el-dialog
      v-model="showEmergencyPlanDialog"
      title="选择应急预案"
      width="50%"
    >
      <el-table
        :data="emergencyPlans"
        border
        style="width: 100%"
        @selection-change="handleEmergencyPlanSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="预案编号" />
        <el-table-column prop="name" label="预案名称" />
        <el-table-column prop="type" label="预案类型">
          <template #default="scope">
            {{ getEmergencyPlanTypeName(scope.row.type) }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEmergencyPlanDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleSelectEmergencyPlan"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 选择备件/人员弹窗 -->
    <el-dialog
      v-model="dialogVisible"
      :title="title"
      width="50%"
      destroy-on-close
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item>
          <el-input
            v-model="queryParams.faultDesc"
            :placeholder="
              title === '请选择关联备件'
                ? '请输入备件编号/名称搜索'
                : '请输入工号/姓名搜索'
            "
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="ledgerList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="isSelectable"
        />
        <!-- 备件表格列 -->
        <template v-if="title === '请选择关联备件'">
          <el-table-column prop="code" label="备件编号" />
          <el-table-column prop="name" label="备件名称" />
          <el-table-column prop="typeName" label="备件类别" />
          <el-table-column prop="specifications" label="规格" />
          <el-table-column prop="stock" label="库存" />
        </template>
        <!-- 人员表格列 -->
        <template v-else>
          <el-table-column prop="workId" label="工号" />
          <el-table-column prop="nickName" label="姓名" />
          <el-table-column prop="sex" label="性别">
            <template #default="scope">
              {{ scope.row.sex === "0" ? "男" : "女" }}
            </template>
          </el-table-column>
          <el-table-column prop="sysDept.deptName" label="所属部门" />
          <el-table-column prop="roleName" label="岗位" />
        </template>
      </el-table>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传报告对话框 -->
    <el-dialog
      v-model="uploadDialogVisible"
      title="上传报告"
      width="400"
      :close-on-click-modal="false"
    >
      <div class="upload-content">
        <el-upload
          class="upload-demo"
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".doc,.docx,.xls,.xlsx"
        >
          <el-button type="primary">点击上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只允许上传word、excel格式文件，大小不超过20M
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpload">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
// 导入公共组件
import OrderBaseInfo from "@/views/emergencyManage/components/OrderBaseInfo.vue";
import RelatedPartsTable from "@/views/emergencyManage/components/RelatedPartsTable.vue";
import RelatedPersonnelTable from "@/views/emergencyManage/components/RelatedPersonnelTable.vue";
// 导入API
import {
  schoolPlanWorkOrderInfo,
  schoolPlanInfo,
  updateSchoolPlanWorkOrder,
  schoolPlanSpareInfo,
  uploadSchoolPlanWorkOrder,
} from "@/api/emergency";
import { sparePartsPage } from "@/api/spare";
import { listUser } from "@/api/system/user";

const router = useRouter();
const route = useRoute();

// 页面状态
const readonly = ref(false);
const isProcessing = ref(false);

// 对话框显示状态
const showEmergencyPlanDialog = ref(false);
const dialogVisible = ref(false);
const title = ref("");

// 选中的数据
const selectedEmergencyPlan = ref(null);
const selectedParts = ref([]);
const selectedPersonnel = ref([]);

// 数据
const workOrderData = ref({});
const relatedParts = ref([]);
const relatedPersonnel = ref([]);
const emergencyPlans = ref([]);
const parts = ref([]);
const personnel = ref([]);

// 表格选择
const emergencyPlanSelection = ref([]);
const partsSelection = ref([]);
const personnelSelection = ref([]);

// 添加预案信息的响应式对象
const planInfo = ref({
  id: "",
  code: "",
  name: "",
  type: "",
  content: "",
  fileName: "",
  fileUrl: "",
  createTime: "",
  updateTime: "",
});

// 上传相关的响应式变量
const uploadAction =
  import.meta.env.VITE_APP_BASE_API +
  "/emergency/schoolPlan/uploadSchoolPlanWorkOrder";
const uploadDialogVisible = ref(false);
const fileList = ref([]);

// 添加新的响应式变量
const ledgerList = ref([]);
const total = ref(0);
const loading = ref(false);
const tableRef = ref(null);
const multipleSelection = ref([]);
const queryParams = ref({
  current: 1,
  size: 10,
  codeAndName: "",
  faultDesc: "",
  status: "0",
});

// 获取预案类型名称
const getEmergencyPlanTypeName = (type) => {
  const typeMap = {
    natural: "自然灾害",
    accident: "事故灾难",
    health: "公共卫生",
    security: "社会安全",
  };
  return typeMap[type] || type;
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 修改下载预案文件方法
const downloadPlanFile = () => {
  if (!planInfo.value?.fileUrl) {
    ElMessage.warning("预案文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let fileUrl = planInfo.value.fileUrl;
    if (fileUrl.startsWith("http:")) {
      fileUrl = fileUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = fileUrl;
    link.setAttribute("download", planInfo.value.fileName || "预案文件");
    link.setAttribute("target", "_blank"); // 添加target属性
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 获取预案详情
const getPlanDetail = async (planId) => {
  try {
    const res = await schoolPlanInfo(planId);
    if (res.code == 200) {
      const currentPlan = res.data;
      planInfo.value = {
        id: currentPlan.id || "",
        code: currentPlan.code || "",
        name: currentPlan.name || "",
        type: currentPlan.type || "",
        content: currentPlan.content || "",
        fileName: currentPlan.fileName || "",
        fileUrl: currentPlan.fileUrl || "",
        createTime: currentPlan.createTime || "",
        updateTime: currentPlan.updateTime || "",
      };
    } else {
      ElMessage.warning("未找到相关预案信息");
    }
  } catch (error) {
    console.error("获取预案详情失败:", error);
    ElMessage.error("获取预案详情失败");
  }
};

// 获取工单数据
const getWorkOrderData = () => {
  try {
    const orderId = route.query.id;
    if (!orderId) {
      ElMessage.error("工单ID不能为空");
      router.back();
      return;
    }

    schoolPlanWorkOrderInfo(orderId)
      .then((res) => {
        if (res.code === 200) {
          console.log("工单详情数据:", res.data);
          const {
            planWorkOrderInfo,
            planWorkOrderPeopleInfo,
            planInfo: responsePlanInfo,
          } = res.data;

          // 处理报告和上传人的显示逻辑
          let reports = [];
          let uploaderName = "-";

          // 如果创建者上传了报告，只显示创建者的报告
          if (
            planWorkOrderInfo.reportFileName &&
            planWorkOrderInfo.reportFileUrl
          ) {
            reports.push({
              fileName: planWorkOrderInfo.reportFileName,
              fileUrl: planWorkOrderInfo.reportFileUrl,
            });
            uploaderName = planWorkOrderInfo.uploadUser;
          } else {
            // 创建者未上传报告时，显示关联人的报告（如果有）
            const relatedPersonsWithReport =
              planWorkOrderPeopleInfo?.filter(
                (person) => person.reportFileName && person.reportFileUrl
              ) || [];

            if (relatedPersonsWithReport.length > 0) {
              relatedPersonsWithReport.forEach((person) => {
                reports.push({
                  fileName: person.reportFileName,
                  fileUrl: person.reportFileUrl,
                });
              });
              uploaderName = relatedPersonsWithReport
                .map((person) => person.nickName)
                .join("、");
            }
          }

          // 设置工单基础信息
          workOrderData.value = {
            orderId: planWorkOrderInfo.code,
            orderType: responsePlanInfo?.type || "-",
            description: planWorkOrderInfo.remark,
            emergencyPlan: responsePlanInfo?.name || "-",
            emergencyPlanId: responsePlanInfo?.id,
            status:
              planWorkOrderInfo.status == 0
                ? "待上传"
                : planWorkOrderInfo.status == 1
                ? "已归档"
                : "待上传",
            reports: reports.length > 0 ? reports : [],
            uploaderName: uploaderName,
            level: planWorkOrderInfo.level || "-",
            createTime: planWorkOrderInfo.createTime,
            handleTime: planWorkOrderInfo.processingTime,
          };

          // 设置预案信息
          if (responsePlanInfo) {
            planInfo.value = {
              id: responsePlanInfo.id,
              code: responsePlanInfo.code || "",
              name: responsePlanInfo.name || "",
              type: responsePlanInfo.type || "",
              content: responsePlanInfo.content || "",
              fileName: responsePlanInfo.fileName || "",
              fileUrl: responsePlanInfo.fileUrl || "",
              createTime: responsePlanInfo.createTime || "",
              updateTime: responsePlanInfo.updateTime || "",
            };
          }

          // 设置关联人员数据
          if (planWorkOrderPeopleInfo && planWorkOrderPeopleInfo.length > 0) {
            relatedPersonnel.value = planWorkOrderPeopleInfo.map((person) => ({
              workerId: person.userId,
              workId: person.workId || "-",
              name: person.nickName,
              department: person.sysDept?.deptName || "-",
              relateTime: person.associatedTime,
              workOrderId: route.query.id,
              notifyTime: person.notifyTime,
              reportFileName: person.reportFileName,
              reportFileUrl: person.reportFileUrl,
              reportStatus: person.reportFileName ? "已上传" : "未上传",
            }));
          }

          // 获取关联备件数据
          getRelatedParts(orderId);
        } else {
          ElMessage.error(res.msg || "获取工单详情失败");
          router.back();
        }
      })
      .catch((error) => {
        console.error("获取工单详情失败:", error);
        ElMessage.error("获取工单详情失败");
        router.back();
      });
  } catch (error) {
    console.error("获取工单数据失败:", error);
    ElMessage.error("获取工单数据失败");
    router.back();
  }
};

// 获取关联备件数据
const getRelatedParts = (planWorkOrderId) => {
  const params = {
    planWorkOrderId,
    pageNum: 1,
    pageSize: 10,
  };

  schoolPlanSpareInfo(params)
    .then((res) => {
      if (res.code === 200) {
        console.log("关联备件数据:", res.data);

        relatedParts.value = res.data.records.map((item) => ({
          id: item.sparePartsId, // 备件ID，用于提交
          partId: item.sparePartsCode, // 备件编号，用于显示
          partName: item.name, // 备件名称
          quantity: item.num || 1, // 消耗量
          useDate: item.createTime, // 使用时间
          stock: item.stock || 0, // 添加库存字段
        }));
      } else {
        ElMessage.warning(res.msg || "获取关联备件数据失败");
      }
    })
    .catch((error) => {
      console.error("获取关联备件数据失败:", error);
      ElMessage.warning("获取关联备件数据失败");
    });
};

// 获取应急预案数据
/* const getEmergencyPlans = () => {
  try {
    const plans = JSON.parse(localStorage.getItem('emergencyPlans') || '[]')
    if (plans.length === 0) {
      const defaultPlans = [
        {
          id: 'EP001',
          name: '设备故障应急预案',
          type: 'accident'
        },
        {
          id: 'EP002',
          name: '系统异常应急预案',
          type: 'security'
        }
      ]
      localStorage.setItem('emergencyPlans', JSON.stringify(defaultPlans))
      emergencyPlans.value = defaultPlans
    } else {
      emergencyPlans.value = plans
    }
  } catch (error) {
    console.error('获取应急预案数据失败:', error)
  }
} */

// 获取备件数据
/* const getParts = () => {
  try {
    const spareList = JSON.parse(localStorage.getItem('WHYWPT_SPARELIST_ALL') || '[]')
    if (spareList.length === 0) {
      const defaultSpares = [
        {
          spareNumber: 'SP001',
          spareName: '螺丝',
          spareStore: 100,
          updateTime: new Date().toISOString()
        },
        {
          spareNumber: 'SP002',
          spareName: '齿轮',
          spareStore: 50,
          updateTime: new Date().toISOString()
        }
      ]
      localStorage.setItem('WHYWPT_SPARELIST_ALL', JSON.stringify(defaultSpares))
      parts.value = defaultSpares.map(item => ({
        partId: item.spareNumber,
        partName: item.spareName,
        quantity: item.spareStore,
        updateTime: item.updateTime
      }))
    } else {
      parts.value = spareList.map(item => ({
        partId: item.spareNumber,
        partName: item.spareName,
        quantity: item.spareStore,
        updateTime: item.updateTime
      }))
    }
  } catch (error) {
    console.error('获取备件数据失败:', error)
  }
} */

// 获取人员数据
/* const getPersonnel = () => {
  personnel.value = [
    {
      workerId: 'EMP001',
      name: '张三',
      department: '维修部',
    },
    {
      workerId: 'EMP002',
      name: '李四',
      department: '设备部',
    },
    {
      workerId: 'EMP003',
      name: '王五',
      department: '维修部',
    }
  ]
} */

// 修改处理完毕按钮为归档按钮的处理函数
const handleArchive = async () => {
  try {
    // 检查是否所有关联人员都已上传报告
    /* const hasUnuploadedReports = relatedPersonnel.value.some(person => !person.reportFileName)
    if (hasUnuploadedReports) {
      ElMessage.warning('存在关联人员未上传处理报告，无法进行归档')
      return
    } */

    const params = {
      id: route.query.id,
      remark: workOrderData.value.description || "",
      status: 1, // 设置状态为已归档
      planId: workOrderData.value.emergencyPlanId || "",
      userIds: relatedPersonnel.value
        .map((person) => person.workerId)
        .filter((id) => id)
        .map((id) => Number(id)), // 添加逗号
      sparePartsDetails: relatedParts.value.map((part) => ({
        sparePartsId: part.id,
        type: 0,
        resourceType: 2,
        resource: "应急事件",
        num: Number(part.quantity),
      })),
    };

    console.log("提交的参数：", params);

    // 调用更新接口
    const res = await updateSchoolPlanWorkOrder(params);
    if (res.code === 200) {
      ElMessage.success("归档成功");
      router.back();
    } else {
      // ElMessage.error(res.msg || '归档失败')
    }
  } catch (error) {
    console.error("归档失败:", error);
    // ElMessage.error('归档失败')
  }
};

// 获取工单状态名称
const getStatusName = (status) => {
  const statusMap = {
    pending: "待上传",
    waiting_upload: "待上传",
    waiting_archive: "待归档",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 上传前的验证
const beforeUpload = (file) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split(".").pop();
  const allowedExtensions = ["doc", "docx", "xls", "xlsx"];

  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error("只能上传Word或Excel格式的文件!");
    return false;
  }
  if (!isLt20M) {
    ElMessage.error("文件大小不能超过20MB!");
    return false;
  }
  return true;
};

// 修改文件改变处理函数
const handleFileChange = (file, uploadFileList) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split(".").pop();
  const allowedExtensions = ["doc", "docx", "xls", "xlsx"];

  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error("只能上传Word或Excel格式的文件!");
    fileList.value = uploadFileList.filter((f) =>
      allowedExtensions.includes(f.name.toLowerCase().split(".").pop())
    );
    return false;
  }
  if (!isLt20M) {
    ElMessage.error("文件大小不能超过20MB!");
    fileList.value = uploadFileList.filter((f) => f.size / 1024 / 1024 < 20);
    return false;
  }

  fileList.value = uploadFileList;
};

// 确认上传
const confirmUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning("请先上传文件");
    return;
  }

  try {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    formData.append("planWorkOrderId", route.query.id);
    formData.append("type", "5");
    formData.append("isUpdate", "true");

    const res = await uploadSchoolPlanWorkOrder(formData);

    if (res.code === 200) {
      ElMessage.success("报告上传成功");
      uploadDialogVisible.value = false;
      fileList.value = []; // 清空文件列表
      // 重新获取工单数据
      getWorkOrderData();
    } else {
      // ElMessage.error(res.msg || '上传失败')
    }
  } catch (error) {
    // console.error('上传失败:', error)
    // ElMessage.error('上传失败')
  }
};

// 获取备件列表数据
const getSparePartsList = () => {
  loading.value = true;
  const params = {
    pageNum: queryParams.value.current,
    pageSize: queryParams.value.size,
    codeAndName: queryParams.value.codeAndName,
  };
  sparePartsPage(params)
    .then((res) => {
      loading.value = false;
      if (res.code === 200) {
        ledgerList.value = res.data.records;
        total.value = res.data.total;
      } else {
        ElMessage.error("获取备件列表失败");
      }
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取备件列表失败:", err);
      ElMessage.error("获取备件列表失败");
    });
};

// 获取人员列表数据
const getList = () => {
  loading.value = true;
  const params = {
    pageNum: queryParams.value.current,
    pageSize: queryParams.value.size,
    nickName: queryParams.value.faultDesc, // 使用 nickName 作为搜索参数
  };
  listUser(params)
    .then((res) => {
      loading.value = false;
      if (res.code === 200) {
        // 修改筛选逻辑，使用includes()检查是否包含运维人员角色
        ledgerList.value = res.data.records.filter(
          (user) => user.roleName && user.roleName.includes("运维人员")
        );
        total.value = ledgerList.value.length; // 更新总数为过滤后的数量
      } else {
        ElMessage.error("获取人员列表失败");
      }
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取人员列表失败:", err);
      ElMessage.error("获取人员列表失败");
    });
};

// 搜索
const handleSearch = () => {
  queryParams.value.current = 1;
  if (title.value === "请选择关联备件") {
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList();
  } else {
    getList();
  }
};

// 重置
const resetQuery = () => {
  queryParams.value = {
    current: 1,
    size: 10,
    codeAndName: "",
    faultDesc: "",
    status: "0",
  };
  if (title.value === "请选择关联备件") {
    getSparePartsList();
  } else {
    getList();
  }
};

// 选择变更处理
const handleSelectionChange = (selection) => {
  multipleSelection.value = selection;
};

// 提交选择
const submitForm = () => {
  if (title.value === "请选择关联备件") {
    // 转换新选择的备件数据 - 保存备件的 id
    const newParts = multipleSelection.value.map((item) => ({
      id: item.id, // 保存备件id
      partId: item.code, // 备件编号用于显示
      partName: item.name,
      quantity: 1,
      useDate: new Date().toISOString(),
    }));

    // 合并新旧数据，使用 partId 去重
    const uniqueParts = [...relatedParts.value];
    newParts.forEach((newPart) => {
      if (!uniqueParts.some((part) => part.partId === newPart.partId)) {
        uniqueParts.push(newPart);
      }
    });
    relatedParts.value = uniqueParts;
  } else {
    // 修改人员数据的转换
    const newPersonnel = multipleSelection.value.map((item) => ({
      workerId: item.userId, // 保存用户ID用于提交
      workId: item.workId || "-", // 显示工号
      name: item.nickName, // 显示姓名
      department: item.sysDept?.deptName || "-", // 部门
      relateTime: formatDate(new Date()), // 格式化关联时间
      reportStatus: "未上传", // 默认报告状态
    }));

    // 合并新旧数据，使用 workerId 去重
    const uniquePersonnel = [...relatedPersonnel.value];
    newPersonnel.forEach((newPerson) => {
      if (
        !uniquePersonnel.some(
          (person) => person.workerId === newPerson.workerId
        )
      ) {
        uniquePersonnel.push(newPerson);
      }
    });
    relatedPersonnel.value = uniquePersonnel;
  }

  dialogVisible.value = false;
  ledgerList.value = [];
  multipleSelection.value = [];
};

// 判断行是否可选
const isSelectable = (row) => {
  if (title.value === "请选择关联备件") {
    // 检查备件是否已存在
    return !relatedParts.value.some((part) => part.partId === row.code);
  } else {
    // 检查人员是否已存在 - 使用userId进行比较
    return !relatedPersonnel.value.some(
      (person) => person.workerId === row.userId
    );
  }
};

// 修改表格行的样式
const tableRowClassName = ({ row }) => {
  if (!isSelectable(row)) {
    return "disabled-row";
  }
  return "";
};

// 返回上一页
const goBack = () => {
  router.go(-1);
  proxy.$tab.closeOpenPage();
};

const downloadFile = (fileUrl, fileName) => {
  if (!fileUrl) {
    ElMessage.warning("文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let downloadUrl = fileUrl;
    if (downloadUrl.startsWith("http:")) {
      downloadUrl = downloadUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = downloadUrl;
    link.setAttribute("download", fileName || "未命名文件");
    link.setAttribute("target", "_blank");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 初始化数据
onMounted(() => {
  getWorkOrderData();
});
</script>

<style scoped lang="scss">
.taskInfo {
  padding: 20px;

  :deep(.el-descriptions) {
    .label-width {
      width: 130px;
      height: 60px;
    }
    .value-width {
      width: 270px;
      min-height: 60px;
    }
  }
}

.taskInfo-btns {
  padding: 20px;
  text-align: center;
  margin-top: 20px;
}

:deep(.el-descriptions__body) {
  .el-descriptions__table.is-bordered .el-descriptions__cell {
    // padding: 30px 11px;
    /* min-width: 120px;
    max-width: 500px; */
    word-break: break-all; // 让内容超出列宽时自动换行显示
    word-wrap: break-word;
  }
}

.upload-content {
  padding: 20px;

  .el-upload__tip {
    margin-top: 10px;
    color: #909399;
  }
}

.custom-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
}

:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

.report-container {
  display: flex;
  align-items: center;
  gap: 20px;

  .upload-btn-wrapper {
    flex-shrink: 0;
  }

  .report-list {
    flex-grow: 1;
  }

  .uploader-name {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }
}

.report-item {
  margin: 5px 0;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}
</style>