<template>
  <!-- 锁屏规则设置对话框 -->
  <el-dialog
    class="custom-dialog lock-rule-dialog"
    v-model="visible"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
    @close="handleCancelRule"
  >
    <template #header>
      <div
        style="font-size: 18px; display: flex; align-items: center; gap: 0 10px"
      >
        锁屏规则设置
        <div
          style="
            font-size: 14px;
            color: red;
            display: flex;
            align-items: center;
            gap: 0 5px;
          "
        >
          <el-icon><QuestionFilled /></el-icon
          >注意：更新锁屏规则后需设备重启才会应用
        </div>
      </div>
    </template>
    <el-scrollbar :max-height="500" style="padding-right: 20px">
      <el-form :model="lockRuleForm" ref="lockRuleRef" label-position="top">
        <el-form-item
          prop="num"
          :rules="{
            required: lockRuleForm.isAutoLock,
            message: '请输入自动锁定的无操作时间',
            trigger: ['change', 'blur'],
          }"
        >
          <template #label>
            <div style="display: inline-block">
              <div style="display: flex; align-items: center; gap: 0 10px">
                自动锁定规则设置
                <el-switch v-model="lockRuleForm.isAutoLock" />
              </div>
            </div>
          </template>
          <div
            v-show="lockRuleForm.isAutoLock"
            style="display: flex; align-items: center; gap: 0 10px"
          >
            检测到设备无操作
            <el-input-number
              v-model="lockRuleForm.num"
              :max="99999"
              :min="1"
              :precision="0"
              :step="1"
              @keydown="SendEventTwo"
            />
            <el-select v-model="lockRuleForm.unit" style="width: 100px">
              <el-option label="秒" :value="1" />
              <el-option label="分钟" :value="2" />
              <el-option label="小时" :value="3" />
              <el-option label="天" :value="4" />
            </el-select>
            后自动锁定
          </div>
        </el-form-item>
        <el-form-item prop="timeRangeList">
          <template #label>
            <div style="display: inline-block">
              <div style="display: flex; align-items: center; gap: 0 10px">
                自动解锁规则设置
                <el-switch
                  v-model="lockRuleForm.isDisable"
                  @change="changeDisable"
                />
                <div
                  style="
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    gap: 0 5px;
                  "
                >
                  <el-icon size="20"><QuestionFilled /></el-icon
                  >在设置的时间段内设备自动解锁，最多五个时间段
                </div>
              </div>
            </div>
          </template>

          <div v-show="lockRuleForm.isDisable" style="width: 100%">
            <div
              style="
                display: flex;
                align-items: center;
                gap: 0 10px;
                font-size: 13px;
                justify-content: center;
                margin-bottom: 10px;
              "
            >
              是否允许手动锁定？
              <el-switch v-model="lockRuleForm.isLock" />
              <div
                style="
                  font-size: 12px;
                  display: flex;
                  align-items: center;
                  gap: 0 5px;
                "
              >
                <el-icon size="20"><QuestionFilled /></el-icon
                >关闭后，在指定解锁时间段内无法手动锁定
              </div>
            </div>
            <div
              class="timeRangeRow"
              v-for="(item, index) in lockRuleForm.timeRangeList"
              :key="index"
            >
              <div class="timeIndex">{{ index + 1 }}</div>
              <el-time-picker
                v-model="item.time"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="margin-bottom: 5px; width: 425px; flex-grow: inherit"
                @change="changeTimeList"
              />
              <el-button
                v-if="lockRuleForm.timeRangeList.length > 1"
                type="danger"
                icon="Delete"
                circle
                @click="handleDelTime(item, index)"
              />
            </div>
            <div
              class="timeAdd"
              @click="handleAddTime"
              v-if="lockRuleForm.timeRangeList.length < 5"
            >
              <el-button type="primary" icon="Plus" color="#333" circle />
              新增时间段
            </div>
          </div>
        </el-form-item>
        <el-form-item label="锁定屏保图片">
          <div style="width: 100%">
            <el-radio-group
              v-model="lockRuleForm.lockImg"
              @change="changeLockImg"
            >
              <el-radio label="默认" :value="0" />
              <el-radio label="自定义" :value="1" />
            </el-radio-group>
          </div>
          <div
            class="upload-item"
            v-show="lockRuleForm.lockImg == 1"
            style="display: flex; align-items: center; gap: 0 10px"
          >
            <img
              v-if="lockRuleForm.lockUrl && !isImgChange"
              class="el-upload-list__item-thumbnail"
              :src="lockRuleForm.lockUrl"
              alt=""
              style="
                width: 148px;
                height: 148px;
                object-fit: contain;
                border: 1px solid #dcdfe6;
                cursor: pointer;
                border-radius: 6px;
              "
              @click="handlePictureCardPreview()"
            />
            <el-upload
              class="uploadImg"
              ref="uploadImgRef"
              :headers="headers"
              action="#"
              list-type="picture-card"
              :limit="1"
              :auto-upload="false"
              :on-change="handleImgChange"
              :on-exceed="handleImgExceed"
              :http-request="httpRequestImgFn"
            >
              <el-button type="primary"
                >{{ lockRuleForm.lockUrl ? "更换" : "选择" }}图片</el-button
              >
              <template #file="{ file }">
                <div>
                  <img
                    class="el-upload-list__item-thumbnail"
                    :src="file.url || lockRuleForm.lockUrl"
                    alt=""
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <el-icon><zoom-in /></el-icon>
                    </span>
                  </span>
                </div>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  建议更换 3840 * 2160 的图片，大小不超过5MB
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
        <!-- 添加接收设备选择 -->
        <el-form-item label="接收设备" prop="selectedDevices">
          <div class="select-device-container">
            <template v-if="lockRuleForm.selectedDevices">
              <el-tag
                v-for="device in lockRuleForm.selectedDevices.split(',')"
                :key="device"
                closable
                class="device-tag"
                @close="removeLockRuleDevice(device)"
              >
                {{ device }}
              </el-tag>
            </template>
            <div v-else class="placeholder-text">请选择设备</div>
          </div>
        </el-form-item>

        <div class="mt-4">
          <div class="mb-3 label">请选择设备</div>
          <div class="tree-container">
            <el-tree
              ref="lockRuleDeviceTreeRef"
              :data="deviceTreeData"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              default-expand-all
              @check="handleLockRuleDeviceCheck"
            />
          </div>
        </div>
      </el-form>
    </el-scrollbar>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="handleRule" v-throttle
          >保存</el-button
        >
        <el-button @click="handleCancelRule">返回</el-button>
      </div>
    </template>
  </el-dialog>

  <el-dialog v-model="dialogVisibleImg" width="900">
    <img
      :src="dialogImageUrl"
      alt="Preview Image"
      style="
        display: block;
        max-width: 100%;
        margin: 0 auto;
        object-fit: contain;
        height: 600px;
      "
    />
  </el-dialog>
</template>

<script setup>
import { getToken } from "@/utils/auth";
import { emit } from "process";
import { ElMessage, genFileId } from "element-plus";
import { deleteScreenImg } from "@/api/deviceControl";

const { proxy } = getCurrentInstance();
const emits = defineEmits(["submit", "cancel"]);
const props = defineProps({
  open: { type: Boolean, default: false },
  formData: { type: Object, default: () => ({}) },
  curRow: { type: Object, default: () => ({}) },
  deviceTreeData: { type: Array, default: () => [] },
});

const actionUrlImg = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/schoolSoftware/uploadScreenImg"
);
const visible = ref(false);
const dialogImageUrl = ref("");
const dialogVisibleImg = ref(false);
const lockRuleRef = ref(null);
const headers = ref({
  Authorization: "Bearer " + getToken(),
  "Content-Type": "multipart/form-data",
});
const deviceTreeData = ref([]);
const defaultProps = ref({
  children: "children",
  label: "label",
});
// 添加新的响应式引用
const lockRuleDeviceTreeRef = ref(null);
const lockRuleExpandedKeys = ref([1, 2]);
const isImgChange = ref(false);
const hasScreenImg = ref(false);
const lockRuleForm = ref({
  num: 10,
  unit: 1,
  timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
  timeList: ["08:00:00", "18:00:00"],
  lockImg: 0,
  lockUrl: "",
  isAutoLock: false,
  isDisable: false,
  isLock: false,
  selectedDevices: "", // 新增字段
  deviceIds: [], // 新增字段
});

watch(
  () => props.open,
  (newValue) => {
    visible.value = newValue;
    if (newValue) {
      if (lockRuleDeviceTreeRef.value) {
        lockRuleDeviceTreeRef.value.setCheckedKeys([]);
      }
      // 当对话框打开时，将 formData 中的数据赋值给 lockRuleForm
      Object.assign(lockRuleForm.value, props.formData);
      // 确保 deviceTreeData 是响应式的
      deviceTreeData.value = props.deviceTreeData;

      // 设置图片相关状态
      if (props.curRow.imageUrl) {
        lockRuleForm.value.lockImg = 1;
        lockRuleForm.value.lockUrl = props.curRow.imageUrl;
        hasScreenImg.value = true;
      } else {
        lockRuleForm.value.lockImg = 0;
        lockRuleForm.value.lockUrl = "";
        hasScreenImg.value = false;
      }

      isImgChange.value = false;

      // 设置树形选择
      nextTick(() => {
        if (lockRuleDeviceTreeRef.value && props.curRow.rawData?.deviceCodes) {
          const nodeIds = props.curRow.rawData.deviceCodes
            .map((code) => findDeviceNodeId(code))
            .filter(Boolean);

          if (nodeIds.length > 0) {
            lockRuleDeviceTreeRef.value.setCheckedKeys(nodeIds);
          }
        }
      });
    }
  }
);

function findDeviceNodeId(deviceCode) {
  let foundId = null;

  const findId = (nodes) => {
    for (const node of nodes) {
      if (node.children?.length < 1 || !node.children) {
        // 检查节点是否包含分隔符
        const nodeDeviceCode = node.label.includes("-")
          ? node.label.split("-")[1]
          : node.label;

        if (nodeDeviceCode === deviceCode) {
          foundId = node.id;
          break;
        }
      } else if (node.children.length > 0) {
        findId(node.children);
      }
    }
  };

  findId(deviceTreeData.value);
  return foundId;
}

// 修改 handleCancelRule 函数，确保完全清空数据
function handleCancelRule() {
  isImgChange.value = false;
  uploadImgRef.value.clearFiles();

  // 重置表单数据到初始状态
  lockRuleForm.value = {
    num: 10,
    unit: 1,
    timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
    timeList: ["08:00:00", "18:00:00"],
    lockImg: 0,
    lockUrl: "",
    isAutoLock: false,
    isDisable: false,
    isLock: false,
    selectedDevices: "",
    deviceIds: [],
    deviceCodes: [], // 确保清空设备代码
  };

  // 清空树选择
  if (lockRuleDeviceTreeRef.value) {
    lockRuleDeviceTreeRef.value.setCheckedKeys([]);
  }

  lockRuleRef.value.resetFields();

  emits("cancel");
}

function submitRule() {
  // 获取树形组件当前选中的所有节点
  const checkedNodes = lockRuleDeviceTreeRef.value.getCheckedNodes();

  // 从选中的节点中提取设备代码，只保留 ZHDP 开头的设备编号
  const deviceCodes = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children) // 只取叶子节点
    .map((node) => {
      const label = node.label;
      // 如果包含 '-'，取后半部分，否则使用整个 label
      return label.includes("-") ? label.split("-")[1] : label;
    })
    .filter(Boolean); // 过滤掉空值

  console.log("选中的设备代码:", deviceCodes);

  emits("submit", {
    ...lockRuleForm.value,
    deviceCodes, // 将提取的设备代码传递给父组件
  });
}

function handleRule() {
  if (
    lockRuleForm.value.timeRangeList.some(
      (item) => item.time == null || item.length < 1
    ) &&
    lockRuleForm.value.isDisable
  ) {
    proxy.$modal.msgWarning("自动解锁时间范围不能为空");
    return;
  }

  if (lockRuleForm.value.selectedDevices.length < 1) {
    proxy.$modal.msgWarning("请至少选择一台接收设备");
    return;
  }
  lockRuleRef.value.validate((valid) => {
    if (valid) {
      console.log(valid);
      if (lockRuleForm.value.lockImg == 0) {
        if (hasScreenImg.value) {
          deleteScreenImg().then((res) => {
            submitRule();
          });
        } else {
          submitRule();
        }
      } else {
        if (!!lockRuleForm.value.lockUrl) {
          if (isImgChange.value) {
            proxy.$refs.uploadImgRef.submit();
          } else {
            submitRule();
          }
        } else {
          proxy.$modal.msgWarning("请选择锁屏屏保图片");
        }
      }
    }
  });
}

// 添加设备选择处理函数
function handleLockRuleDeviceCheck(data, { checkedNodes, checkedKeys }) {
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children)
    .map((node) => {
      if (node.label.includes("-")) {
        return node.label.split("-")[1];
      }
      return node.label;
    })
    .join(",");

  lockRuleForm.value.selectedDevices = devices;
  lockRuleForm.value.deviceIds = checkedKeys;
}

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file?.url || lockRuleForm.value.lockUrl;
  dialogVisibleImg.value = true;
};

const uploadImgRef = ref(null);
const handleImgChange = (file) => {
  console.log(file);

  let fileName = file.name;
  let index = fileName.lastIndexOf(".");
  let fileType = fileName.substring(index + 1);
  let whiteName = ["jpg", "jpeg", "png"];
  if (file.status == "ready") {
    if (whiteName.indexOf(fileType) == -1) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning(`仅支持${whiteName.join("/")}格式的图片`);
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning("图片大小不能超过5MB");
      return;
    }

    isImgChange.value = true;
    lockRuleForm.value.lockUrl = file.url;
  }
};

const handleImgExceed = (files) => {
  uploadImgRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadImgRef.value.handleStart(file);
  lockRuleForm.value.lockUrl = file.name;
};

// 自定义上传屏保
const httpRequestImgFn = (options) => {
  console.log(options, "自定义上传屏保");

  const formData = new FormData();
  formData.append("file", options.file);

  fetch(actionUrlImg.value, {
    method: "POST",
    body: formData,
    headers: { Authorization: "Bearer " + getToken() },
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      }
    })
    .then((res) => {
      console.log(res);
      if (res && res.data && res.code == 200) {
        submitRule();
      } else {
        proxy.$modal.msgError("操作失败");
        handleCancelRule();
      }
    });
};

// 添加设备移除处理函数
function removeLockRuleDevice(deviceToRemove) {
  const devices = lockRuleForm.value.selectedDevices
    .split(",")
    .filter((d) => d !== deviceToRemove);
  lockRuleForm.value.selectedDevices = devices.join(",");

  const treeNodes = lockRuleDeviceTreeRef.value.getCheckedNodes();
  const nodeToUncheck = treeNodes.find(
    (node) =>
      (node.children?.length < 1 || !node.children) &&
      (node.label.includes("-")
        ? node.label.split("-")[1] === deviceToRemove
        : node.label === deviceToRemove)
  );
  if (nodeToUncheck) {
    lockRuleDeviceTreeRef.value.setChecked(nodeToUncheck, false);
    lockRuleForm.value.deviceIds = lockRuleDeviceTreeRef.value.getCheckedKeys();
  }
}

function SendEventTwo(e) {
  if (e.key === "." || e.key === "," || e.key === "-" || e.key === "+") {
    e.preventDefault();
  }
}

function changeTimeList(val) {
  // console.log(val);
}

const handleAddTime = () => {
  if (lockRuleForm.value.timeRangeList.length < 5) {
    lockRuleForm.value.timeRangeList.push({ time: ["08:00:00", "18:00:00"] });
  } else {
    proxy.$modal.msgWarning("最多只能添加五个时间段");
  }
};

const handleDelTime = (item, index) => {
  lockRuleForm.value.timeRangeList.splice(index, 1);
};

function changeLockImg() {
  console.log(
    hasScreenImg.value,
    "hasScreenImg",
    lockRuleForm.value.lockImg,
    "lockImg",
    lockRuleForm.value.lockUrl,
    "lockUrl",
    isImgChange.value,
    "isImgChange"
  );
}

function changeDisable(val) {
  lockRuleForm.value.isLock = false;
}

defineExpose({
  handleCancelRule,
});
</script>

<style lang="scss" scoped>
.timeRangeRow {
  display: flex;
  gap: 0 10px;
  margin-bottom: 10px;
  .timeIndex {
    background-color: #4095e5;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.timeAdd {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 150px;
  margin: 10px auto 0;
}
.uploadImg {
  :deep(.el-upload__tip) {
    margin-top: 0;
  }
  :deep(.el-upload--picture-card) {
    width: 90px !important;
    height: 35px;
    border: none;
    background: none;
  }
  :deep(.el-upload-list--picture-card) {
    align-items: center;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    margin-bottom: 0;
    justify-content: center;
  }
}
.select-label {
  color: #606266;
  margin: 12px 0 8px;
}

.select-container {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.tree-select-container {
  margin-top: 8px;
}

.tree-select-label {
  margin-bottom: 8px;
  font-weight: 700;
  padding-left: 120px;
}

.tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.mb-3 {
  margin-bottom: 12px;
  font-weight: 700;
}

.mt-3 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 25px;
}
.mt-4 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 10px;
}

.select-device-container {
  min-height: 32px;
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  max-height: 100px;
  overflow-y: auto;

  .device-tag {
    margin: 2px 4px;
  }

  .placeholder-text {
    color: #999;
    line-height: 24px;
  }
}

:deep(.custom-dialog) {
  .el-dialog {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
      overflow-y: auto;
      padding: 20px;

      /* 设置滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }

    .el-dialog__header {
      padding: 20px 20px 10px;
      margin-right: 0;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #dcdfe6;
    }
  }
}

/* 针对不同对话框的特定高度设置 */
:deep(.message-plan-dialog) {
  .el-dialog {
    height: 80vh;
  }
}

:deep(.device-control-dialog) {
  .el-dialog {
    height: 85vh;
  }
}

:deep(.lock-rule-dialog) {
  .el-dialog {
    height: 85vh;
  }
}

/* 调整树形控件容器的高度 */
.tree-container {
  max-height: 250px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;

  /* 设置滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

/* 调整设备标签容器的样式 */
.select-device-container {
  min-height: 32px;
  max-height: 100px;
  overflow-y: auto;

  /* 设置滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
:deep(.el-form-item--default) {
  margin-bottom: 15px;
}
</style>