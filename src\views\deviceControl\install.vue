<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <!-- <el-button @click="handleBack"
            >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
          > -->
        </div>
      </template>
      <div class="view-switch">
        <div>
          <el-radio-group
            v-model="queryParams.recommend"
            @change="
              () => {
                queryParams.pageNum = 1;
                queryParams.fileName = '';
                getList();
              }
            "
          >
            <el-radio-button value="0" label="recommend"
              >推荐软件</el-radio-button
            >
            <el-radio-button value="1" label="my">我的软件</el-radio-button>
          </el-radio-group>
        </div>

        <div class="right-actions">
          <el-button
            type="warning"
            icon="Upload"
            plain
            @click="handleUpload"
            v-if="queryParams.recommend == '1'"
            >上传软件</el-button
          >
          <div class="search">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入软件名称"
              class="search-input"
              clearable
              @keyup.enter="handlerSearch"
            />
            <el-button type="primary" icon="Search" @click="handlerSearch"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery" style="margin-left: 0"
              >重置</el-button
            >
          </div>
        </div>
        <!-- 只在"我的软件"视图显示 -->
        <!-- <div class="right-actions" v-if="queryParams.recommend == '1'">
          <div class="search">
            <el-input
              v-model="queryParams.fileName"
              placeholder="请输入软件名称"
              class="search-input"
              @keyup.enter="handlerSearch"
            />
            <el-button type="primary" @click="handlerSearch">搜索</el-button>
          </div>
        </div> -->
      </div>
      <div class="software-main main">
        <p v-if="total == 0">暂无数据</p>
        <div class="software-list" v-else>
          <div class="software-row">
            <div
              class="software-item"
              v-for="(item, index) in softwareList"
              :key="index"
            >
              <div class="software-info" :class="item.first ? 'red' : ''">
                <div class="software-name">{{ item.fileName }}</div>
                <div v-if="item.audit == 2" style="font-size: 12px">
                  审核拒绝
                </div>
              </div>

              <div
                class="software-action"
                style="font-size: 14px; color: gray"
                v-if="
                  curRecover.length > 0 && curRecover.indexOf(item.id) != -1
                "
              >
                静默安装/手动下发中
              </div>
              <div
                class="software-action"
                style="font-size: 14px; color: gray"
                v-else-if="item.audit == 0 && item.status == 1"
              >
                <el-button type="primary" size="small" disabled
                  >审核中</el-button
                >
              </div>
              <div class="software-action" v-else>
                <el-button
                  v-if="item.status == 1 && item.audit != 2"
                  type="primary"
                  icon="Download"
                  size="small"
                  :disabled="curRecover.length > 0"
                  @click="handleInstall(item)"
                  >静默安装</el-button
                >
                <el-button
                  v-if="item.status == 0 || item.audit == 2"
                  type="primary"
                  icon="Download"
                  size="small"
                  :disabled="curRecover.length > 0"
                  @click="handleInstallByUser(item)"
                  >手动下发</el-button
                >
              </div>
            </div>
          </div>
        </div>
        <el-pagination
          v-if="total > 0"
          :total="total"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          @current-change="getList"
          layout="total, prev, pager, next"
          background
          class="pagination"
        />
      </div>

      <!-- 添加安装对话框 -->
      <el-dialog
        v-model="dialogVisible"
        width="550px"
        @close="dialogClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <template #header>
          <div
            style="
              font-size: 18px;
              display: flex;
              align-items: center;
              gap: 0 10px;
            "
          >
            请选择{{ title }}时间
            <div
              style="
                font-size: 13px;
                display: flex;
                align-items: center;
                gap: 0 5px;
              "
            >
              <el-icon><QuestionFilled /></el-icon>若{{
                title == "下发" ? "手动下发" : title
              }}，冰冻中设备将自动重启，请谨慎操作
            </div>
          </div>
        </template>
        <div class="install-form">
          <el-form
            ref="installRef"
            :model="installForm"
            :rules="installRules"
            style="margin-bottom: 25px"
          >
            <el-form-item label="" prop="installTime">
              <div class="time-select">
                <el-date-picker
                  v-model="installForm.installTime"
                  type="datetime"
                  :placeholder="`选择${title == '下发' ? title : '安装'}时间`"
                  :disabled="isNow"
                  format="YYYY-MM-DD HH:mm"
                  :disabled-date="disabledDate"
                  value-format="YYYY-MM-DD HH:mm"
                />
                <el-checkbox
                  v-model="isNow"
                  @change="installRef.validateField('installTime')"
                  >现在立刻</el-checkbox
                >
              </div>
            </el-form-item>
          </el-form>

          <div class="location-select">
            <div style="display: flex; justify-content: space-between">
              <div class="section-title">请选择{{ title }}位置</div>
              <span>共{{ count }}台设备</span>
            </div>
            <div class="location-tree">
              <el-tree
                style="width: 100%"
                :data="positionTreeList"
                show-checkbox
                node-key="id"
                expand-on-click-node
                default-expand-all
                :props="positionProps"
                :default-expanded-keys="curExpandedKeys"
                ref="treeRef"
                @check="treeChange"
              />
            </div>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button type="primary" @click="confirmInstall" v-throttle>{{
              title
            }}</el-button>
            <el-button @click="dialogClose">返回</el-button>
          </span>
        </template>
      </el-dialog>

      <!-- 添加上传软件对话框 -->
      <el-dialog
        class="custom-dialog"
        v-model="uploadDialogVisible"
        title="上传软件"
        style="overflow: hidden"
        width="500px"
        @close="uploadDialogClose"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <div v-loading="uploading" class="upload-form" style="overflow: hidden">
          <el-form
            :model="uploadForm"
            :rules="rules"
            ref="formRef"
            label-width="100px"
          >
            <el-form-item label="上传软件" prop="name" required>
              <div class="upload-item">
                <el-upload
                  class="software-upload"
                  action="#"
                  :auto-upload="false"
                  :on-change="handleFileChange"
                  :on-success="successFile"
                  :on-exceed="handleExceed"
                  :http-request="httpRequestFn"
                  :limit="1"
                  :show-file-list="false"
                  ref="uploadWareRef"
                >
                  <el-button type="primary">选择文件</el-button>
                </el-upload>
                <div class="file-name">{{ uploadForm.name }}</div>
              </div>
            </el-form-item>
            <el-form-item label="软件图标">
              <div class="upload-item">
                <el-upload
                  ref="uploadRef"
                  class="icon-upload"
                  :action="actionUrlImg"
                  :headers="headers"
                  :before-upload="beforeIconFile"
                  :on-success="successIconFile"
                  :on-change="handleIconChange"
                  :limit="1"
                  :data="{
                    type: 8,
                  }"
                  accept=".jpg,.jpeg,.png"
                  :show-file-list="false"
                >
                  <div class="upload-placeholder" v-if="!uploadForm.iconUrl">
                    <span>上传</span>
                  </div>
                  <div v-else class="preview-container">
                    <img :src="uploadForm.iconUrl" class="preview-image" />
                    <div class="preview-actions">
                      <!-- <el-icon @click.stop="previewIcon"><ZoomIn /></el-icon> -->
                      <el-icon @click.stop="removeIcon($event)"
                        ><Delete
                      /></el-icon>
                    </div>
                  </div>
                </el-upload>
                <div class="file-format">jpg/jpeg/png 5M以内</div>
              </div>
            </el-form-item>
            <el-form-item label="软件名称" prop="fileName">
              <el-input
                v-model="uploadForm.fileName"
                maxlength="16"
                show-word-limit
              />
            </el-form-item>
            <el-form-item
              label="静默参数"
              prop="cmd"
              v-if="uploadForm.status == '1'"
            >
              <el-input
                v-model="uploadForm.cmd"
                maxlength="100"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="备注">
              <el-input
                v-model="uploadForm.remark"
                maxlength="300"
                type="textarea"
                show-word-limit
              />
            </el-form-item>
            <el-form-item label="是否审核">
              <el-radio-group v-model="uploadForm.status">
                <el-radio value="1" :label="true"
                  >是，提交审核，升级为静默安装包（审核需1-2日）</el-radio
                >
                <el-radio value="0" :label="false"
                  >否，不提交审核，手动下发</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-form>
        </div>
        <template #footer>
          <div
            style="
              display: flex;
              justify-content: space-between;
              align-items: center;
            "
          >
            <div>
              <div v-if="uploading">
                <el-progress
                  :percentage="progress"
                  :status="progress == 100 ? 'success' : ''"
                  :format="format"
                />
              </div>
            </div>
            <div class="dialog-footer">
              <el-button
                type="primary"
                @click="confirmUpload"
                v-throttle
                :disabled="uploading"
              >
                {{
                  uploading
                    ? progress < 100
                      ? "上传中..."
                      : "发送中..."
                    : "发送"
                }}</el-button
              >

              <el-button v-if="uploading" @click="handleFileSendCancel"
                >终止</el-button
              >
              <el-button @click="confirmCancel" v-if="!uploading"
                >返回</el-button
              >
            </div>
          </div>
        </template>
      </el-dialog>

      <!-- 添加图片预览对话框 -->
      <el-dialog
        class="custom-dialog"
        v-model="previewVisible"
        width="500px"
        align-center
      >
        <img
          :src="uploadForm.iconUrl"
          style="width: 100%"
          v-if="uploadForm.iconUrl"
        />
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="install">
import { useRoute } from "vue-router";
import axios from "axios";
import OSS from "ali-oss";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
} from "vue";
import { ElMessage, genFileId } from "element-plus";
import {
  listSchoolSoftware,
  installationSoftwareMqtt,
  getStsToken,
  uploadSoftware,
  penetrateDevice,
} from "@/api/deviceControl";
import { debounce } from "@/utils/debounce";
import { getToken } from "@/utils/auth";
import { getPositionTree } from "@/api/mediaTeach/position";
import {
  treeToArray,
  treeFindPath,
  timeFormat,
  sendPointRequest,
  sendPointRequestBatch,
} from "@/utils";
import { sm2Decrypt } from "@/utils/sm2encrypt.js";
import {
  checkSupportsCrypto,
  generateRequestId,
  getCurrentTime,
} from "@/utils/sliceUpload";
import { devicePage } from "@/api/mediaTeach/ledger";

const format = (percentage) => (percentage === 100 ? "" : `${percentage}%`);
const actionUrlImg = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/schoolSoftware/uploadSoftware"
);
const actionUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/upload/releaseBigFileSlice"
);
const headers = ref({ Authorization: "Bearer " + getToken() });
const route = useRoute();
const { proxy } = getCurrentInstance();
const positionTreeList = ref([]);
const state = reactive({
  curExpandedKeys: [],
  expandedKeys: [],
  recoverTimer: null,
  addTimer: null,
  addSecond: 0,
  curFileName: "",
  installRef: null,
  installForm: {
    installTime: "",
  },
  curRecover: {
    id: "",
  },
  installRules: {
    installTime: [
      {
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
  },
  title: "静默安装",
  treeRef: null,
  cancelToken: null,
  client: null,
  uploadWareRef: null,
  abortController: null,
  uploading: false,
  progress: 0,
  currentView: "recommend",
  softwareList: [],
  fileList: [],
  dialogVisible: false,
  installTime: "",
  isNow: false,
  count: 0,
  fileData: {
    type: 7,
  },
  locationTree: [
    {
      id: 1,
      label: "教学楼A",
      children: [
        {
          id: 2,
          label: "三楼",
          children: [
            {
              id: 3,
              label: "三年二班",
            },
          ],
        },
      ],
    },
    {
      id: 4,
      label: "教学楼B",
      children: [
        {
          id: 5,
          label: "一楼",
          children: [
            {
              id: 6,
              label: "多媒体",
            },
          ],
        },
      ],
    },
  ],
  searchKeyword: "", // 搜索关键词
  total: 0,
  uploadDialogVisible: false,
  wareType: 7,
  uploadForm: {
    fileName: "",
    iconUrl: "",
    fileUrl: "",
    status: "1",
    name: "",
    cmd: "",
    params: "",
    remark: "",
    needReview: true,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 8,
    fileName: "",
    recommend: "0",
  },
  positionNodeResultList: [],
  positionList: [],
  previewVisible: false,
  deviceId: "",
});

const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});
const locationTreeRef = ref(null);
const uploadRef = ref(null);

const rules = {
  name: [{ required: true, message: "请选择软件", trigger: "change" }],
  fileName: [{ required: true, message: "请输入软件名称", trigger: "blur" }],
  // cmd: [{ required: true, message: "请输入静默参数", trigger: "blur" }],
};

const {
  recoverTimer,
  curRecover,
  curExpandedKeys,
  expandedKeys,
  addTimer,
  addSecond,
  curFileName,
  installRef,
  installForm,
  installRules,
  title,
  treeRef,
  cancelToken,
  client,
  uploadWareRef,
  abortController,
  uploading,
  progress,
  currentView,
  deviceId,
  count,
  fileList,
  softwareList,
  positionNodeResultList,
  dialogVisible,
  queryParams,
  installTime,
  fileData,
  isNow,
  locationTree,
  searchKeyword,
  uploadDialogVisible,
  uploadForm,
  positionList,
  total,
  previewVisible,
  wareType,
} = toRefs(state);

// 检测浏览器是否支持 crypto.subtle.digest
checkSupportsCrypto();

const treeChange = (val) => {
  let checkList = state.treeRef.getCheckedNodes();
  console.log(checkList, "checkList");
  count.value = checkList.filter(
    (item) => item.id.toString().indexOf("ZHDP") != -1
  ).length;
};

function validateTime(rule, value, callback) {
  if (value == "" && !isNow.value) {
    callback(new Error("安装时间不能为空"));
  } else if (new Date(value).getTime() < new Date().getTime() && !isNow.value) {
    callback(new Error("安装时间不能小于当前时间"));
  } else {
    callback();
  }
}

const handleInstallByUser = async (item) => {
  let res = await penetrateDevice();
  if (JSON.stringify(res.data) === "{}" || !res.data) {
    curFileName.value = item.fileName;
    title.value = "下发";
    deviceId.value = item.id;
    state.count = 0;
    curExpandedKeys.value = [...expandedKeys.value];
    dialogVisible.value = true;
  }
};

const disabledDate = (time) => {
  // 获取当前时间
  const now = new Date();
  return time.getTime() + 86400000 < now.getTime(); // 禁用小于当前时间的日期
};

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

// 覆盖文件
const handleExceed = (files) => {
  uploadWareRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadWareRef.value.handleStart(file);
  uploadForm.value.name = file.name;
};

const httpRequestFn = (options) => {
  console.log(options, "自定义上传");

  uploading.value = true;
  progress.value = 0;
  abortController.value = new AbortController();

  // 生成 requestId
  generateRequestId(options.file, 1024 * 1024, function (requestId) {
    try {
      console.log(`生成的 requestId 是: ${requestId}`);

      const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
      let start = 0;
      console.log("文件大小：" + options.file.size);
      let end = Math.min(chunkSize, options.file.size);

      // 上传空文件的函数
      const uploadEmptyFile = (requestId) => {
        const formData = new FormData();
        formData.append("file", new Blob([])); // 空文件
        formData.append("chunkNumber", 1);
        formData.append("totalChunks", 1);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        );
        formData.append("requestId", requestId);
        formData.append("originalFileName", options.file.name);
        formData.append("isLastChunk", "true"); // 空文件只有一个分片

        fetch(actionUrl.value, {
          method: "POST",
          body: formData,
          headers: headers.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              progress.value = 100;
              console.log(`[${getCurrentTime()}] 空文件上传成功`);
              return response.json();
            } else {
              console.error("空文件上传失败:", response.statusText);
            }
          })
          .then((res) => {
            console.log(res);
            if (res && res.data) {
              if (res.code == 200) {
                let data = sm2Decrypt(res.data);
                uploadForm.value.fileUrl = data;
                console.log("安装上传参数", uploadForm.value);
                if (uploadForm.value.status == "0") delete uploadForm.value.cmd;
                uploadSoftware(uploadForm.value)
                  .then((res) => {
                    console.log("接口上传结果", res);
                    proxy.$modal.msgSuccess("上传成功");
                    getList();
                  })
                  .catch(() => {
                    proxy.$modal.msgError("上传失败");
                  })
                  .finally(() => confirmCancel());
              } else {
                proxy.$modal.msgError("上传失败");
                confirmCancel();
              }
            }
          });
      };

      // 对空文件的特殊处理
      if (options.file.size === 0) {
        console.log("检测到空文件，直接上传...");
        uploadEmptyFile(requestId);
        return;
      }

      const uploadChunk = (chunk, chunkNumber, totalChunks) => {
        const formData = new FormData();
        formData.append("file", new Blob([chunk]));
        formData.append("chunkNumber", chunkNumber);
        formData.append("totalChunks", totalChunks);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        ); // 使用文件名和大小作为标识符
        formData.append("requestId", requestId); // 添加生成的 requestId
        formData.append("originalFileName", options.file.name); // 添加原始文件名

        // 如果是最后一个分片，则添加一个标志
        if (chunkNumber === totalChunks) {
          formData.append("isLastChunk", "true");
        }

        fetch(actionUrl.value, {
          // 更新为你提供的URL
          method: "POST",
          body: formData,
          headers: headers.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              // 打印当前时间和分片上传成功信息
              if (chunkNumber === totalChunks) {
                console.log("最后一个分片", response);
                progress.value = 100;
                return response.json();
              } else {
                progress.value = Math.round((chunkNumber / totalChunks) * 100);
              }
              console.log(
                `[${getCurrentTime()}] 分片 ${chunkNumber} 已成功上传`
              );
              processNextChunk(chunkNumber + 1);
            } else {
              console.error("分片上传失败:", response.statusText);
              proxy.$modal.alert(`上传失败`, "提示");
            }
          })
          .then((res) => {
            console.log(res);
            if (res && res.data) {
              if (res.code == 200) {
                let data = sm2Decrypt(res.data);
                uploadForm.value.fileUrl = data;
                console.log("安装上传参数", uploadForm.value);
                if (uploadForm.value.status == "0") delete uploadForm.value.cmd;
                uploadSoftware(uploadForm.value)
                  .then((res) => {
                    console.log("接口上传结果", res);
                    proxy.$modal.msgSuccess("上传成功");
                    getList();
                  })
                  .catch(() => {
                    proxy.$modal.msgError("上传失败");
                  })
                  .finally(() => confirmCancel());
              } else {
                proxy.$modal.msgError("上传失败");
                confirmCancel();
              }
            }
          });
      };

      const processNextChunk = (chunkNumber) => {
        if (start < options.file.size) {
          const nextChunk = options.file.slice(start, end);
          uploadChunk(
            nextChunk,
            chunkNumber,
            Math.ceil(options.file.size / chunkSize)
          );
          start = end;
          end = Math.min(options.file.size, start + chunkSize);
        } else {
          console.log("所有分片已上传完毕");
        }
      };

      // 开始处理第一个分片
      processNextChunk(1);
    } catch {
      proxy.$modal.alert(
        `${progress.value < 100 ? "上传" : "发送"}失败`,
        "提示"
      );
      handleFileSendCancel();
    }
  });
};

const httpRequestFn3 = (options) => {
  proxy.$modal.loading();
  getStsToken()
    .then((resp) => {
      console.log(resp.data);
      if (resp.data) {
        const { ossAccessKeyId, signature, policy, dir, host } = resp.data;

        const formData = new FormData();
        formData.append("name", options.file.name);
        formData.append("policy", policy);
        formData.append("OSSAccessKeyId", ossAccessKeyId);
        formData.append("success_action_status", "200");
        formData.append("signature", signature);
        formData.append("key", dir + options.file.name);
        formData.append("file", options.file);

        fetch(host, {
          method: "POST",
          body: formData,
          headers: {
            "Content-Disposition": `attachment; filename="${options.file.name}"`,
          },
        })
          .then((response) => {
            if (response.ok) {
              console.log("阿里云上传成功", response);
              console.log("上传接口参数", uploadForm.value);
              uploadForm.value.fileUrl = response.url + dir + options.file.name;
              uploadSoftware(uploadForm.value)
                .then((res) => {
                  console.log("接口上传结果", res);
                  proxy.$modal.msgSuccess("上传成功");
                  // if (uploadForm.value.status == "0") {
                  //   deviceId.value = res.data?.id || "";
                  //   dialogVisible.value = true;
                  // }
                  getList();
                  confirmCancel();
                })
                .catch(() => {
                  proxy.$modal.msgError("上传失败");
                  confirmCancel();
                });
            } else {
              console.log("上传失败", response);
              proxy.$modal.msgError("上传失败");
              proxy.$modal.closeLoading();
            }
          })
          .catch((error) => {
            console.log("上传失败", error);
            proxy.$modal.msgError("上传失败");
            proxy.$modal.closeLoading();
          });
      } else {
        proxy.$modal.msgError("上传失败");
        proxy.$modal.closeLoading();
      }
    })
    .catch(() => {
      proxy.$modal.msgError("上传失败");
      proxy.$modal.closeLoading();
    });
};

// 自定义上传
const httpRequestFn2 = (options) => {
  console.log(options, "自定义上传");
  proxy.$modal.loading();
  getStsToken().then(async (resp) => {
    // console.log("获取参数", resp);
    if (resp.data) {
      const { credentials = {} } = resp.data;

      if (credentials.accessKeyId) {
        client.value = new OSS({
          region: "oss-cn-guangzhou",
          accessKeyId: credentials.accessKeyId,
          accessKeySecret: credentials.accessKeySecret,
          stsToken: credentials.securityToken,
          bucket: "xyywpt-oss",
          secure: true,
        });

        try {
          progress.value = 0;
          // uploading.value = true;
          const result = await client.value.put(
            `Software/${timeFormat(new Date().getTime(), "yyyymmddhhMMss")}/${
              options.file.name
            }`,
            options.file,
            {
              headers: {
                "Content-Disposition": `attachment; filename="${uploadForm.value.name}"`,
              },
            }
          );

          uploading.value = false;
          uploadForm.value.fileUrl = result.url;
          console.log("阿里云上传结果", result);

          console.log("上传接口参数", uploadForm.value);
          uploadSoftware(uploadForm.value)
            .then((res) => {
              console.log("接口上传结果", res);
              proxy.$modal.msgSuccess("上传成功");
              deviceId.value = res.data?.id || "";
              if (uploadForm.value.status == 0) {
                curExpandedKeys.value = [...expandedKeys.value];
                dialogVisible.value = true;
              }

              confirmCancel();
            })
            .catch(() => {
              proxy.$modal.msgError("上传失败");
              confirmCancel();
            });
        } catch {
          proxy.$modal.msgError("上传失败");
          uploading.value = false;
        }
      }
    }
  });
};

// 获取列表
function getList() {
  proxy.$modal.loading();
  console.log(queryParams.value, "queryParams.value");
  listSchoolSoftware(queryParams.value)
    .then((res) => {
      console.log(res, "软件安装");
      softwareList.value = res.data.records;
      total.value = res.data.total;
      proxy.$modal.closeLoading();
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
  getRecovering();
}

function getRecovering() {
  penetrateDevice().then((res) => {
    console.log("正在穿透的设备", res);
    if (res.code == 200) {
      let arr = [];
      for (let key in res.data) {
        if (!!key) {
          arr.push(res.data[key].id);
        }
      }
      state.curRecover = [...new Set(arr)];
      console.log("穿透列表", state.curRecover);
    }
  });
}

// 取消上传
const handleFileSendCancel = () => {
  if (abortController.value) abortController.value.abort();
  uploading.value = false;
  progress.value = 0;
};

const confirmCancel = () => {
  console.log("confirmCancel");
  proxy.resetForm("fileRef");
  removeIcon();
  uploadDialogVisible.value = false;
  handleFileSendCancel();
  proxy.$modal.closeLoading();
};

// 查询
const handlerSearch = debounce(() => {
  queryParams.value.pageNum = 1;
  getList();
}, 200);
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.fileName = "";
  getList();
}
// 打开安装对话框
async function handleInstall(item) {
  let res = await penetrateDevice();
  if (JSON.stringify(res.data) === "{}" || !res.data) {
    curFileName.value = item.fileName;
    title.value = "静默安装";
    curExpandedKeys.value = [...expandedKeys.value];
    state.dialogVisible = true;
    deviceId.value = item.id;
    state.count = 0;
    console.log(item);
  }
}

function uploadDialogClose() {
  proxy.$refs.formRef.resetFields();
  uploadDialogVisible.value = false;
  uploadForm.value = {
    fileName: "",
    iconUrl: "",
    status: "1",
    name: "",
    params: "",
    remark: "",
    needReview: true,
  };
  fileList.value = [];
  // abortController.value.abort();
  uploading.value = false;
  progress.value = 0;
}

// 确认安装
function confirmInstall() {
  //   const selectedNodes = locationTreeRef.value.getCheckedNodes();
  console.log("安装时间:", state.installTime);
  console.log("是否立即安装:", state.isNow);
  //   console.log("选中的位置:", selectedNodes);
  //   state.dialogVisible = false;

  let checkList = state.treeRef.getCheckedNodes();
  console.log(checkList, "checkList");

  // handlePositionNode(checkList);
  // const positionList = positionNodeResultList.value.map((item) => {
  //   return {
  //     position: item.names.join("-"),
  //     positionId: item.ids.join(","),
  //   };
  // });
  // console.log("positionList", { positionList });

  if (!state.isNow) {
    // 不是立即执行
    installRef.value.validate((valid) => {
      if (valid && checkList.length < 1) {
        proxy.$modal.msgWarning("请选择安装位置");
      } else if (valid) {
        installTip(
          checkList.filter((item) => item.id.toString().indexOf("ZHDP") != -1)
        );
      }
    });
  } else if (checkList.length < 1) {
    proxy.$modal.msgWarning("请选择安装位置");
  } else {
    installTip(
      checkList.filter((item) => item.id.toString().indexOf("ZHDP") != -1)
    );
  }
}

function installTip(checkList) {
  if (title.value == "下发") {
    proxy.$modal
      .confirm(
        "若设备在冰冻状态下手动下发，则系统会自动重启，且只会下发安装包到相应设备中"
      )
      .then(() => {
        installFn(checkList);
      })
      .catch();
  } else {
    installFn(checkList);
  }
}

function installFn(positionList) {
  let obj = {
    id: deviceId.value,
    installationTime: `${state.installForm.installTime}:00`,
    deviceCodes: positionList.map((item) => item.id),
  };

  if (state.isNow) delete obj.installationTime;
  console.log(obj, "传参");

  proxy.$modal.loading();
  installationSoftwareMqtt(obj)
    .then((res) => {
      proxy.$modal.closeLoading();
      console.log(res);
      proxy.$modal.msgSuccess(
        `${state.title == "静默安装" ? "安装" : "下发"}成功`
      );
      let sendData = {
        userEvents: positionList.map((item) => {
          return {
            event: "Click",
            eventDescribe: "点击软件安装",
            content: `${queryParams.value.recommend},${curFileName.value}`,
            num: 1,
            deviceCode: item.id,
          };
        }),
      };
      console.log(sendData, "批量/单个埋点");
      sendPointRequestBatch(sendData);
      dialogClose();
    })
    .catch((err) => {
      dialogClose();
      proxy.$modal.closeLoading();
    });
}

function dialogClose() {
  proxy.$refs.installRef.resetFields();
  state.treeRef.setCheckedNodes([]);
  state.dialogVisible = false;
  state.installTime = "";
  state.isNow = false;
}

function getPositionTreeList() {
  const params = {
    smartScreen: 1,
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 9999999,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: 1,
    deviceStatus: "",
    positionIds: [],
    putTime: "",
    status: [],
  };
  try {
    devicePage(params).then((res) => {
      console.log(res, "设备列表");
      if (res.code == 200) {
        let noAddrList = res.data.page.records.filter(
          (item) => !item.installAddress
        );
        getPositionTree({ isSoftware: 1, isDevice: 1 })
          .then((response) => {
            console.log(response, "安装位置树");
            let tree = JSON.parse(JSON.stringify(response.data));
            positionTreeList.value = response.data;
            positionList.value = treeToArray(tree);
            expandedKeys.value = positionList.value.map((item) => item.id);
            // console.log("expandedKeys", expandedKeys.value);
            let tree2 = JSON.parse(JSON.stringify(response.data));
            positionTreeList.value = addAttr2(tree2);
            // console.log("positionTreeList", positionTreeList.value);
            // console.log("positionList", positionList.value);
            noAddrList.map((item) => {
              positionTreeList.value.push({
                id: item.deviceCode,
                name: item.deviceCode,
                parentId: "0",
              });
            });
          })
          .catch((err) => {});
      }
    });
  } catch (e) {
    console.log(e);
  }
}

function handlePositionNode(checkList) {
  positionNodeResultList.value = [{ ids: [], names: [] }];
  for (let i = 0; i < checkList.length; i++) {
    positionNodeResultList.value[i] = {
      ids: [],
      names: [],
    };

    positionNodeResultList.value[i].ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == checkList[i].id
    );
    const arr = positionNodeResultList.value[i].ids;
    for (let j = 0; j < arr.length; j++) {
      positionNodeResultList.value[i].names[j] = positionList.value.find(
        (node) => node.id == arr[j]
      ).name;
    }
  }
  // console.log("positionResultNodeList ==> ", positionNodeResultList.value);
}

function addAttr2(data, num = 0) {
  num++;
  for (let j = 0; j < data.length; j++) {
    data[j].disabled = true;
    // console.log(data[j], "data1");
    if (data[j].deviceCodes && data[j].deviceCodes.length > 0) {
      // console.log(data[j], "data2");
      data[j].disabled = false;
      data[j].deviceCodes.forEach((item) => {
        data[j].children.unshift({
          parentId: data[j].id,
          id: item,
          name: item,
          disabled: false,
        });
      });
      addAttr2(data[j].children, num);
    } else if (data[j].children && data[j].children.length > 0) {
      // console.log(data[j], "data3");
      addAttr2(data[j].children, num);
    } else {
      if (data[j].id == data[j].name) data[j].disabled = false; // 若 id = name 则为设备节点（设备编码名称）
      // console.log(data[j], "data4");
      continue;
    }
  }
  return data;
}

// 处理上传图标
function beforeIconFile(file) {
  // 图标上传前
  console.log(file);
  let whiteName = ["jpg", "jpeg", "png"];
  //   let whiteName = white;
  //   let msg = white.join('/')
  let filename = file.name;
  let index = filename.lastIndexOf(".");
  let hzName = filename.substring(index + 1);
  if (whiteName.indexOf(hzName) != -1 && file.size < 5 * 1024 * 1024) {
    return true;
  } else {
    ElMessage({
      message: `仅支持${msg}格式并且小于5mb`,
      type: "warning",
    });
    proxy.$refs.uploadRef.abort();
    proxy.$refs.uploadRef.clearFiles();
    // uploadRef.value.abort();
    // uploadRef.value.clearFiles();
  }
}
function successIconFile(res) {
  // 图标上传成功
  console.log(res, "成功");
  let data = JSON.parse(sm2Decrypt(res.data));
  uploadForm.value.iconUrl = data.url;
  console.log(data, "上传成功");
}

// 处理上传软件
function handleUpload() {
  state.uploadDialogVisible = true;
}

function successFile(res) {
  console.log("结果", res);
  if (res?.data?.url) {
    const data = JSON.parse(sm2Decrypt(res.data));
    console.log("上传成功", data);
    // proxy.$modal.closeLoading();
    // proxy.$modal.msgSuccess("上传成功");
    uploadDialogVisible.value = false;
    deviceId.value = data.id;
    state.count = 0;
    curExpandedKeys.value = [...expandedKeys.value];
    dialogVisible.value = true;
    // getList();
  }
}

function handleFileChange(file) {
  console.log(file, "change");
  let fileName = file.name;
  let index = fileName.lastIndexOf(".");
  let fileType = fileName.substring(index + 1);
  console.log(file, "change");
  console.log(fileType, "fileType");

  if (file.status == "ready") {
    if (fileType != "msi" && fileType != "exe") {
      if (file.size > 500 * 1024 * 1024) {
        fileList.value = [];
        proxy.$modal.msgWarning("文件大小不超过500mb");
        return;
      }
      fileList.value = [];
      proxy.$modal.msgWarning("仅支持msi/exe格式");
      return;
    }

    fileList.value = [file];
    uploadForm.value.name = file.name;
  }

  //   state.uploadForm.file = file.raw;
  // 阻止默认的上传行为
  return false;
}

function handleIconChange(file) {
  //   console.log("handleIconChange called", file);
  //   // 检查文件类型
  //   const isImage = file.raw.type.startsWith("image/");
  //   if (!isImage) {
  //     ElMessage.error("请上传图片文件！");
  //     return false;
  //   }
  //   // 检查文件大小（5MB = 5 * 1024 * 1024 bytes）
  //   const isLt5M = file.raw.size / 1024 / 1024 < 5;
  //   if (!isLt5M) {
  //     ElMessage.error("图片大小不能超过 5MB！");
  //     return false;
  //   }
  //   // 创建预览URL
  //   if (state.uploadForm.iconUrl) {
  //     URL.revokeObjectURL(state.uploadForm.iconUrl);
  //   }
  //   state.uploadForm.icon = file.raw;
  //   state.uploadForm.iconUrl = URL.createObjectURL(file.raw);
  //   console.log("Updated state:", {
  //     icon: state.uploadForm.icon,
  //     iconUrl: state.uploadForm.iconUrl,
  //   });
  //   return false;
}

function confirmUpload() {
  console.log("上传表单数据:", state.uploadForm);
  if (!uploading.value) {
    proxy.$refs.formRef.validate((valid) => {
      console.log(valid);
      if (valid) {
        fileData.value = {
          type: 7,
          ...state.uploadForm,
        };
        console.log(fileData.value, "fileData.value");
        // proxy.$modal.loading();
        proxy.$refs.uploadWareRef.submit();
      }
    });
  }
}

function previewIcon() {
  state.previewVisible = true;
}

function removeIcon(event) {
  console.log(event);
  uploadForm.value.iconUrl = "";
  proxy.$refs.uploadRef.clearFiles();
}

// // 组件卸载时清理 URL
// onBeforeUnmount(() => {
//   if (state.uploadForm.iconUrl) {
//     URL.revokeObjectURL(state.uploadForm.iconUrl);
//   }
// });

onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  recoverTimer.value = setInterval(() => {
    getRecovering();
  }, 30000);
  getPositionTreeList();
  getList();
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览软件安装页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  clearInterval(recoverTimer.value);
});
</script>

<style lang="scss" scoped>
.view-switch {
  margin-bottom: 20px;
  // display: flex;
  justify-content: space-between;
  align-items: center;

  .right-actions {
    display: flex;
    gap: 12px;
    align-items: center;
    justify-content: flex-end;
    .search-input {
      width: 200px;
    }
    .search {
      display: flex;
      gap: 10px;
    }
  }

  :deep(.el-radio-group) {
    .el-radio-button__inner {
      // border-radius: 4px;
      // margin-right: 10px;
      // border: none;
    }

    .el-radio-button:first-child .el-radio-button__inner {
      // border-radius: 4px;
    }

    .el-radio-button:last-child .el-radio-button__inner {
      // border-radius: 4px;
    }

    .el-radio-button:not(:last-child) {
      // margin-right: 10px;
    }

    .el-radio-button:not(:first-child) .el-radio-button__inner {
      // border-left: none;
    }
  }
}
.el-progress {
  width: 200px;
  :deep(.el-progress__text) {
    min-width: 35px;
  }
}
.software-main {
  background-color: #fff;

  p {
    min-height: 400px;
    font-size: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
  }
  .software-list {
    min-height: 360px;
    margin-top: 40px;
    .software-row {
      display: flex;
      flex-wrap: wrap;
      gap: 20px;
    }

    .software-info.red div {
      color: red !important;
    }

    .software-item {
      width: calc(50% - 10px);
      background-color: #fff;
      border: 1px solid #e4e7ed;
      border-radius: 4px;
      padding: 30px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .software-name {
        font-size: 16px;
        color: #303133;
        margin-right: 20px;
      }

      .software-action {
        :deep(.el-button) {
          border-radius: 4px;
          color: #fff;
          font-weight: bold;
        }
      }
    }
  }
}

.install-form {
  .time-select {
    //margin-bottom: 20px;
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .section-title {
    margin-bottom: 10px;
    font-weight: bold;
  }

  .location-tree {
    border: 1px solid #e4e7ed;
    border-radius: 4px;
    padding: 10px;
    max-height: 300px;
    overflow-y: auto;
  }
}

.upload-form {
  .upload-item {
    display: flex;
    align-items: center;
    gap: 10px;

    .file-name {
      flex: 1;
      border-bottom: 1px solid #dcdfe6;
      padding: 5px 0;
      color: #606266;
    }

    .file-format {
      color: #909399;
      font-size: 14px;
    }
  }

  .icon-upload {
    :deep(.el-upload) {
      width: 100px;
      height: 100px;
      border: 1px dashed #d9d9d9;
      border-radius: 6px;
      cursor: pointer;
      position: relative;
      overflow: hidden;

      &:hover {
        border-color: #409eff;

        .preview-actions {
          opacity: 1;
        }
      }
    }

    .upload-placeholder {
      width: 100%;
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 14px;
      color: #8c939d;
      background: #f5f7fa;
    }

    .preview-container {
      width: 100%;
      height: 100%;
      position: relative;

      .preview-image {
        width: 100%;
        height: 100%;
        object-fit: cover;
      }

      .preview-actions {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.6);
        display: flex;
        justify-content: center;
        align-items: center;
        gap: 15px;
        opacity: 0;
        transition: opacity 0.3s;

        .el-icon {
          color: #fff;
          font-size: 20px;
          cursor: pointer;

          &:hover {
            transform: scale(1.1);
          }
        }
      }
    }
  }
}

.pagination {
  display: flex;
  justify-content: center;
  margin-top: 20px;
}
</style>
