import request from "@/utils/request";

// 删除用户自定义菜单
export function deleteUserUse(data) {
  return request({
    url: "/system/schoolUserUseFunction/delete",
    method: "post",
    data,
  });
}

// 添加巡检人员
export function approvalList() {
  return request({
    url: "/system/schoolApprovalConfig/list",
    method: "get",
  });
}

// 是否开启审批 /system/schoolApprovalConfig/update/status
export function updateStatus(data) {
  return request({
    url: "/system/schoolApprovalConfig/update/status",
    method: "put",
    data,
  });
}

// 添加审批人 /system/schoolApprovalPeopleConfig/add
export function addApprovalPeople(data) {
  return request({
    url: "/system/schoolApprovalPeopleConfig/add",
    method: "post",
    data,
  });
}

// 审批单列表 /system/schoolApprovalPeople/user/approval/list
export function approvalPeopleList(params) {
  return request({
    url: "/system/schoolApprovalPeople/user/approval/list",
    method: "get",
    params
  })
}

// 操作审批 /system/schoolApprovalPeople/update
export function updateApprovalPeople(data) {
  return request({
    url: "/system/schoolApprovalPeople/update",
    method: "put",
    data,
  })
}

// 获取变更配置记录 /system/schoolLogStatus/list
export function getSchoolLogStatusList() {
  return request({
    url: "/system/schoolLogStatus/list",
    method: "get",
  })
}

// 修改配置记录 /system/schoolLogStatus/update
export function updateSchoolLogStatus(data) {
  return request({
    url: "/system/schoolLogStatus/update",
    method: "put",
    data,
  })
}


// 记录用户操作功能 /system/schoolUserUseFunction/add
export function addSchoolUserUseFunction(data) {
  return request({
    url: "/system/schoolUserUseFunction/add",
    method: "post",
    data,
  })
}

// 获取常用功能 /system/schoolUserUseFunction/add
export function getSchoolUserUseFunction(params) {
  return request({
    url: "/system/schoolUserUseFunction/list",
    method: "get",
    params
  })
}