<template>
  <div class="dataPermit app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">{{ route.meta.title }}</div>
      </template>

      <div class="info">
        <div class="info-item">
          <div class="info-label">机构名称：</div>
          <div class="info-value">{{ info.corpName }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">机构用户码（corpId）：</div>
          <div class="info-value">{{ info.corpId }}</div>
        </div>
        <div class="info-item">
          <div class="info-label">机构密钥（corpSecret）：</div>
          <div class="info-item_btns">
            <span 
              v-if="showSecret" 
              style="cursor: pointer" 
              @click="copySecret" 
              :title="'点击复制'"
            >{{ info.corpSecret }}</span>
            <span v-else>******</span>
            <el-icon size="20" style="cursor: pointer" @click="toggleSecret">
              <View v-if="showSecret" />
              <Hide v-else />
            </el-icon>
            <el-button type="success" size="small" @click="handleRefresh">刷新</el-button>
          </div>
        </div>
      </div>

      <el-form :model="config" v-if="isShow">
        <div class="config-tit">绑定上级机构</div>

        <!-- 已绑定机构列表 -->
        <div
          class="config-block"
          v-if="config.parentList && config.parentList.length > 0"
        >
          <div v-for="(item, index) in config.parentList" :key="index">
            <template v-if="item.isActive">
              <div class="bound-corp">
                <span>已绑定机构：{{ item.corpName }} ({{ item.corpId }})</span>
                <el-icon
                  v-if="isEdit"
                  color="#f56c6c"
                  size="20"
                  style="cursor: pointer"
                  @click="handleDel(index)"
                >
                  <Delete />
                </el-icon>
              </div>
            </template>
          </div>
        </div>
        <!-- 新增机构表单 -->
        <div class="config-block" v-if="isEdit && pendingCorps.length > 0">
          <div v-for="(item, index) in pendingCorps" :key="index">
            <el-form-item label="corpId">
              <div style="display: flex; align-items: center; gap: 10px">
                <el-input
                  v-model="item.corpId"
                  placeholder="请输入corpId"
                  style="width: 250px"
                  :disabled="item.isActive"
                />
                <el-icon
                  color="#f56c6c"
                  size="20"
                  style="cursor: pointer"
                  @click="handleRemovePending(index)"
                >
                  <Delete />
                </el-icon>
              </div>
            </el-form-item>
            <el-form-item label="corpSecret">
              <div class="config-btns">
                <el-input
                  type="password"
                  v-model="item.corpSecret"
                  placeholder="请输入corpSecret"
                  style="width: 250px"
                  show-password
                  :disabled="item.isActive"
                />
                <el-button
                  type="warning"
                  @click="handleValidate(item)"
                  :disabled="item.isActive"
                  >校验</el-button
                >
                <div class="config-btns_tip">
                  <el-icon :color="item.isActive ? '#a2ef4d' : '#bd3124'" size="20">
                    <CircleCheckFilled v-if="item.isActive" />
                    <CircleCloseFilled v-if="!item.isActive" />
                  </el-icon>
                  {{ item.isActive ? "已" : "未" }}激活
                </div>
              </div>
            </el-form-item>
          </div>
        </div>

        <!-- 添加按钮 -->
        <div v-if="isEdit" style="padding-left: 200px; margin-bottom: 30px">
          <el-icon size="30" style="cursor: pointer" @click="handleShowAddForm">
            <CirclePlusFilled />
          </el-icon>
        </div>

        <!-- 数据权限设置部分 -->
        <div class="config-tit">数据权限设置</div>
        <div class="config-block">
          <el-form-item label="是否开放数据权限给上级机构">
            <el-switch v-model="config.isOpenData" :disabled="!isEdit" />
          </el-form-item>
          <el-form-item
            v-if="config.isOpenData"
            label="请选择开放数据权限"
            prop="permissions"
          >
            <el-checkbox-group v-model="config.permissions" :disabled="!isEdit">
              <!-- <el-checkbox label="角色表" value="6"></el-checkbox>
              <el-checkbox label="用户表" value="5"></el-checkbox>
              <el-checkbox label="待处理工单表" value="4"></el-checkbox>
              <el-checkbox label="已处理工单表" value="3"></el-checkbox>
              <el-checkbox label="待认领工单表" value="2"></el-checkbox>
              <el-checkbox label="设备台账信息" value="1"></el-checkbox> -->
              <el-checkbox label="设备台账信息" value="1"></el-checkbox>
              <el-checkbox label="待认领工单表" value="2"></el-checkbox>
              <el-checkbox label="已处理工单表" value="3"></el-checkbox>
              <el-checkbox label="待处理工单表" value="4"></el-checkbox>
              <el-checkbox label="用户表" value="5"></el-checkbox>
              <el-checkbox label="角色表" value="6"></el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </div>

        <div class="dataPermit-btns">
          <el-button type="primary" @click="handleEdit">{{
            !isEdit ? "编辑" : "保存"
          }}</el-button>
          <el-button v-if="isEdit" @click="handleCancel">取消</el-button>
        </div>
      </el-form>
    </el-card>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { reactive, ref, toRefs, getCurrentInstance, onMounted } from "vue";
import { ElMessage, genFileId, ElMessageBox, ElLoading } from "element-plus";
import { debounce } from "@/utils/debounce";
import {
  corpInfo,
  refreshSecret,
  bind,
  listBindCorp,
  saveBind,
} from "@/api/system/dataPermissions";
import { View, Hide, Delete } from "@element-plus/icons-vue";

const { proxy } = getCurrentInstance();
const route = useRoute();
const isShow = ref(false);
const state = reactive({
  isEdit: false,
  info: {},
  showSecret: false,
  showAddForm: false,
  newCorp: {
    corpId: "",
    corpSecret: "",
    isActive: false,
  },
  config: {
    parentList: [],
  },
  pendingCorps: [], // 用于存储待验证的机构列表
});

const { isEdit, info, config, showSecret, showAddForm, newCorp, pendingCorps } = toRefs(
  state
);

const handleShowAddForm = () => {
  // 检查是否已绑定三个机构
  const totalCorps = state.config.parentList.length + state.pendingCorps.length;
  if (totalCorps >= 3) {
    proxy.$modal.msgWarning("最多只能绑定三个机构");
    return;
  }

  // 先检查是否有未验证的机构
  const unvalidatedCorps = state.pendingCorps.filter((item) => !item.isActive);
  if (unvalidatedCorps.length > 0) {
    const lastCorp = unvalidatedCorps[0];
    if (!lastCorp.corpId || !lastCorp.corpSecret) {
      proxy.$modal.msgWarning("请先输入新增机构的corpId和corpSecret");
      return;
    }
    proxy.$modal.msgWarning("请先对新增机构进行校验");
    return;
  }

  // 新增表单
  state.pendingCorps.push({
    corpId: "",
    corpSecret: "",
    isActive: false,
  });
};

listBindCorp()
  .then((res) => {
    console.log("绑定的机构列表", res);
    res.data.length > 0 ? (isShow.value = false) : (isShow.value = true);
  })
  .catch(() => {});

/* const handleAdd = () => {
  if (!state.newCorp.isActive) {
    proxy.$modal.msgWarning('请先对机构进行校验')
    return
  }

  state.config.parentList.push({
    parentCorpId: state.newCorp.corpId,
    parentCorpName: state.newCorp.corpName,
    isActive: true
  });

  state.showAddForm = false;
} */

const handleDel = (idx) => {
  /* if (state.config.parentList.length == 1) {
        proxy.$modal.msgWarning('至少绑定一个上级机构')
        return
    } */
  state.config.parentList.splice(idx, 1);
};

const handleRefresh = () => {
  ElMessageBox.confirm("确认刷新密钥?", "提示", {
    confirmButtonText: "确认",
    cancelButtonText: "返回",
    type: "warning",
  })
    .then(() => {
      // 调用刷新密钥接口
      refreshSecret()
        .then((res) => {
          state.info = {
            ...state.info,
            corpSecret: res.data.corpSecret,
          };
          ElMessage.success("刷新成功");
        })
        .catch((err) => {
          proxy.$modal.msgError("刷新密钥失败");
        });
    })
    .catch(() => {});
};

// 获取机构信息
const getCorpInfo = () => {
  proxy.$modal.loading();
  corpInfo()
    .then((response) => {
      if (response.code === 200) {
        const res = response.data;
        console.log("机构信息", res);
        state.info = {
          corpId: res.corpId,
          corpName: res.corpName,
          corpSecret: res.corpSecret,
        };

        state.config.isOpenData = res.isOpendata === 1;

        state.config.permissions = [];
        if (res.permissions) {
          if (res.permissions.roleTablePermission) state.config.permissions.push("6");
          if (res.permissions.userTablePermission) state.config.permissions.push("5");
          if (res.permissions.pendingWorkOrderPermission)
            state.config.permissions.push("4");
          if (res.permissions.processedWorkOrderPermission)
            state.config.permissions.push("3");
          if (res.permissions.unclaimedWorkOrderPermission)
            state.config.permissions.push("2");
          if (res.permissions.deviceLedgerPermission) state.config.permissions.push("1");
        }

        state.config.parentList = res.bindCorpInfoList.map((item) => ({
          corpId: item.parentCorpId,
          corpName: item.parentCorpName,
          corpSecret: item.parentCorpSecret, // 保存密钥
          isActive: item.isActive === 1,
          isDefault: item.isDefault === 1,
        }));
        proxy.$modal.closeLoading();
      } else {
        // proxy.$modal.msgError(response.msg || '获取机构信息失败')
        proxy.$modal.closeLoading();
      }
    })
    .catch((err) => {
      // proxy.$modal.msgError('获取机构信息失败')
      proxy.$modal.closeLoading();
    });
};

// 切换密钥显示/隐藏
const toggleSecret = () => {
  state.showSecret = !state.showSecret;
};

// 处理编辑/保存按钮点击
const handleEdit = () => {
  if (state.isEdit) {
    // 检查是否有未验证的机构
    const unvalidatedCorps = state.pendingCorps.filter((item) => !item.isActive);
    if (unvalidatedCorps.length > 0) {
      proxy.$modal.msgWarning("请先对未激活的机构进行校验");
      return;
    }

    // 显示对话框
    ElMessageBox.confirm("确认绑定？该机构将看到您的数据", "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "返回",
      type: "warning",
    })
      .then(() => {
        const params = {
          schoolBindCorpVOS: [
            ...state.config.parentList.map((item) => ({
              corpId: item.corpId,
              corpSecret: item.corpSecret,
            })),
            ...state.pendingCorps.map((item) => ({
              corpId: item.corpId,
              corpSecret: item.corpSecret,
            })),
          ],
          isOpenData: state.config.isOpenData,
          deviceLedgerPermission: state.config.permissions.includes("6"),
          unclaimedWorkOrderPermission: state.config.permissions.includes("5"),
          processedWorkOrderPermission: state.config.permissions.includes("4"),
          pendingWorkOrderPermission: state.config.permissions.includes("3"),
          userTablePermission: state.config.permissions.includes("2"),
          roleTablePermission: state.config.permissions.includes("1"),
        };
        // console.log('保存参数',params);
        // 调用保存接口
        saveBind(params)
          .then((res) => {
            if (res.code === 200) {
              ElMessage.success("保存成功");
            } else {
              // proxy.$modal.msgError(res.msg || '保存失败')
            }
          })
          .catch((err) => {
            // proxy.$modal.msgError('保存失败')
          })
          .finally(() => {
            state.isEdit = false;
            state.pendingCorps = [];
            getCorpInfo(); // 刷新页面数据
          });
      })
      .catch(() => {});
  } else {
    state.isEdit = true;
  }
};

// 处理取消按钮点击
const handleCancel = () => {
  state.isEdit = false;
  // 重新获取数据，恢复原始状态
  getCorpInfo();
};

// 校验绑定的上级机构
const handleValidate = (item) => {
  if (!item.corpId || !item.corpSecret) {
    proxy.$modal.msgWarning("请输入完整的corpId和corpSecret");
    return;
  }

  const loading = ElLoading.service({
    lock: true,
    text: "校验中...",
    background: "rgba(0, 0, 0, 0.7)",
  });

  const params = {
    corpId: item.corpId,
    corpSecret: item.corpSecret,
  };

  bind(params)
    .then((res) => {
      if (res.code === 200) {
        item.isActive = true;
        item.parentCorpName = res.data.corpName;
        ElMessage.success("校验成功");
      }
    })
    .catch((err) => {
      // proxy.$modal.msgError('校验失败')
    })
    .finally(() => {
      loading.close();
    });
};

/* const handleCancelAdd = () => {
  state.showAddForm = false;
  state.newCorp = {
    corpId: '',
    corpSecret: '',
    isActive: false
  };
}; */

const handleRemovePending = (index) => {
  state.pendingCorps.splice(index, 1);
};

// 页面加载时获取数据
onMounted(() => {
  getCorpInfo();
});

const copySecret = () => {
  navigator.clipboard.writeText(info.value.corpSecret)
    .then(() => {
      ElMessage.success('复制成功');
    })
    .catch(() => {
      ElMessage.error('复制失败');
    });
};
</script>

<style lang="scss" scoped>
.dataPermit {
  .info {
    border: 1px solid #ebeef5;
    border-radius: 4px;
    padding: 15px;
    &-item {
      display: flex;
      margin-bottom: 10px;
      &:last-child {
        margin-bottom: 0;
      }
    }
    &-label {
      color: #000;
      font-size: 16px;
    }
    &-value {
      color: #000;
      font-size: 14px;
    }
    &-item_btns {
      display: flex;
      align-items: center;
      gap: 0 20px;
    }
  }
  .config {
    &-tit {
      font-size: 14px;
      color: #606266;
      font-weight: bold;
      margin: 40px 0 25px;
      &::before {
        content: "*";
        padding-right: 3px;
        color: #f56c6c;
      }
    }
    &-block {
      margin-left: 20px;
      padding: 20px;
      border: 1px solid #ebeef5;
      border-radius: 4px;
      margin-bottom: 20px;
    }
    &-btns {
      display: flex;
      gap: 0 20px;
      &_tip {
        display: flex;
        align-items: center;
        gap: 5px;
      }
    }
  }

  &-btns {
    width: 600px;
    display: flex;
    justify-content: center;
    gap: 0 30px;
  }
}

.bound-corp {
  display: flex;
  align-items: center;
  padding: 10px 0;
}
</style>
