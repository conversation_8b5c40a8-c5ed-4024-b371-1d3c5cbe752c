<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <div class="flex">
          <div class="block">
            <div class="block-subTitle">资产覆盖率</div>
            <div class="battery">
              <span class="floating-text">{{
                objData.assetCoverageStatistics.coverage
              }}</span>
            </div>
          </div>
          <div class="block">
            <div class="block-subTitle">部门资产总数</div>
            <div class="echart-item">
              <div class="export-btn" @click="exportDeptAssets">导出</div>
              <Echarts
                id="barData"
                width="100%"
                height="10vw"
                :fullOptions="barOption"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">
          <div class="titleText">网络信息</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in titleTabList"
              :key="item.value"
              :class="{ active: curNetworkTab == item.value }"
              @click="handleNetwork(item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="echart-item" v-loading="loadingNetwork">
          <div class="export-btn" @click="exportNetwork">导出</div>
          <Echarts
            id="lineData"
            width="100%"
            height="10vw"
            :fullOptions="lineOption"
          />
        </div>
      </div>
      <div class="col1-block4">
        <div class="block-title">
          <div class="titleText">备件使用量趋势</div>
        </div>
        <Echarts
          id="lineData2"
          width="100%"
          height="10vw"
          :fullOptions="lineOption2"
        />
      </div>
    </div>

    <div class="col col2">
      <div class="col2-block1">
        <div class="block-title long">
          <div class="titleText">资产增长趋势图</div>
        </div>
        <div class="echart-item">
          <div class="rangeTab">
            <div
              v-for="item in rangeList"
              :key="item.value"
              class="rangeTab_item"
              :class="{ active: curRange1 == item.value }"
              @click="handleRange(0, item.value)"
            >
              {{ item.label }}
            </div>
          </div>
          <Echarts
            id="lineData3"
            width="100%"
            height="14vw"
            :fullOptions="lineOption3"
          />
        </div>
      </div>
      <div class="col2-block2">
        <div class="block-title long">
          <div class="titleText">资产生命周期状态分布</div>
        </div>
        <div class="echart-item">
          <div class="rangeTab">
            <div
              v-for="item in rangeList"
              :key="item.value"
              class="rangeTab_item"
              :class="{ active: curRange2 == item.value }"
              @click="handleRange(1, item.value)"
            >
              {{ item.label }}
            </div>
          </div>
          <Echarts
            id="lineData4"
            width="100%"
            height="14vw"
            :fullOptions="lineOption4"
          />
        </div>
      </div>
      <div class="col2-block3">
        <div class="block-title long"><div class="titleText">排行榜</div></div>
        <div class="flex">
          <div>
            <div class="subtitle">备件使用排行榜</div>
            <div class="table2">
              <div class="table-row opt-title">
                <div class="table-row_number table-row_head">序号</div>
                <div class="table-row_type table-row_head">备件名称</div>
                <div class="table-row_count table-row_head">使用量</div>
              </div>
              <div
                class="table-data"
                @mouseenter="setAnimate(false, 1)"
                @mouseleave="setAnimate(true, 1)"
              >
                <div :class="{ 'table-data_inner': animate1 }">
                  <div
                    class="table-row data"
                    v-for="(item, index) in objData.sparepartsUseStatistics"
                    :key="index"
                    :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
                  >
                    <div class="table-row_number">{{ item.rank }}</div>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.sparepartsName)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_type">
                        {{ item.sparepartsName }}
                      </div>
                    </el-tooltip>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.userNum)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_count">
                        {{ item.userNum > 99999 ? "99999+" : item.userNum }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div>
            <div class="subtitle">设备备件消耗排行榜</div>
            <div class="table2">
              <div class="table-row opt-title">
                <div class="table-row_number table-row_head">序号</div>
                <div class="table-row_type table-row_head">设备名称</div>
                <div class="table-row_count table-row_head">使用量</div>
              </div>
              <div
                class="table-data"
                @mouseenter="setAnimate(false, 2)"
                @mouseleave="setAnimate(true, 2)"
              >
                <div :class="{ 'table-data_inner': animate2 }">
                  <div
                    class="table-row data"
                    v-for="(
                      item, index
                    ) in objData.sparepartsConsumptionStatistics"
                    :key="index"
                    :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
                  >
                    <div class="table-row_number">{{ item.rank }}</div>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.deviceName)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_type">{{ item.deviceName }}</div>
                    </el-tooltip>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.consumptionNum)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_count">
                        {{
                          item.consumptionNum > 99999
                            ? "99999+"
                            : item.consumptionNum
                        }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block">
            <div class="subtitle">应用使用时长排行榜</div>
            <div class="echart-item">
              <Echarts
                id="barData2"
                ref="barData2Ref"
                width="100%"
                height="10vw"
                :getYMax="true"
                :fullOptions="barOption2"
              />
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1" :class="curAssetsTab == 0 ? 'left' : 'right'">
        <div class="col3-block1_left" @click="curAssetsTab = 0"></div>
        <div class="col3-block1_right" @click="curAssetsTab = 1"></div>
      </div>
      <div class="col3-block2 bg" v-if="curAssetsTab == 0">
        <div class="flex" style="margin-bottom: 0.35vw">
          <div class="block">
            <div class="block-subTitle">使用年限占比</div>
            <StripeCircularPie
              :option="pieOption6"
              id="pieData6"
              ref="pieRef6"
            />
          </div>
          <div class="block">
            <div class="block-subTitle">硬盘大小占比</div>
            <RadiusCircularPie
              :option="pieOption10"
              id="pieData10"
              ref="pieRef10"
            />
          </div>
        </div>
        <div class="flex" style="margin-bottom: 0.8vw">
          <div class="block">
            <div class="block-subTitle">设备类型占比</div>
            <CircularPie3D
              :option="pieOption8"
              :data="data3D8"
              id="pieData8"
              ref="pieRef8"
              maxListHeight="4vw"
              bgLeft="0.3vw"
              style="min-height: 11.5vw"
            />
          </div>
          <div class="block">
            <div class="block-subTitle">端口类型占比</div>
            <CircularPie3D
              :option="pieOption9"
              :data="data3D9"
              id="pieData9"
              ref="pieRef9"
              maxListHeight="4vw"
              bgLeft="0.3vw"
            />
          </div>
        </div>
        <div class="flex" style="margin-bottom: 0.4vw">
          <div class="block">
            <div class="block-subTitle">故障时长占比</div>
            <CircularPie :option="pieOption7" id="pieData7" ref="pieRef7" />
          </div>
          <div class="block">
            <div class="block-subTitle">内存占比</div>
            <RadiusCircularPie
              :option="pieOption11"
              id="pieData11"
              ref="pieRef11"
            />
          </div>
        </div>
        <div class="flex" style="padding-bottom: 0.8vw">
          <div class="block">
            <div class="block-subTitle">品牌占比</div>
            <CircularPie3D
              :option="pieOption12"
              :data="data3D12"
              id="pieData12"
              ref="pieRef12"
              maxListHeight="4vw"
              bgLeft="0.3vw"
              style="min-height: 11.5vw"
            />
          </div>
          <div class="block">
            <div class="block-subTitle">信息资产类别等级占比</div>
            <CircularPie3D
              :option="pieOption13"
              :data="data3D13"
              id="pieData13"
              ref="pieRef13"
              maxListHeight="4vw"
              bgLeft="0.3vw"
            />
          </div>
        </div>
      </div>
      <div class="col3-block2" v-if="curAssetsTab == 1">
        <div class="flex" style="margin-bottom: 0.35vw">
          <div class="block">
            <div class="block-subTitle">软件库分类占比</div>
            <StripeCircularPie
              :option="pieOption2"
              id="pieData2"
              ref="pieRef2"
            />
          </div>
          <div class="block">
            <div class="block-subTitle">软件许可证到期时间占比</div>
            <RadiusCircularPie
              :option="pieOption3"
              id="pieData3"
              ref="pieRef3"
            />
          </div>
        </div>
        <div class="flex" style="margin-bottom: 1vw">
          <div class="block">
            <div class="block-subTitle">系统版本占比（正版）</div>
            <CircularPie3D
              :option="pieOption15"
              :data="data3D15"
              id="pieData15"
              ref="pieRef15"
              maxListHeight="4vw"
              bgLeft="0.3vw"
            />
          </div>
          <div class="block">
            <div class="block-subTitle">软件使用状况</div>
            <CircularPie3D
              :option="pieOption4"
              :data="data3D4"
              id="pieData4"
              ref="pieRef4"
              maxListHeight="4vw"
              bgLeft="0.3vw"
            />
          </div>
        </div>
        <div class="col3-mask"></div>
      </div>
    </div>
  </div>
</template>
    
    <script setup name="unifyIndex">
import CircularPie3D from "@/views/unifyManage/component/echarts/circularPie3D.vue";
import StripeCircularPie from "@/views/unifyManage/component/echarts/stripeCircularPie.vue";
import CircularPie from "@/views/unifyManage/component/echarts/circularPie.vue";
import RadiusCircularPie from "@/views/unifyManage/component/echarts/radiusCircularPie.vue";
import Echarts from "@/components/Echarts/index.vue";
import Echarts3D from "@/components/Echarts/index3D.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getSchoolStatistics,
  getWorkOrderStatistics,
  getAssetsStatistics,
  deptAssetsExport,
  getAssetsLifecycle,
  getAssetsTrend,
  getNetworkStatistics,
} from "@/api/unify";
import { fitChartSize, downloadBlob } from "@/utils";
import { autoPicture } from "@/utils/uploadCode2.js";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  pieRef2: null,
  pieRef3: null,
  pieRef4: null,
  pieRef5: null,
  pieRef6: null,
  pieRef7: null,
  pieRef8: null,
  pieRef9: null,
  pieRef10: null,
  pieRef11: null,
  pieRef13: null,
  pieRef12: null,
  pieRef15: null,
  loadingNetwork: false,
  networkTimer: null,
  barData2Ref: null,
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData15Ref: null,
  pieData6Ref: null,
  scrolltimer1: null,
  scrolltimer2: null,
  animate1: false,
  animate2: false,
  curRange1: 3,
  curRange2: 1,
  curNetworkTab: 0,
  titleTabList: [
    { label: "每分钟平均网速", value: 0 },
    { label: "每分钟峰值网速", value: 1 },
  ],
  rangeList: [
    { label: "按年", value: 3 },
    { label: "按月", value: 2 },
    { label: "按日", value: 1 },
  ],
  networkList: [
    { speed: "746kb/s", maxSpeed: "1362kb/s" },
    { speed: "52Mb/s", maxSpeed: "114Mb/s" },
  ],
  curAssetsTab: 0,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  pieRef2,
  pieRef3,
  pieRef4,
  pieRef5,
  pieRef6,
  pieRef7,
  pieRef8,
  pieRef9,
  pieRef10,
  pieRef11,
  pieRef12,
  pieRef13,
  pieRef15,
  pieData8Ref,
  scrolltimer1,
  scrolltimer2,
  loadingNetwork,
  networkTimer,
  barData2Ref,
  curNetworkTab,
  titleTabList,
  rangeList,
  pieData3Ref,
  pieData2Ref,
  pieData11Ref,
  pieData10Ref,
  pieData7Ref,
  pieData9Ref,
  pieData15Ref,
  pieData12Ref,
  pieData13Ref,
  pieData4Ref,
  pieData6Ref,
  animate1,
  animate2,
  curRange1,
  curRange2,
  curAssetsTab,
} = toRefs(state);

const col2_block1_list = ref([
  { name: "硬件资产", num: 0 },
  { name: "软件资产", num: 4 },
  { name: "数据信息资产", num: 0 },
  { name: "网络资产", num: 6 },
  { name: "知识产权", num: 5 },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "开机率", num: "0", unit: "%" },
  { name: "报废数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "0", unit: "人" },
  { name: "今日执勤", num: "0", unit: "人" },
  { name: "今日轮休", num: "0", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "知识库", tip: "", total: 0, ratio: "0%", num: 0 },
  { title: "对比上个月", tip: "个数同比", total: 0, ratio: "0", num: 0 },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  { title: "今日工单平均处理时间", total: 0, ratio: "0%", num: 0 },
  { title: "合计工单平均处理时间", total: 0, fq: "0天/次" },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据

//部门资产总数柱状图相关配置
const barData1_1 = ref([100, 30, 40, 50, 60]); // 系列1的数据
const barData1_2 = ref([60, 50, 40, 30, 100]); // 系列2的数据
const borderHeight = 2; // 底部边框的高度（数据单位）
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        // console.log(params, "params");
        let relVal1 = params[0];
        relVal1.value = Math.ceil(params[0].value);
        let relVal2 = params[1];
        relVal2.value = Math.ceil(params[1].value);
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${relVal1.color.legendColor}"></i>${relVal1.seriesName}</div><b style="margin-left: 20px">${relVal1.value}</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${relVal2.color.legendColor}"></i>${relVal2.seriesName}</div><b style="margin-left: 20px">${relVal2.value}</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemWidth: fitChartSize(10),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: fitChartSize(8),
      left: fitChartSize(4),
      data: [
        { name: "部门人数", itemStyle: { color: "#138CFE" } },
        { name: "资产总数", itemStyle: { color: "#0FFFF2" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    grid: {
      left: "12%",
      right: "0%",
      height: "63%",
      top: "20%",
    },
    xAxis: {
      type: "category",
      data: ["日常部", "巡检部", "技术部", "保安部", "行政部"],
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(9),
        rotate: 18,
        formatter: function (value, index) {
          return value.length > 4 ? `${value.substring(0, 3)}...` : `${value}`;
        },
        margin: fitChartSize(10),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(179, 223, 255, 0.5)",
        },
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(9),
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: ["rgba(217, 231, 255, 0.2)"],
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      // ========== 系列1的底部边框 ==========
      {
        type: "bar",
        data: barData1_1.value.map(() => borderHeight), // 数据固定为边框高度
        barWidth: "20%", // 与主系列宽度一致
        itemStyle: {
          color: "#138CFE", // 边框颜色,
        },
        stack: "border1", // 堆叠组名（需唯一）
        z: 1, // 确保边框系列在主系列下方
        tooltip: { show: false }, // 隐藏提示
      },
      {
        name: "部门人数",
        data: barData1_1.value,
        type: "bar",
        barWidth: "20%",
        barGap: 0,
        showBackground: true,
        backgroundStyle: {
          color: "rgba(196, 196, 196, 0.05)",
        },
        itemStyle: {
          color: {
            type: "linear",
            legendColor: "#138CFE",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(19, 141, 255, 1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 102, 198, 0.6)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        stack: "border1", // 堆叠组名（需唯一）
      },
      // ========== 系列2的底部边框 ==========
      {
        type: "bar",
        data: barData1_2.value.map(() => borderHeight), // 数据固定为边框高度
        barWidth: "20%", // 与主系列宽度一致
        itemStyle: {
          color: "#0FFFF2", // 边框颜色,
        },
        stack: "border2", // 堆叠组名（需唯一）
        z: 1, // 确保边框系列在主系列下方
        tooltip: { show: false }, // 隐藏提示
      },
      {
        name: "资产总数",
        data: barData1_2.value,
        type: "bar",
        barWidth: "20%",
        stack: "border2", // 堆叠组名（需唯一）
        showBackground: true,
        backgroundStyle: {
          color: "rgba(196, 196, 196, 0.05)",
        },
        itemStyle: {
          color: {
            type: "linear",
            legendColor: "#0FFFF2",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(15, 255, 242, 1)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(33, 231, 251, 0.6)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
      },
    ],
  },
});

//应用使用时长TOP5柱状图相关配置
const barData2 = ref([100, 50, 40, 30, 20]);
const borderHeight2 = 3;
const topData = barData2.value.map(() => borderHeight2);
const barColors2 = [
  "rgba(73, 195, 132, 1)",
  "rgba(156, 190, 207, 1)",
  "rgba(43, 142, 243, 1)",
  "rgba(221, 210, 70, 1)",
  "rgba(53, 224, 255, 1)",
];
const yMax = Math.max(...barData2.value) * 1.2;
// 预处理背景系列数据：每个柱子单独配置 borderColor
const backgroundData = barData2.value.map((_, index) => ({
  value: yMax,
  itemStyle: {
    borderColor: barColors2[index % barColors2.length], // 通过取余确保颜色循环
  },
}));
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value + borderHeight2}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "17%",
      right: "5%",
      top: "12%",
    },
    xAxis: {
      type: "category",
      data: ["其它", "娱乐类", "办公类", "教学类", "应用类"],
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(11),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      minInterval: 1,
      splitNumber: 4,
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(11),
        formatter: "{value} h",
        align: "left",
        margin: fitChartSize(45),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      // 背景系列：高度固定为 yMax
      {
        type: "bar",
        barGap: "-100%",
        barWidth: "49%",
        data: backgroundData, // 所有背景条数据 = yMax
        itemStyle: {
          color: "transparent",
          borderWidth: 0.1,
        },
        tooltip: { show: false }, // 隐藏提示框
      },
      {
        data: [
          {
            name: "其它",
            value: barData2.value[0] - borderHeight2,
            itemStyle: {
              color: "rgba(73, 195, 132, 0.5)",
            },
          },
          {
            name: "娱乐类",
            value: barData2.value[1] - borderHeight2,
            itemStyle: { color: "rgba(156, 190, 207, 0.5)" },
          },
          {
            name: "办公类",
            value: barData2.value[2] - borderHeight2,
            itemStyle: { color: "rgba(43, 142, 243, 0.5)" },
          },
          {
            name: "教学类",
            value: barData2.value[3] - borderHeight2,
            itemStyle: { color: "rgba(221, 210, 70, 0.5)" },
          },
          {
            name: "应用类",
            value: barData2.value[4] - borderHeight2,
            itemStyle: { color: "rgba(53, 224, 255, 0.5)" },
          },
        ],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        stack: "total", // 与主系列堆叠
        label: {
          show: true,
          position: "top",
          formatter: (params) => {
            return `${params.value + borderHeight2}`;
          },
          color: '#fff',
          fontSize: fitChartSize(10),
        },
      },
      // 顶部系列：显示固定高度的边框
      {
        type: "bar",
        data: topData.map((value, index) => ({
          value,
          itemStyle: {
            color: barColors2[index], // 自定义颜色逻辑
          },
        })),
        barWidth: "50%",
        stack: "total", // 与主系列堆叠
        tooltip: { show: false }, // 隐藏提示框
      },
    ],
  },
});

//系统版本占比饼图
const pieColor15 = ref([
  "#47c07f",
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D15 = ref([]);
const pieOption15 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 软件资产状况饼图（假数据）
const pieOption2 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "操作系统",
            value: 10,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "教学软件",
            value: 20,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "其它软件",
            value: 30,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
        ],
      },
    ],
  },
});

// 软件到期时间饼图（假数据）
const pieOption3 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["60%", "68%"],
        center: ["49%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "1年以内",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "1-3年",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "3-5年",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "5年以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 软件使用状况饼图
const pieColor4 = ref([
  "#47c07f",
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D4 = ref([]);
const pieOption4 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "一年以内",
            value: 0,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "1-3年",
            value: 0,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "3-5年",
            value: 0,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
          {
            name: "5年以上",
            value: 0,
            itemStyle: { color: "rgba(255,255,0,0.3)", borderColor: "#ffff00" },
          },
        ],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "1天以内",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "1-5天",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "5-10天",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "10天以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图配置项
const pieColor8 = ref([
  "#47c07f",
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D8 = ref([]);
const pieOption8 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产端口类型占比饼图配置项
const pieColor9 = ref([
  "#47c07f",
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D9 = ref([]);
const pieOption9 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["60%", "68%"],
        center: ["49%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "128GB以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "128GB-256GB",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "257GB-512GB",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "513GB-1TB",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
          {
            name: "1TB以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["60%", "68%"],
        center: ["49%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "8G以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "8G-16G",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "16G-32G",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "32G-64G",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "64G及以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieColor12 = ref([
  "#0783FA",
  "#76c5ec",
  "#47c07f",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D12 = ref([]);
const pieOption12 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 信息资产类别等级占比
const pieColor13 = ref([
  "#0783FA",
  "#76c5ec",
  "#47c07f",
  "#ebc23d",
  "#E16262",
  "#F29339",
]);
const data3D13 = ref([]);
const pieOption13 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

//网络信息折线图
const lineOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value + "MB/s"
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value + "MB/s"
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(20),
      itemWidth: fitChartSize(20),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "上行网速", itemStyle: { color: "#0783FA" } },
        { name: "下行网速", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(30),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "8%",
      height: "65%",
      right: "4%",
    },
    series: [
      {
        name: "上行网速",
        data: [20, 80, 60, 12, 40, 100],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: {
            legendColor: "#0783FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#0783FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "下行网速",
        data: [60, 50, 30, 70, 11, 90],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
        tooltip: {
          show: false,
        },
      },
    ],
  },
});

// 备件使用量趋势
const lineOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(20),
      itemWidth: fitChartSize(20),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "6.5%",
      data: [
        { name: "入库量", itemStyle: { color: "#FCEBC0" } },
        { name: "消耗量", itemStyle: { color: "#6FE5F7" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["5.7", "5.8", "5.9", "5.10", "5.11", "5.12", "5.13"],
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(30),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "8%",
      height: "65%",
      right: "4%",
    },
    series: [
      {
        name: "入库量",
        data: [20, 80, 60, 12, 40, 100],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(252, 235, 192, .6)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(252, 235, 192, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#FCEBC0",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#FCEBC0",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "消耗量",
        data: [60, 50, 30, 70, 11, 90],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(111, 229, 247, .6)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(111, 229, 247, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#6FE5F7",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#6FE5F7",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
        tooltip: {
          show: false,
        },
      },
    ],
  },
});

//资产增长趋势折线图
const lineOption3 = ref({
  options: {
    color: ["#00F6FF", "#007CFF"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["软件资产", "硬件资产"],
      itemWidth: fitChartSize(12),
      itemHeight: fitChartSize(12),
      itemGap: fitChartSize(35),
      left: "3%",
      top: "6%",
      icon: "roundRect",
      textStyle: {
        fontSize: fitChartSize(13),
        color: "inherit",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      axisLabel: {
        color: "#fff",
        rotate: 0,
        fontSize: fitChartSize(10.5),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
        align: "left",
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#57CEEA",
          opacity: 0.1,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "22%",
      left: "5%",
      height: "67%",
      right: "3.5%",
    },
    series: [
      {
        name: "软件资产",
        data: [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 246, 255, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 246, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#00F6FF",
        },
        lineStyle: {
          color: "#00F6FF",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "硬件资产",
        data: [120, 250, 200, 400, 450, 500, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 124, 255, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 124, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#007CFF",
        },
        lineStyle: {
          color: "#007CFF",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [480],
        type: "line",
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
        tooltip: { show: false },
      },
    ],
  },
});

//资产生命周期状态分布折线图
const lineOption4 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      data: ["正常", "报障（能正常使用）", "报障（不能使用）", "报废", "开机"],
      itemWidth: fitChartSize(12),
      itemHeight: fitChartSize(12),
      itemGap: fitChartSize(26),
      left: "3%",
      top: "6%",
      itemStyle: {
        borderWidth: fitChartSize(2), // 边框宽度
        borderColor: "inherit", // 占位符（实际颜色通过 formatter 动态设置）
        backgroundColor: "#fff", // 中间填充白色
      },
      textStyle: {
        color: "inherit",
        fontSize: fitChartSize(13),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      axisLabel: {
        color: "#fff",
        rotate: 0,
        fontSize: fitChartSize(10.5),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
        align: "left",
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#57CEEA",
          opacity: 0.1,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "22%",
      left: "5%",
      height: "67%",
      right: "3.5%",
    },
    series: [
      {
        name: "正常",
        data: [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300],
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: "#1185F0",
        },
        lineStyle: {
          color: "#1185F0",
          width: 1.5,
        },
        symbolSize: 5,
      },
      {
        name: "报障（能正常使用）",
        data: [120, 250, 200, 400, 450, 500, 200, 100, 100, 300, 200, 100],
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: "#31FBFB",
        },
        lineStyle: {
          color: "#31FBFB",
          width: 1.5,
        },
        symbolSize: 5,
      },
      {
        name: "报障（不能使用）",
        data: [120, 110, 130, 210, 140, 310, 210, 410, 230, 20, 40, 30],
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: "#5158FA",
        },
        lineStyle: {
          color: "#5158FA",
          width: 1.5,
        },
        symbolSize: 5,
      },
      {
        name: "报废",
        data: [220, 350, 400, 300, 250, 100, 300, 150, 300, 200, 400, 300],
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: "#E16262",
        },
        lineStyle: {
          color: "#E16262",
          width: 1.5,
        },
        symbolSize: 5,
      },
      {
        name: "开机",
        data: [320, 150, 100, 250, 150, 300, 250, 300, 400, 200, 400, 300],
        type: "line",
        smooth: true,
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: "#FFE090",
        },
        lineStyle: {
          color: "#FFE090",
          width: 1.5,
        },
        symbolSize: 5,
      },
      {
        data: [540],
        type: "line",
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
        tooltip: { show: false },
      },
    ],
  },
});

function exportDeptAssets() {
  deptAssetsExport().then((res) => {
    console.log(res, "导出部门资产");
    downloadBlob(res, "application/ms-excel", "部门资产.xls");
  });
}

function setAnimate(flag, type) {
  let timer = null;
  if (flag) {
    play(type);
  } else {
    window.clearInterval(state[`scrolltimer${type}`]);
    timer = setTimeout(() => {
      state[`animate${type}`] = false;
      clearTimeout(timer);
    }, 1000);
  }
}

function play(type) {
  let timer = null;
  if (
    objData.value[
      type == 1 ? "sparepartsUseStatistics" : "sparepartsConsumptionStatistics"
    ].length >= 5 &&
    state[`animate${type}`] == false
  ) {
    clearTimeout(state[`scrolltimer${type}`]);
    state[`scrolltimer${type}`] = setTimeout(() => {
      let arr =
        objData.value[
          type == 1
            ? "sparepartsUseStatistics"
            : "sparepartsConsumptionStatistics"
        ];
      let obj = JSON.parse(JSON.stringify(arr[0]));
      arr.push(obj);
      state[`animate${type}`] = true;
      clearTimeout(timer);
      timer = setTimeout(() => {
        arr.shift();
        state[`animate${type}`] = false;
        play(type);
        clearTimeout(timer);
      }, 1000);
    }, 2000);
  }
}

// function setAnimate(flag, type) {
//   if (flag) {
//     play(type);
//   } else {
//     setTimeout(() => {
//       state[`animate${type}`] = false;
//     }, 1000);
//     window.clearInterval(state[`scrolltimer${type}`]);
//   }
// }

// function play(type) {
//   if (
//     objData.value[
//       type == 1 ? "sparepartsUseStatistics" : "sparepartsConsumptionStatistics"
//     ].length >= 5
//   ) {
//     state[`scrolltimer${type}`] = setInterval(() => {
//       let obj = JSON.parse(
//         JSON.stringify(
//           objData.value[
//             type == 1
//               ? "sparepartsUseStatistics"
//               : "sparepartsConsumptionStatistics"
//           ][0]
//         )
//       );
//       objData.value[
//         type == 1
//           ? "sparepartsUseStatistics"
//           : "sparepartsConsumptionStatistics"
//       ].push(obj);
//       state[`animate${type}`] = true;
//       setTimeout(() => {
//         objData.value[
//           type == 1
//             ? "sparepartsUseStatistics"
//             : "sparepartsConsumptionStatistics"
//         ].shift();

//         state[`animate${type}`] = false;
//       }, 1000);
//     }, 2000);
//   }
// }

// 点击折线图 年月日 筛选
function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    // lineOption3.value.options.xAxis.data =
    //   tab == 2
    //     ? [
    //         "1月",
    //         "2月",
    //         "3月",
    //         "4月",
    //         "5月",
    //         "6月",
    //         "7月",
    //         "8月",
    //         "9月",
    //         "10月",
    //         "11月",
    //         "12月",
    //       ]
    //     : tab == 3
    //     ? ["2019年", "2020年", "2021年", "2022年", "2023年", "2024年", "2025年"]
    //     : ["5.8", "5.9", "5.10", "5.11", "5.12", "5.13", "5.14"];
    // lineOption3.value.options.series[0].data =
    //   tab == 2
    //     ? [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300]
    //     : tab == 3
    //     ? [0, 0, 0, 100, 0, 200, 100]
    //     : [0, 0, 0, 0, 0, 1, 4];
    // lineOption3.value.options.series[1].data =
    //   tab == 2
    //     ? [120, 250, 200, 400, 450, 500, 200, 100, 300, 200, 400, 300]
    //     : tab == 3
    //     ? [0, 10, 0, 200, 0, 20, 10]
    //     : [0, 2, 0, 0, 10, 0, 40];

    getAssetsTrend({ range: state.curRange1 }).then((res) => {
      console.log("资产增长趋势", res);
      if (res.code == 200 && res.data) {
        let arr = [],
          data1 = [],
          data2 = [];
        res.data.map((item) => {
          arr.push(
            state.curRange1 == 2
              ? item.date
              : state.curRange1 == 1
              ? item.date.split(" ")[1] || item.date
              : item.date
          );
          data1.push(item.softwareAssets || 0);
          data2.push(item.hardwareAssets || 0);
        });
        lineOption3.value.options.xAxis.data = arr;
        lineOption3.value.options.series[0].data = data1;
        lineOption3.value.options.series[1].data = data2;
        const yMax = Math.max(...[...data1, ...data2]) * 1.2;
        lineOption3.value.options.series[2].data = [yMax];
      }
    });
  }
  if (type == 1) {
    state.curRange2 = tab;
    // lineOption4.value.options.xAxis.data =
    //   tab == 2
    //     ? [
    //         "1月",
    //         "2月",
    //         "3月",
    //         "4月",
    //         "5月",
    //         "6月",
    //         "7月",
    //         "8月",
    //         "9月",
    //         "10月",
    //         "11月",
    //         "12月",
    //       ]
    //     : tab == 3
    //     ? ["2019年", "2020年", "2021年", "2022年", "2023年", "2024年", "2025年"]
    //     : ["5.8", "5.9", "5.10", "5.11", "5.12", "5.13", "5.14"];
    // lineOption4.value.options.series[0].data =
    //   tab == 2
    //     ? [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300]
    //     : tab == 3
    //     ? [0, 0, 0, 100, 0, 200, 100]
    //     : [0, 0, 0, 0, 0, 1, 4];
    // lineOption4.value.options.series[1].data =
    //   tab == 2
    //     ? [120, 250, 200, 400, 450, 500, 200, 100, 300, 200, 400, 300]
    //     : tab == 3
    //     ? [0, 10, 0, 200, 0, 20, 10]
    //     : [0, 2, 0, 0, 10, 0, 40];
    getAssetsLifecycle({ range: state.curRange2 }).then((res) => {
      console.log("资产生命周期", res);
      if (res.code == 200 && res.data) {
        let arr = [],
          data1 = [],
          data2 = [],
          data3 = [],
          data4 = [],
          data5 = [];
        res.data.map((item) => {
          arr.push(
            state.curRange2 == 2
              ? item.date // substring(5, item.date.length).replace(/-/g, ".")
              : state.curRange2 == 1
              ? item.date.split(" ")[1] || item.date
              : item.date
          );
          data1.push(item.normalDeviceTotal || 0);
          data2.push(item.normalUseDeviceTotal || 0);
          data3.push(item.noNormalUseDeviceTotal || 0);
          data4.push(item.scrapDeviceTotal || 0);
          data5.push(item.bootDeviceTotal || 0);
        });
        lineOption4.value.options.xAxis.data = arr;
        lineOption4.value.options.series[0].data = data1;
        lineOption4.value.options.series[1].data = data2;
        lineOption4.value.options.series[2].data = data3;
        lineOption4.value.options.series[3].data = data4;
        lineOption4.value.options.series[4].data = data5;
        const yMax =
          Math.max(...[...data1, ...data2, ...data3, ...data4, ...data5]) * 1.2;
        lineOption4.value.options.series[5].data = [yMax];
      }
    });
  }
}

// 获取网络信息
function handleNetwork(value) {
  state.curNetworkTab = value;
  getNetworkStatistics()
    .then((res) => {
      console.log(res, "网络资产");
      if (res.code == 200 && res.data && res.data.data) {
        let arr = [],
          data1 = [],
          data2 = [];
        if (value == 0) {
          res.data.data?.networkAvg?.reverse().map((item) => {
            arr.push(item.date.split(" ")[1].substring(0, 5));
            data1.push(item.up.toFixed(2) * 1 || 0);
            data2.push(item.down.toFixed(2) * 1 || 0);
          });
        } else {
          res.data.data?.networkPeak?.reverse().map((item) => {
            arr.push(item.date.split(" ")[1].substring(0, 5));
            data1.push(item.up.toFixed(2) * 1 || 0);
            data2.push(item.down.toFixed(2) * 1 || 0);
          });
        }
        lineOption.value.options.series[0].data = data1;
        lineOption.value.options.series[1].data = data2;
        lineOption.value.options.series[2].data = [
          Math.max(...[...data1, ...data2]) * 1.2,
        ];
        lineOption.value.options.xAxis.data = arr;
      }
    })
    .finally(() => (state.loadingNetwork = false));
}

//导出网络信息折线图
function exportNetwork() {
  let canvas = document.getElementById("lineData");
  console.log(canvas, canvas.clientWidth, canvas.clientHeight, "canvas");
  autoPicture(
    "lineData",
    {
      width: canvas.clientWidth,
      height: canvas.clientHeight,
      backgroundColor: "#011631",
    },
    true,
    `每分钟${state.curNetworkTab == 0 ? "平均" : "峰值"}网速.png`
  );
}

function goAnimate(obj) {
  let timer = null;
  window.clearInterval(state.scrolltimer1);
  window.clearInterval(state.scrolltimer2);
  state.animate1 = false;
  state.animate2 = false;
  setTimeout(() => {
    objData.value = {
      ...objData.value,
      ...obj,
    };
    play(1);
    play(2);
    clearTimeout(timer);
  }, 100);
}

//获取数据
async function getData() {
  handleRange(0, state.curRange1);
  handleRange(1, state.curRange2);
  await getSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        assetsTotal,
        restTotal,
        maintenanceTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        sparepartsUseDateStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
        assetCoverageStatistics,
      } = res.data;
      col2_block1_list.value[0].num = deviceTotal;

      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;

      objData.value = {
        ...objData.value,
        assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      goAnimate({
        sparepartsUseStatistics:
          res.data.sparepartsUseStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || [], // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || [], // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
      });

      if (sparepartsUseDateStatistics) {
        let arr = [],
          data1 = [],
          data2 = [];
        // 备件使用日期柱状图赋值
        sparepartsUseDateStatistics.reverse().map((item) => {
          arr.push(item.date.substring(5, item.date.length).replace(/-/g, "."));
          data1.push(item.userNum);
          data2.push(item.consumptionNum);
        });
        lineOption2.value.options.xAxis.data = arr;
        lineOption2.value.options.series[0].data = data1;
        lineOption2.value.options.series[1].data = data2;
        const yMax = Math.max(...[...data1, ...data2]) * 1.2;
        lineOption2.value.options.series[2].data = [yMax];
      }

      if (assetCoverageStatistics?.assetCoverageRanking) {
        let arr = [],
          data1 = [],
          data2 = [];
        assetCoverageStatistics?.assetCoverageRanking.map((item, index) => {
          arr.push(item.deptName);
          data1.push(item.peopleNum);
          data2.push(item.assetsNum);
        });
        barOption.value.options.xAxis.data = arr;
        barData1_1.value = data1;
        barData1_2.value = data2;
        const yMax = Math.max(...[...data1, ...data2]) * 1.2;
        const newHeight = yMax * borderHeight * 0.01;
        barOption.value.options.series[0].data = barData1_1.value.map((item) =>
          item > newHeight ? newHeight : 0
        );
        barOption.value.options.series[1].data = barData1_1.value.map((item) =>
          item > newHeight ? item - newHeight : item
        );
        barOption.value.options.series[2].data = barData1_2.value.map((item) =>
          item > newHeight ? newHeight : 0
        );
        barOption.value.options.series[3].data = barData1_2.value.map((item) =>
          item > newHeight ? item - newHeight : item
        );
      }

      if (deviceAppUserStatistics?.appUseType) {
        let arr = [];
        deviceAppUserStatistics?.appUseType.map((item, index) => {
          arr.push({
            name: item.appType,
            value: item.num,
            proportion: item.proportion,
            height: 20,
            itemStyle: {
              opacity: 0.6,
              color: pieColor4.value[index % pieColor4.value.length],
            },
          });
        });
        data3D4.value = arr;
        console.log(
          "软件使用状况",
          deviceAppUserStatistics?.appUseType,
          data3D4.value
        );
      }

      if (deviceAppUserStatistics?.appUseTime) {
        let arr = [],
          data = [];
        deviceAppUserStatistics?.appUseTime.map((item, index) => {
          arr.push(item.month);
          data.push(item.timeTotal * 1);
        });
        const yMax = Math.max(...data) * 1.2;
        const newHeight = yMax * borderHeight2 * 0.01;
        barOption2.value.options.xAxis.data = arr;
        barOption2.value.options.series[1].data = data.map((val, index) => ({
          name: arr[index],
          value: val > newHeight ? val - newHeight : val,
          itemStyle: {
            color: barColors2[index % barColors2.length].replace(
              ", 1)",
              ", 0.5)"
            ),
          },
        }));
        barOption2.value.options.series[1].label.formatter = (params) => {
          return `${
            params.value > newHeight ? params.value + newHeight : params.value
          } h`;
        };
        barOption2.value.options.tooltip.formatter = (params) => {
          let relVal = params[0];
          return `${relVal.name}<br/>${
            relVal.value > newHeight ? relVal.value + newHeight : relVal.value
          }小时`;
        };
        barOption2.value.options.series[2].data = data.map((val, index) => ({
          value: val > newHeight ? newHeight : 0,
          itemStyle: {
            color: barColors2[index % barColors2.length],
          },
        }));
        nextTick(() => {
          console.log(yMax, "yMax");
          // 获取 Y 轴最大值
          let newMax = yMax;
          console.log(barData2Ref.value, "barData2Ref");
          newMax = barData2Ref.value.getYAxisMax();
          barOption2.value.options.series[0].data = data.map((_, index) => ({
            value: newMax,
            itemStyle: {
              borderColor: barColors2[index % barColors2.length],
            },
          }));
        });
        console.log("应用使用时长", deviceAppUserStatistics?.appUseTime);
      }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];

        col2_block1_list.value[2].num = knowledgeBaseYear;

        optList1.value[0].total = knowledgeBaseYear;
        optList1.value[1].ratio = knowledgeBaseMonthRise;
        optList1.value[1].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      // proxy.$modal.closeLoading();
    });
}

async function getAssetsData() {
  await getAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsFaultBrandProportion,
      assetsBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
      assetsLevelProportion,
    } = res.data;

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      let arr = [];
      assetsSystemVersionProportion.map((item, index) => {
        arr.push({
          name: item.assetsSystemVersionName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor15.value[index % pieColor15.value.length],
          },
        });
      });
      data3D15.value = arr;
      console.log("系统版本占比", assetsTypeProportion, data3D15.value);
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data.map((item) => {
        let obj = deviceTroubleProportion.find((_) => _.timeRange == item.name);
        item.value = obj?.num || 0;
      });
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data.map((item) => {
        let obj = hardwareAssetsUseProportion.find(
          (_) => _.timeRange == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      let arr = [];
      assetsTypeProportion.map((item, index) => {
        arr.push({
          name: item.assetsTypeName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor8.value[index % pieColor8.value.length],
          },
        });
      });
      data3D8.value = arr;
      console.log("设备类型", assetsTypeProportion, data3D8.value);
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      let arr = [];
      assetsPortProportion.map((item, index) => {
        arr.push({
          name: item.assetsPortName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor9.value[index % pieColor9.value.length],
          },
        });
      });
      data3D9.value = arr;
      console.log("端口类型", assetsPortProportion, data3D9.value);
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data.map((item) => {
        let obj = assetsHardDiskProportion.find(
          (_) => _.assetsHardDiskName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data.map((item) => {
        let obj = assetsMemoryProportion.find(
          (_) => _.assetsMemoryName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 故障资产品牌占比
    if (assetsBrandProportion) {
      let arr = [];
      assetsBrandProportion.map((item, index) => {
        arr.push({
          name: item.assetsBrandName,
          value: item.num,
          proportion: item.proportion,
          height: 15,
          itemStyle: {
            opacity: 0.6,
            color: pieColor12.value[index % pieColor12.value.length],
          },
        });
      });
      data3D12.value = arr;
      // console.log("故障资产品牌占比", assetsBrandProportion, data3D12.value);
    }

    // 信息资产类别等级占比
    if (assetsLevelProportion) {
      let arr = [];
      assetsLevelProportion.map((item, index) => {
        arr.push({
          name: item.assetsLevelName,
          value: item.num,
          proportion: item.proportion,
          height: 15,
          itemStyle: {
            opacity: 0.6,
            color: pieColor13.value[index % pieColor13.value.length],
          },
        });
      });
      data3D13.value = arr;
      // console.log("故障资产品牌占比", assetsBrandProportion, data3D13.value);
    }
  });
}

function getOrderData() {
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList3.value[0].total = processingWorkOrderMonth;
      optList3.value[0].ratio = processingWorkOrderMonthRise;
      optList3.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList3.value[1].total = processingWorkOrderYear;
      optList3.value[1].ratio = processingWorkOrderYearRise;
      optList3.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
      } = workOrderProcrssingTime;
      optList4.value[0].total = todayAverageProcessingTime || 0;
      optList4.value[0].ratio = riseOfProcessingTime || "0%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;

      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
    }
  });
}

const initPieFunc = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: 0, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: 0,
        dataIndex: index,
      });
    });
  });
};

const initPieFunc3D = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: index, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: index,
        dataIndex: index,
      });
    });
  });
};

watch(
  () => state.curNetworkTab,
  () => {
    state.loadingNetwork = true;
    nextTick(() => {
      clearInterval(state.networkTimer);
      state.networkTimer = setInterval(() => {
        handleNetwork(state.curNetworkTab);
      }, 60000);
      // if (state.curNetworkTab == 0) {
      //   state.networkTimer = setInterval(() => {
      //     handleNetwork(state.curNetworkTab);
      //   }, 60000);
      // } else {
      //   state.networkTimer = setInterval(() => {
      //     handleNetwork(state.curNetworkTab);
      //   }, 300000);
      // }
    });
  },
  {
    deep: true,
    immediate: true,
  }
);

watch(
  () => state.curAssetsTab,
  () => {
    console.log("切换tab");
    nextTick(() => {
      initChart();
    });
  },
  {
    deep: true,
    // immediate: true,
  }
);

function initChart() {
  if (state.curAssetsTab == 0) {
    pieRef6.value.initPieFunc();
    pieRef7.value.initPieFunc();
    pieRef10.value.initPieFunc();
    pieRef11.value.initPieFunc();
    pieRef8.value.initPieFunc();
    pieRef9.value.initPieFunc();
    pieRef12.value.initPieFunc();
    pieRef13.value.initPieFunc();
  } else if (state.curAssetsTab == 1) {
    pieRef2.value.initPieFunc();
    pieRef3.value.initPieFunc();
    pieRef4.value.initPieFunc();
    pieRef15.value.initPieFunc();
  }
}

onMounted(async () => {
  proxy.$modal.loading();
  getOrderData();
  handleNetwork(state.curNetworkTab);
  await getData();
  await getAssetsData();
  proxy.$modal.closeLoading();
  initChart();
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000); // 60000
});

onBeforeUnmount(() => {
  window.clearInterval(state.scrolltimer1);
  window.clearInterval(state.scrolltimer2);
  clearInterval(state.networkTimer);
  clearInterval(state.timer2);
});
</script>
    
    <style lang="scss" scoped>
@font-face {
  font-family: "HYYaKuHeiW";
  src: url("@/assets/fontFamily/HYYaKuHeiW.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
:deep(.el-loading-mask) {
  background-color: rgba(0, 0, 0, 0) !important;
}
::-webkit-scrollbar {
  width: 0px; /* 滚动条宽度 */
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #fff;
  padding: 0.5vw 0.2vw;
  font-size: 0.6vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

/* 定义浮动动画 */
@keyframes float {
  0%,
  100% {
    transform: translateY(3px);
  }
  50% {
    transform: translateY(-3px); /* 向上浮动高度 */
  }
}

/* 应用到文字元素 */
.floating-text {
  animation: float 3s linear infinite; /* 3秒循环动画 */
  display: inline-block; /* 确保transform生效 */
}

.unify-main_charts {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  margin-top: -8vw;
  // overflow: hidden;

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;
      background-size: 100% 100%;

      &-subTitle {
        font-size: 0.65vw;
        height: 1.3vw;
        line-height: 1.3vw;
        padding-left: 2vw;
        margin-top: 1vw;
        color: #fff;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
        background-size: 100% 100%;
        text-shadow: 0px 0px 4px #0091ff;
      }
      &-title {
        font-size: 0.85vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1.5vw;
        position: relative;
        font-family: "HYYaKuHeiW";
        // margin-top: 1vw;
        .titleText {
          transform: scale(1, 0.993905) skew(-6.290719deg, 0deg);
          background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
            #ffffff;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent;
        }

        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg.png");
        background-size: 100% 100%;
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.5vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          padding-right: 0.2vw;
          color: #c7d9f9;
        }

        div {
          padding-left: 0.8vw;
        }

        .up {
          color: #2dcd5e;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          color: #fe005d;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 23vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      .battery {
        // border: 1px solid red;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/zcfgl-bg.png");
        background-size: 100% 100%;
        height: 10vw;
        width: 100%;
        text-align: center;
        line-height: 8vw;
        font-family: "ZhengQingKeHuangYouTi";
        font-size: 1.3vw;
        color: #fff;
      }
      .echart-item {
        position: relative;
        .export-btn {
          z-index: 11;
          cursor: pointer;
          position: absolute;
          font-size: 0.55vw;
          right: 0;
          top: 0.3vw;
          width: 3.8vw;
          height: 1.2vw;
          line-height: 1.2vw;
          padding-left: 0.8vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/export-bg.png");
          background-size: 100% 100%;
          color: #fff;
          // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
          // background-size: 100% 100%;
          // text-shadow: 0px 0px 4px #0091ff;
        }
      }
    }

    &-block3 {
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #7fcff4;
        z-index: 11;
        font-family: "微软雅黑";
        &_item {
          cursor: pointer;
          height: 1.4vw;
          padding: 0 1vw;
          line-height: 1.4vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/network-btn.png");
          background-size: 100% 100%;
          &.active {
            color: #fff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/network-btn_checked.png");
            background-size: 100% 100%;
          }
        }
      }
      .echart-item {
        position: relative;
        .export-btn {
          z-index: 11;
          cursor: pointer;
          position: absolute;
          font-size: 0.55vw;
          right: 0;
          top: 0.3vw;
          width: 3.8vw;
          height: 1.2vw;
          line-height: 1.2vw;
          padding-left: 0.8vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/export-bg.png");
          background-size: 100% 100%;
          color: #fff;
        }
      }
    }

    &-block4 {
      // border: 1px solid red;
      margin-top: 0.5vw;
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 45vw;
    .rangeTab {
      position: absolute;
      right: 0;
      top: -0.2vw;
      display: flex;
      font-size: 0.65vw;
      gap: 0 0.3vw;
      padding-top: 1vw;
      color: #b4c0cc;
      z-index: 11;
      &_item {
        cursor: pointer;
        padding: 0.2vw 0.6vw;
        background-color: #3a4356;
        &.active {
          color: #1fc6ff;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
          background-size: 100% 100%;
        }
      }
    }
    &-block1 {
      // border: 1px solid red;
      .echart-item {
        position: relative;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/line-block_bg.png");
        background-size: 100% 100%;
      }
    }

    &-block2 {
      // border: 1px solid red;
      margin: 1vw 0;
      .echart-item {
        position: relative;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/line-block_bg.png");
        background-size: 100% 100%;
      }
    }

    &-block3 {
      color: #fff;
      .echart-item {
        height: 9.85vw;
        flex: 1;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-block-bg.png");
        background-size: 100% 100%;
      }
      .table2 {
        flex: 1;
        height: 9.8vw;
        // border: 1px solid red;
        font-size: 0.6vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-block-bg.png");
        background-size: 100% 100%;

        .table-row {
          display: flex;
          height: 1.2vw;
          line-height: 1.2vw;
          text-align: center;
          // border: 1px solid red;
          margin-bottom: 0.4vw;
          &.data:hover {
            cursor: pointer;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-item_hover.png");
            background-size: 100% 100%;
          }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 8vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .table-data {
          overflow: hidden;
          // border: 1px solid red;
          height: 7.6vw;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -1.6vw;
          }
          .odd,
          .even {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-item_bg.png");
            background-size: 100% 100%;
          }
        }

        .opt-title {
          margin-top: 0.1vw;
          height: 1.3vw;
          line-height: 1.3vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-table_header.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block4 {
      .grid-item {
        display: flex;
        flex-direction: column;

        .block {
          margin-top: 0.5vw;
          height: 6.8vw;
          padding: 0.5vw 0.2vw;

          &-right {
            font-size: 0.6vw !important;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
          }
        }
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 25vw;
    height: 100%;

    &-block1 {
      // border: 1px solid red;
      height: 2vw;
      cursor: pointer;
      display: flex;
      &.left {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/assets-tab1.png");
        background-size: 100% 100%;
      }
      &.right {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/assets-tab3.png");
        background-size: 100% 100%;
      }

      &_left,
      &_right {
        flex: 1;
        height: 2vw;
        // border: 1px solid yellow;
      }
    }

    &-block2 {
      .block-subTitle {
        margin-top: 0;
      }
      &.bg {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/col3-bg.png");
        background-size: 100% 100%;
      }
      .flex {
        gap: 0 0.5vw;
      }
      .block {
        padding: 0vw 0.2vw;
        margin-top: 0.5vw;
      }

      .block2 {
        background: url("@/assets/screen/app/address-bg.png");
        background-size: 100% 100%;
      }

      .address {
        // border: 1px solid red;
        height: 4vw;
        line-height: 4vw;
        font-size: 0.9vw;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          color: #4095e5;
          font-size: 1.1vw;
          letter-spacing: 0.2vw;
        }
      }
    }

    &-mask {
      margin-top: 0.3vw;
      width: 100%;
      height: 22.65vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/col3-mask.png");
      background-size: 100% 100%;
    }

    &-block3 {
      margin-top: 0.5vw;
      padding: 0.7vw 0 0.6vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/network-bg.png");
      background-size: 100% 100%;
      .flex {
        gap: 0;
        margin-bottom: 0.2vw;
      }
      .network {
        position: relative;
        color: #4f5760;
        font-size: 0.8vw;
        line-height: 1.5vw;
        flex: 1;
        padding: 0.5vw 1vw 0.5vw 4vw;
        span {
          font-size: 1vw;
          color: #fff;
          display: inline-block;
          padding-right: 0.2vw;
        }
        &::before {
          position: absolute;
          content: "";
          left: 1vw;
          width: 2.5vw;
          height: 2.5vw;
        }
        &.up::before {
          background: url("@/assets/screen/admin/network_up.png");
          background-size: 100% 100%;
        }

        &.low::before {
          background: url("@/assets/screen/admin/network_low.png");
          background-size: 100% 100%;
        }
      }
    }
  }
}
</style>