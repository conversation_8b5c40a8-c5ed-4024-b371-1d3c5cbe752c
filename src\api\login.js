import request from '@/utils/request'

// 短信验证码检测
export function checkSms(data) {
  return request({
    url: "/sms/check",
    method: "post",
    data,
  });
}

// 发送短信验证码
export function sendSms(data) {
  return request({
    url: "/sms/send",
    method: "post",
    data,
  });
}

// 发送短信验证码
export function sendSms2(data) {
  return request({
    url: "/aliSms/send",
    method: "post",
    data,
  });
}

// 微信扫码登录
export function wxLogin(data) {
  return request({
    url: "/auth/wxLogin",
    method: "post",
    data,
  });
}

// 微信扫码失败登录
export function wxLoginByUnionId(data) {
  return request({
    url: "/auth/bindAndLoginByUnionId",
    method: "post",
    data,
  });
}

// 用户编号登录
export function loginByUserCode(data) {
  return request({
    url: '/auth/loginByUserCode',
    headers: {
      isToken: false
    },
    method: 'post',
    data
  })
}


// 加密登录方法
export function login(data) {
  return request({
    url: '/auth/login',
    headers: {
      isToken: false
    },
    method: 'post',
    data
  })
}
// sso登录方法
export function ssoLogin(data) {
  return request({
    url: '/auth/lunyuLogin',
    headers: {
      isToken: false
    },
    method: 'get',
    params: data  
  })
}

// // 登录方法
// export function login(username, password, code, uuid) {
//   return request({
//     url: '/auth/login',
//     headers: {
//       isToken: false
//     },
//     method: 'post',
//     data: { username, password, code, uuid }
//   })
// }

// 注册方法
export function register(data) {
  return request({
    url: '/auth/register',
    headers: {
      isToken: false
    },
    method: 'post',
    data: data
  })
}

// 刷新方法
export function refreshToken() {
  return request({
    url: '/auth/refresh',
    method: 'post'
  })
}

// 获取用户详细信息
export function getInfo() {
  return request({
    url: '/system/user/getInfo',
    method: 'get'
  })
}

// 退出方法
export function logout() {
  return request({
    url: '/auth/logout',
    method: 'delete'
  })
}

// 获取验证码
export function getCodeImg() {
  return request({
    url: 'auth/captcha/captchaImage',
    headers: {
      isToken: false
    },
    method: 'get',
    timeout: 20000
  })
}