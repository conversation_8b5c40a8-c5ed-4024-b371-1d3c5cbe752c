<template>
  <div class="deviceSwitch" v-loading="loadingOff">
    <div class="deviceSwitch-list">
      <div
        class="deviceSwitch-item"
        v-for="(item, index) in formList"
        :key="index"
      >
        <div style="min-width: 500px">
          <div class="deviceSwitch-header">
            <span>设备开机定时计划</span>
            <div class="flex">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="在下述生效时间点，生效设备在关机状态下自动开机"
                placement="top-start"
              >
                <el-icon :size="16" style="margin-left: 5px"
                  ><QuestionFilled
                /></el-icon>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button
                  type="primary"
                  style="font-size: 20px; margin-left: 10px"
                  icon="Edit"
                  link
                  v-show="!item.isEdit"
                  @click="handleEdit(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-show="(!item.id && item.isEdit) || !!item.id"
            class="deviceSwitch-content"
            :class="{ 'deviceSwitch-content_unedit': !item.isEdit }"
          >
            <div>
              <el-form
                :ref="(el) => setFormRef(el, index)"
                :model="item"
                :label-width="
                  item.isEdit
                    ? item.frequency == 2
                      ? '106px'
                      : '92px'
                    : item.frequency == 2
                    ? '96px'
                    : '82px'
                "
              >
                <el-form-item
                  :label="item.isEdit ? '启动计划：' : '启用状态：'"
                  prop="status"
                >
                  <el-switch
                    v-if="item.isEdit"
                    v-model="item.status"
                    :active-value="0"
                    :inactive-value="1"
                    @change="(val) => handleStatusChange(val, index)"
                  />
                  <span v-else>{{
                    item.status === 0 ? "已启用" : "已禁用"
                  }}</span>
                </el-form-item>
                <el-form-item
                  label="频率："
                  prop="frequency"
                  :rules="{
                    required: item.isEdit,
                    message: '请选择频率',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-if="item.isEdit"
                    v-model="item.frequency"
                    style="width: 200px"
                    @change="handleChangeFrequency(index)"
                    :disabled="!!item.id && item.status === 1"
                  >
                    <el-option label="每天一次" :value="1" />
                    <el-option label="每天两次" :value="2" />
                    <el-option label="每周一次" :value="3"
                  /></el-select>
                  <span v-else>{{ frequencyObj[item.frequency] || "-" }}</span>
                </el-form-item>
                <el-form-item
                  v-if="item.frequency == 3"
                  label="生效日期："
                  prop="effectiveDate"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveDate(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-if="item.isEdit"
                    v-model="item.effectiveDate"
                    style="width: 200px"
                    :disabled="!!item.id && item.status === 1"
                  >
                    <el-option label="星期一" :value="1" />
                    <el-option label="星期二" :value="2" />
                    <el-option label="星期三" :value="3" />
                    <el-option label="星期四" :value="4" />
                    <el-option label="星期五" :value="5" />
                    <el-option label="星期六" :value="6" />
                    <el-option label="星期日" :value="0"
                  /></el-select>
                  <span v-else>{{ weekObj[item.effectiveDate] || "-" }}</span>
                </el-form-item>
                <el-form-item
                  :label="item.frequency == 2 ? '生效时间Ⅰ：' : '生效时间：'"
                  prop="oneEffectiveTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveTime(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="item.isEdit"
                    v-model="item.oneEffectiveTime"
                    placeholder="请选择时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 200px"
                    @change="validChangeEffectiveTime(index)"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.oneEffectiveTime || "-" }}</span>
                </el-form-item>
                <el-form-item
                  v-if="item.frequency == 2"
                  label="生效时间Ⅱ："
                  prop="twoEffectiveTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveTime(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="item.isEdit"
                    v-model="item.twoEffectiveTime"
                    placeholder="请选择时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 200px"
                    @change="validChangeEffectiveTime(index)"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.twoEffectiveTime || "-" }}</span>
                </el-form-item>
                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validSelectedDevices(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="item.selectedDevices"
                    :isEdit="item.isEdit"
                    :device-codes="item.deviceCodes"
                    :disabled="!!item.id && item.status === 1"
                    @valid="handleValid(index)"
                    @change="(val) => handleChangeSelectedDevices(val, index)"
                  />
                </el-form-item>
              </el-form>
              <div class="deviceSwitch-footer" v-if="item.isEdit">
                <el-button type="primary" @click="submitForm(index)" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(index)">取消</el-button>
              </div>
            </div>
            <div v-show="!!item.id && !item.isEdit">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  @click="handleDel(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="deviceSwitch-list" style="margin-top: 15px">
      <div
        class="deviceSwitch-item"
        v-for="(item, index) in formList2"
        :key="index"
      >
        <div style="min-width: 500px">
          <div class="deviceSwitch-header">
            <span>设备关机定时计划</span>
            <div class="flex">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="在下述生效时间点，生效设备在开机状态下自动关机"
                placement="top-start"
              >
                <el-icon :size="16" style="margin-left: 5px"
                  ><QuestionFilled
                /></el-icon>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button
                  type="primary"
                  style="font-size: 20px; margin-left: 10px"
                  icon="Edit"
                  link
                  v-show="!item.isEdit"
                  @click="handleEdit(index, 1)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-show="(!item.id && item.isEdit) || !!item.id"
            class="deviceSwitch-content"
            :class="{ 'deviceSwitch-content_unedit': !item.isEdit }"
          >
            <div>
              <el-form
                :ref="(el) => setFormRef(el, index, 1)"
                :model="item"
                :label-width="
                  item.isEdit
                    ? item.frequency == 2
                      ? '106px'
                      : '92px'
                    : item.frequency == 2
                    ? '96px'
                    : '82px'
                "
              >
                <el-form-item
                  :label="item.isEdit ? '启动计划：' : '启用状态：'"
                  prop="status"
                >
                  <el-switch
                    v-if="item.isEdit"
                    v-model="item.status"
                    :active-value="0"
                    :inactive-value="1"
                    @change="(val) => handleStatusChange(val, index, 1)"
                  />
                  <span v-else>{{
                    item.status === 0 ? "已启用" : "已禁用"
                  }}</span>
                </el-form-item>
                <el-form-item
                  label="频率："
                  prop="frequency"
                  :rules="{
                    required: item.isEdit,
                    message: '请选择频率',
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-if="item.isEdit"
                    v-model="item.frequency"
                    style="width: 200px"
                    @change="handleChangeFrequency(index, 1)"
                    :disabled="!!item.id && item.status === 1"
                  >
                    <el-option label="每天一次" :value="1" />
                    <el-option label="每天两次" :value="2" />
                    <el-option label="每周一次" :value="3" />
                  </el-select>
                  <span v-else>{{ frequencyObj[item.frequency] || "-" }}</span>
                </el-form-item>
                <el-form-item
                  v-if="item.frequency == 3"
                  label="生效日期："
                  prop="effectiveDate"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveDate(rule, value, callback, index, 1),
                    trigger: 'change',
                  }"
                >
                  <el-select
                    v-if="item.isEdit"
                    v-model="item.effectiveDate"
                    style="width: 200px"
                    :disabled="!!item.id && item.status === 1"
                  >
                    <el-option label="星期一" :value="1" />
                    <el-option label="星期二" :value="2" />
                    <el-option label="星期三" :value="3" />
                    <el-option label="星期四" :value="4" />
                    <el-option label="星期五" :value="5" />
                    <el-option label="星期六" :value="6" />
                    <el-option label="星期日" :value="0" />
                  </el-select>
                  <span v-else>{{ weekObj[item.effectiveDate] || "-" }}</span>
                </el-form-item>
                <el-form-item
                  :label="item.frequency == 2 ? '生效时间Ⅰ：' : '生效时间：'"
                  prop="oneEffectiveTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveTime(rule, value, callback, index, 1),
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="item.isEdit"
                    v-model="item.oneEffectiveTime"
                    placeholder="请选择时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 200px"
                    @change="validChangeEffectiveTime(index, 1)"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.oneEffectiveTime || "-" }}</span>
                </el-form-item>
                <el-form-item
                  v-if="item.frequency == 2"
                  label="生效时间Ⅱ："
                  prop="twoEffectiveTime"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validEffectiveTime(rule, value, callback, index, 1),
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="item.isEdit"
                    v-model="item.twoEffectiveTime"
                    placeholder="请选择时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    style="width: 200px"
                    @change="validChangeEffectiveTime(index, 1)"
                    :disabled="!!item.id && item.status === 1"
                  />
                  <span v-else>{{ item.twoEffectiveTime || "-" }}</span>
                </el-form-item>
                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validSelectedDevices(rule, value, callback, index, 1),
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="item.selectedDevices"
                    :isEdit="item.isEdit"
                    :device-codes="item.deviceCodes"
                    :disabled="!!item.id && item.status === 1"
                    @valid="handleValid(index, 1)"
                    @change="
                      (val) => handleChangeSelectedDevices(val, index, 1)
                    "
                  />
                </el-form-item>
              </el-form>
              <div class="deviceSwitch-footer" v-if="item.isEdit">
                <el-button
                  type="primary"
                  @click="submitForm(index, 1)"
                  v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(index, 1)">取消</el-button>
              </div>
            </div>
            <div v-show="!item.isEdit && !!item.id">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  @click="handleDel(index, 1)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import deviceSel from "../deviceSel.vue";
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { listSwitchMachine, addSwitchMachine } from "@/api/deviceControl";
import { deleteSwitchMachine } from "@/api/deviceControl/plan";
import { timeFormat } from "@/utils";

const { proxy } = getCurrentInstance();

const formRefs = ref({});
const formRefs2 = ref({});
// 设置表单引用的函数
const setFormRef = (el, index, type) => {
  if (el) {
    !!type ? (formRefs2.value[index] = el) : (formRefs.value[index] = el);
  } else {
    delete !!type ? formRefs2.value[index] : formRefs.value[index];
  }
};

const state = reactive({
  initFormList: [],
  initFormList2: [],
  formList: [],
  formList2: [],
  weekObj: {
    1: "星期一",
    2: "星期二",
    3: "星期三",
    4: "星期四",
    5: "星期五",
    6: "星期六",
    0: "星期日",
  },
  frequencyObj: {
    1: "每天一次",
    2: "每天两次",
    3: "每周一次",
  },
  loadingOff: false,
});
const {
  initFormList,
  initFormList2,
  loadingOff,
  formList,
  formList2,
  frequencyObj,
  weekObj,
} = toRefs(state);

onMounted(() => getData());

const handleValid = (index, type) => {
  !!type
    ? formRefs2.value[index].validateField(`selectedDevices`)
    : formRefs.value[index].validateField(`selectedDevices`);
};

const handleChangeSelectedDevices = (val, index, type) => {
  state[!!type ? "formList2" : "formList"][index].selectedDevices =
    val?.join(",");
};

const getData = () => {
  loadingOff.value = true;
  listSwitchMachine()
    .then((res) => {
      console.log(res, "获取开关机列表数据");
      if (res.code === 200 && res.data) {
        // 处理数据
        const { list } = res.data;
        if (list && list.length > 0) {
          const idx1 = list.findIndex((_) => _.type === 1);
          const idx2 = list.findIndex((_) => _.type === 0);
          if (idx1 != -1) {
            let item = list[idx1];
            state.formList[0] = {
              ...item,
              id: item.id || item.type + "",
              isEdit: false,
              selectedDevices: item.deviceCodes?.join(","),
            };
          } else {
            resetForm(0);
          }
          if (idx2 != -1) {
            let item = list[idx2];
            state.formList2[0] = {
              ...item,
              id: item.id || item.type + "",
              isEdit: false,
              selectedDevices: item.deviceCodes?.join(","),
            };
          } else {
            resetForm(0, 1);
          }
        } else {
          resetForm(0);
          resetForm(0, 1);
        }

        initFormList.value = JSON.parse(JSON.stringify(formList.value));
        initFormList2.value = JSON.parse(JSON.stringify(formList2.value));
      } else {
        resetForm(0);
        resetForm(0, 1);
      }
    })
    .finally(() => {
      loadingOff.value = false;
    });
};

// 校验生效日期
const validEffectiveDate = (rule, value, callback, index, type) => {
  const obj = state[!!type ? "formList2" : "formList"][index];
  // 如果状态为禁用，跳过验证
  if (obj.status === 1 && !!obj.id) {
    callback();
    return;
  }
  if (value === "") {
    callback(new Error("请选择生效日期"));
    return;
  }

  callback();
};

// 校验生效时间
const validEffectiveTime = (rule, value, callback, index, type) => {
  const obj = state[!!type ? "formList2" : "formList"][index];
  // 如果状态为禁用，跳过验证
  if (obj.status === 1 && !!obj.id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请选择生效时间"));
  } else if (
    obj.frequency == 2 &&
    obj.oneEffectiveTime == obj.twoEffectiveTime
  ) {
    callback(new Error("注：两次生效时间相同，请重新选择"));
  } else {
    callback();
  }
};

// 校验生效设备
const validSelectedDevices = (rule, value, callback, index, type) => {
  const obj = state[!!type ? "formList2" : "formList"][index];
  // 如果状态为禁用，跳过验证
  if (obj.status === 1 && !!obj.id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请选择生效设备"));
  } else {
    callback();
  }
};

const validChangeEffectiveTime = (index, type) => {
  const obj = state[!!type ? "formList2" : "formList"][index];
  // 如果状态为禁用，跳过验证
  if (obj.status === 1 && !!obj.id) {
    callback();
    return;
  }
  if (state[!!type ? "formList2" : "formList"][index].frequency == 2) {
    if (!!type ? !formRefs2.value[index] : !formRefs.value[index]) return;

    !!type
      ? formRefs2.value[index].validateField(`oneEffectiveTime`)
      : formRefs.value[index].validateField(`oneEffectiveTime`);
    !!type
      ? formRefs2.value[index].validateField(`twoEffectiveTime`)
      : formRefs.value[index].validateField(`twoEffectiveTime`);
  }
};

const handleChangeFrequency = (index, type) => {
  state[!!type ? "formList2" : "formList"][index].effectiveDate = "";
  state[!!type ? "formList2" : "formList"][index].oneEffectiveTime = "00:00:00";
  state[!!type ? "formList2" : "formList"][index].twoEffectiveTime = "00:00:00";
};

const handleStatusChange = (val, index, type) => {
  const obj = state[!!type ? "formList2" : "formList"][index];
  obj.isEdit = true;
  if (val === 1)
    !!type
      ? formRefs2.value[index]?.clearValidate()
      : formRefs.value[index]?.clearValidate();
};

const handleEdit = (index, type) => {
  state[!!type ? "formList2" : "formList"][index].isEdit = true;
  if (!state[!!type ? "formList2" : "formList"][index].id)
    state[!!type ? "formList2" : "formList"][index].status = 0;
  nextTick(() => {
    !!type
      ? formRefs2.value[index]?.clearValidate()
      : formRefs.value[index]?.clearValidate();
  });
};

const handleDel = (index, type) => {
  proxy.$modal
    .confirm("删除后已设置的设备重启后失效，是否确认删除？")
    .then((res) => {
      let submitData = {
        type: state[!!type ? "formList2" : "formList"][index].type,
        id: state[!!type ? "formList2" : "formList"][index].id,
      };
      console.log("提交数据:", submitData);
      // 调用接口
      loadingOff.value = true;
      deleteSwitchMachine(submitData)
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            resetForm(index, type);
            // state[!!type ? "formList2" : "formList"][index].isEdit = false;
            proxy.$modal.msgSuccess("操作成功");
            // getData();
          }
        })
        .catch((err) => {
          // proxy.$modal.msgError("操作失败");
        })
        .finally(() => (loadingOff.value = false));
    });
};

const handleCancel = (index, type) => {
  if (!!type ? formRefs2.value[index] : formRefs.value[index]) {
    !!type
      ? formRefs2.value[index].resetFields()
      : formRefs.value[index].resetFields();
  }
  // 清空展开状态
  state[!!type ? "formList2" : "formList"][index].isEdit = false;
  console.log(!state[!!type ? "formList2" : "formList"][index].id);
  if (!state[!!type ? "formList2" : "formList"][index].id) {
    resetForm(index, type);
  } else {
    getData();
  }
};

const submitForm = (index, type) => {
  if (
    state[!!type ? "formList2" : "formList"][index].status === 1 &&
    !state[!!type ? "formList2" : "formList"][index].id
  ) {
    proxy.$modal.alert("请先开启启动计划再保存");
    return;
  }
  const el = !!type ? formRefs2.value[index] : formRefs.value[index];
  console.log(el);
  if (!el) return;
  nextTick(() => {
    el?.validate((valid) => {
      if (valid) {
        let data = !!type ? formList2.value[index] : formList.value[index];
        data.status === 1 && !!data.id
          ? (data = !!type
              ? initFormList2.value[index]
              : initFormList.value[index])
          : "";
        data.frequency !== 2 ? delete data.twoEffectiveTime : "";
        let submitData = {
          type: 2,
          info: [
            {
              ...data,
              status: !!type
                ? formList2.value[index].status
                : formList.value[index].status,
              deviceCodes: data.selectedDevices?.split(",") || [],
            },
          ],
        };
        console.log("提交数据:", submitData);
        // 调用接口
        loadingOff.value = true;
        addSwitchMachine(submitData)
          .then((res) => {
            console.log(res);
            if (res.code === 200) {
              // state[!!type ? "formList2" : "formList"][index].isEdit = false;
              proxy.$modal.msgSuccess("操作成功");
              getData();
            }
          })
          .catch((err) => {
            // proxy.$modal.msgError("操作失败");
          })
          .finally(() => (loadingOff.value = false));
      }
    });
  });
};

const resetForm = (index, type) => {
  state[!!type ? "formList2" : "formList"][index] = {
    isEdit: null,
    id: "",
    type: type === 1 ? 0 : 1,
    status: 1,
    frequency: 1,
    oneEffectiveTime: "00:00:00",
    twoEffectiveTime: "",
    effectiveDate: "",
    selectedDevices: "",
    deviceCodes: [],
  };
  console.log(state.formList2[index]);
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  gap: 0 5px;
}
.deviceSwitch {
  font-size: 14px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 15px 0;
  }

  &-add {
    border: 1px solid #e5e5e5;
    border-top: none;
    padding: 10px;
    color: rgb(152, 152, 152);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    &:hover {
      color: #7e96ae;
      background-color: #f8faff;
    }
  }

  &-item {
    border: 1px solid #e5e5e5;
  }

  &-content {
    padding: 10px 20px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    &_unedit {
      padding-bottom: 10px;
      :deep(.el-form-item--default) {
        margin-bottom: 0px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
  }

  &-footer {
    min-width: 500px;
    text-align: center;
  }
}
</style>