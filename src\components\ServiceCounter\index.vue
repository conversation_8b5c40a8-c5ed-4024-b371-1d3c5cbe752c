<template>
  <div class="counter">
    <div class="counter-list">
      <div
        class="counter-list_item"
        v-for="(item, index) in tabList"
        :key="index"
        @click="handleTab(item)"
      >
        <el-image :src="item.icon" style="width: 3vw; height: 3vw"></el-image>
        {{ item.title }}
      </div>
    </div>

    <el-dialog
      :title="title"
      v-model="dialogVisible"
      :width="title == '电话报障' ? 800 : 600"
    >
      <div v-if="title == '电话报障'" class="repair">
        <div class="repair-search">
          <!-- <el-input
                        v-model="queryParams.name"
                        placeholder="请输入机构名称查询"
                        clearable
                        style="width: 200px;margin-right: 12px;"
                        @keyup.enter="handleQuery"
                    />
                    <el-input
                        v-model="queryParams.number"
                        placeholder="请输入机构编号查询"
                        clearable
                        style="width: 200px;margin-right: 12px;"
                        @keyup.enter="handleQuery"
                    />
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button> -->
        </div>
        <el-table :data="tableList" border>
          <el-table-column label="单位编号" prop="number" />
          <el-table-column label="承建单位" prop="name" />
          <el-table-column label="报障电话">
            <template #default="scope">
              {{ scope.row.phone }}
              <span
                style="
                  display: inline-block;
                  padding-left: 20px;
                  color: #4095e5;
                  cursor: pointer;
                "
                @click="handleCopy(scope.row)"
                >一键复制</span
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="tableList.length > 0"
          :total="tableList.length"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="handleQuery"
        />
      </div>
      <div v-if="title == '小程序报障'" class="minipro">
        请通过扫描二维码登录小程序，进行报障
        <img
          style="width: 10vw; height: 10vw"
          src="@/assets/images/minicode.jpg"
        />
      </div>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRouter } from "vue-router";
import { ref, watch } from "vue";
import { copyContent } from "@/utils";

const router = useRouter();
const props = defineProps({
  isWork: {
    type: Boolean,
    default: false,
  },
  isShow: {
    type: Boolean,
    default: true,
  },
});
const title = ref("电话报障");
const total = ref(1);
const dialogVisible = ref(false);
const queryParams = ref({
  current: 1,
  size: 5,
  name: "",
  number: "",
});
const tabList = ref([
  {
    title: "电话报障",
    icon: new URL("@/assets/icons/repair_phone.png", import.meta.url).href,
  },
  {
    title: "小程序报障",
    icon: new URL("@/assets/icons/repair_minipro.png", import.meta.url).href,
  },
  {
    title: "电话录音查询",
    icon: new URL("@/assets/icons/recording.png", import.meta.url).href,
    path: `/recording${props.isWork ? "?type=1" : ""}`,
  },
  {
    title: "满意度评价",
    icon: new URL("@/assets/icons/satisfaction.png", import.meta.url).href,
    path: `/satisfaction${props.isWork ? "?type=1" : ""}`,
  },
  {
    title: "工作台回访",
    icon: new URL("@/assets/icons/visit.png", import.meta.url).href,
    path: `/visit${props.isWork ? "?type=1" : ""}`,
  },
]);
const tableList_all = ref([
  { number: "01", name: "广州数据集团有限公司", phone: "16603056040" },
  /* { number: '02', name: '白云区第一中学', phone: '440-254-00026' },
    { number: '03', name: '番禺区第三中学', phone: '440-254-00027' },   */
]);
const tableList = ref([]);

watch(
  () => props.isShow,
  (val) => {
    console.log(val);
    if (!val) {
      tabList.value = [
        {
          title: "电话报障",
          icon: new URL("@/assets/icons/repair_phone.png", import.meta.url)
            .href,
        },
        {
          title: "小程序报障",
          icon: new URL("@/assets/icons/repair_minipro.png", import.meta.url)
            .href,
        },
      ];
    } else {
      tabList.value = [
        {
          title: "电话报障",
          icon: new URL("@/assets/icons/repair_phone.png", import.meta.url)
            .href,
        },
        {
          title: "小程序报障",
          icon: new URL("@/assets/icons/repair_minipro.png", import.meta.url)
            .href,
        },
        {
          title: "电话录音查询",
          icon: new URL("@/assets/icons/recording.png", import.meta.url).href,
          path: `/recording${props.isWork ? "?type=1" : ""}`,
        },
        {
          title: "满意度评价",
          icon: new URL("@/assets/icons/satisfaction.png", import.meta.url)
            .href,
          path: `/satisfaction${props.isWork ? "?type=1" : ""}`,
        },
        {
          title: "工作台回访",
          icon: new URL("@/assets/icons/visit.png", import.meta.url).href,
          path: `/visit${props.isWork ? "?type=1" : ""}`,
        },
      ];
    }
  },
  {
    immediate: true,
  }
);

function handleCopy(item) {
  let text = item.phone.split("-").join("");
  copyContent(text);
}

function handleTab(item) {
  if (item.path) {
    router.push(item.path);
  } else {
    if (item.title == "电话报障") {
      resetQuery();
    }
    title.value = item.title;
    dialogVisible.value = true;
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  const { number, name } = queryParams.value;
  if (number + name == "") {
    tableList.value = JSON.parse(JSON.stringify(tableList_all.value));
  } else {
    const arr1 = tableList_all.value.filter(
      (_) => _.number.indexOf(number) != -1
    );
    const arr2 = arr1.filter((_) => _.name.indexOf(name) != -1);
    if (arr2.length > 0) {
      tableList.value = arr2;
    } else tableList.value = [];
  }
}

function resetQuery() {
  queryParams.value = {
    current: 1,
    size: 5,
    name: "",
    number: "",
  };
  handleQuery();
}
</script>

<style lang="scss" scoped>
.counter {
  // border: 1px solid red;
  &-list {
    display: flex;
    flex-wrap: wrap;
    gap: 2vw;
    &_item {
      border: 1px solid #ccc;
      width: 31%;
      font-size: 1.5vw;
      display: flex;
      justify-content: space-between;
      padding: 2vw;
      align-items: center;
      border-radius: 0.4vw;
      cursor: pointer;
    }
  }
  .repair {
    &-search {
      display: flex;
      margin-bottom: 20px;
    }
  }
  .minipro {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}
</style>
