export default {
  created(el, binding, vnode, prevVnode) {
    // console.log(vnode, prevVnode);

    el.addEventListener("click", (e) => customClick(e, binding, el));
  },
  // 离开一定要销毁卸载
  unmounted(el, binding, vnode) {
    // console.log(binding, vnode);
    el.removeEventListener("click", customClick);
  },
};

const customClick = (e, binding, el) => {
  //阻止事件冒泡
  e.stopPropagation();
  if (!el.disabled) {
    // 判断条件
    el.disabled = true;
    setTimeout(() => {
      el.disabled = false;
    }, binding.arg || 1500);
    // 正常触发点击事件
    // binding.value();
  } else {
    // 已经成功拦截点击事件
    console.log("已经成功拦截点击事件");
  }
};
