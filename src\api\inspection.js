// api/inspection.js
import request from "@/utils/request";

// 一键通知
export function inspectionNotice(params) {
  return request({
    url: `/system/schoolInspectionPersonnel/notice`, // 实际导出接口地址
    method: "get",
    params
  });
}

// 导出巡检记录
export function inspectionRecordExport(id) {
  return request({
    url: `/system/schoolInspectionPersonnel/export?patrolPlanId=${id}`, // 实际导出接口地址
    method: "post",
    responseType: "blob", // 必须设置响应类型为 blob
  });
}
