<template>
  <div class="locateSel">
    <div class="monitor-position">
      <div ref="topDivRef">
        <el-input
          v-model="position"
          placeholder="请输入安装位置搜索"
          clearable
          @clear="getPositionTreeList"
          @input="inputDebounce"
        />
        <div class="monitor-title">
          安装位置筛选
          <div class="clear" @click="resetQuery">清空</div>
        </div>
      </div>

      <el-scrollbar
        class="scrollbar"
        :style="{ height: props.maxHeight }"
        v-loading="loading"
      >
        <el-tree
          style="width: 100%"
          :data="positionTreeList"
          show-checkbox
          node-key="id"
          expand-on-click-node
          default-expand-all
          check-strictly
          :indent="10"
          :props="positionProps"
          @check="handleNodeCheck"
          ref="treeRef"
        />
      </el-scrollbar>
    </div>
  </div>
</template>
<script setup>
import { getPositionTree } from "@/api/mediaTeach/position";
import {
  treeToArray,
  extractPropertyFromTree,
  treeFindPath,
  timeFormat,
  sendPointRequest,
} from "@/utils";
import useUserStore from "@/store/modules/user";
import { nextTick } from "process";

const emits = defineEmits(["handleQuery", "getPosition"]);
const props = defineProps({
  maxHeight: {
    type: String,
    default: "calc(100vh - 268px)",
  },
  isTeacher: {
    type: Number,
    default: 0,
  },
});

const selectedKeys = ref([]);
const originKeys = ref([]);
const loading = ref(false);
const topDivRef = ref(null);
const position = ref("");
const treeRef = ref(null);
const positionTreeList = ref([]);
const positionNodeList = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};

const resetQuery = () => {
  treeRef.value.setCheckedKeys([]); // 清空之前的勾选
  selectedKeys.value = [];
  position.value = "";
  nextTick(() => {
    getPositionTreeList();
    emits("handleQuery", { _checkList: treeRef.value.getCheckedKeys() });
  });
};

const queryPos = () => {
  getPositionTreeList();
  nextTick(() => {
    // treeRef.value.setCheckedKeys([]); // 每次筛选都清空之前的勾选
    // console.log(treeRef.value.getCheckedKeys());
    emits("handleQuery", { _checkList: treeRef.value.getCheckedKeys() });
  });
};

const filterNode = (value, data) => {
  if (!value) return true;
  return data.name.includes(value);
};

const inputDebounce = debounce(getPositionTreeList, 300);

async function getPositionTreeList() {
  console.log(useUserStore());
  loading.value = true;
  // treeRef.value.setCheckedKeys([]); // 每次筛选都清空之前的勾选
  let obj = {
    name: position.value,
  };
  if (props.isTeacher === 1) {
    obj.isTeacher = 1;
    obj.isAdmin = useUserStore().roles?.join(",").includes("maintain") ? 1 : 0;
  }
  !!position.value ? (obj.children = 1) : delete obj.children;
  try {
    await getPositionTree(obj).then((response) => {
      console.log("搜索的位置树结果", response, "传参", obj);
      let tree2 = JSON.parse(JSON.stringify(response.data));
      positionTreeList.value = response.data;
      positionNodeList.value = addAttr2(tree2);
      positionList.value = treeToArray(response.data);
      treeRef.value.setCheckedKeys(selectedKeys.value);
      nextTick(() => {
        originKeys.value = treeRef.value.getCheckedKeys();
        // console.log("originKeys", originKeys.value, selectedKeys.value);
      });

      // console.log("positionNodeList", positionNodeList.value);
      emits("getPosition", {
        positionNodeList: positionNodeList.value,
        positionList: positionList.value,
      });
      // emits("handleQuery", { _checkList: treeRef.value.getCheckedKeys() });

      // console.log('positionList ==>', positionList.value)
    });
  } catch (e) {
    console.log(e);
  }
  loading.value = false;
}

function addAttr2(data, num = 0) {
  num++;
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = num != 3 || data[j].status == 1;
    // console.log(num, data[j]);
    if (data[j].children.length > 0) {
      addAttr2(data[j].children, num);
    }
  }
  return data;
}

// 处理节点勾选事件
const handleNodeCheck = (node, checkedStatus) => {
  // console.log(node, checkedStatus);
  // 获取当前节点的选中状态
  const isChecked = treeRef.value.getCheckedKeys().includes(node.id);

  // 当父节点被勾选时，递归勾选所有子节点
  if (isChecked && node.children) {
    checkChildren(node, true);
  }

  let checkedKeys = treeRef.value.getCheckedKeys();
  // console.log(checkedKeys, originKeys.value, "checkedKeys");

  const addArr = checkedKeys.filter((_) => !originKeys.value.includes(_));
  const delArr = originKeys.value.filter((_) => !checkedKeys.includes(_));

  let newSelected = selectedKeys.value.filter((_) => !delArr.includes(_));
  newSelected = newSelected.concat(addArr);

  selectedKeys.value = newSelected;
  nextTick(() => (originKeys.value = treeRef.value.getCheckedKeys()));

  // console.log(addArr, delArr, newSelected, "新增集合、删除集合、合成集合");

  emits("handleQuery", { _checkList: selectedKeys.value });
  // emits("handleQuery", { _checkList: treeRef.value.getCheckedKeys() });
  // 注意：子节点取消勾选时不需特殊处理，父节点会自动保持勾选
};

// 递归勾选所有子节点
const checkChildren = (node, checked) => {
  if (node.children) {
    node.children.forEach((child) => {
      treeRef.value.setChecked(child.id, checked); // 勾选当前子节点
      if (child.children) checkChildren(child, checked); // 递归处理孙节点
    });
  }
};

onMounted(() => {
  getPositionTreeList();
});

defineExpose({
  topDivRef,
  resetQuery,
});
</script>
<style lang="scss" scoped>
.locateSel {
  width: 100%;
  min-width: 200px;
  height: 100%;
  //   border: 1px solid red;
  .scrollbar {
    // height: calc(100vh - 268px);
    // height: 100%;
    border: 1px solid #dcdfe6;
    padding: 5px 0;
    :deep(.el-tree-node__content) {
      // padding-left: 10px!important;
      height: auto !important;
      white-space: pre-wrap;
    }
  }
  :deep(.el-input__wrapper) {
    border-radius: 0;
  }
  .monitor-position {
    width: 100%;
    height: 100%;
  }
  .monitor-title {
    font-size: 15px;
    padding: 10px;
    // background-color: #4095e5;
    // color: #fff;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .clear {
      font-size: 10px;
      cursor: pointer;
      background-color: #4095e5;
      color: #fff;
      padding: 2px 10px;
      border-radius: 3px;
    }
  }
}
</style>