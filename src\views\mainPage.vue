<template>
  <div class="schoolmain">
    <div class="item one">
      <div class="opt opt1">
        设备性能配置
        <div>
          <img v-if="deviceRun[1].value === '异常'" src="@/assets/images/alarm.gif" />
          <el-select
            v-model="deviceId"
            class="deviceOpts"
            placeholder="请选择设备"
            @change="handleChange"
          >
            <el-option
              v-for="item in deviceList"
              :key="item.deviceId"
              :label="item.deviceName"
              :value="item.deviceId"
            />
          </el-select>
        </div>
      </div>
      <div class="opt opt2">
        <div class="opt2-item" v-for="(item, index) in deviceConfig" :key="index">
          <el-icon size="2vw" :color="configIcon[index].color">
            <component :is="configIcon[index].name"></component>
          </el-icon>
          <div style="text-align: center">
            <span
              class="adaptFontSize"
              >{{ item.value }}</span
            ><br />
            {{ item.label }}
          </div>
        </div>
      </div>
      <div class="opt opt3">
        <div>设备运行状况</div>
        校园网络质量总览
      </div>
      <div class="opt opt4">
        <div class="opt4-item" v-for="(item, index) in deviceRun" :key="index">
          {{ item.value }}
          <div>
            <el-icon size="1vw" :color="runIcon[index].color">
              <component :is="runIcon[index].name"></component>
            </el-icon>
            {{ item.label }}
          </div>
        </div>
        <div class="opt5-item" v-for="(item, index) in schoolNet" :key="index">
          {{ item.label }}
          <div>{{ item.value }}</div>
        </div>
      </div>
    </div>
    <div class="item two">
      <div
        v-if="sbsyscOption.hasData"
        class="sbsysc"
        @click="
          getList2(queryParams2);
          open2 = true;
        "
      >
        详情
      </div>
      <Echarts
        v-if="sbsyscOption.hasData"
        @setFontSize="setFontSize"
        id="sbsysc"
        width="100%"
        height="100%"
        :fullOptions="sbsyscOption"
        :loading="false"
      />
      <div v-if="!sbsyscOption.hasData" class="noData sbsyscBlock">暂无数据</div>
    </div>
    <div class="item three">
      <div
        v-if="sbgzlOption.hasData"
        class="sbgzl"
        @click="
          taskType === '正常'
            ? (() => {
                getList2(queryParams1);
                open1 = true;
              })()
            : (() => {
                getList(queryParams4, 0);
                open1 = true;
              })()
        "
      >
        详情
      </div>
      <Echarts
        v-if="sbgzlOption.hasData"
        @setFontSize="setFontSize"
        id="sbgzl"
        width="100%"
        height="100%"
        :fullOptions="sbgzlOption"
        :loading="false"
      />
      <div v-if="!sbgzlOption.hasData" class="noData sbgzlBlock">暂无数据</div>
    </div>
    <div class="item">
      <Echarts
        v-if="gzcxsjOption.hasData"
        @setFontSize="setFontSize"
        @getClickIdx="getClickIdx"
        id="gzcxsj"
        width="100%"
        height="100%"
        :fullOptions="gzcxsjOption"
        :loading="false"
      />
      <div v-if="!gzcxsjOption.hasData" class="noData gzcxsjBlock">暂无数据</div>
    </div>
    <div class="item five">
      <Echarts
        v-if="gzcstjOption.hasData"
        @setFontSize="setFontSize"
        id="gzcstj"
        width="100%"
        height="100%"
        :fullOptions="gzcstjOption"
        :loading="false"
      />
      <div v-if="!gzcstjOption.hasData" class="noData gzcstjBlock">暂无数据</div>
    </div>
    <div class="item">
      <Echarts
        v-if="gzcstopOption.hasData"
        @setFontSize="setFontSize"
        id="cxsjtop"
        width="50%"
        height="100%"
        style="float: left"
        :fullOptions="cxsjtopOption"
        :loading="false"
      />
      <Echarts
        v-if="gzcstopOption.hasData"
        @setFontSize="setFontSize"
        id="gzcstop"
        width="50%"
        height="100%"
        style="float: left"
        :fullOptions="gzcstopOption"
        :loading="false"
      />
      <div v-if="!gzcstopOption.hasData" class="noData cxsjtopBlock" style="width: 50%;">暂无数据</div>
      <div v-if="!gzcstopOption.hasData" class="noData gzcstopBlock" style="width: 50%;">暂无数据</div>
    </div>
    <div class="item">
      <Echarts
        v-if="ydgzcsOption.hasData"
        @setFontSize="setFontSize"
        id="ydgzcs"
        width="100%"
        height="100%"
        :fullOptions="ydgzcsOption"
        :loading="false"
      />
      <div v-if="!ydgzcsOption.hasData" class="noData ydgzcsBlock">暂无数据</div>
    </div>
    <el-dialog v-model="open1" title="学校设备故障详情" top="5vh" width="1000px">
      <div class="task">
        <el-radio-group
          v-model="taskType"
          size="large"
          style="margin-bottom: 10px"
          @change="handleRadio"
        >
          <el-radio-button label="维修中" />
          <el-radio-button label="正常" />
        </el-radio-group>
        <el-table
          v-if="taskType === '维修中'"
          v-loading="loading2"
          :data="tableData3"
          max-height="500px"
          border
        >
          <el-table-column label="状态" align="center" minWidth="120px">
            <template #default="scope">
              <el-tag
                v-if="statusList[scope.row.deviceStatus]"
                effect="dark"
                :type="statusList[scope.row.deviceStatus].type"
                >{{ statusList[scope.row.deviceStatus].label }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="故障照片" align="center" minWidth="120px">
            <template #default="scope">
              <el-image
                style="width: 50px; height: 30px; display: block; margin: 0 auto"
                :src="scope.row.url"
                fit="cover"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="故障描述"
            align="center"
            minWidth="120px"
            prop="remark"
          />
          <el-table-column
            label="设备编码"
            align="center"
            minWidth="120px"
            prop="deviceCode"
          />
          <el-table-column
            label="设备名称"
            align="center"
            minWidth="120px"
            prop="deviceName"
          />
          <el-table-column
            label="设备类型"
            align="center"
            minWidth="120px"
            prop="deviceType"
          />
          <el-table-column
            label="规格型号"
            align="center"
            minWidth="120px"
            prop="model"
          />
          <el-table-column
            label="维修负责人"
            align="center"
            minWidth="120px"
            prop="nickName"
          />
          <el-table-column
            label="维修负责人账号"
            align="center"
            minWidth="120px"
            prop="userName"
          />
          <el-table-column
            label="入库时间"
            align="center"
            minWidth="120px"
            prop="putTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.putTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
        </el-table>
        <el-table
          v-if="taskType === '正常'"
          v-loading="loading2"
          :data="tableData2"
          max-height="500px"
          border
        >
          <el-table-column label="状态" align="center" minWidth="120px">
            <template #default="scope">
              <el-tag
                v-if="statusList[scope.row.deviceStatus]"
                effect="dark"
                :type="statusList[scope.row.deviceStatus].type"
                >{{ statusList[scope.row.deviceStatus].label }}</el-tag
              >
            </template>
          </el-table-column>
          <el-table-column label="设备图片" align="center" minWidth="120px">
            <template #default="scope">
              <el-image
                style="width: 50px; height: 30px; display: block; margin: 0 auto"
                :src="scope.row.deviceImg"
                fit="cover"
              />
            </template>
          </el-table-column>
          <el-table-column
            label="设备编码"
            align="center"
            minWidth="120px"
            prop="deviceCode"
          />
          <el-table-column
            label="设备名称"
            align="center"
            minWidth="120px"
            prop="deviceName"
          />
          <el-table-column
            label="设备类型"
            align="center"
            minWidth="120px"
            prop="deviceType"
          />
          <el-table-column
            label="规格型号"
            align="center"
            minWidth="120px"
            prop="model"
          />
          <el-table-column
            label="入库时间"
            align="center"
            minWidth="120px"
            prop="putTime"
            width="180"
          >
            <template #default="scope">
              <span>{{ parseTime(scope.row.putTime, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="taskType === '正常' && total2 > 0"
          :total="total2"
          v-model:page="queryParams1.current"
          v-model:limit="queryParams1.size"
          @pagination="getList2(queryParams1)"
        />
        <pagination
          v-show="taskType === '维修中' && total3 > 0"
          :total="total3"
          v-model:page="queryParams4.current"
          v-model:limit="queryParams4.size"
          @pagination="getList(queryParams4, 0)"
        />
      </div>
    </el-dialog>
    <el-dialog v-model="open2" title="设备使用年限详情" top="5vh" width="1000px">
      <el-table v-loading="loading2" :data="tableData2" max-height="500px" border>
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column label="状态" align="center" minWidth="120px">
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.deviceStatus]"
              effect="dark"
              :type="statusList[scope.row.deviceStatus].type"
              >{{ statusList[scope.row.deviceStatus].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="设备图片" align="center" minWidth="120px">
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="使用年限"
          align="center"
          minWidth="120px"
          prop="yearNum"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          prop="putTime"
          width="120"
        >
          <template #default="scope">
            <span>{{
              scope.row.putTime ? parseTime(scope.row.putTime, "{y}-{m}-{d}") : "-"
            }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作系统版本号"
          align="center"
          minWidth="130px"
          prop="osVersionStr"
        />
        <el-table-column label="CPU" align="center" minWidth="120px" prop="cpuStr" />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          prop="internalStorageStr"
        />
        <el-table-column label="硬盘" align="center" minWidth="120px" prop="diskStr" />
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          prop="installAddressStr"
        />
        <el-table-column label="品牌" align="center" minWidth="120px" prop="brandStr" />
      </el-table>

      <pagination
        v-show="total2 > 0"
        :total="total2"
        v-model:page="queryParams2.current"
        v-model:limit="queryParams2.size"
        @pagination="getList2(queryParams2)"
      />
    </el-dialog>
    <el-dialog v-model="open3" :title="`设备故障持续时间详情（${name}）`" top="5vh" width="1000px">
      <el-table v-loading="loading1" :data="tableData1" max-height="500px" border>
        <el-table-column label="状态" align="center" minWidth="120px">
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.deviceStatus]"
              effect="dark"
              :type="statusList[scope.row.deviceStatus].type"
              >{{ statusList[scope.row.deviceStatus].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column label="故障照片" align="center" minWidth="120px">
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.url"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column label="故障描述" align="center" minWidth="120px" prop="remark" />
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          prop="deviceType"
        />
        <el-table-column label="规格型号" align="center" minWidth="120px" prop="model" />
        <el-table-column
          label="维修负责人"
          align="center"
          minWidth="120px"
          prop="nickName"
        />
        <el-table-column
          label="维修负责人账号"
          align="center"
          minWidth="120px"
          prop="userName"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          prop="putTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.putTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total1 > 0"
        :total="total1"
        v-model:page="queryParams3.current"
        v-model:limit="queryParams3.size"
        @pagination="getList(queryParams3, 1)"
      />
    </el-dialog>
  </div>
</template>

<script setup name="school">
import Echarts from "@/components/Echarts/index.vue";
import { onMounted, reactive, ref, getCurrentInstance } from "vue";
import useUserStore from "@/store/modules/user";
import { devicePage } from "@/api/mediaTeach/ledger";
import { troublePage } from "@/api/mediaTeach/trouble";
import {
  queTroubleTimeInfo,
  queTroubleTimeRank,
  queTroubleRank,
  queDeviceFaultCount,
  queDeviceFaultRate,
  queMonthFault,
  queOptionList,
  queDeviceRunStatus,
  queDeviceUseTime,
} from "@/api/school";
import { transformSize, formatTextofEchart } from "@/utils";
import { checkPermi } from "@/utils/permission";

const { proxy } = getCurrentInstance();
const userStore = useUserStore();

const open1 = ref(false);
const taskType = ref("维修中");
const statusList = ref([
  { label: "运行良好", value: 0, type: "success" },
  { label: "运行较差", value: 1, type: "danger" },
  { label: "未运行", value: 2, type: "info" },
]);
const queryParams1 = ref({
  current: 1,
  size: 5,
  deviceStatus: taskType.value === "维修中" ? 1 : 0,
  tenantId: userStore.tenantId || "",
});
const total1 = ref(0);
const loading1 = ref(false);

const open2 = ref(false);
const queryParams2 = ref({
  current: 1,
  size: 5,
});
const total2 = ref(0);
const loading2 = ref(false);
const tableData2 = ref([]);
const open3 = ref(false);
const name = ref("");
const queryParams3 = ref({
  current: 1,
  size: 5,
  troubleStatus: 0,
  tenantId: userStore.tenantId || "",
  faultTime: 1,
});
const tableData1 = ref([]);

const queryParams4 = ref({
  current: 1,
  size: 5,
  troubleStatus: 0,
  deviceName: "",
});
const total3 = ref(0);
const tableData3 = ref([]);

/** 设备性能配置 */
let deviceId = ref("");
let deviceList = ref([
  { deviceName: "设备1", deviceId: "1" },
  { deviceName: "设备2", deviceId: "2" },
  { deviceName: "设备3", deviceId: "3" },
  { deviceName: "设备4", deviceId: "4" },
  { deviceName: "设备5", deviceId: "5" },
]);
const configIcon = [
  { name: "Cpu", color: "#5470c6" },
  { name: "Box", color: "#91cc75" },
  { name: "MessageBox", color: "#fac858" },
  { name: "Setting", color: "#ee6666" },
];
const runIcon = [
  { name: "SwitchButton", color: "#73c0de" },
  // { name: 'Odometer', color: '#3ba272' },
  { name: "Monitor", color: "#fc8452" },
];
let deviceConfig = ref([
  { label: "CPU型号", value: "1000" },
  { label: "内存大小", value: "32GB" },
  { label: "硬盘大小", value: "512GB" },
  { label: "操作系统版本", value: "Windows10" },
]);
let deviceRun = ref([
  { label: "开关机状态", value: "开机" },
  // { label: '网络速度', value: '68.5 M/s' },
  { label: "运行状况", value: "运行中" },
]);
let schoolNet = ref([
  { label: "平均出口带宽", value: "100Mbps" },
  { label: "上行网速", value: "53.7 M/s" },
  { label: "下行网速", value: "68.5 M/s" },
]);

/** 设备使用时长 */
let sbsyscSData = [
  { value: 2, name: "1年以内" },
  { value: 3, name: "1年" },
  { value: 10, name: "3年" },
  { value: 5, name: "5年" },
  { value: 6, name: "10年" },
];

/** 设备故障持续时间 */
let gzcxsjXData = ["设备5", "设备4", "设备3", "设备2", "设备1"];
let gzcxsjSData = [100, 140, 230, 100, 130];

/** 学校设备故障率 */
let sbgzlSData = [
  { value: 35, name: "维修中" },
  { value: 65, name: "正常" },
];

/** 设备故障次数统计 */
let gzcstjYData = ["装备5", "装备4", "装备3", "装备2", "装备1"];
let gzcstjSData = [100, 140, 230, 100, 130];

/** 设备故障状态占比 */
let cxsjtopYData = ["装备5", "装备4", "装备3", "装备2", "装备1"];
let cxsjtopSData = [100, 140, 230, 100, 130];

let gzcstopYData = ["装备5", "装备4", "装备3", "装备2", "装备1"];
let gzcstopSData = [100, 140, 230, 100, 130];

/** 月度故障次数 */
let ydgzcsXData = ["1月", "2月", "3月", "4月", "5月", "6月", "7月", "8月", "9月", "10月"];
let ydgzcsSData = [2, 10, 5, 1, 3, 6, 4, 8, 11, 9];

function getClickIdx(idx) {
  queryParams3.value.faultTime = Math.abs(idx) + 1;
  name.value = gzcxsjXData[idx];
  getList(queryParams3.value, 1);
  open3.value = true;
}

function handleRadio(val) {
  if (val === "维修中") {
    loading2.value = true;
    console.log("维修中");
    getList(queryParams4.value, 0);
  } else {
    queryParams1.value.deviceStatus = 0;
    getList2(queryParams1.value);
  }
}

function getList(query, type) {
  !!type ? (loading1.value = true) : (loading2.value = true);
  if (checkPermi(["system:DeviceTrouble:quePage"])) {
    let arr = [];
    troublePage(query).then((response) => {
      arr = response.data.records.reduce((res, cur) => {
        let imgList = cur.troubleImg.split(",");
        res.push({
          ...cur,
          url: imgList[0],
        });
        return res;
      }, []);
      if (!!type) {
        loading1.value = false;
        tableData1.value = arr;
        total1.value = response.data.total;
      } else {
        loading2.value = false;
        tableData3.value = arr;
        total3.value = response.data.total;
      }
    });
  }
}

function getList2(query) {
  if (checkPermi(["system:Device:quePage"])) {
    loading2.value = true;
    devicePage(query).then((response) => {
      console.log('使用年限',response)
      tableData2.value = response.data.page.records.reduce((res, cur) => {
        let {
          deviceName,
          deviceType,
          model,
          osVersion,
          cpu,
          internalStorage,
          disk,
          brand,
          installAddress,
          putTime
        } = cur;
        let yearNum = ''
        !!putTime ? (() => {
          let num = Math.floor((new Date().getTime() - new Date(putTime).getTime()) / 86400000 / 365)
          yearNum = num > 1 ? `${num}年` : '1年以内'
        })() : yearNum = 0
        res.push({
          ...cur,
          yearNum,
          deviceNameStr: deviceName || "-",
          deviceTypeStr: deviceType || "-",
          installAddressStr: installAddress || "-",
          modelStr: model || "-",
          osVersionStr: osVersion || "-",
          cpuStr: cpu || "-",
          internalStorageStr: internalStorage || "-",
          diskStr: disk || "-",
          brandStr: brand || "-",
        });
        return res;
      }, []);
      total2.value = response.data.page.total;
      loading2.value = false;
    });
  }
}

/** 注入 */
function injectOption() {
  proxy.sbsysc = sbsyscOption;
  proxy.gzcxsj = gzcxsjOption;
  proxy.sbgzl = sbgzlOption;
  proxy.gzcstj = gzcstjOption;
  proxy.ydgzcs = ydgzcsOption;
  proxy.cxsjtop = cxsjtopOption;
  proxy.gzcstop = gzcstopOption;
}

/** 设备使用时长 */
const sbsyscOption = reactive({
  options: {
    title: {
      text: "设备使用年限",
      textStyle: {
        color: "#fff",
        fontSize: transformSize(22),
      },
      top: "3%",
      left: "2%",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      bottom: "5%",
      left: "center",
      textStyle: {
        color: "#fff",
      },
    },
    series: [
      {
        name: "使用年限",
        type: "pie",
        radius: "50%",
        color: ["#75bedc", "#ef6567", "#91cd77", "#f9c956", "#5470c6"],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        data: [],
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 设备故障持续时间 */
const gzcxsjOption = reactive({
  options: {
    title: {
      text: "设备故障持续时间",
      textStyle: {
        color: "#fff",
        fontSize: transformSize(22),
      },
      top: "3%",
      left: "2%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "10%",
      right: "3%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#fff",
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#fff",
      },
      splitLine: { show: false },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        itemStyle: {
          color: "#ff69b4",
        },
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 学校设备故障率 */
const sbgzlOption = reactive({
  options: {
    title: {
      text: "学校设备故障率",
      textStyle: {
        color: "#fff",
        fontSize: transformSize(22),
      },
      top: "3%",
      left: "2%",
    },
    tooltip: {
      trigger: "item",
    },
    legend: {
      right: "5%",
      top: "center",
      orient: "vertical",
      textStyle: {
        color: "#fff",
      },
    },
    series: [
      {
        name: "学校设备故障率",
        type: "pie",
        color: ["#75bedc", "#ef6567", "#91cd77", "#f9c956", "#5470c6"],
        radius: "55%",
        data: [],
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
        label: {
          formatter: "{b}: {d}%", // 显示名称和百分比
          color: "#fff", // 设置标签颜色
        },
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 设备故障次数统计 */
const gzcstjOption = reactive({
  options: {
    title: {
      text: "设备故障次数统计",
      textStyle: {
        color: "#fff",
        fontSize: transformSize(22),
      },
      top: "2%",
      left: "2%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      right: "5%",
      top: "8%",
      textStyle: {
        color: "#fff",
      },
    },
    grid: {
      left: "5%",
      right: "10%",
      bottom: "5%",
      height: "75%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01],
      axisLabel: {
        color: "#fff",
      },
    },
    yAxis: {
      type: "category",
      axisLabel: {
        color: "#fff",
        formatter: (value) => formatTextofEchart(value)
      },
      data: [],
    },
    series: [
      {
        name: "故障次数",
        type: "bar",
        barWidth: "40%",
        data: [],
        itemStyle: {
          color: "#ee6666",
        },
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 当月设备故障持续时间top5 */
const cxsjtopOption = reactive({
  options: {
    title: {
      text: "当月设备故障持续时间Top5",
      textStyle: {
        color: "#fff",
      },
      top: "2%",
      left: "2%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "15%",
      right: "8%",
      bottom: "10%",
      height: "70%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01],
      axisLabel: {
        color: "#fff",
        fontSize: '70%',
        rotate: -30,
      },
      splitLine: { show: false },
    },
    yAxis: {
      type: "category",
      axisLabel: {
        color: "#fff",
        fontSize: '70%',
        formatter: (value) => formatTextofEchart(value)
      },
      data: [],
    },
    series: [
      {
        name: "持续时间",
        type: "bar",
        barWidth: "40%",
        data: [],
        itemStyle: {
          color: "#4ad2ff",
        },
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 当月设备故障次数top5 */
const gzcstopOption = reactive({
  options: {
    title: {
      text: "当月设备故障次数Top5",
      textStyle: {
        color: "#fff",
      },
      top: "2%",
      left: "2%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      left: "15%",
      right: "8%",
      bottom: "5%",
      height: "75%",
      containLabel: true,
    },
    xAxis: {
      type: "value",
      boundaryGap: [0, 0.01],
      axisLabel: {
        color: "#fff",
        fontSize: '70%',
      },
      splitLine: { show: false },
    },
    yAxis: {
      type: "category",
      axisLabel: {
        color: "#fff",
        fontSize: '70%',
        formatter: (value) => formatTextofEchart(value)
      },
      data: [],
    },
    series: [
      {
        name: "故障次数",
        type: "bar",
        barWidth: "40%",
        data: [],
        itemStyle: {
          color: "#fac858",
        },
      },
    ],
  },
  hasData: false,
  init: false,
});
/** 月度故障次数 */
const ydgzcsOption = reactive({
  options: {
    title: {
      text: "月度故障次数",
      textStyle: {
        color: "#fff",
        fontSize: transformSize(22),
      },
      top: "3%",
      left: "2%",
    },
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "25%",
      left: "8%",
      right: "4%",
      height: '50%'
    },
    xAxis: {
      type: "category",
      axisLabel: {
        color: "#fff",
        rotate: 30,
        fontSize: '80%'
      },
      data: [],
    },
    yAxis: {
      axisLabel: {
        color: "#fff",
      },
      type: "value",
    },
    series: [
      {
        data: [],
        type: "line",
        itemStyle: {
          color: "#00ffff",
        },
      },
    ],
  },
  hasData: false,
  init: false,
});

let opt1 = sbsyscOption.options;
let opt2 = gzcxsjOption.options;
let opt3 = sbgzlOption.options;
let opt4 = gzcstjOption.options;
let opt5 = ydgzcsOption.options;
let opt6 = cxsjtopOption.options;
let opt7 = gzcstopOption.options;

injectOption();

function setDatas() {
  // 设备使用时长
  // let opt1 = sbsyscOption.options
  opt1.series[0].data = sbsyscSData;

  // 设备故障持续时间
  // let opt2 = gzcxsjOption.options
  opt2.xAxis.data = gzcxsjXData;
  opt2.series[0].data = gzcxsjSData;

  //学校设备故障率
  // let opt3 = sbgzlOption.options
  opt3.series[0].data = sbgzlSData;

  //设备故障次数统计
  // let opt4 = gzcstjOption.options
  opt4.yAxis.data = gzcstjYData;
  opt4.series[0].data = gzcstjSData;

  // 月度故障次数
  // let opt5 = ydgzcsOption.options
  opt5.xAxis.data = ydgzcsXData;
  opt5.series[0].data = ydgzcsSData;

  // 设备故障状态占比
  // let opt6 = cxsjtopOption.options
  // let opt7 = gzcstopOption.options

  //当月设备故障持续时间top5
  opt6.yAxis.data = cxsjtopYData;
  opt6.series[0].data = cxsjtopSData;

  //当月设备故障次数top5
  opt7.yAxis.data = gzcstopYData;
  opt7.series[0].data = gzcstopSData;
}

const setFontSize = (id) => {
  proxy[id].options.title.textStyle.fontSize = transformSize(20);
  proxy[id].options.graphic
    ? (proxy[id].options.graphic.style.fontSize = transformSize(20))
    : "";
};

const handleChange = async (id) => {
  await queDeviceRunStatus({ id }).then((response) => {
    let {
      cpu,
      disk,
      internalStorage,
      osVersion,
      deviceStatus,
      runStatus,
    } = deviceList.value.find((_) => _.deviceId === id);
    deviceConfig.value[0].value = cpu;
    deviceConfig.value[1].value = internalStorage || 0;
    deviceConfig.value[2].value = disk || 0;
    deviceConfig.value[3].value = osVersion;
    let { isShutdown, network, networkDownload, networkUpload } = response.data;
    deviceRun.value[0].value = isShutdown ? "关机" : "开机";
    deviceRun.value[1].value = !!isShutdown ? "/" : !!runStatus ? "较差" : "良好";
    schoolNet.value[0].value = network || 0;
    schoolNet.value[1].value = networkUpload || 0;
    schoolNet.value[2].value = networkDownload || 0;
  });
  getAdaptFontSize()
};

function getDatas() {
  //查询当月设备故障持续时间一览
  queTroubleTimeInfo().then((response) => {
    if (response.data.length > 0) {
      (gzcxsjXData = []), (gzcxsjSData = []);
      response.data.map((item, index) => {
        gzcxsjXData[index] = item.time || 0;
        gzcxsjSData[index] = item.num || 0;
      });
      opt2.xAxis.data = gzcxsjXData;
      opt2.series[0].data = gzcxsjSData;
      gzcxsjOption.hasData = gzcxsjSData.length > 0
    }
  });

  //查询当月设备故障持续时间前5名
  queTroubleTimeRank().then((response) => {
    if (response.data.length > 0) {
      console.log('设备故障持续时间前5名', response.data);
      (cxsjtopYData = []), (cxsjtopSData = []);
      response.data.reverse().map((item, index) => {
        if (index < 5) {
          cxsjtopYData[index] = item.deviceName;
        cxsjtopSData[index] = item.troubleTime || 0;
        }
      });
      opt6.yAxis.data = cxsjtopYData;
      opt6.series[0].data = cxsjtopSData;
      cxsjtopOption.hasData = cxsjtopSData.length > 0
    }
  });

  //查询当月设备故障次数前5名
  queTroubleRank().then((response) => {
    if (response.data.length > 0) {
      console.log('设备故障次数前5名', response.data);
      (gzcstopYData = []), (gzcstopSData = []);
      response.data.reverse().map((item, index) => {
        if (index < 5) {
          gzcstopYData[index] = item.deviceName;
        gzcstopSData[index] = item.num || 0;
        }
      });
      opt7.yAxis.data = gzcstopYData;
      opt7.series[0].data = gzcstopSData;
      gzcstopOption.hasData = gzcstopSData.length > 0
    }
  });

  //查询设备已使用时长
  queDeviceUseTime().then((response) => {
    if (response.data.length > 0) {
      sbsyscSData = [];
      response.data.map((item, index) => {
        sbsyscSData.push({
          name: item.userTime || 0,
          value: (item.percentum * 100).toFixed(2) || 0,
        });
      });
      opt1.series[0].data = sbsyscSData;
      sbsyscOption.hasData = sbsyscSData.length > 0
    }
  });

  // 查询学校设备故障次数
  queDeviceFaultCount().then((response) => {
    if (response.data.length > 0) {
      console.log('设备故障次数', response.data);
      (gzcstjYData = []), (gzcstjSData = []);
      response.data.map((item, index) => {
        if (index < 10) {
          gzcstjYData[index] = item.deviceName;
          gzcstjSData[index] = item.num || 0;
        }
      });
      opt4.yAxis.data = gzcstjYData.reverse();
      opt4.series[0].data = gzcstjSData.reverse();
      gzcstjOption.hasData = gzcstjSData.length > 0
    }
  });
  //查询学校设备月度故障次数
  queMonthFault().then((response) => {
    if (response.data.length > 0) {
      (ydgzcsXData = []), (ydgzcsSData = []);
      response.data.map((item, index) => {
        ydgzcsSData[index] = item.num || 0;
        ydgzcsXData[index] = item.month;
      });
      opt5.xAxis.data = ydgzcsXData;
      opt5.series[0].data = ydgzcsSData;
      ydgzcsOption.hasData = ydgzcsSData.length > 0
    }
  });
  //查询学校设备故障率
  queDeviceFaultRate().then((response) => {
    sbgzlSData = [];
    let { normal, fault } = response.data;
    console.log(response.data);

    sbgzlSData.push(
      {
        name: "维修中",
        value: (fault * 100).toFixed(2) || 0
      },
      {
        name: "正常",
        value: (normal * 100).toFixed(2) || 0,
      }
    );
    opt3.series[0].data = sbgzlSData;
    sbgzlOption.hasData = sbgzlSData.length > 0
  });

  //查询设备下拉列表
  queOptionList().then((response) => {
    deviceList.value = response.data;
    if (deviceList.value.length > 0) {
      deviceId.value = deviceList.value[0].deviceId;
      handleChange(deviceId.value);
    }
  });
  // setDatas()
}

function getAdaptFontSize() {
  const elArr = document.getElementsByClassName('adaptFontSize')
  for (let i = 0; i < elArr.length; i++) {
    let width_screen = document.documentElement.clientWidth, 
    height_screen = document.documentElement.clientHeight
    let width_block = Math.ceil(7 * (width_screen / 100)), 
    height_block = Math.ceil(5 * (height_screen / 100))
    let val_arr = elArr[i].innerHTML.split('')
    let lineNum = Math.ceil(Math.sqrt((val_arr.length * height_block) / width_block)) // 行数
    let fontSize = Math.ceil(height_block / lineNum)
    elArr[i].style.fontSize = (fontSize > parseInt(1.5 * (width_screen / 100)) ? '1.5vw' : `${fontSize}px`)
    elArr[i].style.lineHeight = `${fontSize}px`
    console.log(width_screen, height_screen, width_block, height_block, val_arr, fontSize)
  }
}

const debounce = (func, delay) => {
    let timer;
    return function () {
        if (timer) {
            clearTimeout(timer)
        }
        timer = setTimeout(() => {
            func()
        }, delay)
    }
}

const adaptDebounce = debounce(getAdaptFontSize, 50)

onMounted(() => {
  window.addEventListener('resize', adaptDebounce)
  setDatas();
  getDatas();
});
</script>

<style lang="scss" scoped>
.schoolmain {
  background-color: #0f0c43;
  color: #fff;
  padding: 2.5vh 1vw;
  display: grid;
  grid-template-columns: 33% 33% 32%;
  grid-template-rows: 36vh 22.5vh 25.5vh;
  grid-gap: 1.6vh 1vw;

  .item {
    border: 1px solid #fff;
    display: flex;
    .noData{
      position: relative;
      width: 100%;
      height: 100%;
      display: flex;
      font-size: 1.3vw;
      justify-content: center;
      align-items: center;
      &::before{
        font-weight: bold;
        font-size: 1vw;
        position: absolute;
        left: .8vw;
        top: .8vh;
        content: '哈哈哈哈';
      }
      &.sbsyscBlock::before{
        content: '设备使用年限';
      }
      &.sbgzlBlock::before{
        content: '学校设备故障率';
      }
      &.gzcxsjBlock::before{
        content: '设备故障持续时间';
      }
      &.gzcstjBlock::before{
        content: '设备故障次数统计';
      }
      &.cxsjtopBlock::before{
        font-size: 1vw;
        content: '当月设备故障持续时间Top5';
      }
      &.gzcstopBlock::before{
        font-size: 1vw;
        content: '当月设备故障次数Top5';
      }
      &.ydgzcsBlock::before{
        content: '月度故障次数';
      }
    }
  }

  .one {
    padding: 0 1vw;
    grid-column-start: 1;
    grid-column-end: 3;
    // background-color: rgb(169, 154, 255, .3);
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-template-rows: 7vh 10vh 6vh 11vh;
    grid-gap: 0 1vw;

    .opt {
      grid-column-start: 1;
      grid-column-end: 7;
    }

    .opt2 {
      display: grid;
      grid-template-columns: repeat(4, 1fr);
      grid-template-rows: 100%;
      grid-gap: 0 1vw;

      &-item {
        border: 1px solid #e7e7e7;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1vw;

        .el-icon {
          font-size: 2.5vw !important;
          margin-right: 1.5vw;
        }

        span {
          display: inline-block;
          max-width: 7vw;
          max-height: 5vh;
          word-break: break-all;
          font-size: 1.5vw;
          font-weight: bold;
          overflow: hidden;
        }
      }
    }

    .opt4 {
      display: grid;
      // grid-template-columns: 15.1vw 15.1vw 9.75vw 9.75vw 9.75vw;
      grid-template-columns: repeat(5, 1fr);
      grid-template-rows: 100%;
      grid-gap: 0 1vw;
    }

    .opt4-item {
      // width: 15vw;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 1.5vw;
      font-weight: bold;
      border: 1px solid #e7e7e7;

      div {
        font-size: 1vw;
        margin-top: 0.5vw;
        font-weight: normal;
        display: flex;
        align-items: center;

        .el-icon {
          font-size: 1.5vw !important;
          margin-right: 0.5vw;
        }
      }
    }

    .opt5-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      font-size: 1vw;
      border: 1px solid #e7e7e7;

      div {
        font-size: 1.5vw;
        margin-top: 0.8vw;
        font-weight: bold;
      }
    }

    .opt1 {
      display: flex;
      align-items: center;
      justify-content: space-between;
      font-size: 1.3vw;

      div {
        display: flex;
        align-items: center;

        img {
          margin-right: 1vw;
          width: 3vw;
          height: 5vh;
        }
        :deep(.el-input__wrapper){
          width: 220px;
        }
      }
    }

    .opt3 {
      display: flex;
      font-size: 1.3vw;
      align-items: center;

      div {
        width: 50%;
        margin-right: 0.7vw;
      }
    }
  }

  .two,
  .three {
    position: relative;
  }

  .sbsysc,
  .sbgzl {
    position: absolute;
    cursor: pointer;
    right: 1.5%;
    top: 3.5%;
    font-size: 0.8vw;
    padding: 0.5vh 1vw;
    background-color: #a9abae;
    z-index: 9;
  }

  .five {
    grid-row-start: span 2;
  }
}
</style>
