<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <el-button @click="handleBack"
            >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
          >
        </div>
      </template>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="workOrderCode">
          <el-input
            v-model="queryParams.workOrderCode"
            placeholder="请输入工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="recording-main">
        <div class="recording-main_table">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="录音编号"
              align="center"
              minWidth="120px"
              prop="recordingCode"
            >
              <template #default="scope">
                {{ scope.row.recordingCode || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="录音"
              align="center"
              minWidth="120px"
              prop="fileName"
            >
              <template #default="scope">
                <el-popover
                  v-if="scope.row.fileName"
                  placement="top"
                  :width="300"
                  trigger="click"
                  popper-class="audio-popover"
                >
                  <template #reference>
                    <span
                      style="
                        color: #4095e5;
                        text-decoration: underline;
                        cursor: pointer;
                      "
                      >{{ scope.row.fileName }}</span
                    >
                  </template>
                  <div class="audio-player-container">
                    <audio 
                      controls 
                      :src="getAudioUrl(scope.row)" 
                      style="width: 100%"
                    ></audio>
                  </div>
                </el-popover>
                <span v-else>-</span>
              </template>
            </el-table-column>
            <el-table-column
              label="工单编号"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
            />
            <el-table-column
              label="创建时间"
              align="center"
              minWidth="120px"
              prop="createTime"
            />
            <el-table-column label="操作" min-width="100" align="center">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Download"
                  v-throttle
                  @click.stop="handleDownload(scope)"
                  >下载</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="recording">
import { useRoute } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import { recordingPage } from "@/api/service";

const route = useRoute();
const { proxy } = getCurrentInstance();

const state = reactive({
  dialogVisible: false,
  total: 1,
  loading: false,
  tableList: [],
  typeList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    workOrderCode: "",
  },
});

const {
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  loading,
  total,
} = toRefs(state);

function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

function getAudioUrl(row) {
  return row.fileUrl.indexOf("https://") == -1
    ? row.fileUrl.replace("http", "https")
    : row.fileUrl;
}

function handleDownload({ row }) {
  let url =
    row.fileUrl.indexOf("https://") == -1
      ? row.fileUrl.replace("http", "https")
      : row.fileUrl;
  window.open(url);
}

function getList() {
  state.loading = true;
  console.log(state.queryParams);
  recordingPage(state.queryParams)
    .then((res) => {
      console.log("录音列表", res);
      if (res.data) {
        state.tableList = res.data.records || [];
        state.total = res.data.total || 0;
      }
    })
    .finally(() => (state.loading = false));
}

getList();
</script>

<style scoped>
.audio-player-container {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 5px;
}

:deep(.audio-popover) {
  padding: 8px;
}
</style>