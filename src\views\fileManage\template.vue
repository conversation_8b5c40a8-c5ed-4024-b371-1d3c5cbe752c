<template>
  <div class="template app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <div class="template-main">
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="templateNoOrName">
            <el-input
              v-model="queryParams.templateNoOrName"
              placeholder="请输入模板编号/模板名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="templateTypeId">
            <el-select
              v-model="queryParams.templateTypeId"
              placeholder="请选择模板类型"
              @change="handleQuery"
              clearable
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="item in typeList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              >
              </el-option>
            </el-select>
          </el-form-item>

          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <div class="mb12">
          <el-button plain type="primary" icon="Plus" @click="handleAdd"
            >新建模板</el-button
          >
        </div>

        <el-table v-loading="loading" :data="templateList" border>
          <el-table-column
            label="模板编号"
            align="center"
            minWidth="120px"
            prop="templateNo"
            show-overflow-tooltip
          />
          <el-table-column
            label="模板类别"
            align="center"
            minWidth="120px"
            prop="templateTypeName"
            show-overflow-tooltip
          />
          <el-table-column
            label="模板名称"
            align="center"
            minWidth="120px"
            prop="templateName"
            show-overflow-tooltip
          />
          <el-table-column
            label="模板描述"
            align="center"
            minWidth="120px"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.templateDescribe || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            min-width="250"
            align="center"
            fixed="right"
            class-name="small-padding fixed-width"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Search"
                @click="handleCheck(scope.row)"
                >预览</el-button
              >
              <el-button
                link
                type="warning"
                icon="Document"
                @click="handleUpdate(scope.row)"
                >生成文档</el-button
              >
              <el-button
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <pagination
          v-show="templateList.length > 0"
          :total="templateList.length"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          layout="total, prev, pager, next"
          @pagination="getList"
        />

        <div class="template-main_record" v-if="recordList.length > 0">
          <div class="title">变更记录</div>
          <div
            class="template-main_record-item"
            v-for="(item, index) in recordList.slice(0, 5)"
            :key="index"
          >
            {{
              `${item.name || ""} ${item.role || ""} 于 ${item.time} ${
                item.type
              }了文档模板"${item.templateName}"`
            }}
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="fileTemplate">
import { useRoute, useRouter } from "vue-router";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import {
  getSchoolTemplateList,
  getSchoolTemplateDetail,
  addSchoolTemplate,
  deleteSchoolTemplate,
} from "@/api/fileMamage/template";
import { getSchoolTemplateTypeList } from "@/api/fileMamage/templateCategory";

const { proxy } = getCurrentInstance();
const { nickName, roles } = useUserStore(); // 获取当前登录的用户信息
const route = useRoute();
const router = useRouter();
const formRef = ref(null);
const state = reactive({
  loading: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    templateNoOrName: "",
    templateTypeId: "",
  },
  templateList_all: [],
  templateList: [],
  recordList: [],
  typeList: [],
});

const {
  queryParams,
  loading,
  templateList_all,
  templateList,
  recordList,
  typeList,
} = toRefs(state);

function handleAdd() {
  router.push("/fileManage/templateManage/addTemplate");
}

function handleDelete(row) {
  console.log(row);

  proxy.$modal
    .confirm(`确认删除名称为"${row.templateName}"的模板？`)
    .then(() => {
      // 构造删除参数
      const params = {
        templateId: row.templateId,
      };
      return deleteSchoolTemplate(params);
    })
    .then((res) => {
      proxy.$modal.msgSuccess("删除成功");
      getList(); // 刷新列表
    })
    .catch((error) => {});
}

function handleCheck(item) {
  router.push(`/fileManage/templateManage/templateInfo?id=${item.templateId}`);
}

function handleUpdate(item) {
  router.push(`/addFile?id=${item.templateId}&fromTemp=1`);
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 获取模板列表数据 */
function getList() {
  loading.value = true;
  getSchoolTemplateList(queryParams.value)
    .then((response) => {
      if (response.code === 200) {
        console.log(response, "获取的模板列表数据");

        loading.value = false;
        state.templateList_all = response.data.rows;
        state.templateList = JSON.parse(JSON.stringify(state.templateList_all));
      }
    })
    .catch((error) => {
      console.error("获取模板列表失败:", error);
      proxy.$modal.msgError("获取模板列表失败");
    });
}

getSchoolTemplateTypeList().then((res) => {
  typeList.value = res.data.rows;
  typeList.value.forEach((item) => {
    item.label = item.templateTypeName;
    item.value = item.templateTypeId;
  });
  console.log(typeList.value, "typeList");
});

onMounted(() => {
  getList();
  getSchoolTemplateTypeList();
});
</script>

<style lang="scss" scoped>
.template {
  :deep(.el-card__body) {
    // background-color: #f1f1f1;
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  &-main {
    &_list {
      display: flex;
      // justify-content: space-between;
      flex-wrap: wrap;
      gap: 20px;
      &-item {
        width: 19vw;
        border-radius: 6px;
        border: 1px solid #ddd;
        padding: 1vw;
        background-color: #fff;
        &.add {
          display: flex;
          flex-direction: column;
          justify-content: center;
          align-items: center;
          cursor: pointer;
          gap: 1vw;
          font-size: 1.5vw;
          min-height: 25.6vw;
          .image {
            width: 10vw;
            height: 10vw;
            background: url("@/assets/icons/icon_addFile.png");
            background-size: 100% 100%;
          }
        }
        .item-top,
        .item-bottom {
          display: flex;
          justify-content: space-between;
          gap: 1vw;
          .title {
            padding-left: 1.8vw;
            // border: 1px solid red;
            position: relative;
            &::before {
              position: absolute;
              content: "";
              width: 1.3vw;
              height: 1.35vw;
              top: 0;
              left: 0;
              background: url("@/assets/icons/icon_file.png");
              background-size: 100% 100%;
            }
          }
          .btn {
            border: 1px solid #4faaff;
            cursor: pointer;
            flex: 1;
            text-align: center;
            padding: 0.3vw 0;
            border-radius: 5px;
            color: #4faaff;
            &.use {
              background-color: #4faaff;
              color: #fff;
            }
          }
        }
        .item-middle {
          height: 18vw;
          margin: 1vw 0;
          border-radius: 5px;
          background-color: #dbdbdb;
          display: flex;
          justify-content: center;
          align-items: center;
        }
      }
    }
    &_record {
      .title {
        padding: 2vw 0 0.3vw;
        font-weight: bold;
      }
      &-item {
        margin-top: 0.5vw;
      }
    }
  }
}
</style>
