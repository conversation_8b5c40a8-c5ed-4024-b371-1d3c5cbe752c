<template>
  <div
    :class="{ 'has-logo': showLogo }"
    :style="{
      backgroundColor:
        sideTheme === 'theme-dark'
          ? variables.menuBackground
          : variables.menuLightBackground,
    }"
  >
    <!-- <logo v-if="showLogo" :collapse="isCollapse" /> -->
    <el-scrollbar :class="sideTheme" wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="
          sideTheme === 'theme-dark'
            ? variables.menuBackground
            : variables.menuLightBackground
        "
        :text-color="
          sideTheme === 'theme-dark' ? variables.menuColor : variables.menuLightColor
        "
        :unique-opened="true"
        :active-text-color="variables.sideMenuColorActive"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from "./Logo";
import SidebarItem from "./SidebarItem";
import variables from "@/assets/styles/variables.module.scss";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import useUserStore from "@/store/modules/user";
import { addSchoolUserUseFunction } from "@/api/approval";
import { debounce } from "@/utils/debounce";

const userStore = useUserStore();
const route = useRoute();
const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();

const sidebarRouters = computed(() => permissionStore.sidebarRouters);
// console.log(sidebarRouters)
const showLogo = computed(() => settingsStore.sidebarLogo);
const sideTheme = computed(() => settingsStore.sideTheme);
const theme = computed(() => settingsStore.theme);
const isCollapse = computed(() => !appStore.sidebar.opened);

const activeMenu = computed(() => {
  const { meta, path } = route;
  // if set path, the sidebar will highlight the path you set
  if (meta.activeMenu) {
    return meta.activeMenu;
  }
  return path;
});

// 点击记录菜单选择
// const routeClick = debounce((item) => {
//   console.log(item);

//   if (item.parentId) {
//     let obj = {
//       userId: userStore.userId,
//       menuId: item.parentId,
//       num: 1,
//     };
//     addSchoolUserUseFunction(obj)
//       .then((res) => {})
//       .catch(() => {});
//   }
// }, 200);
</script>
<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";

::v-deep .el-menu-item.is-active{
  background-color: $base-side-menu-bgColor-active !important;
  color: $base-side-menu-color-active !important;
  position: relative;
}

::v-deep .el-sub-menu.is-active .el-sub-menu__title {
  // background-color: $base-side-menu-bgColor-active !important;
  color: $base-side-menu-color-active !important;
  position: relative;
}

::v-deep .el-menu-item.is-active.submenu-title-noDropdown::after, ::v-deep .el-menu-item.is-active::after {
  content: "";
  position: absolute;
  width: 5px;
  height: 56px;
  right: 0;
  top: 0;
  border-top-left-radius: 5px;
  border-bottom-left-radius: 5px;
  background-color: $base-side-menu-color-active !important;
}

::v-deep .el-menu-item.is-active::after {
  height: 50px;
}

</style>
