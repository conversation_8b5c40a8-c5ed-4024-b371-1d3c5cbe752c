import request from "@/utils/request";

// 导出报障频度
export function exportFrequency(range, startDate, endDate) {
  return request({
    url: `/system/deviceTrouble/export/frequency?range=${range}&startDate=${startDate}&endDate=${endDate}`,
    method: "post",
    responseType: "blob",
  });
}

// 报障频度统计
export function troubleFrequency(params) {
  return request({
    url: `/system/deviceTrouble/frequency`,
    method: "get",
    params,
  });
}

// 批量增加备件明细
export function addSparePartsTrouble(data) {
  return request({
    url: "/system/spareParts/deviceTrouble/add",
    method: "post",
    data,
  });
}

// 运维日报
export function troubleDaily() {
  return request({
    url: `/system/deviceTrouble/daily`,
    method: "get",
  });
}

// 查看评价
export function evaluationOne(id) {
  return request({
    url: `/system/schoolEvaluation/${id}`,
    method: "get",
  });
}

// 工单回访
export function workOrderVisit(data) {
  return request({
    url: "/system/schoolEvaluation/update",
    method: "put",
    data,
  });
}

// 工单回访列表
export function visitPage(params) {
  return request({
    url: "/system/deviceTrouble/visit/list",
    method: "get",
    params,
  });
}

// 投诉回访列表
export function visitComplaintsPage(params) {
  return request({
    url: "/system/deviceTrouble/complaints/visit/list",
    method: "get",
    params,
  });
}

// 工单备件使用情况
export function spareUse(params) {
  return request({
    url: "/system/deviceTrouble/use",
    method: "get",
    params,
  });
}

// 修改工单状态
export function troubleUpdate(data) {
  return request({
    url: "/system/deviceTrouble/status/update",
    method: "put",
    data,
  });
}

// 投诉列表
export function complainList(params) {
  return request({
    url: "/system/schoolComplaints/page",
    method: "get",
    params,
  });
}

// 投诉单详情
export function complainInfo(id) {
  return request({
    url: `/system/schoolComplaints/${id}`,
    method: "get",
  });
}

// 回复投诉
export function complainReply(data) {
  return request({
    url: "/system/schoolComplaints/reply",
    method: "put",
    data,
  });
}

// 知识库列表
export function knowledgeBaseList(params) {
  return request({
    url: "/system/deviceTrouble/knowledgeBase/list",
    method: "get",
    params,
  });
}

// 转入知识库
export function addKnowledgeBase(data) {
  return request({
    url: "/system/deviceTrouble/knowledgeBase",
    method: "post",
    data,
  });
}

// 删除知识库
export function delKnowledgeBase(id) {
  return request({
    url: `/system/deviceTrouble/delete/knowledgeBase/${id}`,
    method: "delete",
  });
}

// 获取维修人
export function nickName() {
  return request({
    url: "/system/deviceTrouble/getNickName",
    method: "get",
  });
}

// 根据设备编号获取安装地址
export function repairPosition(data) {
  return request({
    url: "/system/device/repair/info",
    method: "post",
    data,
  });
}

// 工单/维修列表
export function deviceMaintenanceList(data) {
  return request({
    url: "/system/deviceTrouble/maintenance/web/list",
    method: "post",
    data,
  });
}

// 设备维修
export function deviceMaintenance(data) {
  return request({
    url: "/system/deviceTrouble/maintenance",
    method: "post",
    data,
  });
}

// 设备报修 type: 0小程序  1web
export function deviceRepair(data) {
  return request({
    url: "/system/deviceTrouble/repair",
    method: "post",
    data,
  });
}

// 报修详情 type: 0小程序 1web
export function repairInfo(data) {
  return request({
    url: "/system/deviceTrouble/repairInfo",
    method: "post",
    data,
  });
}

export function troubleAdd(data) {
  return request({
    url: "/system/deviceTrouble/add",
    method: "post",
    data,
  });
}

export function troubleEdit(data) {
  return request({
    url: "/system/deviceTrouble/edit",
    method: "post",
    data,
  });
}

export function troubleInfo(data) {
  return request({
    url: "/system/deviceTrouble/getInfo",
    method: "post",
    data,
  });
}

export function troublePage(data) {
  return request({
    url: "/system/deviceTrouble/quePage",
    method: "post",
    data,
  });
}
