<template>
<div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>工单中心</span>
        </div>
      </template>
      <el-button type="primary" size="small" @click="addWorkOrder">新建报障单</el-button>
      <el-table
        :data="ledgerList"
        border
        highlight-current-row
      >
        <el-table-column
          label="工单编号"
          align="center"
          minWidth="120px"
          prop="workOrderCode"
        />
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="报障人姓名"
          align="center"
          minWidth="120px"
          prop="clientName"
        />
        <el-table-column
          label="报障人联系方式"
          align="center"
          minWidth="120px"
          prop="clientPhone"
        />
        <el-table-column
          label="报障时间"
          align="center"
          minWidth="120px"
          prop="faultDate"
        />
        <el-table-column
          label="报障描述"
          align="center"
          minWidth="120px"
          prop="faultDesc"
        />
        <el-table-column
          label="报障来源"
          align="center"
          minWidth="120px"
          prop="faultSource"
        />
        <el-table-column
          label="备件编号"
          align="center"
          minWidth="120px"
          prop="spareCode"
        />
        <el-table-column
          label="单据状态"
          align="center"
          minWidth="120px"
          prop="orderStatus"
        />
       <el-table-column
         label="操作"
         min-width="240"
         align="center"
         fixed="right"
        >
          <template #default="scope">
          <div class="button-group">
          <div class="left-button">
          <el-button 
            v-if="scope.row.orderStatus === '待处理'"
            type="primary" 
            size="small"
            @click="handleProcess(scope.row)"
          >
            处理
          </el-button>
          </div>
          <div class="right-button">
          <el-button 
            type="success" 
            size="small"
            @click="moveToKnowledgeBase(scope.row)"
          >
            转入知识库
          </el-button>
          </div>
        </div>
      </template>
  </el-table-column>
      </el-table>
    </el-card>
     <!-- 添加工单对话框 -->
  <el-dialog
    v-model="dialogVisible"
    title="新增工单"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="formRules"
      label-width="120px"
    >
      <el-form-item label="工单编号" prop="workOrderCode">
        <el-input v-model="formData.workOrderCode" disabled />
      </el-form-item>
      <el-form-item label="设备编码" prop="deviceCode">
        <el-input v-model="formData.deviceCode" placeholder="请输入设备编码" />
      </el-form-item>
      <el-form-item label="报障人姓名" prop="clientName">
        <el-input v-model="formData.clientName" placeholder="请输入报障人姓名" />
      </el-form-item>
      <el-form-item label="报障人电话" prop="clientPhone">
        <el-input v-model="formData.clientPhone" placeholder="请输入报障人电话" />
      </el-form-item>
      <el-form-item label="报障时间" prop="faultDate">
        <el-date-picker
          v-model="formData.faultDate"
          type="datetime"
          placeholder="选择报障时间"
          format="YYYY-MM-DD"
          value-format="YYYY-MM-DD"
          :disabled-date="disabledDate"
        />
      </el-form-item>
      <el-form-item label="报障描述" prop="faultDesc">
        <el-input
          v-model="formData.faultDesc"
          type="textarea"
          placeholder="请输入报障描述"
          :rows="3"
        />
      </el-form-item>
      <el-form-item label="报障来源" prop="faultSource">
        <el-select v-model="formData.faultSource" placeholder="请选择报障来源">
          <el-option label="电话报障" value="电话报障" />
          <el-option label="小程序报障" value="小程序报障" />
          <el-option label="管理端报障" value="管理端报障" />
        </el-select>
      </el-form-item>
      <el-form-item label="备件编号" prop="spareCode">
        <el-input v-model="formData.spareCode" placeholder="请输入备件编号" />
      </el-form-item>
      <el-form-item label="单据状态" prop="orderStatus">
        <el-select v-model="formData.orderStatus" placeholder="请选择单据状态">
          <el-option label="待处理" value="待处理" />
          <el-option label="已处理" value="已处理" />
        </el-select>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="submitForm(formRef)">确认</el-button>
      </span>
    </template>
  </el-dialog>
  </div>
</template>

<script setup name="taskCenter">
import { ref, reactive, onMounted, watch } from 'vue'
// import { ElMessage, ElMessageBox } from 'element-plus'

// 查询参数
const queryParams = reactive({
  workOrderCode: '',
  faultDesc: '',
  dateRange: []
})

// 初始工单数据
const initialLedgerList = [
  {
    workOrderCode: 'WO-0001',
    faultSource: '电话报障',
    deviceCode: 'DVC-0001',
    clientName: '张三',
    clientPhone: '13800000000',
    faultDate: '2023-10-01',
    faultDesc: '设备无法启动',
    spareCode: 'SPR-0001',
    orderStatus: '待处理'
  },
  {
    workOrderCode: 'WO-0002',
    faultSource: '小程序报障',
    deviceCode: 'DVC-0002',
    clientName: '李四',
    clientPhone: '13800000001',
    faultDate: '2023-10-02',
    faultDesc: '屏幕显示异常',
    spareCode: 'SPR-0002',
    orderStatus: '已处理'
  },
  {
    workOrderCode: 'WO-0003',
    faultSource: '管理端报障',
    deviceCode: 'DVC-0003',
    clientName: '王五',
    clientPhone: '13800000002',
    faultDate: '2023-10-03',
    faultDesc: '电池续航短',
    spareCode: 'SPR-0003',
    orderStatus: '已处理'
  },
  {
    workOrderCode: 'WO-0004',
    faultSource: '电话保障',
    deviceCode: 'DVC-0004',
    clientName: '赵六',
    clientPhone: '13800000003',
    faultDate: '2023-10-04',
    faultDesc: '摄像头故障',
    spareCode: 'SPR-0004',
    orderStatus: '待处理'
  },
  {
    workOrderCode: 'WO-0005',
    faultSource: '小程序报障',
    deviceCode: 'DVC-0005',
    clientName: '孙七',
    clientPhone: '13800000004',
    faultDate: '2023-10-05',
    faultDesc: '系统崩溃',
    spareCode: 'SPR-0005',
    orderStatus: '已处理'
  }
]

// 表格数据
const ledgerList = ref([])

// 初始化本地存储
const initLocalStorage = () => {
  const storedData = localStorage.getItem('workOrders')
  if (!storedData) {
    // 如果本地存储中没有数据，使用初始数据
    localStorage.setItem('workOrders', JSON.stringify(initialLedgerList))
    ledgerList.value = initialLedgerList
  } else {
    // 如果有数据，使用存储的数据
    ledgerList.value = JSON.parse(storedData)
  }
}

// 更新本地存储
const updateLocalStorage = () => {
  localStorage.setItem('workOrders', JSON.stringify(ledgerList.value))
}

// 监听数据变化
watch(ledgerList, () => {
  updateLocalStorage()
}, { deep: true })

// 组件挂载时初始化数据
onMounted(() => {
  initLocalStorage()
})

// 处理工单
const handleProcess = (row) => {
  // 更新工单状态
  row.orderStatus = '已处理'
  updateLocalStorage()
  ElMessage.success('工单处理成功：' + row.workOrderCode)
}

// 转入知识库
const moveToKnowledgeBase = (row) => {
  ElMessageBox.confirm(
    '确认将该工单转入知识库？',
    '提示',
    {
      confirmButtonText: '确定',
      cancelButtonText: '取消',
      type: 'warning'
    }
  ).then(() => {
    // 创建知识库数据对象
    const knowledgeData = {
      id: new Date().getTime(),
      workOrderCode: row.workOrderCode,
      faultSource: row.faultSource,
      deviceCode: row.deviceCode,
      clientName: row.clientName,
      clientPhone: row.clientPhone,
      faultDate: row.faultDate,
      faultDesc: row.faultDesc,
      spareCode: row.spareCode,
      orderStatus: row.orderStatus,
      solution: '',
      createTime: new Date().toLocaleString(),
      status: '待处理'
    }

    // 获取已有的知识库数据
    let knowledgeList = JSON.parse(localStorage.getItem('knowledgeBase') || '[]')
    
    // 检查是否已存在
    const exists = knowledgeList.some(item => item.workOrderCode === row.workOrderCode)
    
    if (exists) {
      ElMessage.warning('该工单已在知识库中')
      return
    }
    
    // 添加新数据并保存
    knowledgeList.push(knowledgeData)
    localStorage.setItem('knowledgeBase', JSON.stringify(knowledgeList))
    
    ElMessage.success('已成功转入知识库')
  }).catch(() => {
    ElMessage.info('已取消转入')
  })
}

// 对话框显示控制
const dialogVisible = ref(false)
// 表单ref
const formRef = ref(null)

// 表单数据
const formData = reactive({
  workOrderCode: '',
  deviceCode: '',
  clientName: '',
  clientPhone: '',
  faultDate: '',
  faultDesc: '',
  faultSource: '',
  spareCode: '',
  orderStatus: '待处理'
})

// 表单验证规则
const formRules = {
  deviceCode: [{ required: true, message: '请输入设备编码', trigger: 'blur' }],
  clientName: [{ required: true, message: '请输入报障人姓名', trigger: 'blur' }],
  clientPhone: [
    { required: true, message: '请输入报障人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ],
  faultDate: [{ required: true, message: '请选择报障时间', trigger: 'change' }],
  faultDesc: [{ required: true, message: '请输入报障描述', trigger: 'blur' }],
  faultSource: [{ required: true, message: '请选择报障来源', trigger: 'change' }],
  spareCode: [{ required: true, message: '请输入备件编号', trigger: 'blur' }],
  orderStatus: [{ required: true, message: '请选择单据状态', trigger: 'change' }]
}

// 生成新的工单编号
const generateWorkOrderCode = () => {
  const maxCode = Math.max(
    ...ledgerList.value.map(item => parseInt(item.workOrderCode.split('-')[1]))
  )
  const newNumber = (maxCode + 1).toString().padStart(4, '0')
  return `WO-${newNumber}`
}

// 禁用未来日期
const disabledDate = (time) => {
  return time.getTime() > Date.now()
}

// 打开新增工单对话框
const addWorkOrder = () => {
  formData.workOrderCode = generateWorkOrderCode()
  formData.orderStatus = '待处理'
  formData.faultDate = new Date().toLocaleString()
  dialogVisible.value = true
}

// 提交表单
const submitForm = async (formEl) => {
  if (!formEl) return
  
  await formEl.validate((valid) => {
    if (valid) {
      // 添加新工单到列表
      ledgerList.value.push({
        ...formData
      })
      
      // 本地存储会通过 watch 自动更新
      ElMessage.success('工单添加成功')
      dialogVisible.value = false
      
      // 重置表单数据
      Object.keys(formData).forEach(key => {
        if (key !== 'workOrderCode') {
          formData[key] = ''
        }
      })
      formData.orderStatus = '待处理'
    }
  })
}
</script>

<style lang="scss" scoped>
.el-button {
  padding: 15px 0;
  margin-bottom: 15px;
}

// 两个操作按钮的布局样式
.button-group {
  display: flex;  
  justify-content: space-between; 
  align-items: center;
  padding: 0 10px; 

  .el-button {
    margin: 0;
    min-width: 80px;
    
    &.el-button--primary {
      background-color: #409EFF;
      border-color: #409EFF;
      color: white;
      
      &:hover {
        background-color: #79bbff;
        border-color: #79bbff;
      }
    }
    
    &.el-button--success {
      background-color: #67C23A;
      border-color: #67C23A;
      color: white;
      
      &:hover {
        background-color: #95d475;
        border-color: #95d475;
      }
    }
  }
}

:deep(.el-table) {
  .el-button--small {
    padding: 8px 15px;
  }
}

:deep(.el-dialog__body) {
  padding: 20px 40px;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}

:deep(.el-select) {
  width: 100%;
}

:deep(.el-date-picker) {
  width: 100%;
}
</style>