import request from "@/utils/request";

// 添加应急预案
export function addSchoolPlan(data) {
  return request({
    url: "/system/schoolPlan/add",
    method: "post",
    data,
  });
}

// 应急预案列表
export function schoolPlanList(params) {
  return request({
    url: "/system/schoolPlan/page",
    method: "get",
    params,
  });
}

// 修改应急预案
export function updateSchoolPlan(data) {
  return request({
    url: "/system/schoolPlan/update",
    method: "put",
    data,
  });
}

// 删除应急预案
export function delSchoolPlan(params) {
  return request({
    url: "/system/schoolPlan/delete",
    method: "delete",
    params,
  });
}

// 应急预案详情
export function schoolPlanInfo(id) {
  return request({
    url: `/system/schoolPlan/${id}`,
    method: "get",
  });
}

// 修改关联人员
export function updateSchoolPlanMember(data, id) {
  return request({
    url: `/system/schoolPlan/update/people/${id}`,
    method: "put",
    data,
  });
}

// 上传预案文件
export function uploadSchoolPlan(data) {
  return request({
    url: `/system/upload/uploadImgFile`,
    method: "post",
    data,
  });
}

// 添加应急工单
export function addSchoolPlanWorkOrder(data) {
  return request({
    url: "/system/schoolPlanWorkOrder/add",
    method: "post",
    data,
  });
}

// 应急工单列表
export function schoolPlanWorkOrderList(params) {
  return request({
    url: "/system/schoolPlanWorkOrder/list",
    method: "get",
    params,
  });
}

// 修改应急工单
export function updateSchoolPlanWorkOrder(data) {
  return request({
    url: "/system/schoolPlanWorkOrder/update",
    method: "put",
    data,
  });
}

// 应急工单详情
export function schoolPlanWorkOrderInfo(id) {
  return request({
    url: `/system/schoolPlanWorkOrder/${id}`,
    method: "get",
  });
}

// 上传处理文件
export function uploadSchoolPlanWorkOrder(data) {
  return request({
    url: `/system/upload/uploadImgFile`,
    method: "post",
    data,
  });
}

// 备件使用情况
export function schoolPlanSpareInfo(parmas) {
  return request({
    url: `/system/schoolPlanWorkOrder/use?planWorkOrderId=${parmas.planWorkOrderId}&pageNum=${parmas.pageNum}&pageSize=${parmas.pageSize}`,
    method: "get",
    // parmas,
  });
}

// 一键通知 /system/schoolPlanWorkOrder/notice
export function schoolPlanWorkOrderNotice(params) {
  return request({
    url: "/system/schoolPlanWorkOrder/notice",
    method: "get",
    params
  });
}
