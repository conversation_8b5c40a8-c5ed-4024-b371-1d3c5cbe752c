<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <div class="fileInfo-header">
        <div
          class="fileInfo-header_item"
          v-for="(item, index) in activeList"
          :key="index"
          :class="[active == index ? 'active' : '', `item${index + 1}`]"
        >
          {{ item }}
        </div>
      </div>

      <div class="projectInfo" v-if="active == 0" v-loading="loadingPage">
        <div class="projectInfo-btn">
          <div class="taskInfo-tit">文档名称</div>
          <span>{{ documentName || "-" }}</span>
        </div>

        <el-descriptions title="项目信息" border :column="3">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目编号"
          >
            {{ projectInfo.projectNo || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目名称"
          >
            <span>{{ projectInfo.projectName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目负责人"
          >
            <span>{{ projectInfo.responsibleNameStr || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目参与人"
          >
            <span>{{ projectInfo.partakeNameStr || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目开始时间"
          >
            <span>{{ projectInfo.startTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目结束时间"
          >
            <span>{{ projectInfo.endTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目金额"
          >
            <span>{{ projectInfo.projectAmount || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目归属部门"
          >
            <span>{{ projectInfo.projectDeptName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="是否开放小程序搜索"
          >
            <span>{{
              projectInfo.isOpen == 1
                ? "开放"
                : projectInfo.isOpen == 0
                ? "不开放"
                : "-"
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="甲方"
          >
            <span>{{ projectInfo.partyA || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="乙方"
          >
            <span>{{ projectInfo.partyB || "-" }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="projectInfo-title">项目干系人</div>
        <el-table :data="stakeholderList" border>
          <el-table-column
            label="姓名"
            prop="nickName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="性别"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.sex == "0" ? "男" : row.sex == "1" ? "女" : "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="部门"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.deptName || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="岗位"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.postName || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="手机号码"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.phone || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="邮箱"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.email || "-" }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div v-show="active == 1" class="taskInfo" v-loading="loadingPage">
        <!-- 工单详情表格 -->
        <el-descriptions title="报障信息" border>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单编号"
          >
            {{ repairForm.code }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单来源"
          >
            <span>{{ repairForm.resource }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障人"
          >
            <span>{{ repairForm.repairName }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障人联系方式"
          >
            <span>{{ repairForm.repairPhone }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障时间"
          >
            <span>{{ repairForm.submittedTime }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障来源"
          >
            <span>{{ repairForm.channel }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="2"
            label-class-name="label-width"
            class-name="value-width"
            label="报障描述"
          >
            <span>{{ repairForm.remark }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障单位"
          >
            <span>{{ repairForm.corpName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            :span="3"
            label-class-name="label-width"
            class-name="value-width"
            label="报障图片"
          >
            <span v-if="!repairForm.troubleImg">暂无图片</span>
            <el-image
              v-else
              v-for="(item, index) in repairForm.troubleImg"
              :key="index"
              style="width: 100px; height: 100px; margin-right: 10px"
              :src="item"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="repairForm.troubleImg"
              :initial-index="index"
              fit="contain"
            />
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障附件"
            :span="3"
          >
            <span
              @click="downloadHttp(repairForm.annexUrl)"
              :class="repairForm.annexName ? 'uploadlink' : ''"
              >{{ repairForm.annexName || "-" }}</span
            >
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="电话录音"
            v-if="repairForm.channel == '电话报障'"
          >
            <span
              @click="downloadHttp(repairForm.fileUrl)"
              :class="repairForm.fileName ? 'uploadlink' : ''"
              >{{ repairForm.fileName || "-" }}</span
            >
          </el-descriptions-item>
        </el-descriptions>

        <!-- 设备详情表格 -->
        <el-descriptions title="设备详情" border :column="2" style="margin-top: 20px">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备编码"
          >
            {{ form.deviceCode || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备名称"
          >
            <span :style="{ color: form.isDelete ? 'red' : '#000' }">{{
              form.deviceName
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备图片"
          >
            <span v-if="!form.deviceCode">暂无图片</span>
            <el-image
              v-else
              style="width: 100px; height: 50px"
              :src="form.deviceImg"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="[form.deviceImg]"
              :initial-index="0"
              fit="contain"
            />
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备类型"
          >
            <span>{{ form.deviceType || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备标签"
          >
            <span>{{ form.deviceTag || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="安装位置"
          >
            <span>{{ form.installAddress || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产类别"
          >
            <span>{{ form.assetsTypeName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产等级"
          >
            {{ form.level || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="资产端口类别"
          >
            <span>{{ form.portTypeName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="规格型号"
          >
            <span>{{ form.model || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="物理地址"
          >
            <span>{{ form.macAddress || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="逻辑地址"
          >
            <span>{{ form.logicAddress || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="IP地址"
          >
            <span>{{ form.ipAddress || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="操作系统版本号"
          >
            <span>{{ form.osVersion || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="状态"
          >
            <el-tag
              v-if="statusObj[form.deviceStatus]"
              effect="dark"
              :type="statusObj[form.deviceStatus].type"
              >{{ statusObj[form.deviceStatus].label }}</el-tag
            >
            <span v-else>-</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="CPU"
          >
            <span>{{ form.cpu || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="品牌"
          >
            <span>{{ form.brand || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="内存"
          >
            <span>{{ form.internalStorage || "-" }}</span>
          </el-descriptions-item>

          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="硬盘"
          >
            <span>{{ form.disk || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="端口"
          >
            <span>{{ form.port || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="线缆"
          >
            <span>{{ form.cable || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="入库时间"
          >
            <span>{{ form.putTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="关联设备"
          >
            <span>{{ form.associatedDevice?.replace(/,/g, "、") || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="关联人员"
          >
            <span>{{ form.associatedPeople?.replace(/,/g, "、") || "-" }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 维修详情 -->
        <el-descriptions
          title="维修信息"
          border
          style="margin-top: 20px"
          :column="3"
        >
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="维修人"
          >
            {{ maintainForm.maintainName || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="维修时间"
          >
            <span>{{ maintainForm.maintainTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理耗时"
          >
            {{
              maintainForm.troubleTime
                ? `${maintainForm.troubleTime} 小时`
                : "-"
            }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="维修结果"
            :span="3"
          >
            <span>{{ maintainForm.results || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="维修图片"
          >
            <span v-if="!maintainForm.repairImg">暂无图片</span>
            <el-image
              v-else
              v-for="(item, index) in maintainForm.repairImg"
              :key="index"
              style="width: 50px; height: 30px"
              :src="item"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="maintainForm.repairImg"
              :initial-index="index"
              fit="contain"
            />
          </el-descriptions-item>
        </el-descriptions>

        <!-- 备件表格 -->
        <div class="taskInfo-tit">备件信息</div>
        <el-table :data="spareList" border max-height="300px">
          <el-table-column
            label="备件编号"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
          >
            <template #default="scope">
              {{ scope.row.sparePartsCode || scope.row.code || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="备件名称"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="name"
          />
          <el-table-column
            label="备件类型"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="typeName"
          />
          <el-table-column
            label="规格型号"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="specifications"
          />
          <el-table-column
            label="备件品牌"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="brand"
          />
          <el-table-column
            label="颜色分类"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="color"
          />
          <el-table-column
            label="现有库存"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="stock"
          />
          <el-table-column
            label="消耗量"
            align="center"
            show-overflow-tooltip
            minWidth="150px"
          >
            <template #default="scope">
              <span>{{ scope.row.num }}</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- 评价详情 -->
        <div v-if="!commentForm.id">
          <div class="taskInfo-tit">评价信息</div>
          <div style="font-size: 16px">暂无评价</div>
        </div>

        <el-descriptions
          v-if="!!commentForm.id"
          title="评价信息"
          border
          style="margin-top: 20px"
          :column="2"
        >
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="问题是否解决"
          >
            {{ !!commentForm.resolved ? "已解决" : "未解决" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="服务态度"
          >
            <el-rate
              v-model="commentForm.serviceAttitude"
              disabled
              size="large"
            />
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="技术能力"
          >
            <el-rate
              v-model="commentForm.technicalAbility"
              disabled
              size="large"
            />
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理速度"
          >
            <el-rate
              v-model="commentForm.processingSpeed"
              disabled
              size="large"
            />
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="意见反馈"
          >
            <span>{{ commentForm.feedback || "-" }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <!-- 关联投诉单 -->
        <div v-if="!complaintsForm.id">
          <div class="taskInfo-tit">关联投诉单</div>
          <div style="font-size: 16px">暂无投诉</div>
        </div>
        <el-descriptions
          v-if="!!complaintsForm.id"
          title="关联投诉单"
          border
          style="margin-top: 20px"
          :column="2"
        >
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="投诉单编号"
          >
            {{ complaintsForm.code || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="投诉人"
          >
            {{ complaintsForm.name || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="联系方式"
          >
            {{ complaintsForm.phone || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="投诉类别"
          >
            {{ complaintsForm.type || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="投诉内容"
            :span="2"
          >
            <span>{{ complaintsForm.complaintsContent || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="状态"
          >
            <el-tag
              size="large"
              :type="complaintsForm.status === 1 ? 'success' : 'danger'"
              >{{ complaintsForm.status === 1 ? "已回复" : "未回复" }}</el-tag
            >
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="操作"
          >
            <el-button type="primary" link @click="handleDetail()"
              >查看详情</el-button
            >
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 按钮区域 -->
      <div class="taskInfo-btns">
        <el-button type="primary" @click="handleNext">{{
          active == 0 ? "下一页" : "上一页"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>
  
  <script setup>
import { toRefs, reactive } from "vue";
import { downloadHttp } from "@/utils";
import useUserStore from "@/store/modules/user";
import { getSchoolDocumentDetail } from "@/api/fileMamage/file";
import { useRouter, useRoute } from "vue-router";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  activeList: ["项目/干系人信息", "报障信息"],
  active: 0,
  loadingPage: false,
  isDelete: false,
  documentName: "",
  projectInfo: {},
  stakeholderList: [],
  form: {
    deviceImg: "",
    installAddressId: "",
    internalStorageNumber: 1,
    internalStorageUnit: "GB",
    diskNumber: 1,
    diskUnit: "GB",
    deviceStatus: 0,
    associatedDevice: "",
    associatedPeople: "",
    associatedDeviceList: [],
    associatedDeviceIds: [],
    associatedPeopleList: [],
    associatedPeopleIds: [],
    associatedUserIds: [],
  },
  spareList: [],
  repairForm: {
    deviceCode: "",
    remark: "",
    repairName: "",
    submittedTime: "",
    repairPhone: "",
    repairUnit: "",
    troubleImg: [],
  },
  maintainForm: {
    troubleIds: [],
    maintainTime: "",
    results: "",
    repairImg: "",
    repairFile: null,
    processTime: "", // 新增处理耗时字段
  },
  commentForm: {
    id: "",
  },
  complaintsForm: {
    id: "",
  },
  statusObj: {
    0: {
      label: "正常",
      type: "success",
    },
    1: {
      label: "维修中",
      type: "danger",
    },
    3: {
      label: "待审核",
      type: "primary",
    },
    4: {
      label: "已报废",
      type: "warning",
    },
  },
});
const {
  activeList,
  active,
  projectInfo,
  stakeholderList,
  documentName,
  complaintsForm,
  loadingPage,
  commentForm,
  isDelete,
  statusObj,
  spareList,
  repairForm,
  maintainForm,
  form,
} = toRefs(state);

function handleNext() {
  state.active = state.active == 0 ? 1 : 0;
}

// 取消/返回处理
function handleBack() {
  router.go(-1);
  proxy.$tab.closeOpenPage();
}

const handleDetail = () => {
  router.push({
    path: `/repair/repairInfo`,
    query: {
      id: state.complaintsForm.id,
      documentId: route.query.id,
    },
  });
};

// 修改 getData 方法
async function getData() {
  state.loadingPage = true;
  try {
    getSchoolDocumentDetail(route.query.id)
      .then((res) => {
        if (res.data) {
          const { documentContent, documentName } = res.data;
          if (documentContent) {
            const {
              projectInfo,
              stakeholderList,
              repairForm,
              form,
              maintainForm,
              spareList,
              commentForm,
              complaintsForm,
            } = JSON.parse(documentContent);
            Object.assign(state, {
              documentName,
              projectInfo,
              stakeholderList,
              repairForm,
              form,
              maintainForm,
              spareList,
              commentForm,
              complaintsForm,
            });
          }
        }
      })
      .finally(() => (state.loadingPage = false));
  } catch {
    state.loadingPage = false;
  }
}

onMounted(() => getData());
</script>
  
  <style scoped lang="scss">
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
.fileInfo {
  &-header {
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 2vw;
    gap: 0 10vw;
    font-size: 14px;

    &_item {
      // border: 1px solid red;
      display: flex;
      align-items: center;
      gap: 0 10px;

      &::before {
        content: "1";
        width: 2.5vw;
        height: 2.5vw;
        display: inline-block;
        text-align: center;
        line-height: 2.5vw;
        background-color: #4095e5;
        color: #fff;
        border-radius: 50%;
      }

      &.item2::before {
        content: "2";
      }

      &.active {
        color: #4095e5;
      }
    }
  }
}
.unitFlex {
  display: flex;
  gap: 0 10px;
}
.uploadlink {
  color: #4095e5;
  text-decoration: underline;
  cursor: pointer;
}
.page {
  margin: 0 !important;
  padding: 0 !important;
}
.projectInfo {
  font-size: 14px;
  &-btn {
    display: flex;
    align-items: center;
    gap: 0 10px;
    // margin-top: 10px;
    // margin-bottom: 20px;
  }
  &-title {
    margin: 20px 0;
    font-size: 16px;
    font-weight: bold;
  }
}
.taskInfo {
  margin-top: 20px;
  .avatar {
    width: 100px;
    height: 50px;
  }

  .avatar-uploader {
    width: 100px;
    height: 50px;
  }

  &-tit {
    font-size: 16px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    gap: 0 10px;
    font-weight: bold;
    margin: 20px 0;
  }

  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }

  .upload-area {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .upload-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .file-info {
      margin-left: 20px;
      display: flex;
      align-items: center;
      gap: 10px;

      span {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .repair-file {
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &-btns {
    padding: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
  