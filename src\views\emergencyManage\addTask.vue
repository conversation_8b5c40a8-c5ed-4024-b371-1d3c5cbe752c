<template>
  <div class="addEmergency app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-descriptions title="预案信息" border :column="3" v-if="planInfo">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案编号"
        >
          {{ planInfo?.id || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案类型"
        >
          {{ planInfo?.type || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案文件"
        >
          <el-button v-if="planInfo?.fileUrl" type="primary" @click="downloadPlanFile">
            点击查看
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案名称"
        >
          {{ planInfo?.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          :span="2"
          label="预案内容"
        >
          {{ planInfo?.content || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="修改时间"
        >
          {{ planInfo?.updateTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ planInfo?.createTime || "-" }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form :model="errors">
        <el-descriptions title="应急工单信息" border style="margin-top: 20px" :column="2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="应急工单编号"
          >
            <el-form-item label="" prop="number">
              <el-input
                v-model="form.taskNumber"
                style="width: 100%"
                readonly
                :placeholder="form.taskNumber || '系统自动'"
                :disabled="true"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单状态"
          >
            <el-form-item label="" prop="status">
              <el-input
                v-model="form.status"
                style="width: 100%"
                readonly
                placeholder="待上传"
                :disabled="true"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="应急工单描述"
          >
            <el-form-item label="" prop="description">
              <el-input
                v-model="form.description"
                style="width: 100%"
                type="textarea"
                :rows="3"
                placeholder="请输入工单描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="应急事件等级"
          >
            <el-form-item label="" prop="level">
              <el-select
                v-model="form.level"
                style="width: 30%"
                placeholder="请选择事件等级"
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <!-- 备件表格 -->
      <div class="addEmergency-tit">
        关联备件
        <el-button type="primary" plain icon="Plus" @click="handleSelect(0)"
          >点击选择关联备件</el-button
        >
      </div>
      <el-table :data="selectedSparePartsList" border>
        <el-table-column
          label="备件编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="code"
        />
        <el-table-column
          label="备件名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="name"
        />
        <el-table-column label="消耗数量" align="center" min-width="120px">
          <template #default="{ row }">
            <el-input-number
              v-model="row.num"
              :min="getMinQuantity(row.stock)"
              :max="getMaxQuantity(row.stock)"
              @change="(value) => handleNumChange(row, value)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="剩余库存"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="stock"
        />
        <el-table-column
          label="消耗日期"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
        >
          <template #default="{ row }">
            {{ row.consumeTime }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 人员表格 -->
      <div class="addEmergency-tit">
        关联人员
        <el-button type="primary" plain icon="Plus" @click="handleSelect(1)"
          >点击选择关联人员</el-button
        >
      </div>
      <el-table :data="selectedPersonnelList" border>
        <el-table-column
          label="工号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="workId"
        >
          <template #default="scope">
            {{ scope.row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="nickName"
        />
        <el-table-column
          label="关联时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="createTime"
        />
      </el-table>

      <div class="addEmergency-btns">
        <el-button type="primary" @click="handleSubmit" v-throttle>点击提交</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <!-- <div class="addEmergency-record">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role} 于 ${item.time} ${item.type}了${item.spareName}的现库存`
          }}
        </div>
      </div> -->
    </el-card>

    <!-- 选择备件/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      :width="title == '请选择关联备件' ? '70%' : '70%'"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-form class="search-list" :inline="true" :model="queryParams" ref="queryRef">
        <el-form-item>
          <el-input
            v-model.trim="queryParams.faultDesc"
            :placeholder="
              title == '请选择关联备件'
                ? '请输入备件编号/名称搜索'
                : '请输入工号/姓名搜索'
            "
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div v-if="tempSelectedSparePartsList.length != 0 && title == '请选择关联备件'">
        <span style="color: red">已选择备件：</span>
        <div class="t_item">
          <p v-for="item in tempSelectedSparePartsList" :key="item.id">{{ item.name }}</p>
        </div>
      </div>

      <div
        v-if="tempSelectedPersonnelList.length != 0 && title != '请选择关联备件'"
        style="padding-bottom: 10px"
      >
        <span style="color: red">已选择人员：</span>
        <div class="t_item">
          <p v-for="item in tempSelectedPersonnelList" :key="item.userId">{{ item.nickName }}</p>
        </div>
      </div>

      <!-- 备件表格 -->
      <el-table
        v-if="title == '请选择关联备件'"
        ref="tableRef"
        :data="ledgerList"
        border
        v-loading="loading"
        row-key="id"
        @select="selectBjItem"
        @select-all="selectBjAll"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" reserve-selection align="center" width="70" />
        <el-table-column
          label="备件编号"
          prop="code"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="备件名称"
          prop="name"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="备件类别"
          prop="typeName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="规格"
          prop="specifications"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column label="库存" prop="stock" show-overflow-tooltip min-width="80" />
      </el-table>

      <!-- 人员表格 -->
      <el-table
        v-if="title != '请选择关联备件'"
        ref="tablePersonRef"
        :data="ledgerList"
        border
        row-key="userId"
        v-loading="loading"
        @select="selectPersonItem"
        @select-all="selectPersonAll"
        @row-click="handleRowClick"
      >
        <el-table-column type="selection" reserve-selection align="center" width="70" />
        <el-table-column label="工号" prop="workId" show-overflow-tooltip min-width="100">
          <template #default="scope">
            {{ scope.row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="nickName"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column label="性别" prop="sex" show-overflow-tooltip min-width="90">
          <template #default="scope">
            {{ scope.row.sex === "0" ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          prop="deptName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="岗位"
          prop="postName"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.postName || "-" }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-if="title == '请选择关联备件'"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getSparePartsList"
        :pageSizes="[5, 10, 15, 20, 25, 30]"
      />

      <pagination
        v-else
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :pageSizes="[5, 10, 15, 20, 25, 30]"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { schoolPlanInfo, addSchoolPlanWorkOrder } from "@/api/emergency"; // 引入获取预案列表的接口
import { getUserList } from "@/api/system/user";
import { sparePartsPage } from "@/api/spare";
import { getMaintainList } from "@/api/distribution/member";

const router = useRouter();
const route = useRoute();

const uploadRef = ref(null);
const isEdit = ref(false);
const dialogVisible = ref(false);
const errors = ref({}); // 改为对象形式存储每个字段的错误信息

const { proxy } = getCurrentInstance();

// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  file: null,
  taskNumber: "", // 工单编号
  status: "待上传", // 工单状态
  description: "", // 工单描述
  level: "", // 应急事件等级
});

const title = ref(""); // 弹窗标题
const ledgerList = ref([]); // 备件/人员列表
const recordList = ref([]); // 变更记录列表
const queryParams = ref({
  pageNum: 1,
  pageSize: 5,
  codeAndName: "", // 添加备件编号/名称搜索字段
  nickNameAndWorkId: "", // 添加人员姓名/工号搜索字段
  faultDesc: "",
  // status: "0", // 只查询正常状态的用户
});
const total = ref(0);

// 初始化 planInfo 为空对象
const planInfo = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  fileName: "",
  fileUrl: "",
  createTime: "",
  updateTime: "",
});

const loading = ref(false); // 添加loading变量定义


const selectedSparePartsList = ref([]); // 已选择的备件列表，每项包含 id, num 等信息
const selectedPersonnelList = ref([]); // 已选择的人员列表

// 添加表格ref
const tableRef = ref(null);

// 添加多选数组和相关处理函数
const tableAllSelectedId = ref([]); // 保存所有选中项的ID
const tableAllSelectedRow = ref([]); // 保存所有选中项的完整数据

// 添加临时存储容器
const tempSelectedSparePartsList = ref([]); // 用于存储对话框中临时选中的备件
const tempSelectedPersonnelList = ref([]); // 用于存储对话框中临时选中的人员

// 添加事件等级选项
const levelOptions = [
  {
    label: "非常紧急",
    value: "非常紧急",
  },
  {
    label: "紧急",
    value: "紧急",
  },
  {
    label: "一般",
    value: "一般",
  },
  {
    label: "轻松",
    value: "轻松",
  },
];
/*  */
const handleSelect = (type) => {
  title.value = type ? "请选择关联人员" : "请选择关联备件";
  dialogVisible.value = true;

  if (title.value == "请选择关联备件") {
    // 确保创建一个新的数组实例
    tempSelectedSparePartsList.value = JSON.parse(JSON.stringify(selectedSparePartsList.value));
    // console.log('打开对话框时的临时列表:', JSON.parse(JSON.stringify(tempSelectedSparePartsList.value)));
    
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 5;
    delete queryParams.value.nickNameAndWorkId;
    
    getSparePartsList();
    
    // 在数据加载完成后设置选中状态
    nextTick(() => {
      if (tableRef.value) {
        tableRef.value.clearSelection();
        ledgerList.value.forEach(row => {
          if (tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
            tableRef.value.toggleRowSelection(row, true);
          }
        });
      }
    });
  } else {
    // 人员选择时,创建临时列表的副本
    tempSelectedPersonnelList.value = JSON.parse(JSON.stringify(selectedPersonnelList.value));
    // console.log('打开人员对话框时的临时列表:', JSON.parse(JSON.stringify(tempSelectedPersonnelList.value)));
    
    queryParams.value.pageNum = 1;
    queryParams.value.pageSize = 5;
    delete queryParams.value.codeAndName;
    delete queryParams.value.faultDesc;

    return getList().then(() => {  // 确保返回 Promise
      nextTick(() => {
        if (proxy.$refs.tablePersonRef) {
          proxy.$refs.tablePersonRef.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
              proxy.$refs.tablePersonRef.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  }
};

// 获取备件列表数据
const getSparePartsList = () => {
  loading.value = true;
  const params = {
    codeAndName: queryParams.value.codeAndName,
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    status: "0",
    faultDesc: queryParams.value.faultDesc,
  };
  
  if (title.value == "请选择关联备件") {
    return sparePartsPage(params)
      .then((res) => {
        loading.value = false;
        if (res.code == 200) {
          console.log("获取备件列表成功:", res.data);
          
          ledgerList.value = res.data.records;
          total.value = res.data.total;
          
          // 在数据加载完成后设置选中状态
          nextTick(() => {
            if (tableRef.value) {
              tableRef.value.clearSelection();
              ledgerList.value.forEach(row => {
                if (tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
                  tableRef.value.toggleRowSelection(row, true);
                }
              });
            }
          });
        }
        return res;
      })
      .catch((err) => {
        loading.value = false;
        console.error("获取备件列表失败:", err);
        return Promise.reject(err);
      });
  } else {
    return getList();
  }
};

// 获取人员列表数据
const getList = () => {
  loading.value = true;
  console.log("获取人员列表参数:", queryParams.value);
  const params = {
    current: queryParams.value.pageNum,
    size: queryParams.value.pageSize,
    nickNameAndWorkId: queryParams.value.faultDesc,
  };
  
  return getMaintainList(params)
    .then((res) => {
      loading.value = false;
      if (res.code == 200) {
        console.log("获取人员列表成功:", res.data);
        ledgerList.value = res.data.records;
        total.value = res.data.total;
        
        // 在数据加载完成后设置选中状态
        nextTick(() => {
          if (proxy.$refs.tablePersonRef) {
            proxy.$refs.tablePersonRef.clearSelection();
            ledgerList.value.forEach(row => {
              if (tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
                proxy.$refs.tablePersonRef.toggleRowSelection(row, true);
              }
            });
          }
        });
      }
      return res;
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取人员列表失败:", err);
      return Promise.reject(err);
    });
};

// 文件上传前的验证
const beforeUpload = (file) => {
  const allowedTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
  ];
  const allowedExtensions = ["doc", "docx", "xls", "xlsx", "txt"];
  const extension = file.name.split(".").pop().toLowerCase();
  const isValidType =
    allowedTypes.includes(file.type) || allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  errors.value.file = "";

  if (!isValidType) {
    errors.value.file = "只能上传doc、docx、xls、xlsx、txt格式的文件！";
    return false;
  }
  if (!isLt20M) {
    errors.value.file = "文件大小不能超过20MB！";
    return false;
  }
  return true;
};

// 文件改变时的处理
const handleFileChange = (file) => {
  if (file) {
    form.value.file = file.raw;
    errors.value.file = ""; // 清除文件错误提示
  }
};

// 返回
const handleBack = () => {
  router.back();
};

// 搜索
const handleQuery = () => {
  loading.value = true;
  queryParams.value.pageNum = 1;
  if (title.value === "请选择关联备件") {
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList().then(() => {
      // 在数据加载完成后恢复选中状态
      nextTick(() => {
        if (tableRef.value) {
          tableRef.value.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
              tableRef.value.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  } else {
    queryParams.value.nickNameAndWorkId = queryParams.value.faultDesc;
    getList();
  }
};

// 重置
const resetQuery = () => {
  queryParams.value = {
    pageNum: 1,
    pageSize: 5,
    codeAndName: "",
    faultDesc: "",
    status: "0",
  };

  if (title.value === "请选择关联备件") {
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        if (tableRef.value) {
          tableRef.value.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
              tableRef.value.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  } else {
    getList();
  }
};

// 搜索函数
const handleSearch = () => {
  queryParams.value.current = 1;
  if (title.value === "请选择关联备件") {
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        if (tableRef.value) {
          tableRef.value.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
              tableRef.value.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  } else {
    getList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        if (proxy.$refs.tablePersonRef) {
          proxy.$refs.tablePersonRef.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
              proxy.$refs.tablePersonRef.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  }
};

// 提交选择函数
const submitForm = () => {
  const currentTime = new Date()
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
    })
    .replace(/\//g, "-");

  if (title.value === "请选择关联备件") {
    // 对于备件，保留已有记录的消耗时间，新记录才设置当前时间
    selectedSparePartsList.value = tempSelectedSparePartsList.value.map(item => {
      const existingItem = selectedSparePartsList.value.find(existing => existing.id === item.id);
      return {
        ...item,
        consumeTime: existingItem ? existingItem.consumeTime : currentTime
      };
    });
  } else {
    // 对于人员，保留已有记录的关联时间，新记录才设置当前时间
    selectedPersonnelList.value = tempSelectedPersonnelList.value.map(item => {
      const existingItem = selectedPersonnelList.value.find(existing => existing.userId === item.userId);
      return {
        ...item,
        createTime: existingItem ? existingItem.createTime : currentTime
      };
    });
  }
  dialogVisible.value = false;
};

// 取消选择函数
const handleCancel = () => {
  // console.log('取消前的临时列表:', JSON.parse(JSON.stringify(tempSelectedSparePartsList.value)));
  dialogVisible.value = false;
  // 取消时恢复临时列表为已确认的选择
  tempSelectedSparePartsList.value = JSON.parse(JSON.stringify(selectedSparePartsList.value));
  // console.log('取消后恢复的临时列表:', JSON.parse(JSON.stringify(tempSelectedSparePartsList.value)));
};

// 获取预案详情
const getPlanDetail = async (planId) => {
  try {
    const res = await schoolPlanInfo(planId);
    if (res.code == 200) {
      const currentPlan = res.data;
      planInfo.value = {
        id: currentPlan.code || "",
        name: currentPlan.name || "",
        type: currentPlan.type || "",
        content: currentPlan.content || "",
        fileName: currentPlan.fileName || "",
        fileUrl: currentPlan.fileUrl || "",
        createTime: currentPlan.createTime || "",
        updateTime: currentPlan.updateTime || "",
      };
    } else {
      ElMessage.warning("未找到相关预案信息");
    }
  } catch (error) {
    console.error("获取预案详情失败:", error);
    ElMessage.error("获取预案详情失败");
  }
};

// 提交工单函数
const handleSubmit = async () => {
  // 基本表单验证
  if (!form.value.description) {
    ElMessage.warning("请输入工单描述");
    return;
  }

  if (!form.value.level) {
    ElMessage.warning("请选择应急事件等级");
    return;
  }

  if (selectedPersonnelList.value.length === 0) {
    ElMessage.warning("请选择关联人员");
    return;
  }

  // 添加备件验证
  if (selectedSparePartsList.value.length === 0) {
    ElMessage.warning("请选择关联备件");
    return;
  }

  try {
    // 构造备件详情数据
    const sparePartsDetails = selectedSparePartsList.value.map((item) => ({
      sparePartsId: item.id, // 备件ID
      type: 0, // 固定值：出库
      resourceType: 2, // 固定值：应急事件
      resource: "应急事件", // 固定值
      num: item.num, // 消耗数量
    }));

    const params = {
      planId: route.query.planId,
      remark: form.value.description,
      level: form.value.level, // 添加事件等级参数
      userIds: selectedPersonnelList.value.map((person) => person.userId),
      sparePartsDetails, // 添加备件详情
    };

    console.log(params, "传参");

    const res = await addSchoolPlanWorkOrder(params);
    if (res.code === 200) {
      ElMessage.success("工单创建成功");
      router.push("/emergencyManage/TaskCenter");
    } else {
      // ElMessage.error(res.msg || '工单创建失败');
    }
  } catch (error) {
    // console.error('创建工单失败:', error);
    // ElMessage.error('创建工单失败');
  }
};

// 添加备件消耗数量修改方法
const handleNumChange = (row, value) => {
  const index = selectedSparePartsList.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    selectedSparePartsList.value[index].num = value;
  }
};

// 添加一个方法来获取备件的最小数量
const getMinQuantity = (stock) => {
  // 允许设置为0，这样即使库存为0也能选择
  return 0;
};

// 添加一个方法来获取备件的最大数量
const getMaxQuantity = (stock) => {
  // 确保stock是数字且不小于0
  return Math.max(0, stock || 0);
};

// 下载文件方法
const downloadPlanFile = () => {
  if (!planInfo.value?.fileUrl) {
    ElMessage.warning("预案文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let fileUrl = planInfo.value.fileUrl;
    if (fileUrl.startsWith("http:")) {
      fileUrl = fileUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = fileUrl;
    link.setAttribute("download", planInfo.value.fileName || "预案文件");
    link.setAttribute("target", "_blank"); // 添加target属性
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 分页处理函数
const handlePagination = (val) => {
  queryParams.value.current = val.page;
  queryParams.value.size = val.limit;

  if (title.value === "请选择关联备件") {
    if (queryParams.value.faultDesc) {
      queryParams.value.codeAndName = queryParams.value.faultDesc;
    }
    getSparePartsList();
  } else {
    if (queryParams.value.faultDesc) {
      queryParams.value.nickNameAndWorkId = queryParams.value.faultDesc;
    }
    getList().then(() => {
      // 确保在数据加载完成后重新设置选中状态
      nextTick(() => {
        if (proxy.$refs.tablePersonRef) {
          proxy.$refs.tablePersonRef.clearSelection();
          ledgerList.value.forEach(row => {
            if (tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
              proxy.$refs.tablePersonRef.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  }
};

// handleRowClick函数
const handleRowClick = (row) => {
  // console.log('行点击事件 - 当前行:', row);
  if (title.value === "请选择关联备件") {
    // 获取当前行的选中状态
    const isSelected = tableRef.value.getSelectionRows().some(item => item.id === row.id);
    
    // 切换选中状态
    tableRef.value.toggleRowSelection(row);
    
    // 手动调用选择处理函数
    if (!isSelected) {
      // 如果之前未选中，现在应该添加到临时列表
      if (!tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
        const newItem = {
          ...row,
          num: 1,
          stock: row.stock,
          code: row.code,
          name: row.name,
          consumeTime: new Date()
            .toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            })
            .replace(/\//g, "-")
        };
        tempSelectedSparePartsList.value.push(newItem);
        // console.log('行点击添加后的临时列表:', JSON.parse(JSON.stringify(tempSelectedSparePartsList.value)));
      }
    } else {
      // 如果之前已选中，现在应该从临时列表中移除
      tempSelectedSparePartsList.value = tempSelectedSparePartsList.value.filter(
        item => item.id !== row.id
      );
      // console.log('行点击移除后的临时列表:', JSON.parse(JSON.stringify(tempSelectedSparePartsList.value)));
    }
  } else {
    // 获取当前行的选中状态
    const isSelected = tempSelectedPersonnelList.value.some(item => item.userId === row.userId);
    
    if (!isSelected) {
      // 如果之前未选中，现在应该添加到临时列表
      const newItem = {
        ...row,
        createTime: new Date()
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
          .replace(/\//g, "-")
      };
      tempSelectedPersonnelList.value.push(newItem);
      proxy.$refs.tablePersonRef.toggleRowSelection(row, true);
      // console.log('人员行点击添加后的临时列表:', JSON.parse(JSON.stringify(tempSelectedPersonnelList.value)));
    } else {
      // 如果之前已选中，现在应该从临时列表中移除
      tempSelectedPersonnelList.value = tempSelectedPersonnelList.value.filter(
        item => item.userId !== row.userId
      );
      proxy.$refs.tablePersonRef.toggleRowSelection(row, false);
      // console.log('人员行点击移除后的临时列表:', JSON.parse(JSON.stringify(tempSelectedPersonnelList.value)));
    }
  }
};

// 表格组件的事件绑定
const selectBjItem = (selection, row) => {
  if (selection.includes(row)) {
    if (!tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
      const newItem = {
        ...row,
        num: 1,
        stock: row.stock,
        code: row.code,
        name: row.name
      };
      tempSelectedSparePartsList.value.push(newItem);
    }
  } else {
    tempSelectedSparePartsList.value = tempSelectedSparePartsList.value.filter(
      item => item.id !== row.id
    );
  }
};

const selectBjAll = (selection) => {
  if (selection.length > 0) {
    selection.forEach(row => {
      if (!tempSelectedSparePartsList.value.some(item => item.id === row.id)) {
        const newItem = {
          ...row,
          num: 1,
          stock: row.stock
        };
        tempSelectedSparePartsList.value.push(newItem);
      }
    });
  } else {
    const currentPageIds = ledgerList.value.map(item => item.id);
    tempSelectedSparePartsList.value = tempSelectedSparePartsList.value.filter(
      item => !currentPageIds.includes(item.id)
    );
  }
};

// 人员表格的选择事件处理函数
const selectPersonItem = (selection, row) => {
  const isSelected = selection.includes(row);
  
  if (isSelected) {
    if (!tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
      const newItem = {
        ...row
      };
      tempSelectedPersonnelList.value.push(newItem);
    }
  } else {
    tempSelectedPersonnelList.value = tempSelectedPersonnelList.value.filter(
      item => item.userId !== row.userId
    );
  }
};

const selectPersonAll = (selection) => {
  if (selection.length > 0) {
    selection.forEach(row => {
      if (!tempSelectedPersonnelList.value.some(item => item.userId === row.userId)) {
        const newItem = {
          ...row
        };
        tempSelectedPersonnelList.value.push(newItem);
      }
    });
  } else {
    const currentPageIds = ledgerList.value.map(item => item.userId);
    tempSelectedPersonnelList.value = tempSelectedPersonnelList.value.filter(
      item => !currentPageIds.includes(item.userId)
    );
  }
};

onMounted(() => {
  const planId = route.query.planId;
  if (planId) {
    getPlanDetail(planId);
  }
});
</script>

<style lang="scss" scoped>
.addEmergency {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
:deep(.el-descriptions__body) {
  .el-descriptions__table.is-bordered .el-descriptions__cell {
    // padding: 30px 11px;
    /* min-width: 120px;
    max-width: 500px; */
    word-break: break-all; // 让内容超出列宽时自动换行显示
    word-wrap: break-word;
  }
}

.t_item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  // gap: 20px;

  p {
    padding: 0;
    margin: 0;
    margin-right: 10px;
    margin-top: 5px;
  }
}
</style>
