<template>
  <div class="operation app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div
          class="card-header page-header"
          :style="{ justifyContent: 'flex-start' }"
        >
          {{ useUserStore().corpName || "教职工管理" }}（<span
            :style="{
              display: 'inline-block',
              marginRight: '5px',
              color: total > teacherLimitTotal ? 'red' : '',
            }"
            >{{ total }}人</span
          >
          / {{ teacherLimitTotal }}人）
        </div>
      </template>
      <div class="operation-main">
        <div class="operation-main_left">
          <div class="operation-title" ref="topDivRef">部门</div>
          <el-scrollbar class="scrollbar" :style="{ height: posHeight }">
            <el-tree
              style="width: 100%"
              show-checkbox
              node-key="deptId"
              expand-on-click-node
              default-expand-all
              check-strictly
              :data="deptTreeList"
              :props="deptProps"
              @check="deptTreeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div>

        <div class="operation-main_right" ref="flexRightBox">
          <el-form
            ref="queryRef"
            :model="queryParams"
            :inline="true"
            class="search-list"
          >
            <el-form-item prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入姓名查询"
                clearable
                @keyup.enter="handleQuery"
                @clear="handleQuery"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item prop="phone">
              <el-input
                v-model="queryParams.phone"
                placeholder="请输入联系方式查询"
                clearable
                @keyup.enter="handleQuery"
                @clear="handleQuery"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item prop="employeesPostId">
              <el-select
                v-model="queryParams.employeesPostId"
                placeholder="全部职工岗位"
                clearable
                filterable
                style="width: 200px"
                @change="handleQuery"
              >
                <el-option
                  v-for="item in employeesPost"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12 btn-list">
            <el-col :span="1.5">
              <el-button
                plain
                color="#626aef"
                icon="ChatDotSquare"
                @click="handleConfig"
                >解锁提示配置</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button type="primary" plain icon="Plus" @click="handleAdd"
                >添加教职工</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Upload"
                @click="handleBatchImport"
                >批量导入</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Download"
                @click="handleBatchExport"
                >批量导出</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Delete"
                @click="handleBatchDel"
                :disabled="tableAllSelectedId.length < 1"
                >批量删除</el-button
              >
            </el-col>
          </el-row>

          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
          >
            <el-table-column
              type="selection"
              label=""
              width="80"
              align="center"
            />
            <el-table-column
              label="姓名"
              minWidth="120px"
              prop="nickName"
              show-overflow-tooltip
            />
            <el-table-column
              label="是否为管理员"
              align="center"
              min-width="120"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <div class="freeze-status">
                  <el-tag
                    effect="light"
                    :type="
                      row.roleKey.indexOf('maintainManage') != -1
                        ? 'warning'
                        : row.roleKey.indexOf('maintain') != -1
                        ? 'primary'
                        : 'info'
                    "
                    >{{
                      row.roleKey.indexOf("maintainManage") != -1
                        ? "高级管理员"
                        : row.roleKey.indexOf("maintain") != -1
                        ? "管理员"
                        : "非管理员"
                    }}</el-tag
                  >
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="手机号"
              align="center"
              minWidth="120px"
              prop="phoneNumber"
              show-overflow-tooltip
            />
            <el-table-column
              label="身份证号"
              align="center"
              minWidth="170px"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.idCard || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="设备权限"
              align="center"
              minWidth="120px"
              prop="devicePermission"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.devicePermission?.join("、") || "-" }}
              </template>
            </el-table-column>
            <!-- <el-table-column
              label="部门"
              align="center"
              minWidth="120px"
              prop="deptName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.deptName || "-" }}
              </template>
            </el-table-column> -->
            <el-table-column
              label="职工岗位"
              align="center"
              minWidth="120px"
              prop="employeesPostName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.employeesPostName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="入库时间"
              align="center"
              minWidth="160px"
              prop="createTime"
              show-overflow-tooltip
            />
            <el-table-column label="状态" align="center" minWidth="80px">
              <template #default="{ row }">
                <el-tag :type="statusObj[row.teacherStatus || 0].type">
                  {{ statusObj[row.teacherStatus || 0].name }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="380"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  v-if="scope.row.roleKey.indexOf('maintain') == -1"
                  type="primary"
                  icon="Edit"
                  @click.stop="handleEdit(scope)"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope)"
                  >删除</el-button
                >
                <el-button
                  link
                  type="success"
                  icon="Setting"
                  v-if="scope.row.roleKey.indexOf('maintain') == -1"
                  @click.stop="handleUse(scope.row)"
                  >修改设备使用权</el-button
                >
                <el-button
                  link
                  type="warning"
                  icon="Key"
                  v-if="scope.row.roleKey.indexOf('maintain') == -1"
                  @click.stop="handleReset(scope)"
                  >重置密码</el-button
                >
                <!-- <span v-else style="color: gray">管理员不可操作</span> -->
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>

      <!-- 解锁失败提示语 -->
      <el-dialog
        class="custom-dialog"
        v-model="configDialogVisible"
        title="请输入解锁失败提示语"
        width="500"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancelConfig"
        align-center
      >
        <el-form ref="configRef" :model="configForm" label-width="auto">
          <el-form-item
            prop="configValue"
            label=""
            required
            :rules="[
              { required: true, message: '请输入提示语', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="configForm.configValue"
              maxlength="30"
              type="textarea"
              show-word-limit
              placeholder="请输入提示语"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancelConfig">取消</el-button>
            <el-button type="primary" v-throttle @click="handleSubmitConfig"
              >确定</el-button
            >
          </div>
        </template>
      </el-dialog>

      <!-- 添加 / 编辑教职工 -->
      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        :title="title"
        width="500"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancel"
      >
        <el-form
          ref="formRef"
          :model="teacherInfo"
          :rules="rules"
          label-width="auto"
        >
          <el-form-item prop="nickName" label="姓名" required>
            <el-input
              v-model="teacherInfo.nickName"
              maxlength="25"
              placeholder="请输入姓名"
            />
          </el-form-item>
          <el-form-item prop="phoneNumber" label="手机号">
            <el-input
              v-model="teacherInfo.phoneNumber"
              :disabled="!!teacherInfo.userId"
              placeholder="请输入手机号"
              @blur="checkMaintain"
            />
          </el-form-item>
          <el-form-item label="职工岗位" prop="employeesPostId">
            <el-select
              v-model="teacherInfo.employeesPostId"
              placeholder="请选择职工岗位"
              clearable
              filterable
              style="width: 100%"
              :disabled="isMaintain"
            >
              <el-option
                v-for="item in employeesPost"
                :key="item"
                :label="item.name"
                :value="item.id"
                :disabled="item.disabled"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="deptId" label="部门" required>
            <el-tree-select
              v-model="teacherInfo.deptId"
              placeholder="请选择上级部门"
              :data="deptTreeList_disabled"
              :disabled="!canEditDept"
              :props="{
                value: 'deptId',
                label: 'deptName',
                children: 'children',
                disabled: 'disabled',
              }"
              clearable
              check-strictly
            />
          </el-form-item>
          <el-form-item
            v-if="!!teacherInfo.userId"
            label="密码"
            prop="password"
            required
          >
            <el-input
              v-model="teacherInfo.password"
              placeholder="请输入密码"
              type="password"
              show-password
              maxlength="20"
              clearable
            />
          </el-form-item>
          <el-form-item prop="idCard" label="身份证号">
            <el-input
              v-model="teacherInfo.idCard"
              placeholder="请输入身份证号"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" v-throttle @click="handleSubmit"
              >确定</el-button
            >
          </div>
        </template>
      </el-dialog>

      <!-- 修改设备使用权 -->
      <el-dialog
        class="custom-dialog"
        v-model="useDialogVisible"
        title="修改设备使用权"
        width="500"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancelUse"
      >
        <el-form ref="useRef" :model="teacherInfo" label-width="auto">
          <el-form-item label="职工岗位" prop="employeesPostId">
            <el-select
              v-model="teacherInfo.employeesPostId"
              placeholder="-"
              clearable
              filterable
              disabled
              style="width: 100%"
            >
              <el-option
                v-for="item in employeesPost"
                :key="item"
                :label="item.name"
                :value="item.id"
              />
            </el-select>
          </el-form-item>
          <div class="mt-3">
            <div class="mb-3 label">请选择设备</div>
            <div class="tree-container">
              <el-tree
                ref="deviceTreeRef"
                :data="deviceTreeData"
                :props="defaultProps"
                show-checkbox
                node-key="id"
                default-expand-all
                @check="handleDeviceCheck"
              />
            </div>
          </div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancelUse">取消</el-button>
            <el-button type="primary" v-throttle @click="handleSubmitUse"
              >确定</el-button
            >
          </div>
        </template>
      </el-dialog>

      <el-dialog
        class="custom-dialog"
        v-model="exportDialogVisible"
        title="批量导出"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancelExport"
      >
        <el-form
          ref="exportRef"
          :model="exportForm"
          :rules="exportRules"
          label-width="auto"
        >
          <el-form-item prop="time" label="导出日期" required>
            <el-date-picker
              v-model="exportForm.time"
              type="daterange"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              start-placeholder="开始时间"
              :disabled-date="disabledDateFn"
              end-placeholder="结束时间"
              @change="handleQueryExport"
              @clear="handleQueryExport"
              style="width: 100%"
            />
          </el-form-item>
          <div style="text-align: right">共{{ exportForm.num }}条数据</div>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancelExport">取消</el-button>
            <el-button type="primary" @click="handleExport" v-throttle
              >确定</el-button
            >
          </div>
        </template>
      </el-dialog>

      <el-dialog
        class="custom-dialog"
        v-model="importDialogVisible"
        title="批量导入"
        width="400px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="closeDialog"
      >
        <div class="import-steps" style="padding: 0">
          <div class="step">
            <div class="step-title">选择部门：</div>
            <el-tree-select
              v-model="parentId"
              placeholder="请选择上级部门"
              :data="deptTreeList_disabled"
              :props="{
                value: 'deptId',
                label: 'deptName',
                children: 'children',
                disabled: 'disabled',
              }"
              clearable
              check-strictly
            />
          </div>
          <div class="step">
            <div class="step-title">第一步：下载模板</div>
            <el-button type="primary" @click="downloadTemplate" v-throttle
              >点击下载</el-button
            >
          </div>
          <div class="step">
            <div class="step-title">第二步：填写信息后上传</div>
            <el-upload
              ref="uploadTemplate"
              :headers="headers"
              :action="actionUrl"
              :data="{
                deptId: parentId,
              }"
              :limit="1"
              :file-list="fileList"
              :on-exceed="handleExceed"
              :auto-upload="false"
              :before-upload="beforeUploadFile"
              :on-change="beforeTemplate"
              :on-success="handleSuccessFile"
              :on-remove="handleRemoveFile"
              accept=".xlsx,.xls"
            >
              <template #trigger>
                <el-button type="primary">点击上传</el-button>
              </template>
              <template #tip>
                <div class="el-upload__tip text-red">
                  温馨提示：支持xls，xlsx格式
                </div>
              </template>
            </el-upload>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleClose">关闭</el-button>
            <el-button type="primary" @click="handleImport" v-throttle
              >导入</el-button
            >
          </div>
        </template>
      </el-dialog>
    </el-card>

    <ExportRes
      :data="exportRes"
      :open="exportResultOpen"
      @setOpen="
        (val) => {
          exportResultOpen = val;
        }
      "
    />
  </div>
</template>

<script setup name="operation">
import ExportRes from "@/components/ExportRes";
import { useRoute } from "vue-router";
import { listPost } from "@/api/system/post_new";
import useUserStore from "@/store/modules/user";
import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";
import {
  treeToArray,
  treeFindPath,
  findIndexInObejctArr,
  downloadBlob,
} from "@/utils";
import { getToken } from "@/utils/auth";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import { listTree } from "@/api/system/distribution";
import {
  getTeacherList,
  getTeacherExportNum,
  exportTeacherStaff,
  teacherTemplate,
  delTeacher,
  updateTeacher,
  addTeacher,
  teacherLimit,
  addCustomPrompt,
  getCustomPrompt,
  editCustomPrompt,
  addSchoolDeviceUse,
  checkTeacherLimit,
} from "@/api/system/user";
import { listRole } from "@/api/system/role";
import {
  getEmployeesPostList,
  resetPassword,
  getTeacherInfo,
  isMaintainCheck,
} from "@/api/teacher/index";
import { ElMessage, ElMessageBox, ElLoading } from "element-plus";
import { devicePage } from "@/api/mediaTeach/ledger";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
let resizeObserver = null;
const posHeight = ref("calc(100vh - 238px)");
const flexRightBox = ref(null);
const actionUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
);
const headers = ref({ Authorization: "Bearer " + getToken() });
const parentId = ref("");
const state = reactive({
  roleList: [],
  canEditDept: true,
  expandedKeys: [],
  isMaintain: false,
  useRef: null,
  useDialogVisible: false,
  configRef: null,
  configDialogVisible: false,
  isAddConfig: false,
  configForm: {
    configName: "自定义锁屏提示",
    configKey: "customPrompt",
    configValue: "",
  },
  teacherLimitTotal: 1000,
  exportRes: {},
  exportResultOpen: false,
  uploadTemplate: null,
  fileList: [],
  tableRef: null,
  formRef: null,
  peopleNum: 0,
  title: "",
  dialogVisible: false,
  exportDialogVisible: false,
  importDialogVisible: false,
  total: 0,
  loading: false,
  exportRef: null,
  exportForm: {
    time: [],
    num: 0,
    startTime: "",
    endTime: "",
  },
  teacherInfo: {
    name: "",
    deptId: "",
    phoneNumber: "",
    password: "",
    idCard: "",
    deviceCode: "",
    employeesPostId: "",
    selectedDeviceIds: [],
  },
  deptResult: {
    names: [],
    ids: [],
  },
  deptList: [],
  deptTreeList: [],
  deptTreeList_disabled: [],
  tableList: [],
  tableList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  postList: [],
  employeesPost: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: "",
    phone: "",
    nickNameAndPhone: "",
    employeesPostId: "",
    deptIds: [],
    // roleKey: ["teacherStaff"],
  },
  exportRules: {
    time: [
      {
        required: true,
        type: "array",
        min: 1,
        message: "请选择时间范围",
        trigger: ["blur", "change"],
      },
    ],
  },
  rules: {
    nickName: [
      { required: true, message: "用户姓名不能为空", trigger: "blur" },
      {
        pattern: /^[\u4e00-\u9fa5]{1,25}$/,
        message: "用户姓名只能输入中文汉字",
        trigger: ["blur", "change"],
      },
    ],
    phoneNumber: [
      { required: true, message: "手机号不能为空", trigger: "blur" },
      {
        pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
        message: "请输入正确的手机号码",
        trigger: "blur",
      },
    ],
    password: [
      { required: true, validator: validNewPwd, trigger: ["blur", "change"] },
    ],
    deptId: [{ required: true, message: "部门不能为空", trigger: "blur" }],
    employeesPostId: [
      { required: true, message: "职工岗位不能为空", trigger: "blur" },
    ],
    idCard: [
      { required: false, message: "身份证号不能为空", trigger: "blur" },
      {
        pattern:
          /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(10|11|12))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/,
        message: "请输入正确的身份证号",
        trigger: "blur",
      },
    ],
  },
  deptProps: {
    children: "children",
    label: "deptName",
    value: "deptId",
    disabled: "disabled",
    class: "treeNode",
  },
  statusObj: {
    0: {
      type: "success",
      name: "正常",
    },
    1: {
      type: "danger",
      name: "停用",
    },
  },
  deviceTreeData: [],
  defaultProps: {
    children: "children",
    label: "label",
    disabled: "disabled",
  },
});
const {
  roleList,
  canEditDept,
  isMaintain,
  expandedKeys,
  useRef,
  useDialogVisible,
  isAddConfig,
  statusObj,
  configRef,
  configDialogVisible,
  configForm,
  teacherLimitTotal,
  exportRes,
  exportResultOpen,
  importDialogVisible,
  uploadTemplate,
  exportRules,
  fileList,
  tableRef,
  exportDialogVisible,
  exportRef,
  exportForm,
  deptList,
  deptResult,
  deptTreeList_disabled,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableRadio,
  tableList_all,
  peopleNum,
  formRef,
  title,
  deptTreeList,
  teacherInfo,
  rules,
  tableList,
  postList,
  employeesPost,
  queryParams,
  dialogVisible,
  loading,
  total,
  deptProps,
  deviceTreeData,
  defaultProps,
} = toRefs(state);

/** 查询角色列表 */
function getRoleList() {
  listRole({ pageNum: 1, pageSize: 10, status: "0" }).then((response) => {
    roleList.value = response.data.rows;
    console.log("角色列表", roleList.value);
  });
}
getRoleList();

const checkMaintain = () => {
  console.log(state.teacherInfo);
  if (!!state.teacherInfo.phoneNumber) {
    isMaintainCheck({ phone: state.teacherInfo.phoneNumber }).then((res) => {
      console.log("手机号验证", res);
      if (res.code == 200 && res.data && res.data.isMaintain) {
        const { nickName, employeesPostId, deptId, isMaintain } = res.data;
        state.isMaintain = true;
        teacherInfo.value.nickName = nickName || "";
        teacherInfo.value.employeesPostId = employeesPostId + "" || "";
        teacherInfo.value.deptId = deptId || "";
        state.canEditDept = isMaintain && !deptId;
      } else {
        state.isMaintain = false;
        state.canEditDept = true;
      }
    });
  }
};

function validNewPwd(rule, value, callback) {
  let reg = /^(?=.*[0-9])(?=.*[a-zA-Z]).+$/g;
  if (!value) {
    callback(new Error("密码不能为空"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入包含字母+数字"));
  } else if (value.length < 6) {
    callback(new Error("密码不能小于6位"));
  } else {
    callback();
  }
}

async function handleUse(row) {
  await getDeptList();
  console.log(row);
  state.teacherInfo = JSON.parse(JSON.stringify(row));
  state.useDialogVisible = true;

  // 如果有已选设备，获取对应的节点ID
  if (row.devicePermission) {
    console.log("state.deviceTreeData", state.deviceTreeData);
    const deviceCodes = row.devicePermission.filter((item) => !!item);
    const defaultDevicePermission = row.defaultDevicePermission;
    const selectedIds = [];

    // 遍历树形数据查找对应的节点ID
    const findDeviceIds = (nodes) => {
      nodes.forEach((node) => {
        if (node.children?.length < 1 || !node.children) {
          const nodeDeviceCode = node.label.split("-")[1] ?? node.label;
          console.log(
            deviceCodes,
            nodeDeviceCode,
            deviceCodes.includes(nodeDeviceCode),
            selectedIds
          );
          if (deviceCodes.includes(nodeDeviceCode)) {
            selectedIds.push(node.id);
            console.log(nodeDeviceCode, selectedIds);
          }
          if (
            defaultDevicePermission &&
            defaultDevicePermission.includes(nodeDeviceCode)
          ) {
            node.disabled = true;
          } else {
            node.disabled = false;
          }
        } else if (node.children.length > 0) {
          findDeviceIds(node.children);
        }
      });
    };

    findDeviceIds(state.deviceTreeData[0].children);
    state.teacherInfo.selectedDeviceIds = selectedIds;
    state.teacherInfo.deviceCode = deviceCodes;
  } else {
    state.teacherInfo.selectedDeviceIds = [];
  }
  nextTick(() => {
    deviceTreeRef.value.setCheckedKeys(
      state.teacherInfo.selectedDeviceIds || []
    );
  });
}

function handleSubmitUse() {
  if (!state.teacherInfo.deviceCode?.length) {
    proxy.$modal.msgWarning("请选择设备");
    return;
  }
  const { userId, nickName, idCard, phoneNumber } = state.teacherInfo;
  const params = {
    deviceCode: state.teacherInfo.deviceCode || [],
    userInfos: [{ userId, nickName, idCard, phone: phoneNumber }],
    isRepeated: true,
    isMultipleCodes: true,
  };
  console.log("修改权限传参", params);
  addSchoolDeviceUse(params).then((res) => {
    if (res.code == 200) {
      proxy.$modal.msgSuccess("操作成功");
      state.useDialogVisible = false;
      getList();
    }
  });
}

function handleCancelConfig() {
  proxy.resetForm("configRef");
  state.configDialogVisible = false;
}

function handleSubmitConfig() {
  console.log(configForm.value);
  state.configRef.validate((valid) => {
    if (valid) {
      if (isAddConfig.value) {
        addCustomPrompt(configForm.value).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess("操作成功");
            handleCancelConfig();
          }
        });
      } else {
        editCustomPrompt(configForm.value).then((res) => {
          if (res.code == 200) {
            proxy.$modal.msgSuccess("操作成功");
            handleCancelConfig();
          }
        });
      }
    }
  });
}

function handleConfig() {
  getCustomPrompt().then((res) => {
    console.log(res);
    if (res.code == 200) {
      configForm.value.configValue = res.data.customPrompt;
      isAddConfig.value = !res.data;
      state.configDialogVisible = true;
    }
  });
}

function handleInputName(value) {
  // 使用正则表达式匹配非中文字符并将其替换为空字符串
  state.teacherInfo.nickName = value.replace(/[^\u4e00-\u9fa5]/g, "");
}

// 添加获取职工岗位列表函数
async function employeesPostList() {
  await getEmployeesPostList({ current: 1, pageSize: 999999 }).then((resp) => {
    console.log("职工岗位列表", resp);
    if (resp.code == 200) {
      employeesPost.value = resp.data?.records.map((item) => {
        item.disabled = ["管理员", "总管理员"].indexOf(item.name) != -1;
        return item;
      });
    }
  });
}

const beforeTemplate = (file) => {
  console.log(file, "change");
  let fileType = [
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "application/vnd.ms-excel",
  ];
  let index = fileType.findIndex((item) => item == file.raw.type);

  if (file.status == "ready") {
    if (index == -1) {
      fileList.value = [];
      proxy.$modal.msgWarning("仅支持xls，xlsx格式");
      return;
    }

    fileList.value = [file];
  }
};

const beforeUploadFile = (file) => {
  proxy.$modal.loading();
};

// 移除文件
const handleRemoveFile = () => {
  state.exportRes = {};
};

// 弹窗关闭
const closeDialog = () => {
  parentId.value = "";
  // handleRemoveFile();
  fileList.value = [];
};

// 文件上传成功
const handleSuccessFile = (res) => {
  console.log(res, "上传成功");
  proxy.$modal.closeLoading();

  if (res.code == 500) {
    importDialogVisible.value = false;
    proxy.$modal.msgError(res.msg);
    return;
  }
  state.exportRes = JSON.parse(sm2Decrypt(res.data));
  console.log("导入返回的数据", state.exportRes);
  let { errorCount, successMsg } = state.exportRes;
  if (errorCount == 0 && !successMsg) {
    proxy.$modal.msgSuccess("导入成功");
  } else {
    state.exportResultOpen = true;
  }

  fileList.value = [];
  importDialogVisible.value = false;
  getList();
};

// 清空文件上传列表
const handleExceed = (files) => {
  uploadTemplate.value.clearFiles();
};

function downloadTemplate() {
  proxy.$modal.loading();
  // teacherTemplate({ modelName: "import_teacherStaff" }).then((response) => {
  //   downloadBlob(response, "application/vnd.ms-excel", "教职工导入模板");
  //   setTimeout(() => proxy.$modal.closeLoading(), 200);
  // });
  teacherTemplate().then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "教职工导入模板");
    setTimeout(() => proxy.$modal.closeLoading(), 200);
  });
}

function deptTreeChange(val, obj) {
  state.queryParams.deptIds = obj.checkedKeys;
  getList();
}

function handleCancelExport() {
  proxy.resetForm("exportRef");
  state.exportForm.num = 0;
  state.exportDialogVisible = false;
}

function handleQueryExport(val) {
  console.log(val);
  if (!!val) {
    const [start, end] = val;
    getTeacherExportNum({ startTime: start, endTime: end }).then((res) => {
      console.log(res);
      state.exportForm.num = res.data;
    });
  }
}

function handleClose() {
  importDialogVisible.value = false;
  fileList.value = [];
}

function handleImport() {
  if (!parentId.value) {
    ElMessage.error("请选择部门");
    return;
  }
  if (fileList.value.length == 0) {
    ElMessage.error("请选择上传的文件");
    return;
  }
  uploadTemplate.value.submit();
}

function handleExport() {
  state.exportRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const [start, end] = state.exportForm.time;
      exportTeacherStaff({ startTime: start, endTime: end })
        .then((response) => {
          downloadBlob(response, "application/vnd.ms-excel", "教职工列表");
          proxy.$modal.msgSuccess("操作成功");
          proxy.$modal.closeLoading();
          getList();
          handleCancelExport();
        })
        .catch(() => proxy.$modal.closeLoading());
    }
  });
}

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

async function getPostList() {
  await listPost({ pageNum: 1, pageSize: 999999 }).then((resp) => {
    if (resp.code == 200) {
      console.log("岗位列表", resp.data);
      state.postList = resp.data?.rows;
    }
  });
}

async function getDeptTreeList() {
  await listTree({ deptName: "", status: "" }).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data || []));
    state.deptTreeList = response.data || [];
    state.deptList = treeToArray(state.deptTreeList);
    console.log("部门信息", response.data, state.deptList);
  });
}

async function getDeptTreeList_disable(deptId = "") {
  await listTree({ status: 0, deptId }).then((res) => {
    console.log(res);
    if (res.data) {
      state.deptTreeList_disabled = addAttr(res.data);
    }
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

function handleDept(val) {
  state.deptResult = {
    ids: [],
    names: [],
  };
  state.deptResult.ids = treeFindPath(
    state.deptTreeList,
    (d) => d.deptId == val,
    [],
    "deptId"
  );
  const arr = state.deptResult.ids;
  console.log("deptResult ==> ", val, arr, state.deptTreeList);
  for (let i = 0; i < arr.length; i++) {
    state.deptResult.names[i] = state.deptList.find(
      (node) => node.deptId == arr[i]
    ).deptName;
  }
}

async function handleBatchImport() {
  await getDeptTreeList_disable();
  state.importDialogVisible = true;
}

function handleBatchExport() {
  state.exportForm.time = null;
  state.exportDialogVisible = true;
}

const deviceTreeRef = ref();
async function handleAdd({ row } = {}) {
  await getDeptTreeList_disable();
  state.title = "添加教职工";
  state.teacherInfo = {
    name: "",
    deptId: "",
    employeesPostId: "",
  };
  state.dialogVisible = true;
  nextTick(() => deviceTreeRef.value?.setCheckedKeys([]));
}

async function handleEdit({ row, $index }) {
  let res = await getTeacherInfo({ id: row.userId });
  console.log(res);
  await getDeptTreeList_disable(row.deptId);
  state.title = "编辑教职工";
  state.teacherInfo = JSON.parse(JSON.stringify(row));
  checkMaintain();
  state.teacherInfo.password = res.data.password;
  state.dialogVisible = true;
}

function handleSubmit() {
  state.formRef.validate(async (valid) => {
    if (valid) {
      if (!!state.teacherInfo.userId) {
        console.log(state.teacherInfo);
        updateTeacher(state.teacherInfo).then((res) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.dialogVisible = false;
        });
      } else {
        addTeacher(state.teacherInfo).then((res) => {
          console.log(res, "提交的参数", state.teacherInfo);
          if (res.code == 200) {
            if (!!res.data) {
              console.log(res.data, "教职工检测失败");
              ElMessageBox.alert(res.data, "系统提示", {
                type: "warning",
                confirmButtonText: "确认",
                callback: (action) => {},
              });
            }
            getList();
            proxy.$modal.msgSuccess("新增成功");
            state.dialogVisible = false;
          }
        });
        // const { nickName, phoneNumber } = state.teacherInfo;
        // const idx = roleList.value.findIndex(
        //   (_) => _.roleKey == "teacherStaff"
        // );
        // let roleIds = idx != -1 ? [roleList.value[idx].roleId] : [];
        // let obj = {
        //   nickName,
        //   roleIds,
        //   phonenumber: phoneNumber,
        // };
        // console.log("检测传参", obj);
        // await checkTeacherLimit(obj)
        //   .then((res) => {
        //     console.log(res, "教职工检测成功");
        //   })
        //   .catch((err) => {
        //     console.log(err, "教职工检测失败");
        //     ElMessageBox.alert(err, "系统提示", {
        //       type: "warning",
        //       confirmButtonText: "确认",
        //       callback: (action) => {},
        //     });
        //   })
        //   .finally(async () => {
        //     state.dialogVisible = false;
        //     console.log("修改传参", state.teacherInfo);
        //     await addTeacher(state.teacherInfo);
        //     proxy.$modal.msgSuccess("添加成功");
        //   });
        // getList();
      }
    }
  });
}

function handleCancelUse() {
  deviceTreeRef.value.setCheckedKeys([]);
  state.useDialogVisible = false;
}

function handleCancel() {
  state.formRef.resetFields();
  state.dialogVisible = false;
  state.isMaintain = false;
}

function handleReset({ row, $index }) {
  proxy.$modal
    .confirm(`确定重置姓名为【${row.nickName}】的教职工密码？`)
    .then(async function () {
      proxy.$modal.loading();
      await resetPassword(row.userId);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("重置成功");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function handleDel({ row, $index }) {
  proxy.$modal
    .confirm(`确定要删除姓名为【${row.nickName}】的教职工信息？`)
    .then(async function () {
      proxy.$modal.loading();
      await delTeacher({ userIds: row.userId });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function handleBatchDel() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一个教职工");
    return;
  } else {
    let rows = state.tableAllSelectedRow;
    // for (let i = 0; i < rows.length; i++) {
    //   if (
    //     rows[i].roleKey.indexOf("maintain") != -1 ||
    //     rows[i].roleKey.indexOf("maintainManage") != -1
    //   ) {
    //     proxy.$modal.msgWarning("运维人员/运维管理员不能被删除");
    //     return;
    //   }
    // }
    proxy.$modal
      .confirm("确定批量删除？")
      .then(async function () {
        proxy.$modal.loading();
        await delTeacher({ userIds: state.tableAllSelectedId.join(",") });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .finally(() => {
        proxy.$modal.closeLoading();
      });
  }
}

function getList() {
  console.log("获取教职工列表传参", state.queryParams);
  state.loading = true;
  getTeacherList(state.queryParams).then((response) => {
    if (response.data) {
      console.log(response.data);
      const { records, total } = response.data;
      state.tableList =
        records.map((item) => {
          item.devicePermission =
            item.devicePermission?.filter((item) => !!item) || [];
          return item;
        }) || [];
      state.total = total || 0;
      state.loading = false;
      nextTick(() => {
        state.tableList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.userId) > -1) {
            state.tableRef.toggleRowSelection(item, true);
          } else {
            state.tableRef.toggleRowSelection(item, false);
          }
        });
      });
    }
  });
  getTeacherList({
    ...state.queryParams,
    pageNum: 1,
    pageSize: 999999,
    // roleKey: ["teacherStaff"],
  }).then((response) => {
    const { records } = response.data;
    state.tableList_all = records || [];
  });
}

// 搜索方法
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

// 重置搜索
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  queryParams.value.deptIds = [];
  proxy.$refs.treeRef.setCheckedNodes([]);
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "userId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.userId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
      state.tableAllSelectedId.push(item.userId);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.userId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.userId === a[0].userId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
        state.tableAllSelectedId.push(item.userId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

async function getDeptList() {
  const params = {
    smartScreen: 1,
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 9999999,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: 1,
    deviceStatus: "",
    positionIds: [],
    putTime: "",
    status: [],
  };
  expandedKeys.value = [1];
  try {
    await devicePage(params).then((res) => {
      if (res.code == 200) {
        // 构建树形结构函数
        function buildTree(data) {
          const root = { label: "全选", id: 0, children: [] };
          let nextId = 1;
          const nodeMap = new Map();

          // 按层级深度排序（浅→深）
          data.sort((a, b) => {
            const aDepth = a.installAddress
              ? a.installAddress.split("-").length
              : 0;
            const bDepth = b.installAddress
              ? b.installAddress.split("-").length
              : 0;
            return aDepth - bDepth;
          });

          // 第一步：创建所有分支节点
          data.forEach((item) => {
            if (!item.installAddress) return;

            const parts = item.installAddress.split("-");
            let currentPath = "";
            let parentNode = root;

            for (let i = 0; i < parts.length; i++) {
              const part = parts[i];
              currentPath = currentPath ? `${currentPath}-${part}` : part;

              if (!nodeMap.has(currentPath)) {
                const newNode = {
                  label: part,
                  id: nextId++,
                  children: [],
                };

                parentNode.children.push(newNode);
                nodeMap.set(currentPath, newNode);
                parentNode = newNode;
              } else {
                parentNode = nodeMap.get(currentPath);
              }
            }
          });

          // 第二步：添加数据节点
          data.forEach((item) => {
            if (!item.installAddress) {
              root.children.push({
                label: item.deviceCode.toString(),
                id: nextId++,
                children: [],
              });
              return;
            }

            const parts = item.installAddress.split("-");
            const fullPath = item.installAddress;
            const parentPath = parts.slice(0, -1).join("-");

            // 查找父节点
            const parentNode =
              nodeMap.get(parentPath) || nodeMap.get(fullPath)
                ? nodeMap.get(fullPath)
                : root;

            // 检查是否已存在相同数据节点
            const exists = parentNode.children.some((child) =>
              child.label.includes(item.deviceCode.toString())
            );

            if (!exists) {
              const isLeaf = !nodeMap.has(fullPath);
              const leafLabel = isLeaf
                ? `${parts[parts.length - 1]}-${item.deviceCode}`
                : item.deviceCode.toString();

              parentNode.children.unshift({
                label: leafLabel,
                id: nextId++,
                children: [],
              });
            }
          });

          return [root];
        }
        deviceTreeData.value = buildTree(res.data.page.records);
      }
    });
  } catch (error) {
    console.error("获取设备树数据失败:", error);
  }
}

function handleDeviceCheck(data, { checkedNodes, checkedKeys }) {
  // 获取选中的设备编号
  const devices = checkedNodes
    .filter(
      (node) => (node.children?.length < 1 || !node.children) && !node.disabled
    ) // 只取叶子节点
    .map((node) => node.label.split("-")[1] ?? node.label)
    .join(",");
  console.log(devices, "devices");
  // 保存选中的节点ID
  state.teacherInfo.deviceCode = devices.split(",");
  state.teacherInfo.selectedDeviceIds = checkedKeys; // 新增：保存选中的ID
}

onMounted(() => {
  teacherLimit().then((res) => {
    console.log(res);
    state.teacherLimitTotal = res.data;
  });
  // getPostList();
  getDeptTreeList();
  getList();
  employeesPostList();
  getDeptList();
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - 215;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - 215;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});
</script>

<style lang="scss" scoped>
#el-id-6653-106 {
  width: 160px;
}
.el-cascader {
  width: 100%;
}
.import-steps {
  padding: 20px 0;

  .step {
    margin-bottom: 20px;

    &-title {
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}
.tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 250px;
  overflow-y: auto;
}
.test {
  margin-left: 20px;
}
.mb-3 {
  margin-bottom: 12px;
  font-weight: 700;
}
.mt-3 {
  margin-top: 20px;
  margin-bottom: 20px;
}
.label {
  white-space: nowrap;
  align-items: flex-start;
  box-sizing: border-box;
  color: #606266;
  display: inline-flex;
  flex: 0 0 auto;
  font-size: 14px;
  height: 32px;
  justify-content: flex-end;
  line-height: 32px;
  padding: 0 12px 0 0;
  font-weight: 700;
}
.operation {
  &-title {
    padding: 10px 20px;
    font-size: 16px;
  }
  &-main {
    display: flex;
    align-items: flex-start;
    gap: 0 20px;
    &_left {
      width: 18%;
      min-width: 200px;
      border: 1px solid #dcdfe6;

      :deep(.treeNode) {
        .el-tree-node__content {
          height: auto;
        }
        .el-tree-node__label {
          white-space: wrap;
          padding: 3px 3px 3px 0;
        }
      }

      .scrollbar {
        padding: 5px 0;
        :deep(.el-tree-node__content) {
          // padding-left: 10px!important;
          height: auto !important;
          white-space: pre-wrap;
        }
      }
    }
    &_right {
      width: 82%;
      min-width: 400px;
    }
  }
}
</style>
