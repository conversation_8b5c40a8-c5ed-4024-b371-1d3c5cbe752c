/**
 * MIME 类型速查表 (常见文件扩展名与 MIME 类型映射)
 * 包含 IANA 注册类型 + 常见非官方类型
 * @see https://www.iana.org/assignments/media-types/media-types.xhtml
 */

const mimeTypeMap = {
  // ==================== 文本类型 ====================
  txt: "text/plain",
  html: "text/html",
  htm: "text/html",
  css: "text/css",
  csv: "text/csv",
  ics: "text/calendar",
  js: "text/javascript", // 非官方推荐使用 application/javascript
  mjs: "text/javascript",
  tsv: "text/tab-separated-values",
  vcf: "text/vcard",
  xml: "text/xml",

  // ==================== 图片类型 ====================
  apng: "image/apng",
  avif: "image/avif",
  bmp: "image/bmp",
  gif: "image/gif",
  ico: "image/x-icon",
  cur: "image/x-icon",
  jpg: "image/jpeg",
  jpeg: "image/jpeg",
  jfif: "image/jpeg",
  pjpeg: "image/jpeg",
  pjp: "image/jpeg",
  png: "image/png",
  svg: "image/svg+xml",
  svgz: "image/svg+xml",
  tif: "image/tiff",
  tiff: "image/tiff",
  webp: "image/webp",

  // ==================== 音视频类型 ====================
  aac: "audio/aac",
  mid: "audio/midi",
  midi: "audio/midi",
  mp3: "audio/mpeg",
  oga: "audio/ogg",
  opus: "audio/opus",
  wav: "audio/wav",
  weba: "audio/webm",
  avi: "video/x-msvideo",
  mpeg: "video/mpeg",
  mp4: "video/mp4",
  mpg: "video/mpeg",
  ogv: "video/ogg",
  webm: "video/webm",
  "3gp": "video/3gpp",
  "3g2": "video/3gpp2",

  // ==================== 应用类型 ====================
  abw: "application/x-abiword",
  arc: "application/x-freearc",
  azw: "application/vnd.amazon.ebook",
  bin: "application/octet-stream",
  bz: "application/x-bzip",
  bz2: "application/x-bzip2",
  csh: "application/x-csh",
  doc: "application/msword",
  docx: "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
  eot: "application/vnd.ms-fontobject",
  epub: "application/epub+zip",
  gz: "application/gzip",
  jar: "application/java-archive",
  json: "application/json",
  jsonld: "application/ld+json",
  mpkg: "application/vnd.apple.installer+xml",
  odp: "application/vnd.oasis.opendocument.presentation",
  ods: "application/vnd.oasis.opendocument.spreadsheet",
  odt: "application/vnd.oasis.opendocument.text",
  ogx: "application/ogg",
  otf: "font/otf",
  pdf: "application/pdf",
  php: "application/x-httpd-php",
  ppt: "application/vnd.ms-powerpoint",
  pptx: "application/vnd.openxmlformats-officedocument.presentationml.presentation",
  rar: "application/vnd.rar",
  rtf: "application/rtf",
  sh: "application/x-sh",
  swf: "application/x-shockwave-flash",
  tar: "application/x-tar",
  vsd: "application/vnd.visio",
  xhtml: "application/xhtml+xml",
  xls: "application/vnd.ms-excel",
  xlsx: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
  xul: "application/vnd.mozilla.xul+xml",
  zip: "application/zip",
  "7z": "application/x-7z-compressed",

  // ==================== 字体类型 ====================
  woff: "font/woff",
  woff2: "font/woff2",
  ttf: "font/ttf",

  // ==================== 其他常见类型 ====================
  wasm: "application/wasm",
  exe: "application/x-msdownload",
  msi: "application/x-msi",
  dmg: "application/x-apple-diskimage",
  deb: "application/x-debian-package",
  bat: "application/x-msdos-program",
  dll: "application/x-msdownload",
  rpm: "application/x-redhat-package-manager",
  torrent: "application/x-bittorrent",
};

// ==================== 辅助方法 ====================
/**
 * 根据文件扩展名获取 MIME 类型
 * @param {string} filename - 文件名或扩展名 (如 "image.png" 或 "png")
 * @returns {string} MIME 类型 (默认返回 application/octet-stream)
 */
export function getMimeType(filename) {
  const ext = filename.includes(".")
    ? filename.split(".").pop().toLowerCase()
    : filename.toLowerCase();
  return mimeTypeMap[ext] || "application/octet-stream";
}

/**
 * 添加自定义 MIME 类型映射
 * @param {Object} customTypes - 自定义类型对象 { 扩展名: MIME类型 }
 */
export function extendMimeTypes(customTypes) {
  Object.assign(mimeTypeMap, customTypes);
}

// 导出完整映射表和方法
export default {
  mimeTypeMap,
  getMimeType,
  extendMimeTypes,
};
