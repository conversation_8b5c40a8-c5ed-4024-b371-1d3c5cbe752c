<template>
  <div
    :id="id"
    ref="chartDom"
    class="chartDom"
    :className="className"
    :style="{
      width,
      height,
    }"
  ></div>
  <div
    v-show="useTip && showTip"
    class="pieTip"
    :style="'bottom:' + pieTipBottom"
  >
    <div v-show="!!curTip">{{ curValue }}</div>
    <div :id="id + 'tipContent'" class="tipContent"></div>
  </div>
</template>
<script setup>
import * as echarts from "echarts";
import "echarts-gl";
import elementResizeDetectorMaker from "element-resize-detector";
import { onMounted, ref, onBeforeUnmount, watch } from "vue";
import getGuangZhouData from "@/api/gz.json";
import { nextTick } from "process";

const erd = elementResizeDetectorMaker();
const chartDom = ref("");
const emit = defineEmits(["setFontSize", "getClickIdx"]);
const pieColor = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const props = defineProps({
  id: {
    type: String,
    default: "myChart",
    required: true,
  },
  className: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "300px",
  },
  useTip: {
    type: Boolean,
    default: false,
  },
  loading: {
    type: Boolean,
    default: false,
  },
  fullOptions: {
    type: Object,
    default: () => ({}),
    required: true,
  },
  mapConfig: {
    type: Object,
    default: () => ({
      name: "",
      data: {},
    }),
  },
  dtWidth: {
    type: Number,
    default: () => {
      return 0;
    },
  },
  ratio: {
    type: Number,
    default: () => {
      return 0.8;
    },
  },
  data3D: {
    type: Array,
    default: () => {
      return [];
    },
  },
  pieTipBottom: {
    type: String,
    default: "3.1vw",
  },
});

const state = reactive({
  data3D: [],
  curValue: 0,
  curTip: "",
  showTip: false,
  pieHeight: 20,
  hoverPieHeight: 30,
  legendData: [],
  option: {},
  dtWidths: 0,
  zoom: 0,
});

const {
  data3D,
  curValue,
  curTip,
  showTip,
  hoverPieHeight,
  pieHeight,
  zoom,
  dtWidths,
  option,
  legendData,
} = toRefs(state);

let myChart;

const resizeHandler = () => {
  state.dtWidths = props.dtWidth;
  state.zoom = 1 / document.body.style.zoom;
  emit("setFontSize", props.id);
  myChart.resize();
};

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};

const cancelDebounce = debounce(resizeHandler, 50);

function getPie3D(pieData, internalDiameterRatio) {
  let series = [];
  let sumValue = 0;
  let startValue = 0;
  let endValue = 0;
  legendData.value = [];
  let k =
    typeof internalDiameterRatio !== "undefined"
      ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
      : 1 / 3;

  // 为每一个饼图数据，生成一个 series-surface 配置
  for (let i = 0; i < pieData.length; i++) {
    sumValue += pieData[i].value;

    let seriesItem = {
      name:
        typeof pieData[i].name === "undefined" ? `series${i}` : pieData[i].name,
      type: "surface",
      parametric: true,
      wireframe: {
        show: false,
      },
      pieData: pieData[i],
      pieStatus: {
        selected: false,
        hovered: false,
        k: k,
      },
    };

    if (typeof pieData[i].itemStyle != "undefined") {
      let itemStyle = {};

      typeof pieData[i].itemStyle.color != "undefined"
        ? (itemStyle.color = pieData[i].itemStyle.color)
        : null;
      typeof pieData[i].itemStyle.detail != "undefined"
        ? (itemStyle.detail = { color: pieData[i].itemStyle.detail?.color })
        : null;
      typeof pieData[i].itemStyle.opacity != "undefined"
        ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
        : null;

      seriesItem.itemStyle = itemStyle;
    }

    series.push(seriesItem);
  }

  // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
  // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
  for (let i = 0; i < series.length; i++) {
    endValue = startValue + series[i].pieData.value;

    series[i].pieData.startRatio = startValue / sumValue;
    series[i].pieData.endRatio = endValue / sumValue;
    series[i].parametricEquation = getParametricEquation(
      series[i].pieData.startRatio,
      series[i].pieData.endRatio,
      false,
      false,
      k,
      series[i].pieData.height,
      i,
      series[i].pieData.value
    );

    startValue = endValue;
    legendData.value.push(series[i].name);
  }

  return series;
}

// 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
function getParametricEquation(
  startRatio,
  endRatio,
  isSelected,
  isHovered,
  k,
  height,
  i
) {
  // 计算
  let midRatio = (startRatio + endRatio) / 2;

  let startRadian = startRatio * Math.PI * 2;
  let endRadian = endRatio * Math.PI * 2;
  let midRadian = midRatio * Math.PI * 2;

  // 如果只有一个扇形，则不实现选中效果。
  if (startRatio === 0 && endRatio === 1) {
    isSelected = false;
  }

  // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
  k = typeof k !== "undefined" ? k : 1 / 3;

  // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
  let offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0;
  let offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0;
  let offsetZ = i == 1 ? 2 : 0;
  // 计算高亮效果的放大比例（未高亮，则比例为 1）
  let hoverRate = isHovered ? 1.05 : 1;

  // 返回曲面参数方程
  return {
    u: {
      min: -Math.PI,
      max: Math.PI * 3,
      step: Math.PI / 32,
    },

    v: {
      min: 0,
      max: Math.PI * 2,
      step: Math.PI / 20,
    },

    x: function (u, v) {
      if (u < startRadian) {
        return (
          offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    y: function (u, v) {
      if (u < startRadian) {
        return (
          offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      if (u > endRadian) {
        return (
          offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        );
      }
      return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate;
    },

    z: function (u, v) {
      if (u < -Math.PI * 0.5) {
        return Math.sin(u);
      }
      if (u > Math.PI * 2.5) {
        return Math.sin(u);
      }
      return Math.sin(v) > 0 ? 1 * height : -1;
    },
  };
}
const hoveredIndex = ref("");
const series = ref(null);

const initChart = () => {
  if (myChart != null && myChart != "" && myChart != undefined) {
    myChart.dispose(); //销毁
  }
  myChart = echarts.init(document.getElementById(props.id));
  state.dtWidths = props.dtWidth;
  state.zoom = 1 / document.body.style.zoom;

  window.addEventListener("resize", cancelDebounce);
  erd.listenTo(chartDom.value, () => {
    cancelDebounce();
  });

  const series = getPie3D(state.data3D, props.ratio || 0);
  state.option = props.fullOptions.options;
  // series.push({
  //   name: "pie2d",
  //   type: "pie",
  //   avoidLabelOverlap: true,
  //   label: {
  //     opacity: 1,
  //     lineHeight: 15,
  //     fontSize: 6,
  //     color: "#fff",
  //   },
  //   labelLine: {
  //     length: 3,
  //     length2: 10,
  //     lineStyle: {
  //       width: 1,
  //     },
  //   },
  //   startAngle: 10, //起始角度，支持范围[0, 360]。
  //   clockwise: false, //饼图的扇区是否是顺时针排布。上述这两项配置主要是为了对齐3d的样式
  //   radius: ["60%", "70%"],
  //   center: ["50%", "50%"],
  //   data: state.data3D,
  //   itemStyle: {
  //     opacity: 0,
  //   },
  // });
  state.option.series = series;
  pieHeight.value = state.data3D[0]?.height || 20;
  hoverPieHeight.value = Math.ceil(pieHeight.value * 1.5);
  // console.log("初始化数据", state.option);
  const opt = JSON.parse(JSON.stringify(state.option));
  if (myChart) {
    myChart.setOption(state.option, true);
  }

  // 监听鼠标事件，实现饼图选中效果（单选），近似实现高亮（放大）效果。
  let selectedIndex = "";
  let hoveredIndex = "";

  // 监听点击事件，实现选中效果（单选）
  // myChart.on("click", function (params) {
  //   // 从 option.series 中读取重新渲染扇形所需的参数，将是否选中取反。
  //   const isSelected =
  //     !state.option.series[params.seriesIndex].pieStatus.selected;
  //   const isHovered = state.option.series[params.seriesIndex].pieStatus.hovered;
  //   const k = state.option.series[params.seriesIndex].pieStatus.k;
  //   const startRatio =
  //     state.option.series[params.seriesIndex].pieData.startRatio;
  //   const endRatio = state.option.series[params.seriesIndex].pieData.endRatio;

  //   // 如果之前选中过其他扇形，将其取消选中（对 option 更新）
  //   if (selectedIndex !== "" && selectedIndex !== params.seriesIndex) {
  //     state.option.series[selectedIndex].parametricEquation =
  //       getParametricEquation(
  //         state.option.series[selectedIndex].pieData.startRatio,
  //         state.option.series[selectedIndex].pieData.endRatio,
  //         false,
  //         false,
  //         k,
  //         pieHeight.value
  //       );
  //     state.option.series[selectedIndex].pieStatus.selected = false;
  //   }

  //   // 对当前点击的扇形，执行选中/取消选中操作（对 option 更新）
  //   state.option.series[params.seriesIndex].parametricEquation =
  //     getParametricEquation(
  //       startRatio,
  //       endRatio,
  //       isSelected,
  //       isHovered,
  //       k,
  //       hoverPieHeight.value
  //     );
  //   state.option.series[params.seriesIndex].pieStatus.selected = isSelected;

  //   // 如果本次是选中操作，记录上次选中的扇形对应的系列号 seriesIndex
  //   isSelected ? (selectedIndex = params.seriesIndex) : null;

  //   // 使用更新后的 option，渲染图表
  //   myChart.setOption(state.option);
  // });

  // 监听 mouseover，近似实现高亮（放大）效果
  myChart.on("mouseover", function (params) {
    mouseoverHandler(params);
  });

  // 监听 touchstart，近似实现高亮（放大）效果
  myChart.on("touchover", function (params) {
    mouseoverHandler(params);
    // touchstartHandler(params);
  });

  // 监听 touchstart，近似实现高亮（放大）效果
  // myChart.on("touchstart", function (params) {
  //   // 准备重新渲染扇形所需的参数
  //   let isSelected;
  //   let isHovered;
  //   let startRatio;
  //   let endRatio;
  //   let k;

  //   // 如果触发 touchstart 的扇形当前已高亮，则不做操作
  //   if (hoveredIndex === params.seriesIndex) {
  //     return;

  //     // 否则进行高亮及必要的取消高亮操作
  //   } else {
  //     // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
  //     if (hoveredIndex !== "") {
  //       // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
  //       isSelected = state.option.series[hoveredIndex].pieStatus.selected;
  //       isHovered = false;
  //       startRatio = state.option.series[hoveredIndex].pieData.startRatio;
  //       endRatio = state.option.series[hoveredIndex].pieData.endRatio;
  //       k = state.option.series[hoveredIndex].pieStatus.k;

  //       // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
  //       state.option.series[hoveredIndex].parametricEquation =
  //         getParametricEquation(
  //           startRatio,
  //           endRatio,
  //           isSelected,
  //           isHovered,
  //           k,
  //           pieHeight.value
  //         );
  //       state.option.series[hoveredIndex].pieStatus.hovered = isHovered;

  //       // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
  //       hoveredIndex = "";
  //     }

  //     // 如果触发 touchstart 的扇形不是透明圆环，将其高亮（对 option 更新）
  //     if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "") {
  //       // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
  //       isSelected = state.option.series[params.seriesIndex].pieStatus.selected;
  //       isHovered = true;
  //       startRatio = state.option.series[params.seriesIndex].pieData.startRatio;
  //       endRatio = state.option.series[params.seriesIndex].pieData.endRatio;
  //       k = state.option.series[params.seriesIndex].pieStatus.k;

  //       // 对当前点击的扇形，执行高亮操作（对 option 更新）
  //       state.option.series[params.seriesIndex].parametricEquation =
  //         getParametricEquation(
  //           startRatio,
  //           endRatio,
  //           isSelected,
  //           isHovered,
  //           k,
  //           hoverPieHeight.value
  //         );
  //       state.option.series[params.seriesIndex].pieStatus.hovered = isHovered;

  //       // 记录上次高亮的扇形对应的系列号 seriesIndex
  //       hoveredIndex = params.seriesIndex;
  //     }

  //     // 使用更新后的 option，渲染图表
  //     myChart.setOption(state.option);
  //   }
  // });

  // 修正取消高亮失败的 bug
  myChart.on("globalout", function () {
    globaloutHandler();
  });
};

const mouseoverHandler = (params) => {
  // 准备重新渲染扇形所需的参数
  let isSelected;
  let isHovered;
  let startRatio;
  let endRatio;
  let k;

  // 如果触发 mouseover 的扇形当前已高亮，则不做操作
  if (hoveredIndex.value === params.seriesIndex) {
    return;

    // 否则进行高亮及必要的取消高亮操作
  } else {
    // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
    if (hoveredIndex.value !== "") {
      // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
      isSelected = state.option.series[hoveredIndex.value].pieStatus.selected;
      isHovered = false;
      startRatio = state.option.series[hoveredIndex.value].pieData.startRatio;
      endRatio = state.option.series[hoveredIndex.value].pieData.endRatio;
      k = state.option.series[hoveredIndex.value].pieStatus.k;

      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      state.option.series[hoveredIndex.value].parametricEquation =
        getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          pieHeight.value
        );
      state.option.series[hoveredIndex.value].pieStatus.hovered = isHovered;
      state.option.series[hoveredIndex.value].itemStyle.opacity = 0.6;

      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex.value = "";
    }

    // 如果触发 mouseover 的扇形不是透明圆环，将其高亮（对 option 更新）
    if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "") {
      // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = state.option.series[params.seriesIndex].pieStatus.selected;
      isHovered = true;
      startRatio = state.option.series[params.seriesIndex].pieData.startRatio;
      endRatio = state.option.series[params.seriesIndex].pieData.endRatio;
      k = state.option.series[params.seriesIndex].pieStatus.k;

      // 对当前点击的扇形，执行高亮操作（对 option 更新）
      state.option.series[params.seriesIndex].parametricEquation =
        getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          hoverPieHeight.value
        );
      state.option.series[params.seriesIndex].pieStatus.hovered = isHovered;
      state.option.series[params.seriesIndex].itemStyle.opacity = 1;
      console.log(state.option.series[params.seriesIndex]);

      // 记录上次高亮的扇形对应的系列号 seriesIndex
      hoveredIndex.value = params.seriesIndex;
    }

    // console.log(
    //   state.option.series[params.seriesIndex].pieData,
    //   " state.option.series[params.seriesIndex].pieData"
    // );

    curTip.value = state.option.series[params.seriesIndex].pieData.name
      .match(/.{1,10}/g)
      .join("\n");
    // curTip.value = curTip.value.match(/.{1,10}/g).join("\n");
    curValue.value = state.option.series[params.seriesIndex].pieData.proportion;
    nextTick(() => {
      if (document.getElementById(props.id + "tipContent")) {
        document.getElementById(props.id + "tipContent").innerHTML =
          curTip.value;
        showTip.value = true;
      }
    });
    // 使用更新后的 option，渲染图表
    myChart.setOption(state.option);
  }
};

const touchstartHandler = (params) => {
  // 准备重新渲染扇形所需的参数
  let isSelected;
  let isHovered;
  let startRatio;
  let endRatio;
  let k;

  // 如果触发 touchstart 的扇形当前已高亮，则不做操作
  if (hoveredIndex.value === params.seriesIndex) {
    return;

    // 否则进行高亮及必要的取消高亮操作
  } else {
    // 如果当前有高亮的扇形，取消其高亮状态（对 option 更新）
    if (hoveredIndex.value !== "") {
      // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 false。
      isSelected = state.option.series[hoveredIndex.value].pieStatus.selected;
      isHovered = false;
      startRatio = state.option.series[hoveredIndex.value].pieData.startRatio;
      endRatio = state.option.series[hoveredIndex.value].pieData.endRatio;
      k = state.option.series[hoveredIndex.value].pieStatus.k;

      // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
      state.option.series[hoveredIndex.value].parametricEquation =
        getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          pieHeight.value
        );
      state.option.series[hoveredIndex.value].pieStatus.hovered = isHovered;

      // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
      hoveredIndex.value = "";
    }

    // 如果触发 touchstart 的扇形不是透明圆环，将其高亮（对 option 更新）
    if (params.seriesName !== "mouseoutSeries" && params.seriesName !== "") {
      // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
      isSelected = state.option.series[params.seriesIndex].pieStatus.selected;
      isHovered = true;
      startRatio = state.option.series[params.seriesIndex].pieData.startRatio;
      endRatio = state.option.series[params.seriesIndex].pieData.endRatio;
      k = state.option.series[params.seriesIndex].pieStatus.k;

      // 对当前点击的扇形，执行高亮操作（对 option 更新）
      state.option.series[params.seriesIndex].parametricEquation =
        getParametricEquation(
          startRatio,
          endRatio,
          isSelected,
          isHovered,
          k,
          hoverPieHeight.value
        );
      state.option.series[params.seriesIndex].pieStatus.hovered = isHovered;

      // 记录上次高亮的扇形对应的系列号 seriesIndex
      hoveredIndex.value = params.seriesIndex;
    }

    // 使用更新后的 option，渲染图表
    myChart.setOption(state.option);
  }
};

const globaloutHandler = () => {
  if (hoveredIndex.value !== "") {
    // 从 state.option.series 中读取重新渲染扇形所需的参数，将是否高亮设置为 true。
    const isSelected =
      state.option.series[hoveredIndex.value].pieStatus.selected;
    const isHovered = false;
    const k = state.option.series[hoveredIndex.value].pieStatus.k;
    const startRatio =
      state.option.series[hoveredIndex.value].pieData.startRatio;
    const endRatio = state.option.series[hoveredIndex.value].pieData.endRatio;

    // 对当前点击的扇形，执行取消高亮操作（对 option 更新）
    state.option.series[hoveredIndex.value].parametricEquation =
      getParametricEquation(
        startRatio,
        endRatio,
        isSelected,
        isHovered,
        k,
        pieHeight.value
      );
    state.option.series[hoveredIndex.value].pieStatus.hovered = isHovered;
    state.option.series[hoveredIndex.value].itemStyle.opacity = 0.6;

    // 将此前记录的上次选中的扇形对应的系列号 seriesIndex 清空
    hoveredIndex.value = "";
  }

  showTip.value = false;
  curTip.value = "";
  curValue.value = 0;

  // 使用更新后的 option，渲染图表
  myChart.setOption(state.option);
};

onMounted(() => {
  initChart();
});

const unMountFunc = () => {
  window.removeEventListener("resize", cancelDebounce);
  echarts.dispose(myChart);
};

watch(
  () => props.data3D,
  () => {
    // if (!props.loading) {
    // console.log("监听数据变化", state.option);
    state.data3D = JSON.parse(JSON.stringify(props.data3D));
    initChart();
    // myChart.hideLoading();
    // myChart.setOption(state.option, true);
    // myChart.setOption(props.fullOptions.options, true);
    // }
  },
  {
    deep: true,
  }
);

const dispatchAction = (action) => {
  if (action.type === "highlight") {
    mouseoverHandler(action);
    // touchstartHandler(action);
  } else if (action.type === "downplay") {
    // touchstartHandler(action);
    globaloutHandler();
  }
  // myChart.dispatchAction(action);
};

onBeforeUnmount(() => {
  unMountFunc();
});

defineExpose({
  dispatchAction,
  unMountFunc,
});
</script>

<style lang="scss" scoped>
.chartDom {
  position: relative;
}
.pieTip {
  pointer-events: none;
  width: 100%;
  position: absolute;
  // border: 1px solid red;
  display: flex;
  bottom: 3.1vw;
  left: 0;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 11;
  font-size: 0.85vw;
  color: #fff;
  font-weight: bold;
  .tipContent {
    padding-top: 0.1vw;
    font-size: 0.5vw;
    font-weight: normal;
    white-space: pre-wrap;
  }
}
</style>
