import { getToken } from "@/utils/auth";

let websock = null;
let global_callback = null;
let lastMessageTime = Date.now();
let checkInterval = null;
const serverPort = "8080"; // webSocket连接端口
// const wsuri = "ws://" + window.location.hostname + ":" + serverPort + "/wsdemo";
function createWebSocket(callback, wsuri) {
  // eslint-disable-next-line @typescript-eslint/ban-ts-comment
  // @ts-ignore
  if (websock == null || typeof websock !== WebSocket) {
    return initWebSocket(callback, wsuri);
  }
}
function initWebSocket(callback, wsuri) {
  return new Promise((resolve, reject) => {
    global_callback = callback;
    // 初始化websocket
    websock = new WebSocket(wsuri);
    websock.binaryType = "arraybuffer";
    websock.timeout = 5000;

    // websock.addEventListener('open', (event) => {
    //     console.log('监听open', event)
    //     websock.send('Authorization: Bearer ' + getToken());

    //   });
    websock.onmessage = (e) => {
      websocketonmessage2(e);
      // websocketonmessage(e);

      resolve(e);
    };
    websock.onclose = function (e) {
      global_callback("已断开");
      websocketclose(e);
      resolve(e);
    };
    websock.onopen = function (e) {
      // eslint-disable-next-line @typescript-eslint/ban-ts-comment
      // @ts-ignore
      // websock.send('Authorization: Bearer ' + getToken())
      websocketOpen(e);
      resolve(e);
    };
    // 连接发生错误的回调方法
    websock.onerror = function (e) {
      global_callback("WebSocket连接发生错误", true);
      console.log("WebSocket连接发生错误", e);
      reject(e);
      //createWebSocket();啊，发现这样写会创建多个连接，加延时也不行
    };
  });
}
// 实际调用的方法
function sendSock(agentData) {
  if (websock.readyState === websock.OPEN) {
    // 若是ws开启状态
    websocketsend(agentData);
  } else if (websock.readyState === websock.CONNECTING) {
    // 若是 正在开启状态，则等待1s后重新调用
    setTimeout(function () {
      sendSock(agentData);
    }, 1000);
  } else {
    // 若未开启 ，则等待1s后重新调用
    setTimeout(function () {
      sendSock(agentData);
    }, 1000);
  }
}
async function closeSock() {
  await websock.close();
  clearInterval(checkInterval);
}

// 数据接收
function websocketonmessage2(msg) {
  // console.log("收到数据："+JSON.parse(e.data));
  // console.log("收到数据：", msg);
  // global_callback(JSON.parse(msg.data));
  // 收到信息为Blob类型时
  const nalUnit = new Uint8Array(msg.data);
  const nalType = nalUnit & 0x1f;
  if (nalType === 7 || nalType === 8) {
    // console.log("收到参数集:", nalType === 7 ? "SPS" : "PPS");
  }
  lastMessageTime = Date.now();
  global_callback(nalUnit);
}
// 数据接收
function websocketonmessage(msg) {
  // console.log("收到数据："+JSON.parse(e.data));
  // console.log("收到数据：", msg);
  // global_callback(JSON.parse(msg.data));
  // 收到信息为Blob类型时
  let result = null;
  // debugger
  if (msg.data instanceof Blob) {
    const reader = new FileReader();
    reader.readAsText(msg.data, "UTF-8");
    reader.onload = (e) => {
      console.log(e);
      console.log(reader);
      // if (typeof reader.result === "string") {
      //     result = JSON.parse(reader.result);
      // }
      //console.log("websocket收到", result);
      global_callback(result);
    };
  } else {
    // result = JSON.parse(msg.data);
    result = msg.data;
    //console.log("websocket收到", result);
    global_callback(result);
  }
}
// 数据发送
function websocketsend(agentData) {
  console.log("发送数据：", agentData);
  websock.send(agentData);
}
// 关闭
function websocketclose(e) {
  console.log("connection closed (" + e.code + ")");
}
function websocketOpen(e) {
  console.log(e);
  console.log("连接打开");
  lastMessageTime = Date.now();
  // 每秒检查一次最后消息时间
  checkInterval = setInterval(() => {
    const now = Date.now();
    if (now - lastMessageTime > 3000) {
      console.log("3秒未收到响应，断开连接");
      closeSock();
    }
  }, 1000);
}
export { sendSock, createWebSocket, websocketsend, closeSock };
