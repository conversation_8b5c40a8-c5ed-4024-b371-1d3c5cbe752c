import request from '@/utils/request'

//查询当月设备故障持续时间一览
export function queTroubleTimeInfo(params) {
    return request({
        url: '/system/deviceTrouble/queTroubleTimeInfo',
        method: 'get',
        params
    })
}

//查询当月设备故障持续时间前5名
export function queTroubleTimeRank(params) {
    return request({
        url: '/system/deviceTrouble/queTroubleTimeRank',
        method: 'get',
        params
    })
}

//查询当月设备故障次数前5名
export function queTroubleRank(params) {
    return request({
        url: '/system/deviceTrouble/queTroubleRank',
        method: 'get',
        params
    })
}

//查询设备已使用时长
export function queDeviceUseTime(params) {
    return request({
        url: '/system/device/queDeviceUseTime',
        method: 'get',
        params
    })
}

//查询学校设备运行状态 设备id
export function queDeviceRunStatus(data) {
    return request({
        url: '/system/device/queDeviceRunStatus',
        method: 'post',
        data
    })
}

//查询学校设备故障次数
export function queDeviceFaultCount(data) {
    return request({
        url: '/system/device/queDeviceFaultCount',
        method: 'post',
        data
    })
}

//查询学校设备月度故障次数
export function queMonthFault(data) {
    return request({
        url: '/system/device/queMonthFault',
        method: 'post',
        data
    })
}

//查询学校设备故障率
export function queDeviceFaultRate(data) {
    return request({
        url: '/system/device/queDeviceFaultRate',
        method: 'post',
        data
    })
}

//查询设备下拉列表
export function queOptionList(data) {
    return request({
        url: '/system/device/queOptionList',
        method: 'post',
        data
    })
}