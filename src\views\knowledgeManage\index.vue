<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>知识库列表</span>
        </div>
      </template>
      <el-form ref="queryRef" class="search-list" :inline="true" :model="queryParams">
        <el-form-item prop="code">
          <el-input
            v-model="queryParams.code"
            placeholder="请输入工单编号查询"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="remark">
          <el-input
            v-model="queryParams.remark"
            placeholder="请输入报障描述关键词搜索"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width: 380px"
            @change="setDateTime"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loading" :data="tableList" border highlight-current-row>
        <el-table-column
          label="工单编号"
          align="center"
          minWidth="120px"
          prop="workOrderCode"
          show-overflow-tooltip
        />
        <el-table-column
          label="工单来源"
          align="center"
          minWidth="120px"
          prop="resource"
          show-overflow-tooltip
        />
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.deviceCode || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="报障人姓名"
          align="center"
          minWidth="120px"
          prop="repairName"
          show-overflow-tooltip
        />
        <el-table-column
          label="报障人联系方式"
          align="center"
          minWidth="120px"
          prop="repairPhone"
          show-overflow-tooltip
        />
        <el-table-column
          label="报障时间"
          align="center"
          minWidth="120px"
          prop="createTime"
          show-overflow-tooltip
        />
        <el-table-column
          label="报障描述"
          align="center"
          minWidth="120px"
          prop="remark"
          show-overflow-tooltip
        />
        <el-table-column
          label="备件编号"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">{{ scope.row.sparePartsCode || "-" }}</template>
        </el-table-column>
        <el-table-column
          label="单据状态"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="statusObj[row.troubleStatus]?.type">{{
              statusObj[row.troubleStatus]?.name
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="150" align="center" fixed="right">
          <template #default="scope">
            <el-button link type="primary" icon="Edit" @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button link type="danger" icon="Delete" @click="handleDel(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="knowledgeIndex">
import { ref, reactive, onMounted, computed, toRefs } from "vue";
import { useRouter } from "vue-router";
import { knowledgeBaseList, delKnowledgeBase } from "@/api/mediaTeach/trouble";

const router = useRouter();
const { proxy } = getCurrentInstance();

const state = reactive({
  queryRef: null,
  total: 0,
  loading: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    code: "",
    remark: "",
    dateRange: [],
    startTime: "",
    endTime: "",
  },
  tableList: [],
  statusObj: {
    0: {
      type: "primary",
      name: "待认领",
      path: "/taskManage/taskCenter/claimInfo",
    },
    1: {
      type: "danger",
      name: "待处理",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
    2: {
      type: "warning",
      name: "待评价",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    3: {
      type: "success",
      name: "已完成",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    4: {
      type: "info",
      name: "已挂起",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
  },
});

const { queryRef, total, loading, queryParams, tableList, statusObj } = toRefs(state);

// 同步更改筛选条件的开始时间和结束时间
function setDateTime(val) {
  if (val) {
    state.queryParams.startTime = val[0];
    state.queryParams.endTime = val[1];
  } else {
    state.queryParams.startTime = "";
    state.queryParams.endTime = "";
  }
}

// 从localStorage获取数据，如果没有则使用模拟数据并保存
const getList = () => {
  state.loading = true;
  console.log("queryParams", state.queryParams);
  knowledgeBaseList(state.queryParams).then((response) => {
    if (response.code == 200) {
      console.log("list", response.data);
      state.tableList = response.data?.records || [];
      state.total = response.data?.total || 0;
      state.loading = false;
    }
  });
};

// 搜索方法
const handleQuery = () => {
  state.queryParams.pageNum = 1;
  getList();
};

// 查看详情
const handleView = (row) => {
  const path = state.statusObj[row.troubleStatus].path;
  router.push({
    path,
    query: {
      id: row.troubleId,
      knowledge: 1,
    },
  });
};

const handleDel = (row) => {
  proxy.$modal
    .confirm(`确定删除工单编号为${row.workOrderCode}的信息？`)
    .then(async function () {
      await delKnowledgeBase(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
};

// 修改重置方法，添加分页重置
const resetQuery = () => {
  proxy.resetForm("queryRef");
  state.queryParams.startTime = "";
  state.queryParams.endTime = "";
  state.queryParams.pageSize = 10;
  handleQuery();
};

// 在页面加载时获取数据
onMounted(() => {
  getList();
});
</script>
