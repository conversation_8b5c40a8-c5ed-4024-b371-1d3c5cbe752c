<template>
  <div class="operation app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">职工岗位管理</div>
      </template>
      <div class="operation-main">
        <div class="operation-main_right">
          <el-form
            ref="queryRef"
            :model="queryParams"
            :inline="true"
            class="search-list"
          >
            <el-form-item prop="name">
              <el-input
                v-model.trim="queryParams.name"
                placeholder="请输入职工岗位名称搜索"
                clearable
                @keyup.enter="handleQuery"
                @clear="handleQuery"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12" style="gap: 12px 0px">
            <el-col :span="1.5">
              <el-button type="primary" icon="Plus" plain @click="handleAdd"
                >新增职工岗位</el-button
              >
            </el-col>
          </el-row>

          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="职工岗位编号"
              prop="code"
              min-width="120"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              label="岗位名称"
              prop="name"
              min-width="200"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              label="岗位类型"
              prop="type"
              min-width="120"
              show-overflow-tooltip
              align="center"
            />
            <el-table-column
              label="排序"
              prop="sort"
              min-width="80"
              align="center"
              show-overflow-tooltip
            />
            <el-table-column
              label="备注"
              prop="remark"
              min-width="120"
              show-overflow-tooltip
              align="center"
            >
              <template #default="scope">
                {{ scope.row.remark || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="设备权限"
              prop="permissions"
              min-width="200"
              show-overflow-tooltip
              align="center"
            >
              <template #default="scope">
                {{ scope.row.permissions || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="250"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleEdit(scope.row)"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="success"
                  icon="Setting"
                  @click.stop="handlePermission(scope.row)"
                  >设备权限</el-button
                >
                <el-button
                  v-if="!unDeletableCodes.includes(scope.row.code)"
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDelete(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加新增/编辑职工岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="700px"
      append-to-body
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="formRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="职工岗位编号">
          <!-- <span>系统自动</span> -->
          <el-input v-model="form.code" placeholder="系统自动" disabled />
        </el-form-item>
        <el-form-item label="岗位名称" prop="name">
          <el-input
            v-model.trim="form.name"
            placeholder="请输入岗位名称"
            maxlength="20"
            show-word-limit
            :disabled="
              ['教师', '管理员', '总管理员'].indexOf(form.name) != -1 &&
              !!form.id
            "
          />
        </el-form-item>
        <el-form-item label="岗位类型">
          <!-- <span>只有设备使用者</span> -->
          <el-select v-model="form.type" placeholder="请选择">
            <el-option
              v-for="item in postTypeOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            ></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="排序" prop="sort">
          <el-input-number
            v-model="form.sort"
            :min="0"
            :max="9999"
            :controls="true"
            placeholder="请输入排序号"
          />
        </el-form-item>
        <el-form-item label="备注">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            maxlength="50"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="已选择设备">
          <el-input
            type="textarea"
            v-model="form.selectedDevices"
            readonly
            autosize
            placeholder="点击选择设备"
          />
        </el-form-item>
        <div class="mt-3">
          <div class="mb-3 label test">请选择设备</div>
          <div class="tree-container test">
            <el-tree
              ref="deviceTreeRef"
              :data="deviceTreeData"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              default-expand-all
              @check="handleDeviceCheck"
            />
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-throttle>
            {{ isEdit ? "保存" : "新建" }}
          </el-button>
          <el-button @click="cancel">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加设备权限对话框 -->
    <el-dialog
      class="custom-dialog"
      title="设定设备权限"
      v-model="deviceDialogVisible"
      width="500px"
      append-to-body
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <div class="device-select-row">
        <div class="label">已选择设备</div>
        <el-input
          type="textarea"
          v-model="selectedDeviceCode"
          readonly
          autosize
          placeholder="点击选择设备"
        />
      </div>
      <div class="mt-3">
        <div class="mb-3 label">请选择设备</div>
        <div class="tree-container">
          <el-tree
            ref="deviceTreeRef2"
            :data="deviceTreeData"
            :props="defaultProps"
            show-checkbox
            node-key="id"
            :default-expanded-keys="expandedKeys"
            @check="handleDeviceCheck"
          />
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="saveDevicePermission" v-throttle
            >保存</el-button
          >
          <el-button @click="deviceDialogVisible = false">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="operation">
import { useRoute } from "vue-router";
import { listPost } from "@/api/system/post_new";
import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";
import {
  treeToArray,
  treeFindPath,
  findIndexInObejctArr,
  downloadBlob,
} from "@/utils";
import { getToken } from "@/utils/auth";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  nextTick,
  watch,
} from "vue";
import {
  devicePage,
  deviceInfo,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import {
  getEmployeesPostList,
  addEmployeesPost,
  updateEmployeesPost,
  delEmployeesPost,
} from "@/api/teacher/index";

const { proxy } = getCurrentInstance();
const actionUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
);
const headers = ref({ Authorization: "Bearer " + getToken() });
const parentId = ref("");
const state = reactive({
  expandedKeys: [],
  exportRes: {},
  exportResultOpen: false,
  uploadTemplate: null,
  fileList: [],
  tableRef: null,
  formRef: null,
  peopleNum: 0,
  title: "",
  dialogVisible: false,
  exportDialogVisible: false,
  importDialogVisible: false,
  total: 0,
  loading: false,
  exportRef: null,
  exportForm: {
    time: [],
    num: 0,
    startTime: "",
    endTime: "",
  },
  teacherInfo: {
    name: "",
    deptId: "",
  },
  deptResult: {
    names: [],
    ids: [],
  },
  deptList: [],
  deptTreeList: [],
  deptTreeList_disabled: [],
  tableList: [],
  tableList_all: [],
  tableRadio: [],
  postList: [],
  queryParams: {
    current: 1,
    pageSize: 10,
    name: "",
  },
  exportRules: {
    time: [
      {
        required: true,
        type: "array",
        min: 1,
        message: "请选择时间范围",
        trigger: ["blur", "change"],
      },
    ],
  },
  rules: {
    name: [
      { required: true, message: "岗位名称不能为空", trigger: "blur" },
      { min: 1, max: 20, message: "长度在 1 到 20 个字符", trigger: "blur" },
    ],
    sort: [
      { required: true, message: "排序不能为空", trigger: "blur" },
      {
        validator: (rule, value, callback) => {
          if (value === undefined || value === null) {
            callback(new Error("排序不能为空"));
          } else if (!Number.isInteger(value)) {
            callback(new Error("排序必须为整数"));
          } else if (value < 0 || value > 9999) {
            callback(new Error("排序范围必须在0-9999之间"));
          } else {
            callback();
          }
        },
        trigger: "blur",
      },
    ],
  },
  deptProps: {
    children: "children",
    label: "deptName",
    value: "deptId",
    disabled: "disabled",
    class: "treeNode",
  },
});
const {
  expandedKeys,
  tableRef,
  formRef,
  title,
  rules,
  tableList,
  queryParams,
  dialogVisible,
  loading,
  total,
} = toRefs(state);

const postTypeOptions = ref([{ value: "设备使用者", label: "设备使用者" }]);

function getList() {
  state.loading = true;
  getEmployeesPostList({
    current: state.queryParams.current,
    size: state.queryParams.pageSize,
    name: state.queryParams.name,
  })
    .then((res) => {
      if (res.code === 200) {
        state.tableList = res.data.records;
        state.total = res.data.total;
      }
      state.loading = false;
    })
    .catch(() => {
      state.loading = false;
    });
}

async function getDeptList() {
  const params = {
    smartScreen: 1,
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 9999999,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: 1,
    deviceStatus: "",
    positionIds: [],
    putTime: "",
    status: [],
  };
  expandedKeys.value = [1];
  try {
    await devicePage(params).then((res) => {
      if (res.code == 200) {
        console.log("设备列表", res.data);
        // 构建树形结构函数
        function buildTree(data) {
          const root = { label: "全选", id: 0, children: [] };
          let nextId = 1;
          const nodeMap = new Map();

          // 按层级深度排序（浅→深）
          data.sort((a, b) => {
            const aDepth = a.installAddress
              ? a.installAddress.split("-").length
              : 0;
            const bDepth = b.installAddress
              ? b.installAddress.split("-").length
              : 0;
            return aDepth - bDepth;
          });

          // 第一步：创建所有分支节点
          data.forEach((item) => {
            if (!item.installAddress) return;

            const parts = item.installAddress.split("-");
            let currentPath = "";
            let parentNode = root;

            for (let i = 0; i < parts.length; i++) {
              const part = parts[i];
              currentPath = currentPath ? `${currentPath}-${part}` : part;

              if (!nodeMap.has(currentPath)) {
                const newNode = {
                  label: part,
                  id: nextId++,
                  children: [],
                };

                parentNode.children.push(newNode);
                nodeMap.set(currentPath, newNode);
                parentNode = newNode;
              } else {
                parentNode = nodeMap.get(currentPath);
              }
            }
          });

          // 第二步：添加数据节点
          data.forEach((item) => {
            if (!item.installAddress) {
              root.children.push({
                label: item.deviceCode.toString(),
                id: nextId++,
                children: [],
              });
              return;
            }

            const parts = item.installAddress.split("-");
            const fullPath = item.installAddress;
            const parentPath = parts.slice(0, -1).join("-");

            // 查找父节点
            const parentNode =
              nodeMap.get(parentPath) || nodeMap.get(fullPath)
                ? nodeMap.get(fullPath)
                : root;

            // 检查是否已存在相同数据节点
            const exists = parentNode.children.some((child) =>
              child.label.includes(item.deviceCode.toString())
            );

            if (!exists) {
              const isLeaf = !nodeMap.has(fullPath);
              const leafLabel = isLeaf
                ? `${parts[parts.length - 1]}-${item.deviceCode}`
                : item.deviceCode.toString();

              parentNode.children.unshift({
                label: leafLabel,
                id: nextId++,
                children: [],
              });
            }
          });

          return [root];
        }

        deviceTreeData.value = buildTree(res.data.page.records);
        // 转换数据为树形结构
        // const treeData = [
        //   {
        //     id: 1,
        //     label: "全选",
        //     children: [],
        //   },
        // ];

        // // 用于存储已创建的节点，避免重复
        // const nodeMap = new Map();
        // let currentId = 2; // 从2开始，因为1已经用于根节点

        // res.data.page.records.forEach((device) => {
        //   const addressParts = device.installAddress.split("-");
        //   let currentLevel = treeData[0].children;
        //   let parentPath = "";

        //   // 处理每一级地址
        //   addressParts.forEach((part, index) => {
        //     parentPath = parentPath ? `${parentPath}-${part}` : part;
        //     if (index === addressParts.length - 1) {
        //       // 最后一级，添加设备节点
        //       currentLevel.push({
        //         id: currentId++,
        //         label: part
        //           ? `${part}-${device.deviceCode}`
        //           : `${device.deviceCode}`,
        //       });
        //     } else {
        //       // 查找或创建中间节点
        //       let node = nodeMap.get(parentPath);
        //       if (!node) {
        //         node = {
        //           id: currentId++,
        //           label: part,
        //           children: [],
        //         };
        //         nodeMap.set(parentPath, node);
        //         currentLevel.push(node);
        //       }
        //       currentLevel = node.children;
        //       if (index < addressParts.length - 2) {
        //         // 不是最后两级，保存为默认展开的节点组合
        //         expandedKeys.value.push(node.id);
        //       }
        //     }
        //   });
        // });

        // // 更新设备树数据
        // deviceTreeData.value = treeData;
      }
    });
  } catch (error) {
    console.error("获取设备列表失败", error);
  }
}

// 搜索方法
function handleQuery() {
  state.queryParams.current = 1;
  getList();
}

// 重置搜索
function resetQuery() {
  queryParams.value.name = "";
  queryParams.value.pageSize = 10;
  handleQuery();
}

/** 单击某行 */
/* function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "userId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.userId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
} */

// 添加获取岗位列表函数
async function getPostList() {
  await listPost({ pageNum: 1, pageSize: 999999 }).then((resp) => {
    if (resp.code == 200) {
      console.log("岗位列表", resp.data);
      state.postList = resp.data?.rows;
    }
  });
}

onMounted(() => {
  getList();
  getPostList(); // 添加获取岗位列表
});

const treeRef = ref();

// 表单数据
const form = reactive({
  id: "",
  name: "",
  remark: "",
  selectedDevices: "",
  selectedDeviceIds: [],
  type: "设备使用者",
  sort: 0,
});

// 设备树形数据（示例数据）
const deviceTreeData = ref([
  {
    id: 1,
    label: "全选",
    children: [
      {
        id: 2,
        label: "执信楼",
        children: [
          {
            id: 3,
            label: "1楼",
            children: [
              {
                id: 4,
                label: "101-ZHDP1255462",
              },
            ],
          },
        ],
      },
    ],
  },
]);

console.log(deviceTreeData.value, "设备树形数据");

const defaultProps = {
  children: "children",
  label: "label",
};

const isEdit = ref(false); // 添加编辑状态标识

// 重置表单
function resetForm() {
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 重置 form 的所有字段
  Object.assign(form, {
    id: "",
    name: "",
    code: "",
    type: "设备使用者", // 修改默认值为"设备使用者"
    remark: "",
    selectedDevices: "",
    selectedDeviceIds: [],
    sort: 0,
  });

  // 重置树形选择器
  if (treeRef.value) {
    treeRef.value.setCheckedKeys([]);
  }
  showTreeSelect.value = false;
}

// 处理新增按钮点击
async function handleAdd() {
  await getDeptList(); // 添加获取设备列表
  title.value = "新增职工岗位";
  isEdit.value = false; // 设置为新增状态
  dialogVisible.value = true;
  showTreeSelect.value = false; // 初始不显示树形选择器
  resetForm(); // 确保在打开对话框前重置表单
  nextTick(() => deviceTreeRef.value.setCheckedKeys([]));
}

// 处理编辑按钮点击
async function handleEdit(row) {
  // if (row.name === "教师" || row.name === "管理员" || row.name === "总管理员") {
  //   proxy.$modal.msgError("该岗位不允许编辑");
  //   return; // 阻止编辑操作
  // }
  await getDeptList(); // 添加获取设备列表
  title.value = "编辑职工岗位";
  isEdit.value = true; // 设置为编辑状态
  dialogVisible.value = true;
  showTreeSelect.value = false; // 初始不显示树形选择器
  nextTick(() => {
    // 保存完整的行数据，包括id
    Object.assign(form, {
      id: row.id,
      name: row.name,
      code: row.code,
      type: row.type,
      remark: row.remark,
      selectedDevices: row.permissions || "",
      sort: row.sort || 0,
      selectedDeviceIds: [],
    });

    // 如果有已选设备，获取对应的节点ID
    if (row.permissions) {
      const deviceCodes = row.permissions.split(",");
      const selectedIds = [];
      // 遍历树形数据查找对应的节点ID
      const findDeviceIds = (nodes) => {
        nodes.forEach((node) => {
          if (node.children?.length < 1 || !node.children) {
            const nodeDeviceCode = node.label.split("-")[1] ?? node.label;
            if (deviceCodes.includes(nodeDeviceCode)) {
              selectedIds.push(node.id);
            }
          } else if (node.children.length > 0) {
            findDeviceIds(node.children);
          }
        });
      };

      findDeviceIds(deviceTreeData.value[0].children);
      form.selectedDeviceIds = selectedIds;
      console.log(form.selectedDeviceIds);
    }
    deviceTreeRef.value.setCheckedKeys(form.selectedDeviceIds || []);
  });
}

// 取消按钮
function cancel() {
  dialogVisible.value = false;
  resetForm();
}

// 提交表单
function submitForm() {
  formRef.value.validate((valid) => {
    if (valid) {
      const params = {
        name: form.name,
        type: form.type,
        remark: form.remark,
        permissions: form.selectedDevices,
        sort: form.sort,
      };

      if (isEdit.value) {
        // 编辑模式，添加id
        params.id = form.id;
        updateEmployeesPost(params).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("修改成功");
            dialogVisible.value = false;
            showTreeSelect.value = false;
            getList();
          }
        });
      } else {
        // 新增模式
        addEmployeesPost(params).then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("新增成功");
            dialogVisible.value = false;
            showTreeSelect.value = false;
            getList();
          }
        });
      }
    }
  });
}

// 添加新的响应式变量
const deviceDialogVisible = ref(false);
const selectedDeviceCode = ref("");
const deviceTreeRef = ref();
const deviceTreeRef2 = ref();
const currentRow = ref(null); // 新增：存储当前操作的行数据
const selectedDeviceIds = ref([]);

// 添加控制树形选择器显示的状态
const showTreeSelect = ref(false);

// 修改处理设备权限按钮点击
async function handlePermission(row) {
  await getDeptList(); // 添加获取设备列表
  currentRow.value = row; // 保存当前行数据
  deviceDialogVisible.value = true;
  selectedDeviceCode.value = row.permissions || "";
  showTreeSelect.value = false; // 初始不显示树形选择器
  nextTick(() => {
    console.log(row);
    // 如果有已选设备，获取对应的节点ID
    if (row.permissions) {
      const deviceCodes = row.permissions.split(",");
      const selectedIds = [];

      const findDeviceIds = (nodes) => {
        nodes.forEach((node) => {
          if (node.children?.length < 1 || !node.children) {
            const nodeDeviceCode = node.label.split("-")[1] ?? node.label;
            if (deviceCodes.includes(nodeDeviceCode)) {
              selectedIds.push(node.id);
            }
          } else if (node.children.length > 0) {
            findDeviceIds(node.children);
          }
        });
      };

      findDeviceIds(deviceTreeData.value[0].children);
      selectedDeviceIds.value = selectedIds;
    } else {
      selectedDeviceIds.value = [];
    }
    deviceTreeRef2.value.setCheckedKeys(selectedDeviceIds.value || []);
  });
}

// 修改保存设备权限
function saveDevicePermission() {
  if (!currentRow.value) {
    return;
  }

  const params = {
    id: currentRow.value.id,
    name: currentRow.value.name,
    type: currentRow.value.type,
    remark: currentRow.value.remark,
    permissions: selectedDeviceCode.value,
  };

  updateEmployeesPost(params).then((res) => {
    if (res.code === 200) {
      proxy.$modal.msgSuccess("设备权限设置成功");
      deviceDialogVisible.value = false;
      showTreeSelect.value = false;
      getList(); // 刷新列表
    }
  });
}

// 根据设备编号获取对应的树节点id (示例实现)
function getDeviceIds(deviceCode) {
  const codes = deviceCode.split(",");
  const ids = [];
  // 这里需要根据实际数据结构来实现
  deviceTreeData.value.forEach((node) => {
    traverseTree(node, codes, ids);
  });
  return ids;
}

// 遍历树形结构查找设备编号对应的节点id
function traverseTree(node, codes, ids) {
  if (node.children?.length < 1 || !node.children) {
    const deviceCode = node.label.split("-")[1];
    if (codes.includes(deviceCode)) {
      ids.push(node.id);
    }
    return;
  }
  node.children.forEach((child) => traverseTree(child, codes, ids));
}

// 处理设备树选择变化
function handleDeviceCheck(data, { checkedNodes, checkedKeys }) {
  // 获取选中的设备编号
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children) // 只取叶子节点
    .map((node) => node.label.split("-")[1] ?? node.label)
    .join(",");

  // 保存选中的节点ID
  if (deviceDialogVisible.value) {
    selectedDeviceCode.value = devices;
    selectedDeviceIds.value = checkedKeys; // 新增：保存选中的ID
  } else {
    form.selectedDevices = devices;
    form.selectedDeviceIds = checkedKeys; // 新增：保存选中的ID
  }
}

// 修改显示/隐藏树形选择器的逻辑
watch(
  () => showTreeSelect.value,
  (newVal) => {
    if (newVal) {
      // 显示树形选择器时，设置选中状态
      nextTick(() => {
        if (deviceDialogVisible.value) {
          deviceTreeRef.value?.setCheckedKeys(selectedDeviceIds.value || []);
        } else {
          deviceTreeRef.value?.setCheckedKeys(form.selectedDeviceIds || []);
        }
      });
    }
  }
);

// 处理删除按钮点击
function handleDelete(row) {
  proxy.$modal
    .confirm('是否确认删除岗位名称为"' + row.name + '"的数据项?')
    .then(() => {
      delEmployeesPost(row.id).then((res) => {
        if (res.code === 200) {
          proxy.$modal.msgSuccess("删除成功");
          getList();
        }
      });
    })
    .catch(() => {});
}

// 定义不可删除的岗位编号列表
const unDeletableCodes = ["ZGJS000001", "ZGJS000002", "ZGJS000003"];
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
.mb-3 {
  margin-bottom: 12px;
  font-weight: 700;
}
.mt-3 {
  margin-top: 20px;
  margin-bottom: 20px;
}

.tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  // max-height: 250px;
  height: 200px;
  overflow-y: auto;
}

.device-select-row {
  display: flex;
  align-items: center;
  gap: 12px;

  .label {
    white-space: nowrap;
    align-items: flex-start;
    box-sizing: border-box;
    color: #606266;
    display: inline-flex;
    flex: 0 0 auto;
    font-size: 14px;
    height: 32px;
    justify-content: flex-end;
    line-height: 32px;
    padding: 0 12px 0 0;
    font-weight: 700;
  }

  .el-input {
    flex: 1;
    cursor: pointer;
  }
}

:deep(.el-form-item) {
  .el-input {
    cursor: pointer;
  }
}
.test {
  margin-left: 20px;
}
</style>
