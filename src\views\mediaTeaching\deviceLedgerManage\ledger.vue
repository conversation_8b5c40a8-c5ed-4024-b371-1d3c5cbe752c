<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备编码/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="typeId">
          <el-select
            v-model="queryParams.typeId"
            placeholder="请选择设备类型"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in typeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="tagId">
          <el-select
            v-model="queryParams.tagId"
            placeholder="请选择设备标签"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in tagList"
              :key="item.tagId"
              :label="item.tag"
              :value="item.tagId"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="deviceStatus">
          <el-select
            v-model="queryParams.deviceStatus"
            placeholder="请选择设备状态"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="time">
          <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            start-placeholder="入库开始时间"
            :disabled-date="disabledDateFn"
            end-placeholder="入库结束时间"
            @change="handleQuery"
            @clear="handleQuery"
            style="width: 245px"
          />
        </el-form-item>
        <el-form-item label="" prop="addressStr">
          <el-tree-select
            v-model="queryParams.addressStr"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            style="width: 200px"
            clearable
            @change="handleQueryPosition"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12" style="gap: 10px 0;">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >添加设备</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Delete"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleDelete"
            >批量删除</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Setting"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleRepair"
            >批量报障</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <div style="display: flex; align-items: center">
            <el-upload
              :action="uploadFileUrl"
              :headers="headers"
              :show-file-list="false"
              :before-upload="beforeUpload"
              :on-success="handleUploadSuccess"
            >
              <el-button type="success" plain icon="Download">
                一键导入
              </el-button>
            </el-upload>
            <QuestionFilled
              style="
                width: 20px;
                height: 20px;
                margin-left: 5px;
                cursor: pointer;
              "
              @click="showExport = true"
            />
          </div>
        </el-col>
        <el-col :span="1.5">
          <el-button type="warning" plain icon="Upload" @click="handleExport"
            >一键导出</el-button
          >
        </el-col>
        <!-- <el-col :span="1.5">
                    <el-button type="primary" plain icon="Download" @click="handleDownload">下载导入模板</el-button>
                </el-col> -->
        <el-col :span="1.5">
          <el-button type="success" plain icon="Download" @click="handleActive"
            >生成设备激活码</el-button
          >
        </el-col>

        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="Download"
            @click="handlePrintCode"
            style="background-color: #7728f5; color: white"
            >一键打印设备二维码</el-button
          >
        </el-col>
        <!-- <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar> -->
      </el-row>

      <el-table
        ref="ledgerTable"
        v-loading="loading"
        :data="tableData"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
          show-overflow-tooltip
        />
        <el-table-column
          label="设备名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="设备标签"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="tagName"
        />
        <el-table-column
          label="设备状态"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
        >
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.deviceStatus]"
              effect="dark"
              :type="statusList[scope.row.deviceStatus].type"
              >{{ statusList[scope.row.deviceStatus].label }}</el-tag
            >
          </template>
        </el-table-column>
        <!-- <el-table-column label="设备图片" align="center" show-overflow-tooltip minWidth="120px">
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="cover"
            />
          </template>
        </el-table-column> -->
        <el-table-column
          label="安装位置"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="installAddress"
        />
        <el-table-column
          label="资产端口类别"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="信息资产等级"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="信息资产类别"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="品牌"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="入库时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="putTime"
        />
        <el-table-column
          label="操作"
          :min-width="300"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Search"
              @click.stop="handleCheck(scope.row, true)"
              >详情</el-button
            >
            <el-button
              link
              type="warning"
              icon="Edit"
              @click.stop="handleCheck(scope.row, false)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.stop="handleDelete(scope.row)"
              >删除</el-button
            >
            <el-button
              link
              type="success"
              icon="Setting"
              @click.stop="handleRepair(scope.row)"
              >报修</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加修改设备 -->
    <el-dialog
    class="search-list"
      :title="title"
      v-model="open"
      top="6vh"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-form
        ref="ledgerRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        :validate-on-rule-change="false"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input
                v-model="form.deviceName"
                :readonly="readonly"
                placeholder="请输入设备名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="typeId">
              <el-select
                v-model="form.typeId"
                placeholder="请选择类型"
                :disabled="
                  readonly || (form.deviceType == '智慧大屏' && !!form.deviceId)
                "
                filterable
                style="width: 100%"
                @change="handleChangeType"
              >
                <el-option
                  v-for="item in typeList_disabled"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                  :disabled="item.disabled || !!item.status"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备标签" prop="tagIdList">
              <el-select
                v-model="form.tagIdList"
                placeholder="请选择标签"
                :disabled="readonly"
                filterable
                multiple
                style="width: 100%"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tag"
                  :value="item.tagId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input
                v-model="form.deviceCode"
                :readonly="readonly"
                :disabled="form.deviceType == '智慧大屏' && !!form.deviceId"
                @blur="form.deviceCode = form.deviceCode.replace(/\s*/g, '')"
                placeholder="请输入设备编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item
              label="安装位置"
              prop="installAddressId"
              v-if="!readonly"
            >
              <el-tree-select
                v-model="form.installAddressId"
                :props="positionProps"
                :data="positionTreeList_disabled"
                placeholder="请选择安装位置"
                :render-after-expand="false"
                clearable
                style="width: 100%"
                @change="handlePosition"
              >
              </el-tree-select>
            </el-form-item>
            <el-form-item
              label="安装位置"
              prop="installAddress"
              v-if="readonly"
            >
              <el-input
                v-model="form.installAddress"
                :readonly="readonly"
                placeholder="-"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物理地址" prop="macAddress">
              <el-input
                v-model="form.macAddress"
                :readonly="readonly"
                placeholder="请输入物理地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="model">
              <el-input
                v-model="form.model"
                :readonly="readonly"
                placeholder="请输入规格型号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="逻辑地址" prop="logicAddress">
              <el-input
                v-model="form.logicAddress"
                :readonly="readonly"
                placeholder="请输入逻辑地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input
                v-model="form.ipAddress"
                :readonly="readonly"
                placeholder="请输入IP地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作系统" prop="osVersion">
              <el-input
                v-model="form.osVersion"
                :readonly="readonly"
                placeholder="请输入操作系统版本号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="deviceStatus">
              <el-select
                v-model="form.deviceStatus"
                placeholder="请选择状态"
                disabled
                style="width: 100%"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CPU" prop="cpu">
              <el-input
                v-model="form.cpu"
                :readonly="readonly"
                placeholder="请输入CPU型号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input
                v-model="form.brand"
                :readonly="readonly"
                placeholder="请输入设备品牌名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内存" prop="internalStorage">
              <el-input
                v-model="form.internalStorage"
                :readonly="readonly"
                placeholder="请输入设备内存"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="硬盘" prop="disk">
              <el-input
                v-model="form.disk"
                :readonly="readonly"
                placeholder="请输入设备硬盘"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库时间" prop="putTime">
              <el-date-picker
                v-model="form.putTime"
                format="YYYY-MM-DD"
                :disabled-date="disabledDateFn"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                :readonly="readonly"
                type="date"
                placeholder="请选择入库时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="边缘服务器" prop="ralayHost">
              <el-input
                v-model="form.ralayHost"
                :readonly="readonly"
                placeholder="请输入边缘服务器的ip地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备图片" prop="deviceImg">
              <imgUpload
                @update:modelValue="(url) => setImgUrl(url, 'deviceImg')"
                :limit="1"
                :modelValue="form.deviceImg"
                :disabled="readonly"
                :isShowTip="!readonly"
              />
            </el-form-item>
          </el-col>
          <!-- 新增的 -->
          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="信息资产等级"
              prop="assetLevel"
              label-width="110px"
            >
              <el-select
                v-model="form.assetLevel"
                placeholder="请选择信息资产等级"
                :disabled="readonly"
                style="width: 100%"
              >
                <el-option
                  v-for="item in assetLevelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="资产端口类别"
              prop="portType"
              label-width="110px"
            >
              <el-select
                v-model="form.portType"
                placeholder="请选择资产端口类别"
                :disabled="readonly"
                style="width: 100%"
              >
                <el-option
                  v-for="item in portTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="信息资产类别"
              prop="assetType"
              label-width="110px"
            >
              <el-select
                v-model="form.assetType"
                placeholder="请选择信息资产类别"
                :disabled="readonly"
                style="width: 100%"
              >
                <el-option
                  v-for="item in assetTypeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="设备二维码"
              prop="deviceQrCode"
              label-width="110px"
            >
              <div class="flex items-center">
                <el-button type="primary" @click="handleViewQrCode">
                  查看二维码
                </el-button>
                <a
                  class="download-link ml-4"
                  @click="handleDownloadQrCode"
                  style="color: red"
                >
                  下载
                </a>
              </div>
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="关联配置"
              prop="relatedDeviceCode"
              label-width="110px"
            >
              <el-input
                v-model="form.relatedDeviceCode"
                placeholder="请输入设备编码"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item
              label="关联人员"
              prop="relatedPersonName"
              label-width="110px"
            >
              <el-input
                v-model="form.relatedPersonName"
                placeholder="请输入相关人员名称"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item label="端口" prop="portNumber" label-width="110px">
              <el-input
                v-model="form.portNumber"
                placeholder="请输入端口"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>

          <el-col :span="12" v-if="readonly">
            <el-form-item label="线缆" prop="cableInfo" label-width="110px">
              <el-input
                v-model="form.cableInfo"
                placeholder="请输入线缆信息"
                :readonly="readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-show="!readonly" type="primary" @click="submitForm"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 填写报修单 -->
    <el-dialog
    class="search-list"
      title="报修单填写"
      v-model="showRepair"
      top="10vh"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-table
        :data="repairList"
        border
        style="margin: 0 auto 40px; width: 96%"
        max-height="300"
      >
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column label="设备图片" align="center" minWidth="120px">
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="contain"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          prop="installAddress"
        />
        <el-table-column
          label="物理地址"
          align="center"
          minWidth="120px"
          prop="macAddress"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          minWidth="120px"
          prop="logicAddress"
        />
        <el-table-column
          label="IP地址"
          align="center"
          minWidth="120px"
          prop="ipAddress"
        />
        <el-table-column
          label="操作系统"
          align="center"
          minWidth="120px"
          prop="osVersion"
        />
        <el-table-column
          label="CPU"
          align="center"
          minWidth="120px"
          prop="cpu"
          show-overflow-tooltip
        />
        <el-table-column
          label="品牌"
          align="center"
          minWidth="120px"
          prop="brand"
        />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          prop="internalStorage"
        />
        <el-table-column
          label="硬盘"
          align="center"
          minWidth="120px"
          prop="disk"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          prop="putTime"
        />
      </el-table>

      <el-form
        ref="repairRef"
        :model="repairForm"
        :rules="repairRules"
        label-width="140px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="报修人" prop="repairName">
              <el-input
                v-model="repairForm.repairName"
                placeholder="请输入报修人"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修人联系方式" prop="repairPhone">
              <el-input
                v-model="repairForm.repairPhone"
                placeholder="请输入报修人联系方式"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障描述" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                placeholder="请输入故障描述"
                style="width: 780px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障图片" prop="images">
              <imgUpload
                @update:modelValue="
                  (url) => setImgUrl(url, 'images', 'array', 'repairForm')
                "
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修时间" prop="repairTime">
              <el-date-picker
                type="date"
                v-model="repairForm.repairTime"
                placeholder="请选择报修时间"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="相关附件" prop="attachments">
              <el-upload
                class="upload-demo"
                :action="uploadFileUrl"
                :headers="headers"
                :on-success="handleAttachmentSuccess"
                :on-remove="handleAttachmentRemove"
                :before-upload="beforeAttachmentUpload"
                multiple
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    可上传任意格式文件,单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRepairForm">确 定</el-button>
          <el-button @click="repairCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 生成激活码 -->
    <el-dialog
      title="生成激活码"
      top="30vh"
      v-model="open2"
      width="400px"
      :close-on-click-modal="false"
    >
      <div>生成数量： <el-input-number v-model="activeNum" :min="1" /></div>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleDownload2">确 定</el-button>
          <el-button @click="open2 = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 导入提示 -->
    <el-dialog title="关于导入" top="30vh" v-model="showExport" width="400px">
      <div style="padding: 0 20px">
        如果想一键导入设备信息，请下载当前导入模板，并按照规则填写表格，当前仅支持.xlsx与.xlx格式
        <div
          style="
            color: cornflowerblue;
            text-decoration: underline;
            margin-top: 20px;
            cursor: pointer;
          "
          @click="handleDownload"
        >
          点击下载导入模板.xlsx
        </div>
      </div>
    </el-dialog>
    <!-- 添加查看二维码的对话框 -->
    <el-dialog
      title="设备二维码"
      v-model="qrCodeDialogVisible"
      width="360px"
      append-to-body
      :align-center="true"
    >
      <div class="qrcode-container">
        <div class="qrcode-wrapper">
          <img :src="currentQrCode" alt="设备二维码" style="max-width: 240px" />
        </div>
      </div>
    </el-dialog>
    <ExportRes
      :data="exportRes"
      :open="open3"
      @setOpen="
        (val) => {
          open3 = val;
        }
      "
    />
    <div id="qrcode" style="display: none"></div>
  </div>
</template>

<script setup name="ledgerManage">
import imgUpload from "@/components/ImageUpload";
import ExportRes from "@/components/ExportRes";
import { useRoute } from "vue-router";
import {
  deviceAdd,
  deviceDel,
  deviceEdit,
  devicePage,
  deviceInfo,
  deviceRemove,
  deviceExport,
  templateDownload,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree, findPosition } from "@/api/mediaTeach/position";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import { getUserList } from "@/api/system/user";
import { downloadBlob, treeToArray, treeFindPath } from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import { sm2Decrypt } from "@/utils/sm2encrypt";
import { nextTick } from "vue";
import QRCode from "qrcode";
import { generateQRCode, wrapText } from "@/utils/uploadCode";
import { ElMessage } from "element-plus";
import { debounce } from "@/utils/debounce";

// console.log(sm2Decrypt('04e0a547f921fbb30a8b4195da2adc1d713d917c92742271667e7912fa893951638950ac2a1f99f91be47fc83a693bc1371acfd66485ba539b220ce3c6c2a4b6dfe371080b1973e8519fbf059db6fd826b6c614fb4314de4d2335cad7b878ac29fdd0e4457da01084110cbb29ebed56c11be5a3e890eb312e6be590b25993484c866633e3b7a42f77f8d1b0f0cdd9405d1ed889b87a1707ff1394683f98f8ef5ece18e82e1d025878137f818fe3b759cf4d626417b8b872ece63a5122ddf16198f39617ce983ef19d342ceaadcaacf4744f86af1c1ef19749b'))
// console.log(JSON.parse(sm2Decrypt('04d086fe249c7a025200a4026a585af210a7753ee03a77f996a2d31be7e4108c3d3ba04e95e72bd49710ee8cfaec082dec59ded92f749bc9bfd9334bc5b4d114e7fa5d6b5fb5010da2c819dc13756a27aced6dfb9026fa83672fb7b141e1f4fa1bb8ea9474fdc762dc0d5498e444c6068f1a6d6ba0f6b5107c11b4c6452c69eeafe9dfff3d52d8b797424387ec989d7fe4a56d81888be5c064cf813c167137ab9b0c7e1343ccf73c4e507b35ed8e74435799558b01a1dfcb1f7e4521d6e67d44418b53b7d309e0dd1c29a58e130832782ee3994800a0989579f71f160f0eb0c3f7e4a081d77457882e711ac6b7de1cf93e80bd21762a609345e134c8391abc9b30950a6f8436c8d85459f4ca488711f1ddafd35294fcfbd3fb3f7b11385318b1c865a98909ba2e1bbc9739d167fd3ef9d4179e0717b0772b1a450f78512f1cbd7438b6b8ebd94b226931d28212076cbce50f0b2731db853086aef6ff8b5c23ff09d2f232fddc7ee7931fb3b83d694b40c7c53bc4758a874f4c4fcb5a000bd027f53c4ecaf31921f4e58e243dd5a3c795f79e17625a164059e7438a4f2d3c5979097af24050830c3736de1e63ff5cf5237110348d7600339adb6dfb42586b294f216952ec29d1e2e4414c867a3d35265757f40476c37501a5c9292b8919a4c43d62c3938e14dee019b2c0054e8059d3ff5796c80a5a9f3797dc7ead2c7678fd2b5f8591f448fe02e4c1ad013dfc36e46f4468e26b5dd9fb65d347a54e1b6ef95e68f03959952326d67b2670872a00c50233fb48ca58ac6333d67152fc774acf490c862b33847eefd00028fee46c150384e8d697f88983462c9ad826d6d8f4cacc3aa76b9b29ab7b1123456dd597bef698a9bf59f073d434edc0acc9a92822e74a28c4a16ffcce1dde2c430ce7140a93145f51fac6c715a46ebe5c36d6d4ee64c4bfec309a8d54dffab690947040980fe3d0e9f0325c225c35ef84724c8fd9983bbe184a42c6c51135ce2749e78e5215a2255a384295325ad78f4f84c34e4165d6ac112dea7d2e3fad9a071c642ae75e18aabe2076757fa661e092b7e0bb75c09a54f5293c928752a631c7902003a30422734ad9d2958033d0607771fbabc2736b25f3e7bf6b27ee4aa7a0c0841f61422486a824b3541c0419ddd3e62b248eeb7c93aabe9283f915106088b6976dd936cfcae79ed426d4b8c218381b419d6b01f706cc72f7c21a10c8da5083457e831f0a7483286811efce25')))

const { proxy } = getCurrentInstance();
const route = useRoute();
const isAdmin = !useUserStore().tenantId;

console.log(isAdmin, "isAdmin");

const ledgerRef = ref(null);
const ledgerTable = ref(null);
const exportRes = ref({});
const ledgerList = ref([]);
const repairList = ref([]);
const open = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const activeNum = ref(1);
const loading = ref(false);
const showSearch = ref(true);
const showExport = ref(false);
const showRepair = ref(false);
const readonly = ref(false);
const ids = ref([]);
const positionResult = ref({
  names: [],
  ids: [],
});
const queryPositionResult = ref({
  names: [],
  ids: [],
});
const statusList = ref([
  { label: "正常", value: 0, type: "success" },
  { label: "维修中", value: 1, type: "danger" },
]);
const typeList = ref([]);
const typeList_disabled = ref([]);
const tagList = ref([]);
const positionTreeList = ref([]);
const positionTreeList_disabled = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});
const total = ref(0);
const title = ref("");
const uploadFileUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/device/importData"
); // 导入文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const smartRules = ref({
  tagIdList: [
    {
      required: true,
      message: "设备标签不能为空",
      trigger: ["blur", "change"],
    },
  ],
  macAddress: [
    { required: true, message: "物理地址不能为空", trigger: "blur" },
  ],
  logicAddress: [
    { required: true, message: "逻辑地址不能为空", trigger: "blur" },
  ],
  osVersion: [
    { required: true, message: "操作系统版本号不能为空", trigger: "blur" },
  ],
  cpu: [{ required: true, message: "CPU类型不能为空", trigger: "blur" }],
  internalStorage: [
    { required: true, message: "设备内存不能为空", trigger: "blur" },
  ],
  ipAddress: [{ required: true, message: "IP地址不能为空", trigger: "blur" }],
  ralayHost: [
    { required: true, message: "边缘服务器ip不能为空", trigger: "blur" },
  ],
  brand: [{ required: true, message: "设备品牌不能为空", trigger: "blur" }],
  disk: [{ required: true, message: "设备硬盘不能为空", trigger: "blur" }],
});
const baseRules = ref({
  deviceName: [
    { required: true, message: "设备名称不能为空", trigger: "blur" },
    { max: 50, message: "设备名称最多输入50个字符", trigger: "blur" },
  ],
  typeId: [{ required: true, message: "设备类型不能为空", trigger: "blur" }],
  deviceCode: [
    { required: true, message: "设备编码不能为空", trigger: "blur" },
  ],
  deviceStatus: [
    {
      required: true,
      message: "设备状态不能为空",
      trigger: ["blur", "change"],
    },
  ],
  deviceImg: [
    {
      required: true,
      message: "设备照片不能为空",
      trigger: ["blur", "change"],
    },
  ],
  installAddressId: [
    {
      required: true,
      message: "安装位置不能为空",
      trigger: ["blur", "change"],
    },
  ],
  model: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  putTime: [
    {
      required: true,
      message: "入库时间不能为空",
      trigger: ["blur", "change"],
    },
  ],
});

const data = reactive({
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // 保存表格勾选的行数据
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    installAddressId: "",
    positionId: "",
    createTime: "",
  },
  queryParams: {
    current: 1,
    size: 10,
    tenantId: useUserStore().tenantId,
    model: "",
    deviceName: "",
    deviceCode: "",
    deviceStatus: "",
    address: "",
    typeId: "",
    tagId: "",
    putTime: "",
    time: null,
    startTime: "",
    endTime: "",
  },
  rules: {},
  repairForm: {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报修人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      { required: true, message: "报修人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    remark: [
      { required: true, message: "故障描述不能为空", trigger: "blur" },
      { max: 200, message: "故障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        type: "array",
        min: 1,
        message: "故障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  queryParams,
  form,
  repairForm,
  repairRules,
  tableData,
  rules,
  tableAllSelectedId,
} = toRefs(data);

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

// 打印设备二维码
const handlePrintCode = debounce(() => {
  let rows = data.tableAllSelectedRow;
  if (rows.length == 0) {
    ElMessage.error("请选择设备");
    return;
  }
  let obj = rows.map((item, index) => {
    return {
      deviceCode: item.deviceCode,
      deviceName: item.deviceName,
      installAddress: item.installAddress,
    };
  });

  console.log(obj);
  generateQRCode(obj);
}, 250);

const qrCodeDialogVisible = ref(false);
const currentQrCode = ref("");

// 添加生成单个二维码的方法
async function generateSingleQRCode(deviceData) {
  const { deviceCode, deviceName, installAddress } = deviceData;

  try {
    // 显著减小二维码尺寸
    const qrUrl = await QRCode.toDataURL(deviceCode, {
      width: 128, // 调整为更小的尺寸
      margin: 1, // 减小边距
      scale: 4, // 保持清晰度
    });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    return new Promise((resolve) => {
      img.onload = () => {
        const scale = 2;
        canvas.width = (img.width + 60) * scale; // 继续减小边距
        canvas.height = (img.height + 20 + 15 * 3) * scale;

        ctx.scale(scale, scale);
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.fillStyle = "white";
        ctx.fillRect(0, 0, canvas.width / scale, canvas.height / scale);

        const qrX = (canvas.width / scale - img.width) / 2;
        const qrY = 10;

        ctx.drawImage(img, qrX, qrY);

        const prompt = `设备名称：${deviceName}\n设备编码：${deviceCode}\n安装位置：${installAddress}`;
        ctx.fillStyle = "black";
        ctx.font = "bold 11px Arial"; // 稍微调小字体

        const promptLines = wrapText(ctx, prompt, 150); // 减小文本宽度
        promptLines.forEach((line, index) => {
          const textY = qrY + img.height + 15 + 13 * index; // 调整行间距
          const textWidth = ctx.measureText(line).width;
          const xPosition = (canvas.width / scale - textWidth) / 2;
          ctx.fillText(line, xPosition, textY);
        });

        resolve(canvas.toDataURL("image/png", 1.0));
      };
      img.src = qrUrl;
    });
  } catch (error) {
    console.error("Error generating QR code:", error);
    return null;
  }
}

// 查看二维码方法
async function handleViewQrCode() {
  try {
    const deviceData = {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    };

    const qrCodeUrl = await generateSingleQRCode(deviceData);
    if (qrCodeUrl) {
      currentQrCode.value = qrCodeUrl;
      qrCodeDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError("生成二维码失败");
    console.error(error);
  }
}

// 下载二维码方法
function handleDownloadQrCode() {
  const deviceData = [
    {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    },
  ];

  generateQRCode(deviceData);
}

function handlePosition(val) {
  positionResult.value = {
    ids: [],
    names: [],
  };
  positionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = positionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    positionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("positionResult ==> ", positionResult.value);
}

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  queryParams.value.address = queryPositionResult.value.names.join("-");
  handleQuery();
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

/** 查询台账列表 */
function getList() {
  loading.value = true;
  console.log("查询的参数", data.queryParams);

  devicePage(data.queryParams).then((response) => {
    const { page } = response.data;
    console.log("台账列表", page);
    data.tableData = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tagId,
      } = cur;
      let arr = tagId.split(","),
        names = [];
      arr.map((item) => {
        let obj = tagList.value.find((_) => _.tagId == item);
        // console.log(obj)
        !!obj ? names.push(obj.tag) : "";
      });
      res.push({
        ...cur,
        tagName: names.join("，"),
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
      });
      return res;
    }, []);
    total.value = page.total;
    loading.value = false;
    nextTick(() => {
      ledgerList.value.forEach((item) => {
        if (data.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, data.tableAllSelectedId)
          data.tableData.toggleRowSelection(item, true);
        } else {
          data.tableData.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
    console.log(data.tableData);
  });
  devicePage({
    current: 1,
    size: 9999999,
  }).then((res) => {
    // console.log(res)
    data.tableData_all = res.data.page.records;
  });

}

async function getTagList() {
  await getDeviceTag().then((response) => {
    // console.log('tagList', response.data)
    tagList.value = response.data;
  });
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    positionTreeList_disabled.value = addAttr(tree);
    positionList.value = treeToArray(response.data);
    console.log("arr", positionTreeList_disabled.value, response);
    // console.log('positionList ==>', positionList.value)
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

function getTypeList() {
  getDeviceType().then((response) => {
    typeList.value = response.data;
    typeList_disabled.value = response.data.reduce((res, cur) => {
      res.push({
        ...cur,
        disabled: cur.typeName == "智慧大屏",
      });

      return res;
    }, []);
    // console.log('typeList_disabled', typeList_disabled.value)
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
}
/** 表单重置 */
function repairReset() {
  repairForm.value = {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  };
  proxy.resetForm("repairRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  queryParams.value.startTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[0]
    : "";
  queryParams.value.endTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[1]
    : "";
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.startTime = "";
  queryParams.value.endTime = "";
  proxy.resetForm("queryRef");
  handleQueryPosition();
}
// 根据类型动态修改校验规则
function handleChangeType(val) {
  let name = "";
  typeList.value.forEach((item) => {
    if (item.id == val) name = item.typeName;
  });
  data.rules = {};
  if (name == "智慧大屏") {
    if (baseRules.value.deviceCode[1]) delete baseRules.value.deviceCode[1];
    Object.assign(data.rules, baseRules.value, smartRules.value);
  } else {
    baseRules.value.deviceCode[1] = {
      pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
      message: "设备编码不能以“ZHDP”开头",
      trigger: "blur",
    };
    Object.assign(data.rules, baseRules.value);
  }
  ledgerRef.value.clearValidate();
}
/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      data.tableData.setCurrentRow(null);
      data.tableData.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.deviceId);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      data.tableData.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    data.tableData.setCurrentRow(row);
    data.tableData.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      data.tableAllSelectedId.push(item.deviceId);
      data.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.deviceId);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = ledgerList.value;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        data.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 新增按钮操作 */
function handleAdd() {
  Object.assign(data.rules, baseRules.value);
  reset();
  readonly.value = false;
  open.value = true;
  title.value = "添加设备";
}
/** 查看按钮操作 */
function handleCheck(row, type) {
  reset();
  title.value = type ? "查看设备详情" : "编辑设备信息";
  readonly.value = type;
  deviceInfo({ id: row.deviceId }).then((response) => {
    console.log("deviceInfo", response);
    const { positionId, deviceType } = response.data;
    data.rules = {};
    if (!type) {
      if (deviceType == "智慧大屏") {
        if (baseRules.value.deviceCode[1]) delete baseRules.value.deviceCode[1];
        Object.assign(data.rules, baseRules.value, smartRules.value);
      } else {
        baseRules.value.deviceCode[1] = {
          pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
          message: "设备编码不能以“ZHDP”开头",
          trigger: "blur",
        };
        Object.assign(data.rules, baseRules.value);
      }
    }
    const arr = !!positionId ? positionId.split(",") : [];
    positionResult.value.ids = JSON.parse(JSON.stringify(arr));
    form.value = JSON.parse(JSON.stringify(response.data));
    form.value.installAddressId = arr.length > 0 ? arr[arr.length - 1] * 1 : "";
    for (let i = 0; i < arr.length; i++) {
      positionResult.value.names[i] = positionList.value.find(
        (node) => node.id == arr[i]
      )?.name;
    }
    open.value = true;
  });
}
/** 提交按钮 */
function submitForm() {
  form.value.deviceCode = form.value.deviceCode.replace(/\s*/g, "");
  proxy.$refs["ledgerRef"].validate((valid) => {
    if (valid) {
      form.value.installAddress =
        positionResult.value.names.join("-") || form.value.installAddress;
      form.value.positionId =
        positionResult.value.ids.join(",") || form.value.positionId;
      form.value.tagId = !!form.value.tagIdList
        ? form.value.tagIdList.join(",")
        : "";
      console.log("提交的表单", form.value);
      if (!!form.value.deviceId) {
        deviceEdit(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          open.value = false;
          reset();
        });
      } else {
        deviceAdd(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          open.value = false;
          reset();
        });
      }
    }
  });
}

function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    if (valid) {
      repairForm.value.deviceCodeList = [];
      repairList.value.map((item) => {
        repairForm.value.deviceCodeList.push(item.deviceCode);
      });

      // 构建提交数据
      const submitData = {
        ...repairForm.value,
        channel: "管理端-管理员主动报修",
        attachments: repairForm.value.attachments.map((item) => item.url), // 只提交文件URL
      };

      deviceRepair(submitData).then((res) => {
        proxy.$modal.msgSuccess("报修成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

function handleRepair(row) {
  if (tableAllSelectedId.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  repairReset();
  repairList.value = row.deviceId ? [row] : data.tableAllSelectedRow;
  // console.log(repairList.value)
  showRepair.value = true;
}

/** 删除按钮操作 */
function handleDelete(row) {
  const deviceIds = row.deviceId || tableAllSelectedId.value;
  const deviceNames = [];
  if (tableAllSelectedId.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  // console.log('delete', row, Array.isArray(deviceIds))
  if (Array.isArray(deviceIds)) {
    deviceIds.map((item) =>
      deviceNames.push(
        data.tableAllSelectedRow.find((_) => _.deviceId == item).deviceName
      )
    );
  } else {
    deviceNames.push(row.deviceName);
  }

  proxy.$modal
    .confirm('是否确认删除设备名称为"' + deviceNames.join("、") + '"的数据项？')
    .then(async function () {
      if (typeof deviceIds === "object") {
        await deviceRemove({ idList: deviceIds });
      } else {
        await deviceDel({ id: deviceIds });
      }
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 导出按钮操作 */
function handleExport() {
  deviceExport(queryParams.value).then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "设备清单");
  });
}

const beforeUpload = (file) => {
  const Xls = file.name.split(".");
  if (Xls[1] !== "xls" && Xls[1] !== "xlsx") {
    proxy.$modal.msgError("请上传excel格式的文件!");
    return false;
  } else if (file.size / 1024 / 1024 > 250) {
    proxy.$modal.msgError("请上传250M以下的文件!");
    return false;
  }
  return true;
};

const handleUploadSuccess = (res, file) => {
  if (res.code === 200) {
    getList();
    exportRes.value = JSON.parse(sm2Decrypt(res.data));
    let { errorCount } = exportRes.value;
    if (!errorCount) {
      proxy.$modal.msgSuccess("导入成功");
    } else {
      open3.value = true;
    }
  } else {
    proxy.$modal.msgError(res.msg);
  }
};

function handleDownload() {
  templateDownload({ modelName: "import_device" }).then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "设备导入模板");
  });
}

function handleDownload2() {
  exportDeviceCode({ num: activeNum.value }).then((response) => {
    downloadBlob(response, "application/vnd.ms-excel", "设备激活码");
    proxy.$modal.msgSuccess("操作成功");
    open2.value = false;
  });
}

function handleActive() {
  activeNum.value = 1;
  open2.value = true;
}
/** 设置图片url */
function setImgUrl(url, name, type = "string", formName = "form") {
  // console.log(url)
  if (type == "array") {
    url.split(",").length > 0
      ? (data[formName][name] = url.split(","))
      : data[formName][name].push(url);
  } else {
    data[formName][name] = url;
  }
  // console.log(repairForm.value, data.repairForm)
}

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

getPositionTreeList();
getTagList();
getTypeList();
getList();
</script>
<style scoped lang="scss">
.qrcode-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0; /* 继续减小内边距 */
}

.qrcode-wrapper {
  text-align: center;
  width: fit-content;
  max-width: 100%;
}

.qrcode-wrapper img {
  width: auto;
  max-width: 100%;
  height: auto;
}
</style>
