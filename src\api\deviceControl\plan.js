import request from "@/utils/request";

// 锁屏屏保回显
export function downScreenImg() {
  return request({
    url: "/system/schoolSoftware/downScreenImg",
    method: "get",
  });
}

// 删除锁屏规则（无操作、自动锁、锁屏设置）
export function deleteLockScreenRule(params) {
  return request({
    url: "/system/device/deleteLockScreenRule",
    method: "get",
    params,
  });
}

// 删除开关机计划
export function deleteSwitchMachine(params) {
  return request({
    url: "/system/timePlan/delete/switchMachine",
    method: "get",
    params,
  });
}

// 开关机规则列表 /system/timePlan/switchMachine/list
export function listSwitchMachine(params) {
  return request({
    url: "/system/timePlan/switchMachine/list",
    method: "get",
    params,
  });
}

// 锁屏规则回显
export function getLockScreenRule(params) {
  return request({
    url: "/system/device/getLockScreenRule",
    method: "get",
    params,
  });
}

// 获取锁屏规则计划（最多5条）
export function getAllRules(params) {
  return request({
    url: "/system/device/getAll",
    method: "get",
    params,
  });
}

// 检测设备是否绑定过锁屏规则
export function checkLockScreenRule(params) {
  return request({
    url: "/system/device/checkLockScreenRule",
    method: "get",
    params,
  });
}

// 锁屏规则设置/修改
export function lockScreenRuleBatch(data) {
  return request({
    url: "/system/device/lockScreenRule/batch",
    method: "post",
    data,
  });
}

// 锁屏规则设置/修改
export function lockScreenRuleBatch_nomsg(data) {
  return request({
    url: "/system/device/lockScreenRule/batch",
    method: "post",
    data,
    noMsg: true,
  });
}
