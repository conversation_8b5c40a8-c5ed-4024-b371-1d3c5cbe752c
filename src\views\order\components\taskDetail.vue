<template>
  <div class="taskInfo">
    <el-form
      ref="formRef"
      class="taskInfo-form"
      :model="taskInfo"
      :rules="rules"
    >
      <el-descriptions title="" border :column="2">
        <el-descriptions-item
          v-if="!isAdd"
          label-class-name="label-width"
          class-name="value-width"
          label="任务单编号"
        >
          {{ taskInfo.taskNo || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="任务单名称"
        >
          <span v-if="!isAdd">{{ taskInfo.taskName || "-" }}</span>
          <el-form-item v-else label="" prop="taskName">
            <el-input
              v-model="taskInfo.taskName"
              placeholder="请输入任务单名称"
              clearable
              style="width: 250px"
              maxlength="10"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="归属项目"
        >
          <span v-if="!(isAdd || props.projectId)">{{
            taskInfo.projectName || "-"
          }}</span>
          <el-form-item v-else label="" prop="projectName">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="taskInfo.projectName"
              placement="top"
              :disabled="!taskInfo.projectName"
            >
              <el-input
                v-model="taskInfo.projectName"
                readonly
                :disabled="!!props.projectId"
                placeholder="请选择归属项目"
                @click="handleShowSelect('归属项目')"
                style="width: 250px"
              />
            </el-tooltip>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="归属部门"
        >
          <span v-if="!(isAdd || props.projectId)">{{
            taskInfo.deptName || "-"
          }}</span>
          <el-form-item v-else label="" prop="deptName">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="taskInfo.deptName"
              placement="top"
              :disabled="!taskInfo.deptName"
            >
              <el-input
                v-model="taskInfo.deptName"
                disabled
                placeholder="请先选择归属项目"
                style="width: 250px"
              />
            </el-tooltip>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="任务指派人员"
        >
          <span v-if="!isAdd">{{ taskInfo.assignName || "-" }}</span>
          <el-form-item v-else label="" prop="assignName">
            <el-tooltip
              class="box-item"
              effect="dark"
              :content="taskInfo.assignName"
              placement="top-start"
              :disabled="!taskInfo.assignName"
            >
              <el-input
                v-model="taskInfo.assignName"
                readonly
                placeholder="请选择任务指派人员"
                @click="handleShowSelect('任务指派人员')"
                style="width: 250px"
              />
            </el-tooltip>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          v-if="!isAdd"
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ taskInfo.createdTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          v-if="!isAdd"
          label-class-name="label-width"
          class-name="value-width"
          label="处理时间"
          :span="2"
        >
          {{ taskInfo.handleTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="任务描述"
        >
          <el-form-item label="" prop="taskContent" style="width: 100%">
            <editor
              v-model="taskInfo.taskContent"
              :min-height="200"
              type="url"
              @update:modelValue="contentChange"
              @blur="formRef.validateField('taskContent')"
              :isEdit="isAdd"
            />
            <div
              v-show="isEdit"
              style="position: absolute; bottom: -30px; right: 10px"
            >
              共<span :style="{ color: contentTotal > 500 ? '#f56c6c' : '' }">{{
                ` ${contentTotal} `
              }}</span
              >字
            </div>
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <el-dialog
      class="custom-dialog"
      :title="`请选择${title}`"
      v-model="dialogVisible"
      top="10vh"
      width="700"
    >
      <el-form
        ref="queryRef"
        class="search-list"
        :model="queryParams"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item
          v-if="title == '任务指派人员'"
          label=""
          prop="nickNameAndWorkId"
        >
          <el-input
            placeholder="请输入工号/名称"
            clearable
            style="width: 200px"
            v-model="queryParams.nickNameAndWorkId"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item v-if="title == '归属项目'" label="" prop="searchKey">
          <el-input
            placeholder="请输入编号/名称/项目负责人"
            clearable
            style="width: 200px"
            v-model="queryParams.searchKey"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item v-if="title == '归属部门'" label="" prop="deptName">
          <el-input
            placeholder="请输入部门名称"
            clearable
            style="width: 200px"
            v-model="queryParams.deptName"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        ref="userTableRef"
        v-loading="loading"
        v-if="title == '任务指派人员'"
        :data="tableList_user"
        border
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        <el-table-column label="工号" prop="workId" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="姓名" prop="nickName" show-overflow-tooltip />
        <el-table-column label="性别" prop="sex" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.sex == 0 ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column label="所属部门" prop="deptName" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.sysDept?.deptName || "-" }}
          </template>
        </el-table-column>
      </el-table>

      <el-table
        v-loading="loading"
        v-if="title == '归属项目'"
        :data="tableList_project"
        border
        @row-click="rowSingleClick"
      >
        <el-table-column width="55" align="center" fixed="left">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              label=""
              @click.native.prevent
              @change="
                (val) => {
                  handleSingleChange(val, $index);
                }
              "
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          label="项目编号"
          prop="projectNo"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目名称"
          prop="projectName"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目负责人"
          prop="responsibleNameStr"
          show-overflow-tooltip
        />
        <el-table-column
          label="归属部门"
          prop="projectDeptName"
          show-overflow-tooltip
        />
      </el-table>

      <el-table
        v-loading="loading"
        v-if="title == '归属部门'"
        :data="tableList_dept"
        border
        @row-click="rowSingleClick"
      >
        <el-table-column width="55" align="center" fixed="left">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              label=""
              @change="handleSingleChange"
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column label="部门名称" prop="deptName" />
        <el-table-column label="部门负责人" prop="id" />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :background="false"
        layout="total,prev,pager,next"
      />

      <template #footer>
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </template>
    </el-dialog>
  </div>
</template>
  
  <script setup>
import { reactive, toRefs, getCurrentInstance } from "vue";
import { listUser } from "@/api/system/user";
import { findIndexInObejctArr } from "@/utils";
import {
  addProjectTask,
  getSchoolProjectPage,
  schoolProjectInfo,
} from "@/api/order";

const { proxy } = getCurrentInstance();
const emits = defineEmits(["handleSubmit"]);
const props = defineProps({
  projectId: {
    type: String,
    default: "",
  },
  // 查看/编辑状态
  isEdit: {
    type: Boolean,
    default: true,
  },
  // 是否新增
  isAdd: {
    type: Boolean,
    default: true,
  },
});
const state = reactive({
  userTableRef: null,
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  tableRadio: [],
  contentTotal: 0,
  queryRef: null,
  formRef: null,
  loading: false,
  title: "任务指派人员",
  total: 2,
  dialogVisible: false,
  form: {},
  curProject: {},
  tableList_user: [],
  tableListAll_user: [],
  tableList_project: [],
  tableList_dept: [],
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    nickNameAndWorkId: "",
    numberAndNameAndUser: "",
    deptName: "",
  },
  userList: [],
  deptList: [],
  projectList: [],
  taskInfo: {
    taskName: "",
    taskContent: "",
    deptId: "",
    deptName: "",
    projectName: "",
    projectId: "",
    assignName: "",
    assignId: "",
    isAllowUpdate: 1,
  },
  rules: {
    taskName: [
      {
        required: true,
        message: "任务单名称不能为空",
        trigger: "blur",
      },
    ],
    projectName: [
      {
        required: true,
        message: "归属项目不能为空",
        trigger: ["blur", "change"],
      },
    ],
    deptName: [
      {
        required: true,
        message: "归属部门不能为空",
        trigger: ["blur", "change"],
      },
    ],
    taskContent: [
      {
        required: true,
        message: "任务描述不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  userTableRef,
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableListAll_user,
  contentTotal,
  curProject,
  queryRef,
  formRef,
  loading,
  rules,
  title,
  total,
  form,
  taskInfo,
  userList,
  deptList,
  tableList_user,
  tableList_project,
  tableList_dept,
  projectList,
  dialogVisible,
  queryParams,
} = toRefs(state);

watch(
  () => props.projectId,
  (newVal) => {
    if (!!newVal) {
      schoolProjectInfo(newVal).then((resp) => {
        console.log("项目详情", resp);
        if (resp.data) {
          const { projectDeptId, projectDeptName, projectName, projectId } =
            resp.data;
          Object.assign(state.taskInfo, {
            deptId: projectDeptId,
            deptName: projectDeptName,
            projectName,
            projectId,
          });
        }
      });
    }
  }
);

// 统计活动内容纯文本字数
function contentChange(val) {
  //   console.log("纯文本", val);
  // infotaskInfo.value.content = infotaskInfo.value.content.subString(0, 500)
  contentTotal.value = val
    .replace(/<[^>]+>/g, "", "")
    .replace(/\s/g, "").length;
}

function getList() {
  state.loading = true;
  clearCheck();
  if (state.title == "归属项目") {
    getSchoolProjectPage(state.queryParams)
      .then((resp) => {
        console.log("项目列表", resp);
        if (resp.data) {
          const { rows = [], total = 0 } = resp.data;
          state.tableList_project = rows.map((item) => {
            const { sysResponsibleName, responsibleName } = item;
            item.responsibleNameStr = (sysResponsibleName?.split("、") || [])
              .concat(responsibleName?.split("、") || [])
              .filter((_) => !!_)
              .join("、");
            item.checked = state.curProject == item.projectId;
            return item;
          });
          state.total = total;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
  if (state.title == "任务指派人员") {
    const { pageNum, pageSize } = state.queryParams;
    listUser({ ...state.queryParams, current: pageNum, size: pageSize })
      .then((resp) => {
        console.log("用户列表", resp);
        if (resp.data) {
          const { records = [], total = 0 } = resp.data;
          state.tableList_user = records;
          state.total = total;
          nextTick(() => {
            state.tableList_user.forEach((item) => {
              if (state.tableAllSelectedId.indexOf(item.userId) > -1) {
                state.userTableRef.toggleRowSelection(item, true);
              } else {
                state.userTableRef.toggleRowSelection(item, false);
              }
            });
          });
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));

    listUser({ current: 1, size: 999999 }).then((resp) => {
      console.log("全部用户列表", resp);
      if (resp.data) {
        const { records = [], total = 0 } = resp.data;
        state.tableListAll_user = records;
      }
    });
  }
}

function handleShowSelect(title) {
  state.title = title;
  resetQuery();
  state.dialogVisible = true;
}

function handleSubmit() {
  state.formRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      if (!!props.projectId) state.taskInfo.isAllowUpdate = 0;
      console.log("新增任务单传参", state.taskInfo);
      addProjectTask(state.taskInfo)
        .then((resp) => {
          proxy.$modal.msgSuccess("新增成功");
          handleBack();
          proxy.$modal.closeLoading();
        })
        .catch(() => proxy.$modal.closeLoading());
    }
  });
}

function handleCancel() {
  state.dialogVisible = false;
}

function handleConfirm() {
  if (state.title == "归属项目") {
    if (!state.curProject.projectId) {
      proxy.$modal.msgWarning("请选择项目");
      return;
    }
    const { projectId, projectName, projectDeptId, projectDeptName } =
      state.curProject;
    Object.assign(state.taskInfo, {
      ...state.taskInfo,
      projectId,
      projectName,
      deptId: projectDeptId,
      deptName: projectDeptName,
    });
    state.dialogVisible = false;
  }
  if (state.title == "任务指派人员") {
    if (state.tableAllSelectedId.length == 0) {
      proxy.$modal.msgWarning("请选择人员");
      return;
    }
    state.taskInfo.assignId = state.tableAllSelectedId.join(",");
    state.taskInfo.assignName = state.tableAllSelectedRow
      .map((item) => item.nickName)
      .join(",");
    state.dialogVisible = false;
  }
}

function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleBack(val) {
  proxy.$tab.closeOpenPage(!!val ? "project" : "orderTask");
}

function handleSingleChange(val, index) {
  if (val) {
    clearCheck();
    state.tableList_project[index].checked = true;
    state.curProject = JSON.parse(
      JSON.stringify(state.tableList_project[index])
    );
    console.log("check", val);
  } else {
    state.curProject = {};
  }
}

function clearCheck() {
  state.tableList_project = state.tableList_project.map((item) => {
    item.checked = false;
    return item;
  });
}

function rowSingleClick(row) {
  const idx = state.tableList_project.findIndex(
    (_) => _.projectId == row.projectId
  );
  if (row.checked) {
    state.tableList_project[idx].checked = false;
    state.curProject = {};
  } else {
    clearCheck();
    state.tableList_project[idx].checked = true;
    state.curProject = JSON.parse(JSON.stringify(row));
  }
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "userId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.userTableRef.setCurrentRow(null);
      state.userTableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.userId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.userTableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.userTableRef.setCurrentRow(row);
    state.userTableRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
      state.tableAllSelectedId.push(item.userId);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.userId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList_user;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.userId === a[0].userId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableListAll_user.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.userId) === -1) {
        state.tableAllSelectedId.push(item.userId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

function updateInfo(d) {
  Object.assign(state.taskInfo, d);
  console.log("处理后的详情", state.taskInfo);
}

defineExpose({
  handleSubmit,
  handleBack,
  updateInfo,
});
</script>
  
  <style lang="scss" scoped>
.taskInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
}
</style>