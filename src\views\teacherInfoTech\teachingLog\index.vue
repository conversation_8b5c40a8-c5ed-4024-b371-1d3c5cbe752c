<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
                <el-form-item label="" prop="deviceCode">
                    <el-input v-model="queryParams.deviceCode" placeholder="请输入设备编码" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="subject">
                    <el-input v-model="queryParams.subject" placeholder="请输入科目名称" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Download" @click="handleExport">导出日志</el-button>
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="logList" border>
                <el-table-column type="index" label="序号" width="90" align="center" fixed="left" />
                <el-table-column label="教学图片" align="center" minWidth="120px" prop="macAddr">
                    <template #default="scope">
                        <el-image style="width: 50px;height: 30px;display: block;margin: 0 auto;"
                            :src="scope.row.printscreen" fit="cover" @click="handlePreview(scope.row.printscreen)" />
                    </template>
                </el-table-column>
                <el-table-column label="教学科目" align="center" minWidth="120px" prop="subjects" />
                <el-table-column label="设备编码" align="center" minWidth="120px" prop="deviceCode" />
                <el-table-column label="安装位置" align="center" minWidth="120px" prop="installAddress" />
                <el-table-column label="截取时间" align="center" minWidth="120px" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                v-model:limit="queryParams.size" @pagination="getList" />
        </el-card>
        <el-dialog v-model="dialogVisible" title="预览" width="800px" append-to-body>
            <img :src="dialogImageUrl" style="display: block; max-width: 100%; margin: 0 auto" />
        </el-dialog>
    </div>
</template>

<script setup name="ledgerManage">
import { useRoute } from 'vue-router'
import { queDeviceLogPage, exportDevice } from '@/api/teachInfo/teacher';
import { getTenantList } from '@/api/park'
import { downloadBlob } from '@/utils'

const { proxy } = getCurrentInstance();
const route = useRoute()

const logList = ref([]);
const loading = ref(false);
const dialogVisible = ref(false);
const dialogImageUrl = ref('');
const showSearch = ref(true);
const tenantList = ref([]);
const total = ref(0);

const data = reactive({
    queryParams: {
        current: 1,
        size: 10,
        deviceCode: '',
        subject: ''
    },
});

const { queryParams } = toRefs(data);

/** 查询台账列表 */
function getList() {
    loading.value = true;
    queDeviceLogPage(queryParams.value).then(response => {
        logList.value = response.data.records
        total.value = response.data.total
        loading.value = false
    })
}
/** 查询学校列表 */
function getTenant() {
    getTenantList().then(response => {
        tenantList.value = response.data
    })
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.current = 1;
    getList();
}

/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 查看图片 */
const handlePreview = (url) => {
    dialogImageUrl.value = url
    dialogVisible.value = true
}

/** 导出按钮操作 */
function handleExport() {
    exportDevice().then(response => {
        downloadBlob(response, 'application/vnd.ms-excel', '教学日志')
        proxy.$modal.msgSuccess('操作成功');
    })
}

getTenant();
getList();

</script>
