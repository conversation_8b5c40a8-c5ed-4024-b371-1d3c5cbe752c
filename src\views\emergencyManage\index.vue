<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>应急预案</span>
        </div>
      </template>

      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item prop="codeAndName">
          <el-input
            v-model.trim="queryParams.codeAndName"
            placeholder="请输入预案编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @clear="resetQuery"
          />
        </el-form-item>
        <el-form-item prop="date">
          <el-date-picker
            v-model="queryParams.date"
            type="daterange"
            start-placeholder="创建开始日期"
            range-separator="-"
            end-placeholder="创建结束日期"
            clearable
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            style="width: 370px"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button icon="Search" type="primary" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-button
        class="mb12"
        plain
        icon="Plus"
        type="primary"
        @click="handleAdd"
        >新建预案</el-button
      >

      <el-table v-loading="loading" :data="tableData" border>
        <el-table-column
          prop="code"
          label="预案编号"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          show-overflow-tooltip
          min-width="120"
          prop="name"
          label="预案名称"
        />
        <el-table-column
          show-overflow-tooltip
          min-width="100"
          prop="type"
          label="预案类型"
        />
        <el-table-column
          show-overflow-tooltip
          min-width="150"
          prop="content"
          label="预案内容"
        />
        <el-table-column show-overflow-tooltip min-width="120" label="预案文件">
          <template #default="scope">
            <span>{{ scope.row.fileName || "无" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          show-overflow-tooltip
          min-width="150"
          prop="createTime"
          label="创建时间"
        />
        <el-table-column
          show-overflow-tooltip
          min-width="150"
          prop="updateTime"
          label="修改时间"
        />
        <!-- <el-table-column show-overflow-tooltip min-width="100" label="预案状态">
          <template #default="scope">
            <el-tag :type="statusList[scope.row.status].type">{{
              statusList[scope.row.status].label
            }}</el-tag>
          </template>
        </el-table-column> -->
        <el-table-column
          label="操作"
          min-width="150"
          fixed="right"
          align="center"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Search"
              @click="handleView(scope.row)"
              >查看</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="emergencyIndex">
import { ref, onMounted, reactive, toRefs, getCurrentInstance } from "vue";
import { useRouter } from "vue-router";
import { debounce } from "@/utils";
import { schoolPlanList, delSchoolPlan } from "@/api/emergency";

const { proxy } = getCurrentInstance();
const router = useRouter();
const state = reactive({
  loading: false,
  total: 0,
  tableData: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    date: null,
    codeAndName: "",
  },
  statusList: [
    { label: "待上传", value: 0, type: "warning" },
    { label: "已归档", value: 1, type: "success" },
  ],
});
const { statusList, total, tableData, queryParams, loading } = toRefs(state);

const getList = () => {
  let obj = { ...state.queryParams };
  if (!!obj.date) {
    obj.startTime = obj.date[0];
    obj.endTime = obj.date[1];
  }
  state.loading = true;
  schoolPlanList(obj)
    .then((response) => {
      if (response.data) {
        const { records, total } = response.data;
        state.tableData = records || [];
        state.total = total || 0;
      }
      state.loading = false;
      console.log("预案列表", response.data);
    })
    .catch(() => (state.loading = false));
};

// 搜索方法
const handleQuery = debounce(() => {
  state.queryParams.pageNum = 1;
  getList();
},300)

// 重置搜索
const resetQuery = debounce(() => {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  getList();
},300)

// 新建预案
const handleAdd = () => {
  router.push("/emergencyManage/add");
};

// 查看预案
const handleView = (row) => {
  router.push({
    path: "/emergencyManage/view",
    query: {
      id: row.id,
      mode: "view", // 添加模式参数，表示查看模式
    },
  });
};

// 删除预案
const handleDelete = (row) => {
  proxy.$modal.confirm(`确定删除编号为${row.code}的预案？`).then(() => {
    proxy.$modal.loading();
    delSchoolPlan({ ids: row.id })
      .then((res) => {
        proxy.$modal.msgSuccess("删除成功");
        proxy.$modal.closeLoading();
        getList()
      })
      .catch(() => proxy.$modal.closeLoading());
  });
};

// 初始化加载数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
</style>