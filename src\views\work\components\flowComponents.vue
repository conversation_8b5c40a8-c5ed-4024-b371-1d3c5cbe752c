<template>
    <div class="flow-header">
      <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
  
      <span class="title"
        >{{
          status == 1 ? "挂起" : status == 2 ? "回退" : status == 3 ? "扭转" : "设备报废"
        }}： 是否需要审批：</span
      >
      <el-switch
        v-model="needApproval"
        active-color="#13ce66"
        inactive-color="#ff4949"
        :active-value="1"
        :inactive-value="0"
        @change="handleChange"
      ></el-switch>
      <div class="flowchart">
        <svg width="1000" height="200">
          <!-- 增加高度以容纳更多内容 -->
          <!-- 节点 -->
          <rect x="10" y="50" width="150" height="40" fill="#fff" stroke="#000" />
          <text x="50" y="75" fill="#000" class="clickable">提交申请</text>
  
          <!-- <circle cx="260" cy="70" r="40" fill="#fff" stroke="#000" />
          <text
            x="220"
            y="75"
            fill="#000"
            class="clickable overflow"
            text-anchor="middle"
            dominant-baseline="middle"
          >
            {{ sliceTxt(itemData.nickName) ?? "请选择审批人" }}
          </text> -->
  
          <circle cx="260" cy="70" r="40" fill="#fff" stroke="#000" />
          <text
            x="260"
            y="70"
            fill="#000"
            class="clickable overflow"
            text-anchor="middle"
            dominant-baseline="middle"
          >
            {{ sliceTxt(itemData?.nickName) ?? "请选择审批人" }}
          </text>
  
          <polygon points="475,30 525,70 475,110 425,70" fill="#fff" stroke="#000" />
          <text x="445" y="75" fill="#000" class="clickable">审批通过</text>
  
          <rect x="600" y="50" width="150" height="40" fill="#fff" stroke="#000" />
          <text v-if="status == 1" x="645" y="75" fill="#000" class="clickable">单据挂起</text>
          <text v-else-if="status == 2" x="645" y="75" fill="#000" class="clickable">单据回退</text>
          <text v-else-if="status == 3" x="645" y="75" fill="#000" class="clickable">单据扭转</text>
          <text v-else x="645" y="75" fill="#000" class="clickable">单据挂起</text>
  
          <ellipse cx="900" cy="70" rx="30" ry="20" fill="#fff" stroke="#000" />
          <text x="885" y="75" fill="#000" class="clickable">结束</text>
  
          <!-- 直线箭头 -->
          <line x1="160" y1="70" x2="210" y2="70" stroke="#000" marker-end="url(#arrow)" />
          <line x1="300" y1="70" x2="420" y2="70" stroke="#000" marker-end="url(#arrow)" />
          <line x1="525" y1="70" x2="595" y2="70" stroke="#000" marker-end="url(#arrow)" />
          <line x1="750" y1="70" x2="865" y2="70" stroke="#000" marker-end="url(#arrow)" />
  
          <!-- 添加审批人图标 -->
          <g
            v-if="needApproval == 1"
            @click="addApprover"
            @mouseover="hover = true"
            @mouseleave="hover = false"
          >
            <text x="320" y="65" :fill="hover ? '#409eff' : '#000'" class="clickable">
              {{ itemData?.nickName ? "更换审批人" : "添加审批人" }}
            </text>
            <circle cx="310" cy="61" r="7" :fill="'#fff'" stroke="#000" />
            <text
              x="310"
              y="68"
              :fill="'#000'"
              font-size="20"
              text-anchor="middle"
              class="clickable"
            >
              +
            </text>
          </g>
  
          <!-- 曲线箭头 -->
          <path
            d="M 475 110 Q 630 150, 885 90"
            fill="none"
            stroke="#000"
            marker-end="url(#arrow)"
          />
  
          <!-- 箭头标记 -->
          <defs>
            <marker
              id="arrow"
              markerWidth="10"
              markerHeight="10"
              refX="5"
              refY="5"
              orient="auto"
            >
              <path d="M0,0 L10,5 L0,10 Z" fill="#000" />
            </marker>
          </defs>
        </svg>
      </div>
    </div>
    <el-dialog title="添加审批人" width="60%" v-model="isAdd" @open="dialogOpen">
      <div class="header">
        <el-input
          v-model="params.nickName"
          placeholder="请输入姓名搜索"
          style="width: 30%"
          @keyup.enter="handleQuery"
          clearable
          @clear="handleQuery"
        />
      </div>
      <div>
        <el-table
          :data="userList"
          style="width: 100%"
          empty-text="暂无用户数据"
          max-height="300"
          highlight-current-row
          v-loading="loading"
          @current-change="handleCurrentChange"
          @row-click="handleRowClick"
        >
          <!-- 序号列                 -->
          <el-table-column
            prop="nickName"
            label="姓名"
            min-width="200"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="corpName"
            label="机构"
            min-width="100"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="deptName"
            label="部门"
            min-width="100"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            prop="postName"
            label="岗位"
            min-width="100"
            show-overflow-tooltip
            align="center"
          />
          <el-table-column
            label="请选择"
            min-width="100"
            show-overflow-tooltip
            align="center"
          >
            <template #default="{ row, $index }">
              <el-checkbox v-model="row.checked" @change="(e) => checkChange(e, row)" />
            </template>
          </el-table-column>
        </el-table>
        <pagination
          :total="total"
          v-model:page="params.pageNum"
          v-model:limit="params.pageSize"
          @pagination="getUserList"
        />
      </div>
      <div class="footer">
        <el-button type="primary" @click="submitApprover" v-throttle>确定</el-button>
        <el-button @click="isAdd = false">返回</el-button>
      </div>
    </el-dialog>
  </template>
  
  <script setup>
  import { ref } from "vue";
  import { listUser } from "@/api/system/user";
  import { getRoleList } from "@/api/distribution/member";
  import { debounce } from "@/utils/debounce";
  import { updateStatus, addApprovalPeople } from "@/api/approval";
  
  // <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
  const props = defineProps({
    status: {
      type: String,
      default: "",
    },
    objData: {
      type: Object,
      default: {},
    },
  });
  
  const $emit = defineEmits(["update:needApproval", "update:approver", "getApprovalData"]);
  
  const { proxy } = getCurrentInstance();
  const loading = ref(false);
  const needApproval = ref(0); // 是否需要审批
  const approver = ref({}); // 审批人
  const hover = ref(false); // 用于控制悬停状态
  const isAdd = ref(false); // 是否添加审批人弹窗
  const userList = ref([]); // 用户列表
  const total = ref(0);
  const search = ref(""); // 搜索框
  const currentRow = ref(null); // 当前行
  const itemData = ref({});
  
  const params = ref({
    pageNum: 1,
    pageSize: 10,
    nickName: "",
    //admin           超级管理员
    //common          普通角色
    //cityCabin       驾驶舱（市级）
    //cityManage      市级管理员
    //districtCabin   驾驶舱（区级）
    //districtManage  区级管理员
    //schoolCabin     驾驶舱（校级）
    //schoolManage    校级管理员
    //maintain        运维人员
    //maintainManage  运维管理员
    //teacherStaff    教职工
    //inspection巡检人员
    roleKey: ["maintain", "maintainManage", "schoolManage"],
  });
  
  watch(
    () => props.objData,
    (val) => {
      console.log(val, "val");
  
      // if (props.objData) {
      needApproval.value = val.status;
      if (val?.approvalPeopleList != null && val?.approvalPeopleList.length > 0) {
        itemData.value = val?.approvalPeopleList[0] || {
          nickName: "",
        };
      }
  
      //   approver.value = props.objData.approver;
      // }
    }
  );
  
  // 切换状态
  const handleChange = (val) => {
    console.log(
      {
        //操作 1:挂起 2:回退 3:扭转 4:设备报废
        operationApproval: props.status,
        //0 不需要审批 1 需要审批
        status: val,
      },
      "val"
    );
    // proxy.$modal.loading();
    updateStatus({
      //操作 1:挂起 2:回退 3:扭转 4:设备报废
      operationApproval: props.status,
      //0 不需要审批 1 需要审批
      status: val,
    })
      .then((res) => {
        // proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("操作成功");
        $emit("getApprovalData");
        console.log(res);
      })
      .catch(() => {
        // proxy.$modal.closeLoading();
      });
  };
  
  // 截取文字
  const sliceTxt = (txt) => {
    if (!txt) return "请选择审批人";
    if (txt.length > 6) {
      return txt.slice(0, 5) + "...";
    }
    return txt;
  };
  
  // 弹窗打开
  const dialogOpen = () => {
    console.log("打开");
    getUserList();
  };
  
  // 修改行点击事件处理
  const handleRowClick = (row) => {
    // 清除其他行的选中状态
    userList.value.forEach((item) => {
      item.checked = false;
    });
    // 设置当前行为选中状态
    row.checked = true;
    // 更新当前选中行
    currentRow.value = row;
  };
  
  // 修改复选框变更事件处理
  const checkChange = (e, row) => {
    userList.value.forEach((item) => {
      item.checked = false;
    });
    if (e) {
      row.checked = true;
      currentRow.value = row;
    } else {
      currentRow.value = null;
    }
  };
  
  // 单选方法
  const handleCurrentChange = (row) => {
    currentRow.value = row;
  };
  
  // 点击确定添加审批人
  const submitApprover = () => {
    let item = userList.value.find((item) => item.checked);
    console.log(item);
  
    if (!item) {
      proxy.$modal.msgWarning("请选择审批人");
      return;
    }
    // <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
    itemData.value = item;
    isAdd.value = false;
    let obj = {
      userId: item.userId,
      approvalConfigId: props.objData.approvalConfigId,
      sort: 1,
    };
    console.log(obj);
  
    addApprovalPeople(obj)
      .then((res) => {
        console.log(res);
        $emit("getApprovalData");
  
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => {});
  
    // approver.value = currentRow.value;
    // $emit("update:approver", approver.value);
    // isAdd.value = false;
  };
  
  const addApprover = () => {
    isAdd.value = true;
    //   userList.value = [
    //     { nickName: "张三", orgName: "公司1", sysDept: { name: "部门1" }, postName: "职位1" },
    //     { nickName: "王五", orgName: "公司3", sysDept: { name: "部门3" }, postName: "职位3" },
    //     { nickName: "赵六", orgName: "公司4", sysDept: { name: "部门4" }, postName: "职位4" },
    //     { nickName: "田七", orgName: "公司5", sysDept: { name: "部门5" }, postName: "职位5" },
    //     { nickName: "周八", orgName: "公司6", sysDept: { name: "部门6" }, postName: "职位6" },
    //   ];
  };
  
  const handleQuery = debounce((e) => {
    e?.target?.blur();
    params.value.pageNum = 1;
    getUserList();
  }, 200);
  
  // 获取用户列表
  const getUserList = async () => {
    loading.value = true;
    try {
      const res = await getRoleList(params.value);
      console.log(res, "列表");
      userList.value = res.data.records;
      userList.value.forEach((item) => {
        item.checked = false;
      });
      if (itemData.value.userId) {
        let item = userList.value.find((item) => item.userId == itemData.value.userId);
        if (item) {
          item.checked = true;
        }
      }
      total.value = res.data.total;
      loading.value = false;
    } catch (err) {
      loading.value = false;
    }
  };
  // getUserList();
  </script>
  
  <style scoped>
  .title {
    font-size: 18px;
    font-weight: bold;
    margin-right: 10px;
  }
  .flowchart {
    margin: 20px;
    font-size: small;
    border: 1px solid #ccc;
  }
  
  .clickable {
    cursor: pointer;
    text-align: center;
    display: flex;
    justify-content: center;
  }
  
  .footer {
    display: flex;
    justify-content: end;
    margin-top: 50px;
  }
  
  .overflow {
    background-color: red;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  </style>
  