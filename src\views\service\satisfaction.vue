<template>
  <div class="satisfaction app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <el-button @click="handleBack"
            >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
          >
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="workOrderCode">
          <el-input
            v-model="queryParams.workOrderCode"
            placeholder="请输入单据id"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="phone">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入报障电话"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="satisfaction-main">
        <div class="satisfaction-main_table">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="单据id"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障内容"
              align="center"
              minWidth="200px"
              prop="remark"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障电话"
              align="center"
              minWidth="120px"
              prop="repairPhone"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障人"
              align="center"
              minWidth="120px"
              prop="repairName"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障时间"
              align="center"
              minWidth="160px"
              prop="submittedTime"
              show-overflow-tooltip
            />
            <el-table-column label="报障状态" align="center" minWidth="100px">
              <template #default="scope">
                <el-tag
                  effect="dark"
                  :type="statusObj[scope.row.troubleStatus].type"
                  >{{ statusObj[scope.row.troubleStatus].name }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="电话录音"
              align="center"
              minWidth="160px"
              prop="file"
            >
              <template #default="scope">
                {{ scope.row.file || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="满意度评价"
              align="center"
              minWidth="100px"
              prop="scores"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.scores || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="报障来源"
              align="center"
              minWidth="100px"
              prop="channel"
              show-overflow-tooltip
            />
            <el-table-column
              label="操作"
              min-width="200"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  v-if="!isNewUser"
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleCheck(scope.row)"
                  >查看</el-button
                >
                <el-button
                  link
                  v-if="scope.row.troubleStatus == 2"
                  type="warning"
                  icon="Edit"
                  @click.stop="handleComment(scope.row)"
                  >满意度评价</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>

      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        title="满意度评价"
        width="400"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        align-center
        @close="handleCancel"
      >
        <el-form :model="form" ref="formRef" :rules="rules">
          <el-form-item
            label="问题得到解决了吗？"
            prop="resolved"
            label-position="top"
            required
          >
            <el-radio-group v-model="form.resolved">
              <el-radio value="已解决">已解决</el-radio>
              <el-radio value="未解决">未解决</el-radio>
            </el-radio-group>
          </el-form-item>
          <div style="color: #606266; font-weight: bold; margin-bottom: 20px">
            请对本次服务做出评价：
          </div>
          <el-form-item
            label="处理速度"
            prop="processingSpeed"
            :rules="[
              {
                required: true,
                type: 'number',
                min: 1,
                message: '请点评',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-rate
              v-model="form.processingSpeed"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
            />
          </el-form-item>
          <el-form-item
            label="服务态度"
            prop="serviceAttitude"
            :rules="[
              {
                required: true,
                type: 'number',
                min: 1,
                message: '请点评',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-rate
              v-model="form.serviceAttitude"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
            />
          </el-form-item>
          <el-form-item
            label="技术能力"
            prop="technicalAbility"
            :rules="[
              {
                required: true,
                type: 'number',
                min: 1,
                message: '请点评',
                trigger: ['blur', 'change'],
              },
            ]"
          >
            <el-rate
              v-model="form.technicalAbility"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
            />
          </el-form-item>
          <el-form-item label="意见反馈" prop="feedback" label-position="top">
            <el-input
              type="textarea"
              v-model="form.feedback"
              placeholder="请输入"
              maxlength="200"
            />
          </el-form-item>
          <el-form-item
            label="是否接受回访？"
            prop="access"
            label-position="top"
            required
          >
            <el-radio-group v-model="form.access">
              <el-radio :value="1">接受</el-radio>
              <el-radio :value="0">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button type="primary" @click.stop="handleSubmit" v-throttle>确定</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="satisfaction">
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import { deviceMaintenanceList } from "@/api/mediaTeach/trouble";
import { addEvaluation } from "@/api/service";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();

const route = useRoute();
const router = useRouter();

const { proxy } = getCurrentInstance();
const formRef = ref(null);
const state = reactive({
  isNewUser: !userStore.roleId,
  form: {
    eventId: "",
    resolved: "已解决",
    processingSpeed: 0,
    serviceAttitude: 0,
    technicalAbility: 0,
    feedback: "",
    access: 1,
  },
  curIdx: 0,
  dialogVisible: false,
  total: 0,
  loading: false,
  tableList_all: [],
  tableList: [],
  typeList: [],
  statusObj: {
    2: {
      type: "warning",
      name: "待评价",
    },
    3: {
      type: "success",
      name: "已完成",
    },
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    type: 1,
    troubleStatusList: [2, 3],
    workOrderCode: "",
    phone: "",
  },
  rules: {
    access: [
      {
        required: true,
      },
    ],
  },
});

const {
  isNewUser,
  rules,
  form,
  curIdx,
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  statusObj,
  loading,
  total,
} = toRefs(state);

function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

function handleComment(item) {
  state.form.eventId = item.troubleId;
  dialogVisible.value = true;
}

function handleBack() {
  proxy.$tab.closeOpenPage("/service");
}

function handleCancel() {
  formRef.value.resetFields();
  state.dialogVisible = false;
}

function handleCheck(item) {
  router.push({
    path: `/taskManage/taskCenter/handleTaskInfo`,
    query: {
      id: item.troubleId,
    },
  });
}

function handleSubmit() {
  formRef.value.validate((valid) => {
    if (valid) {
      console.log("提交评价参数", form.value);
      addEvaluation(form.value).then((res) => {
        proxy.$modal.msgSuccess("操作成功");
        handleCancel();
        getList();
      });
    }
  });
}

function getList() {
  state.loading = true;
  console.log("查询参数", state.queryParams);
  deviceMaintenanceList(state.queryParams)
    .then((resp) => {
      console.log("满意度列表", resp);
      if (resp.data) {
        state.tableList = resp.data.maintenanceWebListVOList || [];
        state.total = resp.data.total || 0;
      } else {
        state.tableList = [];
      }
      state.loading = false;
    })
    .catch(() => (state.loading = false));
}

getList();
</script>
