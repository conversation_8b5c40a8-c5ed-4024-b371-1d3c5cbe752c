<template>
  <div class="task app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">任务单</div>
      </template>
      <div class="task-main">
        <div class="task-main_search search-list">
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="queryNoOrName">
              <el-input
                v-model="queryParams.queryNoOrName"
                placeholder="请输入任务单编号/名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="projectId">
              <el-select
                v-model="queryParams.projectId"
                placeholder="请选择任务归属项目"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in projectList"
                  :key="item.projectId"
                  :label="item.projectName"
                  :value="item.projectId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="deptId">
              <el-tree-select
                v-model="queryParams.deptId"
                :props="deptProps"
                :data="deptList"
                placeholder="请选择项目归属部门"
                check-strictly
                :render-after-expand="false"
                :expand-on-click-node="false"
                style="width: 200px"
                clearable
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="assignName">
              <el-input
                v-model="queryParams.assignName"
                placeholder="请输入任务指派人员姓名"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-button
          class="mb12"
          plain
          type="primary"
          icon="Plus"
          @click="handleAdd"
          >新建任务单</el-button
        >
        <div class="task-main_table">
          <el-table ref="tableRef" v-loading="loading" :data="tableList" border>
            <el-table-column
              label="任务单编号"
              align="center"
              minWidth="120px"
              prop="taskNo"
              show-overflow-tooltip
            />
            <el-table-column
              label="任务单名称"
              align="center"
              minWidth="150px"
              prop="taskName"
              show-overflow-tooltip
            />
            <!-- <el-table-column
              label="任务描述"
              align="center"
              minWidth="180px"
              prop="taskContent"
              show-overflow-tooltip
            /> -->
            <el-table-column
              label="归属项目"
              align="center"
              minWidth="150px"
              prop="projectName"
              show-overflow-tooltip
            />
            <el-table-column
              label="归属部门"
              align="center"
              minWidth="120px"
              prop="deptName"
              show-overflow-tooltip
            />
            <el-table-column
              label="任务指派人员"
              align="center"
              minWidth="150px"
              prop="assignName"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.assignName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              minWidth="160px"
              prop="createdTime"
              show-overflow-tooltip
            />
            <el-table-column
              label="处理时间"
              align="center"
              minWidth="160px"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.handleTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column label="状态" align="center" minWidth="100px">
              <template #default="{ row }">
                <el-tag
                  effect="dark"
                  :type="row.handleStatus == 0 ? 'danger' : 'success'"
                  >{{ row.handleStatus == 0 ? "未处理" : "已处理" }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="220"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleCheck(scope.row, 0)"
                  >查看</el-button
                >
                <el-button
                  v-if="scope.row.handleStatus == 0"
                  link
                  type="warning"
                  icon="Edit"
                  @click.stop="handleCheck(scope.row, 1)"
                  >处理</el-button
                >
                <el-button
                  v-if="scope.row.handleStatus == 0"
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="task">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import { useRouter } from "vue-router";
import {
  getSchoolProjectPage,
  getProjectTaskList,
  delProjectTask,
} from "@/api/order";
import { listTree } from "@/api/system/distribution";

const { proxy } = getCurrentInstance();
const router = useRouter();
const taskRef = ref(null);
const userStore = useUserStore(); // 获取当前登录的用户信息
console.log(userStore);
const state = reactive({
  total: 0,
  loading: false,
  tableList: [],
  deptList: [],
  projectList: [],
  // 列表条件筛选字段
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    queryNoOrName: "",
    projectId: "",
    deptId: "",
    assignName: "",
  },
  deptProps: {
    label: "deptName",
    children: "children",
    value: "deptId",
  },
});

const {
  total,
  deptList,
  deptProps,
  recordList,
  tableList,
  projectList,
  queryParams,
  loading,
} = toRefs(state);

function handleAdd() {
  router.push("addProjectTask");
}

/** 列表的详情按钮 */
function handleCheck(item, type) {
  router.push({
    path: "taskInfo",
    query: { id: item.taskId, type },
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 获取部门列表 */
function getDeptList() {
  listTree().then((resp) => {
    console.log("部门树", resp);
    if (resp.data) {
      state.deptList = resp.data;
    }
  });
}

function getProjectList() {
  getSchoolProjectPage({ pageNum: 1, pageSize: 999999 }).then((resp) => {
    console.log("项目列表", resp);
    if (resp.data) {
      const { rows = [] } = resp.data;
      state.projectList = rows;
    }
  });
}

function handleDel(item) {
  proxy.$modal
    .confirm(`确定删除编号为${item.taskNo}的任务单信息？`)
    .then(() => {
      delProjectTask({ taskId: item.taskId }).then((resp) => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      });
    });
}

function getList() {
  state.loading = true;
  getProjectTaskList(state.queryParams)
    .then((resp) => {
      console.log("任务单列表", resp);
      if (resp.data) {
        const { rows = [], total = 0 } = resp.data;
        state.tableList = rows;
        state.total = total;
        state.loading = false;
      }
    })
    .catch(() => (state.loading = false));
}

onMounted(() => {
  getDeptList();
  getProjectList();
  getList();
});
</script>

<style lang="scss" scoped>
.task {
}
</style>