<template>
  <div class="unify">
    <div class="unify-main">
      <div class="unify-main_header">
        <div class="unify-main_header-title">数据中心</div>
        <div class="unify-main_header-btns">
          <div
            class="unify-main_header-btns_btn"
            :class="[
              curBtn == index ? 'active' : '',
              index > 1 ? 'right-btn' : 'left-btn',
            ]"
            v-for="(item, index) in headerBtns"
            :key="index"
            @click="curBtn = index"
          >
            {{ item }}
          </div>
        </div>
        <div class="unify-main_header-date">
          <div class="left-date">
            {{ proxy.parseTime(curTime, "{h}:{i}:{s}") }}
          </div>
          <div class="right-date">
            {{ proxy.parseTime(curTime, "{y}.{m}.{d}") }}
          </div>
        </div>
        <div v-if="weatherIcon" class="unify-main_header-weather">
          <img :src="weatherIcon" alt="">
        </div>
      </div>
      <div class="unify-main_chart">
        <page1 v-if="curBtn == 0" />
        <page2 v-if="curBtn == 1" />
        <page3 v-if="curBtn == 2" />
      </div>
    </div>
  </div>
</template>
  
  <script setup name="unifyIndex">
import page1 from "./component/admin/page1-2.vue";
import page2 from "./component/admin/page2.vue";
import page3 from "./component/admin/page3.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  headerBtns: ["资产数据大屏", "运维数据大屏", "数据智能分析"],
  curBtn: 0,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
});
const { headerBtns, curBtn, timer, timer2, curTime } = toRefs(state);

const weatherIcon = ref("");
// 获取天气
const getWeather = async () => {
  const url = "https://restapi.amap.com/v3/weather/weatherInfo?city=440100&key=f9b3588a8a4599df337256b96611e824";
  const res = await fetch(url);
  const data = await res.json();
  if (data.lives) {
    if (data.lives[0].weather.includes("雨")) {
      weatherIcon.value = "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/rain.png";
    } else if (data.lives[0].weather.includes("云") || data.lives[0].weather.includes("阴")) {
      weatherIcon.value = "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/cloud.png";
    } else {
      weatherIcon.value = "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/sun.png";
    }
  } else {
    weatherIcon.value = "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/sun.png";
  }
}

onMounted(() => {
  getWeather();
  document.addEventListener("selectstart", function (e) {
    e.preventDefault();
  });
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  // state.timer2 = setInterval(() => {}, 60000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
  document.removeEventListener("selectstart", function (e) {
    e.preventDefault();
  });
});
</script>
  
  <style lang="scss" scoped>
.unify {
  width: 100%;
  height: 100%;
  background-color: #020d20;
  // border: 1px solid red;
  color: #b0c0e6;
  overflow-y: hidden;

  &-main {
    width: 100vw;
    height: 56vw;
    // border: 1px solid red;
    // background: url("@/assets/screen/bg.png");
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/bg.jpg");
    background-size: 100% 100%;

    &_header {
      position: relative;
      width: 100%;
      height: 4.2vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/header_bg.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 0.5vw;

      &-title {
        font-size: 2.4vw;
        letter-spacing: 0.4vw;
        color: #fff;
      }

      &-btns {
        position: absolute;
        display: flex;
        width: 100%;
        // border: 1px solid red;
        bottom: -0.2vw;
        font-size: 0.85vw;

        &_btn {
          // border: 1px solid red;
          color: #fff;
          cursor: pointer;
          text-align: center;
          width: 8.5vw;
          height: 2.2vw;
          line-height: 2.2vw;
          position: relative;
          overflow: hidden;

          &.left-btn {
            left: 21.2vw;
            padding-left: 0.4vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_left.png");
            background-size: 100% 100%;
            &.active {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_leftSel.png");
              background-size: 100% 100%;
            }
          }

          &.right-btn {
            left: 44.7vw;
            padding-right: 0.4vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_right.png");
            background-size: 100% 100%;
            &.active {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_rightSel.png");
              background-size: 100% 100%;
            }
          }

          &:hover {
            // transform: translateY(-2px);
          }
        }
      }

      &-date {
        position: absolute;
        left: 1vw;
        bottom: 0;
        text-align: right;
        .left-date {
          font-size: 1.7vw;
        }
        .right-date {
          font-size: 0.85vw;
        }
      }

      &-weather {
        position: absolute;
        left: 10vw;
        top: 1.5vw;
        width: 5.5vw;
        height: 2.5vw;
        text-align: right;
        border-left: 0.2vw solid #9DC5E859;
        img {
          width: 2.5vw;
          height: 2.5vw;
        }
      }
    }

    &_chart {
      padding: 0 0.5vw;
      // 隐藏滚动条
      -ms-overflow-style: none;  /* IE和Edge */
      scrollbar-width: none;  /* Firefox */
      ::-webkit-scrollbar {
        display: none;
      }
    }
  }
}
</style>