<template>
  <div class="change-setting">
    <div class="left">
      是否开启变更记录
      <el-switch v-model="isRecord" :active-value="0" :inactive-value="1" @change="(e) => itemSwitch(e, 'item')"
        @click="switchClick"></el-switch>
      <!-- @change="(e) => itemSwitch(e, 'item')" -->
      <br />
      <div v-if="isRecord == 0" class="record-module">请选择变更记录开启模块 ：</div>
    </div>
    <div class="right" v-if="isRecord == 0">
      <div class="module-list">
        <div class="module-item" v-for="(item, index) in moduleList" :key="index">

          {{

            item.operationType == 1 ? '设备台账管理模块变更记录' : item.operationType == 2 ? '备件管理模块变更记录' : item.operationType == 3 ?
              "工单中心模块变更记录" : item.operationType == 4 ? "文档管理模块变更记录" : item.operationType == 5 ? "应急管理模块变更记录" : '项目管理模块变更记录'
          }}
          <el-switch v-model="item.status" :active-value="0" :inactive-value="1"
            @change="(e) => itemSwitch(e, item)"></el-switch>
        </div>
      </div>
    </div>
  </div>
</template>
<script setup>
import { getSchoolLogStatusList, updateSchoolLogStatus } from '@/api/approval'
import { nextTick } from 'process';
import { onMounted } from 'vue';


const { proxy } = getCurrentInstance();
const isRecord = ref('1');
const moduleList = ref([
  // { name: "设备台账管理模块变更记录", isSelect: false, operationType: 1 },
  // { name: "工单中心模块变更记录", isSelect: false, operationType: 3 },
  // { name: "备件管理模块变更记录", isSelect: false, operationType: 2 },
  // { name: "应急管理模块变更记录", isSelect: false, operationType: 5 },
]);
const isFirst = ref(0)

// watch(() => isRecord.value, (e) => {
//   console.log(e, 'ee')
//   // getStatuListData()
// })

const switchClick = () => {
  isFirst.value += 1

}

onMounted(() => { isFirst.value += 1 })

const itemSwitch = (e, item) => {
  console.log(isRecord.value, e, 11);
  console.log(moduleList.value);
  console.log(isFirst.value);

  if (isFirst.value == 0) return

  if (item == 'item') {
    let obj = {
      id: moduleList.value[0].id,
      //类型 1.设备管理 2.备件管理 3.工单管理 4:文档管理 5:应急管理
      // operationType: 1,
      /** 0 启动 1未启动 */
      // statu: 0,
      modelStatus: e,
    }
    console.log(obj);

    chagneData(obj)
  } else {
    let obj = {
      id: item.id,
      //类型 1.设备管理 2.备件管理 3.工单管理 4:文档管理 5:应急管理
      // operationType: 1,
      /** 0 启动 1未启动 */
      status: e,
      // modelStatus: 0,
    }
    console.log(obj);
    chagneData(obj)
  }
}

function chagneData(data) {
  proxy.$modal.loading()
  updateSchoolLogStatus(data).then(res => {
    proxy.$modal.closeLoading()
    proxy.$modal.msgSuccess('操作成功')
    console.log(res);

  }).catch(() => {
    proxy.$modal.closeLoading()
  })
}

function getStatuListData() {
  proxy.$modal.loading()
  getSchoolLogStatusList().then(res => {
    proxy.$modal.closeLoading()
    console.log(res);
    moduleList.value = res.data.logStatusList
    isRecord.value = res.data.status
    // moduleList.value = res.data.logStatusList.filter((item) => item.operationType != 4)

  }).catch(() => {
    proxy.$modal.closeLoading()
  })
}
getStatuListData()


</script>
<style lang="scss" scoped>
.change-setting {
  align-items: center;
  margin: 2%;

  .left {
    width: 40%;

    flex-direction: column;
    align-items: center;
    justify-content: center;

    .record-module {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }
  }

  .right {
    width: 60%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    .module-list {
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      margin-top: 20px;

      .module-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-top: 10px;
        font-size: 14px;
        color: #666;

        .el-switch {
          margin-left: 10px;
        }
      }
    }
  }
}
</style>
