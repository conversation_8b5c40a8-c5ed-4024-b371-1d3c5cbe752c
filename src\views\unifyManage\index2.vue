<template>
    <div class="unify">
      <div class="unify-main">
        <div class="unify-main_header">
          <div class="unify-main_header-title">数字资产可视化数据大屏</div>
          <div class="unify-main_header-btns">
            <div
              class="unify-main_header-btns_btn"
              :class="curBtn == index ? 'active' : ''"
              v-for="(item, index) in headerBtns"
              :key="index"
              @click="handleBtnClick(index)"
            >
              {{ item.name }}
            </div>
          </div>
          <div class="unify-main_header-date">
            {{ proxy.parseTime(curTime, "{y}年{m}月{d}日 {h}:{i}") }}
          </div>
        </div>
        <div class="unify-main_charts">
          <div class="col col1">
            <div class="item col1-top">
              <div class="item-title">{{ titles.title1 }}</div>
              <div class="echart">
                <div class="echart-item one">
                  <el-progress
                    color="#1a57fa"
                    :stroke-width="12"
                    type="circle"
                    :percentage="37"
                  />
                </div>
                <div class="echart-item two">
                  <Echarts
                    id="barData"
                    width="100%"
                    height="100%"
                    :fullOptions="barOption"
                  />
                </div>
              </div>
            </div>
            <div class="item col1-bottom">
              <div class="item-title">{{ titles.title2 }}</div>
              <div class="item-main flex1">
                <div
                  class="item-opt"
                  v-for="(item, index) in deviceStatusList"
                  :key="index"
                >
                  {{ item.value }}{{ item.unit }}
                  <div class="opt-title">{{ item.title }}</div>
                </div>
              </div>
              <div class="table">
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">设备类型</div>
                  <div class="table-row_count table-row_head">设备数量</div>
                  <div class="table-row_percent table-row_head">占比</div>
                </div>
                <div
                  class="table-row data"
                  v-for="item in tableData.slice(0, 8)"
                  :key="item.numer"
                >
                  <div class="table-row_number">{{ item.number }}</div>
                  <div class="table-row_type">{{ item.type }}</div>
                  <div class="table-row_count">{{ item.count }}</div>
                  <div class="table-row_percent" :class="item.up ? 'up' : 'low'">
                    {{ item.percent }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col col2">
            <div class="col2-top">
              <div
                class="item-opt"
                v-for="(item, index) in deviceCountList"
                :key="index"
              >
                <span>{{ item.value }}</span
                >{{ item.unit }}
                <div>{{ item.title }}</div>
              </div>
            </div>
            <div class="col2-bottom">
              <div class="col2-bottom_bg">
                <div
                  class="item-opt"
                  :class="[
                    `opt${index + 1}`,
                    curTab == index ? 'active' : '',
                    index % 2 == 0 ? 'left' : 'right',
                  ]"
                  v-for="(item, index) in optList5"
                  :key="index"
                  @click="curTab = index"
                >
                  <div>{{ item.name }}</div>
                  <span>{{ item.number }}</span>
                </div>
              </div>
            </div>
          </div>
          <div class="col col3">
            <div class="item col3-top">
              <div class="item-title">{{ titles.title3 }}</div>
              <div class="item-main">
                <div
                  class="item-opt"
                  v-for="(item, index) in optList3"
                  :key="index"
                >
                  {{ item.title }}
                  <div class="item-opt_row">
                    <span>{{ item.money }}</span
                    >万元
                    <div :class="item.up > 0 ? 'up' : 'low'">
                      {{ Math.abs(item.up) }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="item col3-center">
              <div class="item-title">{{ titles.title4 }}</div>
              <div class="echart pie">
                <Echarts
                  id="pieData"
                  width="100%"
                  height="100%"
                  :fullOptions="pieOption"
                />
              </div>
            </div>
            <div class="item col3-bottom">
              <div class="item-title">{{ titles.title5 }}</div>
              <div class="echart line">
                <Echarts
                  id="lineData"
                  width="100%"
                  height="100%"
                  :fullOptions="lineOption"
                />
              </div>
            </div>
          </div>
          <div class="col col4">
            <div class="item col4-top">
              <div class="item-title">{{ titles.title6 }}</div>
              <div class="item-progress">
                <div
                  class="item-opt"
                  v-for="(item, index) in maintenanceList"
                  :key="index"
                >
                  <el-progress
                    :color="item.color"
                    :show-text="false"
                    :stroke-width="12"
                    type="circle"
                    :percentage="item.percentage"
                  />
                  <div class="item-progress_right">
                    {{ item.percentage }}%
                    <div class="opt-title">{{ item.title }}</div>
                  </div>
                </div>
              </div>
              <div class="item-line">
                <div
                  class="item-opt"
                  v-for="(item, index) in optList4"
                  :key="index"
                >
                  {{ item.name }}
                  <div class="item-opt_center">
                    <div class="dot" :style="{ left: `${item.percent}%` }"></div>
                  </div>
                  {{ `${item.percent}%` }}
                </div>
              </div>
            </div>
            <div class="item col4-bottom">
              <div class="item-title">{{ titles.title7 }}</div>
              <div class="item-row">
                设备资源使用率
                <div class="item-row_right">
                  365.64<span class="opt-title">台</span>
                  <div>0.7</div>
                </div>
              </div>
              <div class="table">
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">设备类型</div>
                  <div class="table-row_count table-row_head">设备数量</div>
                  <div class="table-row_percent table-row_head">使用率</div>
                </div>
                <div
                  class="table-row data"
                  v-for="item in tableData"
                  :key="item.numer"
                >
                  <div class="table-row_number">{{ item.number }}</div>
                  <div class="table-row_type">{{ item.type }}</div>
                  <div class="table-row_count">{{ item.count }}</div>
                  <div class="table-row_percent" :class="item.up ? 'up' : 'low'">
                    {{ item.percent }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup name="unifyIndex">
  import Echarts from "@/components/Echarts/index.vue";
  import {
    reactive,
    toRefs,
    getCurrentInstance,
    onMounted,
    onBeforeUnmount,
    ref,
  } from "vue";
  
  const { proxy } = getCurrentInstance();
  
  const titles = ref({
    title1: "设备类型分布",
    title2: "设备运行状态",
    title3: "部门设备概况",
    title4: "设备性能分析",
    title5: "设备告警分布",
    title6: "设备维护情况",
    title7: "设备资源使用",
  });
  
  const tableData = ref([
    { number: "01", type: "服务器", count: 386, percent: "28.4%", up: true },
    { number: "02", type: "网络设备", count: 275, percent: "20.2%", up: true },
    { number: "03", type: "安全设备", count: 198, percent: "14.6%", up: false },
    { number: "04", type: "存储设备", count: 167, percent: "12.3%", up: true },
    { number: "05", type: "监控设备", count: 142, percent: "10.5%", up: false },
    { number: "06", type: "终端设备", count: 108, percent: "7.9%", up: true },
    { number: "07", type: "备份设备", count: 52, percent: "3.8%", up: false },
    { number: "08", type: "其他设备", count: 31, percent: "2.3%", up: true },
  ]);
  
  const optList3 = ref([
    { title: "设备总数量", money: 1359, up: 0.8 },
    { title: "在线设备", money: 1286, up: 0.5 },
    { title: "故障设备", money: 73, up: -0.4 },
    { title: "设备利用率", money: 94.6, up: 0.3 },
  ]);
  
  const optList4 = ref([
    { name: "设备在线率", percent: 92 },
    { name: "设备完好率", percent: 89 },
    { name: "维护及时率", percent: 95 },
    { name: "故障处理率", percent: 97 },
    { name: "备份覆盖率", percent: 88 },
  ]);
  
  const optList5 = ref([
    { name: "从化区", number: "01" },
    { name: "增城区", number: "02" },
    { name: "花都区", number: "03" },
    // { name: "生产部", number: "04" },
    // { name: "数据部", number: "05" },
    // { name: "安全部", number: "06" },
  ]);
  
  const barXData = ["服务器", "网络设备", "安全设备", "存储设备", "监控设备"];
  const barSData = [386, 275, 198, 167, 142];
  
  const barOption = reactive({
    options: {
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      grid: {
        bottom: "15%",
        left: "15%",
        height: "80%",
        right: "3%",
      },
      xAxis: {
        type: "category",
        data: barXData,
        axisLabel: {
          color: "#8495b6",
          fontSize: "50%",
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          color: "#8495b6",
          fontSize: "50%",
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#8495b6",
            opacity: 0.2,
          },
        },
        axisLine: {
          show: false,
        },
      },
      series: [
        {
          data: barSData,
          type: "bar",
          barWidth: "30%",
          showBackground: false,
          backgroundStyle: {
            color: "rgb(4, 22, 50, 1)",
            borderColor: "#c8d7fa",
          },
          itemStyle: {
            color: "rgba(81, 131, 255, .6)",
          },
        },
      ],
    },
  });
  
  const pieOption = reactive({
    options: {
      color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
      tooltip: {
        trigger: "item",
      },
      series: [
        {
          type: "pie",
          radius: ["50%", "65%"],
          center: ["50%", "50%"],
          avoidLabelOverlap: false,
          label: {
            formatter: "{b|{b}}\n{d|{d}%}",
            rich: {
              d: {
                color: "#c8d7fa",
                fontSize: "90%",
                lineHeight: "25",
              },
              b: {
                color: "#6889de",
                fontSize: "70%",
              },
            },
          },
          labelLine: {
            show: true,
            length: 5,
            length2: 15,
            lineStyle: {
              color: "rgba(81, 131, 255, .2)",
            },
          },
          data: [
            { value: 386, name: "服务器" },
            { value: 275, name: "网络设备" },
            { value: 198, name: "安全设备" },
            { value: 167, name: "存储设备" },
          ],
        },
      ],
    },
  });
  
  const lineOption = reactive({
    options: {
      color: ["#092258"],
      tooltip: {
        trigger: "axis",
        axisPointer: {
          type: "shadow",
        },
      },
      xAxis: {
        type: "category",
        boundaryGap: false,
        data: ["1月", "2月", "3月", "4月", "5月", "6月", "7月"],
        axisLabel: {
          color: "#8495b6",
          fontSize: "50%",
        },
        axisTick: {
          show: false,
        },
        axisLine: {
          show: false,
        },
      },
      yAxis: {
        type: "value",
        axisLabel: {
          color: "#8495b6",
          fontSize: "50%",
        },
        axisTick: {
          show: false,
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: "#8495b6",
            opacity: 0.2,
          },
        },
        axisLine: {
          show: false,
        },
      },
      grid: {
        top: "15%",
        left: "10%",
        height: "60%",
        right: "5%",
      },
      series: [
        {
          data: [42, 38, 45, 32, 29, 35, 28],
          type: "line",
          areaStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
          itemStyle: {
            color: "#6a99fc",
          },
          lineStyle: {
            color: "#6a99fc",
          },
          symbol: "circle",
          symbolSize: 5,
        },
      ],
    },
  });
  
  const state = reactive({
    timer: null,
    curBtn: 0,
    curTab: 0,
    curTime: new Date().getTime(),
  });
  
  const { curBtn, curTab, curTime, timer } = toRefs(state);
  
  // 设备状态列表
  const deviceStatusList = ref([
    { value: 92, unit: "%", title: "设备在线率" },
    { value: 85, unit: "%", title: "设备完好率" },
    { value: 78, unit: "%", title: "设备利用率" },
    { value: 95, unit: "%", title: "维护覆盖率" },
  ]);
  
  // 设备数量统计
  const deviceCountList = ref([
    { value: 3653, unit: "台", title: "在线设备" },
    { value: 286, unit: "台", title: "故障设备" },
    { value: 1247, unit: "台", title: "运行设备" },
    { value: 892, unit: "台", title: "待机设备" },
  ]);
  
  // 设备维护情况列表
  const maintenanceList = ref([
    {
      percentage: 67,
      title: "设备利用率",
      color: "#1a57fa",
    },
    {
      percentage: 85,
      title: "维护完成率",
      color: "#47a7fa",
    },
    {
      percentage: 92,
      title: "巡检覆盖率",
      color: "#00b8f9",
    },
  ]);
  
  // 头部按钮数据
  const headerBtns = ref([
    { name: "设备概况", type: "overview" },
    { name: "运行监控", type: "monitor" },
    { name: "告警分析", type: "alarm" },
  ]);
  
  // 按钮点击处理函数
  const handleBtnClick = (index) => {
    curBtn.value = index; // 更新当前选中按钮的索引
  
    // 根据不同按钮类型更新数据
    switch (headerBtns.value[index].type) {
      case "overview": // 设备概况视图
        // 更新设备状态数据 - 显示设备整体运行状态
        deviceStatusList.value = [
          { value: 92, unit: "%", title: "设备在线率" },
          { value: 85, unit: "%", title: "设备完好率" },
          { value: 78, unit: "%", title: "设备利用率" },
          { value: 95, unit: "%", title: "维护覆盖率" },
        ];
  
        // 更新设备数量统计 - 显示不同状态的设备数量
        deviceCountList.value = [
          { value: 3653, unit: "台", title: "在线设备" },
          { value: 286, unit: "台", title: "故障设备" },
          { value: 1247, unit: "台", title: "运行设备" },
          { value: 892, unit: "台", title: "待机设备" },
        ];
  
        // 更新维护情况 - 显示设备维护相关的进度
        maintenanceList.value = [
          { percentage: 67, title: "设备利用率", color: "#1a57fa" },
          { percentage: 85, title: "维护完成率", color: "#47a7fa" },
          { percentage: 92, title: "巡检覆盖率", color: "#00b8f9" },
        ];
  
        // 更新各区域标题
        titles.value = {
          ...titles.value,
          title1: "设备类型分布",
          title2: "设备运行状态",
          title3: "部门设备概况",
        };
        break;
  
      case "monitor": // 运行监控视图
        // 更新状态列表 - 显示系统资源使用情况
        deviceStatusList.value = [
          { value: 88, unit: "%", title: "CPU使用率" },
          { value: 76, unit: "%", title: "内存使用率" },
          { value: 82, unit: "%", title: "存储使用率" },
          { value: 93, unit: "%", title: "网络使用率" },
        ];
  
        // 更新设备数量 - 显示性能相关的设备状态
        deviceCountList.value = [
          { value: 2865, unit: "台", title: "正常运行" },
          { value: 468, unit: "台", title: "性能告警" },
          { value: 986, unit: "台", title: "高负载" },
          { value: 756, unit: "台", title: "待优化" },
        ];
  
        // 更新维护情况 - 显示资源利用率
        maintenanceList.value = [
          { percentage: 78, title: "CPU利用率", color: "#1a57fa" },
          { percentage: 65, title: "内存利用率", color: "#47a7fa" },
          { percentage: 88, title: "网络利用率", color: "#00b8f9" },
        ];
  
        // 更新标题为监控相关
        titles.value = {
          ...titles.value,
          title1: "资源使用分布",
          title2: "性能监控状态",
          title3: "系统负载情况",
        };
        break;
  
      case "alarm": // 告警分析视图
        // 更新状态列表 - 显示告警处理相关指标
        deviceStatusList.value = [
          { value: 95, unit: "%", title: "告警处理率" },
          { value: 89, unit: "%", title: "故障解决率" },
          { value: 72, unit: "%", title: "预警准确率" },
          { value: 86, unit: "%", title: "系统可用率" },
        ];
  
        // 更新设备数量 - 显示不同级别的告警数量
        deviceCountList.value = [
          { value: 156, unit: "个", title: "严重告警" },
          { value: 486, unit: "个", title: "重要告警" },
          { value: 892, unit: "个", title: "次要告警" },
          { value: 1247, unit: "个", title: "提示信息" },
        ];
  
        // 更新维护情况 - 显示告警处理进度
        maintenanceList.value = [
          { percentage: 92, title: "告警处理率", color: "#1a57fa" },
          { percentage: 76, title: "故障修复率", color: "#47a7fa" },
          { percentage: 85, title: "问题解决率", color: "#00b8f9" },
        ];
  
        // 更新标题为告警相关
        titles.value = {
          ...titles.value,
          title1: "告警等级分布",
          title2: "告警处理状态",
          title3: "故障分析概况",
        };
        break;
    }
  
    // 更新图表数据
    updateChartData(headerBtns.value[index].type);
  };
  
  // 更新所有图表的数据
  const updateChartData = (type) => {
    switch (type) {
      case "overview":
        // 设备概况下的图表数据
        barOption.options.series[0].data = [386, 275, 198, 167, 142]; // 柱状图：各类设备数量
        pieOption.options.series[0].data = [
          // 饼图：设备类型分布
          { value: 386, name: "服务器" },
          { value: 275, name: "网络设备" },
          { value: 198, name: "安全设备" },
          { value: 167, name: "存储设备" },
        ];
        lineOption.options.series[0].data = [42, 38, 45, 32, 29, 35, 28]; // 折线图：设备趋势
        break;
  
      case "monitor":
        // 运行监控下的图表数据
        barOption.options.series[0].data = [425, 312, 256, 189, 134]; // 柱状图：资源使用情况
        pieOption.options.series[0].data = [
          // 饼图：资源分布
          { value: 425, name: "CPU" },
          { value: 312, name: "内存" },
          { value: 256, name: "存储" },
          { value: 189, name: "网络" },
        ];
        lineOption.options.series[0].data = [56, 48, 52, 45, 39, 42, 35]; // 折线图：性能趋势
        break;
  
      case "alarm":
        // 告警分析下的图表数据
        barOption.options.series[0].data = [156, 486, 892, 1247, 235]; // 柱状图：告警数量
        pieOption.options.series[0].data = [
          // 饼图：告警等级分布
          { value: 156, name: "严重告警" },
          { value: 486, name: "重要告警" },
          { value: 892, name: "次要告警" },
          { value: 235, name: "提示信息" },
        ];
        lineOption.options.series[0].data = [35, 42, 38, 45, 32, 28, 36]; // 折线图：告警趋势
        break;
    }
  };
  
  onMounted(() => {
    // state.timer = setInterval(() => {
    //     state.curTime += 1000
    // }, 1000)
  });
  
  onBeforeUnmount(() => clearInterval(state.timer));
  </script>
  
  <style lang="scss" scoped>
  .unify {
    width: 100vw;
    min-height: 100vh;
    background-color: #020d20;
    color: #c8d7fa;
    .opt-title {
      color: #6889de !important;
    }
    &-main {
      width: 100%;
      // height: 90vh;
      background: url("@/assets/screen/bg.png");
      background-size: 100% 100%;
      &_header {
        position: relative;
        width: 100%;
        height: 4vw;
        background: url("@/assets/screen/top_nav.png");
        background-size: 100% 100%;
        display: flex;
        align-items: center;
        padding: 0 1vw;
        &-title {
          // border: 1px solid red;
          font-size: 2vw;
          letter-spacing: 0.4vw;
        }
        &-btns {
          display: flex;
          margin-left: 4vw;
          padding-top: 0.5vw;
          &_btn {
            // border: 1px solid red;
            color: #6889de;
            cursor: pointer;
            text-align: center;
            width: 10.3vw;
            height: 2vw;
            line-height: 2vw;
            background: url("@/assets/screen/top_btn.png");
            background-size: 100% 100%;
            transition: all 0.3s ease;
            position: relative;
            overflow: hidden;
  
            &::before {
              content: "";
              position: absolute;
              top: 50%;
              left: 50%;
              width: 0;
              height: 0;
              background: rgba(255, 255, 255, 0.1);
              border-radius: 50%;
              transform: translate(-50%, -50%);
              transition: width 0.5s ease, height 0.5s ease;
            }
  
            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
  
              &::before {
                width: 300%;
                height: 300%;
              }
            }
  
            &.active {
              color: #c8d7fa;
              background: url("@/assets/screen/top_btn_sel.png");
              background-size: 100% 100%;
              transform: translateY(0);
  
              &::before {
                width: 0;
                height: 0;
              }
            }
          }
        }
        &-date {
          position: absolute;
          right: 1vw;
          top: 1.5vw;
        }
      }
      &_charts {
        display: flex;
        // border: 1px solid red;
        width: 100%;
        height: 47vw;
        padding: 1vw;
        .col {
          .item {
            // width: 100%;
            position: relative;
            margin-bottom: 1vw;
            &::before {
              //border: 1px solid red;
              content: "";
              width: 100%;
              height: 2.5vw;
              left: 0;
              bottom: 0;
              position: absolute;
              background: url("@/assets/screen/frame_bottom.png");
              background-size: 100% 100%;
            }
  
            &-title {
              // width: 100%;
              height: 2.5vw;
              line-height: 2.5vw;
              background: url("@/assets/screen/title.png");
              background-size: 100% 100%;
            }
          }
          .flex1 {
            display: flex;
            flex-wrap: wrap;
            gap: 1vw;
          }
        }
        .col1 {
          // border: 1px solid red;
          // background-color: green;
          width: 20vw;
          height: 100%;
          &-top {
            height: 13vw;
            .echart {
              display: flex;
              justify-content: space-between;
              height: 7vw;
              gap: 0 1vw;
              margin-top: 1.7vw;
              &-item {
                flex: 1;
                // border: 1px solid #fff;
                &.one {
                  background: url("@/assets/screen/left_data_frame_top.png");
                  background-size: 100% 100%;
                  text-align: center;
                  align-content: center;
                  :deep(.el-progress-circle) {
                    width: 5vw !important;
                    height: 5vw !important;
                  }
                  :deep(.el-progress path:first-child) {
                    stroke: #042043;
                  }
                  :deep(.el-progress__text) {
                    color: #c8d7fa;
                    font-size: 1.5vw !important;
                    min-width: 0 !important;
                  }
                }
                &.two {
                  //border: 1px solid yellow;
                  flex: 1.7;
                }
              }
            }
          }
          &-bottom {
            .item-main {
              padding: 1vw 0;
            }
            .item-opt {
              // border: 1px solid red;
              width: 9.2vw;
              background: url("@/assets/screen/left_data_frame_middle.png");
              background-size: 100% 100%;
              height: 3vw;
              padding: 0.2vw 0.5vw 0 3.5vw;
              font-size: 1.2vw;
              div {
                font-size: 0.8vw;
              }
            }
            .table {
              font-size: 0.8vw;
              height: 19.5vw;
              // padding-top: 1vw;
              &-row {
                display: flex;
                padding: 0.55vw 0;
                &.data:hover {
                  background-color: #061d47;
                }
                &_number {
                  width: 3vw;
                }
                &_type {
                  width: 9vw;
                }
                &_count {
                  width: 5vw;
                }
                &_percent {
                  width: 4vw;
                  padding-left: 1vw;
                  position: relative;
                  &.up::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_up.png");
                    background-size: 100% 100%;
                  }
                  &.low::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_low.png");
                    background-size: 100% 100%;
                  }
                  &.table-row_head {
                    padding-left: 0;
                  }
                }
              }
            }
          }
        }
        .col2 {
          // border: 1px solid red;
          // background-color: yellow;
          width: 42.5vw;
          height: 100%;
          &-top {
            // border: 1px solid red;
            display: flex;
            padding: 1vw;
            gap: 0 1vw;
            .item-opt {
              padding: 0vw 0 0vw 1.5vw;
              width: 9.5vw;
              height: 4vw;
              // border: 1px solid red;
              background: url("@/assets/screen/top_data_frame.png");
              background-size: 100% 100%;
              letter-spacing: 0.1vw;
              color: #6c95ff;
              span {
                color: #c8d7fa;
                display: inline-block;
                font-size: 2vw;
                margin-bottom: 0.4vw;
              }
            }
          }
          &-bottom {
            height: 39vw;
            // border: 1px solid red;
            &_bg {
              position: relative;
              // border: 1px solid red;
              width: 30vw;
              height: 26vw;
              margin: 6vw auto;
              background: url("@/assets/screen/middle_banner.png");
              background-size: 100% 100%;
              .item-opt {
                width: 10vw;
                height: 2vw;
                position: absolute;
                cursor: pointer;
                div {
                  position: absolute;
                  // border: 1px solid red;
                  width: 8vw;
                  height: 2vw;
                  text-align: center;
                  line-height: 2vw;
                }
                span {
                  position: absolute;
                  right: 0.4vw;
                  top: 0.5vw;
                }
                &.right {
                  background: url("@/assets/screen/middle_frame_right.png");
                  background-size: 100% 100%;
                  div {
                    right: 0;
                  }
                  span {
                    left: 0.4vw;
                  }
                }
                &.left {
                  background: url("@/assets/screen/middle_frame_left.png");
                  background-size: 100% 100%;
                }
                &.opt1 {
                  top: -0.8vw;
                  left: -5.5vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_left_top_line.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 3vw;
                    right: -3.9vw;
                    top: -0.4vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_left_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      right: -4.6vw;
                      top: -1.1vw;
                      width: 5vw;
                      height: 4vw;
                      background: url("@/assets/screen/middle_left_top_line_sel.png");
                      background-size: 100% 100%;
                    }
                  }
                }
                &.opt2 {
                  right: -2.5vw;
                  top: 0.5vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_right_top_line.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 3vw;
                    left: -3.9vw;
                    top: -0.6vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_right_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      background: url("@/assets/screen/middle_right_top_line_sel.png");
                      background-size: 100% 100%;
                      width: 5vw;
                      height: 4vw;
                      left: -4.7vw;
                      top: -1vw;
                    }
                  }
                }
                &.opt3 {
                  top: 5.8vw;
                  left: -5vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_left_middle_line.png");
                    background-size: 100% 100%;
                    width: 3vw;
                    height: 4vw;
                    right: -0.7vw;
                    top: 1.9vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_left_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      position: absolute;
                      content: "";
                      background: url("@/assets/screen/middle_left_middle_line_sel.png");
                      background-size: 100% 100%;
                      width: 4vw;
                      height: 5vw;
                      right: -1.1vw;
                      top: 1.6vw;
                    }
                  }
                }
                &.opt4 {
                  right: -5vw;
                  bottom: 10.5vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_right_middle_line.png");
                    background-size: 100% 100%;
                    width: 3vw;
                    height: 4vw;
                    left: 3.5vw;
                    top: -4vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_right_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      position: absolute;
                      content: "";
                      background: url("@/assets/screen/middle_right_middle_line_sel.png");
                      background-size: 100% 100%;
                      width: 4vw;
                      height: 5vw;
                      left: 3vw;
                      top: -4.6vw;
                    }
                  }
                }
                &.opt5 {
                  bottom: -0.5vw;
                  left: -6vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_left_bottom_line.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 3vw;
                    right: -4vw;
                    top: -0.5vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_left_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      position: absolute;
                      content: "";
                      background: url("@/assets/screen/middle_left_bottom_line_sel.png");
                      background-size: 100% 100%;
                      width: 5vw;
                      height: 4vw;
                      right: -4.6vw;
                      top: -0.9vw;
                    }
                  }
                }
                &.opt6 {
                  right: -3.5vw;
                  bottom: 0.5vw;
                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_right_bottom_line.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 1.8vw;
                    left: -3.9vw;
                    top: -1.5vw;
                  }
                  &.active {
                    background: url("@/assets/screen/middle_frame_right_sel.png");
                    background-size: 100% 100%;
                    &::before {
                      position: absolute;
                      content: "";
                      background: url("@/assets/screen/middle_right_bottom_line_sel.png");
                      background-size: 100% 100%;
                      width: 5.5vw;
                      height: 4vw;
                      left: -5.5vw;
                      top: -3.4vw;
                    }
                  }
                }
              }
            }
          }
        }
        .col3 {
          // border: 1px solid red;
          width: 18vw;
          height: 100%;
          &-top {
            .item-main {
              padding: 1vw 0;
              display: flex;
              flex-wrap: wrap;
              gap: 0.5vw;
            }
            .item-opt {
              width: 8.7vw;
              // border: 1px solid red;
              background: url("@/assets/screen/right_data_frame.png");
              background-size: 100% 100%;
              padding: 0.5vw 0.5vw 1vw;
              font-size: 0.8vw;
              color: #8495b6;
              &_row {
                display: flex;
                align-items: center;
                margin-top: 0.5vw;
                span {
                  display: inline-block;
                  font-size: 1.1vw;
                  padding-right: 0.2vw;
                  color: #c7d9f9;
                }
                div {
                  padding-left: 1.2vw;
                }
                .up {
                  color: #2dcd5e;
                  position: relative;
                  &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0.3vw;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_up.png");
                    background-size: 100% 100%;
                  }
                }
                .low {
                  color: #fe005d;
                  position: relative;
                  &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0.3vw;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_low.png");
                    background-size: 100% 100%;
                  }
                }
              }
            }
          }
          &-center {
            height: 13vw;
            .echart {
              height: 10vw;
            }
          }
          &-bottom {
            height: 16.4vw;
            .echart {
              height: 15vw;
            }
          }
        }
        .col4 {
          // background-color: orange;
          width: 16.3vw;
          height: 100%;
          margin-left: 1vw;
          &-top {
            height: 17.3vw;
            .item-progress {
              padding: 1vw 0;
              display: flex;
              justify-content: space-between;
              :deep(.el-progress-circle) {
                width: 2vw !important;
                height: 2vw !important;
              }
              :deep(.el-progress path:first-child) {
                stroke: #042043;
              }
              &_right {
                font-size: 1vw;
                div {
                  font-size: 0.7vw;
                }
              }
              .item-opt {
                display: flex;
                justify-content: space-between;
                gap: 0 0.5vw;
                // border: 1px solid red;
              }
            }
            .item-line {
              display: flex;
              flex-direction: column;
              gap: 0.5vw;
              font-size: 0.8vw;
              .item-opt {
                padding: 0.3vw 0;
                // cursor: pointer;
                // border: 1px solid red;
                display: flex;
                justify-content: space-between;
                align-items: center;
                // transition: all .1s;
                &:hover {
                  color: #1245c6;
                  background-color: #061b43;
                  .item-opt_center {
                    background-color: #061e4b;
                    border: 0.1vw solid #0a2055;
                    width: 8vw;
                  }
                }
                &_center {
                  position: relative;
                  width: 8vw;
                  height: 0.5vw;
                  background-color: #051639;
                  border: 0.1vw solid #061c46;
  
                  .dot {
                    position: absolute;
                    width: 0.5vw;
                    height: 0.5vw;
                    background: url("@/assets/screen/progress_line_dot.png");
                    background-size: 100% 100%;
                    top: 0;
                    left: 0;
                  }
                }
              }
            }
          }
          &-bottom {
            height: 26.7vw;
            .item-row {
              padding: 0.8vw 0;
              display: flex;
              font-size: 0.9vw;
              justify-content: space-between;
              &_right {
                display: flex;
                align-items: flex-end;
                font-size: 1.1vw;
                span {
                  font-size: 0.8vw;
                  display: inline-block;
                  padding-left: 0.1vw;
                }
                div {
                  font-size: 0.8vw;
                  padding-left: 1.5vw;
                  position: relative;
                  color: #e2650c;
                  &::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0.5vw;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_up2.png");
                    background-size: 100% 100%;
                  }
                }
              }
            }
            .table {
              font-size: 0.8vw;
              height: 19.5vw;
              &-row {
                display: flex;
                align-items: center;
                // margin-bottom: .94vw;
                padding: 0.65vw 0;
                &.data:hover {
                  background-color: #061d47;
                }
                &_number {
                  width: 3vw;
                }
                &_type {
                  width: 5vw;
                }
                &_count {
                  width: 5vw;
                }
                &_percent {
                  width: 4vw;
                  padding-left: 1vw;
                  position: relative;
                  &.up::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_up.png");
                    background-size: 100% 100%;
                  }
                  &.low::before {
                    content: "";
                    position: absolute;
                    top: 0;
                    left: 0;
                    width: 0.7vw;
                    height: 0.8vw;
                    background: url("@/assets/screen/arrow_low.png");
                    background-size: 100% 100%;
                  }
                  &.table-row_head {
                    padding-left: 0;
                  }
                }
              }
            }
          }
        }
      }
    }
  }
  </style>
  