<template>
    <div class="recording app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                   {{ route.meta.title }}
                   <el-button @click="handleBack">返回{{ route.query.type ? '工作' : '服务' }}台</el-button>
                </div>
            </template>
            <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="complaintNumber">
          <el-input
            v-model="queryParams.complaintNumber"
            placeholder="请输入工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
            <div class="recording-main">
                <div class="recording-main_table">
                    <el-table
                        ref="tableRef"
                        v-loading="loading"
                        :data="tableList"
                        border
                        highlight-current-row
                    >
                        <el-table-column
                        label="录音编号"
                        align="center"
                        minWidth="120px"
                        prop="number"
                        />
                        <el-table-column
                        label="录音"
                        align="center"
                        minWidth="120px"
                        prop="file"
                        />
                        <el-table-column
                        label="工单编号"
                        align="center"
                        minWidth="120px"
                        prop="todoName"
                        />
                        <el-table-column
                        label="创建时间"
                        align="center"
                        minWidth="120px"
                        prop="createTime"
                        />
                        <el-table-column
                        label="操作"
                        min-width="120"
                        align="center"
                        >
                        <template #default="scope">
                            <el-button
                            link
                            type="primary"
                            icon="Download"
                            v-throttle
                            @click.stop="handleDownload(scope)"
                            >下载</el-button
                            >
                            <el-button
                            link
                            type="danger"
                            icon="Delete"
                            v-throttle
                            @click.stop="handleDel(scope)"
                            >删除</el-button
                            >
                        </template>
                        </el-table-column>
                    </el-table>

                    <pagination
                        v-show="tableList.length > 0"
                        :total="tableList.length"
                        v-model:page="queryParams.current"
                        v-model:limit="queryParams.size"
                        @pagination="getList"
                    />
                </div>
            </div>
            <el-dialog v-model="dialogVisible" title="满意度评价">

            </el-dialog>
        </el-card>
    </div>
</template>

<script setup name="recording">
import { useRoute } from 'vue-router'
import { ref, reactive, toRefs, getCurrentInstance } from 'vue'

const route = useRoute()
const { proxy } = getCurrentInstance()
const state = reactive({
    dialogVisible: false,
    total: 1,
    loading: false,
    tableList_all: [
        { number: '01', file: '20241126110145.mp3', todoName: '工单111', createTime: '2024-11-26 11:01:45' },
        { number: '02', file: '20241127103259.mp3', todoName: '工单222', createTime: '2024-11-27 10:32:59' },
        { number: '03', file: '20241128152006.mp3', todoName: '工单333', createTime: '2024-11-28 15:20:06' },
    ],
    tableList: [],
    typeList: [],
    statusList: [
        { label: "待处理", value: 0, type: "danger" },
        { label: "已处理", value: 1, type: "success" },
    ],
    queryParams: {
        current: 1,
        size: 10,
    },
})

const { tableList, typeList, queryParams, dialogVisible, statusList, loading, total } = toRefs(state)

function handleBack() {
    proxy.$tab.closeOpenPage(route.query.type ? '/work' : '/service')
}

function handleDel({row, $index}) {
    proxy.$modal.confirm(`确定要删除编号为${row.number}的录音信息？`).then(() => {
        state.tableList_all.splice($index, 1)
        // 同步更新完tableList_all后更新localStorage， 新增、删除、修改操作都需要做这一步，若页面有变更记录则同步更新变更记录列表
        // localStorage.setItem('WHYWPT_SPARELIST_ALL', JSON.stringify(state.tableList_all))

        setTimeout(() => {
            proxy.$modal.msgSuccess('操作成功')
            getList()
        }, 500)
    })
}

function getList() {
    state.tableList = JSON.parse(JSON.stringify(state.tableList_all))
}

getList()

</script>