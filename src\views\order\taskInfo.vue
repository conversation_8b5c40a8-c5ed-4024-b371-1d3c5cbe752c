<template>
  <div class="taskInfo app-container">
    <el-card shadow="never" class="page-card" v-loading="loading">
      <template #header>
        <div class="card-header page-header">任务单详情</div>
      </template>

      <taskDetail ref="detailRef" :isAdd="false" />

      <el-form
        class="taskInfo-form"
        ref="handleRef"
        :model="handleInfo"
        :rules="rules"
        v-if="isEdit || isHandled"
      >
        <el-descriptions title="" border :column="2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理人"
          >
            <el-form-item label="" prop="handleId">
              <el-select
                v-model="handleInfo.handleId"
                style="width: 250px"
                :disabled="isHandled || !isEdit"
                filterable
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId * 1"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理时间"
          >
            <el-form-item label="" prop="handleTime">
              <el-date-picker
                type="datetime"
                v-model="handleInfo.handleTime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 250px"
                :disabled-date="disabledDateFn"
                :disabled-hours="disabledHours"
                :disabled-minutes="disabledMinutes"
                :disabled-seconds="disabledSeconds"
                placeholder="请选择处理时间"
                :disabled="isHandled || !isEdit"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理描述"
          >
            <el-form-item label="" prop="handleDescribe" style="width: 100%">
              <editor
                v-model="handleInfo.handleDescribe"
                :min-height="200"
                type="url"
                @update:modelValue="contentChange"
                @blur="handleRef.validateField('handleDescribe')"
                :isEdit="isEdit && !isHandled"
              />
              <div
                v-show="isEdit"
                style="position: absolute; bottom: -30px; right: 10px"
              >
                共<span
                  :style="{ color: contentTotal > 500 ? '#f56c6c' : '' }"
                  >{{ ` ${contentTotal} ` }}</span
                >字
              </div>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <div class="taskInfo-btns">
        <el-button type="primary" v-if="!isHandled" @click="handleEdit" v-throttle>{{
          !isEdit ? "处理" : "保存"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import taskDetail from "./components/taskDetail.vue";
import {
  timeFormat,
  disabledSeconds,
  disabledDateFn,
  disabledMinutes,
  disabledHours,
} from "@/utils";
import useUserStore from "@/store/modules/user";
import { projectTaskInfo, updateProjectTask } from "@/api/order";
import { listUser } from "@/api/system/user";

const { proxy } = getCurrentInstance();
const route = useRoute();

const state = reactive({
  detailRef: null,
  loading: false,
  contentTotal: 0,
  handleRef: null,
  isHandled: false,
  isEdit: false,
  userList: [{ userName: "张三", id: 1 }],
  handleInfo: {
    taskId: "",
    handleId: useUserStore().userId,
    handleDescribe: "",
    handleTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
  },
  rules: {
    handleDescribe: [
      {
        required: true,
        message: "处理描述不能为空",
        trigger: ["blur", "change"],
      },
    ],
    handledId: [
      {
        required: true,
        message: "处理人不能为空",
        trigger: ["blur", "change"],
      },
    ],
    handleTime: [
      {
        required: true,
        message: "处理时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  rules,
  detailRef,
  loading,
  contentTotal,
  handleRef,
  handleInfo,
  userList,
  isEdit,
  isHandled,
} = toRefs(state);

// 统计活动内容纯文本字数
function contentChange(val) {
  //   console.log("纯文本", val);
  // infotaskInfo.value.content = infotaskInfo.value.content.subString(0, 500)
  contentTotal.value = val
    .replace(/<[^>]+>/g, "", "")
    .replace(/\s/g, "").length;
}

async function handleEdit() {
  if (state.isEdit) {
    state.handleRef.validate((valid) => {
      if (valid) {
        proxy.$modal.loading();
        console.log("处理任务传参", state.handleInfo);
        updateProjectTask(state.handleInfo)
          .then((resp) => {
            proxy.$modal.closeLoading();
            proxy.$modal.msgSuccess("保存成功");
            state.detailRef.updateInfo(state.handleInfo);
            state.isHandled = true;
          })
          .catch(() => proxy.$modal.closeLoading());
      }
    });
  } else {
    state.isEdit = !state.isEdit;
  }
  console.log("处理任务详情", state.handleInfo);
}

function handleBack() {
  proxy.$tab.closeOpenPage("orderTask");
}

/** 查询用户列表 */
function getUserList() {
  listUser({ current: 1, size: 999999 }).then((res) => {
    console.log(res, "查询到的用户列表");
    userList.value = res.data.records;
  });
}

function getData() {
  state.loading = true;
  projectTaskInfo(route.query.id)
    .then((resp) => {
      console.log("任务详情", resp);
      if (resp.data) {
        state.detailRef.updateInfo(resp.data);
        state.isHandled = resp.data.handleStatus == 1;
        if (state.isHandled) {
          state.handleInfo = JSON.parse(JSON.stringify(resp.data));
        } else {
          state.handleInfo.taskId = resp.data.taskId;
        }
      }
      state.loading = false;
    })
    .catch(() => (state.loading = false));
}

onMounted(() => {
  state.isEdit = route.query.type == 1;
  getUserList();
  getData();
});
</script>

<style lang="scss" scoped>
.taskInfo {
  &-cont {
    margin: 20px 0;
  }
  &-form {
    margin-top: 50px;
  }
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-btns {
    padding: 50px 0;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}
</style>