import request from '@/utils/request'

//教师最近6个月使用时长
export function queTeacherMonth(params) {
    return request({
        url: '/system/teacher/queTeacherMonth',
        method: 'get',
        params
    })
}

//导出教学日志
export function exportDevice(data) {
    return request({
        url: '/system/teacher/exportDevice',
        method: 'post',
        responseType: 'blob',
        data
    })
}

//教学日志
export function queDeviceLogPage(data) {
    return request({
        url: '/system/teacher/queDeviceLog',
        method: 'post',
        data
    })
}

//查询老师-年龄使用时长
export function queAgeUseTime(params) {
    return request({
        url: '/system/teacher/queAgeUseTime',
        method: 'get',
        params
    })
}

//查询老师职称使用时长
export function quePostUseTime(params) {
    return request({
        url: '/system/teacher/quePostUseTime',
        method: 'get',
        params
    })
}

//查询老师学历使用时长
export function queEducationUseTime(params) {
    return request({
        url: '/system/teacher/queEducationUseTime',
        method: 'get',
        params
    })
}

//查询男女老师使用时长
export function queTeacherSexUseTime(params) {
    return request({
        url: '/system/teacher/queTeacherSexUseTime',
        method: 'get',
        params
    })
}

//查询教师列表
export function teacherPage(data) {
    return request({
        url: '/system/teacher/quePage',
        method: 'post',
        data
    })
}

//数字设备使用时长教师前5名
export function teacherTimeRank(data) {
    return request({
        url: '/system/teacher/queTimeRank',
        method: 'post',
        data
    })
}

//数字设备使用时长教师前5名
export function teacherInfo(params) {
    return request({
        url: '/system/teacher/getInfo',
        method: 'get',
        params
    })
}