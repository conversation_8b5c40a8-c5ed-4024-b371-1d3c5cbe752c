<template>
    <el-dialog title="设置学生数量" v-model="open" width="400px" :show-close="studentNum > 0" :close-on-press-escape="false"
        :close-on-click-modal="false">
        <div>学生数量： <el-input-number v-model="num" :min="0" /></div>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" @click="handleSubmit">确 定</el-button>
                <el-button v-if="studentNum > 0" @click="open = false">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup>
import { getCurrentInstance, ref, computed } from 'vue';
import { editNum, getInfo } from '@/api/park'
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()
const { proxy } = getCurrentInstance()
const open = ref(false)
const num = ref(0)
const studentNum = computed(() => userStore.studentNum)
function handleSubmit() {
    if (num.value === 0) {
        proxy.$modal.msgWarning('学生数量不能为0')
    } else {
        editNum(num.value).then(response => {
            open.value = false
            userStore.getStudentNum()
            proxy.$modal.msgSuccess('设置成功')
        })
    }
}

async function openSetting() {
    await userStore.getStudentNum()
    num.value = studentNum.value
    open.value = true;
}
defineExpose({
    openSetting
})
</script>