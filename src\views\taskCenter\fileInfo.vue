<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <div class="fileInfo-header">
        <div
          class="fileInfo-header_item"
          v-for="(item, index) in activeList"
          :key="index"
          :class="[active == index ? 'active' : '', `item${index + 1}`]"
        >
          {{ item }}
        </div>
      </div>

      <div class="projectInfo" v-if="active == 0">
        <div class="projectInfo-btn">
          <el-form :model="form" ref="formRef" :rules="rules">
            <el-form-item label="请输入文档名称" prop="documentName">
              <el-input
                v-model="form.documentName"
                maxlength="50"
                placeholder="请输入文档名称"
                style="width: 100%"
              />
            </el-form-item>
            <el-form-item label="请选择关联项目信息" prop="projectId">
              <el-button type="primary" icon="plus" @click="handleAdd"
                >点击选择</el-button
              >
            </el-form-item>
          </el-form>
        </div>

        <el-descriptions title="项目信息" border :column="3">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目编号"
          >
            {{ projectInfo.projectNo || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目名称"
          >
            <span>{{ projectInfo.projectName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目负责人"
          >
            <span>{{ projectInfo.responsibleNameStr || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目参与人"
          >
            <span>{{ projectInfo.partakeNameStr || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目开始时间"
          >
            <span>{{ projectInfo.startTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目结束时间"
          >
            <span>{{ projectInfo.endTime || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目金额"
          >
            <span>{{ projectInfo.projectAmount || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="项目归属部门"
          >
            <span>{{ projectInfo.projectDeptName || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="是否开放小程序搜索"
          >
            <span>{{
              projectInfo.isOpen == 1
                ? "开放"
                : projectInfo.isOpen == 0
                ? "不开放"
                : "-"
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="甲方"
          >
            <span>{{ projectInfo.partyA || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="乙方"
          >
            <span>{{ projectInfo.partyB || "-" }}</span>
          </el-descriptions-item>
        </el-descriptions>

        <div class="projectInfo-title">项目干系人</div>
        <el-table :data="stakeholderList" border>
          <el-table-column
            label="姓名"
            prop="nickName"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column
            label="性别"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.sex == "0" ? "男" : row.sex == "1" ? "女" : "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="部门"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.deptName || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="岗位"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.postName || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="手机号码"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.phone || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="邮箱"
            min-width="100"
            align="center"
            show-overflow-tooltip
          >
            <template #default="{ row }">
              {{ row.email || "-" }}
            </template>
          </el-table-column>
        </el-table>
      </div>

      <Detail
        ref="detailRef"
        v-show="active == 1"
        :troubleId="route.query.id"
        :isHandled="true"
        @handleBack="handleBack"
      />

      <!-- 按钮区域，根据readonly状态显示不同按钮 -->
      <div class="taskInfo-btns">
        <el-button type="primary" @click="handleNext">{{
          active == 0 ? "下一页" : "上一页"
        }}</el-button>
        <el-button v-if="active == 1" type="success" @click="handleSubmit"
          >生成文档</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>

    <el-dialog
      class="custom-dialog"
      width="800"
      :title="title"
      v-model="dialogVisible"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancel"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
        @submit.native.prevent
      >
        <el-form-item label="" prop="searchKey">
          <el-input
            placeholder="请输入编号/名称/项目负责人"
            clearable
            style="width: 200px"
            v-model="queryParams.searchKey"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        :data="tableList"
        border
        @row-click="rowSingleClick"
        v-loading="loading"
      >
        <el-table-column align="center" width="50">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="(val) => handleSingleChange(val, scope.$index)"
              @click.native.stop=""
            />
          </template>
        </el-table-column>
        <el-table-column
          label="项目编号"
          prop="projectNo"
          min-width="100"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目名称"
          prop="projectName"
          min-width="100"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目负责人"
          prop="responsibleNameStr"
          min-width="120"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="项目归属部门"
          prop="projectDeptName"
          min-width="100"
          align="center"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :background="false"
        :autoScroll="false"
        layout="total,prev,pager,next"
      />
      <template #footer>
        <el-button @click.stop="handleCancel">返回</el-button>
        <el-button type="primary" @click.stop="handleConfirm" v-throttle
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>
  
  <script setup>
import { useRoute, useRouter } from "vue-router";
import { toRefs, reactive } from "vue";
import { addKnowledgeBase, troubleUpdate } from "@/api/mediaTeach/trouble";
import useUserStore from "@/store/modules/user";
import Detail from "@/views/taskCenter/components/index.vue";
import { getSchoolProjectPage, schoolProjectInfo } from "@/api/order";
import {
  addSchoolDocument,
  checkDocumentNameUnique,
} from "@/api/fileMamage/file";
import { sendPointRequest } from "@/utils";
import { addPointObj } from "@/utils/addPoint";

const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  detailRef: null,
  stakeholderList: [],
  loading: false,
  tableList: [],
  active: 0,
  total: 0,
  title: "请选择关联项目",
  dialogVisible: false,
  curProject: {},
  projectInfo: {},
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    searchKey: "",
  },
  formRef: null,
  form: {
    documentName: "",
    projectId: "",
  },
  rules: {
    projectId: [
      {
        required: true,
        message: "项目信息不能为空",
        trigger: ["blur", "change"],
      },
    ],
    documentName: [
      {
        required: true,
        validator: validName,
        trigger: ["blur"],
      },
    ],
  },
  processStartTime: null, // 添加开始处理时间字段
  activeList: ["项目/干系人信息", "报障信息"],
});

const {
  formRef,
  form,
  rules,
  detailRef,
  stakeholderList,
  projectInfo,
  loading,
  tableList,
  curProject,
  activeList,
  active,
  total,
  title,
  dialogVisible,
  queryParams,
} = toRefs(state);

async function checkName() {
  let flag = true;
  let obj = {
    documentName: form.value.documentName,
  };
  let res = await checkDocumentNameUnique(obj);
  flag = res.data;
  console.log(!flag);
  return !flag;
}

async function validName(rules, value, callback) {
  console.log(rules, value);
  if (value == "") {
    callback(new Error("文档名称不能为空"));
  } else if (await checkName(value)) {
    callback(new Error("文档名称已存在"));
  } else {
    callback();
  }
}

function handleSubmit() {
  console.log(detailRef.value.state);
  const {
    repairForm,
    form,
    maintainForm,
    spareList,
    commentForm,
    complaintsForm,
  } = detailRef.value.state;
  let d = {
    documentName: state.form.documentName,
    source: "工单管理",
    troubleId: route.query.id,
    projectId: state.projectInfo.projectId,
    documentContent: JSON.stringify({
      projectInfo: state.projectInfo,
      stakeholderList: state.stakeholderList,
      repairForm,
      form,
      maintainForm,
      spareList,
      commentForm,
      complaintsForm,
    }),
  };
  console.log(d, "创建传参");
  proxy.$modal.loading();
  addSchoolDocument(d)
    .then((res) => {
      sendPointRequest({
        event: "Click",
        eventDescribe: "点击生成文档",
        content: "",
        num: 1,
      });
      proxy.$modal.msgSuccess("创建成功");
      handleBack();
    })
    .finally(() => proxy.$modal.closeLoading());
}

function getList() {
  state.loading = true;
  clearCheck();
  getSchoolProjectPage(state.queryParams)
    .then((resp) => {
      console.log("项目列表", resp);
      if (resp.data) {
        const { rows = [], total = 0 } = resp.data;
        state.tableList = rows.map((item) => {
          const {
            sysResponsibleName,
            responsibleName,
            sysStakeholderName,
            stakeholderName,
            partakeName,
            sysPartakeName,
          } = item;
          item.responsibleNameStr = (sysResponsibleName?.split("、") || [])
            .concat(responsibleName?.split("、") || [])
            .filter((_) => !!_)
            .join("、");
          item.partakeNameStr = (sysPartakeName?.split("、") || [])
            .concat(partakeName?.split("、") || [])
            .filter((_) => !!_)
            .join("、");
          item.stakeholderNameStr = (sysStakeholderName?.split("、") || [])
            .concat(stakeholderName?.split("、") || [])
            .filter((_) => !!_)
            .join("、");
          item.checked = state.curProject.projectId == item.projectId;
          return item;
        });
        state.total = total;
      }
      state.loading = false;
    })
    .catch(() => (state.loading = false));
}

/** 弹窗搜索按钮操作 */
function handleQuery() {
  state.queryParams.current = 1;
  state.queryParams.pageNum = 1;
  getList();
}

/** 弹窗重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function handleCancel() {
  dialogVisible.value = false;
  state.curProject = {};
}

function handleConfirm() {
  if (!state.curProject.projectId) {
    proxy.$modal.msgWarning("请选择项目信息");
    return;
  }
  state.projectInfo = JSON.parse(JSON.stringify(state.curProject));
  let { projectId, stakeholderName } = state.projectInfo;
  state.form.projectId = projectId;
  state.formRef.validateField("projectId");
  state.stakeholderList = [];
  proxy.$modal.loading();
  schoolProjectInfo(projectId)
    .then((res) => {
      console.log("项目详情", res);
      if (res.data) {
        state.stakeholderList = res.data.stakeholderUserList || [];
      }
    })
    .finally(() => {
      if (stakeholderName) {
        stakeholderName.split("、").map((item) => {
          state.stakeholderList.push({ nickName: item });
        });
      }
      state.dialogVisible = false;
      proxy.$modal.closeLoading();
    });
}

function handleAdd() {
  state.dialogVisible = true;
  handleQuery();
}

function handleNext() {
  if (state.active == 0) {
    state.formRef.validate((valid) => {
      if (valid) {
        if (!state.projectInfo.projectId) {
          proxy.$modal.msgWarning("请选择项目信息");
          return;
        }
        state.active = 1;
      }
    });
  } else {
    state.active = 0;
  }
}

// 取消/返回处理
function handleBack() {
  router.go(-1);
  proxy.$tab.closeOpenPage();
}

function clearCheck() {
  state.tableList = state.tableList.map((item) => {
    item.checked = false;
    return item;
  });
}

function handleSingleChange(val, index) {
  console.log(val, index, state.tableList);
  if (val) {
    clearCheck();
    state.tableList[index].checked = true;
    state.curProject = JSON.parse(JSON.stringify(state.tableList[index]));
    console.log("check", val);
  } else {
    state.curProject = {};
  }
}

function rowSingleClick(row) {
  console.log("点击单行", state.tableList, row);
  const idx = state.tableList.findIndex((_) => _.projectId == row.projectId);
  if (row.checked) {
    state.tableList[idx].checked = false;
    state.curProject = {};
  } else {
    clearCheck();
    state.tableList[idx].checked = true;
    state.curProject = JSON.parse(JSON.stringify(row));
  }
}
</script>
  
  <style scoped lang="scss">
.fileInfo {
  &-header {
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0 2vw 20px;
    gap: 0 10vw;
    font-size: 14px;

    &_item {
      // border: 1px solid red;
      display: flex;
      align-items: center;
      gap: 0 10px;

      &::before {
        content: "1";
        width: 2.5vw;
        height: 2.5vw;
        display: inline-block;
        text-align: center;
        line-height: 2.5vw;
        background-color: #4095e5;
        color: #fff;
        border-radius: 50%;
      }

      &.item2::before {
        content: "2";
      }

      &.active {
        color: #4095e5;
      }
    }
  }
}
.projectInfo {
  font-size: 14px;
  &-btn {
    display: flex;
    align-items: center;
    gap: 0 10px;
    margin-top: 10px;
  }
  &-title {
    margin: 20px 0;
    font-size: 16px;
    font-weight: bold;
  }
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }
  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.taskInfo {
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }

  &-record {
    font-size: 14px;
    margin-bottom: 5px;
  }
  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }
  &-btns {
    padding: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    margin-top: 40px;
    .el-button {
      margin: 0 10px;
    }
  }
}

.upload-area {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .file-info {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
  