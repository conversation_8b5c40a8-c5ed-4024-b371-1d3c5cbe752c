<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <div class="block-title">
          <div class="block-title_text">
            <div class="titleText">资产覆盖率</div>
          </div>
        </div>
        <div class="battery">
          <span>{{ objData.assetCoverageStatistics.coverage }}</span>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">
          <div class="block-title_text">
            <div class="titleText">部门资产总数</div>
          </div>
        </div>
        <div class="table block">
          <div class="table-row opt-title">
            <div class="table-row_name table-row_head">部门名称</div>
            <div class="table-row_name table-row_head">部门人数</div>
            <div class="table-row_name table-row_head">资产总数</div>
            <div class="table-row_name table-row_head">操作</div>
          </div>
          <div
            class="table-row data"
            v-for="item in objData.assetCoverageStatistics.assetCoverageRanking"
            :key="item.deptName"
          >
            <div class="table-row_name" :title="item.deptName">
              {{ item.deptName }}
            </div>
            <div class="table-row_name">
              {{ item.peopleNum > 99999 ? "99999+" : item.peopleNum }}
            </div>
            <div class="table-row_name">
              {{ item.assetsNum > 99999 ? "99999+" : item.assetsNum }}
            </div>
            <div
              class="table-row_name"
              style="cursor: pointer; color: #008efa"
              @click="handleCheck(item)"
            >
              点击查看
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block4">
        <div class="flex" style="gap: 0 1vw">
          <div class="block echart-item">
            <div class="subtitle">信息资产类别占比</div>
            <div style="position: relative">
              <img
                class="pieData-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie5-bg.png"
              />
              <Echarts
                ref="pieDataRef"
                id="pieData"
                class="echart"
                width="13.5vw"
                height="9vw"
                :fullOptions="pieOption"
              />
            </div>
          </div>
          <div class="block echart-item">
            <div class="subtitle">信息资产类别TOP5</div>
            <Echarts
              id="barData"
              width="100%"
              height="9vw"
              :fullOptions="barOption"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="col col2">
      <div>
        <Echarts
          id="mapData"
          width="100%"
          height="100%"
          :fullOptions="mapOption"
          :mapConfig="{ name: 'GZ', data: getGuangZhouData }"
        />
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1">
        <div style="flex: 1">
          <div class="block-title">
            <div class="titleText">收到工单总数/处理工单数</div>
            <div class="titleTab">
              <div
                class="titleTab_item"
                v-for="item in rangeList"
                :key="item.value"
                :class="{ active: curRange1 == item.value }"
                @click="handleRange(0, item.value)"
              >
                {{ item.label }}
              </div>
            </div>
          </div>
          <div class="flex" style="gap: 0">
            <Echarts
              style="flex: 1"
              id="lineData3"
              width="100%"
              height="10.7vw"
              :fullOptions="lineOption3"
            />
            <div class="block block-right" style="flex: 0.8">
              <div
                class="item-opt"
                v-for="(item, index) in optList2"
                :key="index"
                style="font-size: 0.8vw"
              >
                {{ item.title }}
                <div class="item-opt_row">
                  <span style="font-size: 0.65vw !important"
                    >处理工单数{{
                      item.num > 0
                        ? "同比增长"
                        : item.num < 0
                        ? "同比下降"
                        : "相同"
                    }}</span
                  >
                  <div
                    style="font-size: 0.7vw !important"
                    :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'"
                  >
                    {{ item.ratio.replace(/-/g, "") }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block2">
        <div class="block-title">
          <div class="titleText">工单平均处理时间</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in rangeList"
              :key="item.value"
              :class="{ active: curRange2 == item.value }"
              @click="handleRange(1, item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="flex" style="gap: 0">
          <Echarts
            style="flex: 1"
            id="lineData4"
            width="100%"
            height="10.7vw"
            :fullOptions="lineOption4"
          />
          <div class="block block-right block-right2" style="flex: 0.8">
            <div
              class="item-opt item-opt2"
              v-for="(item, index) in optList4"
              :key="index"
            >
              <div class="center top" v-if="index == 0">
                <div v-if="item.increase == 0">与昨日相同</div>
                <div v-if="item.increase > 0">
                  与昨日相比增加<span>{{ item.increase }}</span
                  >分钟
                </div>
                <div v-if="item.increase < 0">
                  与昨日相比减少<span>{{ Math.abs(item.increase) }}</span
                  >分钟
                </div>
              </div>
              <div class="center top" v-if="index == 1">
                平均
                <span>{{ item.fq.replace("天/次", "") }}</span>
                天处理一次工单
              </div>
              <div class="bottom">
                <div
                  v-if="index == 0"
                  style="
                    display: flex;
                    width: 100%;
                    justify-content: space-between;
                  "
                >
                  {{ item.title }}
                  <div>
                    <span class="minute">{{ item.total }}</span
                    >分钟
                  </div>
                </div>
                <div v-if="index == 1">{{ item.title }}</div>
                <div class="item-opt_row">
                  <div
                    v-if="index == 0"
                    style="
                      display: flex;
                      width: 100%;
                      justify-content: space-between;
                      padding-left: 0;
                    "
                  >
                    {{
                      item.num == 0
                        ? "与昨日相同"
                        : item.num > 0
                        ? `对比昨日同比增长`
                        : "对比昨日同比下降"
                    }}
                    <div
                      v-if="index == 0"
                      :class="
                        item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      "
                    >
                      <span v-if="index == 0" style="font-size: 0.6vw">{{
                        item.ratio.replace(/-/g, "")
                      }}</span>
                    </div>
                  </div>
                  <div
                    v-if="index == 1"
                    style="width: 100%; text-align: center; padding-left: 0"
                  >
                    <span class="minute">{{ item.total }}</span
                    >分钟
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block3">
        <div class="block-title">
          <div class="titleText">工单峰值处理时间</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in rangeList2"
              :key="item.value"
              :class="{ active: curRange3 == item.value }"
              @click="handleRange(2, item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="flex" style="gap: 0">
          <Echarts
            style="flex: 1"
            id="lineData5"
            width="100%"
            height="10.7vw"
            :fullOptions="lineOption5"
          />
          <div class="block block-right block-right2" style="flex: 0.8">
            <div
              class="item-opt item-opt2"
              v-for="(item, index) in optList1"
              :key="index"
            >
              <div class="center top" v-if="index == 0">
                <div v-if="item.increase == 0">与昨日相同</div>
                <div v-if="item.increase > 0">
                  与昨日相比增加<span>{{ item.increase }}</span
                  >分钟
                </div>
                <div v-if="item.increase < 0">
                  与昨日相比减少<span>{{ Math.abs(item.increase) }}</span
                  >分钟
                </div>
              </div>
              <div class="center top" v-if="index == 1"></div>
              <div class="bottom">
                <div
                  v-if="index == 0"
                  style="
                    display: flex;
                    width: 100%;
                    justify-content: space-between;
                  "
                >
                  {{ item.title }}
                  <div>
                    <span class="minute">{{ item.total }}</span
                    >分钟
                  </div>
                </div>
                <div v-if="index == 1">{{ item.title }}</div>
                <div class="item-opt_row">
                  <div
                    v-if="index == 0"
                    style="
                      display: flex;
                      width: 100%;
                      justify-content: space-between;
                      padding-left: 0;
                    "
                  >
                    {{
                      item.num == 0
                        ? "与昨日相同"
                        : item.num > 0
                        ? `对比昨日同比增长`
                        : "对比昨日同比下降"
                    }}
                    <div
                      v-if="index == 0"
                      :class="
                        item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      "
                    >
                      <span v-if="index == 0" style="font-size: 0.6vw">{{
                        item.ratio.replace(/-/g, "")
                      }}</span>
                    </div>
                  </div>
                  <div
                    v-if="index == 1"
                    style="width: 100%; text-align: center; padding-left: 0"
                  >
                    <span class="minute">{{ item.total }}</span
                    >分钟
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      title="查看详情"
      v-model="dialogVisible"
      align-center
      width="500"
    >
      <el-descriptions title="" border :column="1">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门名称"
        >
          {{ deptInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门人数"
        >
          {{ deptInfo.peopleNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="资产总数"
        >
          {{ deptInfo.assetsNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门负责人"
        >
          {{ deptInfo.leaderName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getAdminSchoolStatistics,
  getAdminWorkOrderStatistics,
  getAdminAssetsStatistics,
  getAdminMaintainCountStatistics,
  getAdminWorkOrderTimeStatistics,
  getAdminWorkOrderTime,
  getAdminWorkOrderTimePeak,
  getAdminCorpGps,
} from "@/api/unify";
import getGuangZhouData from "@/api/gz.json";
import { fitChartSize } from "@/utils";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  pieDataRef: null,
  rangeList: [
    { label: "按周", value: 1 },
    { label: "按月", value: 2 },
    { label: "按年", value: 3 },
  ],
  rangeList2: [
    { label: "按月", value: 2 },
    { label: "按年", value: 3 },
  ],
  curRange1: 1,
  curRange2: 1,
  curRange3: 2,
  networkList: [
    { speed: "846kb/s", maxSpeed: "1846kb/s" },
    { speed: "52Mb/s", maxSpeed: "152Mb/s" },
  ],
  curAssetsTab: 0,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  pieDataRef,
  rangeList2,
  rangeList,
  curRange1,
  curRange2,
  curRange3,
  networkList,
  curAssetsTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  curTime,
  timer,
  timer2,
} = toRefs(state);

const summaryLeftList = ref([
  { name: "资产总数", num: "3074", unit: "个" },
  { name: "开机率", num: "28.57", unit: "%" },
  { name: "报废数", num: "14", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "165", unit: "人" },
  { name: "今日执勤", num: "128", unit: "人" },
  { name: "今日轮休", num: "37", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "合计工单最长处理时间", tip: "", total: 0, ratio: "0%", num: 0 },
  {
    title: "合计工单最短处理时间",
    tip: "个数同比",
    total: 0,
    ratio: "0",
    num: 0,
  },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "处理工单数同比",
    total: 0,
    ratio: "-100%",
    num: -100,
  },
  {
    title: "本年度对比上年度",
    tip: "处理工单数同比",
    total: 5,
    ratio: "0%",
    num: 0,
  },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  {
    title: "今日工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    increase: 15,
  },
  {
    title: "合计工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    fq: "2.45",
  },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const optList6 = ref([
  { name: "数据信息资产", number: "01" },
  { name: "网络资产", number: "02" },
  { name: "硬件资产", number: "03" },
  { name: "知识产权资产", number: "04" },
  { name: "软件资产", number: "05" },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {
    coverage: "55.56%",
  },
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//广州市地图配置
const mapFontSize = Math.max(10, (window.innerWidth / 1920) * 12);
const mapFontSizeEmphasis = Math.max(10, (window.innerWidth / 1920) * 14);
const mapBgWidth = Math.max(10, (window.innerWidth / 1920) * 90);
const mapBgHeight = Math.max(10, (window.innerWidth / 1920) * 55);
const mapBgPadding = [
  -Math.max(10, (window.innerWidth / 1920) * 10),
  Math.max(10, (window.innerWidth / 1920) * 20),
  0,
  -Math.max(10, (window.innerWidth / 1920) * 65),
];
const mapBgSymbolSize = [
  Math.max(10, (window.innerWidth / 1920) * 130),
  Math.max(10, (window.innerWidth / 1920) * 50),
];
const mapBgOffset = [
  Math.min(10, (window.innerWidth / 1920) * 2),
  -Math.min(10, (window.innerWidth / 1920) * 4),
];
const mapOption = ref({
  options: {
    tooltip: {
      show: true,
    },
    geo: {
      show: true,
      map: "GZ",
      zoom: 1.2,
      aspectScale: 1,
      tooltip: {
        show: false,
        trigger: "item",
      },
      label: {
        show: true,
        color: "#fff",
        fontSize: "60%",
        formatter: (params) => {
          return `{bg|}{name|${params.name}}`;
        },
        // 定义富文本样式
        rich: {
          bg: {
            // 关键：通过 backgroundColor 的 image 属性设置背景图
            backgroundColor: {
              image:
                "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/city-bg.png", // 背景图 URL
            },
            width: mapBgWidth, // 背景图宽度
            height: mapBgHeight, // 背景图高度
          },
          name: {
            fontSize: mapFontSize,
            padding: mapBgPadding, // 调整文字位置
          },
        },
      },
      itemStyle: {
        normal: {
          opacity: 0, //图形透明度 0 - 1
          borderColor: "yellow",
          // borderColor: "#00c6e8", //图形的描边颜色
          borderWidth: 1, //描边线宽。为 0 时无描边。
          borderType: "solid", //柱条的描边类型，默认为实线，支持 'solid', 'dashed', 'dotted'。
          areaColor: "transparent",
          // areaColor: "rgba(4, 46, 101, .2)", //图形的颜色 #eee
        },
      },
      emphasis: {
        label: {
          color: "#fff",
        },
        itemStyle: {
          opacity: 0,
        },
      },
    },
    series: [
      {
        tooltip: {
          show: true,
          trigger: "item",
          backgroundColor: "transparent",
          borderColor: "transparent",
          formatter: function (params) {
            // console.log(params);
            let { name, detail } = params.data;
            return `
              <div class="tooltip-bg">
                <div class="tooltip-title">${
                  name.length > 11
                    ? name.substring(0, 5) +
                      "..." +
                      name.substring(name.length - 5, name.length)
                    : name
                }</div>
                <div class="tooltip-row row1">
                  <div class="tooltip-label">运维人员：</div>
                  <div class="tooltip-value">${detail.ywry}人</div>
                </div>
                <div class="tooltip-row row2">
                  <div class="tooltip-label">现存工单量：</div>
                  <div class="tooltip-value">${detail.xcgdl}单</div>
                </div>
                <div class="tooltip-row row3">
                  <div class="tooltip-label">平均处理时间：</div>
                  <div class="tooltip-value">${getDay_Hour_Minute(
                    detail.pjclsj
                  )}</div>
                </div>
              </div>
            `;
          }, // 可选：自定义提示内容
        },
        name: "校区标记",
        map: "GZ",
        type: "scatter",
        coordinateSystem: "geo",
        symbolSize: mapBgSymbolSize,
        symbol: `image://https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/school-bg.png`,
        label: {
          show: true,
          color: "#fff",
          fontSize: mapFontSize,
          offset: mapBgOffset,
          formatter: function (params) {
            let { name } = params.data;
            return `${
              name.length > 6
                ? name.substring(0, 3) +
                  "..." +
                  name.substring(name.length - 3, name.length)
                : name
            }`;
          },
        },
        emphasis: {
          label: {
            fontSize: mapFontSizeEmphasis,
          },
        },
        data: [
          {
            name: "云天数据应用端",
            detail: {
              ywry: 1,
              xcgdl: 10,
              pjclsj: 4200,
            },
            value: [113.31954, 23.180377],
          },
        ],
      },
    ],
  },
});

//资产类别占比柱状图
const barData1 = ref([100, 30, 40, 50, 60]); // 系列1的数据
const borderHeight1 = 2; // 底部边框的高度（数据单位）
const barColor1 = ref([
  {
    topColor: "rgba(19, 141, 255, 1)",
    bottomColor: "rgba(19, 141, 255, .6)",
    borderColor: "rgba(19, 141, 255, 1)",
  }, // 系列1的颜色
  {
    topColor: "rgba(15, 255, 242, 1)",
    bottomColor: "rgba(33, 231, 251, 0.6)",
    borderColor: "rgba(33, 231, 251, 1)",
  }, // 系列2的颜色
  {
    topColor: "rgba(70, 221, 148, 1)",
    bottomColor: "rgba(70, 221, 148, 0.6)",
    borderColor: "rgba(70, 221, 148, 1)",
  }, // 系列3的颜色
  {
    topColor: "rgba(221, 210, 70, 1)",
    bottomColor: "rgba(221, 210, 70, 0.6)",
    borderColor: "rgba(221, 210, 70, 1)",
  }, // 系列2的颜色
  {
    topColor: "rgba(221, 145, 70, 1)",
    bottomColor: "rgba(221, 145, 70, 0.6)",
    borderColor: "rgba(221, 145, 70, 1)",
  }, // 系列3的颜色
]);
//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${Math.ceil(relVal.value)}`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "10%",
      right: "5%",
      top: "15%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#fff",
        formatter: (params) => {
          return params.length > 4 ? `${params.slice(0, 3)}...` : params;
        },
        rotate: 18,
        margin: fitChartSize(8),
        fontSize: fitChartSize(8.5),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(20),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: ["rgba(217, 231, 255, 0.2)"],
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        type: "bar",
        data: [
          {
            name: "其它",
            value: borderHeight1,
            itemStyle: {
              color: barColor1.value[0].borderColor,
            },
          },
          {
            name: "娱乐类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[1].borderColor },
          },
          {
            name: "办公类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[2].borderColor },
          },
          {
            name: "教学类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[3].borderColor },
          },
          {
            name: "应用类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[4].borderColor },
          },
        ], // 数据固定为边框高度
        barWidth: "50%", // 与主系列宽度一致
        stack: "border1", // 堆叠组名（需唯一）
        z: 1, // 确保边框系列在主系列下方
        tooltip: { show: false }, // 隐藏提示
      },
      {
        data: [
          {
            name: "其它",
            value: barData1.value[0] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[0].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[0].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "娱乐类",
            value: barData1.value[1] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[1].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[1].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "办公类",
            value: barData1.value[2] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[2].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[2].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "教学类",
            value: barData1.value[3] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[3].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[3].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "应用类",
            value: barData1.value[4] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[4].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[4].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
        type: "bar",
        barWidth: "50%",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(196, 196, 196, 0.05)",
        },
        stack: "border1", // 堆叠组名（需唯一）
      },
    ],
  },
});

//应用使用时长TOP5柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "15%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        formatter: "{value} h",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//标签使用频率饼图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//系统版本占比饼图
const pieOption15 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: 10,
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 智能象限定位函数
function quadrantPosition(point, params, dom, rect, size) {
  // 获取tooltip尺寸
  const domWidth = dom.offsetWidth;
  const domHeight = dom.offsetHeight;

  // 计算图表中心点
  const centerX = size.viewSize[0] / 2;
  const centerY = size.viewSize[1] / 2;

  // 设置边距
  const padding = -10;

  // 根据象限返回不同位置
  if (point[0] >= centerX && point[1] <= centerY) {
    // 第一象限（右上）
    return [size.viewSize[0] - domWidth - padding, padding];
  } else if (point[0] < centerX && point[1] <= centerY) {
    // 第二象限（左上）
    return [padding, padding];
  } else if (point[0] < centerX && point[1] > centerY) {
    // 第三象限（左下）
    return [padding, size.viewSize[1] - domHeight - padding];
  } else {
    // 第四象限（右下）
    return [
      size.viewSize[0] - domWidth - padding,
      size.viewSize[1] - domHeight - padding,
    ];
  }
}
//资产类别占比饼图
const pieColor1 = ref([
  {
    topColor: "#00aaff",
    bottomColor: "#0252ef",
    borderColor: "#00AAFF",
  }, // 系列1的颜色
  {
    topColor: "#01e9ff",
    bottomColor: "#00aafe",
    borderColor: "#00EAFF",
  }, // 系列1的颜色
  {
    topColor: "#55cb69",
    bottomColor: "#79ed8d",
    borderColor: "#00CC03",
  }, // 系列1的颜色
  {
    topColor: "#fe4c01",
    bottomColor: "#ff5b00",
    borderColor: "#FF5501",
  }, // 系列1的颜色
  {
    topColor: "#ff9e1e",
    bottomColor: "#fef702",
    borderColor: "#FFFF00",
  }, // 系列1的颜色
]);
const pieOption = ref({
  options: {
    tooltip: {
      trigger: "item",
      backgroundColor: "transparent",
      borderColor: "transparent",
      position: quadrantPosition,
      formatter: function (params) {
        let { name, value } = params.data;
        return `
              <div class="tooltip-bg2">
                <div class="tooltip-title2">${
                  name.length > 11
                    ? name.substring(0, 5) +
                      "..." +
                      name.substring(name.length - 5, name.length)
                    : name
                }</div>
                <div class="tooltip-row2">${value}
                </div>
              </div>
            `;
      }, // 可选：自定义提示内容
    },
    series: [
      {
        type: "pie",
        radius: ["50%", "55%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: (params) => {
            let name = params.name && params.name.match(/.{1,6}/g).join("\n");
            return `{d|${params.data.proportion}}\n{b|${name}}`;
          },
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(18),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(13),
              lineHeight: fitChartSize(13),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "类别1",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "类别2",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "类别3",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "类别4",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "类别5",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const pieOption2 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "操作系统", value: 51 },
          { name: "教学软件", value: 23 },
          { name: "其他软件", value: 9 },
        ],
      },
    ],
  },
});

// 软件到期时间饼图（假数据）
const pieOption3 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "5年以上", value: 34 },
          { name: "3-5年", value: 63 },
          { name: "1-3年", value: 72 },
          { name: "1年以内", value: 46 },
        ],
      },
    ],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "60%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 1 },
          { name: "教育平台", value: 3 },
          { name: "办公平台", value: 2 },
        ],
      },
    ],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieOption8 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产端口类型占比饼图
const pieOption9 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieOption12 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内故障资产使用年限占比饼图
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      bottom: "25%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
        },
        lineStyle: {
          color: "#009bab",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      bottom: "25%",
      right: "5%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(15),
      itemWidth: fitChartSize(15),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "收到工单总量", itemStyle: { color: "#0783FA" } },
        { name: "处理工单量", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange1 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "15%",
      height: "65%",
      right: "4.5%",
    },
    series: [
      {
        name: "收到工单总量",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(19, 141, 255, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(19, 141, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#0783FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#0783FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "处理工单量",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//工单平均处理时间
const lineOption4 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange2 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(40),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "17%",
      height: "65%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//工单峰值处理时间
const lineOption5 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(15),
      itemWidth: fitChartSize(15),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "最长处理时间峰值", itemStyle: { color: "#F29339" } },
        { name: "最短处理时间峰值", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange3 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "15%",
      height: "65%",
      right: "4.5%",
    },
    series: [
      {
        name: "最长处理时间峰值",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(242, 147, 57, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(242, 147, 57, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#F29339",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#F29339",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "最短处理时间峰值",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getWorkOrderLine1();
  }
  if (type == 1) {
    state.curRange2 = tab;
    getWorkOrderLine2();
  }
  if (type == 2) {
    state.curRange3 = tab;
    getWorkOrderLine3();
  }
}

function handleCheck(item) {
  state.deptInfo = {
    ...item,
  };
  state.dialogVisible = true;
}

objData.value.corpAssets = [
  {
    corpName: "广州市天河区第一实验小学",
    gpsX: 113.348337,
    gpsY: 23.129589,
    workOrderTotal: 39,
    total: 159,
    maintenanceTotal: 9,
    totalAverageProcessingTime: 12958.77,
  },
  {
    corpName: "广州市实验外语学校（小学部）",
    gpsX: 113.26171,
    gpsY: 23.247371,
    workOrderTotal: 46,
    total: 117,
    maintenanceTotal: 7,
    totalAverageProcessingTime: 24737.79,
  },
  {
    corpName: "广东外语外贸大学实验中学",
    gpsX: 113.363154,
    gpsY: 23.301951,
    workOrderTotal: 63,
    total: 174,
    maintenanceTotal: 11,
    totalAverageProcessingTime: 30195.83,
  },
  {
    corpName: "天河区御景小学",
    gpsX: 113.383368,
    gpsY: 23.175122,
    workOrderTotal: 78,
    total: 106,
    maintenanceTotal: 12,
    totalAverageProcessingTime: 38336.85,
  },
  {
    corpName: "海珠小学",
    gpsX: 113.345811,
    gpsY: 23.093671,
    workOrderTotal: 74,
    total: 182,
    maintenanceTotal: 11,
    totalAverageProcessingTime: 34581.73,
  },
  {
    corpName: "广州市海珠区实验小学",
    gpsX: 113.284902,
    gpsY: 23.100963,
    workOrderTotal: 59,
    total: 194,
    maintenanceTotal: 12,
    totalAverageProcessingTime: 28490.71,
  },
  {
    corpName: "广州高新区第一小学",
    gpsX: 113.495927,
    gpsY: 23.193623,
    workOrderTotal: 38,
    total: 139,
    maintenanceTotal: 9,
    totalAverageProcessingTime: 19362.54,
  },
  {
    corpName: "黄埔区萝峰小学",
    gpsX: 113.519948,
    gpsY: 23.184295,
    workOrderTotal: 35,
    total: 129,
    maintenanceTotal: 12,
    totalAverageProcessingTime: 18429.68,
  },
  {
    corpName: "华师附中番禺小学",
    gpsX: 113.369485,
    gpsY: 23.030568,
    workOrderTotal: 75,
    total: 141,
    maintenanceTotal: 11,
    totalAverageProcessingTime: 36948.66,
  },
  {
    corpName: "番禺毓秀小学",
    gpsX: 113.325003,
    gpsY: 22.966873,
    workOrderTotal: 70,
    total: 134,
    maintenanceTotal: 8,
    totalAverageProcessingTime: 32500.64,
  },
  {
    corpName: "广州市荔湾区汇龙小学",
    gpsX: 113.241166,
    gpsY: 23.139255,
    workOrderTotal: 53,
    total: 146,
    maintenanceTotal: 9,
    totalAverageProcessingTime: 24116.62,
  },
  {
    corpName: "芳村小学",
    gpsX: 113.24526,
    gpsY: 23.090921,
    workOrderTotal: 52,
    total: 153,
    maintenanceTotal: 7,
    totalAverageProcessingTime: 24526.51,
  },
  {
    corpName: "南沙小学（珠江湾校区）",
    gpsX: 113.587463,
    gpsY: 22.746424,
    workOrderTotal: 117,
    total: 135,
    maintenanceTotal: 8,
    totalAverageProcessingTime: 58746.61,
  },
  {
    corpName: "南沙高新小学",
    gpsX: 113.423635,
    gpsY: 22.811211,
    workOrderTotal: 99,
    total: 185,
    maintenanceTotal: 10,
    totalAverageProcessingTime: 42363.46,
  },
  {
    corpName: "广州市从化区流溪小学",
    gpsX: 113.5888876,
    gpsY: 23.552665,
    workOrderTotal: 112,
    total: 189,
    maintenanceTotal: 8,
    totalAverageProcessingTime: 55266.47,
  },
  {
    corpName: "广州市从化区西宁小学",
    gpsX: 113.555098,
    gpsY: 23.58149,
    workOrderTotal: 108,
    total: 167,
    maintenanceTotal: 7,
    totalAverageProcessingTime: 55149.53,
  },
  {
    corpName: "广州大学附属中学花都附属小学",
    gpsX: 113.246141,
    gpsY: 23.427947,
    workOrderTotal: 48,
    total: 190,
    maintenanceTotal: 9,
    totalAverageProcessingTime: 24614.36,
  },
  {
    corpName: "广州市花都区新华第七小学",
    gpsX: 113.228086,
    gpsY: 23.394361,
    workOrderTotal: 43,
    total: 114,
    maintenanceTotal: 8,
    totalAverageProcessingTime: 22808.42,
  },
  {
    corpName: "广州市增城区实验小学",
    gpsX: 113.812648,
    gpsY: 23.250421,
    workOrderTotal: 47,
    total: 156,
    maintenanceTotal: 9,
    totalAverageProcessingTime: 25042.25,
  },
  {
    corpName: "广州市增城区挂绿小学",
    gpsX: 113.836811,
    gpsY: 23.314937,
    workOrderTotal: 51,
    total: 164,
    maintenanceTotal: 11,
    totalAverageProcessingTime: 31493.49,
  },
];
// 中间广州市地图
mapOption.value.options.series[0].data = objData.value.corpAssets.map(
  (item) => {
    return {
      name: item.corpName,
      detail: {
        ywry: item.maintenanceTotal || 0,
        xcgdl: item.workOrderTotal || 0,
        pjclsj: item.totalAverageProcessingTime || 0,
      },
      value: [item.gpsX, item.gpsY],
    };
  }
);
function getCorpInfo() {
  getAdminCorpGps().then((res) => {
    console.log(res, "机构位置信息");
    if (res.data) {
      mapOption.value.options.series[0].data = res.data.map((item) => {
        let gps = item.gps
          ? JSON.parse(item.gps)
          : { gpsX: 113.298, gpsY: 23.17 };
        return {
          name: item.corpName,
          detail: {
            ywry: item.maintenanceTotal || 0,
            xcgdl: item.workOrderTotal || 0,
            pjclsj: item.totalAverageProcessingTime
              ? item.totalAverageProcessingTime.replace("分钟", "") * 1
              : 0,
          },
          value: [gps.gpsX, gps.gpsY],
        };
      });
    }
  });
}

//获取数据
function getData() {
  getCorpInfo();
  getAdminSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        maintenanceTotal,
        currentRunTotal,
        assetsTotal,
        restTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
      } = res.data;
      mapOption.value.options.series[0].data[0].detail.yjzc = deviceTotal;
      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;

      objData.value = {
        ...objData.value,
        sparepartsUseStatistics: res.data.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      lineOption2.value.options.xAxis.data = [
        "2024-10",
        "2024-11",
        "2024-12",
        "2025-01",
        "2025-02",
        "2025-03",
      ];
      lineOption2.value.options.series[0].data = [0, 0, 0, 0, 5, 0];

      if (deviceAppUserStatistics?.appUseTime) {
        // 软件使用时长柱状图赋值
        barOption2.value.options.xAxis.data =
          deviceAppUserStatistics.appUseTime.map((item) => item.month);
        barOption2.value.options.series[0].data =
          deviceAppUserStatistics.appUseTime.map((item) => item.timeTotal);
      }

      if (deviceAppUserStatistics?.appUseType) {
        // 软件使用占比饼图赋值
        pieOption4.value.options.series[0].data =
          deviceAppUserStatistics.appUseType.map((item) => {
            return {
              name: item.appType,
              value: item.num,
            };
          });
      }

      if (assetsTypeStatistics) {
        let arr = [],
          data1 = [],
          data2 = [];
        assetsTypeStatistics?.slice(0, 5).map((item, index) => {
          arr.push(item.assetsTypeName);
          data1.push(item.num);
          data2.push(item.proportion);
        });
        barOption.value.options.xAxis.data = arr;
        barData1.value = data1;
        const yMax = Math.max(...data1) * 1.2;
        const newHeight = yMax * borderHeight1 * 0.01;
        barOption.value.options.series[0].data = barData1.value.map(
          (val, index) => ({
            value: val > newHeight ? newHeight : 0,
            itemStyle: {
              color:
                barColor1.value[index % barColor1.value.length].borderColor,
            },
          })
        );
        barOption.value.options.series[1].data = barData1.value.map(
          (val, index) => ({
            name: arr[index],
            value: val > newHeight ? val - newHeight : val,
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color:
                      barColor1.value[index % barColor1.value.length].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color:
                      barColor1.value[index % barColor1.value.length]
                        .bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          })
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = barData1.value.map(
          (val, index) => ({
            name: arr[index],
            value: val,
            proportion: data2[index],
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color:
                      pieColor1.value[index % pieColor1.value.length].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color:
                      pieColor1.value[index % pieColor1.value.length]
                        .bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              bgColor:
                pieColor1.value[index % pieColor1.value.length].borderColor,
            },
          })
        );
      }
      console.log(pieOption.value, barOption.value, "资产类别111");

      if (knowledgeBaseStatistics) {
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
          (item) => item.num
        );
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAdminAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsFaultBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
    } = res.data;

    // 标签使用频率
    if (assetsTagUseProportion) {
      const arr = ["小学部", "初中部", "高中部", "易消耗品", "维护过的物品"];

      // 资产类别占比柱状图赋值
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map(
          (item, index) => arr[index] || item.assetsTagName
        ) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];
    }

    // console.log(barOption3.value, "barOption3.value");

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      pieOption15.value.options.series[0].data =
        assetsSystemVersionProportion?.map((item, index) => {
          return {
            name: item.assetsSystemVersionName,
            value: item.num,
          };
        });
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data =
        deviceTroubleProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data =
        hardwareAssetsUseProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      pieOption8.value.options.series[0].data =
        assetsTypeProportion?.map((item) => {
          return {
            name: item.assetsTypeName,
            value: item.num,
          };
        }) || [];
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      pieOption9.value.options.series[0].data =
        assetsPortProportion?.map((item) => {
          return {
            name: item.assetsPortName,
            value: item.num,
          };
        }) || [];
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data =
        assetsHardDiskProportion?.map((item) => {
          return {
            name: item.assetsHardDiskName,
            value: item.num,
          };
        }) || [];
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data =
        assetsMemoryProportion?.map((item) => {
          return {
            name: item.assetsMemoryName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产品牌占比
    if (assetsFaultBrandProportion) {
      pieOption12.value.options.series[0].data =
        assetsFaultBrandProportion?.map((item) => {
          return {
            name: item.assetsFaultBrandName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产使用年限占比
    if (assetsFaultUseYearProportion) {
      pieOption13.value.options.series[0].data =
        assetsFaultUseYearProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }
  });
}

function getWorkOrderLine1() {
  getAdminWorkOrderTimeStatistics({ range: state.curRange1 }).then((res) => {
    console.log(res, "收到工单/处理工单数");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      let data1 = [],
        data2 = [];
      lineOption3.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption3.value.options.series[0].data = data1 =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption3.value.options.series[1].data = data2 =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
      const yMax = Math.max(...[...data1, ...data2]) * 1.1;
      lineOption3.value.options.series[2].data = [yMax];
    }
  });
}

function getWorkOrderLine2() {
  getAdminWorkOrderTime({ range: state.curRange2 }).then((res) => {
    console.log(res, "工单平均处理时间");
    let { workOrderTimeStatistics } = res.data;
    if (workOrderTimeStatistics) {
      let data = [];
      lineOption4.value.options.xAxis.data =
        workOrderTimeStatistics?.map((item) => item.statisticsTime) || [];
      lineOption4.value.options.series[0].data = data =
        workOrderTimeStatistics?.map((item) => item.avgProcessingTime) || [];
      const yMax = Math.max(...data) * 1.1;
      lineOption4.value.options.series[1].data = [yMax];
    }
  });
}

function getWorkOrderLine3() {
  getAdminWorkOrderTimePeak({ range: state.curRange3 }).then((res) => {
    console.log(res, "处理时间峰值");
    let { workOrderPeakTimeStatistics } = res.data;
    if (workOrderPeakTimeStatistics) {
      let data1 = [],
        data2 = [];
      lineOption5.value.options.xAxis.data =
        workOrderPeakTimeStatistics?.map((item) => item.statisticsTime) || [];
      lineOption5.value.options.series[0].data = data1 =
        workOrderPeakTimeStatistics?.map((item) => item.maxProcessingTime) ||
        [];
      lineOption5.value.options.series[1].data = data2 =
        workOrderPeakTimeStatistics?.map((item) => item.minProcessingTime) ||
        [];
      const yMax = Math.max(...[...data1, ...data2]) * 1.1;
      lineOption5.value.options.series[2].data = [yMax];
    }
  });
}

// 根据分钟获取 天、小时、分钟 的对象
function getDay_Hour_Minute(minute) {
  minute = Math.abs(minute);
  let obj = { day: 0, hour: 0, minute: 0 };
  if (minute < 24 * 60) {
    obj.day = 0;
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  } else {
    obj.day = Math.floor(minute / (24 * 60));
    minute = minute % (24 * 60);
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  }
  return `${obj.day || 0}天${obj.hour || 0}小时${
    obj.minute.toFixed(2) || 0
  }分钟`;
}

function getOrderData() {
  getWorkOrderLine1();
  getWorkOrderLine2();
  getWorkOrderLine3();
  getAdminWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList2.value[0].total = processingWorkOrderMonth;
      optList2.value[0].ratio = processingWorkOrderMonthRise;
      optList2.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList2.value[1].total = processingWorkOrderYear;
      optList2.value[1].ratio = processingWorkOrderYearRise;
      optList2.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        allMaxProcessingTime,
        allMinProcessingTime,
        yesterdayAllMaxProcessingTime,
        yesterdayTotalAverageProcessingTime,
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
        yesterdayAverageProcessingTime,
      } = workOrderProcrssingTime;

      optList1.value[0].total = Math.abs(allMaxProcessingTime);
      optList1.value[0].increase =
        (allMaxProcessingTime * 1 || 0) -
        (yesterdayAllMaxProcessingTime * 1 || 0);
      optList1.value[0].ratio = yesterdayAllMaxProcessingTime
        ? (
            (((allMaxProcessingTime * 1 || 0) -
              (yesterdayAllMaxProcessingTime * 1 || 0)) /
              (yesterdayAllMaxProcessingTime * 1 || 0)) *
            100
          ).toFixed(2) + "%"
        : "0%";
      optList1.value[0].num = yesterdayAllMaxProcessingTime
        ? ((allMaxProcessingTime * 1 || 0) -
            (yesterdayAllMaxProcessingTime * 1 || 0)) /
          (yesterdayAllMaxProcessingTime * 1 || 0)
        : 0;

      optList1.value[1].total = Math.abs(allMinProcessingTime);

      optList4.value[0].total = optList4.value[1].total =
        Math.floor(todayAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "";
      optList4.value[0].ratio = riseOfProcessingTime || "0.00%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;
      optList4.value[0].increase = Math.floor(
        (todayAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
          (yesterdayAverageProcessingTime?.replace("分钟", "") * 1 || 0)
      );

      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "";
      optList4.value[1].fq = averageTime;
      optList4.value[1].ratio =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (
              (((totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) /
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) *
              100
            ).toFixed(2) + "%"
          : "0.00%";
      optList4.value[1].num =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (
              (totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
              (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                0)
            ).toFixed(2)
          : 0;
    }
  });
}

const initPieFunc = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: 0, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: 0,
        dataIndex: index,
      });
    });
  });
};

onMounted(() => {
  proxy.$modal.loading();
  getData();
  getOrderData();
  getAssetsData();
  initPieFunc("pieData");
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>

<style lang="scss" scoped>
:deep(.tooltip-bg) {
  position: absolute;
  top: -2.5vw;
  left: 0;
  background-color: transparent;
  width: 16vw;
  height: 10vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-bg3.png");
  background-size: 100% 100%;
}
:deep(.tooltip-title) {
  // border: 1px solid red;
  // padding-left: 3vw;
  font-size: 0.8vw;
  font-weight: bold;
  height: 2.2vw;
  line-height: 2.2vw;
  display: flex;
  align-items: center;
  gap: 0 0.5vw;
  color: #fff;
  &::before {
    content: "";
    width: 2vw;
    height: 2vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-icon.png");
    background-size: 100% 100%;
  }
}
:deep(.tooltip-label) {
  width: 5.3vw;
}
:deep(.tooltip-value) {
  padding-left: 0.5vw;
  width: 9.2vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/value-bg.png");
  background-size: 100% 100%;
}
:deep(.tooltip-row) {
  // border: 1px solid red;
  font-size: 0.8vw;
  display: flex;
  align-items: center;
  gap: 0 0.3vw;
  padding-left: 1vw;
  height: 2.2vw;
  margin-top: 0.3vw;
  line-height: 2.2vw;
  color: #fff;
}
:deep(.tooltip-bg2) {
  background-color: transparent;
  padding: 0.2vw;
  width: 5.5vw;
  height: 2.5vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/assets-type_frame.png");
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
:deep(.tooltip-title2) {
  font-size: 0.5vw;
  line-height: 0.6vw;
  width: 5vw;
  white-space: wrap;
  color: #fff;
}
:deep(.tooltip-row2) {
  font-size: 0.8vw;
  font-weight: bold;
  line-height: 1;
  color: #0bf9fe;
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #b0c0e6;
  padding: 0.2vw 0vw 0.2vw 0.5vw;
  font-size: 0.65vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-top: 0.3vw;
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.4vw;
      color: #4095e5;
      &_item {
        border: 1px solid rgba(255, 255, 255, 0);
        padding: 0.05vw 0.3vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/range-bg.png");
          background-size: 100% 100%;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }
    &.range2 {
      position: absolute;
      // border: 1px solid red;
      right: 2%;
      z-index: 11;
      .range-tabs_item {
        padding: 0.1vw 0.5vw;
      }
    }
  }
}
.unify-main_charts {
  margin-top: -1.5vw;
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  // height: 51vw;
  overflow: hidden;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gz-bg.png");
  background-size: 100% 100%;

  .col {
    .flex {
      display: flex;
      gap: 0 0.5vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;

      &-title {
        font-size: 0.8vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1.5vw;
        margin-top: 1vw;
        color: #fff;
        position: relative;
        font-family: "HYYaKuHeiW";
        .titleText {
          transform: scale(1, 0.993905) skew(-6.290719deg, 0deg);
          background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
            #ffffff;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent;
        }
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg.png");
        background-size: 100% 100%;
        .titleTab {
          position: absolute;
          right: 0.5vw;
          top: 0.3vw;
          display: flex;
          font-size: 0.65vw;
          gap: 0 0.5vw;
          color: #b4c0cc;
          z-index: 11;
          &_item {
            cursor: pointer;
            padding: 0vw 0.5vw;
            background-color: #3a4356;
            height: 1.3vw;
            line-height: 1.3vw;
            &.active {
              color: #1fc6ff;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
              background-size: 100% 100%;
            }
          }
        }
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.3vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          padding-right: 0.2vw;
          color: #c7d9f9;
        }

        div {
          padding-left: 0.8vw;
        }

        .up {
          // color: #ff2200;
          color: #2dcd5e;
          position: relative;
          span {
            color: #2dcd5e !important;
          }
          &::before {
            content: "";
            position: absolute;
            top: 0.1vw;
            left: -0.2vw;
            width: 1vw;
            height: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          // color: #fe005d;
          color: #3be27e !important;
          position: relative;
          span {
            color: #3be27e !important;
          }
          &::before {
            content: "";
            position: absolute;
            top: 0.1vw;
            left: -0.2vw;
            width: 1vw;
            height: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 28vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;
      padding: 1vw 0 0;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        height: 7vw;
        width: 100%;
        text-align: center;
        line-height: 6vw;
        font-size: 1.9vw;
        color: #fff;
        font-family: "ZhengQingKeHuangYouTi";
      }
    }

    &-block3 {
      .table {
        min-height: 14vw;
        font-size: 0.75vw;
        padding: 0.2vw 0vw 0.5vw;
        // margin-top: 0.5vw;
        background: radial-gradient(
            150.78% 100% at 50% 100%,
            #d6f0ff33 0%,
            #85e2ff1a 20.31%,
            #3b80c00f 41.67%,
            #032f7215 62.5%,
            #0210410c 80.73%,
            #00000000 100%
          ),
          linear-gradient(180deg, #050e2e4d 0%, #0c1a4b4d 100%);
        border-bottom: 1px solid #0091ff80;

        .opt-title {
          margin-bottom: 0.5vw;
        }

        &-row {
          display: flex;
          padding: 0.4vw 0;
          text-align: center;
          justify-content: space-between;
          margin-top: 0.4vw;
          gap: 0 0.5vw;
          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 7vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0 1vw;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }

    &-block4 {
      .subtitle {
        font-size: 0.7vw;
        height: 1.5vw;
        line-height: 1.5vw;
        padding-left: 2.5vw;
        margin-top: 1vw;
        color: #fff;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
        background-size: 100% 100%;
        text-shadow: 0px 0px 4px #0091ff;
      }
      .pieData {
        &-bg {
          position: absolute;
          top: 1.75vw;
          left: 4vw;
          width: 5.5vw;
          height: 5.5vw;
        }
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 41vw;
    height: 51vw;
    padding: 0.5vw 0vw 0;
    div {
      margin: 0 auto;
      width: 40vw;
      height: 50vw;
      // border: 1px solid red;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gzmap-bg2.png");
      background-size: 100% 100%;
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 28vw;
    height: 50vw;
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: 2vw;
    // height: 100%;

    .subtitle {
      font-size: 0.6vw;
      padding-top: 0.3vw;
    }

    .block {
      // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
      // background-size: 100% 100%;
      &-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        padding-top: 1.8vw;
        .item-opt {
          text-align: center;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg.png");
          background-size: 100% 100%;
          padding: 0.5vw;
          color: #fff;

          &_row {
            margin: 0;
            padding: 0;
            padding-top: 0.5vw;
            span {
              color: #fff;
            }
          }
          .minute {
            color: #0bf9fe;
          }
        }
      }
      &-right2 {
        position: relative;
        padding-top: 0vw;
        gap: 0vw;
        .item-opt {
          background: none;
          color: #fff;
          padding: 0.4vw;
          .top {
            white-space: nowrap;
            font-size: 0.7vw;
            padding: 0.4vw 0.5vw;
            min-height: 1.5vw;
            color: #fff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-title_bg.png");
            background-size: 100% 100%;
            span {
              color: #0bf9fe;
              font-size: 0.8vw;
              padding: 0 0.1vw;
            }
          }
          .bottom {
            padding: 0.2vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg2.png");
            background-size: 100% 100%;
          }
        }
      }
    }

    &-block2 {
      .item-opt {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/trend-bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0.5vw;
        &_row {
          margin: 0;
          padding: 0;
        }
      }
      .center {
        margin: 0.2vw auto;
        color: #4095e5;
        text-align: left;
      }
    }

    &-block3 {
      .opt2 {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/trend-bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0.5vw;
        &_row {
          margin: 0;
          padding: 0;
        }
      }
      .center {
        margin: 0.2vw auto;
        color: #4095e5;
        text-align: left;
      }
      .center2 {
        text-align: center;
        .item-opt_row {
          justify-content: center;
        }
      }
    }
  }

  .block-title_text {
    background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
      #ffffffff;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent;
  }
}
</style>
