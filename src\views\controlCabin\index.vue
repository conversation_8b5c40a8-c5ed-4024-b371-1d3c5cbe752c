<template>
    <div class="cabinPage">
        <div class="title">
            {{ userStore.cabinRole === 'cityCabin' ? '市' : userStore.cabinRole === 'districtCabin' ? '区' : '校' }}级教育驾驶舱
            <div class="loginout" @click="loginout">退出</div>
        </div>
        <City v-if="userStore.cabinRole === 'cityCabin'" />
        <District v-else-if="userStore.cabinRole === 'districtCabin'" />
        <School v-else-if="userStore.cabinRole === 'schoolCabin'" />
    </div>
</template>

<script setup name="Index">
import School from '@/views/controlCabin/components/school.vue';
import City from '@/views/controlCabin/components/city.vue';
import District from '@/views/controlCabin/components/district.vue';
import { getCurrentInstance } from 'vue';
import useUserStore from '@/store/modules/user'

const userStore = useUserStore()

const loginout = () => {
    userStore.logOut().then(() => {
        location.href = '/index';
    })
}

</script>

<style lang="scss" scoped>
.cabinPage {
    background-color: #0f0c43;
    color: #fff;
    height: auto;

    .title {
        padding-top: 2.5vh;
        font-size: 2rem;
        width: 100%;
        text-align: center;

        .loginout {
            position: absolute;
            background-color: #f56c6c;
            font-size: 1vw;
            padding: .5vw 1.5vw;
            right: 1.2vw;
            top: 1.5vw;
            cursor: pointer;
            border-radius: .2vw;
        }
    }
}
</style>
