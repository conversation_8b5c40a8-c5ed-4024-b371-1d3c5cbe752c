import request from "@/utils/request";

// 根据手机号判断是否是运维人员和运维管理人员
export function isMaintainCheck(params) {
  return request({
    url: "/system/user/checkAdmin",
    method: "get",
    params,
  });
}

// 获取教职工详细接口
export function getTeacherInfo(params) {
  return request({
    url: "/system/user/getTeacherInfo",
    method: "get",
    params,
  });
}

// 教职工密码修改 /system/user/teacherStaff/change/password
export function changePassword(data) {
  return request({
    url: "/system/user/teacherStaff/change/password",
    method: "post",
    data,
  });
}

// 教职工重置密码 /system/user/teacherStaff/reset/password
export function resetPassword(id) {
  return request({
    url: `/system/user/teacherStaff/reset/password?id=${id}`,
    method: "post",
    // data,
  });
}

// 获取教职工设备权限列表 /system/teacher/getDevicePermissions
export function getDevicePermissions(data) {
  return request({
    url: "/system/teacher/getDevicePermissions",
    method: "post",
    data,
  });
}

// 设备申请列表 /system/schoolDeviceUse/list
export function getDeviceApplyList(params) {
  return request({
    url: "/system/schoolDeviceUse/list",
    method: "get",
    params,
  });
}

// 审核 /system/schoolDeviceUse/audit
export function auditDeviceApply(data) {
  return request({
    url: "/system/schoolDeviceUse/audit",
    method: "put",
    data,
  });
}

// 职工岗位列表 /system/employeesPost/list
export function getEmployeesPostList(params) {
  return request({
    url: "/system/employeesPost/list",
    method: "get",
    params,
  });
}

// 添加职工岗位 /system/employeesPost/add
export function addEmployeesPost(data) {
  return request({
    url: "/system/employeesPost/add",
    method: "post",
    data,
  });
}

// 修改职工岗位 /system/employeesPost/update
export function updateEmployeesPost(data) {
  return request({
    url: "/system/employeesPost/update",
    method: "put",
    data,
  });
}

// 删除职工岗位 /system/employeesPost/delete/1912782358398078978
export function delEmployeesPost(id) {
  return request({
    url: `/system/employeesPost/delete/${id}`,
    method: "post",
  });
}
