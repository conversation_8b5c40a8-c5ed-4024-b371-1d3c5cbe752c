<template>
  <el-dialog
    class="custom-dialog"
    title="修改密码"
    v-model="show"
    width="400"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    align-center
    @close="handleCancel"
  >
    <el-form :model="pwdForm" ref="pwdRef" :rules="pwdRules">
      <el-form-item label="旧密码" prop="oldPassword">
        <el-input
          v-model="pwdForm.oldPassword"
          placeholder="请输入旧密码"
          type="password"
          show-password
          maxlength="32"
          clearable
        />
      </el-form-item>
      <el-form-item label="新密码" prop="newPassword">
        <el-input
          v-model="pwdForm.newPassword"
          placeholder="请输入新密码"
          type="password"
          show-password
          maxlength="20"
          clearable
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="pwdForm.confirmPassword"
          placeholder="请确认新密码"
          type="password"
          show-password
          maxlength="20"
          clearable
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button
          type="primary"
          @click="submitForm"
          :disabled="loading"
          v-throttle
        >
          修改
        </el-button>
        <el-button @click="handleCancel">返回</el-button>
      </div>
    </template>
  </el-dialog>
</template>
<script setup>
import useUserStore from "@/store/modules/user";
import { changePassword } from "@/api/teacher";

const { proxy } = getCurrentInstance();
const userStore = useUserStore();

const emits = defineEmits(["cancel"]);
const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
});

watch(
  () => props.open,
  (val) => {
    show.value = val;
  }
);
const loading = ref(false);
const show = ref(false);
const pwdRef = ref(null);
const pwdForm = ref({
  oldPassword: "",
  newPassword: "",
  confirmPassword: "",
});
const validOldPwd = (rule, value, callback) => {
  if (!value) {
    callback(new Error("旧密码不能为空"));
  }
  // else if (/^(?=.*[0-9])(?=.*[a-zA-Z]).+$/.test(value)) {
  //   callback(new Error("请输入包含字母+数字"));
  // }
  else {
    callback();
  }
};

const validNewPwd = (rule, value, callback) => {
  let reg = /^(?=.*[0-9])(?=.*[a-zA-Z]).+$/g;
  if (!value) {
    callback(new Error("新密码不能为空"));
  } else if (!reg.test(value)) {
    callback(new Error("请输入包含字母+数字"));
  } else if (value.length < 6) {
    callback(new Error("密码不能小于6位"));
  } else {
    callback();
  }
};

const validConPwd = (rule, value, callback) => {
  if (!value) {
    callback(new Error("确认密码不能为空"));
  } else if (value != pwdForm.value.newPassword) {
    callback(new Error("两次输入密码不一致"));
  } else {
    callback();
  }
};
const pwdRules = ref({
  oldPassword: [
    { required: true, validator: validOldPwd, trigger: ["change", "blur"] },
  ],
  newPassword: [
    { required: true, validator: validNewPwd, trigger: ["change", "blur"] },
  ],
  confirmPassword: [
    { required: true, validator: validConPwd, trigger: ["change", "blur"] },
  ],
});

const submitForm = () => {
  pwdRef.value.validate((valid) => {
    if (valid) {
      loading.value = true;
      changePassword(pwdForm.value)
        .then((res) => {
          proxy.$modal.msgSuccess("修改成功");
          emits("cancel");
        })
        .finally(() => (loading.value = false));
    }
  });
};
const handleCancel = () => {
  pwdRef.value.resetFields();
  emits("cancel");
};
</script>
<style lang="scss" scoped></style>