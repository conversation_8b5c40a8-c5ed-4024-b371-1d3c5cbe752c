import request from "@/utils/request";

// 添加端口类别
export function addSchoolAssetsPortType(data) {
  return request({
    url: "/system/schoolAssetsPortType/add",
    method: "post",
    data,
  });
}

// 端口类别列表
export function schoolAssetsPortTypeList(params) {
  return request({
    url: `/system/schoolAssetsPortType/list`,
    method: "get",
    params,
  });
}

// 修改端口类别
export function updateSchoolAssetsPortType(data) {
  return request({
    url: "/system/schoolAssetsPortType/update",
    method: "put",
    data,
  });
}

// 删除端口类别
export function delSchoolAssetsPortType(id) {
  return request({
    url: `/system/schoolAssetsPortType/delete/${id}`,
    method: "delete",
  });
}

// 添加资产信息类别
export function addSchoolAssetsType(data) {
  return request({
    url: "/system/schoolAssetsType/add",
    method: "post",
    data,
  });
}

// 查询资产信息类别树形结构
export function schoolAssetsTypeTree(params) {
  return request({
    url: `/system/schoolAssetsType/tree`,
    method: "get",
    params,
  });
}

// 资产信息根节点列表
export function schoolAssetsTypeRootList(params) {
  return request({
    url: `/system/schoolAssetsType/root/list`,
    method: "get",
    params,
  });
}

// 资产信息子节点列表
export function schoolAssetsTypeChildrenList(id) {
  return request({
    url: `/system/schoolAssetsType/find/children/${id}`,
    method: "get",
  });
}

// 修改资产信息类别
export function updateSchoolAssetsType(data) {
  return request({
    url: "/system/schoolAssetsType/update",
    method: "put",
    data,
  });
}

// 删除资产信息类别
export function delSchoolAssetsType(id) {
  return request({
    url: `/system/schoolAssetsType/delete/${id}`,
    method: "delete",
  });
}
