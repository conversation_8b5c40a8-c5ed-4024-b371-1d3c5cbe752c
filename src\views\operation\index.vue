<template>
  <div class="operation app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          部门管理
        </div>
      </template>
      <div class="operation-main">
        <el-form :inline="true" class="search-list">
          <el-form-item>
            <span class="search-text">部门名称:</span>
            <el-input
              v-model.trim="queryParams.deptName"
              placeholder="请输入部门名称"
              clearable
              @keyup.enter="handleQuery"
              @clear="handleQuery"
              style="width: 200px;"
            />
          </el-form-item>
          <el-form-item>
            <span class="search-text">状态:</span>
            <el-select
              v-model="queryParams.corpStatus"
              placeholder="部门状态"
              clearable
              style="width: 200px;"
              @change="handleQuery"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="mb12" style="display: flex; align-items: center;justify-content: space-between;">
          <div>
            <el-button plain type="primary" icon="Plus" @click="handleAdd">新增</el-button>
            <el-button 
              color="#f4f4f5" 
              icon="Sort" 
              style="color: #90939c"
              @click="toggleExpand"
            >{{ isExpand ? '折叠' : '展开' }}</el-button>
          </div>
        </div>
        <div class="operation-main_table">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            row-key="deptId"
            @expand-change="tableExpand"
            :default-expand-all="isExpand"
            :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
          >
            <el-table-column
              label="部门全称"
              minWidth="180px"
              align="left"
              prop="deptName"
              show-overflow-tooltip
            />
            <el-table-column
              label="排序"
              align="center"
              minWidth="120px"
              prop="orderNum"
            />
            <el-table-column label="状态" align="center" minWidth="100px">
              <template #default="scope">
                <el-tag
                  v-if="statusList[scope.row.status]"
                  effect="dark"
                  :type="statusList[scope.row.status].type"
                  >{{ statusList[scope.row.status].label }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              minWidth="120px"
              prop="createTime"
            />
            <el-table-column
              label="操作"
              min-width="200"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleEdit(scope)"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>
        </div>
        <pagination
          v-show="tableList.length > 0"
          :total="tableList.length"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="getList"
        />
      </div>

      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        :title="title"
        width="500"
      >
        <el-form
          ref="deptRef"
          :model="deptInfo"
          :rules="deptRules"
          label-width="100"
        >
          <el-form-item label="上级部门" prop="parentId">
            <el-tree-select
              v-model="deptInfo.parentId"
              placeholder="请选择上级部门"
              :data="deptOptions"
              :props="{
                value: 'deptId',
                label: 'deptName',
                children: 'children'
              }"
              clearable
              check-strictly
            />
          </el-form-item>
          <el-form-item label="部门名称" prop="deptName">
            <el-input v-model.trim="deptInfo.deptName" maxlength="15" show-word-limit placeholder="请输入部门名称" />
          </el-form-item>
          <el-form-item label="排序" prop="orderNum">
            <el-input-number 
              v-model="deptInfo.orderNum" 
              :min="0" 
              :max="9999"
              :controls="true"
              placeholder="请输入排序号"
            />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="deptInfo.status">
              <el-radio
                v-for="item in statusList"
                :key="item.value"
                :value="item.value"
              >{{ item.label }}</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="负责人" prop="leaderId">
            <el-select
              v-model="deptInfo.leaderId"
              placeholder="请选择负责人"
              clearable
              filterable
              @change="handleLeaderChange"
            >
              <el-option
                v-for="item in userList"
                :key="item.userId"
                :label="item.nickName"
                :value="item.userId"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="联系电话" prop="leaderPhone">
            <el-input 
              v-model.trim="deptInfo.leaderPhone" 
              maxlength="11" 
              disabled
              placeholder="负责人联系电话" 
            />
          </el-form-item>
          <el-form-item label="邮箱" prop="leaderEmail">
            <el-input 
              v-model.trim="deptInfo.leaderEmail" 
              maxlength="50" 
              disabled
              placeholder="负责人邮箱地址" 
            />
          </el-form-item>
        </el-form>
        
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleCancel">取消</el-button>
            <el-button type="primary" @click="handleSubmit(deptRef)">确定</el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="operation">
import { useRoute } from "vue-router";
import { tranListToTreeData } from "@/utils";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted
} from "vue";
import { nextTick } from "process";
import { debounce } from "@/utils/debounce";
import { listTree, addDept, updateDept, delDept, getDept } from "@/api/system/distribution";
import { listUser } from "@/api/system/user";

const route = useRoute();
const { proxy } = getCurrentInstance();
const deptRef = ref(null);
const tableRef = ref(null);
const state = reactive({
  maxHeight: 0,
  title: "新增部门",
  dialogVisible: false,
  loading: true,
  deptInfo: {
    deptId: "",
    parentId: 0,
    deptName: "",
    orderNum: 0,
    status: 0,
    leaderId: "",
    leaderPhone: "",
    leaderEmail: ""
  },
  tableList: [],
  statusList: [
    { label: "启用", value: 0, type: "success" },
    { label: "停用", value: 1, type: "danger" },
  ],
  queryParams: {
    current: 1,
    size: 10,
    deptName: '',
    corpStatus: '',
  },
  deptRules: {
    deptName: [{ required: true, message: "部门名称不能为空", trigger: "blur" }],
    orderNum: [
      { required: true, message: "排序不能为空", trigger: "blur" },
      { 
        validator: (rule, value, callback) => {
          if (value === undefined || value === null) {
            callback(new Error('排序不能为空'));
          } else if (!Number.isInteger(value)) {
            callback(new Error('排序必须为整数'));
          } else if (value < 0 || value > 9999) {
            callback(new Error('排序范围必须在0-9999之间'));
          } else {
            callback();
          }
        },
        trigger: 'blur'
      }
    ],
    phone: [
      { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
    ],
    email: [
      { type: "email", message: "请输入正确的邮箱地址", trigger: "blur" }
    ]
  },
  deptOptions: [],
  userList: []
});

const {
  maxHeight,
  title,
  deptInfo,
  deptRules,
  tableList,
  statusList,
  queryParams,
  dialogVisible,
  loading,
  deptOptions,
  userList
} = toRefs(state);

const isExpand = ref(false);

function tableExpand() {
  nextTick(() => state.maxHeight = tableRef.value.$el.clientHeight)
}

function handleAdd() {
  getUserList();
  state.deptInfo = {
    parentId: 0,
    deptName: "",
    orderNum: 0,
    status: 0,
    leaderId: "",
    leaderPhone: "",
    leaderEmail: ""
  };
  state.dialogVisible = true;
  state.title = "新增部门";
}

function handleEdit({ row }) {
  getUserList();
  getDept(row.deptId).then(response => {
    console.log('部门详情', response)
    const deptData = response.data;
    const leader = state.userList.find(user => user.userId === deptData.leaderId);
    
    state.deptInfo = {
      deptId: deptData.deptId,
      deptName: deptData.deptName,
      orderNum: deptData.orderNum,
      parentId: deptData.parentId || 0,
      status: Number(deptData.status),
      leaderId: deptData.leaderId,
      leaderPhone: deptData.leaderPhone,
      leaderEmail: deptData.leaderEmail
    };

    if (deptData.leaderId && deptData.leaderName && !leader) {
      state.userList.push({
        userId: deptData.leaderId,
        nickName: deptData.leaderName
      });
    }

    state.dialogVisible = true;
    state.title = "编辑部门";
  });
}

const handleSubmit = debounce((formRef) => {
  formRef.validate((valid) => {
    if (valid) {
      const params = {
        deptId: state.deptInfo.deptId,
        deptName: state.deptInfo.deptName,
        orderNum: state.deptInfo.orderNum,
        parentId: state.deptInfo.parentId,
        status: state.deptInfo.status,
        leaderId: state.deptInfo.leaderId
      };
      console.log(`${state.title}部门参数:`, params);
      
      if (state.title.includes("编辑")) {
        updateDept(params).then(response => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          handleCancel();
        });
      } else {     
        addDept(params).then(response => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          handleCancel();
        });
      }
    }
  });
}, 200);

function handleCancel() {
  deptRef.value.resetFields();
  state.dialogVisible = false;
}

function handleDel({ row, $index }) {
  proxy.$modal
    .confirm(
      `确定要删除名称为${row.deptName}的部门信息？`
    )
    .then(() => {
      delDept(row.deptId).then(response => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      });
    });
}

function getList() {
  loading.value = true;
  let obj = {
    deptName: state.queryParams.deptName,
    status: state.queryParams.corpStatus
  }
  
  listTree(obj).then(res => {
    loading.value = false;
    state.tableList = res.data;
    console.log('获取部门列表:', res);
    
    // 获取部门选项数据
    let obj2 = {
      deptName: '',
      statusch: ''
    }
    listTree(obj2).then(response => {
      // 直接使用树形数据
      state.deptOptions = [{
        deptId: 0,
        deptName: '无上级部门',
        children: response.data
      }];
    })
  }).catch(() => {
    state.loading = false;
  });
}

/* function handleQuery() {
  getList();
} */
const handleQuery = debounce(() => {
  getList();
  isExpand.value = true;
},200)

const resetQuery = debounce(() => {
  state.queryParams = {
    current: 1,
    size: 10,
    deptName: '',
    corpStatus: '',
  };
  getList();
}, 200);

const toggleExpand = () => {
  isExpand.value = !isExpand.value;
  const rows = tableRef.value.data;
  const toggleRows = (data) => {
    data.forEach(row => {
      tableRef.value.toggleRowExpansion(row, isExpand.value);
      if (row.children && row.children.length) {
        toggleRows(row.children);
      }
    });
  };
  toggleRows(rows);
};

// 获取用户列表
const getUserList = async () => {
  try {
    const params = {
      current: 1,
      size: 100
    };
    const res = await listUser(params);
    state.userList = res.data.records || [];
  } catch (error) {
    console.error('获取用户列表失败:', error);
  }
};

// 负责人选择变更处理
const handleLeaderChange = (value) => {
  if (!value) {
    // 如果清空选择，则清空相关联字段
    state.deptInfo.leaderId = '';
    state.deptInfo.leaderPhone = '';
    state.deptInfo.leaderEmail = '';
    return;
  }
  
  // 查找选中的用户信息
  const selectedUser = state.userList.find(user => user.userId === value);
  if (selectedUser) {
    // 自动填充电话和邮箱
    state.deptInfo.leaderId = selectedUser.userId;
    state.deptInfo.leaderPhone = selectedUser.phoneNumber || '';
    state.deptInfo.leaderEmail = selectedUser.email || '';
  }
};

onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
#el-id-6653-106{
  width: 160px;
}
.el-cascader {
  width: 100%;
}
.operation{
  &-main{
    .search-list{
      .search-text{
        margin-right: 10px;
        font-weight: bold;
        color: #626266;
      }
    }
    &_table{
      display: flex;
      &-left{
        margin-right: 10px;
        border: 1px solid #f1f1f1;
        :deep(.treeNode) {
          .el-tree-node__content {
            height: auto;
          }
          .el-tree-node__label{
            white-space: wrap;
            padding: 3px 3px 3px 0;
          }
        }
      }
    }
  }
}
</style>
