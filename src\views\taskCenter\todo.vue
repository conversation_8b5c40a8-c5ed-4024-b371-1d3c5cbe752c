<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item prop="workOrderCode">
          <el-input
            v-model="queryParams.workOrderCode"
            placeholder="请输入工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="deviceCode">
          <el-input
            v-model="queryParams.deviceCode"
            placeholder="请输入设备编码"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="nameAndPhone">
          <el-input
            v-model="queryParams.nameAndPhone"
            placeholder="请输入报障人姓名/联系方式"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="channel">
          <el-select
            v-model="queryParams.channel"
            style="width: 200px"
            clearable
            filterable
            placeholder="请选择报障来源"
            @change="handleQuery"
          >
            <el-option
              v-for="item in channelList"
              :label="item"
              :value="item"
              :key="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="status">
          <el-select
            v-model="queryParams.status"
            style="width: 200px"
            clearable
            filterable
            placeholder="请选择工单状态"
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusList"
              :label="item.label"
              :value="item.value"
              :key="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="time">
          <el-date-picker
            v-model="queryParams.time"
            type="daterange"
            clearable
            format="YYYY-MM-DD"
            value-format="YYYY-MM-DD"
            style="width: 320px"
            @change="handleSearch"
            :disabled-date="disabledDate"
            start-placeholder="报障开始日期"
            end-placeholder="报障结束日期"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12" v-if="!isExternalPark">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建报障单</el-button
          >
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="ledgerList" border>
        <el-table-column
          label="工单编号"
          align="center"
          show-overflow-tooltip
          prop="workOrderCode"
          min-width="120px"
        />
        <el-table-column
          label="设备编码"
          align="center"
          min-width="140px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.deviceCode || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="报障人姓名"
          align="center"
          show-overflow-tooltip
          min-width="120px"
          prop="repairName"
        />
        <el-table-column
          label="报障人联系方式"
          align="center"
          show-overflow-tooltip
          min-width="120px"
          prop="repairPhone"
        />
        <el-table-column
          label="报障单位"
          align="center"
          min-width="140px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.corpName || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="报障时间"
          align="center"
          show-overflow-tooltip
          minWidth="160px"
          prop="submittedTime"
        >
          <template #header>
            <div
              style="padding-left: 5px; cursor: pointer"
              @click="handleSortDate"
            >
              报障时间
              <span class="caret-wrapper">
                <i
                  class="sort-caret ascending"
                  :class="queryParams.sort == 'asc' ? 'active' : ''"
                ></i>
                <i
                  class="sort-caret descending"
                  :class="queryParams.sort == 'desc' ? 'active' : ''"
                ></i>
              </span>
            </div> </template
        ></el-table-column>
        <el-table-column
          label="设备能否正常使用"
          align="center"
          min-width="130px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.isNormal == 1 ? "不能正常使用" : "能正常使用" }}
          </template>
        </el-table-column>
        <el-table-column
          label="报障描述"
          align="center"
          show-overflow-tooltip
          min-width="150px"
          prop="remarkStr"
        />
        <el-table-column
          label="报障附件"
          align="center"
          min-width="140px"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span
              :class="scope.row.annexName ? 'uploadlink' : ''"
              @click="downloadHttp(scope.row.annexUrl)"
              >{{ scope.row.annexName || "-" }}</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="报障来源"
          align="center"
          show-overflow-tooltip
          min-width="180px"
          prop="channel"
        />
        <el-table-column
          label="认领人"
          align="center"
          show-overflow-tooltip
          min-width="180px"
          prop="maintainName"
        />
        <el-table-column
          label="单据状态"
          align="center"
          show-overflow-tooltip
          min-width="100px"
        >
          <template #default="{ row }">
            <el-tag :type="statusObj[row.troubleStatus].type">{{
              statusObj[row.troubleStatus].name
            }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="260"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
          v-hasPermi="['system:deviceTrouble:maintenance']"
        >
          <template #default="scope">
            <template v-if="!isExternalPark">
              <el-button
                link
                type="primary"
                icon="Search"
                @click="handleRepair(scope.row, 0)"
                >查看</el-button
              >
              <el-button
                v-if="scope.row.troubleStatus == 1 && scope.row.canEdit"
                link
                type="warning"
                icon="Edit"
                @click="handleRepair(scope.row, 1)"
                >处理</el-button
              >
              <el-button
                v-if="scope.row.troubleStatus == 4 && scope.row.canEdit"
                link
                type="success"
                icon="RefreshLeft"
                @click="handleRecover(scope.row)"
                >恢复</el-button
              >
              <el-button
                v-if="scope.row.canEdit"
                link
                type="success"
                icon="Upload"
                @click="handleToKnowledge(scope.row)"
                >转入知识库</el-button
              >
            </template>
            <template v-else>
              <span>-</span>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
  </div>
</template>

<script setup name="ledgerManage">
import {
  getCurrentInstance,
  ref,
  reactive,
  toRefs,
  onMounted,
  onBeforeUnmount,
  onUnmounted,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import useUserStore from "@/store/modules/user";
import {
  deviceMaintenanceList,
  addKnowledgeBase,
  troubleUpdate,
} from "@/api/mediaTeach/trouble";
import { downloadHttp, sendPointRequest } from "@/utils";
import { addPointObj } from "@/utils/addPoint";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const userStore = useUserStore();

const state = reactive({
  addTimer: null,
  addSecond: 0,
  ledgerList: [],
  loading: false,
  total: 0,
  channelList: ["电话报障", "扫码报障", "手动报障", "管理员主动报障"],
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    troubleStatusList: [1, 4],
    type: 2,
    deviceCode: "",
    workOrderCode: "",
    nameAndPhone: "",
    status: "",
    time: "",
    channel: "",
  },
  statusObj: {
    1: {
      type: "danger",
      name: "待处理",
    },
    4: {
      type: "info",
      name: "已挂起",
    },
  },
  statusList: [
    { label: "待处理", value: 1 },
    { label: "已挂起", value: 4 },
  ],
});
const {
  addTimer,
  addSecond,
  statusList,
  ledgerList,
  loading,
  total,
  statusObj,
  channelList,
  form,
  queryParams,
} = toRefs(state);

const isExternalPark = ref(false);

function handleSortDate(val) {
  if (queryParams.value.sort === "asc") {
    queryParams.value.sort = "desc";
  } else {
    queryParams.value.sort = "asc";
  }
  getList();
}

const disabledDate = (date) => {
  return new Date() < date;
};

function handleParkChange(event) {
  if (route.path === "/taskManage/taskCenter/todo") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

const handleAdd = () => {
  let obj = localStorage.getItem("WHYWPT_ADDPOINT")
    ? JSON.parse(localStorage.getItem("WHYWPT_ADDPOINT"))
    : {};
  obj.orderStatus = 2;
  localStorage.setItem("WHYWPT_ADDPOINT", JSON.stringify(obj));
  router.push("addTodo?type=1");
};

const handleRecover = (item) => {
  proxy.$modal
    .confirm(`确定恢复此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: item.troubleId,
        status: 6,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("恢复成功");
          getList();
        }
      });
    })
    .catch();
};

// 获取列表数据
function getList() {
  state.loading = true;
  console.log("查询的参数", state.queryParams);
  const { status } = state.queryParams;
  let time = state.queryParams.time;
  state.queryParams.repairStartTime = time ? time[0] : "";
  state.queryParams.repairEndTime = time ? time[1] : "";
  deviceMaintenanceList({
    ...state.queryParams,
    troubleStatusList: status ? [status] : [1, 4],
  })
    .then((response) => {
      console.log("列表数据", response.data);
      const { maintenanceWebListVOList = [], total = 0 } = response.data || {};

      state.ledgerList = maintenanceWebListVOList.map((item) => ({
        ...item,
        remarkStr: item.remark || "-",
        canEdit: item.maintainUserId == userStore.userId,
      }));

      state.total = total;
      state.loading = false;
    })
    .catch(() => {
      state.loading = false;
    });
}

// 搜索相关方法
function handleSearch() {
  handleQuery();
}

function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  delete queryParams.value.sort;
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

// 页面操作方法
function handleRepair(row, type) {
  localStorage.setItem("todoPage", queryParams.value.pageNum);
  router.push({
    path: "/taskManage/taskCenter/todoTaskInfo",
    query: { id: row.troubleId, type },
  });
}

function handleToKnowledge(row) {
  // 添加到知识库
  proxy.$modal
    .confirm(`确定将编号为${row.workOrderCode}的工单转入知识库？`)
    .then(() => {
      addKnowledgeBase({
        troubleId: row.troubleId,
      }).then((resp) => {
        if (resp.code == 200) {
          sendPointRequest({
            event: "Click",
            eventDescribe: "点击转入知识库",
            content:
              row.troubleStatus > -1
                ? addPointObj[row.troubleStatus].status
                : "",
            num: 1,
          });
          proxy.$modal.msgSuccess("转入成功");
          getList();
        }
      });
    })
    .catch();
}

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

// 初始化
onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  if (route.query.status) {
    let { status, time } = route.query;
    state.queryParams.status = status * 1;
    state.queryParams.time = time ? [time, time] : null;
  }
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem("todoPage");
  if (savedPage) {
    queryParams.value.pageNum = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem("todoPage");
  }
  initParkState();
  getList();
  window.addEventListener("parkChange", handleParkChange);
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览工单中心待处理",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
});

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});
</script>

<style lang="scss">
.todo {
  &-tit {
    margin-bottom: 10px;
  }
  &-statics {
    border: 1px solid #d4d4d4;
    display: flex;
    align-items: center;
    padding: 20px;
    margin-bottom: 20px;
    &_item {
      padding: 2vw;
      flex: 1;
      display: flex;
      justify-content: center;
      div {
        margin-right: 20px;
      }
      &:last-child {
        border-left: 1px solid #d4d4d4;
      }
    }
  }
}
</style>

<style lang="scss" scoped>
.ascending.active {
  border-bottom-color: #4095e5;
}
.descending.active {
  border-top-color: #4095e5;
}
.uploadlink {
  color: #4095e5;
  text-decoration: underline;
  cursor: pointer;
}
.disUoloadBtn .el-upload--picture-card {
  display: none;
}

.dialog-search {
  display: flex;
  margin-bottom: 20px;
  .btns {
    margin-left: 10px;
  }
}

.tableDiv {
  padding: 0 20px 20px;
}
</style>
