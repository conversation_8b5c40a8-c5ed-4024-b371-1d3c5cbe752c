<template>
  <div class="addTodo app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-button
        class="mb12"
        plain
        icon="Plus"
        type="primary"
        @click="handleSelect"
        >选择设备信息</el-button
      >
      <el-table :data="deviceList" border>
        <el-table-column
          label="设备编码"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceCodeStr"
        />
        <el-table-column
          label="设备名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备图片"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
        >
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="contain"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="设备类型"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="设备标签"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTagStr"
        />
        <el-table-column
          label="安装位置"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="installAddressStr"
        />
        <el-table-column
          label="信息资产等级"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="levelStr"
        />
        <el-table-column
          label="资产端口类别"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="portTypeNameStr"
        />
        <el-table-column
          label="信息资产类别"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="assetsTypeNameStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column
          label="物理地址"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="macAddressStr"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="logicAddressStr"
        />
        <el-table-column
          label="IP地址"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="ipAddressStr"
        />
        <el-table-column
          label="操作系统版本号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="osVersionStr"
        />
        <el-table-column
          label="状态"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
        >
          <template #default="scope">
            <el-tag
              effect="dark"
              :type="statusObj[scope.row.deviceStatus]?.type"
              >{{ statusObj[scope.row.deviceStatus]?.name }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="CPU"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="cpuStr"
        />
        <el-table-column
          label="品牌"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="brandStr"
        />
        <el-table-column
          label="内存"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="internalStorageStr"
        />
        <el-table-column
          label="硬盘"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="diskStr"
        />
        <el-table-column
          label="端口"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="portStr"
        />
        <el-table-column
          label="线缆"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="cableStr"
        />
        <el-table-column
          label="入库时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="putTime"
          width="180"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.putTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="关联设备"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="associatedDeviceStr"
        />
        <el-table-column
          label="关联人员"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="associatedPeopleStr"
        />
      </el-table>

      <!-- 工单详情表格 -->
      <el-form ref="repairRef" :model="repairForm" :rules="rules">
        <el-descriptions
          title="工单详情"
          border
          style="margin-top: 20px"
          :column="2"
        >
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单来源"
          >
            <span>{{ repairForm.resource }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障人"
          >
            <el-form-item label="" prop="userId">
              <el-select
                v-model="repairForm.userId"
                placeholder="请选择报障人"
                filterable
                clearable
                style="width: 250px"
                @change="repairUserChange"
              >
                <el-option
                  v-for="item in userList"
                  :key="item.userId"
                  :label="item.nickName"
                  :value="item.userId + ''"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障人联系方式"
          >
            <el-form-item label="" prop="repairPhone">
              <el-input
                v-model="repairForm.repairPhone"
                style="width: 250px"
                clearable
                placeholder="请输入联系方式"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障时间"
          >
            <el-form-item label="" prop="repairTime">
              <el-date-picker
                type="datetime"
                placeholder="请选择报障时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                v-model="repairForm.repairTime"
                :disabled-date="disabledDateFn"
                style="width: 250px"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障来源"
          >
            <el-form-item label="" prop="channel">
              <el-select v-model="repairForm.channel" style="width: 250px">
                <el-option
                  v-for="item in channelList"
                  :key="item"
                  :label="item"
                  :value="item"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="能否正常使用"
          >
            <el-form-item label="" prop="isNormal">
              <el-select
                v-model="repairForm.isNormal"
                filterable
                clearable
                style="width: 250px"
              >
                <el-option label="能正常使用" :value="0" />
                <el-option label="不能正常使用" :value="1" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障描述"
            :span="3"
          >
            <el-form-item label="" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                style="width: 100%"
                maxlength="200"
                placeholder="请输入报障描述"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="报障图片"
            :span="3"
          >
            <el-form-item label="" prop="images">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    repairForm.images = url;
                  }
                "
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报障附件"
            :span="3"
          >
            <el-form-item label="" prop="annexUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    repairForm.annexUrl = url;
                  }
                "
                :fileSize="10"
                :fileType="['txt', 'doc', 'docx', 'xls', 'xlsx', 'pdf']"
                :type="4"
                :isWorkOrderAnnex="true"
                :limit="1"
                :modelValue="repairForm.annexUrl"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="repairForm.channel == '电话报障'"
            label-class-name="label-width"
            class-name="value-width"
            label="电话录音"
            :span="3"
          >
            <el-form-item label="" prop="fileUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    repairForm.fileUrl = url;
                  }
                "
                :limit="1"
                :fileType="['mp3', 'm4a', 'wav', 'aac']"
                :modelValue="repairForm.fileUrl"
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <!-- 按钮区域，根据readonly状态显示不同按钮 -->
      <div class="addTodo-btns">
        <el-button type="primary" @click="handleSubmit" v-throttle
          >新建工单</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>

    <el-dialog
      class="custom-dialog"
      title="选择设备信息"
      v-model="dialogVisible"
      width="800"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item prop="deviceCode">
          <el-input
            v-model="queryParams.deviceCode"
            placeholder="请输入设备编码"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="positionId">
          <el-tree-select
            v-model="queryParams.positionId"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            check-strictly
            style="width: 200px"
            clearable
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        :data="ledgerList"
        border
        @row-click="rowClick"
      >
        <el-table-column align="center" width="55">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="(val) => handleChangeCheck(val, scope.$index)"
              @click.native.stop=""
            ></el-checkbox>
          </template>
        </el-table-column>
        <el-table-column
          label="设备编码"
          prop="deviceCodeStr"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          label="设备名称"
          prop="deviceNameStr"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="设备类型"
          prop="deviceTypeStr"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          label="安装位置"
          prop="installAddressStr"
          min-width="120"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
        :background="false"
        layout="total, prev, pager, next"
        :autoScroll="false"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisibleImg">
      <img w-full :src="dialogImageUrl" alt="图片预览" />
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { devicePage } from "@/api/mediaTeach/ledger";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import { listUser } from "@/api/system/user";
import { onMounted, toRefs } from "vue";
import {
  timeFormat,
  scrollToErrorField,
  sendPointRequest,
} from "@/utils/index";
import imgUpload from "@/components/ImageUpload";
import fileUpload from "@/components/FileUpload";
import useUserStore from "@/store/modules/user";
import { getUserByRole } from "@/api/system/user";
import { getPositionTree } from "@/api/mediaTeach/position";

const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  repairRef: null,
  loading: false,
  total: 0,
  deviceList: [],
  dialogImageUrl: "",
  userList: [],
  dialogVisibleImg: false,
  dialogVisible: false,
  ledgerList: [],
  queryParams: {
    current: 1,
    size: 5,
    deviceCode: "",
    deviceName: "",
    positionId: "",
  },
  repairForm: {
    type: 1,
    userId: userStore.userId + "",
    repairName: userStore.nickName,
    repairPhone: userStore.phonenumber,
    repairTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    deviceCodeList: [],
    remark: "",
    resource: "管理后台",
    channel: "管理员主动报障",
    images: "",
    fileUrl: "",
  },
  positionTreeList: [],
  curDevice: {},
  positionResult: {
    names: [],
    ids: [],
  },
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  channelList: ["电话报障", "管理员主动报障"],
  statusObj: {
    0: {
      name: "正常",
      type: "success",
    },
    1: {
      name: "维修中",
      type: "danger",
    },
    3: {
      name: "待审核",
      type: "primary",
    },
    4: {
      name: "已报废",
      type: "warning",
    },
  },
  rules: {
    userId: [
      {
        required: true,
        message: "报障人不能为空",
        trigger: ["blur", "change"],
      },
    ],
    repairPhone: [
      { required: true, message: "报障人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    repairTime: [
      {
        required: true,
        validator: validateRepairTime,
        trigger: ["blur", "change"],
      },
    ],
    isNormal: [
      {
        required: true,
        message: "请选择设备能否正常使用",
        trigger: ["blur", "change"],
      },
    ],
    remark: [
      { required: true, message: "报障描述不能为空", trigger: "blur" },
      { max: 200, message: "报障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        message: "报障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
    file: [
      {
        required: true,
        message: "录音文件至少要有1个",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const {
  repairRef,
  positionResult,
  loading,
  positionProps,
  positionTreeList,
  total,
  channelList,
  deviceList,
  statusObj,
  userList,
  dialogImageUrl,
  dialogVisibleImg,
  dialogVisible,
  ledgerList,
  queryParams,
  repairForm,
  form,
  rules,
} = toRefs(state);

function validateRepairTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择报修时间"));
  } else if (new Date(value).getTime() > new Date().getTime()) {
    callback(new Error("报修时间不能选择未来时间"));
  } else {
    callback();
  }
}

async function getPositionTreeList() {
  await getPositionTree({}).then((response) => {
    state.positionTreeList = response.data;
  });
}

const repairUserChange = (val) => {
  const idx = state.userList.findIndex((_) => _.userId == val);
  console.log("报障人更改", val, idx);
  state.repairForm.repairPhone = state.userList[idx]?.phoneNumber || "";
  state.repairForm.repairName = state.userList[idx]?.nickName || "";
};

function rowClick(row) {
  console.log("rowClick", row);
  const idx = state.ledgerList.findIndex((_) => _.deviceId == row.deviceId);
  if (row.checked) {
    state.ledgerList[idx].checked = false;
    state.curDevice = {};
    // state.deviceList = [];
  } else {
    clearCheck();
    state.ledgerList[idx].checked = true;
    state.curDevice = JSON.parse(JSON.stringify(row));
    // state.deviceList = [row];
  }
}

function handleChangeCheck(val, index) {
  if (val) {
    clearCheck();
    state.ledgerList[index].checked = true;
    state.curDevice = JSON.parse(JSON.stringify(state.ledgerList[index]));
    // state.deviceList = [state.ledgerList[index]];
    console.log("check", val);
  } else {
    state.curDevice = {};
  }
}

function clearCheck() {
  state.ledgerList = state.ledgerList.map((item) => {
    item.checked = false;
    return item;
  });
}

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

const handleSelect = () => {
  resetQuery();
  clearCheck();
  state.dialogVisible = true;
};

const handleCancel = () => {
  proxy.resetForm("queryRef");
  state.dialogVisible = false;
};

/** 弹窗搜索按钮操作 */
function handleQuery() {
  state.queryParams.current = 1;
  getList();
}

/** 弹窗重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

function submitForm() {
  if (!state.curDevice.deviceId) {
    proxy.$modal.msgWarning("请选择设备");
    return;
  }
  state.deviceList = [state.curDevice];
  state.dialogVisible = false;
}

function handleSubmit() {
  console.log(Array.isArray(state.repairForm.images));

  // console.log(typeof state.repairForm.images, state.repairForm.images.split(","));
  if (deviceList.value.length == 0) {
    proxy.$modal.msgWarning("请选择报障设备");
    return;
  }
  proxy.$refs["repairRef"].validate((valid, error) => {
    if (valid) {
      state.repairForm.deviceCodeList =
        deviceList.value.map((item) => item.deviceCode) || [];
      // if(typeof state.repairForm.images)

      // try {
      if (!Array.isArray(state.repairForm.images)) {
        // state.repairForm.images = state.repairForm.images.join(",");
        state.repairForm.images = state.repairForm.images?.split(",") || "";
      }
      console.log("报障传的参数", state.repairForm);
      deviceRepair(state.repairForm)
        .then((res) => {
          sendPointRequest({
            event: "Click",
            eventDescribe: '点击新建报障单',
            content: JSON.parse(localStorage.getItem("WHYWPT_ADDPOINT")).orderStatus ?? '',
            num: 1,
          });
          proxy.$modal.msgSuccess("报障成功");
          handleBack();
        })
        .catch(() => {});
      // } catch (error) {}
    } else {
      // console.log(error)
      scrollToErrorField();
    }
  });
}

// 取消/返回处理
function handleBack() {
  proxy.$tab.closeOpenPage(
    `${
      route.query.type == 0
        ? "claim"
        : route.query.type == 1
        ? "todo"
        : "/visit"
    }`
  );
}

/** 查询用户列表 */
async function getUsers2() {
  await listUser({ nickName: "", phone: "" }).then((response) => {
    console.log("用户列表", response.data);
    state.userList = response.data.records || [];
  });
}

/** 获取运维人员列表 */
async function getUsers() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    console.log("拥有角色的用户列表", resp);
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

async function getList() {
  state.loading = true;
  clearCheck();
  const { positionId } = state.queryParams;
  console.log(
    {
      ...state.queryParams,
      positionIds: positionId ? [positionId] : null,
      deviceStatus: [1, 2, 3],
    },
    "传参"
  );

  await devicePage({
    ...state.queryParams,
    positionIds: positionId ? [positionId] : null,
    smartScreen: 1,
    // deviceStatus: [1, 2, 3],
  }).then((response) => {
    console.log("设备列表", response.data);
    const { records = [], total = 0 } = response.data?.page || {};
    state.total = total;
    state.ledgerList = records.reduce((res, cur) => {
      let {
        deviceStatus,
        deviceName,
        deviceType,
        isOff,
        level,
        installAddress,
        portTypeName,
        assetsTypeName,
        port,
        cable,
        associatedDevice = "",
        associatedPeople = "",
        runStatus,
        ralayHost,
        deviceCode,
        ipAddress,
        logicAddress,
        macAddress,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tenantName,
      } = cur;
      res.push({
        ...cur,
        status: !!isOff ? 0 : !!runStatus ? 2 : 1,
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        deviceCodeStr: deviceCode || "-",
        ipAddressStr: ipAddress || "-",
        ralayHostStr: ralayHost || "-",
        installAddressStr: installAddress || "-",
        logicAddressStr: logicAddress || "-",
        macAddressStr: macAddress || "-",
        tenantNameStr: tenantName || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        levelStr: level || "-",
        portTypeNameStr: portTypeName || "-",
        assetsTypeNameStr: assetsTypeName || "-",
        portStr: port || "-",
        cableStr: cable || "-",
        associatedDeviceStr: associatedDevice.replace(/,/g, "、") || "-",
        associatedPeopleStr: associatedPeople.replace(/,/g, "、") || "-",
        checked:
          state.deviceList.findIndex((_) => _.deviceId == cur.deviceId) != -1
            ? true
            : false,
      });
      return res;
    }, []);
    state.loading = false;
  });
}

onMounted(() => {
  getUsers();
  getPositionTreeList();
});
</script>

<style scoped lang="scss">
.addTodo {
  padding: 20px;
  .el-descriptions {
    :deep(.label-width) {
      width: 135px;
      height: 60px;
      &.required {
        position: relative;
        &::before {
          font-size: 14px;
          content: "*";
          color: red;
          padding-right: 5px;
        }
      }
    }
    :deep(.value-width) {
      width: 265px;
      min-height: 60px;
    }
  }

  &-tit {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0;
  }

  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }

  .upload-area {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .upload-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .file-info {
      margin-left: 20px;
      display: flex;
      align-items: center;
      gap: 10px;

      span {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .repair-file {
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &-btns {
    padding: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
