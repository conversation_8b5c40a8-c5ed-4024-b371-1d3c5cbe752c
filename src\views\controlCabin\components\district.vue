<template>
    <div class="districtmain">
        <div class="item item2" v-for="(item, index) in deviceConfig" :key="index">
            <!-- <div class="item2-opt"> -->
            <el-icon size="2vw" :color="configIcon[index].color">
                <component :is="configIcon[index].name"></component>
            </el-icon>
            <div style="text-align: center;">
                <span>{{ item.value }}</span><br />
                {{ item.label }}
            </div>
            <!-- </div> -->
        </div>
        <div class="item item1">
            <div class="sbgzzcs" @click="open1 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="sbgzzcs" width="100%" height="100%" :fullOptions="sbgzzcsOption"
                :loading="false" />
        </div>
        <div class="item item3">
            <Echarts @setFontSize="setFontSize" id="yyyxzt" width="100%" height="100%" style="float: left;"
                :fullOptions="yyyxztOption" :loading="false" />
        </div>
        <div class="item item4">
            <Echarts @setFontSize="setFontSize" id="qqxsjb" width="100%" height="100%" style="float: left;"
                :fullOptions="qqxsjbOption" :loading="false" />
        </div>
        <div class="item item5">
            <Echarts @setFontSize="setFontSize" id="gzcstop" width="100%" height="100%" style="float: left;"
                :fullOptions="gzcstopOption" :loading="false" />
        </div>
        <div class="item item6">
            <div class="qqsbsysc" @click="open2 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="qqsbsysc" width="100%" height="100%" :fullOptions="qqsbsyscOption"
                :loading="false" />
        </div>
        <div class="item item7">
            <Echarts @setFontSize="setFontSize" id="qqjsjb" width="100%" height="100%" style="float: left;"
                :fullOptions="qqjsjbOption" :loading="false" />
        </div>
        <div class="item item8">
            <div class="sbgzqs" @click="open3 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="sbgzqs" width="100%" height="100%" :fullOptions="sbgzqsOption"
                :loading="false" />
        </div>
        <div class="item item9">
            <div class="sbgzztzb" @click="open4 = true">详情</div>
            <Echarts @setFontSize="setFontSize" id="sbgzztzb" width="100%" height="100%" :fullOptions="sbgzztzbOption"
                :loading="false" />
        </div>
        <el-dialog v-model="open1" title="当月各校设备故障总次数" width="600px">
            <el-table :data="tableData1" style="width: 100%" border max-height="72vh">
                <el-table-column prop="tenantName" label="学校名称" />
                <el-table-column prop="malfunctNum" label="当月故障次数" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open2" title="当月全区多媒体教学时长" width="600px">
            <el-table :data="tableData2" style="width: 100%" border max-height="72vh">
                <el-table-column prop="tenantName" label="学校名称" />
                <el-table-column prop="runTime" label="当月多媒体教学时长" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open3" title="年度各校设备故障趋势" width="1100px">
            <el-table :data="tableData3" style="width: 100%" border max-height="72vh">
                <el-table-column prop="name" label="学校名称" />
                <el-table-column v-for="(item, index) in sbgzqsXData" :key="index" :prop="`value${index + 1}`"
                    :label="item" />
            </el-table>
        </el-dialog>
        <el-dialog v-model="open4" title="各校设备故障状态占比" width="600px">
            <el-table :data="tableData4" style="width: 100%" border max-height="72vh">
                <el-table-column prop="tenantName" label="学校名称" />
                <el-table-column prop="normalNum" label="正常设备" />
                <el-table-column prop="malfunctNum" label="故障设备" />
            </el-table>
        </el-dialog>
    </div>
</template>

<script setup name="Index">
import Echarts from '@/components/Echarts/index.vue';
import { onMounted, reactive, ref, getCurrentInstance } from 'vue';
import { queDeviceUseRatio, queDeviceNum, queDeviceRunTime, queRegionalDevice, queMalfunctRank, queMonthMalfunct, queYearMalfunct } from '@/api/district'
import { transformSize } from '@/utils'
import { sm2Decrypt } from '@/utils/sm2encrypt'

// console.log(JSON.parse(sm2Decrypt('040f71145b8f210c6978b05fee11304994876c5e8451c4d5516def009ad9391c0b4da49280b5fc63ecc1fffb8a4143fed8903d672df6f5292cff175642b52ae03a1404dcdd13d7b2a588f525b341668c5d4ed30e0d82c8a48a1ba4208d144678bcae27309786d279dbc64ee6e3d4c41568751f698eb8cae314b1f18330d694c151121e662bc052bd3ce6a31ea487951ada9aa2e895119c45af48e06aee00aacb2f43e62f85b068d9434551f0d1684f77276d871a3122626dd8c6b2fd6ac8')))

const { proxy } = getCurrentInstance()

const open1 = ref(false)
const tableData1 = ref([
    { tenantName: '文艺中学', malfunctNum: 36 },
    { tenantName: '能力小学', malfunctNum: 10 },
    { tenantName: '桥东小学', malfunctNum: 23 }
])
const open2 = ref(false)
const tableData2 = ref([
    { tenantName: '文艺中学', runTime: 36 },
    { tenantName: '能力小学', runTime: 10 },
    { tenantName: '桥东小学', runTime: 23 }
])
const open3 = ref(false)
const tableData3 = ref([
    { tenantName: '文艺中学', date1: 36, date2: 23 },
    { tenantName: '能力小学', date1: 10, date2: 10 },
    { tenantName: '桥东小学', date1: 23, date2: 36 }
])
const open4 = ref(false)
const tableData4 = ref([
    { tenantName: '文艺中学', normalNum: 36, malfunctNum: 23 },
    { tenantName: '能力小学', normalNum: 10, malfunctNum: 10 },
    { tenantName: '桥东小学', normalNum: 23, malfunctNum: 36 }
])

const configIcon = [
    { name: 'Menu', color: '#4ad2ff' },
    { name: 'Platform', color: '#91cc75' },
    { name: 'WarnTriangleFilled', color: '#ee6666' },
    { name: 'PieChart', color: '#a0cfff' },
    { name: 'DataLine', color: '#E6A23C' },
    { name: 'Warning', color: '#F56C6C' }
]

let deviceConfig = ref([
    { label: '设备总数', value: '1000' },
    { label: '开机率', value: '98%' },
    { label: '故障数', value: '1000' },
    { label: '利用率', value: '100%' },
    { label: '性能', value: '正常' },
    { label: '平均故障天数', value: '10' },
])

/** 全区设备使用时长 */
let qqsbsyscXData = ['学校1', '学校2', '学校3', '学校4', '学校5']
let qqsbsyscSData = [100, 140, 230, 100, 130]

/** 各校设备故障总次数 */
let sbgzzcsXData = ['学校1', '学校2', '学校3', '学校4', '学校5']
let sbgzzcsSData = [100, 140, 230, 100, 130]

/** 年度各校设备故障趋势 */
let test = [
    {
        month: 1,
        tenantMalfunct: [
            {
                tenantName: '学校1',
                month: 1,
                malfunctNum: 1
            },
            {
                tenantName: '学校2',
                month: 1,
                malfunctNum: 2
            }
        ]
    },
    {
        month: 2,
        tenantMalfunct: [
            {
                tenantName: '学校1',
                month: 2,
                malfunctNum: 11
            },
            {
                tenantName: '学校2',
                month: 2,
                malfunctNum: 22
            }
        ]
    }
]

let sbgzqsLData = ['学校1', '学校2', '学校3']
let sbgzqsXData = ['10.28', '10.29', '10.30', '10.31', '11.1']
let sbgzqsSData = [
    {
        name: '学校1',
        type: 'line',
        stack: 'Total',
        data: [12, 13, 10, 13, 9, 23, 21]
    },
    {
        name: '学校2',
        type: 'line',
        stack: 'Total',
        data: [12, 18, 19, 13, 19, 13, 11]
    },
    {
        name: '学校3',
        type: 'line',
        stack: 'Total',
        data: [15, 23, 20, 15, 19, 13, 4]
    }
]

/** 各校设备故障状态占比 */
let sbgzztzbXData = ['学校1', '学校2', '学校3', '学校4', '学校5']
let sbgzztzbSData = [
    {
        name: '正常',
        barGap: 0,
        type: 'bar',
        color: '#91cc75',
        barMaxWidth: '25%',
        data: [100, 140, 230, 100, 130]
    },
    {
        name: '故障',
        type: 'bar',
        color: '#ee6666',
        barMaxWidth: '25%',
        data: [150, 100, 200, 140, 100]
    }
]

/** 全区应用运行状态 */
let yyyxztSData = [
    { value: 50, name: '运行良好' },
    { value: 30, name: '运行较差' },
    { value: 20, name: '未运行' },
]

/** 全区生机比 */
let qqxsjbSData = [
    { name: '设备', value: 52 },
    { name: '学生', value: 4881 },
]

/** 全区师机比 */
let qqjsjbSData = [
    { name: '设备', value: 52 },
    { name: '教师', value: 56 },
]

/** 全区设备故障次数top5 */
let gzcstopYData = ['设备5', '设备4', '设备3', '设备2', '设备1']
let gzcstopSData = [100, 100, 130, 140, 230]

/** 注入 */
function injectOption() {
    proxy.qqsbsysc = qqsbsyscOption
    proxy.sbgzzcs = sbgzzcsOption
    proxy.sbgzqs = sbgzqsOption
    proxy.sbgzztzb = sbgzztzbOption
    proxy.yyyxzt = yyyxztOption
    proxy.gzcstop = gzcstopOption
    proxy.qqxsjb = qqxsjbOption
    proxy.qqjsjb = qqjsjbOption
}

/** 当月全区设备使用时长 */
const qqsbsyscOption = reactive({
    options: {
        title: {
            text: '当月全区多媒体教学时长',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '15%',
            left: '8%',
            right: '3%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'bar',
                itemStyle: {
                    color: '#99ccff'
                },
                barMaxWidth: '30%'
            }
        ]
    },
    init: false
})
/** 当月各校设备故障总次数 */
const sbgzzcsOption = reactive({
    options: {
        title: {
            text: '当月各校设备故障总次数',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '15%',
            left: '8%',
            right: '3%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'bar',
                itemStyle: {
                    color: '#fcf16e'
                },
                barMaxWidth: '30%'
            }
        ]
    },
    init: false
})
/** 年度各校设备故障趋势 */
const sbgzqsOption = reactive({
    options: {
        title: {
            text: '年度各校设备故障趋势',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis'
        },
        legend: {
            data: [],
            right: '2%',
            top: '20%',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            height: '65%',
            containLabel: true
        },
        xAxis: {
            type: 'category',
            boundaryGap: false,
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: []
    },
    init: false
})
/** 各校设备故障状态占比 */
const sbgzztzbOption = reactive({
    options: {
        title: {
            text: '各校设备故障状态占比',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '19%',
            right: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '12%',
            left: '7%',
            right: '4%'
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                color: '#fff'
            },
            data: []
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: []
    },
    init: false
})
/** 全区应用运行状态 */
const yyyxztOption = reactive({
    options: {
        title: {
            text: '全区设备运行状态',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '1.5%',
            left: '2.5%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '5%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '占比',
                type: 'pie',
                radius: ['35%', '50%'],
                // color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                color: ['#91cd77', '#ef6567', '#75bedc', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
/** 全区生机比 */
const qqxsjbOption = reactive({
    options: {
        title: {
            text: '全区生机比',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '2.5%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            right: '2%',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '数量',
                type: 'pie',
                radius: '50%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                color: ['#5470c6', '#ff69b4'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
/** 全区师机比 */
const qqjsjbOption = reactive({
    options: {
        title: {
            text: '全区师机比',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '2.5%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            orient: 'vertical',
            right: '2%',
            bottom: '2%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '数量',
                type: 'pie',
                radius: '50%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                color: ['#5470c6', '#00ffff'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
/** 全区设备故障次数top5 */
const gzcstopOption = reactive({
    options: {
        title: {
            text: '全区设备故障次数Top',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '1.5%',
            left: '2.5%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '5%',
            right: '10%',
            bottom: '5%',
            height: '75%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLabel: {
                color: '#fff'
            },
            splitLine: { show: false },
        },
        yAxis: {
            type: 'category',
            axisLabel: {
                color: '#fff'
            },
            data: []
        },
        series: [
            {
                name: '故障次数',
                type: 'bar',
                barMaxWidth: '40%',
                data: [],
                itemStyle: {
                    color: '#fac858'
                }
            },
        ]
    },
    init: false
})

let opt1 = qqsbsyscOption.options
let opt2 = sbgzzcsOption.options
let opt3 = sbgzqsOption.options
let opt4 = sbgzztzbOption.options
let opt5 = yyyxztOption.options
let opt6 = gzcstopOption.options
let opt7 = qqxsjbOption.options
let opt8 = qqjsjbOption.options

injectOption()

function setDatas() {
    // 当月全区设备使用时长
    opt1.series[0].data = qqsbsyscSData
    opt1.xAxis.data = qqsbsyscXData

    // 当月各校设备故障总次数
    opt2.series[0].data = sbgzzcsSData
    opt2.xAxis.data = sbgzzcsXData

    //年度各校设备故障趋势
    opt3.legend.data = sbgzqsLData
    opt3.xAxis.data = sbgzqsXData
    opt3.series = sbgzqsSData

    //各校设备故障状态占比
    opt4.xAxis.data = sbgzztzbXData
    opt4.series = sbgzztzbSData

    //全区应用运行状态
    opt5.series[0].data = yyyxztSData

    //当月设备故障次数top5
    opt6.yAxis.data = gzcstopYData
    opt6.series[0].data = gzcstopSData

    //全区生机比
    opt7.series[0].data = qqxsjbSData

    //全区师机比
    opt8.series[0].data = qqjsjbSData

}

function getDatas() {
    //当月全区设备使用时长
    queDeviceRunTime().then(response => {
        console.log('当月全区设备使用时长 => ', response.data)
        if (response.data.length > 0) {
            qqsbsyscSData = [], qqsbsyscXData = []
            response.data.map((item, index) => {
                qqsbsyscXData[index] = item.tenantName
                qqsbsyscSData[index] = (item.runTime * 1).toFixed(2) || 0
            })
            opt1.series[0].data = qqsbsyscSData
            opt1.xAxis.data = qqsbsyscXData

            tableData2.value = response.data
        }
    })
    // 大屏利用率
    queDeviceUseRatio().then(response => {
        deviceConfig.value[3].value = ((response.data.useRatio * 1).toFixed(2) || 0) + '%'
    })
    //区级设备数量 - 开机率 - 故障数
    queRegionalDevice().then(response => {
        yyyxztSData = [], qqjsjbSData = [], qqxsjbSData = []
        let { deviceNum, offNum, runNum, finePercentum, runPercentum, teacherNum, studentNum, troubleNum, avgTime } = response.data
        deviceConfig.value[0].value = deviceNum || 0
        deviceConfig.value[1].value = (runPercentum * 100).toFixed(2) + '%'
        deviceConfig.value[2].value = troubleNum || 0
        deviceConfig.value[5].value = avgTime || 0
        yyyxztSData.push({
            name: '运行良好', value: (finePercentum * 100).toFixed(2) || 0
        }, {
            name: '运行较差', value: ((1 - finePercentum) * 100).toFixed(2) || 0
        }, {
            name: '未运行', value: ((1 - runPercentum) * 100).toFixed(2) || 0
        })
        qqjsjbSData.push({
            name: '设备', value: deviceNum || 0
        }, {
            name: '教师', value: teacherNum || 0
        })
        qqxsjbSData.push({ name: '设备', value: deviceNum || 0 },
            { name: '学生', value: studentNum || 0 },)
        opt5.series[0].data = yyyxztSData
        opt8.series[0].data = qqjsjbSData
        opt7.series[0].data = qqxsjbSData
    })
    // 全区故障次数前三名
    queMalfunctRank().then(response => {
        console.log('全区故障次数前三名 => ', response.data)
        if (response.data.length > 0) {
            gzcstopYData = [], gzcstopSData = []
            response.data.map((item, index) => {
                gzcstopYData[index] = item.tenantName
                gzcstopSData[index] = item.malfunctNum || 0
            })
            opt6.yAxis.data = gzcstopYData
            opt6.series[0].data = gzcstopSData
        }

    })
    //当月各校设备故障总次数
    queMonthMalfunct().then(response => {
        console.log('当月各校设备故障总次数 => ', response.data)
        if (response.data.length > 0) {
            sbgzzcsXData = [], sbgzzcsSData = []
            response.data.map((item, index) => {
                sbgzzcsXData[index] = item.tenantName
                sbgzzcsSData[index] = item.malfunctNum || 0
            })
            opt2.series[0].data = sbgzzcsSData
            opt2.xAxis.data = sbgzzcsXData

            tableData1.value = response.data
        }

    })
    // 故障状态占比
    queDeviceNum().then(response => {
        console.log('当月各校设备故障状态占比 => ', response.data)
        if (response.data.length > 0) {
            sbgzztzbXData = [], sbgzztzbSData[0].data = [], sbgzztzbSData[1].data = []
            response.data.map((item, index) => {
                sbgzztzbXData[index] = item.tenantName
                sbgzztzbSData[0].data[index] = item.normalNum || 0
                sbgzztzbSData[1].data[index] = item.malfunctNum || 0
            })
            opt4.xAxis.data = sbgzztzbXData
            opt4.series = sbgzztzbSData

            tableData4.value = response.data
        }

    })
    // 年度各校设备故障次数
    queYearMalfunct().then(response => {
        console.log('年度各校设备故障次数 => ', response.data)
        if (response.data.length > 0) {
            sbgzqsXData = [], sbgzqsLData = [], sbgzqsSData = []
            response.data[0].tenantMalfunct.map((item, index) => {
                sbgzqsLData[index] = item.tenantName
                sbgzqsSData.push({
                    name: item.tenantName,
                    type: 'line',
                    stack: `Total${index}`,
                    data: []
                })
            })
            response.data.map((item, index) => {
                sbgzqsXData[index] = item.month
                item.tenantMalfunct.map((item2, index2) => {
                    sbgzqsSData[index2].data[index] = item2.malfunctNum || 0
                })
            })
            opt3.legend.data = sbgzqsLData
            opt3.xAxis.data = sbgzqsXData
            opt3.series = sbgzqsSData
            tableData3.value = []
            sbgzqsSData.map(item => {
                let obj = { name: item.name }
                item.data.map((val, index) => {
                    obj[`value${index + 1}`] = val
                })
                tableData3.value.push(obj)
            })
        }
    })
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(22)
    proxy[id].options.graphic ? proxy[id].options.graphic.style.fontSize = transformSize(25) : ''
}

onMounted(() => {
    setDatas()
    getDatas()
})

</script>

<style lang="scss" scoped>
.districtmain {
    background-color: #0f0c43;
    color: #fff;
    padding: 2.5vh 1vw 1.5vh;
    display: grid;
    grid-template-columns: repeat(6, 15.7%);
    grid-template-rows: 10vh 23vh 23vh 31vh;
    grid-gap: 1.6vh 1vw;

    .item {
        border: 1px solid #fff;
    }

    .item2 {
        // &-opt {
        // border: 1px solid #fff;
        display: flex;
        height: 100%;
        align-items: center;
        justify-content: center;
        font-size: 1vw;

        .el-icon {
            font-size: 2.5vw !important;
            margin-right: 1.5vw;
        }

        span {
            display: inline-block;
            max-width: 8vw;
            word-break: break-all;
            font-size: 1.5vw;
            font-weight: bold;
        }

        // }
    }

    .item1,
    .item6 {
        grid-column-start: 1;
        grid-column-end: 4;
        position: relative;
    }

    .item3,
    .item5 {
        grid-row-start: span 2;
    }

    .item8 {
        grid-column-start: 1;
        grid-column-end: 4;
        position: relative;
    }

    .item9 {
        grid-column-start: 4;
        grid-column-end: 7;
        position: relative;
    }

    .sbgzzcs,
    .qqsbsysc,
    .sbgzqs,
    .sbgzztzb {
        position: absolute;
        cursor: pointer;
        right: 1.5%;
        top: 3.5%;
        font-size: .8vw;
        padding: .5vh 1vw;
        background-color: #a9abae;
        z-index: 9;
    }

    .sbgzzcs,
    .qqsbsysc {
        top: 5%;
    }
}
</style>
