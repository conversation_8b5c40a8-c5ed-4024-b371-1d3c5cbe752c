import request from '@/utils/request'

//区级 大屏利用率
export function queDeviceUseRatio(params) {
    return request({
        url: '/system/device/queDeviceUseRatio',
        method: 'get',
        params
    })
}

//各校设备故障状态占比
export function queDeviceNum(params) {
    return request({
        url: '/system/device/queDeviceNum',
        method: 'get',
        params
    })
}

//当月全区设备使用时长
export function queDeviceRunTime(params) {
    return request({
        url: '/system/device/queDeviceRunTime',
        method: 'get',
        params
    })
}

//查询区级设备数量-开机率-故障数
export function queRegionalDevice(params) {
    return request({
        url: '/system/device/queRegionalDevice',
        method: 'get',
        params
    })
}

//全区故障次数前三名
export function queMalfunctRank(params) {
    return request({
        url: '/system/deviceTrouble/queMalfunctRank',
        method: 'get',
        params
    })
}

//当月各校设备故障总次数
export function queMonthMalfunct(params) {
    return request({
        url: '/system/deviceTrouble/queMonthMalfunct',
        method: 'get',
        params
    })
}

//年度各校设备故障次数
export function queYearMalfunct(params) {
    return request({
        url: '/system/deviceTrouble/queYearMalfunct',
        method: 'get',
        params
    })
}