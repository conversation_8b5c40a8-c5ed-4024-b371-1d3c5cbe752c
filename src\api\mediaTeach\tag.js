import request from '@/utils/request'

// 查询该类型是否有设备绑定
export function checkDeviceTag(id) {
    return request({
        url: `/system/deviceTag/find/device/${id}`,
        method: 'get',
        id
    })
}

// 设备标签列表
export function getDeviceTag(params) {
    return request({
        url: '/system/deviceTag/queList',
        method: 'get',
        params
    })
}

// 设备标签分页
export function deviceTagPage(data) {
    return request({
        url: '/system/deviceTag/quePage',
        method: 'post',
        data
    })
}

// 新增设备标签
export function deviceTagAdd(data) {
    return request({
        url: '/system/deviceTag/add',
        method: 'post',
        data
    })
}

// 修改设备标签
export function deviceTagEdit(data) {
    return request({
        url: '/system/deviceTag/edit',
        method: 'post',
        data
    })
}

// 删除设备标签
export function deviceTagDel(data) {
    return request({
        url: '/system/deviceTag/delById',
        method: 'post',
        data
    })
}