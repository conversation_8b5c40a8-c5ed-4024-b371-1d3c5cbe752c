import { login, logout, getInfo, loginByUserCode, ssoLogin } from "@/api/login";
import { getInfo as getTenantInfo } from "@/api/park";
import { getToken, setToken, removeToken } from "@/utils/auth";
import defAva from "../../assets/images/admin.png";

const keyMap = {
  teacher: "teacherStaff",
};

const useUserStore = defineStore("user", {
  state: () => ({
    isCommon: false, //是否通用版
    isManage: false, // 是否管理端账号
    isTeacher: false, //是否教职工
    isLogout: false, // 是否强制退出（更改页面pathname）
    initPathname: sessionStorage.getItem("initPathname") || "/admin/",
    isDemo: window.location.hostname == "maintainappdemo.gzwinteam.com",
    token: getToken(),
    userId: "",
    name: "",
    nickName: "",
    isAdmin: false,
    avatar: "",
    roles: [],
    roleId: "",
    tenantId: "",
    studentNum: 0,
    cabinRole: "",
    permissions: [],
    phonenumber: "",
    email: "",
    acceptAlertMail: "",
    roleName: "",
    corpId: "",
    corpName: "",
    ssoLoading: false,
  }),
  actions: {
    // 登录
    login(userInfo) {
      const username = userInfo.username.trim();
      const password = userInfo.password;
      const code = userInfo.code;
      const uuid = userInfo.uuid;
      let info = {
        userCode: username,
        permission: keyMap[window.location.pathname.replace(/\//g, "")] || "",
        // username,
        password,
        code,
        uuid,
      };
      // console.log(info, "登录传参");
      return new Promise((resolve, reject) => {
        loginByUserCode(info)
          // login(info)
          .then((res) => {
            // console.log(res, 'login')
            const { access_token } = res.data;
            setToken(access_token);
            this.token = access_token;
            // this.initPathname = window.location.pathname;
            sessionStorage.setItem(`initPathname`, window.location.pathname);
            this.initPathname = window.location.pathname;
            // console.log(this.initPathname, "this.initPathname");
            resolve();
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    //SSO免密登录
    loginBySSO(SSOcode) {
      return new Promise(async (resolve, reject) => {
        try {
          this.ssoLoading = true;
          const res = await ssoLogin({ ssoId: SSOcode });
          // console.log(res, 'login')
          const { access_token } = res.data;
          setToken(access_token);
          this.token = access_token;
          // this.initPathname = window.location.pathname;
          sessionStorage.setItem(`initPathname`, window.location.pathname);
          this.initPathname = window.location.pathname;
          // console.log(this.initPathname, "this.initPathname");
          resolve();
        } catch (error) {
          reject(error);
        }
      });
    },
    // 获取用户信息
    getInfo() {
      return new Promise((resolve, reject) => {
        getInfo()
          .then((res) => {
            console.log(res, "info");
            const user = res.data.user;
            const avatar =
              user.avatar == "" || user.avatar == null ? defAva : user.avatar;
            if (res.data.roles && res.data.roles.length > 0) {
              // 验证返回的roles是否是一个非空数组
              this.roles = res.data.roles;
              this.permissions = res.data.permissions;
            } else {
              this.roles = ["ROLE_DEFAULT"];
            }
            this.isManage = res.data.isAdmin === 1 || res.data.isAdmin === "1";
            if (this.isDemo) {
              this.isManage = user.nickName.indexOf("管理端") !== -1;
            }
            this.corpName = res.data.corpName;
            this.isTeacher =
              window.location.pathname.indexOf("/teacher") != -1 &&
              this.roles.indexOf("teacherStaff") != -1; // 检测是否是教职工专用页面
            // console.log(this.initPathname, "this.initPathname");
            this.isLogout =
              window.location.pathname != this.initPathname ||
              (window.location.pathname.indexOf("/teacher") != -1 &&
                this.roles.indexOf("teacherStaff") == -1);
            // console.log(
            //   this.isLogout,
            //   "this.isLogout",
            //   this.isTeacher,
            //   "this.isTeacher"
            // );
            this.roleId = user.roleId;
            this.tenantId = user.tenantId;
            this.userId = user.userId;
            this.name = user.userName;
            this.nickName = user.nickName;
            this.avatar = avatar;
            this.isAdmin = user.admin;
            this.phonenumber = user.phonenumber;
            this.email = user.email;
            this.corpId = user.corpId;
            this.acceptAlertMail = user.acceptAlertMail;
            this.isCommon = res.data.corpType === "1";

            resolve(res);
          })
          .catch((error) => {
            reject(error);
          });
      });
    },
    getStudentNum() {
      if (this.roles.indexOf("schoolManage") !== -1) {
        return new Promise((resolve, reject) => {
          getTenantInfo({ id: this.tenantId })
            .then((res) => {
              this.studentNum = res.data.studentNum || 0;
              // console.log("storeStudentNum => ", this.studentNum);
              resolve(res);
            })
            .catch((err) => {
              reject(err);
            });
        });
      }
    },
    // 退出系统
    logOut() {
      return new Promise((resolve, reject) => {
        fetch(
          import.meta.env.VITE_APP_BASE_API +
            "/system/schoolDeviceLog/recordLogout",
          {
            method: "post",
            headers: { Authorization: "Bearer " + getToken() },
            keepalive: true,
          }
        )
          .then((response) => {
            if (response.ok) {
              return response.json();
            }
          })
          .then((res) => {
            console.log(res, "记录退出成功");
          })
          .catch((error) => {
            console.log(error, "记录退出失败");
          });
        logout(this.token)
          .then((res) => {
            console.log("退出成功");
            resolve(res);
          })
          .catch((error) => {
            reject(error);
          })
          .finally(() => {
            this.token = "";
            this.roles = [];
            this.permissions = [];
            removeToken();
          });
      });
    },
  },
});

export default useUserStore;
