import request from "@/utils/request";

// 修改拦截/白名单-新
export function updateWindowIntercept(data) {
  return request({
    url: "/system/windowIntercept/new/update/status",
    method: "post",
    data,
  });
}

// 弹窗列表树
export function getWindowInterceptTree(data) {
  return request({
    url: "/system/windowIntercept/tree/list",
    method: "post",
    data,
  });
}

// 定时更新
export function windowInterceptSchedule(params) {
  return request({
    url: "/system/windowIntercept/schedule",
    method: "get",
    params,
  });
}

// 上报弹窗
export function addWindowIntercept(data) {
  return request({
    url: "/system/windowIntercept/add",
    method: "post",
    data,
  });
}

// 弹窗列表
export function getWindowIntercept(data) {
  return request({
    url: "/system/windowIntercept/list",
    method: "post",
    data,
  });
}

// 修改拦截/白名单
export function updateWindowIntercept2(data) {
  return request({
    url: "/system/windowIntercept/update/status",
    method: "post",
    data,
  });
}

// 弹窗详情
export function getWindowInterceptInfo(data) {
  return request({
    url: "/system/windowIntercept/info",
    method: "post",
    data,
  });
}
