<template>
    <el-dialog v-model="importDialogVisible" title="批量导入" width="400px">
        <div class="import-result" v-if="importResult.show">
          <div class="result-title">本次导入：</div>
          <div class="result-content">
            <div class="success">成功：{{ importResult.success }}条</div>
            <div class="error">失败：{{ importResult.error }}条</div>
          </div>
          <div class="error-reason" v-if="importResult.errorReason">
            <div>失败原因如下：</div>
            <div>{{ importResult.errorReason }}</div>
          </div>
        </div>
        <div class="import-steps" v-else>
          <div class="step">
            <div class="step-title">第一步：下载模板</div>
            <el-button type="primary" @click="downloadTemplate">点击下载</el-button>
          </div>
          <div class="step">
            <div class="step-title">第二步：填写信息后上传</div>
            <el-upload
              class="upload-demo"
              action="#"
              :auto-upload="false"
              :on-change="handleFileChange"
              :show-file-list="true"
              accept=".xlsx,.xls"
            >
              <el-button type="primary">上传文件</el-button>
            </el-upload>
          </div>
        </div>
        <template #footer>
          <div class="dialog-footer">
            <el-button @click="handleClose">{{ importResult.show ? '关闭' : '返回' }}</el-button>
            <el-button v-if="!importResult.show" type="primary" @click="handleImport">导入</el-button>
            <el-button v-else type="primary" @click="handleRetry">重新上传</el-button>
          </div>
        </template>
    </el-dialog>
</template>

<script setup>

</script>