<!-- components/emergency/OrderTimeline.vue -->
<template>
  <div class="order-timeline">
    <div class="taskInfo-tit">工单时间线</div>
    <el-timeline>
      <template v-for="event in sortedEvents" :key="event.time">
        <el-timeline-item
          :timestamp="formatDate(event.time)"
          placement="top"
        >
          <div class="timeline-title">{{ event.title }}</div>
        </el-timeline-item>
      </template>
    </el-timeline>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  workOrderData: {
    type: Object,
    required: true
  }
})

// 计算排序后的时间线事件
const sortedEvents = computed(() => {
  const events = []
  
  if (props.workOrderData.createTime) {
    events.push({
      time: props.workOrderData.createTime,
      title: '创建工单'
    })
  }
  
  if (props.workOrderData.handleTime) {
    events.push({
      time: props.workOrderData.handleTime,
      title: '处理工单'
    })
  }
  
  if (props.workOrderData.reportUploadTime) {
    events.push({
      time: props.workOrderData.reportUploadTime,
      title: '上传报告'
    })
  }
  
  if (props.workOrderData.notifyTime) {
    events.push({
      time: props.workOrderData.notifyTime,
      title: '通知相关人员'
    })
  }
  
  return events.sort((a, b) => new Date(a.time) - new Date(b.time))
})

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return '-'
  const date = new Date(dateStr)
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:${String(date.getMinutes()).padStart(2, '0')}`
}
</script>

<style scoped lang="scss">
.order-timeline {
  .taskInfo-tit {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0;
  }
  
  .timeline-title {
    font-weight: bold;
    color: #409EFF;
  }
}
</style>