<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
                <el-form-item label="" prop="model">
                    <el-input v-model="queryParams.model" placeholder="请输入设备型号" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="tenantId">
                    <el-select v-model="queryParams.tenantId" placeholder="请选择所在学校" @change="handleQuery" clearable
                        filterable style="width: 200px">
                        <el-option v-for="item in tenantList" :key="item.tenantId" :label="item.tenantName"
                            :value="item.tenantId">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Warning" :disabled="multiple" @click="handleAdd">批量报修</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="danger" plain icon="QuestionFilled" @click="handleDownload">常见问题解决方法</el-button>
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="ledgerList" border @selection-change="handleSelectionChange">
                <el-table-column type="selection" width="55" align="center" />
                <el-table-column label="设备编码" align="center" minWidth="120px" prop="deviceCode" />
                <el-table-column label="设备名称" align="center" minWidth="120px" prop="deviceName" />
                <el-table-column label="设备类型" align="center" minWidth="120px" prop="deviceType" />
                <el-table-column label="规格型号" align="center" minWidth="120px" prop="model" />
                <el-table-column label="状态" align="center" minWidth="120px">
                    <template #default="scope">
                        <el-tag v-if="statusList[scope.row.deviceStatus]" effect="dark"
                            :type="statusList[scope.row.deviceStatus].type">{{
                                statusList[scope.row.deviceStatus].label }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="设备图片" align="center" minWidth="120px" prop="macAddr">
                    <template #default="scope">
                        <el-image style="width: 50px;height: 30px;display: block;margin: 0 auto;" :src="scope.row.deviceImg"
                            fit="cover" />
                    </template>
                </el-table-column>
                <el-table-column label="所在学校" align="center" minWidth="120px" prop="tenantName" />
                <!-- <el-table-column label="物理地址" align="center" minWidth="120px" prop="macAddress" />
                <el-table-column label="逻辑地址" align="center" minWidth="120px" prop="logicAddress" />
                <el-table-column label="IP地址" align="center" minWidth="120px" prop="ipAddress" /> -->
                <el-table-column label="操作系统版本号" align="center" minWidth="120px" prop="osVersion" />
                <el-table-column label="CPU" align="center" minWidth="120px" prop="cpu" />
                <el-table-column label="内存" align="center" minWidth="120px" prop="internalStorage" />
                <el-table-column label="硬盘" align="center" minWidth="120px" prop="disk" />
                <el-table-column label="入库时间" align="center" minWidth="120px" prop="putTime" width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.putTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="品牌" align="center" minWidth="120px" prop="brand" />
                <el-table-column label="操作" min-width="150" align="center" fixed="right"
                    class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button link type="warning" icon="Edit" @click="handleUpdate(scope.row)">报修</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                v-model:limit="queryParams.size" @pagination="getList" />
        </el-card>

        <!-- 添加或修改岗位对话框 -->
        <el-dialog :title="title" v-model="open" width="800px" append-to-body>
            <el-form ref="ledgerRef" :model="form" :rules="rules" label-width="100px">
                <el-row>
                    <el-col :span="12">
                        <el-form-item label="设备名称" prop="deviceName">
                            <el-input v-model="form.deviceName" :readonly="readonly" placeholder="请输入设备名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备类型" prop="deviceType">
                            <el-input v-model="form.deviceType" :readonly="readonly" placeholder="请输入设备类型
                            " />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="设备编码" prop="deviceCode">
                            <el-input v-model="form.deviceCode" :readonly="readonly" placeholder="请输入设备编码" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="物理地址" prop="macAddress">
                            <el-input v-model="form.macAddress" :readonly="readonly" placeholder="请输入物理地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="规格型号" prop="model">
                            <el-input v-model="form.model" :readonly="readonly" placeholder="请输入规格型号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="逻辑地址" prop="logicAddress">
                            <el-input v-model="form.logicAddress" :readonly="readonly" placeholder="请输入逻辑地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="IP地址" prop="ipAddress">
                            <el-input v-model="form.ipAddress" :readonly="readonly" placeholder="请输入IP地址" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="操作系统" prop="osVersion">
                            <el-input v-model="form.osVersion" :readonly="readonly" placeholder="请输入操作系统版本号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="状态" prop="troubleStatus">
                            <el-select v-model="form.troubleStatus" placeholder="请选择状态" :disabled="readonly"
                                style="width: 100%">
                                <el-option v-for="item in stateList" :key="item.value" :label="item.label"
                                    :value="item.value">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="CPU" prop="cpu">
                            <el-input v-model="form.cpu" :readonly="readonly" placeholder="请输入CPU型号" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="品牌" prop="brand">
                            <el-input v-model="form.brand" :readonly="readonly" placeholder="请输入设备品牌名称" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="内存" prop="internalStorage">
                            <el-input v-model="form.internalStorage" :readonly="readonly" placeholder="请输入设备内存" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="硬盘" prop="disk">
                            <el-input v-model="form.disk" :readonly="readonly" placeholder="请输入设备硬盘" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="入库时间" prop="putTime">
                            <el-date-picker v-model="form.putTime" format="YYYY-MM-DD" style="width: 100%"
                                :readonly="readonly" type="date" placeholder="请选择入库时间" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="维修负责人" prop="maintainUserId">
                            <el-select v-model="form.maintainUserId" placeholder="请选择负责人" style="width: 100%">
                                <el-option v-for="item in userList" :key="item.userId"
                                    :label="item.nickName + ' - ' + item.userId" :value="item.userId">
                                </el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>
                    <el-col :span="12">
                        <el-form-item label="故障描述" prop="remark">
                            <el-input v-model="form.remark" type="textarea" placeholder="请输入故障描述" />
                        </el-form-item>
                    </el-col>
                    <el-col :span="24">
                        <el-form-item label="故障图片" prop="imgList">
                            <imgUpload @update:modelValue="setImgUrl" :limit="3" :modelValue="form.imgList" />
                        </el-form-item>
                    </el-col>
                </el-row>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="ledgerManage">
import imgUpload from '@/components/ImageUpload'
import { useRoute } from 'vue-router';
import { devicePage, deviceInfo } from '@/api/mediaTeach/ledger';
import { troubleAdd } from '@/api/mediaTeach/trouble';
import { getTenantList } from '@/api/park'
import { getUserList } from '@/api/system/user'

const { proxy } = getCurrentInstance();
const route = useRoute()

const ledgerList = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const readonly = ref(true);
const ids = ref([]);
const tenantList = ref([]);
const userList = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const stateList = ref([
    { label: '维修中', value: 0 },
    { label: '正常', value: 1 }
])
const statusList = ref([
    { label: '正常', value: 0, type: 'success' },
    { label: '维修中', value: 1, type: 'danger' }
])

const imageSet = reactive({
    uploadUrl: import.meta.env.VITE_APP_BASE_API + "/ruoyi-file/upload/uploadFile?type=1",
    coversList: [],
    limitCountImg: 9,
    showBtnDealImg: true,
    noneBtnImg: false
})
const data = reactive({
    form: {},
    queryParams: {
        current: 1,
        size: 10,
        tenantId: '',
        model: ''
    },
    rules: {
        remark: [{ required: true, message: "故障描述不能为空", trigger: "blur" }],
        maintainUserId: [{ required: true, message: "维修负责人不能为空", trigger: ["blur", "change"] }],
        imgList: [{ required: true, message: "故障图片不能为空", trigger: ["blur", "change"] }],
    }
});
const { queryParams, form, rules } = toRefs(data);
const { uploadUrl, limitCountImg, showBtnDealImg, noneBtnImg } = toRefs(imageSet);

/** 查询设备列表 */
function getList() {
    loading.value = true;
    devicePage(queryParams.value).then(response => {
        ledgerList.value = response.data.records
        total.value = response.data.total
        loading.value = false
    })
}
/** 查询学校列表 */
function getTenant() {
    getTenantList().then(response => {
        tenantList.value = response.data
    })
}
/** 查询用户列表 */
function getUsers() {
    getUserList({ nickName: '', phone: '' }).then(response => {
        userList.value = response.data
    })
}
/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        troubleId: '',
        deviceId: '',
        deviceCode: '',
        deviceName: '',
        deviceType: '',
        troubleStatus: 0,
        macAddress: '',
        logicAddress: '',
        model: '',
        ipAddress: '',
        osVersion: '',
        cpu: '',
        internalStorage: '',
        disk: '',
        putTime: '',
        brand: '',
        imgList: [],
        maintainUserId: '',
        remark: ''
    };
    proxy.resetForm("ledgerRef");
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.current = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.postId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 批量报修按钮操作 */
function handleAdd() {
    reset();
    // open.value = true;
    title.value = "批量创建报修单";
}
/** 报修按钮操作 */
function handleUpdate(row) {
    reset();
    title.value = "创建报修单";
    deviceInfo({ id: row.deviceId }).then(response => {
        form.value = JSON.parse(JSON.stringify(response.data))
        form.value.troubleStatus = 0
        open.value = true;
    })
}

function handleDownload() {
    // templateDownload({ })
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["ledgerRef"].validate(valid => {
        if (valid) {
            form.value.imgList = form.value.imgList ? form.value.imgList.split(',') : []
            troubleAdd(form.value).then(response => {
                proxy.$modal.msgSuccess("创建成功");
                getList();
                open.value = false
            })
        }
    });
}
/** 设置图片url */
function setImgUrl(url) {
    form.value.imgList = url
}

getTenant()
getUsers()
getList();
</script>

<style lang="scss" scoped>
.disUoloadBtn .el-upload--picture-card {
    display: none;
    /* 上传按钮隐藏 */
}

.dialog-search {
    display: flex;
    margin-bottom: 20px;

    .btns {
        margin-left: 10px;
    }
}

.tableDiv {
    padding: 0 20px 20px;
}
</style>
