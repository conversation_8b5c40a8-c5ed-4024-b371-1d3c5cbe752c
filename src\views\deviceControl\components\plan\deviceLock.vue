<template>
  <div class="deviceLock">
    <div class="deviceLock-list" v-loading="loading">
      <div
        class="deviceLock-item"
        v-for="(item, index) in formList"
        :key="index"
      >
        <div style="min-width: 500px">
          <div class="deviceLock-header">
            <span>无操作锁定设置</span>
            <div class="flex">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="在下述生效规则下，生效设备重启后按设定规则自动进入锁屏状态"
                placement="top-start"
              >
                <el-icon :size="16" style="margin-left: 5px"
                  ><QuestionFilled
                /></el-icon>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button
                  type="primary"
                  style="font-size: 20px; margin-left: 10px"
                  icon="Edit"
                  link
                  v-show="!item.isEdit"
                  @click="handleEdit(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-show="(!item.id && item.isEdit) || !!item.id"
            class="deviceLock-content"
            :class="{ 'deviceLock-content_unedit': !item.isEdit }"
          >
            <div>
              <el-form
                :ref="(el) => setFormRef(el, index)"
                :model="item"
                :label-width="item.isEdit ? '92px' : '82px'"
              >
                <el-form-item
                  :label="item.isEdit ? '启动计划：' : '启用状态：'"
                  prop="isDisable"
                  label-width="82px"
                >
                  <el-switch
                    v-if="item.isEdit"
                    v-model="item.isDisable"
                    :active-value="true"
                    :inactive-value="false"
                    @change="(val) => handleStatusChange(val, index)"
                  />
                  <span v-else>{{ item.isDisable ? "已启用" : "已禁用" }}</span>
                </el-form-item>
                <el-form-item
                  label="无操作锁定规则："
                  prop="num"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validDuration(rule, value, callback, index),
                    trigger: 'blur',
                  }"
                  :label-width="item.isEdit ? '134px' : '124px'"
                >
                  检测到设备无操作
                  <div v-if="item.isEdit" style="margin: 0 10px">
                    <el-input-number
                      v-model="item.num"
                      :min="1"
                      :max="60"
                      :precision="0"
                      :value-on-clear="0"
                      style="width: 150px"
                      :disabled="!!item.id && !item.isDisable"
                    />
                    <el-select
                      v-model="item.unit"
                      style="margin-left: 10px; width: 80px"
                      :disabled="!!item.id && !item.isDisable"
                    >
                      <el-option label="秒" :value="1" />
                      <el-option label="分钟" :value="2" />
                      <el-option label="小时" :value="3" />
                      <el-option label="天" :value="4" />
                    </el-select>
                  </div>
                  <span v-else
                    >{{ item.num || 1 }}{{ unitObj[item.unit] || "秒" }}</span
                  >后自动锁定
                </el-form-item>

                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validSelectedDevices(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="item.selectedDevices"
                    :isEdit="item.isEdit"
                    :device-codes="item.deviceCodes"
                    :disabled="!!item.id && item.isDisable === false"
                    @valid="handleValid(index)"
                    @change="(val) => handleChangeSelectedDevices(val, index)"
                  />
                </el-form-item>
              </el-form>
              <div class="deviceLock-footer" v-if="item.isEdit">
                <el-button type="primary" @click="submitForm(index)" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(index)">取消</el-button>
              </div>
            </div>
            <div v-show="!!item.id && !item.isEdit">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  @click="handleDel(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import deviceSel from "../deviceSel.vue";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import {
  getLockScreenRule,
  deleteLockScreenRule,
  lockScreenRuleBatch,
} from "@/api/deviceControl/plan";
import { timeFormat } from "@/utils";

const { proxy } = getCurrentInstance();

const formRefs = ref({});
// 设置表单引用的函数
const setFormRef = (el, index) => {
  if (el) {
    formRefs.value[index] = el;
  } else {
    delete formRefs.value[index];
  }
};

const state = reactive({
  loading: false,
  initFormList: [],
  formList: [],
  unitObj: {
    1: "秒",
    2: "分钟",
    3: "小时",
    4: "天",
  },
});
const { initFormList, loading, formList, unitObj } = toRefs(state);

onMounted(() => getData());

const handleValid = (index) => {
  formRefs.value[index].validateField(`selectedDevices`);
};

const handleChangeSelectedDevices = (val, index) => {
  state.formList[index].selectedDevices = val?.join(",");
};

function getData() {
  loading.value = true;
  getLockScreenRule({ type: 3 })
    .then((res) => {
      console.log(res, "无操作锁定详情");
      if (res.data && res.data.id) {
        const { deviceCodes, isDisable, num, unit, id } = res.data;
        formList.value[0] = {
          isEdit: false,
          num: num || 1,
          unit: unit || 1,
          isDisable,
          selectedDevices: deviceCodes?.join(",") || "",
          deviceCodes,
          id,
        };

        initFormList.value = JSON.parse(JSON.stringify(formList.value));
      } else {
        resetForm(0);
      }
    })
    .finally(() => (loading.value = false));
}

// 校验持续时间
const validDuration = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].isDisable === false && !!state.formList[index].id) {
    callback();
    return;
  }

  if (!value) {
    callback(new Error("请输入无操作时间"));
  } else {
    callback();
  }
};

// 校验生效设备
const validSelectedDevices = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (state.formList[index].isDisable === false && !!state.formList[index].id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请选择生效设备"));
  } else {
    callback();
  }
};

const handleStatusChange = (val, index) => {
  state.formList[index].isEdit = true;
  if (!val) formRefs.value[index]?.clearValidate();
};

const handleEdit = (index) => {
  state.formList[index].isEdit = true;
  if (!state.formList[index].id) state.formList[index].isDisable = true;
  nextTick(() => {
    formRefs.value[index]?.clearValidate();
  });
};

const handleDel = (index) => {
  proxy.$modal
    .confirm("删除后已设置的设备重启后失效，是否确认删除？")
    .then((res) => {
      let submitData = {
        id: formList.value[index].id,
        type: 3,
      };
      console.log("提交数据:", submitData);
      // 调用接口
      loading.value = true;
      deleteLockScreenRule(submitData)
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            // state[!!type ? "formList2" : "formList"][index].isEdit = false;
            resetForm(0);
            proxy.$modal.msgSuccess("操作成功");
            // getData();
          }
        })
        .catch((err) => {
          // proxy.$modal.msgError("操作失败");
        })
        .finally(() => (loading.value = false));
    });
};

const handleCancel = (index) => {
  if (formRefs.value[index]) {
    formRefs.value[index].resetFields();
  }

  if (!state.formList[index].id) {
    resetForm(index);
  } else {
    getData();
  }
};

const submitForm = (index) => {
  console.log(formRefs.value[index]);
  if (state.formList[index].isDisable === false && !state.formList[index].id) {
    proxy.$modal.alert("请先开启启动计划再保存");
    return;
  }
  if (!formRefs.value[index]) return;
  nextTick(() => {
    formRefs.value[index]?.validate((valid) => {
      if (valid) {
        const { isDisable, selectedDevices, id, num, unit } =
          state[
            !state.formList[index].isDisable && !!state.formList[index].id
              ? "initFormList"
              : "formList"
          ][index];
        let submitData = {
          lockId: id,
          isDisable: state.formList[index].isDisable,
          deviceCodes: selectedDevices
            ? selectedDevices.split(",").filter(Boolean)
            : [""],
          num: num || 1,
          unit: unit || 1,
          type: 3,
        };
        console.log("提交数据:", submitData, JSON.stringify(submitData));
        // state.formList[index].isEdit = false;
        // 调用接口
        loading.value = true;
        lockScreenRuleBatch(submitData)
          .then((res) => {
            if (res.code === 200) {
              proxy.$modal.msgSuccess("操作成功");
              getData();
            }
          })
          .catch((err) => {
            // proxy.$modal.msgError("操作失败");
          })
          .finally(() => (loading.value = false));
      }
    });
  });
};

const resetForm = (index) => {
  state.formList[index] = {
    isEdit: null,
    id: "",
    isDisable: false,
    num: 1,
    unit: 1,
    selectedDevices: "",
    deviceCodes: [],
  };
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  gap: 0 5px;
}
.deviceLock {
  font-size: 14px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 15px 0;
  }

  &-add {
    border: 1px solid #e5e5e5;
    border-top: none;
    padding: 10px;
    color: rgb(152, 152, 152);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    &:hover {
      color: #7e96ae;
      background-color: #f8faff;
    }
  }

  &-item {
    border: 1px solid #e5e5e5;
  }

  &-content {
    padding: 10px 20px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_unedit {
      padding-bottom: 10px;
      :deep(.el-form-item--default) {
        margin-bottom: 0px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
  }

  &-footer {
    min-width: 500px;
    text-align: center;
  }
}
</style>