#!/bin/bash

# 前端部署脚本
# 用法: ./deploy.sh <版本号>
# 例如: ./deploy.sh 20250711-1

# 显示帮助信息
show_help() {
    echo "前端部署脚本"
    echo ""
    echo "用法:"
    echo "  $0 <版本号>"
    echo ""
    echo "参数:"
    echo "  版本号    Docker镜像的版本标签 (例如: 20250711-1)"
    echo ""
    echo "示例:"
    echo "  $0 20250711-1"
    echo ""
    echo "选项:"
    echo "  -h, --help    显示此帮助信息"
}

# 检查参数
if [ $# -eq 0 ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

VERSION=$1
HARBOR_REGISTRY="192.168.3.219/maintainapp"
IMAGE_NAME="maintainapp-frontend"
PROJECT_DIR="/home/<USER>/prj/maintainapp-frontned-deploy"

echo "========================================="
echo "开始部署前端应用"
echo "版本号: $VERSION"
echo "========================================="

# 1. 进入前端目录并更新代码
echo "1. 更新代码..."
cd "$PROJECT_DIR" || { echo "错误: 无法进入项目目录 $PROJECT_DIR"; exit 1; }
git pull || { echo "错误: Git pull 失败"; exit 1; }

# 2. 构建前端应用
echo "2. 构建前端应用..."
NODE_OPTIONS="--max-old-space-size=4096" yarn build:rela || { echo "错误: 构建失败"; exit 1; }

# 3. 准备Docker构建
echo "3. 准备Docker构建..."
cd "$PROJECT_DIR/docker" || { echo "错误: 无法进入docker目录"; exit 1; }
cp -r ../dist/ . || { echo "错误: 复制dist目录失败"; exit 1; }

# 4. 构建Docker镜像
echo "4. 构建Docker镜像..."
docker build -t "$IMAGE_NAME:$VERSION" . || { echo "错误: Docker构建失败"; exit 1; }

# 5. 标记镜像
echo "5. 标记镜像..."
docker tag "$IMAGE_NAME:$VERSION" "$HARBOR_REGISTRY/$IMAGE_NAME:$VERSION" || { echo "错误: 镜像标记失败"; exit 1; }

# 6. 推送到Harbor
echo "6. 推送到Harbor..."
docker push "$HARBOR_REGISTRY/$IMAGE_NAME:$VERSION" || { echo "错误: 推送失败"; exit 1; }

# 7. 清理临时文件
echo "7. 清理临时文件..."
rm -rf dist/

echo "========================================="
echo "部署完成！"
echo "镜像: $HARBOR_REGISTRY/$IMAGE_NAME:$VERSION"
echo "========================================="