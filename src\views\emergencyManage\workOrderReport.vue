<!-- views/emergency/workOrderReport.vue -->
<template>
  <div class="workReport app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>上传报告</span>
        </div>
      </template>

      <el-descriptions title="预案信息" border column="3">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案编号"
        >
          {{ workOrderData.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案类型"
        >
          {{ workOrderData.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案文件"
        >
          {{ workOrderData.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案名称"
        >
          {{ workOrderData.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          :span="2"
          label="预案内容"
        >
          {{ workOrderData.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="修改时间"
        >
          {{ workOrderData.updateTime }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ workOrderData.createTime }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form :model="workOrderData">
        <el-descriptions
          title="应急工单信息"
          border
          style="margin-top: 20px"
          column="2"
        >
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="应急工单编号"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="应急预案"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="应急工单类别"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单状态"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="应急工单描述"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理报告"
          >
            <el-form-item label="" prop="content">
              <el-upload
                ref="uploadRef"
                class="upload-demo"
                action="#"
                :auto-upload="false"
                :on-change="handleFileChange"
                :before-upload="beforeUpload"
                :limit="1"
              >
                <template #trigger>
                  <el-button type="primary">点击上传</el-button>
                </template>
                <template #tip>
                  <div class="el-upload__tip">
                    支持格式：doc、docx、xls、xlsx、txt，文件大小不超过20MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理人"
          >
            {{ workOrderData.number }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <!-- 备件表格 -->
      <div class="workReport-tit">关联备件</div>
      <el-table :data="ledgerList" border>
        <el-table-column
          label="备件编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="备件名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="消耗量"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceType"
        />
        <el-table-column
          label="消耗日期"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTag"
        />
      </el-table>

      <!-- 人员表格 -->
      <div class="workReport-tit">关联人员</div>
      <el-table :data="ledgerList" border>
        <el-table-column
          label="工号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceName"
        />
        <el-table-column
          label="关联时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceType"
        />
        <el-table-column
          label="报告"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceType"
        />
      </el-table>

      <div class="workReport-btns">
        <el-button type="primary" @click="handleSubmit">点击提交</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <div class="workReport-record">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role} 于 ${item.time} ${item.type}了${item.spareName}的现库存`
          }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
// 导入公共组件
import OrderBaseInfo from "@/views/emergencyManage/components/OrderBaseInfo.vue";
import RelatedPartsTable from "@/views/emergencyManage/components/RelatedPartsTable.vue";
import RelatedPersonnelTable from "@/views/emergencyManage/components/RelatedPersonnelTable.vue";

const router = useRouter();
const route = useRoute();

// 数据
const workOrderData = ref({});
const relatedParts = ref([]);
const relatedPersonnel = ref([]);
const selectedFile = ref(null);
const recordList = ref([
  {
    spareName: "备件1",
    type: "修改",
    name: "张三",
    role: "管理员",
    time: "2024-11-20 21:49:11",
  },
]);

// 通知相关的响应式变量
const notifyDialogVisible = ref(false);
const currentNotifyPerson = ref(null);

// 获取工单数据
const getWorkOrderData = () => {
  try {
    const workOrders = JSON.parse(
      localStorage.getItem("emergencyWorkOrders") || "[]"
    );
    const currentOrder = workOrders.find(
      (order) => order.orderId === route.query.id
    );

    if (currentOrder) {
      workOrderData.value = currentOrder;

      // 处理关联备件数据
      if (
        currentOrder.relatedPartsFull &&
        currentOrder.relatedPartsFull.length > 0
      ) {
        relatedParts.value = currentOrder.relatedPartsFull;
      } else if (currentOrder.relatedParts) {
        relatedParts.value = currentOrder.relatedParts
          .split("、")
          .map((partName) => ({
            partName: partName,
            partId: "-",
            quantity: "-",
            useDate: currentOrder.handleTime || "-",
          }));
      }

      // 处理关联人员数据，确保包含通知状态
      if (currentOrder.relatedPersonnelFull) {
        relatedPersonnel.value = currentOrder.relatedPersonnelFull.map(
          (person) => ({
            ...person,
            notified: person.notified || false,
          })
        );
      } else if (currentOrder.relatedPerson) {
        relatedPersonnel.value = currentOrder.relatedPerson
          .split("、")
          .map((name) => ({
            name: name,
            workerId: "-",
            department: "-",
            relateTime: currentOrder.handleTime || "-",
            notified: false,
          }));
      }
    } else {
      ElMessage.error("未找到工单数据");
      // router.back();
    }
  } catch (error) {
    console.error("获取工单数据失败:", error);
    ElMessage.error("获取数据失败");
  }
};

// 处理文件上传
const handleFileChange = (uploadFile) => {
  selectedFile.value = uploadFile.raw;
};

// 移除文件
const removeFile = () => {
  selectedFile.value = null;
};

// 处理通知按钮点击
const handleNotify = (person) => {
  currentNotifyPerson.value = person;
  notifyDialogVisible.value = true;
};

// 确认通知
const confirmNotify = async () => {
  try {
    const workOrders = JSON.parse(
      localStorage.getItem("emergencyWorkOrders") || "[]"
    );
    const index = workOrders.findIndex(
      (order) => order.orderId === workOrderData.value.orderId
    );

    if (index > -1) {
      const currentOrder = workOrders[index];

      // 更新通知状态
      if (currentOrder.relatedPersonnelFull) {
        currentOrder.relatedPersonnelFull =
          currentOrder.relatedPersonnelFull.map((person) => {
            if (person.workerId === currentNotifyPerson.value.workerId) {
              return { ...person, notified: true };
            }
            return person;
          });
      }

      // 检查是否所有人员都已通知
      const allNotified =
        currentOrder.relatedPersonnelFull?.every((person) => person.notified) ??
        false;

      // 如果所有人都已通知，更新工单的通知时间
      if (allNotified) {
        currentOrder.notifyTime = new Date().toISOString();

        // 修改状态流转逻辑
        if (currentOrder.handleTime && currentOrder.report) {
          currentOrder.status = "archived";
        }
      }

      // 更新本地数据
      workOrders[index] = currentOrder;
      localStorage.setItem("emergencyWorkOrders", JSON.stringify(workOrders));

      // 更新页面数据
      workOrderData.value = currentOrder;
      relatedPersonnel.value = currentOrder.relatedPersonnelFull || [];

      notifyDialogVisible.value = false;
      currentNotifyPerson.value = null;

      ElMessage.success(
        allNotified ? "所有人员已通知，工单状态已更新" : "通知成功"
      );
    }
  } catch (error) {
    console.error("通知发送失败:", error);
    ElMessage.error("通知发送失败");
  }
};

// 保存报告
const handleSave = async () => {
  try {
    if (!selectedFile.value) {
      ElMessage.warning("请先上传报告文件");
      return;
    }

    const workOrders = JSON.parse(
      localStorage.getItem("emergencyWorkOrders") || "[]"
    );
    const index = workOrders.findIndex(
      (order) => order.orderId === workOrderData.value.orderId
    );

    if (index > -1) {
      const currentOrder = workOrders[index];

      // 修改状态流转逻辑
      let newStatus;
      if (currentOrder.handleTime) {
        if (currentOrder.notifyTime) {
          // 如果已处理且已通知，上传报告后直接归档
          newStatus = "archived";
        } else {
          // 如果已处理但未通知，上传报告后变为待归档
          newStatus = "waiting_archive";
        }
      } else {
        // 如果未处理，上传报告后保持待处理状态
        newStatus = "pending";
      }

      workOrders[index] = {
        ...currentOrder,
        status: newStatus,
        report: selectedFile.value.name,
        reportUploadTime: new Date().toISOString(),
      };

      localStorage.setItem("emergencyWorkOrders", JSON.stringify(workOrders));

      let message;
      switch (newStatus) {
        case "archived":
          message = "报告上传成功，工单已归档";
          break;
        case "waiting_archive":
          message = "报告上传成功，工单状态已更新为待归档";
          break;
        default:
          message = "报告上传成功";
      }
      ElMessage.success(message);

      router.back();
    }
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

// 取消
const handleCancel = () => {
  router.back();
};

onMounted(() => {
  getWorkOrderData();
});
</script>

<style scoped lang="scss">
.workReport {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}

.taskInfo {
  padding: 20px;
}

.report-section {
  margin-top: 20px;
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 4px;

  .taskInfo-tit {
    font-size: 18px;
    font-weight: bold;
  }

  .upload-area {
    display: flex;
    align-items: center;
    margin-top: 20px;

    .upload-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .file-info {
      margin-left: 20px;
      display: flex;
      align-items: center;
      gap: 10px;
      span {
        display: inline-block;
        width: 100px;
      }
    }
  }
}

.notified-text {
  color: #67c23a;
  font-size: 14px;
}

.taskInfo-btns {
  padding: 20px;
  text-align: center;
  margin-top: 20px;
}
</style>