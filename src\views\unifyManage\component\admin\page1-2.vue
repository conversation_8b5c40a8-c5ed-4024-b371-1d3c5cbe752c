<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">
          <div class="block-title_text">辖区内资产覆盖率</div>
        </div>
        <div class="battery">
          <span>{{
            (objData.assetCoverageStatistics &&
              objData.assetCoverageStatistics.coverage) ||
            0
          }}</span>
        </div>
      </div>
      <div class="col1-block4">
        <div class="block-title">
          <div class="block-title_text">辖区内资产总数</div>
        </div>
        <div class="table block">
          <div class="table-row opt-title">
            <div class="table-row_name table-row_head">校区名称</div>
            <div class="table-row_count table-row_head">资产总数</div>
          </div>
          <div
            class="table-row data"
            v-for="item in objData.corpAssets"
            :key="item.corpName"
          >
            <div class="table-row_name">{{ item.corpName }}</div>

            <div class="table-row_count">
              {{ item.total > 99999 ? "99999+" : item.total }}
            </div>
          </div>
        </div>
      </div>
      <div class="col3-block3">
        <!-- <div class="block-title" style="margin-top: 0.5vw">排行榜</div> -->
        <div class="table2">
          <div class="subtitle">辖区内备件使用排行榜</div>
          <div class="table-row opt-title">
            <div class="table-row_number table-row_head">序号</div>
            <div class="table-row_type table-row_head">备件名称</div>
            <div class="table-row_count table-row_head">使用量</div>
          </div>
          <div
            class="table-data"
            @mouseenter="setAnimate(false)"
            @mouseleave="setAnimate(true)"
          >
            <div :class="{ 'table-data_inner': animate }">
              <div
                class="table-row data"
                v-for="(item, index) in objData.sparepartsUseStatistics"
                :key="index"
                :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
              >
                <div class="table-row_number">{{ item.rank }}</div>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="String(item.sparepartsName)"
                  placement="bottom"
                  :enterable="false"
                  :hide-after="0"
                >
                  <div class="table-row_type">{{ item.sparepartsName }}</div>
                </el-tooltip>
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  :content="String(item.userNum)"
                  placement="bottom"
                  :enterable="false"
                  :hide-after="0"
                >
                  <div class="table-row_count">
                    {{ item.userNum > 99999 ? "99999+" : item.userNum }}
                  </div>
                </el-tooltip>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div class="col col2">
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list"
          :key="index"
        >
          <div>
            <span>{{ item.num }}</span
            >个
          </div>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="col2-block2">
        <div>
          <Echarts
            id="mapData"
            width="100%"
            height="100%"
            :fullOptions="mapOption"
            :mapConfig="{ name: 'GZ', data: getGuangZhouData }"
          />
        </div>
      </div>
      <div class="col2-block3">
        <div class="flex">
          <div class="block echart-item">
            <div class="subtitle">辖区内应用使用时长排行榜</div>
            <Echarts
              id="barData2"
              width="100%"
              height="10vw"
              :fullOptions="barOption2"
            />
          </div>
          <div class="block echart-item">
            <div class="subtitle">辖区内信息资产类别Top5</div>
            <Echarts
              id="barData"
              width="100%"
              height="10vw"
              :fullOptions="barOption"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1" :class="curAssetsTab == 0 ? 'left' : 'right'">
        <div class="col3-block1_left" @click="curAssetsTab = 0"></div>
        <div class="col3-block1_right" @click="curAssetsTab = 1"></div>
      </div>
      <div class="col3-block2" v-if="curAssetsTab == 0">
        <!-- <div class="block-title" style="margin-top: 0.5vw">
          辖区内硬件资产状况
        </div> -->
        <div class="flex flex-bg">
          <div class="block big-block">
            <div class="block-left">
              <div class="subtitle">使用年限占比</div>
              <div style="position: relative">
                <img
                  class="pieData6-bg"
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie6-bg.png"
                />
                <Echarts
                  ref="pieData6Ref"
                  id="pieData6"
                  class="echart"
                  width="100%"
                  height="7.5vw"
                  :fullOptions="pieOption6"
                />
                <div class="pieData6-list">
                  <div
                    class="pieData6-item"
                    v-for="(item, index) in pieOption6.options.series[0].data"
                    :data-index="index"
                    :key="index"
                  >
                    <div class="pieData6-name">
                      <div
                        :style="{
                          backgroundColor: item.itemStyle.color,
                          border: `1px solid ${item.itemStyle.borderColor}`,
                        }"
                        class="item-dot"
                      >
                        <i
                          :style="{
                            backgroundColor: item.itemStyle.borderColor,
                          }"
                        ></i>
                      </div>
                      {{ item.name }}
                    </div>
                    <div class="pieData6-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block big-block">
            <div class="block-left">
              <div class="subtitle">硬盘大小占比</div>
              <div style="position: relative">
                <img
                  class="pieData10-bg"
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
                />
                <Echarts
                  ref="pieData10Ref"
                  id="pieData10"
                  class="echart"
                  width="100%"
                  height="7.5vw"
                  :fullOptions="pieOption10"
                />
                <div class="pieData10-list">
                  <div
                    class="pieData10-item"
                    v-for="(item, index) in pieOption10.options.series[0].data"
                    :data-index="index"
                  >
                    <div class="pieData10-name">
                      <div
                        :style="{
                          backgroundColor: item.itemStyle.bgColor,
                        }"
                        class="item-dot"
                      ></div>
                      {{ item.name }}
                    </div>
                    <div class="pieData10-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="block big-block">
            <div class="block-left">
              <div class="subtitle">硬盘大小占比</div>
              <Echarts
                id="pieData10"
                width="100%"
                height="6.4vw"
                :fullOptions="pieOption10"
              />
            </div>
          </div> -->
        </div>
        <div class="flex">
          <div class="block block-bg-none">
            <div class="subtitle">设备类型占比</div>
            <div style="position: relative">
              <img
                class="pieData8-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
              />
              <Echarts3D
                id="pieData8"
                width="100%"
                height="10vw"
                ref="pieData8Ref"
                pieTipBottom="6.3vw"
                :useTip="true"
                :fullOptions="pieOption8"
                :data3D="data3D8"
              />
              <div class="pieData8-list">
                <div
                  class="pieData8-item"
                  v-for="(item, index) in data3D8"
                  :data-index="index"
                >
                  <div class="pieData8-name">
                    <div
                      :style="{
                        backgroundColor: item.itemStyle.color,
                        border: `1px solid ${item.itemStyle.color}`,
                      }"
                      class="item-dot"
                    >
                      <i
                        :style="{
                          backgroundColor: item.itemStyle.color,
                        }"
                      ></i>
                    </div>
                    <div class="name" :title="item.name">{{ item.name }}</div>
                  </div>
                  <div class="pieData8-count">
                    {{ item.value > 999 ? "999+" : item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block block-bg-none">
            <div class="subtitle">端口类型占比</div>
            <div style="position: relative">
              <img
                class="pieData9-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
              />
              <Echarts3D
                id="pieData9"
                width="100%"
                height="10vw"
                pieTipBottom="6.3vw"
                ref="pieData9Ref"
                :useTip="true"
                :fullOptions="pieOption9"
                :data3D="data3D9"
              />
              <div class="pieData9-list">
                <div
                  class="pieData9-item"
                  v-for="(item, index) in data3D9"
                  :data-index="index"
                >
                  <div class="pieData9-name">
                    <div
                      :style="{
                        backgroundColor: item.itemStyle.color,
                        border: `1px solid ${item.itemStyle.color}`,
                      }"
                      class="item-dot"
                    >
                      <i
                        :style="{
                          backgroundColor: item.itemStyle.color,
                        }"
                      ></i>
                    </div>
                    <div class="name" :title="item.name">{{ item.name }}</div>
                  </div>
                  <div class="pieData9-count">
                    {{ item.value > 999 ? "999+" : item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex block-mgt">
          <div class="block block-bg-none">
            <div class="subtitle">内存占比</div>
            <div style="position: relative">
              <img
                class="pieData11-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie5-bg.png"
              />
              <Echarts
                ref="pieData11Ref"
                id="pieData11"
                class="echart"
                width="100%"
                height="7.5vw"
                :fullOptions="pieOption11"
              />
              <div class="pieData11-list">
                <div
                  class="pieData11-item"
                  v-for="(item, index) in pieOption11.options.series[0].data"
                  :data-index="index"
                >
                  <div class="pieData11-name">
                    <div
                      :style="{
                        backgroundColor: item.itemStyle.bgColor,
                      }"
                      class="item-dot"
                    ></div>
                    {{ item.name }}
                  </div>
                  <div class="pieData11-count">
                    {{ item.value > 999 ? "999+" : item.value }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block block-bg-none">
            <div class="subtitle">故障时长占比</div>
            <div style="position: relative">
              <img
                class="pieData7-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
              />
              <Echarts
                ref="pieData7Ref"
                id="pieData7"
                class="echart"
                width="100%"
                height="7.5vw"
                :fullOptions="pieOption7"
              />
              <div class="pieData7-list">
                <div
                  class="pieData7-item"
                  v-for="(item, index) in pieOption7.options.series[0].data"
                  :data-index="index"
                >
                  <div
                    class="pieData7-bar"
                    :style="{ backgroundColor: item.itemStyle.bgColor2 }"
                  >
                    <div
                      class="pieData7-subBar"
                      :style="{ backgroundColor: item.itemStyle.bgColor }"
                    ></div>
                  </div>
                  <div>
                    <div class="pieData7-name">
                      {{ item.name }}
                    </div>
                    <div class="pieData7-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="block block-3d-h">
            <div class="subtitle">品牌占比</div>
            <img
              class="pieData12-bg"
              src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
            />
            <Echarts3D
              id="pieData12"
              width="100%"
              height="10vw"
              pieTipBottom="6.3vw"
              ref="pieData12Ref"
              :useTip="true"
              :fullOptions="pieOption12"
              :data3D="data3D12"
            />
            <div class="pieData12-list">
              <div
                class="pieData12-item"
                v-for="(item, index) in data3D12"
                :data-index="index"
                :key="index"
              >
                <div class="pieData12-name">
                  <div
                    :style="{
                      backgroundColor: item.itemStyle.color,
                      border: `1px solid ${item.itemStyle.color}`,
                    }"
                    class="item-dot"
                  >
                    <i
                      :style="{
                        backgroundColor: item.itemStyle.color,
                      }"
                    ></i>
                  </div>
                  <div class="name" :title="item.name">{{ item.name }}</div>
                </div>
                <div class="pieData12-count">
                  {{ item.value > 999 ? "999+" : item.value }}
                </div>
              </div>
            </div>
          </div>
          <div class="block block-3d-h">
            <div class="subtitle">系统版本占比</div>
            <img
              class="pieData15-bg"
              src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
            />
            <Echarts3D
              id="pieData15"
              width="100%"
              height="10vw"
              pieTipBottom="6.3vw"
              ref="pieData15Ref"
              :useTip="true"
              :fullOptions="pieOption15"
              :data3D="data3D15"
            />
            <div class="pieData15-list">
              <div
                class="pieData15-item"
                v-for="(item, index) in data3D15"
                :data-index="index"
                :key="index"
              >
                <div class="pieData15-name">
                  <div
                    :style="{
                      backgroundColor: item.itemStyle.color,
                      border: `1px solid ${item.itemStyle.color}`,
                    }"
                    class="item-dot"
                  >
                    <i
                      :style="{
                        backgroundColor: item.itemStyle.color,
                      }"
                    ></i>
                  </div>
                  <div class="name" :title="item.name">{{ item.name }}</div>
                </div>
                <div class="pieData15-count">
                  {{ item.value > 999 ? "999+" : item.value }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col3-block2 other" v-if="curAssetsTab == 1">
        <!-- <div class="block-title" style="margin-top: 0.5vw">
          辖区内其它资产状况
        </div> -->
        <div class="flex flex-bg">
          <div class="block big-block">
            <div class="block-left">
              <div class="subtitle">网络资产状况</div>
              <div style="position: relative">
                <img
                  class="pieData5-bg"
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie6-bg.png"
                />
                <Echarts
                  ref="pieData5Ref"
                  id="pieData5"
                  class="echart"
                  width="100%"
                  height="7.5vw"
                  :fullOptions="pieOption5"
                />
                <div class="pieData5-list">
                  <div class="subtitle">共入库<span>6</span>个网站地址</div>
                  <div
                    class="pieData5-item"
                    v-for="(item, index) in pieOption5.options.series[0].data"
                    :data-index="index"
                    :key="index"
                  >
                    <div class="pieData5-name">
                      <div
                        :style="{
                          backgroundColor: item.itemStyle.color,
                          border: `1px solid ${item.itemStyle.borderColor}`,
                        }"
                        class="item-dot"
                      >
                        <i
                          :style="{
                            backgroundColor: item.itemStyle.borderColor,
                          }"
                        ></i>
                      </div>
                      {{ item.name }}
                    </div>
                    <div class="pieData5-count">
                      {{ item.value > 999 ? "999+" : item.value }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block big-block">
            <div class="block-left">
              <div class="subtitle">软件使用状况</div>
              <div style="position: relative">
                <img
                  class="pieData4-bg"
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
                />
                <Echarts
                  ref="pieData4Ref"
                  id="pieData4"
                  class="echart"
                  width="100%"
                  height="7.5vw"
                  :fullOptions="pieOption4"
                />
                <div class="pieData4-list">
                  <div
                    class="pieData4-item"
                    v-for="(item, index) in pieOption4.options.series[0].data"
                    :data-index="index"
                    :key="index"
                  >
                    <div
                      class="pieData4-bar"
                      :style="{ backgroundColor: item.itemStyle.bgColor2 }"
                    >
                      <div
                        class="pieData4-subBar"
                        :style="{ backgroundColor: item.itemStyle.bgColor }"
                      ></div>
                    </div>
                    <div>
                      <div class="pieData4-name">
                        {{ item.name }}
                      </div>
                      <div class="pieData4-count">
                        {{ item.value > 999 ? "999+" : item.value }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div class="block block-3d-h block-bg-none">
            <div class="subtitle">软件库分类占比</div>
            <img
              class="pieData2-bg"
              src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
            />
            <Echarts3D
              id="pieData2"
              width="100%"
              height="10vw"
              pieTipBottom="6.3vw"
              ref="pieData2Ref"
              :useTip="true"
              :fullOptions="pieOption2"
              :data3D="data3D2"
            />
            <div class="pieData2-list">
              <div
                class="pieData2-item"
                v-for="(item, index) in data3D2"
                :data-index="index"
                :key="index"
              >
                <div class="pieData2-name">
                  <div
                    :style="{
                      backgroundColor: item.itemStyle.color,
                      border: `1px solid ${item.itemStyle.color}`,
                    }"
                    class="item-dot"
                  >
                    <i
                      :style="{
                        backgroundColor: item.itemStyle.color,
                      }"
                    ></i>
                  </div>
                  <div class="name" :title="item.name">{{ item.name }}</div>
                </div>
                <div class="pieData2-count">
                  {{ item.value > 999 ? "999+" : item.value }}
                </div>
              </div>
            </div>
          </div>
          <div class="block block-3d-h block-bg-none">
            <div class="subtitle">软件许可证到期时间占比</div>
            <img
              class="pieData2-bg"
              src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
            />
            <Echarts3D
              id="pieData3"
              width="100%"
              height="10vw"
              pieTipBottom="6.3vw"
              ref="pieData3Ref"
              :useTip="true"
              :fullOptions="pieOption3"
              :data3D="data3D3"
            />
            <div class="pieData3-list">
              <div
                class="pieData3-item"
                v-for="(item, index) in data3D3"
                :data-index="index"
                :key="index"
              >
                <div class="pieData3-name">
                  <div
                    :style="{
                      backgroundColor: item.itemStyle.color,
                      border: `1px solid ${item.itemStyle.color}`,
                    }"
                    class="item-dot"
                  >
                    <i
                      :style="{
                        backgroundColor: item.itemStyle.color,
                      }"
                    ></i>
                  </div>
                  <div class="name" :title="item.name">{{ item.name }}</div>
                </div>
                <div class="pieData3-count">
                  {{ item.value > 999 ? "999+" : item.value }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flex">
          <div class="block block-size-90">
            <div class="subtitle range">
              数据信息资产状况
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  :class="curRange1 == 2 ? 'active' : ''"
                  @click="handleRange(0, 2)"
                >
                  月
                </div>
                <div
                  class="range-tabs_item"
                  :class="curRange1 == 3 ? 'active' : ''"
                  @click="handleRange(0, 3)"
                >
                  年
                </div>
              </div>
            </div>
            <Echarts
              id="lineData"
              width="100%"
              height="5.3vw"
              :fullOptions="lineOption"
            />
          </div>
          <div class="block block-right">
            <div class="item-opt center">
              {{ optList1[0].title }}
              <div class="item-opt_row">
                <span>{{ optList1[0].total || 0 }}</span>
                条数据
              </div>
            </div>
            <div class="item-opt">
              {{ optList1[1].title }}
              <div class="item-opt_row">
                <span>{{ optList1[1].tip }}</span>
                <div
                  :class="
                    optList1[1].num > 0
                      ? 'up'
                      : optList1[1].num < 0
                      ? 'low'
                      : 'none'
                  "
                >
                  {{ optList1[1].ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex flex-bg1">
          <div class="block block-size-90">
            <div class="subtitle range">
              知识产权资产状况
              <div class="range-tabs">
                <div
                  class="range-tabs_item"
                  :class="curRange2 == 2 ? 'active' : ''"
                  @click="handleRange(1, 2)"
                >
                  月
                </div>
                <div
                  class="range-tabs_item"
                  :class="curRange2 == 3 ? 'active' : ''"
                  @click="handleRange(1, 3)"
                >
                  年
                </div>
              </div>
            </div>
            <Echarts
              id="lineData2"
              width="100%"
              height="5.3vw"
              :fullOptions="lineOption2"
            />
          </div>
          <div class="block block-right">
            <div
              class="item-opt"
              v-for="(item, index) in optList2"
              :key="index"
            >
              {{ item.title }}
              <div class="item-opt_row">
                <span>{{ item.tip }}</span>
                <div
                  :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'"
                >
                  {{ item.ratio.replace(/-/g, "") }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <!-- <div class="block-title">辖区网络信息</div> -->
        <div class="flex" style="padding-top: 1vw; padding-bottom: 0.6vw">
          <div class="network up">
            平均上行网速<br />
            <span>{{ networkList.avgUp || 0 }}</span>
            <span class="unit">MB/S</span>
          </div>
          <div class="network up">
            峰值上行网速<br />
            <span>{{ networkList.peakUp || 0 }}</span>
            <span class="unit">MB/S</span>
          </div>
        </div>
        <div class="flex" style="padding-top: 0.6vw; padding-bottom: 0.6vw">
          <div class="network low">
            平均下行网速<br />
            <span>{{ networkList.avgDown || 0 }}</span>
            <span class="unit">MB/S</span>
          </div>
          <div class="network low">
            峰值下行网速<br />
            <span>{{ networkList.peakDown || 0 }}</span>
            <span class="unit">MB/S</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import Echarts3D from "@/components/Echarts/index3D-2.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getAdminSchoolStatistics,
  getAdminWorkOrderStatistics,
  getAdminAssetsStatistics,
  getAdminMaintainCountStatistics,
  getAdminWorkOrderTimeStatistics,
  getAdminKnowledgeStatistics,
  getAdminCorpGps,
} from "@/api/unify";
import getGuangZhouData from "@/api/gz.json";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData5Ref: null,
  pieData6Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData15Ref: null,
  scrolltimer: null,
  animate: false,
  curRange1: 2,
  curRange2: 2,
  networkList: {
    avgUp: 0,
    avgDown: 0,
    peakUp: 0,
    peakDown: 0,
  },
  curAssetsTab: 0,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  scrolltimer,
  animate,
  curRange1,
  curRange2,
  networkList,
  curAssetsTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  curTime,
  timer,
  timer2,
  pieData2Ref,
  pieData3Ref,
  pieData4Ref,
  pieData5Ref,
  pieData6Ref,
  pieData7Ref,
  pieData8Ref,
  pieData9Ref,
  pieData10Ref,
  pieData11Ref,
  pieData12Ref,
  pieData13Ref,
  pieData15Ref,
} = toRefs(state);

const col2_block1_list = ref([
  { name: "硬件资产", num: 0 },
  { name: "软件资产", num: 4 },
  { name: "数据信息资产", num: 0 },
  { name: "网络资产", num: 6 },
  { name: "知识产权", num: 5 },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "辖区开机率", num: "0", unit: "%" },
  { name: "总报废数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "辖区运维人员总人数", num: "0", unit: "人" },
  { name: "今日总执勤", num: "0", unit: "人" },
  { name: "今日总轮休", num: "0", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "知识库", tip: "", total: 0, ratio: "0%", num: 0 },
  { title: "对比上个月", tip: "个数同比", total: 0, ratio: "0", num: 0 },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "知识产权个数同比",
    total: 0,
    ratio: "-50%",
    num: -50,
  },
  {
    title: "本年度对比上年度",
    tip: "知识产权个数同比",
    total: 5,
    ratio: "300%",
    num: 300,
  },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  { title: "今日工单平均处理时间", total: 0, ratio: "0%", num: 0 },
  { title: "合计工单平均处理时间", total: 0, fq: "0天/次" },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const optList6 = ref([
  { name: "数据信息资产", number: "01" },
  { name: "网络资产", number: "02" },
  { name: "硬件资产", number: "03" },
  { name: "知识产权资产", number: "04" },
  { name: "软件资产", number: "05" },
]);

const objData = ref({
  icCover: true,
  sparepartsUseStatistics: [
    // { rank: 1, sparepartsName: "打印机", userNum: 1 },
    // { rank: 2, sparepartsName: "打印机", userNum: 1 },
    // { rank: 3, sparepartsName: "打印机", userNum: 1 },
    // { rank: 4, sparepartsName: "打印机", userNum: 1 },
    // { rank: 5, sparepartsName: "打印机", userNum: 1 },
  ],
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//广州市地图配置
const mapFontSize = Math.max(10, (window.innerWidth / 1920) * 12);
const mapFontSizeEmphasis = Math.max(10, (window.innerWidth / 1920) * 14);
const mapBgWidth = Math.max(10, (window.innerWidth / 1920) * 90);
const mapBgHeight = Math.max(10, (window.innerWidth / 1920) * 55);
const mapBgPadding = [
  -Math.max(10, (window.innerWidth / 1920) * 10),
  Math.max(10, (window.innerWidth / 1920) * 20),
  0,
  -Math.max(10, (window.innerWidth / 1920) * 65),
];
const mapBgSymbolSize = [
  Math.max(10, (window.innerWidth / 1920) * 90),
  Math.max(10, (window.innerWidth / 1920) * 50),
];
const mapBgOffset = [
  Math.min(10, (window.innerWidth / 1920) * 2),
  -Math.min(10, (window.innerWidth / 1920) * 4),
];
const mapOption = ref({
  options: {
    tooltip: {
      show: true,
    },
    geo: {
      show: true,
      map: "GZ",
      zoom: 1.2,
      aspectScale: 1,
      tooltip: {
        show: false,
        trigger: "item",
      },
      label: {
        show: true,
        color: "#fff",
        fontSize: "70%",
        formatter: (params) => {
          return `{bg|}{name|${params.name}}`;
        },
        // 定义富文本样式
        rich: {
          bg: {
            // 关键：通过 backgroundColor 的 image 属性设置背景图
            backgroundColor: {
              image:
                "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/city-bg.png", // 背景图 URL
            },
            width: mapBgWidth, // 背景图宽度
            height: mapBgHeight, // 背景图高度
          },
          name: {
            fontSize: mapFontSize,
            padding: mapBgPadding, // 调整文字位置
          },
        },
      },
      itemStyle: {
        normal: {
          opacity: 0, //图形透明度 0 - 1
          borderColor: "yellow", //图形的描边颜色
          // borderColor: "#00c6e8", //图形的描边颜色
          borderWidth: 1, //描边线宽。为 0 时无描边。
          borderType: "solid", //柱条的描边类型，默认为实线，支持 'solid', 'dashed', 'dotted'。
          areaColor: "transparent",
          // areaColor: "rgba(4, 46, 101, 0)", //图形的颜色 #eee
        },
      },
      emphasis: {
        label: {
          color: "#fff",
        },
        itemStyle: {
          opacity: 0,
        },
      },
    },
    series: [
      {
        tooltip: {
          show: true,
          trigger: "item",
          backgroundColor: "transparent",
          borderColor: "transparent",
          formatter: function (params) {
            // console.log(params);
            let { name, detail } = params.data;
            return `
              <div class="tooltip-bg">
                <div class="tooltip-title">${
                  name.length > 11
                    ? name.substring(0, 5) +
                      "..." +
                      name.substring(name.length - 5, name.length)
                    : name
                }</div>
                <div class="tooltip-row row1">
                  <div class="tooltip-label">资产总数：</div>
                  <div class="tooltip-value">${detail.zczs}</div>
                </div>
                <div class="tooltip-row row2">
                  <div class="tooltip-label">在线率：</div>
                  <div class="tooltip-value">${detail.zxl}</div>
                </div>
              </div>
            `;
          }, // 可选：自定义提示内容
        },
        name: "校区标记",
        map: "GZ",
        type: "scatter",
        coordinateSystem: "geo",
        symbolSize: mapBgSymbolSize,
        symbol: `image://https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/school-bg.png`,
        label: {
          show: true,
          color: "#fff",
          fontSize: mapFontSize,
          offset: mapBgOffset,
          formatter: function (params) {
            let { name } = params.data;
            return `${
              name.length > 4
                ? name.substring(0, 2) +
                  "..." +
                  name.substring(name.length - 2, name.length)
                : name
            }`;
          },
        },
        emphasis: {
          label: {
            fontSize: mapFontSizeEmphasis,
          },
        },
        data: [
          {
            name: "云天数据应用端",
            detail: {
              zczs: 1,
              zxl: "0.00%",
            },
            value: [113.31954, 23.18037],
          },
        ],
      },
    ],
  },
});

//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: 8,
        // 关键配置：使用 formatter 实现换行
        formatter: function (value) {
          // 每4个字符换行（可根据需要调整）
          return value.length > 3 ? value.substring(0, 3) + "..." : value;
        },
        // 设置宽度和高度
        width: 40, // 固定宽度
        overflow: "break", // 超出换行
        interval: 0, // 显示所有标签
        rotate: 20,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: 10,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1, // 从上到下的渐变
            colorStops: [
              { offset: 0, color: "rgba(81, 130, 255, 1)" }, // 起始颜色（顶部）
              { offset: 0.02, color: "rgba(81, 130, 255, 1)" }, // 2% 处保持颜色不变
              { offset: 0.021, color: "rgba(22, 88, 255, 0.20)" }, // 2% 后立即切换到深色
              { offset: 1, color: "rgba(22, 88, 255, 0.20)" }, // 剩余部分保持深色
            ],
          },
          // 鼠标经过的时候
          emphasis: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(0, 240, 255, 1)" }, // 顶部 2% 悬停色
                { offset: 0.02, color: "rgba(0, 240, 255, 1)" },
                { offset: 0.021, color: "rgba(0,240,255,0.3)" }, // 保持底部颜色不变
                { offset: 1, color: "rgba(0,240,255,0.3)" },
              ],
            },
          },
        },
      },
    ],
  },
});

//应用使用时长TOP5柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "15%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: 8,
        // 关键配置：使用 formatter 实现换行
        formatter: function (value) {
          // 每4个字符换行（可根据需要调整）
          return value.length > 3 ? value.substring(0, 3) + "..." : value;
        },
        // 设置宽度和高度
        width: 40, // 固定宽度
        overflow: "break", // 超出换行
        interval: 0, // 显示所有标签
        rotate: 20,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: 10,
        formatter: "{value} h",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1, // 从上到下的渐变
            colorStops: [
              { offset: 0, color: "rgba(81, 130, 255, 1)" }, // 起始颜色（顶部）
              { offset: 0.02, color: "rgba(81, 130, 255, 1)" }, // 2% 处保持颜色不变
              { offset: 0.021, color: "rgba(22, 88, 255, 0.20)" }, // 2% 后立即切换到深色
              { offset: 1, color: "rgba(22, 88, 255, 0.20)" }, // 剩余部分保持深色
            ],
          },
          // 鼠标经过的时候
          emphasis: {
            color: {
              type: "linear",
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                { offset: 0, color: "rgba(0, 240, 255, 1)" }, // 顶部 2% 悬停色
                { offset: 0.02, color: "rgba(0, 240, 255, 1)" },
                { offset: 0.021, color: "rgba(0,240,255,0.3)" }, // 保持底部颜色不变
                { offset: 1, color: "rgba(0,240,255,0.3)" },
              ],
            },
          },
        },
      },
    ],
  },
});

//标签使用频率饼图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//系统版本占比饼图
const pieColor15 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D15 = ref([]);
const pieOption15 = ref({
  options: {
    legend: {
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

//资产类别占比饼图
const pieOption = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc", "#b3a855"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "65%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "70%",
              lineHeight: 20,
            },
            b: {
              color: "#6889de",
              fontSize: "60%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const data3D2 = ref([
  {
    name: "操作系统",
    value: 160,
    proportion: "35.70%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#0783FAFF",
    },
  },
  {
    name: "教学系统",
    value: 144,
    proportion: "32.15%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#FFD15CFF",
    },
  },
  {
    name: "其他软件",
    value: 144,
    proportion: "32.15%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#20E6A4FF",
    },
  },
]);
const pieOption2 = ref({
  options: {
    legend: {
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 软件到期时间饼图（假数据）
const data3D3 = ref([
  {
    name: "1年以内",
    value: 160,
    proportion: "27.00%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#0783FAFF",
    },
  },
  {
    name: "1-3年",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#FFD15CFF",
    },
  },
  {
    name: "3-5年",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#20E6A4FF",
    },
  },
  {
    name: "5年以上",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#07D1FAFF",
    },
  },
]);
const pieOption3 = ref({
  options: {
    legend: {
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["55%", "65%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 9,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 7,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "浏览器",
            value: 100,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "办公类",
            value: 150,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "教学类",
            value: 209,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
          {
            name: "娱乐类",
            value: 188,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["22%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 10,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 8,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "办公平台",
            value: 100,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "教育平台",
            value: 200,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "门户网站",
            value: 300,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
        ],
      },
    ],
  },
});

// 创建条纹图案
function createStripePattern(color) {
  const canvas = document.createElement("canvas");
  canvas.width = 5;
  canvas.height = 5;
  const ctx = canvas.getContext("2d");

  // 绘制条纹
  ctx.strokeStyle = color;
  ctx.lineWidth = 1.5;
  ctx.beginPath();
  ctx.moveTo(0, 0);
  ctx.lineTo(6, 6);
  ctx.stroke();

  return canvas;
}

const initPieFunc = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: 0, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: 0,
        dataIndex: index,
      });
    });
  });
};

const initPieFunc3D = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: index, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: index,
        dataIndex: index,
      });
    });
  });
};

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["47%", "64%"],
        center: ["22%", "52%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 10,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 8,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "一年以内",
            value: 0,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "1-3年",
            value: 0,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "3-5年",
            value: 0,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
          {
            name: "5年以上",
            value: 0,
            itemStyle: { color: "rgba(255,255,0,0.3)", borderColor: "#ffff00" },
          },
        ],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["52%", "60%"],
        center: ["22%", "52%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 9,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 7,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "1天以内",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "1-5天",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "5-10天",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "10天以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieColor8 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D8 = ref([]);
const pieOption8 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产端口类型占比饼图
const pieColor9 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D9 = ref([]);
const pieOption9 = ref({
  options: {
    legend: {
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["52%", "60%"],
        center: ["22%", "52%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 9,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 7,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "128GB以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "128GB-256GB",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "257GB-512GB",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "513GB-1TB",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
          {
            name: "1TB以上",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["22%", "52%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: 10,
              lineHeight: 14,
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: 8,
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "8G以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "8G-16G",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "16G-32G",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "32G-64G",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "64G及以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieColor12 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D12 = ref([]);
const pieOption12 = ref({
  options: {
    legend: {
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-20%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 180,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产内故障资产使用年限占比饼图
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
        margin: 5,
        formatter: function (value) {
          let val = value;
          if (curRange1.value == 2 && value) {
            val = value.split("-")[1] + "月";
          }
          return val;
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      bottom: "25%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
          borderColor: "#000",
          borderWidth: 1,
          shadowColor: "rgba(0, 0, 0, 0.5)",
          shadowBlur: 5,
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 5,
        emphasis: {
          itemStyle: {
            borderColor: "#000",
            borderWidth: 1,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            shadowBlur: 10,
          },
        },
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
          borderColor: "#000",
          borderWidth: 1,
          shadowColor: "rgba(0, 0, 0, 0.5)",
          shadowBlur: 5,
        },
        lineStyle: {
          color: "#009bab",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 5,
        emphasis: {
          itemStyle: {
            borderColor: "#000",
            borderWidth: 1,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            shadowBlur: 10,
          },
        },
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
        margin: 5,
        formatter: function (value) {
          let val = value;
          if (curRange1.value == 2 && value) {
            val = value.split("-")[1] + "月";
          }
          return val;
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: 6,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      bottom: "25%",
      right: "5%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
          borderColor: "#000",
          borderWidth: 1,
          shadowColor: "rgba(0, 0, 0, 0.5)",
          shadowBlur: 5,
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 5,
        emphasis: {
          itemStyle: {
            borderColor: "#000",
            borderWidth: 1,
            shadowColor: "rgba(0, 0, 0, 0.5)",
            shadowBlur: 10,
          },
        },
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        rotate: 0,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计2
const lineOption4 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [42],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//运维考勤打卡情况
const lineOption5 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

function setAnimate(flag) {
  if (flag) {
    play();
  } else {
    setTimeout(() => {
      state.animate = false;
    }, 1000);
    window.clearInterval(state.scrolltimer);
  }
}

function play() {
  if (objData.value.sparepartsUseStatistics.length >= 5) {
    state.scrolltimer = setInterval(() => {
      let obj = JSON.parse(
        JSON.stringify(objData.value.sparepartsUseStatistics[0])
      );
      objData.value.sparepartsUseStatistics.push(obj);

      state.animate = true;
      setTimeout(() => {
        objData.value.sparepartsUseStatistics.shift();
        state.animate = false;
      }, 1000);
    }, 2000);
  }
}

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getAdminKnowledgeStatistics({ range: state.curRange1 }).then((res) => {
      console.log("知识库统计", res);
      const { knowledgeBaseStatistics } = res.data;
      if (knowledgeBaseStatistics) {
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
          (item) => item.num
        );
      }
    });
  }
  if (type == 1) {
    state.curRange2 = tab;
    lineOption2.value.options.xAxis.data =
      tab == 2
        ? [
            "2024-09",
            "2024-10",
            "2024-11",
            "2024-12",
            "2025-01",
            "2025-02",
            "2025-03",
          ]
        : ["2019", "2020", "2021", "2022", "2023", "2024", "2025"];
    lineOption2.value.options.series[0].data =
      tab == 2 ? [0, 0, 0, 1, 0, 2, 1] : [0, 0, 0, 0, 0, 1, 4];
  }
}

function getCorpInfo() {
  getAdminCorpGps().then((res) => {
    console.log(res, "机构位置信息");
    if (res.data) {
      mapOption.value.options.series[0].data = res.data.map((item) => {
        let gps = item.gps
          ? JSON.parse(item.gps)
          : { gpsX: 113.298, gpsY: 23.17 };
        return {
          name: item.corpName,
          detail: {
            zczs: item.deviceTotal || 0,
            zxl: item.onlineRatio,
          },
          value: [gps.gpsX, gps.gpsY],
        };
      });
    }
  });
}

//获取数据
function getData() {
  getCorpInfo();
  handleRange(0, 2);
  handleRange(1, 2);
  getAdminSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        maintenanceTotal,
        assetsTotal,
        dutyTotal,
        runTotal,
        restTotal,
        scrapTotal,
        corpAssets,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
        networkStatistics,
      } = res.data;

      mapOption.value.options.series[0].data[0].detail.yjzc = deviceTotal;

      col2_block1_list.value[0].num = deviceTotal;

      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;
      console.log(res.data.assetCoverageStatistics, "objData.value1111");

      window.clearInterval(state.scrolltimer);
      objData.value = {
        ...objData.value,
        corpAssets,
        sparepartsUseStatistics:
          res.data.sparepartsUseStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || objData.value.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };
      play();

      // 设置部门名称假数据
      // objData.value.assetCoverageStatistics.assetCoverageRanking[0].deptName =
      //   "教务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[1].deptName =
      //   "德育处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[2].deptName =
      //   "总务处";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[3].deptName =
      //   "学校办公室";
      // objData.value.assetCoverageStatistics.assetCoverageRanking[4].deptName =
      //   "保卫处";

      // lineOption2.value.options.xAxis.data = [
      //   "2024-10",
      //   "2024-11",
      //   "2024-12",
      //   "2025-01",
      //   "2025-02",
      //   "2025-03",
      // ];
      // lineOption2.value.options.series[0].data = [0, 0, 0, 0, 5, 0];

      if (corpAssets?.length) {
      }

      if (deviceAppUserStatistics?.appUseTime) {
        // 软件使用时长柱状图赋值
        barOption2.value.options.xAxis.data =
          deviceAppUserStatistics.appUseTime.map((item) => item.month);
        barOption2.value.options.series[0].data =
          deviceAppUserStatistics.appUseTime.map((item) => item.timeTotal);
      }

      // if (deviceAppUserStatistics?.appUseType) {
      //   // 软件使用占比饼图赋值
      //   pieOption4.value.options.series[0].data =
      //     deviceAppUserStatistics.appUseType.map((item) => {
      //       return {
      //         name: item.appType,
      //         value: item.num,
      //       };
      //     });
      // }

      if (assetsTypeStatistics) {
        // 资产类别占比柱状图赋值
        barOption.value.options.xAxis.data = assetsTypeStatistics.map(
          (item) => item.assetsTypeName
        );
        barOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => item.num
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => {
            return {
              name: item.assetsTypeName,
              value: item.num,
            };
          }
        );

        // 假数据
        // pieOption.value.options.series[0].data[0].name = "计算机";
        // pieOption.value.options.series[0].data[1].name = "工作站";
        // pieOption.value.options.series[0].data[2].name = "服务器";
        // pieOption.value.options.series[0].data[3].name = "磁盘";
        // pieOption.value.options.series[0].data[4].name = "电灯泡";

        // barOption.value.options.xAxis.data[0] = "计算机";
        // barOption.value.options.xAxis.data[1] = "工作站";
        // barOption.value.options.xAxis.data[2] = "服务器";
        // barOption.value.options.xAxis.data[3] = "磁盘";
        // barOption.value.options.xAxis.data[4] = "电灯泡";
      }
      // console.log(pieOption.value, barOption.value, "资产类别111");

      // if (knowledgeBaseStatistics) {
      //   lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
      //     (item) => item.statisticsTime
      //   );
      //   lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
      //     (item) => item.num
      //   );
      // }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];

        mapOption.value.options.series[0].data[0].detail.sjxx =
          knowledgeBaseYear;

        col2_block1_list.value[2].num = knowledgeBaseYear;

        optList1.value[0].total = knowledgeBaseMonth;
        optList1.value[1].ratio = knowledgeBaseMonthRise;
        optList1.value[1].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;

        // optList1.value[1].total = knowledgeBaseYear;
        // optList1.value[1].ratio = knowledgeBaseYearRise;
        // optList1.value[1].num =
        //   knowledgeBaseYearRise?.replace("%", "") * 1 || 0;
      }

      if (networkStatistics) {
        networkList.value = networkStatistics;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAdminAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsBrandProportion,
      assetsFaultBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
    } = res.data;

    // 标签使用频率
    if (assetsTagUseProportion) {
      // const arr = ["小学部", "初中部", "高中部", "易消耗品", "维护过的物品"];
      // pieOption14.value.options.series[0].data = assetsTagUseProportion?.map(
      //   (item, index) => {
      //     return {
      //       name: arr[index],
      //       value: item.num,
      //     };
      //   }
      // );
      // 资产类别占比柱状图赋值
      // barOption3.value.options.xAxis.data =
      //   assetsTagUseProportion?.map(
      //     (item, index) => arr[index] || item.assetsTagName
      //   ) || [];
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map((item, index) => item.assetsTagName) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];
    }

    // console.log(barOption3.value, "barOption3.value");

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      let arr = [];
      assetsSystemVersionProportion?.map((item, index) => {
        arr.push({
          name: item.assetsSystemVersionName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor15.value[index % pieColor15.value.length],
          },
        });
      }) || [];
      data3D15.value = arr;

      console.log(pieOption15.value, "pieOption15.value");
      // 资产类别占比柱状图赋值
      // barOption4.value.options.xAxis.data = assetsSystemVersionProportion.map(
      //   (item) => item.assetsSystemVersionName
      // );
      // barOption4.value.options.series[0].data =
      //   assetsSystemVersionProportion.map((item) => item.num);
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data.map((item) => {
        let obj = deviceTroubleProportion.find((_) => _.timeRange == item.name);
        item.value = obj?.num || 0;
      });
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data.map((item) => {
        let obj = hardwareAssetsUseProportion.find(
          (_) => _.timeRange == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      let arr = [];
      assetsTypeProportion.map((item, index) => {
        arr.push({
          name: item.assetsTypeName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor8.value[index % pieColor8.value.length],
          },
        });
      });
      data3D8.value = arr;
      console.log("设备类型", assetsTypeProportion, data3D8.value);

      // pieOption8.value.options.series[0].data =
      //   assetsTypeProportion?.map((item) => {
      //     return {
      //       name: item.assetsTypeName,
      //       value: item.num,
      //     };
      //   }) || [];
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      let arr = [];
      assetsPortProportion.map((item, index) => {
        arr.push({
          name: item.assetsPortName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor9.value[index % pieColor9.value.length],
          },
        });
      });
      data3D9.value = arr;
      console.log("端口类型", assetsPortProportion, data3D9.value);

      // pieOption9.value.options.series[0].data =
      //   assetsPortProportion?.map((item) => {
      //     return {
      //       name: item.assetsPortName,
      //       value: item.num,
      //     };
      //   }) || [];
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data.map((item) => {
        let obj = assetsHardDiskProportion.find(
          (_) => _.assetsHardDiskName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data.map((item) => {
        let obj = assetsMemoryProportion.find(
          (_) => _.assetsMemoryName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 故障资产品牌占比
    if (assetsBrandProportion) {
      let arr = [];
      assetsBrandProportion?.map((item, index) => {
        arr.push({
          name: item.assetsBrandName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor12.value[index % pieColor12.value.length],
          },
        });
      }) || [];

      data3D12.value = arr;
      console.log(data3D12.value, "data3D12.value");
    }

    // 故障资产使用年限占比
    if (assetsFaultUseYearProportion) {
      pieOption13.value.options.series[0].data =
        assetsFaultUseYearProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }
  });
}

function getWorkOrderLine1() {
  getAdminWorkOrderTimeStatistics(orderParams1.value).then((res) => {
    console.log(res, "工单处理统计");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      lineOption3.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption3.value.options.series[0].data =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption3.value.options.series[1].data =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
    }
  });
}

function getWorkOrderLine2() {
  getAdminWorkOrderTimeStatistics(orderParams2.value).then((res) => {
    console.log(res, "工单处理统计2");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      lineOption4.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption4.value.options.series[0].data =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption4.value.options.series[1].data =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
    }
  });
}

function getWorkOrderLine3() {
  getAdminMaintainCountStatistics(orderParams3.value).then((res) => {
    console.log(res, "运维统计");
    let { clockStatistics, shouldClockCountToday, clockedCountToday } =
      res.data;
    optList5.value[0].total = shouldClockCountToday;
    optList5.value[1].total = clockedCountToday;
    if (clockStatistics) {
      lineOption5.value.options.xAxis.data =
        clockStatistics?.map((item) => item.dateLabel) || [];
      lineOption5.value.options.series[0].data =
        clockStatistics?.map((item) => item.shouldClockUsers) || [];
      lineOption5.value.options.series[1].data =
        clockStatistics?.map((item) => item.clockedUsers) || [];
    }
  });
}

function getOrderData() {
  // getWorkOrderLine1();
  // getWorkOrderLine2();
  // getWorkOrderLine3();
  getAdminWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList3.value[0].total = processingWorkOrderMonth;
      optList3.value[0].ratio = processingWorkOrderMonthRise;
      optList3.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList3.value[1].total = processingWorkOrderYear;
      optList3.value[1].ratio = processingWorkOrderYearRise;
      optList3.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
      } = workOrderProcrssingTime;
      optList4.value[0].total = todayAverageProcessingTime || 0;
      optList4.value[0].ratio = riseOfProcessingTime || "0%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;

      // optList4.value[1].total = (totalAverageProcessingTime.replace('分钟', '') * 1 / 1440).toFixed(1) + '天';
      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
    }
  });
}

watch(
  () => state.curAssetsTab,
  () => {
    console.log("切换tab");
    nextTick(() => {
      if (state.curAssetsTab == 0) {
        initPieFunc("pieData6");
        initPieFunc("pieData10");
        initPieFunc("pieData7");
        initPieFunc("pieData11");
        setTimeout(() => {
          initPieFunc3D("pieData12");
          initPieFunc3D("pieData15");
          initPieFunc3D("pieData8");
          initPieFunc3D("pieData9");
        }, 1000);
      } else if (state.curAssetsTab == 1) {
        initPieFunc("pieData5");
        initPieFunc("pieData4");
        setTimeout(() => {
          initPieFunc3D("pieData2");
          initPieFunc3D("pieData3");
        }, 1000);
      }
    });
  },
  {
    deep: true,
    immediate: true,
  }
);

onMounted(() => {
  proxy.$modal.loading();
  getData();
  getOrderData();
  getAssetsData();
  // initPieFunc("pieData4");
  // initPieFunc("pieData5");
  // initPieFunc("pieData7");
  // initPieFunc("pieData11");
  // setTimeout(() => {
  //   initPieFunc3D("pieData2");
  //   initPieFunc3D("pieData3");
  //   initPieFunc3D("pieData8");
  //   initPieFunc3D("pieData9");
  //   initPieFunc3D("pieData12");
  //   initPieFunc3D("pieData15");
  // }, 1000);
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  window.clearInterval(state.scrolltimer);
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
  
  <style lang="scss" scoped>
@font-face {
  font-family: "HYYaKuHeiW";
  src: url("@/assets/fontFamily/HYYaKuHeiW.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
:deep(.tooltip-bg) {
  position: absolute;
  top: -2.5vw;
  left: 0;
  background-color: transparent;
  width: 16vw;
  height: 8vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-bg.png");
  background-size: 100% 100%;
}
:deep(.tooltip-title) {
  // border: 1px solid red;
  padding-left: 1vw;
  font-size: 0.8vw;
  font-weight: bold;
  height: 2.2vw;
  line-height: 2.2vw;
  display: flex;
  align-items: center;
  gap: 0 0.5vw;
  color: #fff;
  &::before {
    content: "";
    width: 2vw;
    height: 2vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-icon.png");
    background-size: 100% 100%;
  }
}
:deep(.tooltip-label) {
  // border: 1px solid red;
  width: 4vw;
}
:deep(.tooltip-value) {
  font-weight: bold;
}
:deep(.tooltip-row) {
  // border: 1px solid red;
  font-size: 0.8vw;
  display: flex;
  align-items: center;
  gap: 0 0.3vw;
  padding-left: 1.5vw;
  height: 2.5vw;
  padding-top: 0.8vw;
  color: #fff;
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #c6d6ff;
  // color: #fff;
  padding: 0.2vw 0vw;
  font-size: 0.7vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.6vw;
      color: #4095e5;
      &_item {
        border: 1px solid #1658ff4d;
        padding: 0.1vw 0.6vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/admin/range_checked.png");
          background-size: 96% 96%;
          background-position: center;
          background-repeat: no-repeat;
          color: #00f0ffff;
        }
      }
    }
  }
}
.unify-main_charts {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  overflow: hidden;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gz-bg.png");
  background-size: 100% 100%;

  .block-mgt {
    // margin-top: -1.5vw;
  }

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-pie_bg.png");
      background-size: 100% 100%;

      &-title {
        font-size: 0.8vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1vw;
        margin-top: 1vw;
        color: #fff;
        background: url("@/assets/screen/app/block-title1.png");
        background-size: 100% 100%;
        font-family: "HYYaKuHeiW";
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/block-title_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;
      // color: #fff;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.3vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          padding-right: 0.2vw;
          color: #c7d9f9;
        }

        div {
          padding-left: 0.8vw;
        }

        .up {
          color: #2dcd5e;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          color: #fe005d;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0;
            left: 0.3vw;
            width: 0.5vw;
            height: 0.6vw;
            background: url("@/assets/screen/arrow_low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 24vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;
      padding: 1vw 0 0;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 3vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/network-bg.png");
      background-size: 100% 100%;
      .flex {
        gap: 0;
      }
      .network {
        position: relative;
        color: #4f5760;
        font-size: 0.85vw;
        line-height: 1.3vw;
        flex: 1;
        padding: 0 1vw 0 4vw;
        span {
          font-size: 1vw;
          color: #fff;
          display: inline-block;
          padding-right: 0.2vw;
        }
        &::before {
          position: absolute;
          content: "";
          left: 1vw;
          width: 2.5vw;
          height: 2.5vw;
        }
        &.up::before {
          background: url("@/assets/screen/admin/network_up.png");
          background-size: 100% 100%;
        }

        &.low::before {
          background: url("@/assets/screen/admin/network_low.png");
          background-size: 100% 100%;
        }
        .unit {
          color: #4f5760;
        }
      }
    }

    &-block3 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        height: 5.5vw;
        width: 100%;
        text-align: center;
        line-height: 4.5vw;
        font-size: 1.5vw;
        color: #fff;
      }
      .block-title_text {
        background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
          #ffffffff;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent;
      }
    }

    &-block4 {
      .block-title_text {
        background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
          #ffffffff;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent;
      }
      .table {
        min-height: 14.45vw;
        font-size: 0.75vw;
        padding: 0.2vw 0vw 0.5vw;
        margin-top: 0.5vw;
        color: #fff;
        background: radial-gradient(
            150.78% 100% at 50% 100%,
            #d6f0ff33 0%,
            #85e2ff1a 20.31%,
            #3b80c00f 41.67%,
            #032f7215 62.5%,
            #0210410c 80.73%,
            #00000000 100%
          ),
          linear-gradient(180deg, #050e2e4d 0%, #0c1a4b4d 100%);
        border-bottom: 1px solid #0091ff80;

        .opt-title {
          // margin-bottom: 1vw;
          font-size: 1vw;
        }

        &-row {
          display: flex;
          padding: 0.4vw 0;
          text-align: center;
          justify-content: space-between;
          margin-top: 0.4vw;
          gap: 0 0.5vw;
          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 11vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0 1vw;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 6vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 48vw;
    &-block1 {
      padding-top: 1vw;
      width: 100%;
      // border: 1px solid red;
      display: flex;
      justify-content: center;
      font-size: 0.9vw;
      color: #6c95ff;
      gap: 0 1vw;
      &_item {
        // border: 1px solid red;
        width: 8.2vw;
        padding: 0.6vw 1vw 0.1vw;
        line-height: 1.2vw;
        letter-spacing: 0.1vw;
        background: url("@/assets/screen/top_data_frame.png");
        background-size: 100% 100%;
        span {
          font-size: 1.5vw;
          color: #c6d6ff;
          display: inline-block;
          padding-right: 0.2vw;
        }
      }
    }

    &-block2 {
      margin-top: 0.2vw;
      // border: 1px solid red;
      height: 35vw;
      position: relative;
      width: 49vw;
      div {
        margin: 0 auto;
        width: 30vw;
        height: 35vw;
        // border: 1px solid red;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gzmap-bg2.png");
        background-size: 100% 100%;
      }
    }

    &-block3 {
      .echart-item {
        // border: 1px solid red;
        height: 11.5vw;
        flex: 1;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 24.5vw;
    height: 100%;

    .subtitle {
      font-size: 0.6vw;
      padding: 0vw;
    }

    .block {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
      background-size: 100% 100%;
      height: auto;
    }

    .block-size-90 {
      background-size: 100% 100%;
    }

    &-block1 {
      // margin-top: -0.3vw;
      // border: 1px solid red;
      height: 2vw;
      cursor: pointer;
      display: flex;
      &.left {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/assetsTab_left.png");
        background-size: 100% 100%;
      }
      &.right {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/assetsTab_right.png");
        background-size: 100% 100%;
      }

      &_left,
      &_right {
        flex: 1;
        height: 2vw;
        // border: 1px solid yellow;
      }
    }

    &-block2 {
      margin-top: 1vw;
      &.other {
        .subtitle {
          color: #c6d6ffff;
          font-size: 0.8vw;
          padding-top: 0;
          white-space: nowrap;
        }
        .range {
          margin: 1vw 0;
        }
      }
      .flex {
        gap: 0 0.5vw;
      }
      .flex-bg {
        background: linear-gradient(0deg, #1658ff00 0%, #1658ff12 100%);
        // margin-top: 0.8vw;
      }
      .flex-bg1 {
        background: linear-gradient(180deg, #1658ff00 0%, #1658ff12 100%);
        padding-bottom: 1vw;
        margin-top: 0.4vw;
      }
      .block {
        padding: 0vw 0.2vw;
        margin-top: 0.5vw;
      }
      .block-3d-h {
        // height: 11vw;
      }
      .block-bg-none {
        background: none;
      }
      .block-right {
        margin-top: 3vw;
      }
      .big-block {
        display: flex;
        align-items: center;
        justify-content: space-between;
        // height: 9vw;
        background: none;
        margin-top: 0;
        padding: 0;

        .block-left {
          flex: 1;
        }
        .block-right {
          flex: 1;
          .subtitle {
            color: #fff;
            white-space: nowrap;
            span {
              color: #1658ff;
              font-size: 0.9vw;
              margin-left: 0.3vw;
            }
          }

          .use-list {
            text-align: center;
            gap: 0 0.5vw;
            font-size: 0.6vw;
            width: 45%;
            white-space: nowrap;
            .use-item {
              color: #ffffff99;
            }
            .use-num {
              color: #fff;
              font-size: 1vw;
              margin-top: 0.2vw;
            }
          }
          .use-list:nth-child(1) {
            margin-bottom: 1vw;
          }
        }

        .right-flex {
          display: flex;
          flex-wrap: wrap;
        }
      }
      .pieData5,
      .pieData6,
      .pieData10,
      .pieData11 {
        &-list {
          width: 6vw;
          height: 65%;
          position: absolute;
          margin-top: 12%;
          top: 0;
          right: 0;
          font-size: 0.55vw;
          display: flex;
          flex-direction: column;
          justify-content: space-between;

          .subtitle {
            color: #fff;
            font-size: 0.6vw;
            // padding-top: 0.3vw;
            white-space: nowrap;

            span {
              color: #1658ff;
              font-size: 0.9vw;
              margin-left: 0.3vw;
            }
          }
        }
        &-item {
          cursor: pointer;
          // border: 1px solid red;
          color: #fff;
          display: flex;
          justify-content: space-between;
          align-items: center;
          height: 1.1vw;
          line-height: 1.1vw;
          border-width: 1px;
          border-style: solid;
          padding: 0vw 0.4vw;
          border-image-source: radial-gradient(
            50% 50% at 50% 100%,
            #c7d6ff4d 0%,
            #c7d3ff00 100%
          );
          border-image-slice: 1;
          background: radial-gradient(
            100% 175.73% at 0% 50%,
            #77adff40 0%,
            #002cc700 100%
          );
          // margin-top: 0.2vw;
          // border-right: none;
        }
        &-name {
          display: flex;
          align-items: center;
          gap: 0 0.2vw;
          color: #d4deffff;
          .item-dot {
            width: 0.4vw;
            height: 0.4vw;
            display: flex;
            align-items: center;
            justify-content: center;
            i {
              display: flex;
              width: 0.15vw;
              height: 0.15vw;
            }
          }
        }
        &-bg {
          position: absolute;
          top: 1.25vw;
          left: 0.02vw;
          width: 5.3vw;
          height: 5.3vw;
        }
      }

      .pieData10,
      .pieData11 {
        &-bg{
          left: 0vw;
        }
        &-list {
          margin-top: 7.8%;
          gap: 0.05vw 0;
        }
        &-item {
          padding: 0 0.3vw;
          border-width: 1px;
          border-style: solid;
          border-image-source: linear-gradient(
            0deg,
            #c7d6ff4d 0%,
            #c7d6ff00 28.9%
          );
          border-image-slice: 1;
          box-sizing: border-box;
          background: radial-gradient(
            50% 71.05% at 50% 100%,
            #a3cdff40 0%,
            #0020c700 100%
          );
        }
        &-name {
          .item-dot {
            width: 0.4vw;
            height: 0.4vw;
            border-radius: 50%;
            margin-right: 0.1vw;
          }
        }
      }
      .pieData11-bg{
        left: -0.1vw;
      }

      .pieData7,
      .pieData4 {
        &-list {
          height: 90%;
          margin-top: 7.5%;
          position: absolute;
          top: 0;
          right: 0;
          font-size: 0.6vw;
          display: flex;
          justify-content: space-between;
          width: 6vw;
          flex-direction: row;
          flex-wrap: wrap;
        }
        &-item {
          cursor: pointer;
          display: flex;
          width: 3vw;
          height: 1.5vw;
          text-align: left;
          color: #fff;
        }
        &-bar {
          width: 0.1vw;
          height: 100%;
          margin-right: 0.25vw;
        }
        &-subBar {
          width: 0.1vw;
          height: 0.7vw;
        }
        &-name {
          display: flex;
          align-items: center;
          gap: 0 0.2vw;
          font-size: 0.5vw;
          color: rgba(255, 255, 255, 0.6);
        }
        &-count {
          margin-top: 0.2vw;
        }
        &-bg {
          position: absolute;
          left: -0.1vw;
          top: 1.25vw;
          width: 5.3vw;
          height: 5.3vw;
        }
      }
      .pieData4, .pieData5 {
        &-bg {
          top: 1.05vw;
          left: -0.05vw;
        }
      }

      .pieData8,
      .pieData9,
      .pieData12,
      .pieData3,
      .pieData15,
      .pieData2 {
        &-list {
          // border: 1px solid red;
          color: #fff;
          width: 100%;
          max-height: 2.2vw;
          overflow-y: scroll;
          margin-top: 7.5%;
          position: absolute;
          top: 6.5vw;
          right: 0;
          font-size: 0.55vw;
          display: flex;
          flex-wrap: wrap;
          gap: 0.5vw 0.8vw;
          padding: 0 0.5vw;
          z-index: 1;
          &::-webkit-scrollbar {
            width: 0;
            display: none;
          }
          scrollbar-width: none;
          -ms-overflow-style: none;
        }
        &-item {
          width: 46%;
          // border: 1px solid red;
          cursor: pointer;
          display: flex;
          justify-content: space-between;
          align-items: center;
          gap: 0 0.5vw;
        }
        &-name {
          display: flex;
          align-items: center;
          gap: 0 0.2vw;
          .item-dot {
            width: 0.3vw;
            height: 0.3vw;
            display: flex;
            align-items: center;
            justify-content: center;
          }
          .name {
            color: #ffffffa3;
            width: 3vw;
            /* 旧版弹性盒 */
            display: -webkit-box;
            /* 弹性盒子元素垂直排列 */
            -webkit-box-orient: vertical;
            /* 控制要显示的行数 */
            -webkit-line-clamp: 1;
            overflow: hidden;
            // border: 1px solid red;
          }
        }
        &-count {
          // margin-left: 1vw;
          color: #fff;
        }
        &-bg {
          position: absolute;
          top: -0.2vw;
          left: 0.1vw;
          width: 11.5vw;
          height: 7vw;
        }
      }

      .pieData12-list,
      .pieData15-list,
      .pieData3-list,
      .pieData2-list {
        // border: 1px solid;
        margin-top: 1.5vw;
      }
      .pieData2-bg {
        top: 0.9vw;
        left: 0.3vw;
      }
      .pieData12-bg,
      .pieData15-bg {
        position: absolute;
        top: 0.5vw;
        left: 0.3vw;
        width: 11.5vw;
        height: 7vw;
      }

      .block2 {
        background: url("@/assets/screen/app/address-bg.png");
        background-size: 100% 100%;
      }

      .address {
        // border: 1px solid red;
        height: 4vw;
        line-height: 4vw;
        font-size: 0.9vw;
        display: flex;
        justify-content: center;
        align-items: center;
        span {
          color: #4095e5;
          font-size: 1.1vw;
          letter-spacing: 0.2vw;
        }
      }
    }

    &-block3 {
      margin-top: 0.6vw;
      .table2 {
        flex: 1;
        height: 11.5vw;
        // border: 1px solid red;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0;
        font-size: 0.7vw;

        .table-row {
          display: flex;
          // padding: .5vw 0 0;
          height: 1.2vw;
          line-height: 1.2vw;
          text-align: center;
          // border: 1px solid red;
          margin-bottom: 0.4vw;
          font-size: 0.6vw;
          // &.data:hover {
          //   background-color: #061d47;
          // }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 19vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            // border: 1px solid red;
          }

          &_count {
            // border: 1px solid red;
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .table-data {
          overflow: hidden;
          // border: 1px solid red;
          height: 7.6vw;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -1.6vw;
          }
          .odd {
            // background-color: rgba(63, 86, 237, 0.35);
            background: #1658ff4d;
          }
          .even {
            // background-color: rgba(53, 206, 217, 0.35);
            background: #00f0ff4d;
          }
        }

        .opt-title {
          margin-top: 0.1vw;
          height: 1.3vw;
          line-height: 1.3vw;
          // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/tableHead-bg.png");
          // background-size: 100% 100%;
          background: radial-gradient(
            99.01% 198.21% at 99.01% 50%,
            #71b2ff26 0%,
            #0014c700 100%
          );
        }
      }
    }
  }
}
</style>