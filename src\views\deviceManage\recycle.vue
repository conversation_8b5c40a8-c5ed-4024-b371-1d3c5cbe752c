<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <div class="monitor_flex">
        <locateSel
          class="flex_1"
          ref="locateRef"
          :maxHeight="posHeight"
          @getPosition="getPosition"
          @handleQuery="handleQuery"
        />
        <!-- <div class="monitor-position flex_1">
          <div class="monitor-title">安装位置筛选</div>
          <el-scrollbar :max-height="500">
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              show-checkbox
              node-key="id"
              expand-on-click-node
              default-expand-all
              :props="positionProps"
              @check="treeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div> -->

        <div class="flex_2" ref="flexRightBox">
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            @submit.native.prevent
            style="width: 100%"
          >
            <el-form-item prop="deviceCodeAndName">
              <el-input
                v-model="queryParams.deviceCodeAndName"
                placeholder="请输入设备编码/名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="typeId">
              <el-select
                v-model="queryParams.typeId"
                placeholder="请选择设备类型"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="tagId">
              <el-select
                v-model="queryParams.tagId"
                placeholder="请选择设备标签"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tag"
                  :value="item.tagId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="deviceStatus">
              <el-select
                v-model="queryParams.deviceStatus"
                placeholder="请选择设备状态"
                @change="handleQuery"
                clearable
                filterable
                style="width: 200px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="time">
              <el-date-picker
                v-model="queryParams.time"
                type="daterange"
                format="YYYY-MM-DD"
                value-format="YYYY-MM-DD"
                start-placeholder="入库开始时间"
                end-placeholder="入库结束时间"
                @change="handleQuery"
                @clear="handleQuery"
                style="width: 245px"
              />
            </el-form-item>
            <!-- <el-form-item label="" prop="addressStr">
          <el-tree-select
            v-model="queryParams.addressStr"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            style="width: 200px"
            clearable
            @change="handleQueryPosition"
          />
        </el-form-item> -->
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12" style="gap: 10px 0">
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleDelete"
                >批量彻底删除</el-button
              >
            </el-col>
            <el-col :span="1.5" v-if="!isExternalPark">
              <el-button
                type="success"
                plain
                icon="Setting"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleRecover"
                >批量恢复</el-button
              >
            </el-col>
          </el-row>

          <div>
            <el-table
              ref="ledgerTable"
              v-loading="loading"
              :data="tableData"
              border
              highlight-current-row
              @row-click="rowClick"
              @selection-change="selectionChange"
              @select="onTableSelect"
              @select-all="selectSingleTableAll"
              style="width: 100%"
            >
              <el-table-column
                type="selection"
                width="55"
                align="center"
                fixed="left"
              />
              <el-table-column
                label="设备编码"
                align="center"
                min-width="120px"
                prop="deviceCode"
                show-overflow-tooltip
              />
              <el-table-column
                label="设备名称"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="deviceNameStr"
              />
              <el-table-column
                label="设备类型"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="deviceTypeStr"
              />
              <el-table-column
                label="设备标签"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="tagName"
              />
              <el-table-column
                label="设备能否正常使用"
                align="center"
                min-width="130px"
                show-overflow-tooltip
              >
                <template #default="scope">
                  {{ scope.row.isNormal == 1 ? "不能正常使用" : "能正常使用" }}
                </template>
              </el-table-column>
              <el-table-column
                label="设备状态"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
              >
                <template #default="scope">
                  <el-tag
                    v-if="statusObj[scope.row.deviceStatus]"
                    effect="dark"
                    :type="statusObj[scope.row.deviceStatus].type"
                    >{{ statusObj[scope.row.deviceStatus].label }}</el-tag
                  >
                </template>
              </el-table-column>
              <el-table-column
                label="安装位置"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="installAddress"
              >
                <template #default="{ row }">
                  {{ row.installAddress || "-" }}
                </template>
              </el-table-column>
              <el-table-column
                label="资产端口类别"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="portTypeNameStr"
              />
              <el-table-column
                label="信息资产类别"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="assetsTypeNameStr"
              />
              <el-table-column
                label="信息资产等级"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="levelStr"
              />
              <el-table-column
                label="品牌"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="brandStr"
              />
              <el-table-column
                label="规格型号"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="modelStr"
              />
              <el-table-column
                label="入库时间"
                align="center"
                show-overflow-tooltip
                minWidth="120px"
                prop="putTime"
              >
                <template #header>
                  <div
                    style="padding-left: 5px; cursor: pointer"
                    @click="handleSortDate"
                  >
                    入库时间
                    <span class="caret-wrapper">
                      <i
                        class="sort-caret ascending"
                        :class="queryParams.sort == 'asc' ? 'active' : ''"
                      ></i>
                      <i
                        class="sort-caret descending"
                        :class="queryParams.sort == 'desc' ? 'active' : ''"
                      ></i>
                    </span>
                  </div> </template
              ></el-table-column>
              <el-table-column
                label="操作"
                :min-width="240"
                align="center"
                fixed="right"
                class-name="small-padding fixed-width"
              >
                <template #default="scope">
                  <template v-if="!isExternalPark">
                    <el-button
                      link
                      type="primary"
                      icon="Search"
                      @click.stop="handleCheck(scope.row, 0)"
                      >详情</el-button
                    >
                    <el-button
                      link
                      type="success"
                      icon="RefreshLeft"
                      @click.stop="handleRecover(scope.row)"
                      >恢复</el-button
                    >
                    <el-button
                      link
                      type="danger"
                      icon="Delete"
                      @click.stop="handleDelete(scope.row)"
                      >彻底删除</el-button
                    >
                  </template>
                  <template v-else>
                    <span>-</span>
                  </template>
                </template>
              </el-table-column>
            </el-table>

            <pagination
              v-show="total > 0"
              :total="total"
              v-model:page="queryParams.current"
              v-model:limit="queryParams.size"
              @pagination="getList"
            />
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup name="ledgerManage">
import locateSel from "./components/locateSel.vue";
import { useRoute, useRouter } from "vue-router";
import { devicePage, recoverDevice } from "@/api/mediaTeach/ledger";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { getUserList, getUserByRole } from "@/api/system/user";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import {
  treeToArray,
  treeFindPath,
  timeFormat,
  sendPointRequest,
} from "@/utils";
import { getToken } from "@/utils/auth";
import useUserStore from "@/store/modules/user";
import { nextTick } from "vue";
import QRCode from "qrcode";
import { generateQRCode, wrapText } from "@/utils/uploadCode2";
import { ElMessage } from "element-plus";
import { debounce } from "@/utils/debounce";
import { onMounted, onBeforeUnmount } from "vue";
import { deviceStatusObj } from "@/utils/addPoint";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
const ledgerRef = ref(null);
const exportRes = ref({});
const repairList = ref([]);
const open = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const activeNum = ref(1);
const loading = ref(false);
const showSearch = ref(true);
const showExport = ref(false);
const showRepair = ref(false);
const positionResult = ref({
  names: [],
  ids: [],
});
const queryPositionResult = ref({
  names: [],
  ids: [],
});
const statusList = ref([
  { label: "正常", value: 0, type: "success" },
  { label: "维修中", value: 1, type: "danger" },
  { label: "报障中", value: 2, type: "warning" },
  { label: "待审核", value: 3, type: "primary" },
  { label: "已报废", value: 4, type: "info" },
]);
const typeList = ref([]);
const typeList_disabled = ref([]);
const tagList = ref([]);
const positionTreeList = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
  disabled: "disabled",
});
const total = ref(0);
const uploadFileUrl = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/device/importData"
); // 导入文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });
const smartRules = ref({
  tagIdList: [
    {
      required: true,
      message: "设备标签不能为空",
      trigger: ["blur", "change"],
    },
  ],
  macAddress: [
    { required: true, message: "物理地址不能为空", trigger: "blur" },
  ],
  logicAddress: [
    { required: true, message: "逻辑地址不能为空", trigger: "blur" },
  ],
  osVersion: [
    { required: true, message: "操作系统版本号不能为空", trigger: "blur" },
  ],
  cpu: [{ required: true, message: "CPU类型不能为空", trigger: "blur" }],
  internalStorage: [
    { required: true, message: "设备内存不能为空", trigger: "blur" },
  ],
  ipAddress: [{ required: true, message: "IP地址不能为空", trigger: "blur" }],
  ralayHost: [
    { required: true, message: "边缘服务器ip不能为空", trigger: "blur" },
  ],
  brand: [{ required: true, message: "设备品牌不能为空", trigger: "blur" }],
  disk: [{ required: true, message: "设备硬盘不能为空", trigger: "blur" }],
});
const baseRules = ref({
  deviceName: [
    { required: true, message: "设备名称不能为空", trigger: "blur" },
    { max: 50, message: "设备名称最多输入50个字符", trigger: "blur" },
  ],
  typeId: [{ required: true, message: "设备类型不能为空", trigger: "blur" }],
  deviceCode: [
    { required: true, message: "设备编码不能为空", trigger: "blur" },
  ],
  deviceStatus: [
    {
      required: true,
      message: "设备状态不能为空",
      trigger: ["blur", "change"],
    },
  ],
  deviceImg: [
    {
      required: true,
      message: "设备照片不能为空",
      trigger: ["blur", "change"],
    },
  ],
  installAddressId: [
    {
      required: true,
      message: "安装位置不能为空",
      trigger: ["blur", "change"],
    },
  ],
  model: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
  putTime: [
    {
      required: true,
      message: "入库时间不能为空",
      trigger: ["blur", "change"],
    },
  ],
});

const data = reactive({
  treeRef: null,
  addTimer: null,
  addSecond: 0,
  statusObj: {
    0: { label: "正常", type: "success" },
    1: { label: "维修中", type: "danger" },
    2: { label: "报障中", type: "warning" },
    3: { label: "待审核", type: "primary" },
    4: { label: "已报废", type: "info" },
  },
  nodeRef: null,
  positionNodeResultList: [
    {
      names: [],
      ids: [],
    },
  ],
  positionNodeList: [],
  repairRef: null,
  ledgerTable: null,
  userList: [],
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // 保存表格勾选的行数据
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    isRecycle: 1,
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    installAddressId: "",
    positionId: "",
    createTime: "",
  },
  queryParams: {
    current: 1,
    size: 10,
    tenantId: "",
    model: "",
    deviceCodeAndName: "",
    deviceStatus: route.query?.deviceStatus || "",
    // address: "",
    typeId: "",
    isRecycle: 1,
    tagId: "",
    putTime: "",
    time: null,
    startTime: "",
    endTime: "",
  },
  rules: {},
  repairForm: {
    userId: "",
    type: 1,
    repairName: "",
    repairTime: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: "",
    attachments: [],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报障人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      { required: true, message: "报障人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    remark: [
      { required: true, message: "报障描述不能为空", trigger: "blur" },
      { max: 200, message: "报障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        message: "报障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const {
  treeRef,
  addTimer,
  addSecond,
  statusObj,
  nodeRef,
  positionNodeResultList,
  positionNodeList,
  ledgerTable,
  repairRef,
  userList,
  queryParams,
  form,
  repairForm,
  repairRules,
  tableData,
  tableAllSelectedId,
} = toRefs(data);

const isExternalPark = ref(false);

function handleSortDate(val) {
  if (queryParams.value.sort === "asc") {
    queryParams.value.sort = "desc";
  } else {
    queryParams.value.sort = "asc";
  }
  getList();
}

function handleParkChange(event) {
  if (route.path === "/deviceLedger/ledger") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  initParkState();
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem("deviceManagePage");
  if (savedPage) {
    queryParams.value.current = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem("deviceManagePage");
  }

  if (route.query.deviceStatus !== undefined) {
    queryParams.value.deviceStatus = Number(route.query.deviceStatus);
  }
  getList();
  window.addEventListener("parkChange", handleParkChange);
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - 215;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - 215;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览访问设备回收站页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});

const handleToAdd = () => {
  router.push("baseInfo/location");
};

const handleCancel = () => {
  data.nodeRef.setCheckedNodes([]);
  open2.value = false;
};

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

const repairUserChange = (val) => {
  const idx = data.userList.findIndex((_) => _.userId == val);
  console.log("报障人更改", val, idx);
  data.repairForm.repairPhone = data.userList[idx].phoneNumber || "";
  data.repairForm.repairName = data.userList[idx].nickName || "";
};

/** 查询用户列表 */
async function getUsers() {
  // await getUserList({ nickName: "", phone: "" }).then((response) => {
  //   console.log("用户列表", response.data);
  //   data.userList = response.data || [];
  // });
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    console.log("拥有角色的用户列表", resp);
    if (resp.data) {
      data.userList = resp.data.records;
    }
  });
}

// 打印设备二维码
const handlePrintCode = debounce(() => {
  let rows = data.tableAllSelectedRow;
  if (rows.length == 0) {
    ElMessage.error("请选择设备");
    return;
  }
  if (data.tableAllSelectedRow.findIndex((_) => _.deviceStatus > 1) != -1) {
    proxy.$modal.msgWarning("待审核/已报废设备不能进行此操作");
    return;
  }
  let obj = rows.map((item, index) => {
    return {
      deviceCode: item.deviceCode,
      deviceName: item.deviceName,
      installAddress: item.installAddress,
    };
  });

  console.log(obj);
  generateQRCode(obj, true, `${form.value.deviceName}-设备二维码.png`);
}, 250);

const qrCodeDialogVisible = ref(false);
const currentQrCode = ref("");

// 添加生成单个二维码的方法
async function generateSingleQRCode(deviceData) {
  const { deviceCode, deviceName, installAddress } = deviceData;

  try {
    // 显著减小二维码尺寸
    const qrUrl = await QRCode.toDataURL(deviceCode, {
      width: 128, // 调整为更小的尺寸
      margin: 1, // 减小边距
      scale: 4, // 保持清晰度
    });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    return new Promise((resolve) => {
      img.onload = () => {
        const scale = 2;
        canvas.width = (img.width + 60) * scale; // 继续减小边距
        canvas.height = (img.height + 20 + 15 * 3) * scale;

        ctx.scale(scale, scale);
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.fillStyle = "white";
        ctx.fillRect(0, 0, canvas.width / scale, canvas.height / scale);

        const qrX = (canvas.width / scale - img.width) / 2;
        const qrY = 10;

        ctx.drawImage(img, qrX, qrY);

        const prompt = `设备名称：${deviceName}\n设备编码：${deviceCode}\n安装位置：${installAddress}`;
        ctx.fillStyle = "black";
        ctx.font = "bold 11px Arial"; // 稍微调小字体

        const promptLines = wrapText(ctx, prompt, 150); // 减小文本宽度
        promptLines.forEach((line, index) => {
          const textY = qrY + img.height + 15 + 13 * index; // 调整行间距
          const textWidth = ctx.measureText(line).width;
          const xPosition = (canvas.width / scale - textWidth) / 2;
          ctx.fillText(line, xPosition, textY);
        });

        resolve(canvas.toDataURL("image/png", 1.0));
      };
      img.src = qrUrl;
    });
  } catch (error) {
    console.error("Error generating QR code:", error);
    return null;
  }
}

// 查看二维码方法
async function handleViewQrCode() {
  try {
    const deviceData = {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    };

    const qrCodeUrl = await generateSingleQRCode(deviceData);
    if (qrCodeUrl) {
      currentQrCode.value = qrCodeUrl;
      qrCodeDialogVisible.value = true;
    }
  } catch (error) {
    proxy.$modal.msgError("生成二维码失败");
    console.error(error);
  }
}

// 下载二维码方法
function handleDownloadQrCode() {
  const deviceData = [
    {
      deviceCode: form.value.deviceCode,
      deviceName: form.value.deviceName,
      installAddress: form.value.installAddress,
    },
  ];

  generateQRCode(deviceData);
}

function handlePosition(val) {
  positionResult.value = {
    ids: [],
    names: [],
  };
  positionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = positionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    positionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("positionResult ==> ", positionResult.value);
}

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  // queryParams.value.address = queryPositionResult.value.names.join("-");
  handleQuery();
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

/** 查询台账列表 */
function getList() {
  loading.value = true;
  console.log("查询的参数", data.queryParams);

  devicePage(data.queryParams).then((response) => {
    const { page } = response.data;
    console.log("回收列表", page);
    data.tableData = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tagId,
        portTypeName,
        assetsTypeName,
        level,
      } = cur;
      let arr = tagId.split(","),
        names = [];
      arr.map((item) => {
        let obj = tagList.value.find((_) => _.tagId == item);
        // console.log(obj)
        !!obj ? names.push(obj.tag) : "";
      });
      res.push({
        ...cur,
        tagName: names.join("，") || "-",
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        portTypeNameStr: portTypeName || "-",
        assetsTypeNameStr: assetsTypeName || "-",
        levelStr: level || "-",
      });
      return res;
    }, []);
    total.value = page.total;
    loading.value = false;
    nextTick(() => {
      data.tableData.forEach((item) => {
        if (data.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, data.tableAllSelectedId)
          data.ledgerTable.toggleRowSelection(item, true);
        } else {
          data.ledgerTable.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
    console.log(data.tableData);
  });
  devicePage({
    ...data.queryParams,
    current: 1,
    size: 9999999,
    isRecycle: 1,
  }).then((res) => {
    // console.log(res)
    data.tableData_all = res.data.page.records;
  });
}

async function getTagList() {
  await getDeviceTag().then((response) => {
    // console.log('tagList', response.data)
    tagList.value = response.data;
  });
}

function getPosition(d) {
  data.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree2 = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    data.positionNodeList = addAttr2(tree2);
    positionList.value = treeToArray(response.data);
    console.log("positionNodeList", data.positionNodeList);
    // console.log('positionList ==>', positionList.value)
  });
}
// getPositionTreeList();

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1;
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

function addAttr2(data, num = 0) {
  num++;
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = num != 3 || data[j].status == 1;
    // console.log(num, data[j]);
    if (data[j].children.length > 0) {
      addAttr2(data[j].children, num);
    }
  }
  return data;
}

function getTypeList() {
  getDeviceType().then((response) => {
    typeList.value = response.data;
    typeList_disabled.value = response.data.reduce((res, cur) => {
      res.push({
        ...cur,
        disabled: cur.typeName == "智慧大屏",
      });

      return res;
    }, []);
    // console.log('typeList_disabled', typeList_disabled.value)
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    isRecycle: 1,
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
}
/** 表单重置 */
function repairReset() {
  data.repairRef.resetFields();
  data.repairForm = {
    type: 1,
    userId: useUserStore().userId + "",
    repairName: useUserStore().nickName,
    repairPhone: useUserStore().phonenumber,
    repairTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    deviceCodeList: [],
    remark: "",
    resource: "管理后台",
    channel: "管理员主动报障",
    images: "",
  };
  // console.log("当前报障人信息", data.repairForm);
}
/** 搜索按钮操作 */
function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.positionIds = d?._checkList;
  }
  queryParams.value.current = 1;
  queryParams.value.startTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[0]
    : "";
  queryParams.value.endTime = Array.isArray(queryParams.value.time)
    ? queryParams.value.time[1]
    : "";
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.startTime = "";
  queryParams.value.endTime = "";
  delete queryParams.value.sort;
  proxy.resetForm("queryRef");
  queryParams.value.positionIds = [];
  queryParams.value.size = 10;
  proxy.$refs.locateRef.resetQuery();
  // proxy.$refs.treeRef.setCheckedNodes([]);
  // handleQueryPosition();
}
// 根据类型动态修改校验规则
function handleChangeType(val) {
  let name = "";
  typeList.value.forEach((item) => {
    if (item.id == val) name = item.typeName;
  });
  data.rules = {};
  if (name == "智慧大屏") {
    if (baseRules.value.deviceCode[1]) delete baseRules.value.deviceCode[1];
    Object.assign(data.rules, baseRules.value, smartRules.value);
  } else {
    baseRules.value.deviceCode[1] = {
      pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
      message: '设备编码不能以"ZHDP"开头',
      trigger: "blur",
    };
    Object.assign(data.rules, baseRules.value);
  }
  ledgerRef.value.clearValidate();
}
/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      data.ledgerTable.setCurrentRow(null);
      data.ledgerTable.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.deviceId);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      data.ledgerTable.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    data.ledgerTable.setCurrentRow(row);
    data.ledgerTable.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      data.tableAllSelectedId.push(item.deviceId);
      data.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.deviceId);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = data.tableData;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        data.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 新增设备操作 */
function handleAdd() {
  router.push("addDevice");
}
/** 查看按钮操作 */
function handleCheck(row, type) {
  // 保存当前页码到localStorage
  localStorage.setItem("deviceManagePage", queryParams.value.current);

  router.push({
    path: "deviceInfo",
    query: {
      id: row.deviceId,
      type,
      from: 4,
    },
  });
}
/*

 */
function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    if (valid) {
      data.repairForm.deviceCodeList =
        repairList.value.map((item) => item.deviceCode) || [];
      data.repairForm.images = data.repairForm.images.split(",") || "";
      deviceRepair(data.repairForm).then((res) => {
        proxy.$modal.msgSuccess("报障成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

/* 回复按钮操作 */
function handleRecover(row) {
  const deviceIds = row?.deviceId ? [row.deviceId] : tableAllSelectedId.value;
  const deviceNames = [];
  if (deviceIds.length === 0) {
    proxy.$modal.msgError("请选择要恢复的设备");
    return;
  }

  // 获取设备名称
  if (row?.deviceId) {
    deviceNames.push(row.deviceName);
  } else {
    deviceIds.forEach((id) => {
      const device = data.tableAllSelectedRow.find(
        (item) => item.deviceId === id
      );
      if (device) {
        deviceNames.push(device.deviceName);
      }
    });
  }

  proxy.$modal
    .confirm(
      `是否确认${
        !row?.deviceId ? "批量" : ""
      }恢复设备名称为【${deviceNames.join("、")}】的数据项？`
    )
    .then(() => {
      return recoverDevice({
        isRecycle: 0,
        deviceId: deviceIds.join(","),
      });
    })
    .then(() => {
      sendPointRequest({
        event: "Click",
        eventDescribe: `点击恢复设备`,
        content: "",
        num: 1,
      });
      proxy.$modal.msgSuccess("恢复成功");
      data.tableAllSelectedId = []; // 操作成功后清空选中的id
      data.tableAllSelectedRow = []; // 操作成功后清空选中的数据
      getList();
    })
    .catch(() => {});
}

/** 彻底删除按钮操作 */
function handleDelete(row) {
  const deviceIds = row?.deviceId ? [row.deviceId] : tableAllSelectedId.value;
  const deviceNames = [];
  if (deviceIds.length === 0) {
    proxy.$modal.msgError("请选择要删除的设备");
    return;
  }

  // 获取设备名称
  if (row?.deviceId) {
    deviceNames.push(row.deviceName);
  } else {
    deviceIds.forEach((id) => {
      const device = data.tableAllSelectedRow.find(
        (item) => item.deviceId === id
      );
      if (device) {
        deviceNames.push(device.deviceName);
      }
    });
  }

  const deviceStatusList = [
    ...new Set(data.tableAllSelectedRow.map((item) => item.deviceStatus)),
  ];

  console.log(deviceIds, data.tableAllSelectedRow, "deviceIds");
  proxy.$modal
    .confirm(
      `是否确认${
        !row?.deviceId ? "批量" : ""
      }彻底删除设备名称为【${deviceNames.join("、")}】的数据项？`
    )
    .then(() => {
      recoverDevice({
        isRecycle: 2,
        deviceId: deviceIds.join(","),
      }).then((res) => {
        if (res.code == 200) {
          sendPointRequest({
            event: "Click",
            eventDescribe: `点击${!row.deviceId ? "批量" : ""}彻底删除`,
            content: !row.deviceId
              ? (() => {
                  return deviceStatusList
                    .map((status) => deviceStatusObj[status].status)
                    .join(",");
                })()
              : deviceStatusObj[row.deviceStatus].status,
            num: 1,
          });
          proxy.$modal.msgSuccess("删除成功");
          data.tableAllSelectedId = []; // 操作成功后清空选中的id
          data.tableAllSelectedRow = []; // 操作成功后清空选中的数据
          getList();
        }
      });
    })
    .catch(() => {});
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  handleQuery();
}

// 处理节点勾选事件
const handleNodeCheck = (node, checkedStatus) => {
  console.log(node, checkedStatus);
  // 获取当前节点的选中状态
  const isChecked = treeRef.value.getCheckedKeys().includes(node.id);

  // 当父节点被勾选时，递归勾选所有子节点
  if (isChecked && node.children) {
    checkChildren(node, true);
  }

  queryParams.value.positionIds = treeRef.value.getCheckedKeys();
  // sendPointRequest({
  //   event: "Click",
  //   eventDescribe: "点击安装位置筛选框",
  //   content: "",
  //   num: 1,
  // });
  handleQuery();
  // 注意：子节点取消勾选时不需特殊处理，父节点会自动保持勾选
};

// 递归勾选所有子节点
const checkChildren = (node, checked) => {
  if (node.children) {
    node.children.forEach((child) => {
      treeRef.value.setChecked(child.id, checked); // 勾选当前子节点
      if (child.children) checkChildren(child, checked); // 递归处理孙节点
    });
  }
};

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

getTagList();
getTypeList();
getUsers();
getList();
</script>
<style scoped lang="scss">
.ascending.active {
  border-bottom-color: #4095e5;
}
.descending.active {
  border-top-color: #4095e5;
}
.dialog-header {
  font-size: 18px;
  display: flex;
  align-items: center;
  span {
    font-size: 12px;
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}
.qrcode-container {
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 8px 0; /* 继续减小内边距 */
}

.qrcode-wrapper {
  text-align: center;
  width: fit-content;
  max-width: 100%;
}

.qrcode-wrapper img {
  width: auto;
  max-width: 100%;
  height: auto;
}
.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}
.monitor_flex {
  display: flex;
  align-items: flex-start;
  gap: 0 10px;
  min-height: calc(100vh - 215px);
  .flex_1 {
    min-width: 200px;
    width: 18%;
    margin-right: 10px;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }
  .flex_2 {
    // flex: 1;
    width: 82%;

    // .search-list {
    //   flex-wrap: wrap;
    //   display: flex;
    //   flex-wrap: wrap;
    // }
  }
}
</style>
