<template>
  <div class="visit app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <el-button @click="handleBack"
            >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
          >
        </div>
      </template>

      <el-radio-group v-model="listType" class="mb12" @change="resetQuery">
        <el-radio-button label="满意度回访" value="满意度" />
        <el-radio-button label="投诉回访" value="投诉" />
      </el-radio-group>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="workOrderCode">
          <el-input
            v-model="queryParams.workOrderCode"
            placeholder="请输入工单编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="phone" v-if="listType == '满意度'">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入报障电话"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="phone" v-if="listType == '投诉'">
          <el-input
            v-model="queryParams.phone"
            placeholder="请输入联系方式"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="visit-main">
        <div class="visit-main_table">
          <el-table
            v-if="listType == '满意度'"
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="回访编号"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
              ><template #default="scope">
                {{ scope.row.visitCode || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访类型"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
            >
              <template #default> 满意度回访 </template></el-table-column
            >
            <el-table-column
              label="工单编号"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障描述"
              align="center"
              minWidth="200px"
              prop="remark"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障电话"
              align="center"
              minWidth="120px"
              prop="repairPhone"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障人"
              align="center"
              minWidth="120px"
              prop="repairName"
              show-overflow-tooltip
            />
            <el-table-column
              label="报障时间"
              align="center"
              minWidth="160px"
              prop="repairTime"
              show-overflow-tooltip
            />
            <el-table-column label="报障状态" align="center" minWidth="100px">
              <template #default="scope">
                <el-tag
                  v-if="statusObj[scope.row.status]"
                  effect="dark"
                  :type="statusObj[scope.row.status].type"
                  >{{ statusObj[scope.row.status].name }}</el-tag
                >
              </template>
            </el-table-column>
            <el-table-column label="电话录音" align="center" minWidth="170px">
              <template #default="scope">
                {{ scope.row.fileName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="满意度评价"
              align="center"
              minWidth="100px"
              prop="rate"
              show-overflow-tooltip
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleCheck(scope)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
            <el-table-column label="是否回访" align="center" minWidth="120px">
              <template #default="scope">
                {{ !!scope.row.access ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访结果"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.visitResults || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访时间"
              align="center"
              minWidth="160px"
              prop="visitTime"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.visitTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="180"
              align="center"
              v-if="!isNewUser"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleVisit(scope)"
                  >{{ scope.row.visitResults ? "修改" : "点击回访" }}</el-button
                >
                <el-button
                  v-if="scope.row.visitResults"
                  link
                  type="danger"
                  icon="Plus"
                  @click.stop="handleAdd(scope)"
                  >新增工单</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <el-table
            v-if="listType == '投诉'"
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="回访编号"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
              ><template #default="scope">
                {{ scope.row.visitCode || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访类型"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
            >
              <template #default> 投诉回访 </template></el-table-column
            >
            <el-table-column
              label="工单编号"
              align="center"
              minWidth="120px"
              prop="workOrderCode"
              show-overflow-tooltip
              ><template #default="scope">
                {{ scope.row.workOrderCode || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="投诉描述"
              align="center"
              minWidth="200px"
              prop="complaintsContent"
              show-overflow-tooltip
            />
            <el-table-column
              label="联系方式"
              align="center"
              minWidth="120px"
              prop="complaintsPhone"
              show-overflow-tooltip
            />
            <el-table-column
              label="投诉人"
              align="center"
              minWidth="120px"
              prop="complaintsName"
              show-overflow-tooltip
            />
            <el-table-column
              label="投诉时间"
              align="center"
              minWidth="160px"
              prop="complaintsTime"
              show-overflow-tooltip
            />
            <el-table-column label="电话录音" align="center" minWidth="170px">
              <template #default="scope">
                {{ scope.row.fileName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="投诉单详情"
              align="center"
              minWidth="100px"
              prop="rate"
              show-overflow-tooltip
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleCheck(scope)"
                  >查看</el-button
                >
              </template>
            </el-table-column>
            <el-table-column label="是否回访" align="center" minWidth="120px">
              <template #default="scope">
                {{ !!scope.row.access ? "是" : "否" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访结果"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.visitResults || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="回访时间"
              align="center"
              minWidth="160px"
              prop="visitTime"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.visitTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              v-if="!isNewUser"
              label="操作"
              min-width="180"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleVisit(scope)"
                  >{{ scope.row.visitResults ? "修改" : "点击回访" }}</el-button
                >
                <el-button
                  v-if="scope.row.visitResults"
                  link
                  type="danger"
                  icon="Plus"
                  @click.stop="handleAdd(scope)"
                  >新增工单</el-button
                >
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>

      <!-- 回访结果 -->
      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        title="回访结果"
        width="600"
        align-center
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancel"
      >
        <el-form :model="form" ref="formRef">
          <el-form-item
            label="回访结果"
            prop="visitResults"
            :rules="[
              { required: true, message: '回访结果不能为空', trigger: 'blur' },
            ]"
          >
            <el-input
              v-model="form.visitResults"
              style="width: 100%"
              :rows="2"
              type="textarea"
              maxlength="200"
              placeholder="请输入回访结果"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button type="primary" @click.stop="handleSubmit" v-throttle
            >确定</el-button
          >
        </template>
      </el-dialog>

      <!-- 评价详情 -->
      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible2"
        title="满意度评价"
        width="400"
        align-center
        @close="dialogVisible2 = false"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form :model="commentInfo">
          <el-form-item
            label="问题得到解决了吗？"
            prop="resolved"
            label-position="top"
          >
            <el-radio-group v-model="commentInfo.resolved" disabled>
              <el-radio value="已解决">已解决</el-radio>
              <el-radio value="未解决">未解决</el-radio>
            </el-radio-group>
          </el-form-item>
          <div style="color: #606266; font-weight: bold; margin-bottom: 20px">
            请对本次服务做出评价：
          </div>
          <el-form-item label="处理速度" prop="processingSpeed">
            <el-rate
              v-model="commentInfo.processingSpeed"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
              disabled
            />
          </el-form-item>
          <el-form-item label="服务态度" prop="serviceAttitude">
            <el-rate
              v-model="commentInfo.serviceAttitude"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
              disabled
            />
          </el-form-item>
          <el-form-item label="技术能力" prop="technicalAbility">
            <el-rate
              v-model="commentInfo.technicalAbility"
              :texts="['1星', '2星', '3星', '4星', '5星']"
              show-text
              disabled
            />
          </el-form-item>
          <el-form-item label="意见反馈" prop="feedback" label-position="top">
            <el-input
              type="textarea"
              v-model="commentInfo.feedback"
              placeholder="-"
              readonly
            />
          </el-form-item>
          <el-form-item
            label="是否接受回访？"
            prop="access"
            label-position="top"
          >
            <el-radio-group v-model="commentInfo.access" disabled>
              <el-radio :value="1">接受</el-radio>
              <el-radio :value="0">拒绝</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click.stop="dialogVisible2 = false">返回</el-button>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="visit">
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import {
  visitPage,
  workOrderVisit,
  complainReply,
  evaluationOne,
  visitComplaintsPage,
} from "@/api/mediaTeach/trouble";
import useUserStore from "@/store/modules/user";

const userStore = useUserStore();
console.log(userStore, userStore.roleId);
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const formRef = ref(null);
const state = reactive({
  isNewUser: !userStore.roleId,
  listType: "满意度",
  form: {
    id: "",
    visitResults: "",
  },
  commentInfo: {},
  curIdx: 0,
  dialogVisible: false,
  dialogVisible2: false,
  total: 0,
  loading: false,
  tableList: [],
  typeList: [],
  statusObj: {
    1: {
      type: "danger",
      name: "待处理",
    },
    2: {
      type: "warning",
      name: "待评价",
    },
    3: {
      type: "success",
      name: "已完成",
    },
  },
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    workOrderCode: "",
    phone: "",
  },
});

const {
  isNewUser,
  listType,
  form,
  curIdx,
  commentInfo,
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  dialogVisible2,
  statusObj,
  loading,
  total,
} = toRefs(state);

function handleAdd() {
  router.push({
    path: `/taskManage/taskCenter/addTodo`,
    query: {
      type: 2,
    },
  });
}

function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

function handleSubmit(item) {
  console.log(state.listType, state.form);

  formRef.value.validate((valid) => {
    if (valid) {
      if (state.listType == "满意度") {
        workOrderVisit(state.form).then((resp) => {
          console.log(resp);
          proxy.$modal.msgSuccess("操作成功");
          handleQuery();
          handleCancel();
        });
      } else {
        let obj = {
          ...state.form,
          visitResults: state.form.visitResults,
        };
        console.log(obj);
        complainReply(obj).then((resp) => {
          console.log(resp);
          proxy.$modal.msgSuccess("操作成功");
          handleQuery();
          handleCancel();
        });
      }
    }
  });
}

function handleCancel() {
  formRef.value.resetFields();
  state.dialogVisible = false;
}

function handleCheck({ row }) {
  if (state.listType == "满意度") {
    evaluationOne(row.troubleId).then((resp) => {
      console.log("评价详情", resp);
      if (resp.data) {
        state.commentInfo = resp.data;
        state.dialogVisible2 = true;
      }
    });
  }
  if (state.listType == "投诉") {
    router.push({
      path: state.isNewUser ? `/repairInfo_new` : `/repair/repairInfo`,
      query: {
        id: row.complaintsId,
        type: 0,
        visit: 1,
      },
    });
  }
}

function handleVisit({ row }) {
  console.log(row);

  if (state.listType == "满意度") {
    state.form.id = row.evaluationId;
    state.form.visitResults = row.visitResults || "";
  } else {
    state.form.id = row.complaintsId;
    state.form.visitResults = row.visitResults || "";
  }
  state.dialogVisible = true;
}

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

function getList() {
  state.loading = true;
  state.tableList = [];
  state.total = 0;
  if (state.listType == "满意度") {
    visitPage(state.queryParams)
      .then((resp) => {
        console.log("满意度列表", resp);
        if (resp.data) {
          const { records = {}, total = 0 } = resp.data;
          state.tableList = records || [];
          state.total = total;
        }
      })
      .finally(() => (state.loading = false));
  }
  if (state.listType == "投诉") {
    visitComplaintsPage(state.queryParams)
      .then((resp) => {
        console.log("投诉列表", resp);
        if (resp.data) {
          const { records = {}, total = 0 } = resp.data;
          state.tableList = records || [];
          state.total = total;
        }
      })
      .finally(() => (state.loading = false));
  }
}

onMounted(() => {
  state.listType = route.query.complaints == 1 ? "投诉" : "满意度";
  getList();
});
</script>
