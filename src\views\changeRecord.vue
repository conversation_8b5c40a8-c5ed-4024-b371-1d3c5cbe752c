<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="changeName">
          <el-input
            v-model="queryParams.changeName"
            placeholder="请输入变更人姓名"
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item label="" prop="changeTime">
          <el-date-picker
            type="daterange"
            v-model="queryParams.changeTime"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
            style="width: 300px"
            @change="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <div class="recording-main">
        <div class="recording-main_table">
          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
          >
            <el-table-column
              label="变更功能模块"
              align="center"
              minWidth="120px"
              prop="moduleName"
            />
            <el-table-column
              label="变更子功能模块"
              align="center"
              minWidth="120px"
            >
              <template #default="scope">
                {{ scope.row.childrenModule || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              label="变更原因"
              align="center"
              minWidth="120px"
            >
              <template #default="scope">
                {{ scope.row.reason || '-' }}
              </template>
            </el-table-column>
            <el-table-column
              label="变更时间"
              align="center"
              minWidth="120px"
              prop="operationTime"
            />
            <el-table-column
              label="变更人"
              align="center"
              minWidth="120px"
              prop="userName"
            />
            <el-table-column label="操作" min-width="120" align="center">
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="ViewReceipts(scope)"
                  >查看单据</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </div>
      </div>
      <el-dialog v-model="dialogVisible" title="满意度评价"> </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="recording">
import { useRoute } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { listRecord } from "@/api/system/record";
import { repairInfo } from "@/api/mediaTeach/trouble";

const route = useRoute();
const { proxy } = getCurrentInstance();
const state = reactive({
  dialogVisible: false,
  total: 0,
  loading: false,
  tableList: [],
  typeList: [],
  statusList: [
    { label: "待处理", value: 0, type: "danger" },
    { label: "已处理", value: 1, type: "success" },
  ],
  queryParams: {
    current: 1,
    size: 10,
    changeName: "",
    changeTime: "",
  },
});

const {
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  statusList,
  loading,
  total,
} = toRefs(state);

const formatDateTime = (date) => {
  if (!date) return '';
  const d = new Date(date);
  const pad = (n) => n.toString().padStart(2, '0');
  
  return `${d.getFullYear()}-${pad(d.getMonth() + 1)}-${pad(d.getDate())} ${pad(d.getHours())}:${pad(d.getMinutes())}:${pad(d.getSeconds())}`;
};

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

function handleDel({ row, $index }) {
  proxy.$modal.confirm(`确定要删除编号为${row.number}的录音信息？`).then(() => {
    state.tableList_all.splice($index, 1);
    // 同步更新完tableList_all后更新localStorage， 新增、删除、修改操作都需要做这一步，若页面有变更记录则同步更新变更记录列表
    // localStorage.setItem('WHYWPT_SPARELIST_ALL', JSON.stringify(state.tableList_all))

    setTimeout(() => {
      proxy.$modal.msgSuccess("操作成功");
      getList();
    }, 500);
  });
}

async function getList() {
  state.loading = true;
  try {
    const params = {
      pageNum: state.queryParams.current,
      pageSize: state.queryParams.size,
      userName: state.queryParams.changeName,
      operationTimeList: state.queryParams.changeTime ? [
        formatDateTime(state.queryParams.changeTime[0]),
        formatDateTime(state.queryParams.changeTime[1])
      ] : undefined
    };
    console.log("params:", params);
    
    const response = await listRecord(params);
    if (response.code == 200 && response.data) {
      console.log("获取列表成功:", response);
      state.tableList = response.data.rows;
      state.total = response.data.total;
    } else {
      proxy.$modal.msgError(response.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取列表失败:", error);
    proxy.$modal.msgError("获取数据失败");
  } finally {
    state.loading = false;
  }
}

function handleSearch(){
  getList();
}

function resetQuery(){
  queryParams.value = {
    current: 1,
    size: 10,
    changeName: "",
    changeTime: "",
  }
  handleSearch();
}

async function ViewReceipts(scope) {
  localStorage.setItem('changeRecordPage', queryParams.value.current);
  const { objectId, type } = scope.row;
  let path = '';
  
  switch (type) {
    case 1: // 设备管理
      path = `/deviceLedger/deviceInfo?id=${objectId}&type=0&from=3`;
      break;
    case 2: // 备件管理
      path = `/spareManage/spareInfo?id=${objectId}&type=0&changeId=1`;
      break;
    case 3: // 工单管理
      try {
        const response = await repairInfo({
          type: 1,
          troubleId: objectId
        });
        
        if (response.code === 200 && response.data) {
          const { status } = response.data.repairVO;
          switch (status) {
            case 0:
              path = `/taskManage/taskCenter/claimInfo?id=${objectId}`;
              break;
            case 1:
            case 4:
              path = `/taskManage/taskCenter/todoTaskInfo?id=${objectId}&type=0`;
              break;
            case 2:
            case 3:
              path = `/taskManage/taskCenter/handleTaskInfo?id=${objectId}`;
              break;
            default:
              proxy.$modal.msgError('未知的工单状态');
              return;
          }
        } else {
          proxy.$modal.msgError(response.msg || '获取工单信息失败');
          return;
        }
      } catch (error) {
        console.error('获取工单信息失败:', error);
        proxy.$modal.msgError('获取工单信息失败');
        return;
      }
      break;
    case 4: // 文档管理
      path = `/fileInfo?id=${objectId}&type=1&from=changeRecord`;
      break;
    case 5: // 应急管理
      path = `/emergencyManage/workOrderDetail?id=${objectId}`;
      break;
    case 6: // 项目信息
      path = `/order/projectInfo?id=${objectId}&type=0`;
      break;
    default:
      proxy.$modal.msgError('未知的单据类型');
      return;
  }

  proxy.$router.push(path);
}

onMounted(() => {
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem('changeRecordPage');
  if (savedPage) {
    queryParams.value.current = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem('changeRecordPage');
  }
getList();

});

</script>