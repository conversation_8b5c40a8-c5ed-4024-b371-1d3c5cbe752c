<template>
  <div class="complaint app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>投诉列表</span>
        </div>
      </template>
      <div class="complaint-main">
        <!-- 搜索区域 -->
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="codeAndName">
            <el-input
              v-model="queryParams.codeAndName"
              placeholder="请输入单号/投诉人"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="replyName">
            <el-input
              v-model="queryParams.replyName"
              placeholder="请输入回复人"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <!-- 表格区域 -->
        <el-table ref="tableRef" v-loading="loading" :data="tableList" border>
          <el-table-column
            label="投诉单编号"
            align="center"
            prop="code"
            min-width="140"
            show-overflow-tooltip
          />
          <el-table-column
            label="投诉类型"
            align="center"
            prop="type"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="投诉人"
            align="center"
            prop="name"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="投诉内容"
            align="center"
            prop="complaintsContent"
            show-overflow-tooltip
            min-width="200"
          >
            <template #default="scope">
              {{ scope.row.complaintsContent?.length > 50 ? scope.row.complaintsContent.substring(0, 50) + '...' : scope.row.complaintsContent }}
            </template>
          </el-table-column>
          <el-table-column
            label="联系方式"
            align="center"
            prop="phone"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="投诉时间"
            align="center"
            prop="complaintsTime"
            min-width="160"
            show-overflow-tooltip
          />
          <el-table-column
            label="回复人"
            align="center"
            min-width="160"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.replyName || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="回复时间"
            align="center"
            min-width="160"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.replyTime || "-" }}
            </template>
          </el-table-column>
          <el-table-column label="投诉状态" align="center" min-width="100">
            <template #default="scope">
              <el-tag :type="statusList[scope.row.status].type">
                {{ statusList[scope.row.status].label }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            width="150"
            fixed="right"
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Search"
                @click="handleCheck(scope.row, 0)"
              >
                查看
              </el-button>
              <el-button
                link
                v-if="!scope.row.status"
                type="success"
                icon="ChatDotRound"
                @click="handleCheck(scope.row, 1)"
              >
                回复
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup name="complaint">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { timeFormat } from "@/utils";
import { useRouter } from "vue-router";
import { complainList } from "@/api/mediaTeach/trouble";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  queryRef: null,
  total: 0,
  loading: false,
  // 状态选项
  statusList: [
    { label: "待回复", value: "待回复", type: "danger" },
    { label: "已回复", value: "已回复", type: "success" },
  ],
  // 查询参数
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    complaintNumber: "",
    complaintTime: [],
    status: "",
    type: 0
  },
  // 当前显示的表格数据
  tableList: [],
});

const { queryRef, total, loading, statusList, tableList, queryParams } =
  toRefs(state);

/** 获取列表数据 */
function getList() {
  state.loading = true;
  complainList(state.queryParams).then((response) => {
    if (response.data) {
      const { records, total } = response.data;
      state.tableList = records || [];
      state.total = total || 0;
      state.loading = false;
      console.log("投诉列表", records);
    }
  });
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 查看/回复按钮操作 */
function handleCheck(row, type) {
  // 保存当前页码到localStorage
  localStorage.setItem('repairPage', queryParams.value.pageNum);

  router.push(`repairInfo?id=${row.id}&type=${type}`);
}

onMounted(() => {
  // 从localStorage获取保存的页码
  const savedPage = localStorage.getItem('repairPage');
  if (savedPage) {
    queryParams.value.pageNum = Number(savedPage);
    // 清除保存的页码
    localStorage.removeItem('repairPage');
  }
  getList();
});
</script>

<style lang="scss" scoped>
.complaint {
  &-main {
    &_search {
      margin-bottom: 20px;
    }
    &_table {
      margin-top: 20px;
    }
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ddd;
    margin-right: 0;
    padding-bottom: 20px;
  }
}

.info-row {
  display: flex;
  justify-content: space-between;
  width: 100%;

  .form-item-left {
    flex: 1;
    margin-right: 20px;
  }

  .form-item-right {
    flex: 1;
    margin-right: 0;
  }

  :deep(.el-form-item__content) {
    justify-content: flex-start;
  }
}

.complaint-content,
.reply-content {
  line-height: 24px;
  padding: 8px 12px;
  background-color: #f5f7fa;
  border-radius: 4px;
  min-height: 60px;
  white-space: pre-wrap;
  word-break: break-all;
}

:deep(.el-form-item__label) {
  font-weight: bold;
}

:deep(.el-dialog__body) {
  padding: 30px;
}
</style>