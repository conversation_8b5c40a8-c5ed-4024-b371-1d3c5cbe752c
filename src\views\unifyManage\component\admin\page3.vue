<template>
  <div class="unify-main_charts">
    <div class="computer">
      <div class="tab" :class="curTab == 1 ? 'right' : ''">
        <div class="tab-item" @click="curTab = 0"></div>
        <div class="tab-item" @click="curTab = 1"></div>
      </div>
      <div class="main" v-if="curTab == 0">
        <div class="block block1">
          <div class="block-title long">请选择统计机构和时间范围</div>
          <div class="radioBox">
            <div class="radio">
              <div
                class="radio-item"
                v-for="item in corpList"
                :key="item.corpId"
                @click="handleCheck(item.corpId, 0)"
              >
                <div
                  class="check"
                  :class="
                    curCorpIdList.indexOf(item.corpId) != -1 ? 'active' : ''
                  "
                ></div>
                {{ item.corpName }}
              </div>
            </div>
          </div>

          <div class="timeRange">
            <el-date-picker
              popper-class="custom-popper"
              type="daterange"
              v-model="curTimeRange"
              start-placeholder="请选择统计开始时间"
              end-placeholder="请选择统计结束时间"
              range-separator="一"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :disabled-date="disabledDateFn"
              @change="initCompute"
            />
          </div>
          <div class="operation">
            <div class="operation-list">
              <div
                class="operation-item"
                :class="item.type == 'symbol' ? 'symbol' : ''"
                :draggable="true"
                @dragstart="onDragStart($event)"
                @dragover="onDragOver($event)"
                @drop="onDrop($event)"
                v-for="(item, index) in operationList"
                :key="index"
              >
                <div
                  class="operation-item_label"
                  :class="item.type == 'symbol' ? 'symbol' : ''"
                >
                  {{ item.label }}
                </div>
                <div v-if="item.type == 'var'" class="operation-item_value">
                  {{ item.value > 999999999 ? "999999999+" : item.value }}
                </div>
                <div
                  v-if="item.value !== ''"
                  class="close"
                  @click="handleClose(item, index)"
                ></div>
              </div>
              <div class="limit">{{ operationList.length }} / 11</div>
            </div>
            <div class="operation-btns">
              <div class="btn" @click="handleCalculate">点击运算</div>
              <div class="btn result" @click="handleResult">
                <div class="result-tip">
                  结果框<br />
                  （点击可加入运算框）
                </div>
                <div class="result-cont">
                  {{
                    resultNum > 999999999
                      ? "999999999+"
                      : resultNum === 0
                      ? 0
                      : resultNum || ""
                  }}
                </div>
              </div>
              <div class="btn" @click="exportResult">点击导出</div>
            </div>
          </div>
        </div>
        <div class="block block2">
          <div class="block-title">请选择运算符</div>
          <div class="symbol">
            <div
              class="symbol-item"
              :class="`symbol${index + 1}`"
              v-for="(item, index) in symbolList"
              :key="item"
              @click="handleSymbol(item)"
            ></div>
          </div>
        </div>
        <div class="block block3" v-if="showVar">
          <div class="block-title title2">请选择字段</div>
          <div class="var">
            <div
              class="var-item"
              v-for="item in varList"
              :key="item"
              @click="handleVar(item)"
            >
              {{ item.name }}
            </div>
          </div>
        </div>
      </div>
      <div class="main" v-if="curTab == 1">
        <div class="block block1">
          <div class="block-title long">请选择统计机构和时间范围</div>
          <div class="radio">
            <div
              class="radio-item"
              v-for="item in corpList"
              :key="item.corpId"
              @click="handleCheck(item.corpId, 1)"
            >
              <div
                class="check"
                :class="
                  curCorpIdList2.indexOf(item.corpId) != -1 ? 'active' : ''
                "
              ></div>
              {{ item.corpName }}
            </div>
          </div>
          <div class="timeRange">
            <el-date-picker
              popper-class="custom-popper"
              type="daterange"
              v-model="curTimeRange2"
              start-placeholder="请选择统计开始时间"
              end-placeholder="请选择统计结束时间"
              range-separator="一"
              value-format="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD"
              :disabled-date="disabledDateFn"
            />
          </div>
        </div>
        <div class="block block3">
          <div class="var" style="padding-left: 1vw">
            <div
              class="var-item"
              :class="curOptList.indexOf(item) != -1 ? 'active' : ''"
              v-for="item in varList2"
              :key="item"
              @click="handleCheckOpt(item)"
            >
              {{ item }}
            </div>
            <div class="var-item">敬请期待...</div>
          </div>
        </div>
        <div class="block block4">
          <div class="exportBtn" @click="exportField">导出统计表格</div>
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      :width="title == '请选择分部门人数' ? 700 : 550"
      :title="title"
      v-model="dialogVisible"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancel"
    >
      <div class="dialogMain" v-if="title == '请选择运行异常类型'">
        <el-radio-group v-model="statusVal" @change="handleChangeRadio">
          <el-radio :value="0"
            >同时拥有所有异常类型的设备数量（除关机状态）</el-radio
          >
          <el-radio :value="1">
            同时包含一项/多项异常的设备数量
            <el-select
              v-model="statusSelList"
              :disabled="statusVal != 1"
              style="width: 250px"
              multiple
              clearable
              placeholder="请选择设备异常状态"
              @change="handleChangeStatus"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-radio>
          <el-radio :value="2">
            仅含一种/多种异常的设备数量
            <el-select
              v-model="statusSelList2"
              :disabled="statusVal != 2"
              style="width: 250px"
              multiple
              clearable
              placeholder="请选择设备异常状态"
              @change="handleChangeStatus"
            >
              <el-option
                v-for="item in statusList"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-radio>
        </el-radio-group>
        <div style="text-align: center; margin: 10px 0 0">
          总数量：{{ dialogForm.deviceTotal || 0 }}台
        </div>
      </div>
      <div class="dialogMain" v-if="title == '请选择分部门人数'">
        <div class="dialogMain-deptTitle">
          已选择的部门：<span>共{{ dialogForm.peopleTotal }}人</span>
        </div>
        <div class="dialogMain-deptChecked">
          <div
            class="dialogMain-deptChecked_item"
            v-for="(item, index) in dialogForm.deptRows"
            :key="item.deptId"
          >
            {{ item.deptName }}
            <div class="deptClose" @click="handleDelDept(item, index)">×</div>
          </div>
        </div>
        <div class="dialogMain-corpSel">
          请选择部门
          <el-select
            v-model="dialogForm.corpId"
            style="width: 250px"
            clearable
            placeholder="请选择机构"
            @change="handleChangeCorp"
          >
            <el-option
              v-for="item in dialogForm.corpList"
              :key="item.corpId"
              :label="item.corpName"
              :value="item.corpId"
            />
          </el-select>
          <el-tree-select
            ref="deptTreeRef"
            v-model="dialogForm.deptSelList"
            :data="dialogForm.deptTreeList"
            node-key="deptId"
            :filter-node-method="filterNodeMethod"
            filterable
            multiple
            :render-after-expand="false"
            show-checkbox
            check-strictly
            clear
            check-on-click-node
            style="width: 250px"
            placeholder="请选择部门"
            :props="dialogForm.deptProps"
            :disabled="!dialogForm.corpId"
            @change="canWatch = true"
            @node-click="canWatch = true"
          />
        </div>
      </div>
      <div class="dialogMain" v-if="title == '导出结果'">
        <el-form
          :model="dialogForm"
          ref="dialogRef"
          :rules="{
            resultName: [
              { required: true, message: '请输入结果名称', trigger: 'blur' },
            ],
          }"
        >
          <el-form-item label="请输入导出结果名称" prop="resultName">
            <el-input
              v-model="dialogForm.resultName"
              style="width: 250px"
              clearable
            />
          </el-form-item>
        </el-form>
      </div>
      <template #footer>
        <div>
          <el-button type="primary" @click="handleConfirm">确定</el-button>
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
    
<script setup name="unifyIndex">
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
  watch,
} from "vue";
import {
  getAdminCorp,
  smartFieldStatistics,
  smartOperationStatistics,
  smartOperationResult,
  getAdminDept,
  findNum,
  getDeviceNum,
} from "@/api/unify";
import { downloadBlob, treeToArray, timeFormat } from "@/utils";
import { isNumber } from "@vueuse/core";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  dragElement: null,
  dialogRef: null,
  canWatch: true,
  deptTreeRef: null,
  statusVal: 0,
  statusSelList2: [],
  statusSelList: [],
  showVar: false,
  dialogVisible: false,
  title: "导出结果",
  dialogForm: {
    resultName: "",
    deviceTotal: 0,
    peopleTotal: 0,
    corpId: "",
    deptSelList: [],
    corpList: [],
    deptTreeList: [],
    deptList: [],
    deptProps: {
      label: "deptName",
      chilfren: "children",
    },
    deptRows: [],
  },
  statusList: [
    { label: "关机", value: 0, type: "info" },
    { label: "运行良好", value: 1, type: "success" },
    { label: "网速过慢", value: 2, type: "warning" },
    { label: "GPU过高", value: 3, type: "danger" },
    { label: "磁盘占用率过高", value: 4, type: "danger" },
    { label: "CPU过高", value: 5, type: "danger" },
    { label: "内存过高", value: 6, type: "danger" },
  ],
  operationList: [{ type: "var", label: "", value: "" }],
  resultNum: "",
  curTimeRange: [],
  curTimeRange2: [],
  curCorpIdList: [],
  curCorpIdList2: [],
  curTab: 0,
  curOpt: "设备总数量",
  curOptList: [],
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  corpList: [{ corpName: "全选", corpId: 0 }],
  symbolList: ["+", "-", "×", "÷"],
  varList: [
    { name: "设备总数量", varName: "deviceTotal" },
    { name: "信息化设备总数量", varName: "deviceInfoTotal" },
    { name: "非信息化设备总数量", varName: "deviceNoInfoTotal" },
    { name: "当前开机数量", varName: "deviceRunTotal" },
    { name: "当前运行良好数量", varName: "deviceGoodTotal" },
    { name: "当前运行异常数量", varName: "deviceAbnormalTotal" },
    { name: "合计工单数量", varName: "deviceWorkOrderTotal" },
    { name: "合计已处理工单数量", varName: "deviceWorkOrderProcessedTotal" },
    { name: "合计待处理工单数量", varName: "deviceWorkOrderUnprocessedTotal" },
    { name: "合计待认领工单数量", varName: "deviceWorkOrderUnclaimedTotal" },
    { name: "设备开关机次数", varName: "deviceSwitchTotal" },
    { name: "设备USB插入次数", varName: "deviceUsbInsertTotal" },
    { name: "设备USB拔出次数", varName: "deviceUsbRemoveTotal" },
    { name: "设备报废数量", varName: "deviceScrapTotal" },
    { name: "机构总人数", varName: "corpPeopleTotal" },
    { name: "分部门人数", varName: "deptTotal" },
  ],
  varList2: ["CPU类型", "硬盘大小", "内存大小", "品牌"],
  varObj: {
    deptTotal: 0,
    deviceAbnormalTotal: 0,
    deviceTotal: 0,
    deviceInfoTotal: 0,
    deviceNoInfoTotal: 0,
    deviceRunTotal: 0,
    deviceGoodTotal: 0,
    deviceExceptionTotal: 0,
    deviceMemoryHighTotal: 0,
    deviceCpuHighTotal: 0,
    deviceOffTotal: 0,
    deviceDiskHighTotal: 0,
    deviceGpuHighTotal: 0,
    deviceNetSpeedTotal: 0,
    deviceScrapTotal: 0,
    deviceWorkOrderTotal: 0,
    deviceWorkOrderProcessedTotal: 0,
    deviceWorkOrderUnprocessedTotal: 0,
    deviceWorkOrderUnclaimedTotal: 0,
    deviceSwitchTotal: 0,
    deviceUsbInsertTotal: 0,
    deviceUsbRemoveTotal: 0,
    corpPeopleTotal: 0,
  },
});

const {
  dragElement,
  dialogRef,
  canWatch,
  deptTreeRef,
  statusVal,
  statusSelList2,
  statusSelList,
  varObj,
  showVar,
  dialogVisible,
  title,
  dialogForm,
  deptList,
  statusList,
  curOptList,
  varList2,
  curOpt,
  varList,
  symbolList,
  operationList,
  resultNum,
  curTimeRange,
  curTimeRange2,
  curCorpIdList,
  curCorpIdList2,
  corpList,
  curTab,
  curTime,
  timer,
  timer2,
} = toRefs(state);

watch(
  [() => state.dialogForm.corpId, () => state.dialogForm.deptSelList],
  ([newId, newList], [oldId, oldList]) => {
    // console.log([newId, oldId], [newList, oldList]);
    const arr = [...new Set(newList.concat(oldList))];
    const diff = arr.filter(
      (_) => !newList.includes(_) || !oldList.includes(_)
    );
    console.log(diff);
    if (newId == oldId && diff.length > 0 && state.canWatch) {
      console.log("处理已选择部门");
      state.canWatch = true;
      diff.map((item) => {
        const idx = state.dialogForm.deptRows.findIndex(
          (_) => _.deptId == item
        );
        if (idx == -1) {
          const idx2 = state.dialogForm.deptList.findIndex(
            (_) => _.deptId == item
          );
          state.dialogForm.deptRows.push(state.dialogForm.deptList[idx2]);
        } else {
          state.dialogForm.deptRows.splice(idx, 1);
        }
        state.dialogForm.peopleTotal = getDeptPeopleTotal();
      });
    }
  }
);

const onDragStart = (e) => {
  console.log(e);
  // 获取当前拖拽元素， 暂存拖拽元素
  state.dragElement = e.currentTarget;
};

const onDragOver = (e) => {
  // 默认的当你dragover的时候会阻止你做drop的操作，所以需要取消这个默认
  e.preventDefault();
};

const onDrop = (e) => {
  console.log(e);
  // 当拖动结束的时候，给拖动div所在的位置下面的div做drop事件
  let dropElement = e.currentTarget;
  let flag =
    dropElement.className.includes("symbol") ==
    state.dragElement.className.includes("symbol");
  console.log(flag);
  if (state.dragElement != null && state.dragElement != dropElement && flag) {
    const divs = document.getElementsByClassName("operation-item");
    let idx1 = -1,
      idx2 = -1;
    for (let i = 0; i < divs.length; i++) {
      if (divs[i] == state.dragElement) idx1 = i;
      if (divs[i] == dropElement) idx2 = i;
    }
    const temp = JSON.parse(JSON.stringify(state.operationList[idx2]));
    state.operationList[idx2] = JSON.parse(
      JSON.stringify(state.operationList[idx1])
    );
    state.operationList[idx1] = temp;
    console.log(idx1, idx2);
  }
};

const exportResult = () => {
  if (state.resultNum === "") {
    proxy.$modal.msgWarning("运算结果不能为空");
    return;
  }
  title.value = "导出结果";
  state.dialogVisible = true;
};

const getDeptPeopleTotal = () => {
  let sum = 0;
  state.dialogForm.deptRows.map((item) => {
    sum += item.total || 0;
  });
  return sum;
};

const handleDelDept = (item, index) => {
  const idx = state.dialogForm.deptSelList.indexOf(item.deptId);
  idx != -1 ? state.dialogForm.deptSelList.splice(idx, 1) : "";
  state.dialogForm.deptRows.splice(index, 1);
  state.dialogForm.peopleTotal = getDeptPeopleTotal();
};

const filterNodeMethod = (value, data) => {
  // console.log(data, value);
  return data.deptName.includes(value);
};

const handleChangeCorp = (val) => {
  state.dialogForm.deptSelList = [];
  if (val) {
    getAdminDept({ corpId: val }).then((res) => {
      if (res.data) {
        state.dialogForm.deptTreeList = res.data;
        state.dialogForm.deptList = treeToArray(res.data);

        console.log("回显已选择部门");
        const checkIds = state.dialogForm.deptRows.map((item) => item.deptId);
        const curDeptIds = state.dialogForm.deptList.map((item) => item.deptId);
        const arr = [...new Set(checkIds.concat(curDeptIds))];
        state.dialogForm.deptSelList = arr.filter(
          (_) => checkIds.includes(_) && curDeptIds.includes(_)
        );
        state.canWatch = state.dialogForm.deptSelList.length < 1;
      }
      console.log(res, state.dialogForm.deptList);
    });
  }
};

const handleConfirm = () => {
  if (state.title == "导出结果") {
    state.dialogRef.validate((valid) => {
      if (valid) {
        proxy.$modal.loading();
        let d = {
          time: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
          formula: state.operationList.map((item) => item.label).join(""),
          num: state.operationList
            .map((item) => item.value)
            .filter((item) => isNumber(item))
            .join(","),
          result: state.resultNum,
          name: state.dialogForm.resultName,
        };
        console.log("导出运算结果传参", d);
        smartOperationResult(d)
          .then((res) => {
            console.log(res);
            downloadBlob(res, "application/vnd.ms-excel", "运算结果表格");
          })
          .finally(() => {
            proxy.$modal.closeLoading();
            handleCancel();
          });
      }
    });
    return;
  }
  if (state.title == "请选择运行异常类型") {
    state.varObj.deviceAbnormalTotal = state.dialogForm.deviceTotal || 0;
    handleVar(state.varList[5], false);
  }
  if (state.title == "请选择分部门人数") {
    if (state.dialogForm.deptRows.length > 0) {
      state.varObj.deptTotal = state.dialogForm.peopleTotal || 0;
      handleVar(state.varList[15], false);
    } else {
      proxy.$modal.msgWarning("请选择部门");
      return;
    }
  }
  handleCancel();
};

const handleCancel = () => {
  state.dialogVisible = false;
  if (state.title == "请选择运行异常类型") {
    state.statusVal = 0;
    state.dialogForm.deviceTotal = 0;
    state.statusSelList = [];
    state.statusSelList2 = [];
  }
  if (state.title == "请选择分部门人数") {
    state.dialogForm.deptRows = [];
    state.dialogForm.corpId = "";
    state.dialogForm.deptSelList = [];
    state.dialogForm.peopleTotal = 0;
  }
  if (state.title == "导出结果") {
    state.dialogForm.resultName = "";
  }
};

const handleChangeStatus = (val) => {
  console.log(val);
  if (val.length > 0) {
    getDeviceTotal(val, state.dialogForm.statusVal == 1);
  } else {
    state.dialogForm.deviceTotal = 0;
  }
};

const handleChangeRadio = (val) => {
  state.statusSelList = [];
  state.statusSelList2 = [];
  state.dialogForm.deviceTotal = 0;
  if (val == 0) {
    getDeviceTotal([1, 2, 3, 4, 5, 6], true);
  }
};

const handleResult = () => {
  if (state.resultNum === "") {
    proxy.$modal.msgWarning("请先进行运算");
  } else {
    const len = state.operationList.length;
    if (state.operationList[len - 1].type == "symbol") {
      state.operationList.push({
        type: "var",
        label: "运算结果",
        value: state.resultNum,
      });
      nextTick(() => {
        document
          .getElementsByClassName("operation-item")
          [len].setAttribute("data-value", new Date().getTime());
      });
    } else {
      // state.curOpt = "";
      proxy.$modal.msgWarning("请先选择运算符");
    }
  }
};

const handleCalculate = () => {
  const len = state.operationList.length;
  if (
    state.operationList[len - 1].type == "symbol" ||
    state.operationList.length == 1 ||
    state.operationList[len - 1].value === ""
  ) {
    proxy.$modal.msgWarning("请补全表达式");
    return;
  }

  let calculate = () => {
    let arr = state.operationList.map((item) =>
      item.value == "×" ? "*" : item.value == "÷" ? "/" : item.value
    );
    console.log(arr);
    return `return ${arr.join("")}`;
  };
  let cal = new Function(calculate());
  state.resultNum = cal().toFixed(2) * 1 || 0;
  console.log(cal(), state.resultNum);
};

const handleSymbol = (item) => {
  const len = state.operationList.length;
  if (len == 11) {
    proxy.$modal.msgWarning("已达运算框数量上限");
    return;
  }

  if (state.operationList[len - 1].type == "var") {
    if (state.operationList[len - 1].value === "") {
      proxy.$modal.msgWarning("请先选择字段");
    } else {
      state.operationList.push({
        type: "symbol",
        label: item,
        value: item,
      });
      nextTick(() => {
        document
          .getElementsByClassName("operation-item")
          [len].setAttribute("data-value", new Date().getTime());
      });
    }
  } else {
    proxy.$modal.msgWarning("请先选择字段");
  }
};

const getDeviceTotal = (status, isAnd) => {
  let d = {
    corpIdList: [
      ...corpList.value
        .filter(
          (item) =>
            curCorpIdList.value.indexOf(item.corpId) != -1 && item.corpId != 0
        )
        .map((item) => item.corpId),
    ],
    statu: [1, 2, 3, 4, 5, 6],
    isAnd: true,
  };
  console.log("设备数量传参", d);
  getDeviceNum(d).then((res) => {
    console.log(res);
    if (res.code == 200 && res.data) {
      state.dialogForm.deviceTotal = res.data.abnormalDeviceCount || 0;
    }
  });
};

const handleVar = async (item, flag = true) => {
  if (flag && item.name == "当前运行异常数量") {
    state.title = "请选择运行异常类型";
    state.dialogVisible = true;
    getDeviceTotal([1, 2, 3, 4, 5, 6], true);
    return;
  } else if (flag && item.name == "分部门人数") {
    state.title = "请选择分部门人数";
    state.dialogVisible = true;
    getCoprList(true);
    return;
  }

  let deviceNum = 0,
    isDevice = false;

  if (item.name == "当前开机数量" || item.name == "当前运行良好数量") {
    try {
      let d = {
        corpIdList: [
          ...corpList.value
            .filter(
              (item) =>
                curCorpIdList.value.indexOf(item.corpId) != -1 &&
                item.corpId != 0
            )
            .map((item) => item.corpId),
        ],
        status: [1, 2, 3, 4, 5, 6],
        isAnd: true,
      };
      console.log("设备数量传参", d);
      await getDeviceNum(d).then((res) => {
        console.log(res);
        if (res.code == 200 && res.data) {
          isDevice = true;
          const { abnormalDeviceCount, currentDeviceCount, goodDeviceCount } =
            res.data;
          deviceNum =
            item.name == "当前开机数量" ? currentDeviceCount : goodDeviceCount;
        }
      });
    } catch (error) {
      console.log(error);
    }
  }

  if (!state.operationList[0].label) {
    state.operationList[0].label = item.name;
    state.operationList[0].value = isDevice
      ? deviceNum
      : state.varObj[item.varName];
    nextTick(() => {
      document
        .getElementsByClassName("operation-item")[0]
        .setAttribute("data-value", new Date().getTime());
    });
  } else {
    const len = state.operationList.length;
    if (state.operationList[len - 1].type == "symbol") {
      state.operationList.push({
        type: "var",
        label: item.name,
        value: isDevice ? deviceNum : state.varObj[item.varName],
      });
      nextTick(() => {
        document
          .getElementsByClassName("operation-item")
          [len].setAttribute("data-value", new Date().getTime());
      });
    } else {
      proxy.$modal.msgWarning("请先选择运算符");
    }
  }
};

const handleClose = (item, index) => {
  console.log(index);
  if (state.operationList[index + 1]) {
    state.operationList.splice(index + 1, 1);
  }
  if (index == 0 && state.operationList.length == 1) {
    state.operationList[0].label = "";
    state.operationList[0].value = "";
  } else {
    state.operationList.splice(index, 1);
  }
};

const disabledDateFn = (date) => {
  return new Date().getTime() < new Date(date).getTime();
};

const exportField = () => {
  if (state.curCorpIdList2.length < 1) {
    proxy.$modal.msgWarning("请选择机构");
    return;
  }
  if (!state.curTimeRange2 || state.curTimeRange2.length < 1) {
    proxy.$modal.msgWarning("请选择时间范围");
    return;
  }
  if (state.curOptList.length < 1) {
    proxy.$modal.msgWarning("请选择字段");
    return;
  }

  proxy.$modal.loading();
  let d = {
    startTime: state.curTimeRange2[0],
    endTime: state.curTimeRange2[1],
    corpIdList: state.curCorpIdList2,
    names: state.curOptList,
  };
  console.log("导出统计表格传参", d);
  smartFieldStatistics(d)
    .then((res) => {
      console.log(res);
      downloadBlob(res, "application/vnd.ms-excel", "字段类型统计表格");
    })
    .finally(() => proxy.$modal.closeLoading());
};

const handleCheckOpt = (item) => {
  const idx = state.curOptList.indexOf(item);
  idx == -1 ? state.curOptList.push(item) : state.curOptList.splice(idx, 1);
  console.log(state.curOptList);
};

const handleCheck = (id, type) => {
  const name = type ? "curCorpIdList2" : "curCorpIdList";
  const idx = state[name].indexOf(id);
  if (idx != -1) {
    if (id == 0) {
      state[name] = [];
    } else {
      state[name].splice(idx, 1);
      const index = state[name].indexOf(0);
      index != -1 ? state[name].splice(index, 1) : "";
    }
  } else {
    if (id == 0) {
      state[name] = [];
      state[name] = state.corpList.map((item) => item.corpId);
    } else {
      state[name].push(id);
      const index = state[name].indexOf(0);
      index == -1 && state[name].length == state.corpList.length - 1
        ? state[name].push(0)
        : "";
    }
  }
  if (!type) {
    initCompute();
  }
};

const initCompute = () => {
  // state.curOpt = "";
  state.operationList = [{ type: "var", label: "", value: "" }];
  state.resultNum = "";
  if (
    state.curCorpIdList.length > 0 &&
    !!state.curTimeRange &&
    state.curTimeRange.length > 0
  ) {
    let d = {
      corpIdList: state.curCorpIdList,
      startTime: state.curTimeRange[0],
      endTime: state.curTimeRange[1],
    };
    console.log("统计传参", d);
    smartOperationStatistics(d).then((res) => {
      console.log(res);
      if (res.data) {
        const {
          corpPeopleVO,
          deviceStatisticsVO,
          workOrderStatisticsVO,
          deviceSwitchTotal,
          deviceUsbInsertTotal,
          deviceUsbRemoveTotal,
        } = res.data;
        state.varObj = {
          ...corpPeopleVO,
          ...workOrderStatisticsVO,
          ...deviceStatisticsVO,
          deviceSwitchTotal,
          deviceUsbInsertTotal,
          deviceUsbRemoveTotal,
        };
      }
      state.showVar = true;
    });
  } else {
    state.showVar = false;
  }
};

const getCoprList = async (flag = false) => {
  try {
    if (!flag) {
      await getAdminCorp().then((res) => {
        console.log("机构列表", res);
        if (res.data) {
          state.corpList = [
            ...state.corpList,
            ...res.data.data.map((item) => ({
              corpName: item.corpName,
              corpId: item.corpId,
            })),
          ];
        }
      });
    }

    if (flag) {
      state.dialogForm.corpList = [
        ...corpList.value.filter(
          (item) =>
            curCorpIdList.value.indexOf(item.corpId) != -1 && item.corpId != 0
        ),
      ];
      console.log(
        "机构列表",
        state.corpList,
        curCorpIdList.value,
        state.dialogForm.corpList
      );
    }
  } catch (error) {
    console.log(error);
  }
};

onMounted(() => {
  getCoprList();
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
    
<style lang="scss" scoped>
::-webkit-scrollbar {
  width: 0px; /* 滚动条宽度 */
}
:deep(.custom-dialog) {
  .dialogMain {
    &-deptTitle {
      display: flex;
      justify-content: space-between;
    }
    &-deptChecked {
      display: flex;
      gap: 10px;
      flex-wrap: wrap;
      padding: 10px 0 20px;
      &_item {
        // border: 1px solid red;
        cursor: pointer;
        position: relative;
        padding: 5px 10px;
        border-radius: 2px;
        font-size: 12px;
        background-color: #f4f4f5;
        color: #909399;
        .deptClose {
          width: 12px;
          height: 12px;
          display: flex;
          font-size: 12px;
          line-height: 1;
          justify-content: center;
          position: absolute;
          right: -5px;
          top: -5px;
          cursor: pointer;
          border: 1px solid #909399;
          border-radius: 50%;
          background-color: #fff;
          transition: all 0.3s;
          &:hover {
            background-color: #909399;
            color: #fff;
          }
        }
      }
    }
    &-corpSel {
      display: flex;
      align-items: center;
      gap: 0 10px;
    }
  }
  .el-radio {
    margin-bottom: 10px;
    height: auto;
    line-height: 1;
  }
  .el-radio__label {
    display: flex;
    align-items: center;
    gap: 0 10px;
  }
}
.unify-main_charts {
  // border: 1px solid yellow;
  width: 100%;
  height: 52vw;
  display: flex;
  align-items: center;
  justify-content: center;
  .computer {
    // border: 1px solid red;
    position: relative;
    width: 85vw;
    height: 50vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/bg.png");
    background-size: 100% 100%;
    .tab {
      margin: 1.5vw auto;
      width: 30vw;
      height: 3vw;
      // border: 1px solid red;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/tab-bg1.png");
      background-size: 100% 100%;
      display: flex;
      cursor: pointer;
      &.right {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/tab-bg2.png");
        background-size: 100% 100%;
      }
      &-item {
        // border: 1px solid red;
        width: 15vw;
        height: 3vw;
      }
    }
    .main {
      // border: 1px solid red;
      width: 75vw;
      height: 37.5vw;
      margin: 0 auto;
      .block {
        &-title {
          // border: 1px solid red;
          width: 11vw;
          height: 3.5vw;
          line-height: 3.5vw;
          padding-left: 2vw;
          font-size: 1vw;
          margin: 0.5vw -0.5vw;
          color: #fff;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/title-bg.png");
          background-size: 100% 100%;
          &.long {
            width: 17vw;
          }
          &.title2 {
            margin-left: 0.5vw;
          }
        }
      }

      .block1 {
        .radioBox {
          // border: 1px solid red;
          max-height: 3.2vw;
          overflow-y: scroll;
        }
        .radio {
          display: flex;
          flex-wrap: wrap;
          gap: 0.5vw 1.5vw;
          color: #fff;
          padding-left: 1vw;
          font-size: 0.95vw;
          &-item {
            display: flex;
            align-items: center;
            gap: 0 0.5vw;
            cursor: pointer;
            .check {
              width: 1.1vw;
              height: 1.1vw;
              // border: 1px solid red;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/check.png");
              background-size: 100% 100%;
              &.active {
                background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/checked.png");
                background-size: 100% 100%;
              }
            }
          }
        }
        .timeRange {
          padding: 1vw 1vw;

          :deep(.el-range-editor) {
            &.el-input__wrapper {
              // padding: 0 !important;
              cursor: pointer;
              height: 2.4vw;
              width: 27vw;
              border-radius: 0;
              background-color: #072151;
              border: 0.01vw solid #134fe3;
              box-shadow: none;
            }
            .el-range__close-icon {
              &:hover {
                color: #fff;
              }
            }
            .el-range-input {
              font-size: 0.9vw;
              color: #fff;
              cursor: pointer;
            }
            .el-range-separator {
              color: #134fe3;
            }
            .el-range__icon {
              color: #072151;
            }
          }
        }
        .operation {
          padding: 0 1vw 1vw;
          display: flex;
          align-items: center;
          gap: 0 1vw;
          &-list {
            position: relative;
            display: flex;
            align-items: center;
            padding: 0.5vw;
            gap: 0 1vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/var-list-bg.png");
            background-size: 100% 100%;
            .limit {
              position: absolute;
              font-size: 0.6vw;
              right: 0;
              color: #00f0ff;
              bottom: -1.2vw;
            }
          }
          &-itemBg {
            width: 3.8vw;
            height: 3.8vw;
            border-radius: 0.1vw;
            background-color: #10163e;
          }
          &-item {
            position: relative;
            width: 3.8vw;
            height: 3.8vw;
            // padding: 0.5vw;
            color: #00f0ff;
            font-size: 0.7vw;
            word-break: break-all;
            text-align: center;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/var-bg.png");
            background-size: 100% 100%;
            cursor: pointer;
            &.symbol {
              background: none;
              border: 0.01vw solid #4aa5ff;
              border-radius: 0.1vw;
            }
            &_label {
              // border: 1px solid red;
              padding: 0.1vw 0.5vw;
              font-size: 0.5vw;
              line-height: 0.7vw;
              height: 1.8vw;
              display: flex;
              align-items: center;
              justify-content: center;
              &.symbol {
                font-size: 2vw;
                // border: 1px solid red;
                height: 3.8vw;
                line-height: 3.8vw;
                padding: 0;
              }
            }
            &_value {
              padding: 0.2vw 0.5vw;
              // border: 1px solid red;
              height: 2vw;
              display: flex;
              align-items: center;
              justify-content: center;
            }
            .close {
              position: absolute;
              cursor: pointer;
              right: -0.4vw;
              top: -0.6vw;
              width: 1vw;
              height: 1.25vw;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/var-close.png");
              background-size: 100% 100%;
            }
          }
          &-btns {
            display: flex;
            align-items: center;
            gap: 0 1vw;
            color: #00f0ff;
            .btn {
              cursor: pointer;
              // border: 1px solid red;
              width: 4.8vw;
              height: 4.8vw;
              line-height: 4.8vw;
              text-align: center;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/btn-bg2.png");
              background-size: 100% 100%;
              font-size: 0.7vw;
              &.result {
                background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/result-bg.png");
                background-size: 100% 100%;
                line-height: 0.75vw;
                font-size: 0.45vw;
                .result-tip {
                  padding: 0.15vw;
                  height: 1.8vw;
                  // border: 1px solid red;
                }
                .result-cont {
                  padding: 0.5vw;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  font-size: 0.85vw;
                  line-height: 0.85vw;
                  height: 3vw;
                  word-break: break-all;
                  // border: 1px solid red;
                }
              }
            }
          }
        }
      }

      .block2 {
        .symbol {
          padding-left: 2.5vw;
          display: flex;
          align-items: center;
          gap: 0 2vw;
          &-item {
            // border: 1px solid red;
            width: 3.8vw;
            height: 3.8vw;
            cursor: pointer;
            &.symbol1 {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/add.png");
              background-size: 100% 100%;
            }
            &.symbol2 {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/del.png");
              background-size: 100% 100%;
            }
            &.symbol3 {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/multiple.png");
              background-size: 100% 100%;
            }
            &.symbol4 {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/division.png");
              background-size: 100% 100%;
            }
          }
        }
      }

      .block3 {
        .var {
          display: flex;
          flex-wrap: wrap;
          gap: 0.8vw 1.5vw;
          padding-left: 2.5vw;
          &-item {
            // border: 1px solid red;
            cursor: pointer;
            width: 7.5vw;
            font-size: 0.8vw;
            text-align: center;
            height: 3vw;
            line-height: 3.5vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/opt-bg.png");
            background-size: 100% 100%;
            color: #fff;
            transition: all 0.3s;
            &:hover,
            &.active {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/opt-bgSel.png");
              background-size: 100% 100%;
              color: #00f0ff;
            }
          }
        }
      }

      .block4 {
        text-align: center;
        color: #00f0ff;
        margin-top: 5vw;
        .exportBtn {
          // border: 1px solid red;
          cursor: pointer;
          font-size: 1.2vw;
          display: inline-block;
          width: 12vw;
          height: 3vw;
          line-height: 3vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/smart/btn-bg.png");
          background-size: 100% 100%;
        }
      }
    }
  }
}
</style>