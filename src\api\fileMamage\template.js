import request from "@/utils/request";

// 查询模板列表 /system/schoolTemplate
export function getSchoolTemplateList(params) {
  return request({
    url: "/system/schoolTemplate",
    method: "get",
    params,
  });
}

// 查询模板详情 /system/schoolTemplate/1877645532043964417
export function getSchoolTemplateDetail(id) {
  return request({
    url: "/system/schoolTemplate/" + id,
    method: "get",
  });
}

// 新增模板 /system/schoolTemplate
export function addSchoolTemplate(data) {
  return request({
    url: "/system/schoolTemplate",
    method: "post",
    data,
  });
}

// 删除模板 /system/schoolTemplate
export function deleteSchoolTemplate(params) {
  return request({
    url: "/system/schoolTemplate",
    method: "delete",
    params,
  });
}