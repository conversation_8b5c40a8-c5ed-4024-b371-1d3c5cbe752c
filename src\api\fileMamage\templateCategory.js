import request from "@/utils/request"; 

// 查询模板列表 /system/schoolTemplateType
export function getSchoolTemplateTypeList(params) {
  return request({
    url: "/system/schoolTemplateType",
    method: "get",
    params,
  });
}

// 查询模板类别详情 /system/schoolTemplateType/1877601217636835329
export function getSchoolTemplateTypeDetail(id) {
  return request({
    url: "/system/schoolTemplateType/" + id,
    method: "get",
  });
}

// 新增模板类别 /system/schoolTemplateType
export function addSchoolTemplateType(data) {
  return request({
    url: "/system/schoolTemplateType",
    method: "post",
    data,
  });
}

// 修改模板类别 /system/schoolTemplateType
export function editSchoolTemplateType(data) {
  return request({
    url: "/system/schoolTemplateType",
    method: "put",
    data,
  });
}

// 删除模板类别 /system/schoolTemplateType
export function deleteSchoolTemplateType(params) {
  return request({
    url: "/system/schoolTemplateType",
    method: "delete",
    params,
  });
}

// 批量删除模板类别 /system/schoolTemplateType/deleteBatch
export function deleteBatchSchoolTemplateType(data) {
  return request({
    url: "/system/schoolTemplateType/deleteBatch",
    method: "delete",
    data,
  });
}