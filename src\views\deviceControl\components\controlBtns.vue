<template>
  <div class="controlBtns" :class="{ isNew: isNew }" v-if="isLoad">
    <div class="monitor2-bottom" v-if="!isNew">
      <el-tooltip
        class="box-item"
        effect="light"
        content="切回新版"
        placement="top"
      >
        <div class="monitor2-change" @click="handleTransform(1)"></div>
      </el-tooltip>
      <div class="monitor2-btns">
        <div
          v-for="(item, index) in remoteList"
          :key="item.value"
          class="monitor2-btn"
          @click="handleOpeartionRemote(item.value)"
          :style="{
            borderColor: item.color,
            '--bg-color': item.color,
            '--hover-bg-color': item.hoverColor,
            '--icon': item.icon,
            '--hover-icon': item.hoverIcon,
          }"
        >
          {{ item.label }}
        </div>
      </div>
    </div>
    <div class="newBtns" v-if="!!isNew">
      <div
        class="newBtns-btns"
        v-if="showBtns"
        @mouseleave="showBtns = false"
        @mouseover="showBtns = true"
        @touchstart="showBtns = true"
        @touchend="showBtns = false"
      >
        <el-tooltip
          v-for="(item, index) in remoteList"
          :key="index"
          class="box-item"
          effect="light"
          :content="item.label"
          placement="top"
        >
          <div
            class="newBtns-btn"
            :class="`btn${index + 1}`"
            @click="handleOpeartionRemote(item.value)"
            :style="{
              borderColor: item.color,
              '--bg-color': item.color,
              '--hover-bg-color': item.hoverColor,
              '--new-icon': item.newIcon,
              '--new-hover-icon': item.newHoverIcon,
            }"
          ></div>
        </el-tooltip>
      </div>
      <div
        class="newBtns-block"
        @mouseenter="showBtns = true"
        @mouseover="showBtns = true"
        @touchstart="showBtns = true"
      >
        <el-tooltip
          class="box-item"
          effect="light"
          content="切回旧版"
          placement="top"
        >
          <div class="newBtns-change" @click="handleTransform(0)"></div>
        </el-tooltip>
      </div>
    </div>
    <fix-time-off
      :tableAllSelectedId="tableAllSelectedId"
      :tableAllSelectedRow="tableAllSelectedRow"
      :form="form"
      :isBatch="true"
      title="批量定时关机"
      :open="open"
      @cancel="open = false"
    />
  </div>
</template>

<script setup>
import fixTimeOff from "@/views/deviceControl/components/fixTimeOff.vue";
import {
  checkBatch,
  handleBatchStart,
  handleBatchLock,
  handleBatchClear,
  handleBatchRemote,
  handleBatchWifi,
} from "@/utils/control";
import { setControlBtn, getControlBtn } from "@/api/deviceControl";

const { proxy } = getCurrentInstance();

const props = defineProps({
  tableAllSelectedId: {
    type: Array,
    default: [],
  },
  tableAllSelectedRow: {
    type: Array,
    default: [],
  },
});

const state = reactive({
  isLoad: false,
  showBtns: false,
  form: {},
  isNew: false,
  open: false,
  remoteList: [
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/off.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/off_hover.png)",
      color: "rgba(255, 59, 48, 1)",
      hoverColor: "rgba(255, 59, 48, 0.1)",
      label: "远程关机",
      value: 0,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/off.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/off_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/on.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/on_hover.png)",
      color: "rgba(0, 122, 255, 1)",
      hoverColor: "rgba(0, 122, 255, 0.1)",
      label: "远程开机",
      value: 1,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/on.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/on_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/restart.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/restart_hover.png)",
      color: "rgba(52, 199, 89, 1)",
      hoverColor: "rgba(52, 199, 89, 0.1)",
      label: "远程重启",
      value: 2,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/restart.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/restart_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/clock.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/clock_hover.png)",
      color: "rgba(255, 45, 85, 1)",
      hoverColor: "rgba(255, 45, 85, 0.1)",
      label: "远程定时关机",
      value: 3,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/clock.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/clock_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/wifi.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/wifi_hover.png)",
      color: "rgba(50, 173, 230, 1)",
      hoverColor: "rgba(50, 173, 230, 0.1)",
      label: "远程开启wifi",
      value: 4,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/wifi.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/wifi_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/nowifi.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/nowifi_hover.png)",
      color: "rgba(175, 82, 222, 1)",
      hoverColor: "rgba(175, 82, 222, 0.1)",
      label: "远程关闭wifi",
      value: 5,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/nowifi.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/nowifi_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/clean.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/clean_hover.png)",
      color: "rgba(255, 149, 0, 1)",
      hoverColor: "rgba(255, 149, 0, 0.1)",
      label: "远程清除缓存",
      value: 6,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/clean.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/clean_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/lock.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/lock_hover.png)",
      color: "rgba(255, 204, 0, 1)",
      hoverColor: "rgba(255, 204, 0, 0.1)",
      label: "锁屏",
      value: 7,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/lock.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/lock_hover.png)",
    },
    {
      icon: "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/unlock.png)",
      hoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/unlock_hover.png)",
      color: "rgba(48, 176, 199, 1)",
      hoverColor: "rgba(48, 176, 199, 0.1)",
      label: "解锁",
      value: 8,
      newIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/unlock.png)",
      newHoverIcon:
        "url(https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/unlock_hover.png)",
    },
  ],
});

const { isLoad, isNew, form, remoteList, open, showBtns } = toRefs(state);

onMounted(() => {
  getControl();
});

const getControl = () => {
  showBtns.value = false;
  isLoad.value = false;
  getControlBtn()
    .then((res) => {
      console.log(res, "集控按钮版本");
      isNew.value = res.data === 1;
    })
    .finally(() => (isLoad.value = true));
};

const handleTransform = (val) => {
  if (!!val) showBtns.value = false;
  isNew.value = !!val;
  setControlBtn({
    configKey: "button_control",
    configValue: val,
  });
};

const handleOpeartionRemote = (val) => {
  console.log("远程操作", val);
  switch (val) {
    // 批量远程关机
    case 0:
      handleBatchRemote(1, 0, {}, props);
      break;
    //批量远程开机
    case 1:
      handleBatchStart(props);
      break;
    // 批量远程重启
    case 2:
      handleBatchRemote(1, 1, {}, props);
      break;
    // 批量远程定时关机
    case 3:
      handleBatchFixed();
      break;
    // 批量远程开启WIFI
    case 4:
      handleBatchWifi(1, 1, {}, props);
      break;
    // 批量远程关闭WIFI
    case 5:
      handleBatchWifi(1, 0, {}, props);
      break;
    // 批量远程清除缓存
    case 6:
      handleBatchClear(1, {}, props);
      break;
    // 批量远程锁屏
    case 7:
      handleBatchLock(1, 0, {}, props);
      break;
    // 批量远程解锁
    case 8:
      handleBatchLock(1, 1, {}, props);
      break;
    default:
      break;
  }
};

/** 批量定时关机按钮操作 */
const handleBatchFixed = () => {
  if (props.tableAllSelectedId?.length < 1) {
    proxy.$modal.msgWarning("请选择设备");
    return;
  }

  const { flag, recoverArr } = checkBatch(props);

  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning("设备存在关机状态，无法进行此操作");
    return;
  }
  open.value = true;
};

defineExpose({
  getControl,
});
</script>

<style lang="scss" scoped>
.controlBtns {
  &.isNew {
    position: absolute;
    bottom: 0;
    left: 60%;
  }
}
.monitor2 {
  &-change {
    width: 2.2vw;
    height: 2.1vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/oldVersion/switch.png");
    background-size: 100% 100%;
    cursor: pointer;
    pointer-events: auto;
  }
  &-btn {
    flex: 1;
    height: 2.5vw;
    border: 0.1vw solid;
    font-size: 0.85vw;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 0.2vw;
    gap: 0 5px;
    background-color: var(--bg-color);
    color: #fff;
    transition: all 0.1s;
    &::before {
      content: "";
      width: 1.1vw;
      height: 1.1vw;
      background-image: var(--icon);
      background-size: 100% 100%;
    }
    &:hover {
      background-color: var(--hover-bg-color);
      color: var(--bg-color);
      &::before {
        background-image: var(--hover-icon);
        background-size: 100% 100%;
      }
    }
  }
  &-bottom {
    width: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    // z-index: 2001;
    pointer-events: none;
    background-color: #fff;
    border-radius: 5px;
    gap: 0 10px;
    padding-right: 10px;
  }
  &-btns {
    // border: 1px solid red;
    border-left: 1px solid #888888;
    pointer-events: auto;
    display: flex;
    // max-width: 90%;
    width: 100%;
    // flex-wrap: wrap;
    justify-content: space-between;
    padding-left: 1vw;
    gap: 0 1vw;
  }
}
.newBtns {
  position: relative;
  width: 100%;
  height: 8vw;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 2001;
  pointer-events: none;
  // border: 1px solid red;

  &-change {
    width: 1.6vw;
    height: 1.6vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/switch_new.png");
    background-size: 100% 100%;
    position: absolute;
    right: 3vw;
    bottom: 0.4vw;
  }

  &-block {
    position: absolute;
    bottom: 0;
    width: 25vw;
    height: 5vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/slideBlock.png");
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    gap: 0.4vw 0;
    pointer-events: auto;
    cursor: pointer;
    &::before {
      content: "";
      width: 2.5vw;
      height: 2.5vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/slide.png");
      background-size: 100% 100%;
    }
    &::after {
      content: "远程操作";
      font-weight: bold;
      color: #fff;
      font-size: 0.9vw;
    }
  }

  &-btns {
    width: 30vw;
    height: 8vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/controlBtns/newVersion/slideBtnsBlock.png");
    background-size: 100% 100%;
    // z-index: 2001;
    pointer-events: auto;
    // border: 1px solid red;
    position: absolute;
    bottom: 0;
    transition: all 0.3s;
  }
  &-btn {
    pointer-events: auto;
    cursor: pointer;
    position: absolute;
    width: 2vw;
    height: 2vw;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.1s;
    background-image: var(--new-icon);
    background-size: 100% 100%;
    z-index: 2001;

    &:hover {
      background-image: var(--new-hover-icon);
      background-size: 100% 100%;
    }
    &.btn1 {
      bottom: 0.2vw;
      left: 1.3vw;
    }
    &.btn2 {
      bottom: 2.6vw;
      left: 3.7vw;
    }
    &.btn3 {
      bottom: 4.2vw;
      left: 6.8vw;
    }
    &.btn4 {
      bottom: 5.1vw;
      left: 10.2vw;
    }
    &.btn5 {
      bottom: 5.5vw;
      left: 13.8vw;
    }
    &.btn6 {
      bottom: 5.1vw;
      right: 10.5vw;
    }
    &.btn7 {
      bottom: 4.2vw;
      right: 7vw;
    }
    &.btn8 {
      bottom: 2.6vw;
      right: 3.7vw;
    }
    &.btn9 {
      bottom: 0.2vw;
      right: 1.3vw;
    }
  }
}
</style>