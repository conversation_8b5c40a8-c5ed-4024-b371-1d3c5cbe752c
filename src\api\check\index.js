import request from "@/utils/request";

// 添加巡检人员
export function addInspection(data) {
  return request({
    url: "/system/user/addInspection",
    method: "post",
    data,
  });
}

// 巡检人员列表
export function inspectionPage(data) {
  return request({
    url: "/system/user/role/list",
    method: "post",
    data,
  });
}

// 删除巡检人员
export function delInspection(data) {
  return request({
    url: "/system/user/deleteInspection",
    method: "post",
    data,
  });
}

// 一键通知
export function inspectionNotice(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/notice`, // 实际导出接口地址
    method: "post",
    data
  });
}

// 导出巡检报告
export function exportInspection(data) {
  return request({
    url: "/system/schoolInspectionPersonnel/export",
    method: "post",
    data,
  });
}

// 添加指标
export function addSchoolIndicators(data) {
  return request({
    url: "/system/schoolIndicators/add",
    method: "post",
    data,
  });
}

// 指标详情
export function schoolIndicatorsInfo(id) {
  return request({
    url: `/system/schoolIndicators/${id}`,
    method: "get",
  });
}

// 指标列表
export function schoolIndicatorsList(params) {
  return request({
    url: "/system/schoolIndicators/list",
    method: "get",
    params,
  });
}

// 修改指标
export function updateSchoolIndicators(data) {
  return request({
    url: "/system/schoolIndicators/update",
    method: "put",
    data,
  });
}

// 批量删除指标
export function delSchoolIndicators(params) {
  return request({
    url: "/system/schoolIndicators/delete",
    method: "delete",
    params,
  });
}

// 点位详情
export function schoolPointInfo(id) {
  return request({
    url: `/system/schoolPoint/${id}`,
    method: "get",
  });
}

// 点位添加
export function addSchoolPoint(data) {
  return request({
    url: `/system/schoolPoint/add`,
    method: "post",
    data,
  });
}

// 点位修改
export function updateSchoolPoint(data) {
  return request({
    url: "/system/schoolPoint/update",
    method: "put",
    data,
  });
}

// 删除点位
export function delSchoolPoint(params) {
  return request({
    url: "/system/schoolPoint/delete",
    method: "delete",
    params,
  });
}

// 点位列表
export function schoolPointList(params) {
  return request({
    url: "/system/schoolPoint/list",
    method: "get",
    params,
  });
}

// 巡检频率列表
export function schoolInspectionRulesList() {
  return request({
    url: `/system/schoolInspectionRules/list`,
    method: "get",
  });
}

// 巡检频率规则详情
export function schoolInspectionRulesInfo() {
  return request({
    url: `/system/schoolInspectionRules/info`,
    method: "get",
  });
}

// 添加和修改频率规则
export function addAndUpdate(data) {
  return request({
    url: `/system/schoolInspectionRules/addAndUpdate`,
    method: "post",
    data,
  });
}

// 巡检记录列表
export function schoolInspectionRecordList(params) {
  return request({
    url: "/system/schoolInspectionPersonnel/record/list",
    method: "get",
    params,
  });
}

// 巡检记录详情
export function schoolInspectionRecordInfo(id) {
  return request({
    url: `/system/schoolInspectionPersonnel/${id}`,
    method: "get",
  });
}

// 巡检结果详情
export function schoolInspectionResultList(params) {
  return request({
    url: `/system/schoolInspectionPersonnel/results`,
    method: "get",
    params
  });
}

// 巡检图片列表
export function schoolInspectionImageList(params) {
  return request({
    url: `/system/schoolInspectionPersonnel/image/page`,
    method: "get",
    params
  });
}

// 巡检计划列表
export function schoolInspectionList(params) {
  return request({
    url: "/system/schoolInspectionPersonnel/list",
    method: "get",
    params,
  });
}

// 巡检人员列表
export function userInspectionList(params) {
  return request({
    url: "/system/user/inspection/list",
    method: "get",
    params,
  });
}

// 添加巡检计划人员
export function addSchoolInspection(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/add`,
    method: "post",
    data,
  });
}

// 为巡检计划添加点位
export function addSchoolInspectionPoint(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/point/add`,
    method: "post",
    data,
  });
}

// 为巡检计划添加指标
export function addSchoolInspectionIndicators(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/indicators/add`,
    method: "post",
    data,
  });
}

// 为巡检计划添加频率
export function addSchoolInspectionRules(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/rules/add`,
    method: "post",
    data,
  });
}

// 巡检计划统计
export function schoolInspectionStatistics() {
  return request({
    url: `/system/schoolInspectionPersonnel/statistics`,
    method: "get",
  });
}

// 定时刷新巡检任务计划单
export function patrolPlan(data) {
  return request({
    url: `/system/schoolInspectionPersonnel/refresh/patrolPlan`,
    method: "post",
    data,
  });
}
