<template>
  <div class="schedule app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          工作排班
          <div>
            <el-button type="success" icon="FullScreen" @click="handleCode"
              >生成打卡二维码</el-button
            >
            <el-button type="warning" icon="Setting" @click="handleSet"
              >设置打卡规则</el-button
            >
            <el-button type="primary" icon="Edit" @click="handleEdit"
              >编辑工作内容</el-button
            >
          </div>
        </div>
      </template>
      <div class="schedule-main">
        <el-form :model="queryParams" class="search-list" :inline="true">
          <el-form-item label="" prop="month">
            <div class="custom-date-picker">
              <el-button
                icon="ArrowLeft"
                text
                @click="handlePrevMonth"
              ></el-button>
              <el-popover
                v-model:visible="monthPickerVisible"
                trigger="manual"
                :width="300"
                placement="bottom-start"
                ><!--  -->
                <template #reference>
                  <div class="month-selector" @click="showMonthPicker">
                    <span v-if="queryParams.month">{{
                      formatMonth(queryParams.month)
                    }}</span>
                    <span v-else class="placeholder">请选择月份</span>
                  </div>
                </template>
                <div class="month-picker-popover">
                  <div class="month-picker-header">
                    <el-button
                      text
                      :icon="ArrowLeft"
                      @click="handlePrevYear"
                    ></el-button>
                    <span>{{ currentYear }}年</span>
                    <el-button
                      text
                      :icon="ArrowRight"
                      @click="handleNextYear"
                    ></el-button>
                  </div>
                  <div class="month-picker-content">
                    <div
                      v-for="month in 12"
                      :key="month"
                      class="month-item"
                      :class="{
                        active: isCurrentMonth(month),
                        disabled: isMonthDisabled(month),
                      }"
                      @click="selectMonth(month)"
                    >
                      {{ month }}月
                    </div>
                  </div>
                </div>
              </el-popover>
              <el-button
                icon="ArrowRight"
                text
                @click="handleNextMonth"
              ></el-button>
            </div>
          </el-form-item>
          <el-form-item label="" prop="week">
            <div class="custom-date-picker">
              <el-button
                icon="ArrowLeft"
                text
                @click="handlePrevWeek"
              ></el-button>
              <el-popover
                v-model:visible="weekPickerVisible"
                trigger="manual"
                :width="240"
                placement="bottom-start"
              >
                <template #reference>
                  <div class="week-selector" @click="showWeekPicker">
                    <span v-if="currentWeek">第{{ currentWeek }}周</span>
                    <span v-else class="placeholder">请选择周数</span>
                    <!-- <el-icon class="el-icon--right"><arrow-down /></el-icon> -->
                  </div>
                </template>
                <div class="week-picker-popover">
                  <div class="week-picker-content">
                    <div
                      v-for="week in totalWeeks"
                      :key="week"
                      class="week-item"
                      :class="{ active: week === currentWeek }"
                      @click="selectWeek(week)"
                    >
                      第{{ week }}周
                    </div>
                  </div>
                </div>
              </el-popover>
              <el-button
                icon="ArrowRight"
                text
                @click="handleNextWeek"
              ></el-button>
            </div>
          </el-form-item>
          <el-form-item label="" prop="name">
            <el-input
              v-model.trim="queryParams.name"
              placeholder="请输入姓名"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <table
          style="width: 100%"
          border="1"
          cellspacing="0"
          v-loading="loading"
        >
          <tr>
            <th class="table-th cell">姓名</th>
            <th
              class="table-th cell"
              v-for="(item, index) in week"
              :key="index"
            >
              {{ item }}{{ getDateString(index) }}
            </th>
          </tr>

          <tbody v-for="(item, index) in tableList" :key="index">
            <tr>
              <td class="table-td2" :colspan="8">
                <span>本周工作时长：{{ item.workTime }}h</span>
                <!-- <span>本周请假时长：{{ item.absentTime }}h</span>
                <span>本周加班时长：{{ item.otTime }}h</span>
                <span>本周调休时长：{{ item.restTime }}h</span>
                <span>本周剩余调休时长：{{ item.restTime_remain }}h</span>
                <span>总计调休时长：{{ item.restTime_total }}h</span> -->
              </td>
            </tr>
            <tr>
              <td class="table-td cell">{{ tableList[index].name }}</td>
              <td class="table-td cell" v-for="(opt, idx) in 7" :key="idx">
                <template v-if="isDateInCurrentMonth(idx)">
                  <template v-if="tableList[index][`field${idx + 1}`]">
                    {{ tableList[index][`field${idx + 1}`] }}
                    <div
                      v-if="!isPastDate(idx)"
                      @click="handleEditField(item, index, idx)"
                    >
                      点击更改
                    </div>
                  </template>
                  <template v-else>
                    <div
                      v-if="!isPastDate(idx)"
                      class="empty-schedule"
                      @click="handleEditField(item, index, idx)"
                    >
                      暂无安排
                    </div>
                  </template>
                </template>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- 工作内容 -->
      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        :title="title"
        width="600"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        @close="handleCancel"
      >
        <el-table
          ref="optRef"
          :data="optList"
          style="width: 100%"
          border
          @current-change="handleCurrentChange"
          @row-click="handleRowClick"
        >
          <el-table-column
            v-if="title !== '编辑工作内容'"
            label=""
            align="center"
            min-width="80"
          >
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.checked"
                @change="(val) => handleChangeCheck(val, scope.$index)"
                @click.native.stop
              />
            </template>
          </el-table-column>
          <el-table-column
            type="index"
            label="选项编号"
            align="center"
            min-width="100"
          />
          <el-table-column label="选项名称" align="center" min-width="200">
            <template #default="scope">
              <el-input
                v-if="scope.row.isEdit"
                v-model="scope.row.name"
                maxlength="20"
              ></el-input>
              <div v-else>{{ scope.row.name }}</div>
            </template>
          </el-table-column>
          <el-table-column label="选项类别" align="center" min-width="150">
            <template #default="scope">
              <el-select v-if="scope.row.isEdit" v-model="scope.row.type">
                <el-option label="需打卡" :value="1" />
                <el-option label="不需打卡" :value="0" />
              </el-select>
              <div v-else>
                {{ scope.row.type === 1 ? "需打卡" : "不需打卡" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            v-if="title === '编辑工作内容'"
            label="操作"
            min-width="100"
            align="center"
          >
            <template #default="scope">
              <el-button
                v-if="!scope.row.isDefault"
                link
                type="primary"
                :icon="scope.row.isEdit ? 'Check' : 'Edit'"
                @click.stop="handleEditOpt(scope)"
              ></el-button>
              <el-button
                v-if="!scope.row.isDefault"
                link
                type="danger"
                icon="Delete"
                @click.stop="handleDelOpt(scope)"
              ></el-button>
            </template>
          </el-table-column>
        </el-table>
        <div
          v-if="title == '编辑工作内容'"
          class="optAdd"
          @click="handleAddOpt"
        >
          + 新增选项
        </div>
        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button type="primary" @click.stop="handleSubmit">{{
            title == "编辑工作内容" ? "保存修改" : "确定"
          }}</el-button>
        </template>
      </el-dialog>

      <!-- 打卡规则 -->
      <el-dialog
        class="custom-dialog"
        title="设置打卡规则"
        v-model="dialogVisible2"
        width="600"
      >
        <el-form :model="clockForm">
          <el-form-item label="上班打卡时间设置" prop="beginTime">
            <el-time-picker
              v-model="clockForm.beginTime"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
            />
          </el-form-item>
          <el-form-item label="下班打卡时间设置" prop="overTime">
            <el-time-picker
              v-model="clockForm.overTime"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
            />
          </el-form-item>
          <el-form-item
            :label="index == 0 ? `WIFI打卡名称设置` : ''"
            v-for="(item, index) in clockForm.wifiList"
            :prop="`wifiList.[${index}]`"
            label-width="126"
          >
            <el-row style="width: 100%">
              <el-col :span="15">
                <el-input
                  v-model="clockForm.wifiList[index].name"
                  placeholder="请输入"
                  style="width: 250px"
                />
              </el-col>
              <el-col :span="9" style="display: flex">
                <el-button
                  type="primary"
                  circle
                  icon="Plus"
                  v-if="
                    clockForm.wifiList.length < 5 &&
                    index == clockForm.wifiList.length - 1
                  "
                  @click.stop="handleAddWifi"
                ></el-button>
                <el-button
                  type="danger"
                  circle
                  icon="Delete"
                  v-if="clockForm.wifiList.length > 1"
                  @click.stop="handleDelWifi(index)"
                ></el-button>
              </el-col>
            </el-row>
          </el-form-item>
          <div style="display: flex; gap: 0 30px">
            <el-form-item label="打卡范围设置"> </el-form-item>
            <div>
              <el-form-item label="中心点" prop="center">
                <el-input
                  placeholder="请选择地点"
                  v-model="clockForm.centerAddress"
                  style="width: 250px"
                  readonly
                  @click="handleShowMap"
                  clearable
                />
              </el-form-item>
              <el-form-item label="范围半径" prop="range">
                <el-select v-model="clockForm.range" style="width: 250px">
                  <el-option
                    v-for="(item, index) in rangeList"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </div>
        </el-form>

        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button type="primary" v-throttle @click.stop="handleSave"
            >保存</el-button
          >
        </template>
      </el-dialog>

      <!-- 生成二维码 -->
      <el-dialog
        class="custom-dialog"
        title="生成打卡二维码"
        v-model="dialogVisible3"
        width="400"
      >
        <el-form :model="codeForm">
          <el-form-item label="二维码名称" prop="name">
            <el-input
              v-model="codeForm.name"
              placeholder="请输入二维码名称"
              style="width: 250px"
              maxlength="20"
              show-word-limit
              clearable
            />
          </el-form-item>
          <el-form-item label="二维码时效" prop="time">
            <el-select v-model="codeForm.time" style="width: 250px">
              <el-option
                v-for="(item, index) in codeList"
                :key="index"
                :label="item"
                :value="item"
              />
            </el-select>
          </el-form-item>
        </el-form>
        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button type="primary" @click.stop="handleSave(1)"
            >点击下载</el-button
          >
        </template>
      </el-dialog>

      <!-- 添加地图选择对话框 -->
      <el-dialog
        class="custom-dialog"
        title="选择地点"
        v-model="mapVisible"
        width="1000px"
      >
        <BaiduMap :isNum="isNum" ref="mapRef" />
        <template #footer>
          <el-button @click="mapVisible = false">取消</el-button>
          <el-button type="primary" @click="handleConfirmLocation"
            >确定</el-button
          >
        </template>
      </el-dialog>
    </el-card>
  </div>

  <div id="qrcode"></div>
</template>

<script setup name="schedule">
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  watch,
  computed,
  onMounted,
  nextTick,
} from "vue";
import BaiduMap from "./components/map.vue";
import { generateQRCode } from "@/utils/uploadCode";
import { timeFormat } from "@/utils";
import {
  addWorkContent,
  listWorkContent,
  updateWorkContent,
  delWorkContent,
  listWork,
  addWork,
  updateWork,
  getClockRule,
  setClockRule,
} from "@/api/distribution/schedule";
import { ArrowLeft, ArrowRight } from "@element-plus/icons-vue";

const route = useRoute();
const { proxy } = getCurrentInstance();
const optRef = ref(null);
const state = reactive({
  isNum: 0,
  curIdx: 0,
  curField: "",
  title: "编辑工作内容",
  week: ["周一", "周二", "周三", "周四", "周五", "周六", "周日"],
  select: "",
  dialogVisible: false,
  dialogVisible2: false,
  dialogVisible3: false,
  total: 1,
  loading: false,
  rangeList: [
    { label: "200m", value: 0 },
    { label: "500m", value: 1 },
    { label: "800m", value: 2 },
    { label: "1km", value: 3 },
  ],
  codeList: ["一天", "一周", "一个月", "无限期"],
  clockForm: {
    beginTime: [],
    overTime: [],
    wifiList: [{ name: "" }],
    center: "",
    range: "",
  },
  codeForm: {
    name: "",
    time: "",
  },
  tableList_all: [],
  tableList: [],
  optList: [],
  optIdMap: new Map(),
  queryParams: {
    current: 1,
    size: 10,
    month: "",
    week: "",
    name: "",
  },
  mapVisible: false,
  currentWeek: 1,
  weekPickerVisible: false,
  selectedWeek: null,
  totalWeeks: computed(() => {
    if (!state.queryParams.month) return [];
    const date = new Date(state.queryParams.month + "-01");
    return Array.from({ length: getWeeksInMonth(date) }, (_, i) => i + 1);
  }),
  monthPickerVisible: false,
  currentYear: new Date().getFullYear(),
  currentWorkCalendarId: null,
  currentWorkDate: null,
});

const {
  week,
  title,
  optList,
  tableList,
  clockForm,
  queryParams,
  dialogVisible,
  dialogVisible2,
  dialogVisible3,
  rangeList,
  codeList,
  loading,
  codeForm,
  total,
  mapVisible,
  currentWeek,
  weekPickerVisible,
  totalWeeks,
  monthPickerVisible,
  currentYear,
  isNum,
} = toRefs(state);

const mapRef = ref(null);

function handleCode() {
  state.dialogVisible3 = true;
}

function handleDelWifi(idx) {
  state.clockForm.wifiList.splice(idx, 1);
}

function handleAddWifi(val) {
  state.clockForm.wifiList.push({ name: "" });
}

function handleCurrentChange(val) {
  if (!val) return;
  state.select = val.name;
}

function handleMonth(val) {
  if (val) {
    // 重置为第一周
    state.currentWeek = 1;
    state.queryParams.week = "";
    handleQuery();
  }
}

// 修改 getWeekStartDate 函数
function getWeekStartDate(date, weekNum) {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const firstDayWeekDay = firstDay.getDay() || 7; // 将周日(0)转换为7

  // 计算第一周的周一日期
  const firstWeekStart = new Date(firstDay);
  const daysToAdd = firstDayWeekDay === 1 ? 0 : 8 - firstDayWeekDay;
  firstWeekStart.setDate(firstDay.getDate() - firstDayWeekDay + 1);

  // 计算目标周的周一日期
  const targetWeekStart = new Date(firstWeekStart);
  targetWeekStart.setDate(firstWeekStart.getDate() + (weekNum - 1) * 7);

  return targetWeekStart;
}

/** 弹窗的确认按钮 */
async function handleSubmit() {
  if (state.title == "编辑工作内容") {
    if (state.optList.length < 1) {
      proxy.$modal.msgWarning("请添加选项");
      return;
    }

    // 检查是否有未保存的编辑项
    let hasUnsavedEdits = state.optList.some((_) => _.isEdit == true);
    if (hasUnsavedEdits) {
      proxy.$modal.msgWarning("请先保存所有选项");
      return;
    }

    proxy.$modal.msgSuccess("已保存");
    handleCancel();
  } else {
    if (state.select == "") {
      proxy.$modal.msgWarning("请选择选项");
      return;
    }

    try {
      const selectedOpt = state.optList.find((opt) => opt.checked);
      if (!selectedOpt) {
        proxy.$modal.msgWarning("请选择工作内容");
        return;
      }
      const workContentId = selectedOpt.originalId;

      // 如果有workCalendarId，说明是更新操作
      if (state.currentWorkCalendarId) {
        const params = {
          workCalendarId: state.currentWorkCalendarId,
          userId: state.tableList[state.curIdx].userId,
          workContentId: workContentId,
          workDate: state.currentWorkDate,
        };

        console.log("更新工作参数：", params);

        const res = await updateWork(params);
        if (res.code === 200) {
          proxy.$modal.msgSuccess("更新成功");
          handleQuery();
        } else {
        }
      } else {
        // 原有的添加操作
        const params = {
          userId: state.tableList[state.curIdx].userId,
          workContentId: workContentId,
          workDate: state.currentWorkDate,
        };

        console.log("添加工作参数：", params);

        const res = await addWork(params);
        if (res.code === 200) {
          proxy.$modal.msgSuccess("添加成功");
          handleQuery();
        } else {
        }
      }
    } catch (error) {}

    handleCancel();
  }
}

// 保持一致的日期计算逻辑
function isDateInCurrentMonth(dayIndex) {
  if (!state.queryParams.month) return false;

  const [year, month] = state.queryParams.month.split("-");
  const weekStart = getWeekStartDate(
    new Date(year, parseInt(month) - 1),
    state.currentWeek
  );
  const targetDate = new Date(weekStart);
  targetDate.setDate(weekStart.getDate() + dayIndex);

  // 判断日期是否在当前选择的月份内
  return (
    targetDate.getFullYear() === parseInt(year) &&
    targetDate.getMonth() === parseInt(month) - 1
  );
}

/** 搜索按钮操作 */
async function handleQuery() {
  state.loading = true;
  try {
    // 从当前选中月份和周数构建查询参数
    const [year, month] = state.queryParams.month.split("-");
    const params = {
      year: year,
      month: month,
      weekNumInMonth: state.currentWeek.toString(),
      searchName: state.queryParams.name || "",
    };
    console.log("查询参数：", params);

    const res = await listWork(params);
    if (res.code === 200) {
      // 转换后端数据为表格所需格式
      state.tableList = res.data.map((user) => {
        // 创建一个包含7天的工作内容对象
        const weekData = {
          name: user.nickName,
          userId: user.userId,
          workTime: user.workTimeInList || 0,
        };

        // 初始化每天的工作内容为空
        for (let i = 1; i <= 7; i++) {
          weekData[`field${i}`] = "";
        }

        // 填充实际的工作内容
        if (user.workCalendarVoList && user.workCalendarVoList.length > 0) {
          user.workCalendarVoList.forEach((workDay) => {
            const date = new Date(workDay.workDate);
            const dayOfWeek = date.getDay() || 7;
            weekData[`field${dayOfWeek}`] = workDay.workContentName;
            // 保存 workCalendarId
            weekData[`workCalendarId${dayOfWeek}`] = workDay.workCalendarId;
          });
        }

        return weekData;
      });
    } else {
    }
  } catch (error) {
  } finally {
    state.loading = false;
  }
}

/** 重置按钮操作 */
function resetQuery() {
  state.loading = true;
  queryParams.value.name = "";

  // 设置为当前月份
  queryParams.value.month = getCurrentMonth();

  // 计算当前日期在当月的第几周
  const now = new Date();
  state.currentWeek = calculateWeekOfMonth(now);

  setTimeout(() => {
    handleQuery();
    state.loading = false;
  }, 300);
}

// 计算当前日期是当月第几周
function calculateWeekOfMonth(date) {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const firstDayWeekDay = firstDay.getDay();
  const dayOfMonth = date.getDate();
  const adjustedFirstDayWeekDay = firstDayWeekDay === 0 ? 7 : firstDayWeekDay;
  return Math.ceil((dayOfMonth + adjustedFirstDayWeekDay - 1) / 7);
}

/** 弹窗的取消按钮 */
function handleCancel() {
  getList();
  // 重置所有选项的编辑状态和选中状态
  state.optList = state.optList.map((item) => ({
    ...item,
    isEdit: false,
    checked: false,
  }));

  // 重置选中的选项
  state.select = "";

  // 关闭所有对话框
  state.dialogVisible = false;
  state.dialogVisible2 = false;
  state.dialogVisible3 = false;
  state.weekPickerVisible = false;
  state.selectedWeek = null;

  // 清空二维码表单内容
  state.codeForm.name = ""; // 清空二维码名称
  state.codeForm.time = ""; // 清空二维码时效
}

function handleEditField(row, index, idx) {
  state.curIdx = index;
  state.curField = `field${idx + 1}`;

  // 获取当前日期对应的工作日历数据
  const [year, month] = state.queryParams.month.split("-");
  const weekStart = getWeekStartDate(
    new Date(year, parseInt(month) - 1),
    state.currentWeek
  );
  const targetDate = new Date(weekStart);
  targetDate.setDate(weekStart.getDate() + idx);

  // 将日期加一天
  const nextDate = new Date(targetDate);
  nextDate.setDate(nextDate.getDate() + 1);
  const workDate = nextDate.toISOString().slice(0, 10);

  // 保存当前工作日期
  state.currentWorkDate = workDate;

  // 如果有现有工作安排，保存workCalendarId
  const currentWork = state.tableList[index][`field${idx + 1}`];
  if (currentWork) {
    state.currentWorkCalendarId =
      state.tableList[index][`workCalendarId${idx + 1}`];
    state.title = `更改 ${row.name} 的工作内容`;
  } else {
    state.currentWorkCalendarId = null;
    state.title = `添加 ${row.name} 的工作内容`;
  }

  state.dialogVisible = true;
}

function handleSet() {
  state.dialogVisible2 = true;
  getClockRuleData();
}

function handleEdit() {
  state.title = "编辑工作内容";
  state.dialogVisible = true;
}

async function handleEditOpt({ row, $index }) {
  if (row.isEdit) {
    // 保存编辑
    if (!row.name) {
      proxy.$modal.msgWarning("选项名称不能为空");
      return;
    }

    try {
      if (!row.originalId) {
        // 新增选项
        const params = {
          workContentName: row.name,
          isNeedClock: row.type === "需打卡" || row.type === 1 ? 1 : 0,
        };

        const res = await addWorkContent(params);
        if (res.code !== 200) {
          // proxy.$modal.msgError(res.msg || '新增工作内容失败');
          return;
        }
        proxy.$modal.msgSuccess("新增成功");
        getList(); // 刷新列表
        // const res2 = await listWorkContent();
        // console.log(res2);
        // if (res2.code === 200 && res2.data) {
        //   // 转换工作内容数据
        //   state.optList[$index] = res2.data[$index];
        //   const { workContentName, isNeedClock } = res2.data[$index];
        //   state.optList[$index].name = workContentName;
        //   state.optList[$index].type = isNeedClock;
        //   // 打印检查数据
        //   console.log("工作内容选项:", state.optList);
        // }
        row.isEdit = false;
        return;
      } else {
        // 编辑已有选项
        const params = {
          workContentId: row.originalId,
          workContentName: row.name,
          isNeedClock: row.type === "需打卡" || row.type === 1 ? 1 : 0,
        };

        const res = await updateWorkContent(params);
        if (res.code === 200) {
          proxy.$modal.msgSuccess("编辑成功");
          getList(); // 刷新列表
          // const res2 = await listWorkContent();
          // console.log(res2);
          // if (res2.code === 200 && res2.data) {
          //   // 转换工作内容数据
          //   state.optList[$index] = res2.data[$index];
          //   const { workContentName, isNeedClock } = res2.data[$index];
          //   state.optList[$index].name = workContentName;
          //   state.optList[$index].type = isNeedClock;
          //   // 打印检查数据
          //   console.log("工作内容选项:", state.optList);
          // }
          row.isEdit = false;
          return;
        } else {
          return;
        }
      }
    } catch (error) {
      return;
    }
  }

  row.isEdit = !row.isEdit;
}

function getValidTime(val) {
  let today = new Date();
  let year = today.getFullYear();
  let month = today.getMonth() + 1;
  let day = today.getDate();
  let monthStr = month > 9 ? month + "" : "0" + month;
  let dayStr = day > 9 ? day + "" : "0" + day;
  if (val == "一天") {
    return {
      time: `${timeFormat(today.getTime(), "yyyy-mm-dd")} 23:59:59`,
      timeStr: `${timeFormat(today.getTime(), "yyyy-mm-dd")}`,
    };
  }
  if (val == "一周") {
    return {
      time: `${timeFormat(
        today.getTime() + 86400000 * 7,
        "yyyy-mm-dd"
      )} 23:59:59`,
      timeStr: `${timeFormat(today.getTime(), "yyyy-mm-dd")} ~ ${timeFormat(
        today.getTime() + 86400000 * 7,
        "yyyy-mm-dd"
      )}`,
    };
  }
  if (val == "一个月") {
    return {
      time: `${timeFormat(
        today.getTime() + 86400000 * 30,
        "yyyy-mm-dd"
      )} 23:59:59`,
      timeStr: `${timeFormat(today.getTime(), "yyyy-mm-dd")} ~ ${timeFormat(
        today.getTime() + 86400000 * 30,
        "yyyy-mm-dd"
      )}`,
    };
  }
  if (val == "无限期") {
    return {
      time: `${year + 100}-${monthStr}-${dayStr} 23:59:59`,
      timeStr: `${timeFormat(today.getTime(), "yyyy-mm-dd")} ~ ${
        year + 100
      }-${monthStr}-${dayStr}`,
    };
  }
}

async function handleSave(type) {
  if (type == 1) {
    if (!codeForm.value.time) {
      proxy.$modal.msgWarning("请选择二维码时效");
      return;
    }

    let timeObj = getValidTime(codeForm.value.time);
    let dataArr = [
      {
        name: !codeForm.value.name ? "日常考勤二维码" : codeForm.value.name,
        time: timeObj.time,
        timeStr: timeObj.timeStr,
      },
    ];
    generateQRCode(dataArr, false);
    proxy.$modal.msgSuccess("下载成功");
    setTimeout(() => {
      state.dialogVisible3 = false;
      codeForm.value.name = "";
      codeForm.value.time = "";
      proxy.$modal.closeLoading();
    }, 200);
    return;
  }

  // 分开验证上班打卡时间和下班打卡时间
  if (
    !Array.isArray(state.clockForm.beginTime) ||
    !state.clockForm.beginTime.length
  ) {
    proxy.$modal.msgWarning("上班打卡时间不能为空");
    return;
  }

  if (
    !Array.isArray(state.clockForm.overTime) ||
    !state.clockForm.overTime.length
  ) {
    proxy.$modal.msgWarning("下班打卡时间不能为空");
    return;
  }

  try {
    // 转换上班打卡时间
    const clockInRule = state.clockForm.beginTime.map((time) => {
      return (
        time.getHours().toString().padStart(2, "0") +
        ":" +
        time.getMinutes().toString().padStart(2, "0") +
        ":00"
      );
    });

    // 转换下班打卡时间
    const clockOffRule = state.clockForm.overTime.map((time) => {
      return (
        time.getHours().toString().padStart(2, "0") +
        ":" +
        time.getMinutes().toString().padStart(2, "0") +
        ":00"
      );
    });

    // 过滤掉空的WIFI名称
    const wifiNameRule = state.clockForm.wifiList
      .map((item) => item.name)
      .filter((name) => name);

    // 转换打卡范围
    const radiusMap = {
      0: 200,
      1: 500,
      2: 800,
      3: 1000,
    };

    const params = {
      clockInRule,
      clockOffRule,
      wifiNameRule,
      clockPlaceCenter: state.clockForm.center || "",
      clockPlaceRadius: radiusMap[state.clockForm.range] || 200,
    };

    const res = await setClockRule(params);
    if (res.code === 200) {
      proxy.$modal.msgSuccess("保存成功");
      state.dialogVisible2 = false;
    } else {
      proxy.$modal.msgError(res.msg || "保存失败");
    }
  } catch (error) {
    proxy.$modal.msgError("保存失败");
  }
}

async function handleDelOpt({ row, $index }) {
  proxy.$modal
    .confirm("是否确认删除该工作内容？")
    .then(async () => {
      if (!!row.originalId) {
        try {
          const params = {
            workContentId: row.originalId,
          };
          const res = await delWorkContent(params);
          if (res.code === 200) {
            proxy.$modal.msgSuccess("删除成功");
            optList.value.splice($index, 1);
            // getList(); // 刷新列表
          } else {
          }
        } catch (error) {}
      } else {
        optList.value.splice($index, 1);
      }
    })
    .catch(() => {});
}

function handleAddOpt() {
  if (state.optList.length >= 5) {
    proxy.$modal.msgWarning("最多只能添加5个选项");
    return;
  }

  const newNumber = (state.optList.length + 1).toString();
  state.optList.push({
    number: newNumber,
    originalId: null,
    name: "",
    type: 1,
    isEdit: true,
  });
}

function handleChangeCheck(val, index) {
  if (val) {
    // 清除其他选中项
    state.optList = state.optList.map((item, idx) => {
      item.checked = idx === index;
      return item;
    });
  }
  state.select = val ? state.optList[index].name : "";
}

function handleRowClick(row) {
  if (state.title === "编辑工作内容") return; // 编辑模式下不处理行点击

  const idx = state.optList.findIndex((item) => item.number === row.number);
  if (idx > -1) {
    state.optList[idx].checked = !state.optList[idx].checked;
    if (state.optList[idx].checked) {
      // 如果选中，清除其他选中项
      state.optList.forEach((item, index) => {
        if (index !== idx) {
          item.checked = false;
        }
      });
    }
    state.select = state.optList[idx].checked ? state.optList[idx].name : "";
  }
}

async function getList() {
  try {
    const res = await listWorkContent();
    if (res.code === 200 && res.data) {
      // 转换工作内容数据
      state.optList = res.data.map((item, index) => ({
        number: (index + 1).toString(),
        originalId: item.workContentId,
        name: item.workContentName,
        type: item.isNeedClock,
        isDefault: item.isDefault || false,
        isEdit: false,
        checked: false,
      }));

      // 打印检查数据
      console.log("工作内容选项:", state.optList);
    } else {
    }
  } catch (error) {}
}

function handleShowMap() {
  state.mapVisible = true;
  isNum.value += 1;
}

function handleConfirmLocation() {
  console.log("mapRef", mapRef.value);

  const mapData = mapRef.value.mapData;
  // 保存经纬度但显示地址
  state.clockForm.center = mapData.lat + "," + mapData.lng;
  state.clockForm.centerAddress = mapData.address;
  state.mapVisible = false;
}

// 添加处理月份切换的方法
function handlePrevMonth() {
  if (!queryParams.value.month) return;
  const date = new Date(queryParams.value.month);
  date.setMonth(date.getMonth() - 1);
  queryParams.value.month = date.toISOString().slice(0, 7);
  handleMonth(queryParams.value.month);
}

function handleNextMonth() {
  if (!queryParams.value.month) return;
  const date = new Date(queryParams.value.month);
  date.setMonth(date.getMonth() + 1);
  queryParams.value.month = date.toISOString().slice(0, 7);
  handleMonth(queryParams.value.month);
}

// 初始化数据
function initDateData() {
  // 设置当前月份
  state.queryParams.month = getCurrentMonth();
  // 计算并设置当前周数
  const now = new Date();
  state.currentWeek = calculateWeekOfMonth(now);
  // 触发查询
  handleQuery();
}

// 在组件挂载时初始化
onMounted(() => {
  getList(); // 添加这行确保获取工作内容选项
  initDateData();
});

// 处理上一周
function handlePrevWeek() {
  if (!state.queryParams.month) return;

  const currentDate = new Date(state.queryParams.month + "-01");
  const maxWeeks = getWeeksInMonth(currentDate);

  state.currentWeek--;
  if (state.currentWeek < 1) {
    // 如果当前周小于1，切换到上个月的最后一周
    handlePrevMonth();
    const prevDate = new Date(state.queryParams.month + "-01");
    state.currentWeek = getWeeksInMonth(prevDate);
  }

  handleQuery();
}

// 处理下一周
function handleNextWeek() {
  if (!state.queryParams.month) return;

  const currentDate = new Date(state.queryParams.month + "-01");
  const maxWeeks = getWeeksInMonth(currentDate);

  state.currentWeek++;
  if (state.currentWeek > maxWeeks) {
    // 如果当前周大于本月最大周数，切换到下个月第一周
    handleNextMonth();
    state.currentWeek = 1;
  }

  handleQuery();
}

// 获取某月有几周
function getWeeksInMonth(date) {
  const firstDay = new Date(date.getFullYear(), date.getMonth(), 1);
  const lastDay = new Date(date.getFullYear(), date.getMonth() + 1, 0);
  const firstDayWeekDay = firstDay.getDay() || 7;
  return Math.ceil((lastDay.getDate() + firstDayWeekDay - 1) / 7);
}

// 获取当前日期并格式化为YYYY-MM
function getCurrentMonth() {
  const now = new Date();
  const year = now.getFullYear();
  const month = String(now.getMonth() + 1).padStart(2, "0");
  return `${year}-${month}`;
}

// 显示周数选择器
function showWeekPicker() {
  state.weekPickerVisible = true;
}

// 选择周数
function selectWeek(week) {
  state.currentWeek = week;
  state.weekPickerVisible = false;
  handleQuery();
}

// 显示月份选择器
function showMonthPicker() {
  state.monthPickerVisible = true;
  if (state.queryParams.month) {
    state.currentYear = parseInt(state.queryParams.month.split("-")[0]);
  }
}

// 格式化月份显示
function formatMonth(monthStr) {
  if (!monthStr) return "";
  const [year, month] = monthStr.split("-");
  return `${year}年${parseInt(month)}月`; // 使用 parseInt 去除前导零
}

// 判断是否是当前选中的月份
function isCurrentMonth(month) {
  if (!state.queryParams.month) return false;
  const currentMonth = parseInt(state.queryParams.month.split("-")[1]);
  return month === currentMonth;
}

// 选择月份
function selectMonth(month) {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  const oneYearLater = new Date();
  oneYearLater.setFullYear(now.getFullYear() + 1);

  // 检查选择的日期是否在允许范围内
  if (
    (state.currentYear === currentYear && month < currentMonth) || // 过去的月份
    (state.currentYear === oneYearLater.getFullYear() &&
      month > oneYearLater.getMonth() + 1) || // 超过一年后的月份
    state.currentYear < currentYear || // 过去的年份
    state.currentYear > oneYearLater.getFullYear() // 超过一年后的年份
  ) {
    return;
  }

  const monthStr = month.toString().padStart(2, "0");
  state.queryParams.month = `${state.currentYear}-${monthStr}`;
  state.monthPickerVisible = false;
  handleMonth(state.queryParams.month);
}

// 添加月份项的禁用样式判断
function isMonthDisabled(month) {
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  const oneYearLater = new Date();
  oneYearLater.setFullYear(now.getFullYear() + 1);

  return (
    (state.currentYear === currentYear && month < currentMonth) || // 过去的月份
    (state.currentYear === oneYearLater.getFullYear() &&
      month > oneYearLater.getMonth() + 1) || // 超过一年后的月份
    state.currentYear < currentYear || // 过去的年份
    state.currentYear > oneYearLater.getFullYear() // 超过一年后的年份
  );
}

// 处理年份切换
function handlePrevYear() {
  const now = new Date();
  const currentYear = now.getFullYear();
  if (state.currentYear > currentYear) {
    state.currentYear--;
  }
}

function handleNextYear() {
  const now = new Date();
  const maxYear = now.getFullYear() + 1;
  if (state.currentYear < maxYear) {
    state.currentYear++;
  }
}

// 修改加载腾讯地图API的函数
function loadTencentMap() {
  return new Promise((resolve, reject) => {
    if (window.TMap) {
      resolve(window.TMap);
      return;
    }

    window.init = function () {
      const checkService = setInterval(() => {
        if (window.TMap && window.TMap.service) {
          clearInterval(checkService);
          resolve(window.TMap);
        }
      }, 100);
    };

    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=T7GBZ-CWSW5-HXVIJ-IOR44-YLMQ5-ZCBE7&libraries=service&callback=init`;
    document.head.appendChild(script);

    script.onerror = reject;
  });
}

// 添加一个转换经纬度到地址的函数
async function convertLocationToAddress(location) {
  try {
    await loadTencentMap();
    return new Promise((resolve) => {
      if (!window.TMap || !TMap.service) {
        console.error("TMap service not loaded");
        resolve("");
        return;
      }

      const [lat, lng] = location.split(",");
      const geocoder = new TMap.service.Geocoder();
      const latLng = new TMap.LatLng(parseFloat(lat), parseFloat(lng));

      geocoder
        .getAddress({
          location: latLng,
          get_poi: 1,
        })
        .then((result) => {
          if (result.status === 0 && result.result) {
            // 使用基础地址
            resolve(result.result.address || "");
          } else {
            console.error("获取地址失败:", result);
            resolve("");
          }
        })
        .catch((error) => {
          console.error("获取地址信息失败:", error);
          resolve("");
        });
    });
  } catch (error) {
    console.error("转换地址失败:", error);
    return "";
  }
}

// 修改获取打卡规则的函数
async function getClockRuleData() {
  try {
    const res = await getClockRule();
    if (res.code === 200 && res.data) {
      console.log("打卡规则数据：", res.data);

      // 转换上班打卡时间
      state.clockForm.beginTime = res.data.clockInRule.map((time) => {
        const [hours, minutes] = time.split(":");
        return new Date(2000, 0, 1, hours, minutes);
      });

      // 转换下班打卡时间
      state.clockForm.overTime = res.data.clockOffRule.map((time) => {
        const [hours, minutes] = time.split(":");
        return new Date(2000, 0, 1, hours, minutes);
      });

      // 转换WIFI列表
      state.clockForm.wifiList = res.data.wifiNameRule.map((name) => ({
        name,
      }));
      if (state.clockForm.wifiList.length === 0) {
        state.clockForm.wifiList = [{ name: "" }];
      }

      // 设置打卡范围
      const radiusToOption = {
        200: 0,
        500: 1,
        800: 2,
        1000: 3,
      };
      state.clockForm.range = radiusToOption[res.data.clockPlaceRadius] ?? 0;

      // 保存经纬度并转换为地址
      if (res.data.clockPlaceCenter) {
        state.clockForm.center = res.data.clockPlaceCenter;
        // 异步转换地址
        const address = await convertLocationToAddress(
          res.data.clockPlaceCenter
        );
        state.clockForm.centerAddress = address;
      } else {
        state.clockForm.center = "";
        state.clockForm.centerAddress = "";
      }
    }
  } catch (error) {
    console.error("获取打卡规则失败:", error);
  }
}

function isPastDate(dayIndex) {
  if (!state.queryParams.month) return false;

  const [year, month] = state.queryParams.month.split("-");
  const weekStart = getWeekStartDate(
    new Date(year, parseInt(month) - 1),
    state.currentWeek
  );
  const targetDate = new Date(weekStart);
  targetDate.setDate(weekStart.getDate() + dayIndex);

  // 设置目标日期为当天的 00:00:00
  targetDate.setHours(0, 0, 0, 0);

  // 获取当前日期，并设置为 00:00:00
  const today = new Date();
  today.setHours(0, 0, 0, 0);

  // 如果目标日期小于等于今天，返回true（包括今天）
  return targetDate <= today;
}

// 获取日期字符串
function getDateString(dayIndex) {
  if (!state.queryParams.month) return "";

  const [year, month] = state.queryParams.month.split("-");
  const weekStart = getWeekStartDate(
    new Date(year, parseInt(month) - 1),
    state.currentWeek
  );
  const targetDate = new Date(weekStart);
  targetDate.setDate(weekStart.getDate() + dayIndex);

  return `（${targetDate.getMonth() + 1}月${targetDate.getDate()}日）`;
}
</script>

<style lang="scss" scoped>
.schedule {
  table {
    border: none;
    margin-bottom: 20px;
    border-bottom: 1px solid #ebeef5;
    border-right: 1px solid #ebeef5;

    tbody:nth-child(odd) {
      background-color: #fafafa;
    }
    /*  */
    .table-td2 {
      background-color: #ffffff; // 本周工作时长行的背景色
    }

    .table-td {
      background-color: #f8f8f9; // 用户工作内容行的背景色
    }

    .cell {
      min-width: 150px;
      color: #606266;
      font-size: 14px;
      div {
        margin-top: 5px;
        // text-decoration: underline;
        color: #4095e5;
        cursor: pointer;
      }
    }

    .table {
      &-th {
        border: 1px solid #ebeef5;
        padding: 10px;
        background-color: #f8f8f9;
        border-bottom: none;
        border-right: none;
        color: #515a6e;
      }
      &-td {
        padding: 15px;
        text-align: center;
        border: 1px solid #ebeef5;
        border-bottom: none;
        border-right: none;
      }
      &-td2 {
        padding: 10px;
        border: 1px solid #ebeef5;
        border-bottom: none;
        border-right: none;
        font-size: 14px;
        span {
          display: inline-block;
          margin-right: 20px;
          color: #606266;
        }
      }
    }
  }

  .week {
    &-link {
      text-decoration: underline;
      color: #4095e5;
      cursor: pointer;
    }
  }
  .optAdd {
    cursor: pointer;
    // border: 1px solid red;
    padding: 10px;
    text-align: center;
    color: #4095e5;
    // background-color: #f1f1f1;
  }

  .empty-schedule {
    color: #4095e5;
    cursor: pointer;

    &:hover {
      text-decoration: underline;
    }
  }
}

:deep(.el-table-column--selection .cell) {
  padding-right: 14px;
  padding-left: 14px;
}

.custom-date-picker {
  display: flex;
  align-items: center;
  gap: 5px;

  :deep(.el-input__wrapper) {
    padding: 0 8px;
  }

  :deep(.el-input__inner) {
    text-align: center;
  }

  .el-button {
    border: none;
    padding: 8px;
    &:hover {
      background-color: #f5f7fa;
    }
  }

  .month-selector {
    width: 120px;
    height: 32px;
    line-height: 32px;
    padding: 0 8px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    border: none;
    background: none;

    &:hover {
      color: var(--el-color-primary);
    }

    .placeholder {
      color: var(--el-text-color-placeholder);
    }
  }
}

.week-display {
  min-width: 80px;
  text-align: center;
  line-height: 32px;
  background-color: var(--el-fill-color-blank);
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 0 8px;
  font-size: 14px;
  color: var(--el-text-color-regular);
  cursor: pointer;

  &:hover {
    border-color: var(--el-border-color-hover);
  }
}

.week-picker-popover {
  .week-picker-content {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;

    .week-item {
      text-align: center;
      padding: 8px 0;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-light);
      }

      &.active {
        color: var(--el-color-primary);
        font-weight: bold;
        background-color: var(--el-color-primary-light-9);
      }
    }
  }
}

.month-picker-popover {
  .month-picker-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 8px 12px;
    border-bottom: 1px solid var(--el-border-color-light);
    margin-bottom: 8px;
  }

  .month-picker-content {
    padding: 8px;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;

    .month-item {
      text-align: center;
      padding: 8px 0;
      cursor: pointer;
      border-radius: 4px;

      &:hover {
        color: var(--el-color-primary);
        background-color: var(--el-fill-color-light);
      }

      &.active {
        color: var(--el-color-primary);
        font-weight: bold;
        background-color: var(--el-color-primary-light-9);
      }

      &.disabled {
        color: var(--el-text-color-disabled);
        cursor: not-allowed;
        background-color: var(--el-fill-color-light);

        &:hover {
          color: var(--el-text-color-disabled);
          background-color: var(--el-fill-color-light);
        }
      }
    }
  }
}
</style>
