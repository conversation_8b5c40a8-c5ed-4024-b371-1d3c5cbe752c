<template>
  <div class="pieData">
    <div class="pieData-chart">
      <img
        class="pieData-bg"
        src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie3-bg.png"
      />
      <Echarts
        :ref="id + 'Ref'"
        :id="id"
        class="echart"
        width="100%"
        height="6vw"
        :fullOptions="option"
      />
    </div>
    <div class="pieData-list">
      <div
        class="pieData-item"
        :class="[`${id}-item`]"
        v-for="(item, index) in option.options.series[0].data"
        :data-index="index"
      >
        <div
          class="pieData-bar"
          :style="{ backgroundColor: item.itemStyle.bgColor2 }"
        >
          <div
            class="pieData-subBar"
            :style="{ backgroundColor: item.itemStyle.bgColor }"
          ></div>
        </div>
        <div>
          <div class="pieData-name">
            {{ item.name }}
          </div>
          <div class="pieData-count">
            {{ item.value > 999 ? "999+" : item.value }}
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Echarts from "@/components/Echarts/index.vue";
import { toRefs } from "vue";

const props = defineProps({
  id: {
    type: String,
    default: "pieData",
    required: true,
  },
  option: {
    type: Object,
    default: () => ({
      options: {
        series: [
          {
            type: "pie",
            radius: ["58%", "64%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            padAngle: 0,
            emphasis: {
              scaleSize: 2.5,
              label: {
                show: true,
                fontSize: 40,
                fontWeight: "bold",
              },
            },
            label: {
              show: false,
              position: "center",
              formatter: "{d|{d}%}\n{b|{b}}",
              rich: {
                d: {
                  color: "#fff",
                  fontSize: fitChartSize(14),
                  lineHeight: fitChartSize(18),
                  fontWeight: "bold",
                },
                b: {
                  color: "#D4EDFF",
                  fontSize: fitChartSize(12),
                },
              },
            },
            labelLine: {
              show: false,
              length: 2,
              length2: 5,
              lineStyle: {
                color: "rgba(81, 131, 255, .2)",
              },
            },
            data: [
              {
                name: "1天以内",
                value: 10,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#00aaff",
                      },
                      {
                        offset: 1,
                        color: "#0170ff",
                      },
                    ],
                  },
                  bgColor: "#0091ff",
                  bgColor2: "rgba(0, 145, 255, 0.3)",
                },
              },
              {
                name: "1-5天",
                value: 20,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#0bc699",
                      },
                      {
                        offset: 1,
                        color: "#0ed877",
                      },
                    ],
                  },
                  bgColor: "#0ED877",
                  bgColor2: "rgba(14, 216, 119, 0.3)",
                },
              },
              {
                name: "5-10天",
                value: 30,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#00eaff",
                      },
                      {
                        offset: 1,
                        color: "#00abff",
                      },
                    ],
                  },
                  bgColor: "#67DBFF",
                  bgColor2: "rgba(103, 219, 255, 0.3)",
                },
              },
              {
                name: "10天以上",
                value: 40,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#fda331",
                      },
                      {
                        offset: 1,
                        color: "#ff4e02",
                      },
                    ],
                  },
                  bgColor: "#FFBF60",
                  bgColor2: "rgba(255, 191, 96, 0.3)",
                },
              },
            ],
          },
        ],
      },
    }),
    required: true,
  },
});

const state = reactive({
  pieData1Ref: null,
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData5Ref: null,
  pieData6Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData14Ref: null,
  pieData15Ref: null,
});
const {
  pieData1Ref,
  pieData2Ref,
  pieData3Ref,
  pieData4Ref,
  pieData5Ref,
  pieData6Ref,
  pieData7Ref,
  pieData8Ref,
  pieData9Ref,
  pieData10Ref,
  pieData11Ref,
  pieData12Ref,
  pieData13Ref,
  pieData14Ref,
  pieData15Ref,
} = toRefs(state);

const instance = getCurrentInstance();

const initPieFunc = () => {
  let timer = null;
  function waitForRef() {
    clearTimeout(timer);
    const echarts3DRef = instance?.proxy?.$refs[props.id + "Ref"];
    if (echarts3DRef) {
      document.querySelectorAll(`.${props.id}-item`).forEach((div) => {
        div.addEventListener("mouseover", function (e) {
          const index = parseInt(this.dataset.index); // 获取对应的数据索引
          echarts3DRef.dispatchAction({
            type: "highlight", // 触发高亮动作
            seriesIndex: 0, // 系列索引（第一个饼图）
            dataIndex: index, // 数据项索引
          });
        });

        div.addEventListener("mouseout", function () {
          const index = parseInt(this.dataset.index);
          echarts3DRef.dispatchAction({
            type: "downplay", // 取消高亮
            seriesIndex: 0,
            dataIndex: index,
          });
        });
      });
    } else {
      timer = setTimeout(waitForRef, 100);
    }
  }
  nextTick(waitForRef);
};

onBeforeUnmount(() => {
  if (state[`${props.id}Ref`]) {
    state[`${props.id}Ref`].unMountFunc();
  }
});

defineExpose({
  initPieFunc,
});
</script>

<style lang="scss" scoped>
.pieData {
  display: flex;
  align-items: center;
  margin-top: 0.5vw;
  &-chart {
    position: relative;
    flex: 1;
  }
  &-bg {
    position: absolute;
    top: 0.95vw;
    left: 0.8vw;
    width: 4.1vw;
    height: 4.1vw;
  }
  &-list {
    flex: 1;
    height: 90%;
    font-size: 0.6vw;
    display: flex;
    justify-content: space-between;
    flex-direction: row;
    flex-wrap: wrap;
    gap: 1vw 0;
  }
  &-item {
    cursor: pointer;
    display: flex;
    width: 50%;
    height: 1.5vw;
    text-align: left;
    color: #fff;
  }
  &-bar {
    width: 0.1vw;
    height: 100%;
    margin-right: 0.25vw;
  }
  &-subBar {
    width: 0.1vw;
    height: 0.7vw;
  }
  &-name {
    display: flex;
    align-items: center;
    gap: 0 0.2vw;
    font-size: 0.5vw;
    color: rgba(255, 255, 255, 0.6);
  }
  &-count {
    margin-top: 0.2vw;
  }
}
</style>