<template>
  <div class="project app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">项目信息管理</div>
      </template>
      <div class="project-main">
        <div class="project-main_search search-list">
          <el-form
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="searchNoOrName">
              <el-input
                v-model="queryParams.searchNoOrName"
                placeholder="请输入项目编号/名称"
                style="width: 200px"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="searchResponsible">
              <el-input
                v-model="queryParams.searchResponsible"
                placeholder="请输入项目负责人"
                style="width: 200px"
                clearable
                @keyup.enter="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="searchStartTime">
              <el-date-picker
                v-model="queryParams.searchStartTime"
                type="datetimerange"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                start-placeholder="项目开始时间范围"
                end-placeholder="项目开始时间范围"
                clearable
                style="width: 370px"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="searchEndTime">
              <el-date-picker
                v-model="queryParams.searchEndTime"
                type="datetimerange"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                start-placeholder="项目结束时间范围"
                end-placeholder="项目结束时间范围"
                clearable
                style="width: 370px"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="projectDeptId">
              <el-tree-select
                v-model="queryParams.projectDeptId"
                :props="deptProps"
                :data="deptList"
                placeholder="请选择项目归属部门"
                check-strictly
                :render-after-expand="false"
                :expand-on-click-node="false"
                style="width: 200px"
                clearable
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
        </div>
        <el-button
          class="mb12"
          plain
          type="primary"
          icon="Plus"
          @click="handleAdd"
          >新建项目信息</el-button
        >
        <div class="project-main_table">
          <el-table ref="tableRef" v-loading="loading" :data="tableList" border>
            <el-table-column
              label="项目编号"
              align="center"
              minWidth="100px"
              prop="projectNo"
              show-overflow-tooltip
            />
            <el-table-column
              label="项目名称"
              align="center"
              minWidth="150px"
              prop="projectName"
              show-overflow-tooltip
            />
            <el-table-column
              label="项目负责人"
              align="center"
              minWidth="100px"
              show-overflow-tooltip
              prop="responsibleNameStr"
            />
            <el-table-column
              label="项目干系人"
              align="center"
              minWidth="100px"
              prop="stakeholderNameStr"
              show-overflow-tooltip
            />
            <el-table-column
              label="项目开始时间"
              align="center"
              minWidth="160px"
              prop="startTime"
            />
            <el-table-column
              label="项目结束时间"
              align="center"
              minWidth="160px"
              prop="endTime"
            >
              <template #default="scope">
                {{ scope.row.endTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="项目金额"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
              prop="projectAmount"
            />
            <el-table-column
              label="项目参与人"
              align="center"
              minWidth="100px"
              show-overflow-tooltip
              prop="partakeNameStr"
            />
            <el-table-column
              label="项目归属部门"
              align="center"
              minWidth="120px"
              prop="projectDeptName"
              show-overflow-tooltip
            />
            <el-table-column
              label="甲方"
              align="center"
              minWidth="120px"
              prop="partyA"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.partyA || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="乙方"
              align="center"
              minWidth="120px"
              prop="partyB"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.partyB || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="是否开放小程序搜索"
              align="center"
              minWidth="100px"
            >
              <template #default="{ row }">
                {{ row.isOpen ? "开放" : "不开放" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="320"
              align="center"
              fixed="right"
            >
              <template #default="scope">
                <el-button
                  link
                  type="success"
                  icon="Search"
                  @click.stop="handleCheck(scope.row, 0)"
                  >查看</el-button
                >
                <el-button
                  link
                  type="primary"
                  icon="Edit"
                  @click.stop="handleCheck(scope.row, 1)"
                  >编辑</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope.row)"
                  >删除</el-button
                >
                <el-button
                  link
                  type="warning"
                  icon="Document"
                  @click.stop="handleCreate(scope.row)"
                  >生成任务单</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 详情弹窗 -->
    <el-dialog
      class="custom-dialog"
      title="新建项目信息"
      v-model="dialogVisible"
      top="5vh"
      width="645px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancel"
    >
      <el-form ref="projectRef" :model="projectInfo" :rules="rules">
        <el-form-item prop="projectName" label="项目名称">
          <el-input
            v-model="projectInfo.projectName"
            placeholder="请输入项目名称"
            style="width: 100%"
            maxlength="30"
            clearable
          />
        </el-form-item>
        <el-form-item prop="responsibleId" label="项目负责人">
          <el-select
            v-model="projectInfo.responsibleId"
            placeholder="请选择项目负责人"
            style="width: 250px"
            clearable
            filterable
            multiple
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
          <el-input
            v-model="projectInfo.responsibleName"
            placeholder="请输入其他项目负责人"
            style="width: 250px; margin-left: 10px"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item prop="stakeholderId" label="项目干系人">
          <el-select
            v-model="projectInfo.stakeholderId"
            placeholder="请选择项目干系人"
            style="width: 250px"
            filterable
            multiple
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
          <el-input
            v-model="projectInfo.stakeholderName"
            placeholder="请输入其他项目干系人"
            style="width: 250px; margin-left: 10px"
            maxlength="50"
          />
        </el-form-item>
        <el-form-item prop="partakeId" label="项目参与人">
          <el-select
            v-model="projectInfo.partakeId"
            placeholder="请选择项目参与人"
            style="width: 250px"
            filterable
            multiple
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
          <el-input
            v-model="projectInfo.partakeName"
            placeholder="请输入其他项目参与人"
            style="width: 250px; margin-left: 10px"
            maxlength="50"
          />
        </el-form-item>
        <el-row>
          <el-col :span="24">
            <el-form-item prop="startTime" label="项目开始时间">
              <el-date-picker
                v-model="projectInfo.startTime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请选择项目开始时间"
                style="width: 250px"
                @change="projectInfo.endTime = null"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="endTime" label="项目结束时间">
              <el-date-picker
                v-model="projectInfo.endTime"
                type="datetime"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                placeholder="请先选择项目开始时间"
                style="width: 250px"
                :disabled-date="disabledDate"
                :disabled="!projectInfo.startTime"
                @change="handleEndTimeChange"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="projectAmount" label="项目金额">
              <el-input-number
                v-model="projectInfo.projectAmount"
                :min="0"
                :value-on-clear="0"
                style="margin-right: 10px; width: 170px"
              />元
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="projectDeptId" label="项目归属部门">
              <el-tree-select
                v-model="projectInfo.projectDeptId"
                :props="deptProps"
                :data="deptList"
                placeholder="请选择项目归属部门"
                check-strictly
                :render-after-expand="false"
                :expand-on-click-node="false"
                style="width: 250px"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="partyA" label="甲方">
              <el-input
                v-model="projectInfo.partyA"
                placeholder="请输入甲方名称"
                style="width: 250px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item prop="partyB" label="乙方">
              <el-input
                v-model="projectInfo.partyB"
                placeholder="请输入乙方名称"
                style="width: 250px"
              />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item prop="isOpen" label="开放小程序">
          <el-radio-group v-model="projectInfo.isOpen">
            <el-radio label="不开放" :value="0" />
            <el-radio label="开放" :value="1" />
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click.stop="handleCancel">取消</el-button>
        <el-button type="primary" @click.stop="handleSubmit" v-throttle
          >确定</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="project">
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import { useRouter } from "vue-router";
import {
  addSchoolProject,
  getSchoolProjectPage,
  delSchoolProject,
} from "@/api/order";
import { getUserByRole } from "@/api/system/user";
import { listTree } from "@/api/system/distribution";

const { proxy } = getCurrentInstance();
const router = useRouter();
const projectRef = ref(null);
const userStore = useUserStore(); // 获取当前登录的用户信息
const state = reactive({
  total: 0,
  curprojectIndex: -1,
  loading: false,
  dialogVisible: false,
  // 根据条件筛选后显示的数据
  tableList: [],
  userList: [],
  deptList: [],
  typeList: [
    { label: "技术支持", value: "技术支持" },
    { label: "物流查询", value: "物流查询" },
    { label: "财务咨询", value: "财务咨询" },
    { label: "售后服务", value: "售后服务" },
  ],
  statusList: [
    { label: "未答复", value: "未答复", type: "danger" },
    { label: "已答复", value: "已答复", type: "success" },
  ],
  // 列表条件筛选字段
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    projectDeptId: "",
    searchResponsible: "",
    searchNoOrName: "",
    searchStartTime: "",
    searchEndTime: "",
  },
  // 详情弹窗的某条数据详情
  projectInfo: {
    projectName: "",
    responsibleId: [],
    responsibleName: "",
    stakeholderId: [],
    stakeholderName: "",
    partakeId: [],
    partakeName: "",
    startTime: "",
    endTime: "",
    projectAmount: 0,
    projectDeptId: "",
    partyA: "",
    partyB: "",
    isOpen: 0,
  },
  deptProps: {
    label: "deptName",
    children: "children",
    value: "deptId",
  },
  rules: {
    projectName: [
      { required: true, message: "项目名称不能为空", trigger: "blur" },
    ],
    projectAmount: [
      {
        validator: validateAmount,
        trigger: ["blur", "change"],
      }
    ],
    responsibleId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 0),
        trigger: ["blur", "change"],
      },
    ],
    stakeholderId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 1),
        trigger: ["blur", "change"],
      },
    ],
    partakeId: [
      {
        required: true,
        validator: (rule, value, callback) =>
          validateName(rule, value, callback, 2),
        trigger: ["blur", "change"],
      },
    ],
    startTime: [
      {
        required: true,
        message: "项目开始时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
    projectDeptId: [
      {
        required: true,
        message: "归属部门不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  rules,
  total,
  deptProps,
  userList,
  deptList,
  curprojectIndex,
  tableList_all,
  projectInfo,
  tableList,
  typeList,
  statusList,
  dialogVisible,
  queryParams,
  loading,
} = toRefs(state);

const disabledDate = (date) => {
  if (date.getTime() < new Date(state.projectInfo.startTime).getTime()) {
    return true;
  }
  return false;
};

const handleEndTimeChange = (val) => {
  console.log("项目结束时间", val);
  if (
    new Date(state.projectInfo.startTime).getTime() >= new Date(val).getTime()
  ) {
    proxy.$modal.msgError("结束时间不能小于等于开始时间");
    state.projectInfo.endTime = null;
  }
};

function validateAmount(rule, value, callback) {
  if (value > 999999999) {
    callback(new Error('项目金额不能大于999999999'));
  } else {
    callback();
  }
};

const validateName = (rule, value, callback, index) => {
  let arr = [
    { label: "项目负责人", prop: "responsibleName" },
    { label: "项目干系人", prop: "stakeholderName" },
    { label: "项目参与人", prop: "partakeName" },
  ];
  // console.log(value, state.projectInfo[arr[index].prop]);
  if (state.projectInfo[arr[index].prop] === "" && value.length == 0) {
    callback(new Error(`${arr[index].label}不能为空`));
  } else {
    callback();
  }
};

/** 获取运维人员列表 */
function getUserList() {
  getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["maintain", "maintainManage"],
  }).then((resp) => {
    console.log("运维人员", resp);
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

/** 获取部门列表 */
function getDeptList() {
  listTree({ status: 0 }).then((resp) => {
    console.log("部门树", resp);
    if (resp.data) {
      state.deptList = resp.data;
    }
  });
}

function handleAdd() {
  state.projectInfo = {
    projectName: "",
    responsibleId: [],
    responsibleName: "",
    stakeholderId: [],
    stakeholderName: "",
    partakeId: [],
    partakeName: "",
    startTime: "",
    endTime: "",
    projectAmount: 0,
    projectDeptId: "",
    partyA: "",
    partyB: "",
    isOpen: 0,
  };
  state.dialogVisible = true;
}

function handleDel(item) {
  proxy.$modal
    .confirm(`确定删除项目名称为${item.projectName}的信息？`)
    .then(() => {
      delSchoolProject({ projectId: item.projectId }).then((resp) => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      });
    });
}

function handleCreate(item) {
  router.push({
    path: "/order/addProjectTask",
    query: { projectId: item.projectId },
  });
}

/** 列表的详情按钮 */
function handleCheck({ projectId }, type) {
  // 判断该行数据的任务状态，已答复则弹窗里的任务状态项为禁用
  router.push({
    path: "projectInfo",
    query: { id: projectId, type },
  });
}

/** 弹窗的确认按钮 */
function handleSubmit() {
  projectRef.value.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const { responsibleId, stakeholderId, partakeId } = state.projectInfo;
      let d = {
        ...state.projectInfo,
        responsibleId: responsibleId.join(","),
        stakeholderId: stakeholderId.join(","),
        partakeId: partakeId.join(","),
      };
      addSchoolProject(d)
        .then((resp) => {
          proxy.$modal.closeLoading();
          proxy.$modal.msgSuccess("新增成功");
          handleCancel();
          getList();
        })
        .catch(() => proxy.$modal.closeLoading());
    }
  });
}

/** 弹窗的取消按钮 */
function handleCancel() {
  proxy.resetForm("projectRef");
  state.dialogVisible = false;
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

function getList() {
  state.loading = true;
  getSchoolProjectPage(state.queryParams)
    .then((resp) => {
      if (resp.data) {
        const { rows = [], total = 0 } = resp.data;
        console.log("项目列表", resp, state.tableList);

        state.tableList = rows.reduce((res, cur) => {
          const {
            partakeName,
            sysPartakeName,
            sysResponsibleName,
            responsibleName,
            sysStakeholderName,
            stakeholderName,
          } = cur;
          res.push({
            ...cur,
            partakeNameStr: (sysPartakeName?.split("、") || [])
              .concat(partakeName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
            responsibleNameStr: (sysResponsibleName?.split("、") || [])
              .concat(responsibleName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
            stakeholderNameStr: (sysStakeholderName?.split("、") || [])
              .concat(stakeholderName?.split("、") || [])
              .filter((_) => !!_)
              .join("、"),
          });
          return res;
        }, []);
        state.total = total;
      }
      state.loading = false;
    })
    .catch(() => (state.loading = false));
}

onMounted(() => {
  getList();
  getDeptList();
  getUserList();
});
</script>

<style lang="scss">
.project {
  :deep(.custom-dialog) {
    :deep(.el-dialog__header) {
      border-bottom: 1px solid #ddd;
      margin-right: 0;
      padding-bottom: 20px;
    }
  }
  :deep(.el-dialog__header) {
    border-bottom: 1px solid #ddd;
    margin-right: 0;
    padding-bottom: 20px;
  }
  .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
  }
  .dialog-header {
    border-bottom: 1px solid #ddd;
  }
}
</style>