<template>
  <el-menu
    :default-active="activeMenu"
    mode="horizontal"
    @select="handleSelect"
    :ellipsis="false"
  >
    <template v-for="(item, index) in topMenus">
      <el-menu-item
        :style="{ '--theme': theme }"
        :index="item.path"
        :key="index"
        v-if="index < visibleNumber"
        @click="menuItem(item)"
        ><svg-icon
          v-if="
            item.meta &&
            item.meta.icon &&
            item.meta.icon !== '#' &&
            item.meta.title != 'gg'
          "
          :icon-class="item.meta.icon"
        />
        {{ item.meta.title }}</el-menu-item
      >
    </template>

    <!-- 顶部菜单超出数量折叠 -->
    <el-sub-menu
      :style="{ '--theme': theme }"
      index="more"
      v-if="topMenus.length > visibleNumber"
      popper-class="subMenu-grid"
    >
      <template #title
        ><svg-icon
          icon-class="more"
          style="margin-right: 5px"
        />更多</template
      >
      <template v-for="(item, index) in topMenus">
        <el-menu-item
          :index="item.path"
          :key="index"
          v-if="index >= visibleNumber"
          ><svg-icon :icon-class="item.meta.icon" style="margin-right: 5px" />
          {{ item.meta.title }}</el-menu-item
        >
      </template>
    </el-sub-menu>
  </el-menu>
</template>

<script setup>
import { constantRoutes } from "@/router";
import { isHttp } from "@/utils/validate";
import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";
import usePermissionStore from "@/store/modules/permission";
import useUserStore from "@/store/modules/user";
import { addSchoolUserUseFunction } from "@/api/approval";
import { debounce } from "@/utils/debounce";
const userStore = useUserStore();

const props = defineProps({
  showParkSelect: {
    type: Boolean,
    default: false,
  },
});
// 顶部栏初始数
const visibleNumber = ref(null);
// 当前激活菜单的 index
const currentIndex = ref(null);
// 隐藏侧边栏路由
const hideList = ["/index", "/user/profile"];

const appStore = useAppStore();
const settingsStore = useSettingsStore();
const permissionStore = usePermissionStore();
const route = useRoute();
const router = useRouter();

// 主题颜色
const theme = computed(() => settingsStore.theme);
// 所有的路由信息
const routers = computed(() => permissionStore.topbarRouters);
// 顶部显示菜单
const topMenus = computed(() => {
  let topMenus = [];
  routers.value.map((menu) => {
    if (menu.hidden !== true) {
      // 兼容顶部栏一级菜单内部跳转
      if (menu.path === "/") {
        topMenus.push(menu.children[0]);
      } else {
        topMenus.push(menu);
      }
    }
    // console.log(menu, "menu");
    // console.log(userStore.roleName, menu.children[0].name);
    // console.log(userStore.roleName.includes("服务台人员"));

    // if (!userStore.roleName.includes("服务台人员")) {
    //   // if (menu.name != "TaskManage") {
    //   if (menu.hidden !== true) {
    //     // 兼容顶部栏一级菜单内部跳转
    //     if (menu.path === "/") {
    //       topMenus.push(menu.children[0]);
    //     } else {
    //       topMenus.push(menu);
    //     }
    //   }
    // } else {
    //   if (menu.children[0].name != "Work") {
    //     if (menu.hidden !== true) {
    //       // 兼容顶部栏一级菜单内部跳转
    //       if (menu.path === "/") {
    //         topMenus.push(menu.children[0]);
    //       } else {
    //         topMenus.push(menu);
    //       }
    //     }
    //   }
    // }
  });
  return topMenus;
});

// 点击记录菜单选择
const menuItem = debounce((item) => {
  console.log(item);

  if (item.menuId) {
    let obj = {
      userId: userStore.userId,
      menuId: item.menuId,
      num: 1,
    };
    addSchoolUserUseFunction(obj)
      .then((res) => {
        console.log(res);
      })
      .catch((err) => {
        console.log("err");
      });
  }
}, 200);

// 设置子路由
const childrenMenus = computed(() => {
  let childrenMenus = [];
  routers.value.map((router) => {
    for (let item in router.children) {
      if (router.children[item].parentPath === undefined) {
        if (router.path === "/") {
          router.children[item].path = "/" + router.children[item].path;
        } else {
          if (!isHttp(router.children[item].path)) {
            router.children[item].path =
              router.path + "/" + router.children[item].path;
          }
        }
        router.children[item].parentPath = router.path;
      }
      childrenMenus.push(router.children[item]);
    }
  });

  return constantRoutes.concat(childrenMenus);
});

// 默认激活的菜单
const activeMenu = computed(() => {
  const path = route.path;
  let activePath = path;
  if (
    path !== undefined &&
    path.lastIndexOf("/") > 0 &&
    hideList.indexOf(path) === -1
  ) {
    const tmpPath = path.substring(1, path.length);
    activePath = "/" + tmpPath.substring(0, tmpPath.indexOf("/"));
    if (!route.meta.link) {
      appStore.toggleSideBarHide(false);
    }
  } else if (!route.children) {
    activePath = path;
    appStore.toggleSideBarHide(true);
  }
  activeRoutes(activePath);
  return activePath;
});

function setVisibleNumber() {
  const width = document.body.getBoundingClientRect().width / 2;
  visibleNumber.value = parseInt(width / (props.showParkSelect ? 130 : 100));
}

function handleSelect(key, keyPath) {
  currentIndex.value = key;
  const route = routers.value.find((item) => item.path === key);
  if (isHttp(key)) {
    // http(s):// 路径新窗口打开
    window.open(key, "_blank");
  } else if (!route || !route.children) {
    // 没有子路由路径则内部打开
    console.log("没有子路由");

    const routeMenu = childrenMenus.value.find((item) => item.path === key);
    if (routeMenu && routeMenu.query) {
      let query = JSON.parse(routeMenu.query);
      router.push({ path: key, query: query });
    } else {
      router.push({ path: key });
    }
    appStore.toggleSideBarHide(true);
  } else {
    // 显示左侧联动菜单
    let routes = activeRoutes(key);
    // console.log(routes)
    console.log("1111");

    router.push({
      path: routes[0].children
        ? `${routes[0].path}/${routes[0].children[0].path}`
        : routes[0].path,
    });
    appStore.sidebar.opened = true;
    appStore.toggleSideBarHide(false);
  }
}

function activeRoutes(key) {
  let routes = [];
  if (childrenMenus.value && childrenMenus.value.length > 0) {
    childrenMenus.value.map((item) => {
      if (key == item.parentPath || (key == "index" && "" == item.path)) {
        routes.push(item);
      }
    });
  }
  if (routes.length > 0) {
    permissionStore.setSidebarRouters(routes);
  } else {
    appStore.toggleSideBarHide(true);
  }
  return routes;
}

onMounted(() => {
  window.addEventListener("resize", setVisibleNumber);
});
onBeforeUnmount(() => {
  window.removeEventListener("resize", setVisibleNumber);
});

onMounted(() => {
  setVisibleNumber();
});
</script>

<style lang="scss">
@import "@/assets/styles/variables.module.scss";

.subMenu-grid .el-menu.el-menu--popup.el-menu--popup-bottom-start {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
}

.topmenu-container.el-menu--horizontal > .el-menu-item {
  float: left;
  font-size: 14px;
  height: 55px !important;
  line-height: 55px !important;
  color: #fff !important;
  padding: 0 10px !important;
  margin: 0 4px !important;
}

.el-menu--horizontal.el-menu {
  border-bottom: none !important;
}

.topmenu-container.el-menu--horizontal > .el-menu-item.is-active,
.el-menu--horizontal > .el-sub-menu.is-active .el-submenu__title {
  // border-bottom: 2px solid #{'var(--theme)'} !important;
  border-bottom: 5px solid #cde3fa !important;
  color: #fff !important;
  box-sizing: border-box;
  background-color: $base-top-menu-color !important;
  // background-color: rgba(255, 255, 255, .2);
  &:hover {
    background-color: rgba(255, 255, 255, 0.2) !important;
    // background-color: transparent !important;
  }
}

/* sub-menu item */
.topmenu-container.el-menu--horizontal > .el-sub-menu .el-sub-menu__title {
  float: left;
  height: 55px !important;
  line-height: 55px !important;
  color: #ffffff !important;
  padding: 0 5px !important;
  margin: 0 10px 0 0 !important;
}

/* 背景色隐藏 */
.topmenu-container.el-menu--horizontal > .el-menu-item:not(.is-disabled):focus,
.topmenu-container.el-menu--horizontal > .el-menu-item:not(.is-disabled):hover,
.topmenu-container.el-menu--horizontal > .el-submenu .el-submenu__title:hover {
  // background-color: $base-top-menu-color !important;
  background-color: rgba(255, 255, 255, 0.2);
  // background-color: transparent;
}

.el-sub-menu__title:hover {
  background-color: transparent !important;
}

/* 图标右间距 */
.topmenu-container .svg-icon {
  margin-right: 4px;
}

/* topmenu more arrow */
.topmenu-container .el-sub-menu .el-sub-menu__icon-arrow {
  position: static;
  vertical-align: middle;
  margin-left: 8px;
  margin-top: 0px;
}
</style>
