import request from "@/utils/request";
//--------------------------------- 管理端接口-------------------------------------

// 根据运行状态获取设备数量
export function getDeviceNum(data) {
  return request({
    url: "/schoolCorpDevice/statistics",
    method: "post",
    data,
    headers: {
      isControl: true,
    },
  });
}

export function findNum(data) {
  return request({
    url: "/system/device/findNum",
    method: "post",
    data,
  });
}

// 获取对应机构部门人数
export function getAdminDept(params) {
  return request({
    url: "/deptMini/listTree",
    method: "get",
    headers: {
      isControl: true,
    },
    params,
  });
}

// 结果导出
export function smartOperationResult(data) {
  return request({
    url: "/system/school/operation/result",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 数据智能分析(推送汇总)
export function smartOperationStatistics(data) {
  return request({
    url: "/system/school/smart/operation/statistics",
    method: "post",
    data,
  });
}

// 字段类型结果导出(推送汇总)
export function smartFieldStatistics(data) {
  return request({
    url: "/system/school/smart/field/statistics",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 获取有权限的机构列表
export function getAdminCorp() {
  return request({
    url: "/system/school/admin/find/corp",
    method: "get",
  });
}

// 所有机构位置信息
export function getAdminCorpGps(params) {
  return request({
    url: "/system/school/admin/find/gps",
    method: "get",
    params,
  });
}

// 平均处理时间峰值年月筛选统计
export function getAdminWorkOrderTimePeak(params) {
  return request({
    url: "/system/school/admin/workOrder/peak/time",
    method: "get",
    params,
  });
}

// 平均处理时间年月周筛选统计
export function getAdminWorkOrderTime(params) {
  return request({
    url: "/system/school/admin/workOrder/time",
    method: "get",
    params,
  });
}

// 知识库年月周筛选统计 /system/school/knowledgeBase/time
export function getAdminKnowledgeStatistics(params) {
  return request({
    url: "/system/school/admin/knowledgeBase/time",
    method: "get",
    params,
  });
}
// 工单处理年月周筛选统计
export function getAdminWorkOrderTimeStatistics(params) {
  return request({
    url: "/system/school/admin/workOrder/processingTime",
    method: "get",
    params,
  });
}

// 查询运维考勤打卡数据
export function getAdminMaintainCountStatistics(params) {
  return request({
    url: "/system/school/admin/maintainCount/statistics",
    method: "get",
    params,
  });
}

// 获取运维大屏数据
export function getAdminSchoolStatistics() {
  return request({
    url: "/system/school/admin/statistics",
    method: "get",
  });
}

// 工单统计
export function getAdminWorkOrderStatistics(params) {
  return request({
    url: "/system/school/admin/workOrder/statistics",
    method: "get",
    params,
  });
}

// 资产状况统计
export function getAdminAssetsStatistics() {
  return request({
    url: "/system/school/admin/assetsStatus/statistics",
    method: "get",
  });
}

//--------------------------------- 应用端接口-------------------------------------

// 网络信息
export function getNetworkStatistics() {
  return request({
    url: "/system/school/network/statistics",
    method: "get",
  });
}

// 部门资产导出
export function deptAssetsExport() {
  return request({
    url: `/system/school/export/dept/assets`, // 实际导出接口地址
    method: "post",
    responseType: "blob", // 必须设置响应类型为 blob
  });
}

// 资产生命周期状态分布
export function getAssetsLifecycle(params) {
  return request({
    url: "/system/school/asset/lifecycle/statistics",
    method: "get",
    params,
  });
}

// 资产增长趋势
export function getAssetsTrend(params) {
  return request({
    url: "/system/school/asset/increase/trend",
    method: "get",
    params,
  });
}

// 平均处理时间年月周筛选统计
export function getWorkOrderTime(params) {
  return request({
    url: "/system/school/workOrder/time",
    method: "get",
    params,
  });
}

// 考勤偏差值年月周筛选统计
export function getClockOffsetStatistics(params) {
  return request({
    url: "/system/school/clockOffset/statistics",
    method: "get",
    params,
  });
}

// 知识库年月周筛选统计 /system/school/knowledgeBase/time
export function getKnowledgeStatistics(params) {
  return request({
    url: "/system/school/knowledgeBase/time",
    method: "get",
    params,
  });
}

// 工单处理年月周筛选统计 /system/school/maintainCount/statistics
export function getWorkOrderTimeStatistics(params) {
  return request({
    url: "/system/school/workOrder/processingTime",
    method: "get",
    params,
  });
}

// 查询运维考勤打卡数据 /system/school/maintainCount/statistics
export function getMaintainCountStatistics(params) {
  return request({
    url: "/system/school/maintainCount/statistics",
    method: "get",
    params,
  });
}

// 获取运维大屏数据 /system/school/statistics
export function getSchoolStatistics() {
  return request({
    url: "/system/school/statistics",
    method: "get",
  });
}

// 工单统计 /system/school/workOrder/statistics
export function getWorkOrderStatistics(params) {
  return request({
    url: "/system/school/workOrder/statistics",
    method: "get",
    params,
  });
}

// 资产状况统计 /system/school/workOrder/statistics
export function getAssetsStatistics() {
  return request({
    url: "/system/school/assetsStatus/statistics",
    method: "get",
  });
}
