<template>
  <div class="lockScreen" v-loading="loading">
    <div class="lockScreen-list">
      <div class="lockScreen-item">
        <div>
          <div class="lockScreen-header">
            <span>开机锁屏</span>
            <div class="flex">
              <el-tooltip
                class="box-item"
                effect="dark"
                content="在下述生效时段内，生效设备开机后自动进入锁屏状态"
                placement="top-start"
              >
                <el-icon :size="16" style="margin-left: 5px"
                  ><QuestionFilled
                /></el-icon>
              </el-tooltip>
              <el-tooltip content="修改" placement="top">
                <el-button
                  v-show="!form.isEdit"
                  type="primary"
                  style="font-size: 20px; margin-left: 10px"
                  icon="Edit"
                  link
                  @click="handleEdit()"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
          <div
            v-show="(!form.id && form.isEdit) || !!form.id"
            class="lockScreen-content"
            :class="{ 'lockScreen-content_unedit': !form.isEdit }"
          >
            <div>
              <el-form
                ref="formRef"
                :model="form"
                :label-width="form.isEdit ? '92px' : '82px'"
              >
                <el-form-item
                  :label="form.isEdit ? '启动计划：' : '启用状态：'"
                  prop="isFlag"
                  label-width="82px"
                >
                  <el-switch
                    v-if="form.isEdit"
                    v-model="form.isFlag"
                    :active-value="true"
                    :inactive-value="false"
                    @change="handleStatusChange"
                  />
                  <span v-else>{{ form.isFlag ? "已启用" : "已禁用" }}</span>
                </el-form-item>
                <el-form-item
                  label="生效时段："
                  prop="effectiveTimeRange"
                  :rules="{
                    required: form.isEdit,
                    validator: validEffectiveTimeRange,
                    trigger: 'change',
                  }"
                >
                  <el-time-picker
                    v-if="form.isEdit"
                    v-model="form.effectiveTimeRange"
                    is-range
                    range-separator="~"
                    start-placeholder="开始时间"
                    end-placeholder="结束时间"
                    format="HH:mm:ss"
                    value-format="HH:mm:ss"
                    @change="changeTimeList"
                    :disabled="!!form.id && !form.isFlag"
                  />
                  <span v-else>{{
                    form.effectiveTimeRange?.join("~") || "-"
                  }}</span>
                </el-form-item>

                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: form.isEdit,
                    validator: validSelectedDevices,
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="form.selectedDevices"
                    :isEdit="form.isEdit"
                    :device-codes="form.deviceCodes"
                    :disabled="!!form.id && form.isFlag === false"
                    @valid="handleValid"
                    @change="handleChangeSelectedDevices"
                  />
                </el-form-item>
              </el-form>
              <div class="lockScreen-footer" v-if="form.isEdit">
                <el-button type="primary" @click="submitForm()" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel()">取消</el-button>
              </div>
            </div>
            <div v-show="!!form.id && !form.isEdit">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  @click="handleDel()"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="lockScreen-list" style="margin-top: 15px">
      <div class="lockScreen-item">
        <div>
          <div class="lockScreen-header">
            <span>锁定屏保设置</span>
            <el-tooltip
              class="box-item"
              effect="dark"
              content="生效设备开机后按设定规则开启锁定屏保"
              placement="top-start"
            >
              <el-icon :size="16" style="margin-left: 5px"
                ><QuestionFilled
              /></el-icon>
            </el-tooltip>
            <el-tooltip content="修改" placement="top">
              <el-button
                type="primary"
                style="font-size: 20px; margin-left: 10px"
                icon="Edit"
                link
                v-show="!form2.isEdit"
                @click="handleEdit(1)"
              ></el-button>
            </el-tooltip>
          </div>
          <div
            class="lockScreen-content"
            :class="{ 'lockScreen-content_unedit': !form2.isEdit }"
          >
            <div>
              <el-form
                ref="formRef2"
                :model="form2"
                :label-width="form2.isEdit ? '120px' : '110px'"
              >
                <el-form-item
                  label="锁定屏保图片："
                  prop="lockImg"
                  :rules="{
                    required: form2.isEdit,
                    validator: validLockImg,
                    trigger: 'change',
                  }"
                >
                  <el-radio-group
                    v-if="form2.isEdit"
                    v-model="form2.lockImg"
                    @change="changeLockImg"
                  >
                    <el-radio label="默认" :value="0" />
                    <el-radio label="自定义" :value="1" />
                  </el-radio-group>
                  <div v-else>
                    <img
                      v-if="form2.lockImg === 0"
                      class="el-upload-list__item-thumbnail"
                      :src="defaultScreenImg"
                      alt=""
                      style="
                        width: 148px;
                        height: 148px;
                        object-fit: contain;
                        border: 1px solid #dcdfe6;
                        cursor: pointer;
                        border-radius: 6px;
                      "
                      @click="handlePictureCardPreview(defaultScreenImg)"
                    />
                    <img
                      v-else
                      class="el-upload-list__item-thumbnail"
                      :src="form2.lockUrl"
                      alt=""
                      style="
                        width: 148px;
                        height: 148px;
                        object-fit: contain;
                        border: 1px solid #dcdfe6;
                        cursor: pointer;
                        border-radius: 6px;
                      "
                      @click="handlePictureCardPreview()"
                    />
                  </div>
                </el-form-item>

                <el-form-item v-show="form2.isEdit">
                  <img
                    v-show="form2.lockImg === 0"
                    class="el-upload-list__item-thumbnail"
                    :src="defaultScreenImg"
                    alt=""
                    style="
                      width: 148px;
                      height: 148px;
                      object-fit: contain;
                      border: 1px solid #dcdfe6;
                      cursor: pointer;
                      border-radius: 6px;
                    "
                    @click="handlePictureCardPreview(defaultScreenImg)"
                  />
                  <div
                    v-show="form2.lockImg === 1"
                    class="upload-item"
                    style="display: flex; align-items: center; gap: 0 10px"
                  >
                    <img
                      v-if="form2.lockUrl && !isImgChange"
                      class="el-upload-list__item-thumbnail"
                      :src="form2.lockUrl"
                      alt=""
                      style="
                        width: 148px;
                        height: 148px;
                        object-fit: contain;
                        border: 1px solid #dcdfe6;
                        cursor: pointer;
                        border-radius: 6px;
                      "
                      @click="handlePictureCardPreview()"
                    />
                    <el-upload
                      class="uploadImg"
                      ref="uploadImgRef"
                      :headers="headers"
                      action="#"
                      list-type="picture-card"
                      :limit="1"
                      :auto-upload="false"
                      :on-change="handleImgChange"
                      :on-exceed="handleImgExceed"
                      :http-request="httpRequestImgFn"
                    >
                      <el-button type="primary"
                        >{{ form2.lockUrl ? "更换" : "选择" }}图片</el-button
                      >
                      <template #file="{ file }">
                        <div>
                          <img
                            class="el-upload-list__item-thumbnail"
                            :src="file.url || form2.lockUrl"
                            alt=""
                          />
                          <span class="el-upload-list__item-actions">
                            <span
                              class="el-upload-list__item-preview"
                              @click="handlePictureCardPreview(file)"
                            >
                              <el-icon><zoom-in /></el-icon>
                            </span>
                          </span>
                        </div>
                      </template>
                      <template #tip>
                        <div class="el-upload__tip">
                          建议更换 3840 * 2160 的图片，大小不超过5MB
                        </div>
                      </template>
                    </el-upload>
                  </div>
                </el-form-item>
              </el-form>
              <div class="lockScreen-footer" v-if="form2.isEdit">
                <el-button type="primary" @click="submitForm(1)" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(1)">取消</el-button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog v-model="dialogVisibleImg" width="900">
      <img
        :src="dialogImageUrl"
        alt="Preview Image"
        style="
          display: block;
          max-width: 100%;
          margin: 0 auto;
          object-fit: contain;
          height: 600px;
        "
      />
    </el-dialog>
  </div>
</template>

<script setup>
import deviceSel from "../deviceSel.vue";
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import {
  deleteLockScreenRule,
  getLockScreenRule,
  lockScreenRuleBatch,
  downScreenImg,
} from "@/api/deviceControl/plan";
import { deleteScreenImg } from "@/api/deviceControl";
import { timeFormat } from "@/utils";
import { getToken } from "@/utils/auth";
import { emit, nextTick } from "process";
import { ElMessage, genFileId } from "element-plus";

const { proxy } = getCurrentInstance();

const formRef = ref(null);
const formRef2 = ref(null);
const uploadImgRef = ref(null);
const actionUrlImg = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/schoolSoftware/uploadScreenImg"
);
const dialogImageUrl = ref("");
const dialogVisibleImg = ref(false);
const headers = ref({
  Authorization: "Bearer " + getToken(),
  "Content-Type": "multipart/form-data",
});
const defaultScreenImg = ref(
  "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/screenImage.png"
);
const isImgChange = ref(false);
const hasScreenImg = ref(false);

const state = reactive({
  loading: false,
  loading2: false,
  initForm: {},
  form: {
    // isEdit: null,
    // id: "",
    // type: 2,
    // isFlag: false,
    // effectiveTimeRange: [],
    // selectedDevices: "",
  },
  form2: {
    isEdit: false,
    id: "",
    isOpen: false,
    lockImg: 0,
    lockUrl: "",
    selectedDevices: "",
    deviceCodes: [],
  },
});
const { loading, loading2, form, form2, initForm } = toRefs(state);

onMounted(() => {
  getData();
});

const handleValid = () => {
  formRef.value.validateField(`selectedDevices`);
};

const handleChangeSelectedDevices = (val) => {
  state.form.selectedDevices = val?.join(",");
};

const getData = async () => {
  try {
    loading.value = true;
    const [ruleRes, imgRes] = await Promise.all([
      getLockScreenRule({ type: 2 }),
      downScreenImg(),
    ]);
    console.log(ruleRes, imgRes, "屏保信息");
    uploadImgRef.value?.clearFiles();
    isImgChange.value = false;
    hasScreenImg.value = false;
    resetForm();
    resetForm(1);
    if (ruleRes.code === 200 && ruleRes.data) {
      const {
        timeRangeList,
        deviceCodes,
        id,
        isFlag,
        isOpen: isOpen1,
      } = ruleRes.data;
      Object.assign(state.form, {
        isEdit: false,
        isFlag,
        id,
        deviceCodes,
        selectedDevices: deviceCodes?.join(",") || "",
        effectiveTimeRange:
          timeRangeList && timeRangeList[0]
            ? [timeRangeList[0].startTime, timeRangeList[0].endTime]
            : ["00:00:00", "23:59:59"],
      });

      state.initForm = JSON.parse(JSON.stringify(state.form));

      if ((imgRes.code == 200 && imgRes.data) || isOpen1) {
        const { img, isOpen: isOpen2 } = imgRes.data || {};
        Object.assign(state.form2, {
          id: " 1",
          isOpen: isOpen1 || isOpen2,
        });
        const imageUrl = img ? `data:image/png;base64,${img}` : "";
        if (imageUrl) {
          form2.value.lockImg = 1;
          form2.value.lockUrl = imageUrl;
          hasScreenImg.value = true;
        } else {
          form2.value.lockImg = 0;
          form2.value.lockUrl = "";
          hasScreenImg.value = false;
        }
      }
    }
  } catch (error) {
    console.error("获取锁屏计划列表失败:", error);
    proxy.$modal.msgError("获取数据失败");
  } finally {
    loading.value = false;
  }
};

const handleImgChange = (file) => {
  console.log(file);

  let fileName = file.name;
  let index = fileName.lastIndexOf(".");
  let fileType = fileName.substring(index + 1);
  let whiteName = ["jpg", "jpeg", "png"];
  if (file.status == "ready") {
    if (whiteName.indexOf(fileType) == -1) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning(`仅支持${whiteName.join("/")}格式的图片`);
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning("图片大小不能超过5MB");
      return;
    }

    isImgChange.value = true;
    form2.value.lockUrl = file.url;
  }
};

const handleImgExceed = (files) => {
  uploadImgRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadImgRef.value.handleStart(file);
  form2.value.lockUrl = file.name;
};

// 自定义上传屏保
const httpRequestImgFn = (options) => {
  console.log(options, "自定义上传屏保");

  const formData = new FormData();
  formData.append("file", options.file);

  fetch(actionUrlImg.value, {
    method: "POST",
    body: formData,
    headers: { Authorization: "Bearer " + getToken() },
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      }
    })
    .then((res) => {
      console.log(res);
      if (res && res.data && res.code == 200) {
        submitRule();
      } else {
        proxy.$modal.msgError("操作失败");
        handleCancel(1);
      }
    });
};

// 时间字符串转分钟数
const timeToMinutes = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 60 + minutes;
};

// 时间字符串转秒数
const timeToSeconds = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes, seconds] = timeStr.split(":").map(Number);
  return hours * 60 * 60 + minutes * 60 + seconds;
};

// 校验锁屏图片
const validLockImg = (rule, value, callback) => {
  if (state.form2.lockImg === 1 && !value) {
    callback(new Error("请选择图片"));
  } else {
    callback();
  }
};

// 校验生效时间
const validEffectiveTimeRange = (rule, value, callback) => {
  // 如果状态为禁用，跳过验证
  if (form.value.isFlag === false && !!form.value.id) {
    callback();
    return;
  }

  if (!value || value.length !== 2) {
    callback(new Error("请选择生效时段"));
    return;
  }

  const [start, end] = value;
  const startMin = timeToSeconds(start);
  const endMin = timeToSeconds(end);

  if (startMin >= endMin) {
    callback(new Error("开始时间必须小于结束时间"));
  } else {
    callback();
  }
};

// 校验生效设备
const validSelectedDevices = (rule, value, callback) => {
  // 如果状态为禁用，跳过验证
  if (form.value.isFlag === false && !!form.value.id) {
    callback();
    return;
  }
  if (!value) {
    callback(new Error("请选择生效设备"));
  } else {
    callback();
  }
};

function changeTimeList() {
  if (formRef.value) {
    formRef.value.validateField(`effectiveTimeRange`);
  }
}

const handleStatusChange = (val, type) => {
  let obj = state[!!type ? "form2" : "form"];
  obj.isEdit = true;
  if (!val && !!obj.id)
    !!type ? formRef2.value?.clearValidate() : formRef.value?.clearValidate();
  if (!val) formRef.value?.clearValidate();
};

const changeLockImg = () => {
  console.log(
    hasScreenImg.value,
    "hasScreenImg",
    form2.value,
    isImgChange.value,
    "isImgChange"
  );
};

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file?.url || form2.value.lockUrl || file;
  dialogVisibleImg.value = true;
};

const handleEdit = (type) => {
  state[!!type ? "form2" : "form"].isEdit = true;
  if (!type) {
    if (!state.form.id) state.form.isFlag = true;
    nextTick(() => {
      formRef.value?.clearValidate();
    });
  }
};

const handleDel = (type) => {
  proxy.$modal
    .confirm("删除后已设置的设备重启后失效，是否确认删除？")
    .then((res) => {
      let submitData = {
        type: 2,
        id: state.form.id,
      };
      console.log("提交数据:", submitData);
      // 调用接口
      loading.value = true;
      deleteLockScreenRule(submitData)
        .then((res) => {
          console.log(res);
          if (res.code === 200) {
            resetForm();
            proxy.$modal.msgSuccess("操作成功");
          }
        })
        .catch((err) => {
          // proxy.$modal.msgError("操作失败");
        })
        .finally(() => (loading.value = false));
    });
};

const handleCancel = (type) => {
  // if (!!type ? formRef2.value : formRef.value) {
  //   !!type ? formRef2.value.resetFields() : formRef.value.resetFields();
  // }
  // if (!state[!!type ? "form2" : "form"].id) {
  //   resetForm(type);
  // }

  getData();
};

const submitRule = (obj = null) => {
  let submitData = obj || {
    isOpen: state.form2.lockImg === 1,
    type: 4,
  };
  console.log("提交数据:", submitData, JSON.stringify(submitData));
  loading.value = true;
  lockScreenRuleBatch(submitData)
    .then((res) => {
      console.log("修改成功", res);
      getData();
      proxy.$modal.msgSuccess("保存成功");
    })
    .finally(() => (loading.value = false));
};

const submitForm = (type) => {
  if (!!type) {
    const submitScreen = () => {
      if (form2.value.lockImg === 0) {
        deleteScreenImg().then((res) => {
          submitRule();
        });
      } else {
        if (!!form2.value.lockUrl) {
          if (isImgChange.value) {
            proxy.$refs.uploadImgRef.submit();
          } else {
            submitRule();
          }
        } else {
          proxy.$modal.msgWarning("请选择锁屏屏保图片");
        }
      }
    };
    if (form2.value.lockImg === 1) {
      formRef2.value.validate((valid) => {
        if (valid) {
          submitScreen();
        }
      });
    } else {
      submitScreen();
    }
  } else {
    if (state.form.isFlag === false && !state.form.id) {
      proxy.$modal.alert("请先开启启动计划再保存");
      return;
    }
    formRef.value?.validate((valid) => {
      if (valid) {
        const { effectiveTimeRange, selectedDevices } =
          state[!form.value.isFlag && !!form.value.id ? "initForm" : "form"];
        let obj = {
          ...state.form,
          type: 2,
          timeList: [
            {
              startTime: effectiveTimeRange[0],
              endTime: effectiveTimeRange[1],
            },
          ],
          deviceCodes: selectedDevices?.split(",").filter(Boolean) || [],
        };
        console.log("修改传参", obj);
        submitRule(obj);
      }
    });
  }
};

const resetForm = (type) => {
  if (!!type) {
    state.form2 = {
      isEdit: false,
      id: "",
      isOpen: false,
      lockImg: 0,
      lockUrl: "",
      selectedDevices: "",
      deviceCodes: [],
    };
  } else {
    state.form = {
      isEdit: null,
      id: "",
      type: 2,
      isFlag: false,
      effectiveTimeRange: ["00:00:00", "23:59:59"],
      selectedDevices: "",
      deviceCodes: [],
    };
  }
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  gap: 0 5px;
}
.uploadImg {
  :deep(.el-upload__tip) {
    margin-top: 0;
  }
  :deep(.el-upload--picture-card) {
    width: 90px !important;
    height: 35px;
    border: none;
    background: none;
  }
  :deep(.el-upload-list--picture-card) {
    align-items: center;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    margin-bottom: 0;
    justify-content: center;
  }
}
.lockScreen {
  font-size: 14px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 15px 0;
  }

  &-add {
    border: 1px solid #e5e5e5;
    border-top: none;
    padding: 10px;
    color: rgb(152, 152, 152);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    &:hover {
      color: #7e96ae;
      background-color: #f8faff;
    }
  }

  &-item {
    border: 1px solid #e5e5e5;
  }

  &-content {
    padding: 10px 20px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_unedit {
      padding-bottom: 10px;
      :deep(.el-form-item--default) {
        margin-bottom: 0px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    font-size: 16px;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
  }

  &-footer {
    min-width: 500px;
    text-align: center;
  }
}
</style>