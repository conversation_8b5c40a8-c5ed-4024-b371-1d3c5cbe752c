// 检测浏览器是否支持 crypto.subtle.digest
const supportsCrypto = window.crypto && crypto.subtle && crypto.subtle.digest;

export function checkSupportsCrypto() {
  // 如果不支持 crypto.subtle，加载 js-sha256 库
  if (!supportsCrypto) {
    const script = document.createElement("script");
    script.src =
      "https://cdn.jsdelivr.net/npm/js-sha256@0.9.0/src/sha256.min.js";
    script.onload = () => {
      console.log("js-sha256 已加载");
    };
    document.head.appendChild(script);
  }
}

/**
 * 生成文件的唯一标识（requestId）
 * @param {File} file - 文件对象
 * @param {number} chunkSize - 采样大小（默认1MB）
 * @param {function} callback - 回调函数，接收生成的 requestId
 */
export function generateRequestId(file, chunkSize, callback) {
  // 1. 文件大小和文件名
  const fileSize = file.size;
  const fileName = file.name;

  // 对于空文件的特殊处理
  if (fileSize === 0) {
    const combinedData = `${fileName}-0-empty`;
    calculateHash(new TextEncoder().encode(combinedData), function (finalHash) {
      const requestId = finalHash.substring(0, 12);
      callback(requestId);
    });
    return;
  }

  // 2. 读取文件头尾数据
  readFileSlice(file, 0, chunkSize, function (head) {
    readFileSlice(file, fileSize - chunkSize, fileSize, function (tail) {
      const combined = new Uint8Array([...head, ...tail]); // 合并头尾数据

      // 3. 计算文件头尾采样数据的哈希值
      calculateHash(combined, function (headTailHash) {
        // 4. 将文件名、文件大小和头尾哈希值拼接
        const combinedData = `${fileName}-${fileSize}-${headTailHash}`;

        // 5. 计算最终哈希值
        calculateHash(
          new TextEncoder().encode(combinedData),
          function (finalHash) {
            // 6. 截取前 12 位哈希值作为 requestId
            const requestId = finalHash.substring(0, 12);
            callback(requestId);
          }
        );
      });
    });
  });
}

// 读取文件的部分数据（兼容旧版浏览器）
function readFileSlice(file, start, end, callback) {
  const reader = new FileReader();
  reader.onload = function () {
    callback(new Uint8Array(reader.result));
  };
  reader.onerror = function () {
    console.error("文件读取失败");
  };
  const slice = file.slice(start, end);
  reader.readAsArrayBuffer(slice);
}

// 计算哈希值
function calculateHash(data, callback) {
  if (supportsCrypto) {
    // 使用 crypto.subtle.digest
    crypto.subtle.digest("SHA-256", data).then(function (hashBuffer) {
      const hashHex = Array.from(new Uint8Array(hashBuffer))
        .map((byte) => byte.toString(16).padStart(2, "0"))
        .join("");
      callback(hashHex);
    });
  } else {
    // 使用 js-sha256
    const hashHex = sha256(data);
    callback(hashHex);
  }
}

// 获取当前时间并格式化为字符串
export function getCurrentTime() {
  const now = new Date();
  return now.toLocaleString(); // 格式化为本地时间字符串
}
