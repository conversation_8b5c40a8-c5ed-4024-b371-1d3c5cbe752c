<template>
  <div class="navbar">
    <!-- <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container"
      @toggleClick="toggleSideBar" /> -->
    <div style="display: flex; gap: 0 20px">
      <div
        class="navbar-logo"
        style="padding-left: 10px"
        v-if="useUserStore().isTeacher"
      >
        课件上传
      </div>
      <div class="navbar-logo" v-else>
        <img
          src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/nav-logo.png"
        />
        <div style="white-space: nowrap">
          {{ title }}
          <div class="subTit">{{ subTitle }}</div>
        </div>
      </div>

      <breadcrumb
        id="breadcrumb-container"
        class="breadcrumb-container"
        v-if="!settingsStore.topNav"
      />
      <top-nav
        id="topmenu-container"
        class="topmenu-container"
        v-if="settingsStore.topNav"
        :key="showParkSelect"
        :showParkSelect="showParkSelect"
      />
    </div>

    <!-- 添加园区选择框，仅在特定路由下显示 -->
    <div class="park-select-container" v-if="showParkSelect">
      <el-select
        v-model="selectedPark"
        placeholder="请筛选园区"
        size="small"
        clearable
      >
        <el-option
          v-for="item in parkList"
          :key="item.value"
          :label="item.label"
          :value="item.value"
        />
      </el-select>
    </div>

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">
        <!-- <header-search id="header-search" class="right-menu-item" /> -->

        <!-- <el-tooltip content="源码地址" effect="dark" placement="bottom">
          <ruo-yi-git id="ruoyi-git" class="right-menu-item hover-effect" />
        </el-tooltip>

        <el-tooltip content="文档地址" effect="dark" placement="bottom">
          <ruo-yi-doc id="ruoyi-doc" class="right-menu-item hover-effect" />
        </el-tooltip> -->

        <el-tooltip
          content="点击下载安装包"
          effect="dark"
          placement="bottom"
          v-if="!userStore.isTeacher"
        >
          <div
            class="right-menu-item hover-effect"
            style="display: flex; align-items: center; height: 100%"
            @click="handleDownload"
          >
            <el-icon color="#fff" size="25"><UploadFilled /></el-icon>
          </div>
        </el-tooltip>
        <screenfull id="screenfull" class="right-menu-item hover-effect" />
        <div
          class="msg-container right-menu-item hover-effect"
          v-if="!userStore.isTeacher"
        >
          <el-popover placement="bottom" :width="300" trigger="click">
            <template #reference>
              <el-badge
                :value="msgCount"
                :show-zero="false"
                :badge-style="{
                  fontSize: '10px',
                  height: '13px',
                  backgroundColor: '#F6BCB8',
                  color: '#FF3B30',
                  fontWeight: 'bold',
                  padding: '0 5px',
                  border: '1px solid #F6BCB8',
                }"
                :offset="[0, 2]"
              >
                <img
                  src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/msg.png"
                  class="msg-avatar"
                />
              </el-badge>
            </template>
            <template #default>
              <div v-if="msgList.length > 0">
                <div class="msg-list" style="margin-bottom: 12px">
                  <el-scrollbar max-height="250px">
                    <div
                      class="msg-list_item"
                      v-for="(item, index) in msgList"
                      :key="index"
                      @click="handleMsg(item)"
                      style="
                        cursor: pointer;
                        padding: 6px 0px;
                        border-bottom: 0.5px solid #e4e4e4;
                      "
                    >
                      <div class="msg-list_item-title">{{ item.msgTitle }}</div>
                      <div
                        class="msg-list_item-tite"
                        style="
                          color: #727272;
                          text-align: right;
                          margin-top: 5px;
                        "
                      >
                        {{ item.createTime }}
                      </div>
                    </div>
                  </el-scrollbar>
                </div>
                <div style="text-align: center">
                  <el-button type="primary" size="small" @click="readOnClick"
                    >一键已读</el-button
                  >
                </div>
              </div>
              <div v-if="msgList.length < 1" style="text-align: center">
                暂无消息
              </div>
            </template>
          </el-popover>
        </div>

        <!-- <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip> -->
      </template>
      <div class="avatar-container">
        <el-dropdown
          @command="handleCommand"
          class="right-menu-item hover-effect"
          trigger="click"
        >
          <div class="avatar-wrapper">
            <!-- <img src="@/assets/images/admin.png" class="user-avatar" /> -->
            <img :src="userStore.avatar" class="user-avatar" />
            <!-- <el-icon><caret-bottom /></el-icon> -->
            <div class="avatar-name" v-if="useUserStore().isTeacher">
              欢迎！{{
                `${userStore.roleName.split(",")[0]}  ${userStore.nickName}`
              }}老师
            </div>
            <div class="avatar-name" v-else>
              {{ `${userStore.roleName.split(",")[0]}  ${userStore.nickName}` }}
            </div>
          </div>

          <template #dropdown>
            <el-dropdown-menu>
              <!-- <router-link to="/user/profile">
                <el-dropdown-item>个人中心</el-dropdown-item>
              </router-link> -->
              <!-- <el-dropdown-item command="setStudent"
                v-if="userStore.roles.indexOf('schoolManage') !== -1">学生数量</el-dropdown-item> -->
              <el-dropdown-item
                command="setLayout"
                v-if="settingsStore.showSettings"
              >
                <span>布局设置</span>
              </el-dropdown-item>
              <el-dropdown-item
                @click="showPwd = true"
                v-if="useUserStore().isTeacher"
              >
                <span>修改密码</span>
              </el-dropdown-item>
              <el-dropdown-item @click="logout">
                <span>退出登录</span>
              </el-dropdown-item>
            </el-dropdown-menu>
          </template>
        </el-dropdown>
      </div>
    </div>

    <el-dialog
      class="install-dialog"
      v-model="showDownload"
      width="700"
      align-center
    >
      <div class="windows-icon"></div>
      <div class="install-list">
        <div class="install-item" v-for="item in clientList">
          <div class="install-btn" @click="download(item)">
            <div class="install-item_left">
              <div
                class="install-item_version"
                :title="`版本 ${item.packageVersion}`"
              >
                {{ `版本 ${item.packageVersion}` }}
              </div>
              <div class="install-item_size">
                {{
                  item.packageSize
                    ? (item.packageSize / 1024 / 1024).toFixed(2) + "MB"
                    : "-"
                }}
              </div>
            </div>
            <img
              src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/install-icon.png"
            />
          </div>
          <div class="install-tip" :title="item.packageExplanation">
            {{ item.packageExplanation || "" }}
          </div>
        </div>
      </div>
    </el-dialog>
    <PwdDialog :open="showPwd" @cancel="showPwd = false" />
  </div>
</template>

<script setup>
import PwdDialog from "@/components/Pwd";
import { ElMessageBox } from "element-plus";
import Breadcrumb from "@/components/Breadcrumb";
import { useRouter, useRoute } from "vue-router";
import TopNav from "@/components/TopNav";
import Hamburger from "@/components/Hamburger";
import Screenfull from "@/components/Screenfull";
import SizeSelect from "@/components/SizeSelect";
import HeaderSearch from "@/components/HeaderSearch";
import RuoYiGit from "@/components/RuoYi/Git";
import RuoYiDoc from "@/components/RuoYi/Doc";
import useAppStore from "@/store/modules/app";
import useUserStore from "@/store/modules/user";
import useSettingsStore from "@/store/modules/settings";
import { getMsg, readMsg } from "@/api/park";
import { getCurrentInstance } from "vue";
import { computed, onMounted, watch, onUnmounted } from "vue";
import { listBindCorp } from "@/api/system/dataPermissions";
import { downloadPackage, getPackageList } from "@/api/files";
import { downloadBlob, isValidUrl, isIPv4 } from "@/utils";

const { proxy } = getCurrentInstance();
const appStore = useAppStore();
const router = useRouter();
const userStore = useUserStore();
const settingsStore = useSettingsStore();
const route = useRoute();

const title = ref("智控管理平台");
const subTitle = ref("Smart Platform");
const msgCount = ref(0);
const msgList = ref([]);
const showDownload = ref(false);
const showPwd = ref(false);

const clientList = ref([]);
const loading = ref(false);
let a = ref(null);

// 添加园区相关数据
const selectedPark = ref("");
const parkList = ref([]);

const handleDownload = () => {
  showDownload.value = true;
  getClientList();
};

const download = (row) => {
  if (row.packageUri) {
    proxy.$modal.loading();
    downloadPackage({ filePath: row.packageUri })
      .then((res) => {
        console.log(res.data.replace("http://", "https://"));
        if (res.code == 200 && res.data) {
          a.value.setAttribute("href", res.data.replace("http://", "https://"));
          a.value.setAttribute("target", "_blank");
          a.value.click();
        }
      })
      .finally(() => proxy.$modal.closeLoading());
  }
};

const getClientList = async () => {
  loading.value = true;
  getPackageList()
    .then((res) => {
      console.log("安装包列表", res);
      if (res.code === 200 && res.data) {
        res.data = res.data.filter((item) => item.status != 1);
        clientList.value = res.data ?? [];
      }
    })
    .finally(() => {
      loading.value = false;
    });
};

// 获取园区列表数据
const getParkList = async () => {
  try {
    const res = await listBindCorp();
    console.log("获取园区列表成功:", res.data);

    if (!res.data || res.data.length === 0) {
      // 如果数据为空，清除选中的园区和localStorage
      selectedPark.value = "";
      localStorage.removeItem("selectedParkData");
      parkList.value = [];
      return;
    }

    parkList.value = res.data.map((item) => ({
      label: item.corpName,
      value: item.corpId,
      corpSecret: item.corpSecret,
      corpUrl: item.corpUrl,
    }));
  } catch (error) {
    console.error("获取园区列表失败:", error);
    // 发生错误时也清除选中的园区和localStorage
    selectedPark.value = "";
    localStorage.removeItem("selectedParkData");
    parkList.value = [];
  }
};

// 修改初始化函数
const initParkSelect = () => {
  try {
    const selectedParkData = localStorage.getItem("selectedParkData");
    if (selectedParkData) {
      const parkData = JSON.parse(selectedParkData);
      // 等待园区列表数据加载完成后再设置选中值
      if (parkList.value.length > 0) {
        const park = parkList.value.find((p) => p.value === parkData.corpId);
        if (park) {
          selectedPark.value = parkData.corpId;
        } else {
          // 如果找不到对应的园区，清除 localStorage
          localStorage.removeItem("selectedParkData");
        }
      }
    }
  } catch (error) {
    console.warn("初始化园区选择失败:", error);
    localStorage.removeItem("selectedParkData");
  }
};

// 监听 parkList 的变化
watch(
  parkList,
  (newVal) => {
    if (newVal.length > 0) {
      initParkSelect();
    }
  },
  { immediate: true }
);

// 添加页面刷新事件监听
const handleBeforeUnload = () => {
  // 检查当前路径是否在用户管理或角色管理页面
  const currentPath = window.location.hash.replace("#", "");
  if (
    currentPath.includes("/system/user") ||
    currentPath.includes("/system/role")
  ) {
    localStorage.removeItem("selectedParkData");
  }
};

onMounted(() => {
  a.value = document.createElement("a");
  getParkList();
  // 添加页面刷新事件监听
  window.addEventListener("beforeunload", handleBeforeUnload);
});

onUnmounted(() => {
  // 移除事件监听
  window.removeEventListener("beforeunload", handleBeforeUnload);
});

// 修改路由监听逻辑
watch(
  () => route.path,
  (newPath, oldPath) => {
    // 如果是在用户管理、角色管理、设备管理和任务中心页面之间切换
    if (
      (newPath.includes("/system/user") ||
        newPath.includes("/system/role") ||
        newPath.includes("/deviceLedger/ledger") ||
        newPath.includes("/taskManage/taskCenter/claim") ||
        newPath.includes("/taskManage/taskCenter/todo") ||
        newPath.includes("/taskManage/taskCenter/handled")) &&
      (oldPath?.includes("/system/user") ||
        oldPath?.includes("/system/role") ||
        oldPath?.includes("/deviceLedger/ledger") ||
        oldPath?.includes("/taskManage/taskCenter/claim") ||
        oldPath?.includes("/taskManage/taskCenter/todo") ||
        oldPath?.includes("/taskManage/taskCenter/handled"))
    ) {
      // 立即清除选择
      selectedPark.value = "";
      localStorage.removeItem("selectedParkData");
      // 触发清除事件
      window.dispatchEvent(
        new CustomEvent("parkChange", {
          detail: {
            type: "clear",
            path: newPath,
          },
        })
      );
    }
    // 如果离开这两个页面，也清除选择
    else if (
      !newPath.includes("/system/user") &&
      !newPath.includes("/system/role")
    ) {
      selectedPark.value = "";
      localStorage.removeItem("selectedParkData");
    }
  },
  { immediate: true }
);

// selectedPark 监听
watch(selectedPark, (newValue) => {
  if (newValue) {
    const selectedParkData = parkList.value.find(
      (park) => park.value === newValue
    );
    if (selectedParkData) {
      const parkData = {
        corpId: selectedParkData.value,
        corpSecret: selectedParkData.corpSecret,
        corpUrl: selectedParkData.corpUrl,
      };
      localStorage.setItem("selectedParkData", JSON.stringify(parkData));

      window.dispatchEvent(
        new CustomEvent("parkChange", {
          detail: {
            type: "select",
            data: parkData,
            path: route.path,
          },
        })
      );
    }
  } else {
    localStorage.removeItem("selectedParkData");
    window.dispatchEvent(
      new CustomEvent("parkChange", {
        detail: {
          type: "clear",
          path: route.path,
        },
      })
    );
  }
});

function handleGetMsg() {
  getMsg().then((res) => {
    console.log(res);
    msgCount.value = res.data.length;
    msgList.value = res.data;
  });
}

handleGetMsg();

// setInterval(() => {
// getMsg()
// }, 5000)

function handleMsg(item) {
  readMsg({ msgId: item.msgId }).then((res) => {
    console.log("readMsg ==>", res);
    if (res.code == 200) {
      handleGetMsg();
      router.push(`${msgObj.value[item.msgType]}`);
    }
  });
}

function readOnClick() {
  readMsg({}).then((res) => {
    if (res.code == 200) {
      handleGetMsg();
    }
  });
}

function toggleSideBar() {
  appStore.toggleSideBar();
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout();
      break;
    case "setStudent":
      setStudent();
      break;
    case "logout":
      logout();
      break;
    default:
      break;
  }
}

function logout() {
  proxy.$modal.confirm("是否确认退出").then(() => {
    proxy.$modal.loading();
    userStore
      .logOut()
      .then((res) => {
        if (res.code == 200) {
          proxy.$modal.msgSuccess("退出成功");
          setTimeout(() => {
            proxy.$modal.closeLoading();
            const name = window.location.pathname.replace(/\//g, "");
            window.location.href =
              import.meta.env.VITE_APP_BASE_API == "/djg-prod-api"
                ? `/djgAdmin`
                : "/" + name;

            if (!isIPv4(window.location.hostname)) window.location.reload();
          }, 1000);
        }
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  });
}

const emits = defineEmits(["setLayout", "setStudent"]);
function setLayout() {
  emits("setLayout");
}
function setStudent() {
  emits("setStudent");
}

// 计算属性判断是否显示园区选择框
const showParkSelect = computed(() => {
  return (
    parkList.value.length > 0 &&
    (route.path === "/system/user" ||
      route.path === "/system/role" ||
      route.path === "/deviceLedger/ledger" ||
      route.path === "/taskManage/taskCenter/claim" ||
      route.path === "/taskManage/taskCenter/todo" ||
      route.path === "/taskManage/taskCenter/handled")
  );
});
</script>

<style lang="scss" scoped>
@import "@/assets/styles/variables.module.scss";

.navbar {
  display: flex;
  justify-content: space-between;
  height: 55px;
  overflow: hidden;
  position: relative;
  background: $base-top-menu-color;
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);
  overflow: hidden;
  border-bottom-left-radius: 8px;
  border-bottom-right-radius: 8px;
  padding: 0 5px 0 15px;

  &-logo {
    display: flex;
    align-items: center;
    gap: 0 10px;
    font-size: 16px;
    color: #fff;
    font-weight: bold;
    img {
      width: 45px;
      height: 30px;
    }
    .subTit {
      font-size: 10px;
      font-weight: normal;
    }
  }

  .hamburger-container {
    line-height: 65px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    background-color: $base-top-menu-color;
    // position: absolute;
    // left: 30px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    // float: right;
    height: 100%;
    line-height: 55px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 16px;
      color: #ffffff;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }
    }
    .msg-container {
      margin-top: 8px;
      padding: 0 10px;
      .msg-avatar {
        cursor: pointer;
        width: 25px;
        height: 25px;
        border-radius: 10px;
      }
    }
    .msg-list {
      &_item {
        border-bottom: 1px solid #ddd;
        &-title {
        }
        &-time {
          color: #ccc;
          font-size: 14px;
        }
      }
    }
    .avatar-container {
      // margin-right: 40px;

      .avatar-wrapper {
        // margin-top: 13px;
        position: relative;
        display: flex;
        align-items: center;
        height: 55px;

        .user-avatar {
          cursor: pointer;
          width: 40px;
          height: 40px;
          border-radius: 10px;
          object-fit: contain;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
      .avatar-name {
        color: #fff;
        margin-left: 10px;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        overflow: hidden;
      }
    }
  }

  .park-select-container {
    margin-left: 20px;
    display: flex;
    align-items: center;

    :deep(.el-select) {
      width: 160px;
    }
  }
  .windows-icon {
    position: relative;
    width: 65px;
    height: 75px;
    margin: 20px auto;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/windows.png");
    background-size: 100% 100%;
    &::before {
      content: "Windows";
      bottom: -40px;
      font-size: 28px;
      left: -25px;
      color: #333;
      position: absolute;
    }
  }
  :deep(.install-dialog) {
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/install-bg.png");
    background-size: 100% 100%;
  }
  .install-list {
    display: flex;
    justify-content: center;
    margin: 80px 0 20px;
    gap: 0 40px;
    .install-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      color: #333;
      &_version {
        width: 110px;
        overflow: hidden;
        display: -webkit-box;
        /* 弹性盒子元素垂直排列 */
        -webkit-box-orient: vertical;
        /* 控制要显示的行数 */
        -webkit-line-clamp: 1;
      }
      &_size {
        margin-top: 5px;
        color: #fff;
        display: inline-block;
        background-color: #30a6ff;
        padding: 2px 10px;
        border-top-left-radius: 10px;
        border-bottom-right-radius: 10px;
        font-size: 12px;
      }
      .install-btn {
        cursor: pointer;
        display: flex;
        justify-content: space-between;
        align-items: center;
        width: 170px;
        padding: 10px;
        gap: 0 10px;
        font-size: 12px;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/install-btn.png");
        background-size: 100% 100%;
        img {
          width: 25px;
          height: 20px;
        }
      }
      .install-tip {
        width: 160px;
        font-size: 10px;
        margin-top: 5px;
        word-break: break-all;
        overflow: hidden;
        display: -webkit-box;
        text-align: center;
        /* 弹性盒子元素垂直排列 */
        -webkit-box-orient: vertical;
        /* 控制要显示的行数 */
        -webkit-line-clamp: 2;
      }
    }
  }
}
</style>
