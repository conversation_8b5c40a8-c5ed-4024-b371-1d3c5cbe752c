<template>
  <div class="aprr-setting">
    <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
    <flowComponents
      status="2"
      @getApprovalData="getApprovalData"
      :objData="objData1"
      ref="flow1Ref"
    />
  </div>
  <div class="aprr-setting">
    <flowComponents
      @getApprovalData="getApprovalData"
      status="1"
      :objData="objData2"
      ref="flow2Ref"
    />
  </div>
  <div class="aprr-setting">
    <flowComponents
      @getApprovalData="getApprovalData"
      status="3"
      :objData="objData3"
      ref="flow3Ref"
    />
  </div>
</template>

<script setup>
import { ref } from "vue";
import flowComponents from "./components/flowComponents.vue";
import { approvalList } from "@/api/approval";

const objData1 = ref({});
const objData2 = ref({});
const objData3 = ref({});
const { proxy } = getCurrentInstance();

// 选择审批人
const changePerson = () => {};

// 获取审批列表
function getApprovalData() {
  proxy.$modal.loading();
  approvalList()
    .then((res) => {
      console.log(res.data, "配置");
      // <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
      objData1.value = res.data.find((item) => item.operationApproval == 2) || {
        approvalPeopleList: [],
      };
      objData2.value = res.data.find((item) => item.operationApproval == 1) || {
        approvalPeopleList: [],
      };
      objData3.value = res.data.find((item) => item.operationApproval == 3) || {
        approvalPeopleList: [],
      };
      proxy.$modal.closeLoading();
    })
    .catch(() => {
      proxy.$modal.closeLoading();
    });
}
getApprovalData();
</script>

<style scoped>
.aprr-setting {
  padding: 20px;
  background-color: #f5f5f5;
  border-radius: 5px;
  margin-top: 20px;
  box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
}
</style>
