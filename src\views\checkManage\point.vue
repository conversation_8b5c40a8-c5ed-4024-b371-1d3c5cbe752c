<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="indicatorsCode">
          <el-input
            v-model="queryParams.indicatorsCode"
            placeholder="请输入指标编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入指标名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建指标</el-button
          >
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleBatchDel"
            :disabled="tableAllSelectedId.length < 1"
            >批量删除</el-button
          >
        </el-col>
      </el-row>

      <el-table
        ref="pointTable"
        v-loading="loading"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column type="selection" label="" width="80" align="center" />
        <el-table-column
          label="编号"
          align="center"
          minWidth="100px"
          prop="indicatorsCode"
          show-overflow-tooltip
        />
        <el-table-column
          label="指标名称"
          align="center"
          minWidth="120px"
          prop="name"
          show-overflow-tooltip
        />
        <el-table-column
          label="指标描述"
          align="center"
          minWidth="180px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.remark || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="180"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click.stop="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click.stop="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="pointRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item v-if="form.id" label="编号" prop="indicatorsCode">
          <el-input v-model="form.indicatorsCode" disabled placeholder="-" />
        </el-form-item>
        <el-form-item label="指标名称" prop="name">
          <el-input
            maxlength="10"
            v-model="form.name"
            :readonly="readonly"
            placeholder="请输入指标名称"
          />
        </el-form-item>
        <el-form-item label="指标描述" prop="remark">
          <el-input
            v-model="form.remark"
            maxlength="100"
            type="textarea"
            :readonly="readonly"
            placeholder="请输入指标描述"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-throttle>{{
            form.id ? "保存" : "新建"
          }}</el-button>
          <el-button @click="cancel">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import {
  addSchoolIndicators,
  schoolIndicatorsInfo,
  schoolIndicatorsList,
  updateSchoolIndicators,
  delSchoolIndicators,
} from "@/api/check";
import { findIndexInObejctArr } from "@/utils";

const route = useRoute();
const { proxy } = getCurrentInstance();
// const pointTable = ref(null)

const state = reactive({
  pointTable: null,
  open: false,
  loading: false,
  readonly: false,
  total: 0,
  title: "",
  tableList_all: [],
  tableList: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: "",
    indicatorsCode: "",
  },
  rules: {
    name: [
      { required: true, message: "指标名称不能为空", trigger: "blur" },
      { max: 10, message: "指标名称最多输入10个字符", trigger: "blur" },
    ],
    remark: [
      { max: 100, message: "类别描述最多输入100个字符", trigger: "blur" },
    ],
  },
});

const {
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  pointTable,
  open,
  loading,
  readonly,
  total,
  title,
  queryParams,
  form,
  rules,
  tableList,
  tableList_all,
} = toRefs(state);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

/** 查询类别列表 */
function getList() {
  state.loading = true;
  schoolIndicatorsList(state.queryParams).then((response) => {
    console.log(response);
    if (response.code == 200) {
      const { records, total } = response.data;
      state.tableList = records || [];
      state.total = total || 0;
      state.loading = false;
      nextTick(() => {
        state.tableList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.id) > -1) {
            state.pointTable.toggleRowSelection(item, true);
          } else {
            state.pointTable.toggleRowSelection(item, false);
          }
        });
      });
    }
  });
  schoolIndicatorsList({
    ...state.queryParams,
    pageNum: 1,
    pageSize: 999999,
  }).then((response) => {
    if (response.code == 200) {
      const { records } = response.data;
      state.tableList_all = records || [];
    }
  });
}
/** 取消按钮 */
function cancel() {
  state.open = false;
  reset();
}
/** 表单重置 */
function reset() {
  state.form = {
    id: "",
    name: "",
    remark: "",
    createTime: "",
  };
  proxy.resetForm("pointRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  state.queryParams.pageSize = 10;
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  state.open = true;
  state.title = "新建指标";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  state.title = "编辑指标";
  state.form = JSON.parse(JSON.stringify(row));
  state.open = true;
}
/** 查看按钮操作 */
function handleCheck(row) {
  reset();
  state.title = "查看类别";
  state.readonly = true;
  state.form = JSON.parse(JSON.stringify(row));
  state.open = true;
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["pointRef"].validate((valid) => {
    if (valid) {
      if (!!state.form.id) {
        updateSchoolIndicators(state.form).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.open = false;
        });
      } else {
        addSchoolIndicators(state.form).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          state.open = false;
        });
      }
    }
  });
}
// 批量删除操作
function handleBatchDel() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一项指标");
    return;
  } else {
    proxy.$modal
      .confirm("确定批量删除？")
      .then(async function () {
        await delSchoolIndicators({ ids: state.tableAllSelectedId.join(",") });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
}

/** 删除按钮操作 */
function handleDelete(row) {
  //   checkPonint(row.id).then((res) => {
  //     console.log("检查", res);
  //     if (!!res.data) {
  //       proxy.$modal
  //         .confirm(`${row.typeName}类别已存在设备，确认删除?`)
  //         .then(function () {
  //           return delSchoolIndicators({ id: row.id });
  //         })
  //         .then(() => {
  //           getList();
  //           proxy.$modal.msgSuccess("删除成功");
  //         })
  //         .catch(() => {});
  //     } else {
  proxy.$modal
    .confirm('是否确认删除指标名称为"' + row.name + '"的数据项？')
    .then(async function () {
      await delSchoolIndicators({ ids: row.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
  // }
  //   });
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "id"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.pointTable.setCurrentRow(null);
      state.pointTable.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.id);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.pointTable.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.pointTable.setCurrentRow(row);
    state.pointTable.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.id) === -1) {
      state.tableAllSelectedId.push(item.id);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.id);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.id) === -1) {
        state.tableAllSelectedId.push(item.id); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

getList();
</script>
