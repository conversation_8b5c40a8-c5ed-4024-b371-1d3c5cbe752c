import QRCode from "qrcode";
import J<PERSON>Zip from "jszip";
import { saveAs } from "file-saver";

// 添加日期格式化函数
function formatDate(dateStr) {
  if (!dateStr) return "";

  // 获取当前日期
  const today = new Date();
  const formatNum = (num) => String(num).padStart(2, "0");

  // 格式化日期显示
  function formatDateRange(startDate, endDate) {
    return (
      `${startDate.getFullYear()}-${formatNum(
        startDate.getMonth() + 1
      )}-${formatNum(startDate.getDate())}~` +
      `${endDate.getFullYear()}-${formatNum(
        endDate.getMonth() + 1
      )}-${formatNum(endDate.getDate())}`
    );
  }

  // 计算结束日期
  let startDate = new Date();
  let endDate = new Date();

  if (dateStr === "一天") {
    endDate.setDate(startDate.getDate() + 1);
    return formatDateRange(startDate, endDate);
  }
  if (dateStr === "一周") {
    endDate.setDate(startDate.getDate() + 7);
    return formatDateRange(startDate, endDate);
  }
  if (dateStr === "一个月") {
    endDate.setMonth(startDate.getMonth() + 1);
    return formatDateRange(startDate, endDate);
  }
  if (dateStr === "无限期") {
    endDate.setFullYear(startDate.getFullYear() + 100);
    return formatDateRange(startDate, endDate);
  }

  // 处理已有的日期格式（如果需要）
  const dates = dateStr.split("-");
  if (dates.length >= 4) {
    return `${dates[0]}-${dates[1]}-${dates[2]}~${dates[3]}-${dates[4]}-${dates[5]}`;
  }
  return dateStr;
}

export function generateQRCode(dataArray, isZip = true) {
  return new Promise((resolve, reject) => {
    if (isZip) {
      const zip = new JSZip();
      const promises = dataArray.map((data, index) => {
        const { name, time } = data;

        // 修改提示文字格式
        const prompt = `${name}\n有效期：${formatDate(time)}`;
        const maxWidth = 300; // 设置最大宽度
        const lineHeight = 15; // 行高
        // const promptLines = wrapText(ctx, prompt, maxWidth);

        return QRCode.toDataURL(time).then((url) => {
          // 创建 canvas
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          const img = new Image();

          return new Promise((resolve) => {
            img.onload = () => {
              // 设置画布大小
              canvas.width = img.width + 200;
              canvas.height = img.height + 30 + lineHeight * 3; // 留出空间放置提示文字
              // ctx.drawImage(img, 0, 0);

              // 添加提示文字背景
              const promptHeight = 40; // 背景高度
              ctx.fillStyle = "white"; // 背景颜色
              // ctx.fillRect(0, img.height, img.width, promptHeight); // 绘制背景矩形
              ctx.fillRect(0, 0, canvas.width, canvas.height); // 绘制背景矩形

              // 计算二维码和提示文字的居中位置
              const qrX = (canvas.width - img.width) / 2; // 水平居中
              const qrY = 10; // 垂直位置

              // 绘制二维码
              ctx.drawImage(img, qrX, qrY);

              // 添加提示文字
              ctx.fillStyle = "black";
              ctx.font = "12px Arial";
              // ctx.textAlign = "center";

              // 将 prompt 拆分为多行
              // const promptLines = prompt.split("\n");
              const promptLines = wrapText(ctx, prompt, maxWidth);
              promptLines.forEach((line, index) => {
                const textY = qrY + img.height + 20 + lineHeight * index;

                // 计算每行的宽度并使其水平居中
                const textWidth = ctx.measureText(line).width;
                const xPosition = (canvas.width - textWidth) / 2; // 计算居中位置

                // 检查宽度并添加省略号
                if (textWidth > maxWidth) {
                  let truncatedLine = line;
                  while (
                    ctx.measureText(truncatedLine + "...").width > maxWidth
                  ) {
                    truncatedLine = truncatedLine.slice(0, -1); // 去掉最后一个字符
                  }

                  // 计算每行的宽度并使其水平居中
                  const textWidth1 = ctx.measureText(truncatedLine).width;
                  const xPosition1 = (canvas.width - textWidth1) / 2; // 计算居中位置

                  ctx.fillText(truncatedLine + "...", xPosition1, textY); // 绘制带省略号的文本
                } else {
                  // 计算每行的宽度并使其水平居中

                  ctx.fillText(line, xPosition, textY); // 绘制正常文本
                }

                // ctx.fillText(line, xPosition, textY); // 确保文字水平居中
              });

              // 转为 Blob
              canvas.toBlob((blob) => {
                zip.file(`${name}-设备二维码.png`, blob);
                resolve();
              }, "image/png");
            };
            img.src = url;
          });
        });
      });

      // 等待所有二维码生成完毕
      Promise.all(promises)
        .then(() => {
          zip
            .generateAsync({ type: "blob" })
            .then((content) => {
              saveAs(content, `设备二维码.zip`);
              resolve(); // Resolve the promise after successful download
            })
            .catch(reject);
        })
        .catch(reject);
    } else {
      let { name, time } = dataArray[0];

      // 生成实际的日期范围字符串用于二维码
      const today = new Date();
      const formatNum = (num) => String(num).padStart(2, "0");
      let endDate = new Date(today);

      if (time === "一天") {
        endDate.setDate(today.getDate() + 1);
      } else if (time === "一周") {
        endDate.setDate(today.getDate() + 7);
      } else if (time === "一个月") {
        endDate.setMonth(today.getMonth() + 1);
      } else if (time === "无限期") {
        endDate.setFullYear(today.getFullYear() + 100);
      } else {
        // 如果是已经格式化的日期字符串，直接使用
        const dates = time.split("-");
        if (dates.length >= 4) {
          time = time; // 保持原样
        }
      }

      // 只有在time是预设选项时才重新格式化日期
      if (["一天", "一周", "一个月", "无限期"].includes(time)) {
        time =
          `${today.getFullYear()}-${formatNum(
            today.getMonth() + 1
          )}-${formatNum(today.getDate())}-` +
          `${endDate.getFullYear()}-${formatNum(
            endDate.getMonth() + 1
          )}-${formatNum(endDate.getDate())}`;
      }

      // 修改提示文字格式
      const prompt = `${name}\n有效期：${formatDate(time)}`;
      const maxWidth = 300;
      const lineHeight = 15;

      QRCode.toDataURL(time)
        .then((url) => {
          // 创建 canvas
          const canvas = document.createElement("canvas");
          const ctx = canvas.getContext("2d");
          const img = new Image();

          return new Promise((resolve, reject) => {
            img.onerror = () => {
              reject(new Error("图片加载失败"));
            };

            img.onload = () => {
              try {
                // 设置画布大小
                canvas.width = img.width + 200;
                canvas.height = img.height + 30 + lineHeight * 3;

                // 添加提示文字背景
                ctx.fillStyle = "white";
                ctx.fillRect(0, 0, canvas.width, canvas.height);

                // 计算二维码和提示文字的居中位置
                const qrX = (canvas.width - img.width) / 2;
                const qrY = 10;

                // 绘制二维码
                ctx.drawImage(img, qrX, qrY);

                // 添加提示文字
                ctx.fillStyle = "black";
                ctx.font = "12px Arial";

                // 将 prompt 拆分为多行
                const promptLines = wrapText(ctx, prompt, maxWidth);
                promptLines.forEach((line, index) => {
                  const textY = qrY + img.height + 20 + lineHeight * index;
                  const textWidth = ctx.measureText(line).width;
                  const xPosition = (canvas.width - textWidth) / 2;

                  if (textWidth > maxWidth) {
                    let truncatedLine = line;
                    while (
                      ctx.measureText(truncatedLine + "...").width > maxWidth
                    ) {
                      truncatedLine = truncatedLine.slice(0, -1);
                    }
                    const textWidth1 = ctx.measureText(truncatedLine).width;
                    const xPosition1 = (canvas.width - textWidth1) / 2;
                    ctx.fillText(truncatedLine + "...", xPosition1, textY);
                  } else {
                    ctx.fillText(line, xPosition, textY);
                  }
                });

                resolve(canvas.toDataURL("image/png", 1.0));
              } catch (error) {
                reject(error);
              }
            };
            img.src = url;
          });
        })
        .then((res) => {
          try {
            var blob = getBlob(res);
            var link = document.createElement("a");
            var href = window.URL.createObjectURL(blob);
            link.href = href;
            const fileName = name.replace(/[【】]/g, "").trim();
            link.download = `${fileName || "日常考勤"}-二维码.png`;
            document.body.appendChild(link);
            link.click();
            setTimeout(function () {
              window.URL.revokeObjectURL(href);
              document.body.removeChild(link);
              resolve();
            }, 100);
          } catch (error) {
            reject(error);
          }
        })
        .catch((error) => {
          console.error("二维码生成失败:", error);
          reject(error);
        });
    }
  });
}

export function wrapText(ctx, text, maxWidth) {
  const lines = text.split("\n").flatMap((line) => {
    const words = line.split(" ");
    const wrappedLines = [];
    let currentLine = "";

    words.forEach((word) => {
      const testLine = currentLine + word + " ";
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine) {
        wrappedLines.push(currentLine);
        currentLine = word + " ";
      } else {
        currentLine = testLine;
      }
    });

    if (currentLine) {
      wrappedLines.push(currentLine);
    }

    return wrappedLines;
  });

  return lines;
}

function getBlob(base64) {
  try {
    var mimeString = base64.split(",")[0].split(":")[1].split(";")[0];
    var byteString = atob(base64.split(",")[1]);
    var arrayBuffer = new ArrayBuffer(byteString.length);
    var intArray = new Uint8Array(arrayBuffer);
    for (var i = 0; i < byteString.length; i += 1) {
      intArray[i] = byteString.charCodeAt(i);
    }
    return new Blob([intArray], { type: mimeString });
  } catch (error) {
    console.error("Blob 创建失败:", error);
    throw error;
  }
}

// export function generateQRCode(Eltext, filename) {
//   // 获取输入的文本
//   // var text = document.getElementById("text").value;
//   var text = Eltext;
//   // 清空之前的二维码
//   document.getElementById("qrcode").innerHTML = "";
//   // Elqrcode.innerHTML = "";

//   // 添加提示文字
//   var promptElement = document.createElement("div");
//   promptElement.textContent = 'promptText'; // 设置提示文字
//   promptElement.style.marginTop = "10px"; // 设置与二维码的间距
//   promptElement.style.textAlign = "center"; // 设置文本居中
//   document.getElementById("qrcode").appendChild(promptElement); // 将提示文字添加到二维码容器中

//   // 生成新的二维码
//   let url = new QRCode(document.getElementById("qrcode"), text);
//   let base64Text = url._el.querySelector("canvas").toDataURL("image/png");
//   var blob = getBlob(base64Text);

//   var link = document.createElement("a");
//   var href = window.URL.createObjectURL(blob);
//   link.href = href;
//   link.download = `${filename}-签到码.png`; //a标签的下载属性
//   // document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
//   link.click();
//   // 延时保证下载成功执行，否则可能下载未找到文件的问题
//   setTimeout(function () {
//     window.URL.revokeObjectURL(href); // 释放Url对象
//     // document.body.removeChild(link);
//   }, 100);

// }

// function getBlob(base64) {
//   var mimeString = base64.split(",")[0].split(":")[1].split(";")[0]; // mime类型
//   var byteString = atob(base64.split(",")[1]); //base64 解码
//   var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
//   var intArray = new Uint8Array(arrayBuffer); //创建视图
//   for (var i = 0; i < byteString.length; i += 1) {
//     intArray[i] = byteString.charCodeAt(i);
//   }
//   return new Blob([intArray], {
//     type: mimeString,
//   });
// }
