<template>
  <div class="unify">
    <div class="unify-main">
      <div class="unify-main_header">
        <div class="unify-main_header-title">数字资产可视化数据大屏</div>
        <div class="unify-main_header-btns">
          <div class="unify-main_header-btns_btn" :class="curBtn == index ? 'active' : ''"
            v-for="(item, index) in headerBtns" :key="index">
            {{ item.name }}
          </div>
        </div>
        <div class="unify-main_header-date">
          {{ proxy.parseTime(curTime, "{y}年{m}月{d}日 {h}:{i}") }}
        </div>
      </div>
      <div class="unify-main_charts">
        <div class="col col1">
          <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
            <div class="info-left">
              <div class="flex" v-for="(item, index) in summaryLeftList" :key="index">
                <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
                <div class="info-left_cont">
                  <div class="info-left_title">{{ item.name }}</div>
                  <div class="info-left_count">
                    <span>{{ item.num }}</span>{{ item.unit }}
                  </div>
                </div>
              </div>
            </div>
            <div class="info-right">
              <div class="flex" v-for="(item, index) in summaryRightList" :key="index">
                <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
                <div class="info-right_cont">
                  <div class="info-right_title">{{ item.name }}</div>
                  <div class="info-right_count">
                    <span>{{ item.num }}</span>{{ item.unit }}
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="col1-block2">
            <div class="block-title">信息资产类别情况</div>
            <div class="flex">
              <div class="block echart-item">
                <Echarts id="pieData" width="100%" height="100%" :fullOptions="pieOption" />
              </div>
              <div class="block echart-item">
                <Echarts id="barData" width="100%" height="100%" :fullOptions="barOption" />
              </div>
            </div>
          </div>
          <div class="col1-block3">
            <div class="block-title">资产覆盖率</div>
            <div class="battery">
              <span>{{ objData.assetCoverageStatistics.coverage }}</span>
            </div>
            <div class="subtitle">部门资产状况</div>
            <div class="table block">
              <div class="table-row opt-title">
                <div class="table-row_name table-row_head">部门名称</div>
                <div class="table-row_name table-row_head">部门人数</div>
                <div class="table-row_name table-row_head">资产总数</div>
                <div class="table-row_name table-row_head">操作</div>
              </div>
              <div class="table-row data" v-for="item in objData.assetCoverageStatistics
                .assetCoverageRanking" :key="item.deptName">
                <div class="table-row_name">{{ item.deptName }}</div>
                <div class="table-row_name">
                  {{ item.peopleNum > 99999 ? "99999+" : item.peopleNum }}
                </div>
                <div class="table-row_name">
                  {{ item.assetsNum > 99999 ? "99999+" : item.assetsNum }}
                </div>
                <div class="table-row_name" style="cursor: pointer" @click="handleCheck(item)">
                  点击查看
                </div>
              </div>
            </div>
            <div class="flex">
              <div class="table table2">
                <div class="subtitle">备件使用排行榜</div>
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">备件名称</div>
                  <div class="table-row_count table-row_head">使用量</div>
                </div>
                <div class="table-row data" v-for="(item, index) in objData.sparepartsUseStatistics" :key="index">
                  <div class="table-row_number">{{ index + 1 }}</div>
                  <div class="table-row_type">{{ item.sparepartsName }}</div>
                  <div class="table-row_count">
                    {{ item.userNum > 99999 ? "99999+" : item.userNum }}
                  </div>
                </div>
              </div>
              <div class="table table2">
                <div class="subtitle">设备备件消耗排行榜</div>
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">备件名称</div>
                  <div class="table-row_count table-row_head">使用量</div>
                </div>
                <div class="table-row data" v-for="(
item, index
                  ) in objData.sparepartsConsumptionStatistics" :key="index">
                  <div class="table-row_number">{{ index + 1 }}</div>
                  <div class="table-row_type">{{ item.deviceName }}</div>
                  <div class="table-row_count">
                    {{
                      item.consumptionNum > 99999
                        ? "99999+"
                        : item.consumptionNum
                    }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col col2">
          <div class="col2-block1">
            <div class="item-opt2" :class="[
              `opt${index + 1}`,
              curTab == index ? 'active' : '',
              index % 2 == 0 ? 'left' : 'right',
            ]" v-for="(item, index) in optList6" :key="index">
              <div>{{ item.name }}</div>
              <span>{{ item.number }}</span>
            </div>
          </div>
          <div class="col2-block2">
            <div class="grid-item network">
              <div class="block-title">网络资产状况</div>
              <div class="flex">
                <div class="block">
                  <div class="subtitle">网络资产类型占比</div>
                  <Echarts id="pieData5" width="100%" height="100%" :fullOptions="pieOption5" />
                </div>
                <div class="block block2">
                  <div class="subtitle">网络资产状况</div>
                  <div class="address">
                    共入库网络地址个数
                    <div><span>6</span>个</div>
                  </div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="block-title">数据信息资产总数</div>
              <div class="echart flex">
                <div class="block" style="flex: 1.4">
                  <Echarts id="lineData" width="100%" height="100%" :fullOptions="lineOption" />
                </div>
                <div class="block block-right">
                  <div class="item-opt" v-for="(item, index) in optList1" :key="index">
                    {{ item.title }}
                    <div class="item-opt_row">
                      <span>{{ item.total || 0 }}</span>
                      <div :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                        ">
                        {{ item.ratio }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="block-title">知识产权资产状况</div>
              <div class="echart flex">
                <div class="block" style="flex: 1.4">
                  <Echarts id="lineData2" width="100%" height="100%" :fullOptions="lineOption2" />
                </div>
                <div class="block block-right">
                  <div class="item-opt" v-for="(item, index) in optList2" :key="index">
                    {{ item.title }}
                    <div class="item-opt_row">
                      <span>{{ item.total || 0 }}</span>
                      <div :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                        ">
                        {{ item.ratio }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="grid-item">
              <div class="block-title">运维考勤打卡情况</div>
              <div class="echart flex">
                <div class="block" style="flex: 1.4">
                  <Echarts id="lineData5" width="100%" height="100%" :fullOptions="lineOption5" />
                </div>
                <div class="block block-right">
                  <div class="item-opt" v-for="(item, index) in optList5" :key="index">
                    {{ item.title }}
                    <div class="item-opt_row">
                      <span>{{ item.total || 0 }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col col3">
          <div class="col3-block3">
            <div class="block-title">硬件资产状况</div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">使用年限占比</div>
                <Echarts id="pieData6" width="100%" height="100%" :fullOptions="pieOption6" />
              </div>
              <div class="block">
                <div class="subtitle">故障时长占比</div>
                <Echarts id="pieData7" width="100%" height="100%" :fullOptions="pieOption7" />
              </div>
            </div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">资产设备类型占比</div>
                <Echarts id="pieData8" width="100%" height="100%" :fullOptions="pieOption8" />
              </div>
              <div class="block">
                <div class="subtitle">资产端口类型占比</div>
                <Echarts id="pieData9" width="100%" height="100%" :fullOptions="pieOption9" />
              </div>
            </div>
            <div class="flex">
              <div class="block block2">
                <div class="subtitle">硬件资产标签使用TOP5</div>
                <Echarts id="barData3" width="100%" height="100%" :fullOptions="barOption3" />
              </div>
            </div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">资产硬盘大小占比</div>
                <Echarts id="pieData10" width="100%" height="100%" :fullOptions="pieOption10" />
              </div>
              <div class="block">
                <div class="subtitle">资产内存占比</div>
                <Echarts id="pieData11" width="100%" height="100%" :fullOptions="pieOption11" />
              </div>
            </div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">故障资产品牌占比</div>
                <Echarts id="pieData12" width="100%" height="100%" :fullOptions="pieOption12" />
              </div>
              <div class="block">
                <div class="subtitle">资产内故障资产使用年限占比</div>
                <Echarts id="pieData13" width="100%" height="100%" :fullOptions="pieOption13" />
              </div>
            </div>
            <div class="flex">
              <div class="block block2">
                <div class="subtitle">资产系统版本占比TOP5</div>
                <Echarts id="barData4" width="100%" height="100%" :fullOptions="barOption4" />
              </div>
            </div>
          </div>
        </div>
        <div class="col col4">
          <div class="col4-block1">
            <div class="col4-block1_item" :class="[`item${index + 1}`]" v-for="(item, index) in maintainList"
              :key="index">
              <div class="row">
                <div>{{ item.title }}</div>
                <div class="amount">
                  <span>{{ item.total }}</span>{{ item.unit }}
                </div>
              </div>
              <div class="row">
                <div>同比</div>
                <div class="row-center">
                  与昨日{{
                    item.num > 0
                      ? `相比多出${item.num}`
                      : item.num < 0 ? `相比少于${Math.abs(item.num)}` : "相同" }}<span v-if="item.num != 0">{{ item.unit
                  }}</span>
                </div>
                <div class="icon" :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'">
                  {{ item.ratio }}
                </div>
              </div>
            </div>
          </div>
          <div class="col4-block2">
            <div class="block-title">软件资产状况</div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">软件资产状况</div>
                <Echarts id="pieData2" width="100%" height="100%" :fullOptions="pieOption2" />
              </div>
              <div class="block">
                <div class="subtitle">软件到期时间</div>
                <Echarts id="pieData3" width="100%" height="100%" :fullOptions="pieOption3" />
              </div>
            </div>
            <div class="flex">
              <div class="block">
                <div class="subtitle">软件使用状况</div>
                <Echarts id="pieData4" width="100%" height="100%" :fullOptions="pieOption4" />
              </div>
              <div class="block">
                <div class="subtitle">应用使用时长Top5</div>
                <Echarts id="barData2" width="100%" height="100%" :fullOptions="barOption2" />
              </div>
            </div>
          </div>
          <div class="col4-block3">
            <div class="block-title">工单统计</div>
            <div class="echart flex">
              <div class="block block2" style="flex: 1.4">
                <div class="subtitle">工单平均处理时间</div>
                <Echarts id="lineData4" width="100%" height="100%" :fullOptions="lineOption4" />
              </div>
              <div class="block block-right">
                <div class="item-opt" v-for="(item, index) in optList4" :key="index">
                  {{ item.title }}
                  <div class="item-opt_row">
                    <span style="font-size: 0.6vw">{{ item.total || 0 }}</span>
                    <div v-if="item.fq" style="color: #4095e5">
                      {{ item.fq }}
                    </div>
                    <div v-else :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      ">
                      {{ item.ratio }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="echart flex">
              <div class="block block2" style="flex: 1.4">
                <div class="subtitle">收到/处理工单总数</div>
                <Echarts id="lineData3" width="100%" height="100%" :fullOptions="lineOption3" />
              </div>
              <div class="block block-right">
                <div class="item-opt" v-for="(item, index) in optList3" :key="index">
                  {{ item.title }}
                  <div class="item-opt_row">
                    <span>{{ item.total || 0 }}</span>
                    <div :class="item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      ">
                      {{ item.ratio }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog class="custom-dialog" title="查看详情" v-model="dialogVisible" align-center width="500">
      <el-descriptions title="" border :column="1">
        <el-descriptions-item label-class-name="label-width" class-name="value-width" label="部门名称">
          {{ deptInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="label-width" class-name="value-width" label="部门人数">
          {{ deptInfo.peopleNum }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="label-width" class-name="value-width" label="资产总数">
          {{ deptInfo.assetsNum }}
        </el-descriptions-item>
        <el-descriptions-item label-class-name="label-width" class-name="value-width" label="部门负责人">
          {{ deptInfo.leaderName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>
  </div>
</template>

<script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import { fitChartSize } from "@/utils";
import {
  getSchoolStatistics,
  getWorkOrderStatistics,
  getAssetsStatistics,
  getMaintainCountStatistics,
  getWorkOrderTimeStatistics,
} from "@/api/unify";
import { listTree } from "@/api/system/distribution";

const { proxy } = getCurrentInstance();
const router = useRouter();

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "开机次数", num: "0", unit: "次" },
  { name: "报废总数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "0", unit: "人" },
  { name: "今日执勤", num: "0", unit: "次" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "本月度知识库", total: 0, ratio: "0%", num: 0 },
  { title: "本年度知识库", total: 0, ratio: "0", num: 0 },
]);

const optList2 = ref([
  { title: "本月度知识产权个数", total: 0, ratio: "-100%", num: -100 },
  { title: "本年度知识产权个数", total: 5, ratio: "0%", num: 0 },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  { title: "今日工单平均处理时间", total: 0, ratio: "0%", num: 0 },
  { title: "合计工单平均处理时间", total: 0, fq: "0天/次" },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const optList6 = ref([
  { name: "数据信息资产", number: "01" },
  { name: "网络资产", number: "02" },
  { name: "硬件资产", number: "03" },
  { name: "知识产权资产", number: "04" },
  { name: "软件资产", number: "05" },
]);

// const optList6 = ref([
//   { name: "数据信息资产", number: "01" },
//   { name: "工作台", number: "02" },
//   { name: "网络资产", number: "03" },
//   { name: "硬件资产", number: "04" },
//   { name: "知识产权资产", number: "05" },
//   { name: "软件资产", number: "06" },
// ]);

const state = reactive({
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curBtn: 0,
  curTab: -1,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  dialogVisible,
  deptInfo,
  chartsDOM,
  curBtn,
  curTab,
  curTime,
  timer,
  timer2,
} = toRefs(state);

// 头部按钮数据
const headerBtns = ref([
  { name: "资产总览", type: "overview" },
  // { name: "设备集控", type: "monitor" },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "15%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//应用使用时长TOP5柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "30%",
      left: "23%",
      top: "10%",
      right: "5%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "35%",
        formatter: "{value} h",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "10%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//硬件资产标签使用TOP5柱状图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "25%",
      left: "10%",
      top: "15%",
      right: "3%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "10%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//资产系统版本占比TOP5
const barOption4 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "26%",
      left: "10%",
      top: "15%",
      right: "3%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "10%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//资产类别占比饼图
const pieOption = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["40%", "55%"],
        center: ["50%", "55%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: 10,
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const pieOption2 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "操作系统", value: 51 },
          { name: "教学软件", value: 23 },
          { name: "其他软件", value: 9 },
        ],
      },
    ],
  },
});

// 软件到期时间饼图（假数据）
const pieOption3 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "5年以上", value: 34 },
          { name: "3-5年", value: 63 },
          { name: "1-3年", value: 72 },
          { name: "1年以内", value: 46 },
        ],
      },
    ],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 1 },
          { name: "教育平台", value: 3 },
          { name: "办公平台", value: 2 },
        ],
      },
    ],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieOption8 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产端口类型占比饼图
const pieOption9 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieOption12 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

// 资产内故障资产使用年限占比饼图
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["35%", "50%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
        },
        lineStyle: {
          color: "#009bab",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "10%",
      height: "70%",
      right: "5%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        rotate: 0,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//工单统计2
const lineOption4 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "15%",
      bottom: "32%",
      right: "5%",
    },
    series: [
      {
        data: [42],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

//运维考勤打卡情况
const lineOption5 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: "#00f0ff",
        },
        lineStyle: {
          color: "#00f0ff",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

function handleCheck(item) {
  state.deptInfo = {
    ...item,
  };
  state.dialogVisible = true;
}

//获取数据
function getData() {
  getSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        maintenanceTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
      } = res.data;
      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = runTotal;
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;

      objData.value = {
        ...objData.value,
        sparepartsUseStatistics: res.data.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      // 设置部门名称假数据
      objData.value.assetCoverageStatistics.assetCoverageRanking[0].deptName = "教务处";
      objData.value.assetCoverageStatistics.assetCoverageRanking[1].deptName = "德育处";
      objData.value.assetCoverageStatistics.assetCoverageRanking[2].deptName = "总务处";
      objData.value.assetCoverageStatistics.assetCoverageRanking[3].deptName = "学校办公室";
      objData.value.assetCoverageStatistics.assetCoverageRanking[4].deptName = "保卫处";

      lineOption2.value.options.xAxis.data = [
        "2024-10",
        "2024-11",
        "2024-12",
        "2025-01",
        "2025-02",
        "2025-03",
      ];
      lineOption2.value.options.series[0].data = [0, 0, 0, 0, 5, 0];

      if (deviceAppUserStatistics?.appUseTime) {
        // 软件使用时长柱状图赋值
        barOption2.value.options.xAxis.data =
          deviceAppUserStatistics.appUseTime.map((item) => item.month);
        barOption2.value.options.series[0].data =
          deviceAppUserStatistics.appUseTime.map((item) => item.timeTotal);
      }

      if (deviceAppUserStatistics?.appUseType) {
        // 软件使用占比饼图赋值
        pieOption4.value.options.series[0].data =
          deviceAppUserStatistics.appUseType.map((item) => {
            return {
              name: item.appType,
              value: item.num,
            };
          });
      }

      if (assetsTypeStatistics) {
        // 资产类别占比柱状图赋值
        barOption.value.options.xAxis.data = assetsTypeStatistics.map(
          (item) => item.assetsTypeName
        );
        barOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => item.num
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = assetsTypeStatistics.map(
          (item) => {
            return {
              name: item.assetsTypeName,
              value: item.num,
            };
          }
        );

        // 假数据
        pieOption.value.options.series[0].data[0].name = '计算机'
        pieOption.value.options.series[0].data[1].name = '工作站'
        pieOption.value.options.series[0].data[2].name = '服务器'
        // pieOption.value.options.series[0].data[3].name = '磁盘'
        // pieOption.value.options.series[0].data[4].name = '电灯泡'

        barOption.value.options.xAxis.data[0] = '计算机'
        barOption.value.options.xAxis.data[1] = '工作站'
        barOption.value.options.xAxis.data[2] = '服务器'
        // barOption.value.options.xAxis.data[3] = '磁盘'
        // barOption.value.options.xAxis.data[4] = '电灯泡'

      }
      console.log(pieOption.value, barOption.value, '资产类别111')

      if (knowledgeBaseStatistics) {
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = knowledgeBaseStatistics.map(
          (item) => item.num
        );
      }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];
        optList1.value[0].total = knowledgeBaseMonth;
        optList1.value[0].ratio = knowledgeBaseMonthRise;
        optList1.value[0].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;

        optList1.value[1].total = knowledgeBaseYear;
        optList1.value[1].ratio = knowledgeBaseYearRise;
        optList1.value[1].num =
          knowledgeBaseYearRise?.replace("%", "") * 1 || 0;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsFaultBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
    } = res.data;

    // 硬件资产标签使用TOP5
    if (assetsTagUseProportion) {
      // 资产类别占比柱状图赋值
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map((item) => item.assetsTagName) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];

      // 假数据
      barOption3.value.options.xAxis.data[0] = '小学部'
      barOption3.value.options.xAxis.data[1] = '初中部'
      barOption3.value.options.xAxis.data[2] = '高中部'
      barOption3.value.options.xAxis.data[3] = '易消耗品'
      barOption3.value.options.xAxis.data[4] = '维护过的物品'
    }

    console.log(barOption3.value, "barOption3.value")

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      // 资产类别占比柱状图赋值
      barOption4.value.options.xAxis.data = assetsSystemVersionProportion.map(
        (item) => item.assetsSystemVersionName
      );
      barOption4.value.options.series[0].data =
        assetsSystemVersionProportion.map((item) => item.num);
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data =
        deviceTroubleProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data =
        hardwareAssetsUseProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      pieOption8.value.options.series[0].data =
        assetsTypeProportion?.map((item) => {
          return {
            name: item.assetsTypeName,
            value: item.num,
          };
        }) || [];
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      pieOption9.value.options.series[0].data =
        assetsPortProportion?.map((item) => {
          return {
            name: item.assetsPortName,
            value: item.num,
          };
        }) || [];
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data =
        assetsHardDiskProportion?.map((item) => {
          return {
            name: item.assetsHardDiskName,
            value: item.num,
          };
        }) || [];
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data =
        assetsMemoryProportion?.map((item) => {
          return {
            name: item.assetsMemoryName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产品牌占比
    if (assetsFaultBrandProportion) {
      pieOption12.value.options.series[0].data =
        assetsFaultBrandProportion?.map((item) => {
          return {
            name: item.assetsFaultBrandName,
            value: item.num,
          };
        }) || [];
    }

    // 故障资产使用年限占比
    if (assetsFaultUseYearProportion) {
      pieOption13.value.options.series[0].data =
        assetsFaultUseYearProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }
  });
}

function getWorkOrderLine1() {
  getWorkOrderTimeStatistics(orderParams1.value).then((res) => {
    console.log(res, "工单处理统计");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      lineOption3.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption3.value.options.series[0].data =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption3.value.options.series[1].data =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
    }
  });
}

function getWorkOrderLine2() {
  getWorkOrderTimeStatistics(orderParams2.value).then((res) => {
    console.log(res, "工单处理统计2");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      lineOption4.value.options.xAxis.data =
        workOrderStatistics?.map((item) => item.statisticsTime) || [];
      lineOption4.value.options.series[0].data =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption4.value.options.series[1].data =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
    }
  });
}

function getWorkOrderLine3() {
  getMaintainCountStatistics(orderParams3.value).then((res) => {
    console.log(res, "运维统计");
    let { clockStatistics, shouldClockCountToday, clockedCountToday } =
      res.data;
    optList5.value[0].total = shouldClockCountToday;
    optList5.value[1].total = clockedCountToday;
    if (clockStatistics) {
      lineOption5.value.options.xAxis.data =
        clockStatistics?.map((item) => item.dateLabel) || [];
      lineOption5.value.options.series[0].data =
        clockStatistics?.map((item) => item.shouldClockUsers) || [];
      lineOption5.value.options.series[1].data =
        clockStatistics?.map((item) => item.clockedUsers) || [];
    }
  });
}

function getOrderData() {
  getWorkOrderLine1();
  getWorkOrderLine2();
  getWorkOrderLine3();
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList3.value[0].total = processingWorkOrderMonth;
      optList3.value[0].ratio = processingWorkOrderMonthRise;
      optList3.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList3.value[1].total = processingWorkOrderYear;
      optList3.value[1].ratio = processingWorkOrderYearRise;
      optList3.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
      } = workOrderProcrssingTime;
      optList4.value[0].total = todayAverageProcessingTime || 0;
      optList4.value[0].ratio = riseOfProcessingTime || "0%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;

      // optList4.value[1].total = (totalAverageProcessingTime.replace('分钟', '') * 1 / 1440).toFixed(1) + '天';
      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
    }
  });
}

onMounted(() => {
  proxy.$modal.loading();
  getData();
  getOrderData();
  getAssetsData();
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>

<style lang="scss" scoped>
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}

.unify {
  width: 100%;
  height: 100%;
  background-color: #020d20;
  // border: 1px solid red;
  // color: #c8d7fa;
  color: #b0c0e6;
  overflow-y: hidden;

  .opt-title {
    color: #6889de !important;
  }

  .subtitle {
    color: #b0c0e6;
    padding: 0.2vw 0.5vw;
    font-size: 0.6vw;
  }

  &-main {
    width: 100vw;
    height: 56vw;
    // border: 1px solid red;
    background: url("@/assets/screen/bg.png");
    background-size: 100% 100%;

    &_header {
      position: relative;
      width: 100%;
      height: 3.5vw;
      background: url("@/assets/screen/top_nav.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 0 1vw;

      &-title {
        // border: 1px solid red;
        font-size: 2vw;
        letter-spacing: 0.4vw;
      }

      &-btns {
        display: flex;
        margin-left: 4vw;
        padding-top: 0.5vw;

        &_btn {
          // border: 1px solid red;
          color: #6889de;
          cursor: pointer;
          text-align: center;
          width: 10.5vw;
          height: 1.75vw;
          line-height: 1.75vw;
          background: url("@/assets/screen/top_btn.png");
          background-size: 100% 100%;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.5s ease, height 0.5s ease;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);

            &::before {
              width: 300%;
              height: 300%;
            }
          }

          &.active {
            color: #c8d7fa;
            background: url("@/assets/screen/top_btn_sel.png");
            background-size: 100% 100%;
            transform: translateY(0);

            &::before {
              width: 0;
              height: 0;
            }
          }
        }
      }

      &-date {
        position: absolute;
        right: 1vw;
        top: 1vw;
      }
    }

    &_charts {
      display: flex;
      // border: 1px solid red;
      width: 100%;
      height: 52vw;
      padding: 0 0.5vw;
      gap: 0 0.5vw;

      .col {
        .flex {
          display: flex;
          gap: 0 0.5vw;
        }

        .block {
          flex: 1;
          //   border: 1px solid red;
          position: relative;
          background: url("@/assets/screen/app/block-bg.png");
          background-size: 100% 100%;

          &-title {
            font-size: 0.8vw;
            height: 2vw;
            line-height: 2vw;
            padding-left: 1vw;
            margin-top: 1vw;
            color: #fff;
            background: url("@/assets/screen/app/block-title.png");
            background-size: 100% 100%;
          }
        }

        .item-opt {
          width: 100%;
          // border: 1px solid red;
          background: url("@/assets/screen/app/trend-info_bg.png");
          background-size: 100% 100%;
          padding: 0.3vw 0.5vw;
          font-size: 0.6vw;
          color: #8495b6;

          &_row {
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding-top: 0.5vw;

            span {
              display: inline-block;
              // font-size: 1vw;
              // line-height: 1.2vw;
              padding-right: 0.2vw;
              color: #c7d9f9;
            }

            div {
              padding-left: 1.2vw;
            }

            .up {
              color: #2dcd5e;
              position: relative;

              &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0.3vw;
                width: 0.5vw;
                height: 0.6vw;
                background: url("@/assets/screen/arrow_up.png");
                background-size: 100% 100%;
              }
            }

            .low {
              color: #fe005d;
              position: relative;

              &::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0.3vw;
                width: 0.5vw;
                height: 0.6vw;
                background: url("@/assets/screen/arrow_low.png");
                background-size: 100% 100%;
              }
            }
          }
        }
      }

      .col1 {
        // border: 1px solid red;
        width: 21vw;
        height: 100%;

        &-block1 {
          //   border: 1px solid red;
          font-size: 0.7vw;
          padding: 1vw 0 0;

          .flex {
            align-items: center;
          }

          .info-left {
            display: flex;
            flex-direction: column;
            gap: 0.8vw 0;
            flex: 1;

            &_icon {
              // border: 1px solid red;
              width: 2.5vw;
              height: 2.5vw;
            }

            .icon1 {
              background: url("@/assets/screen/app/col1-block1_icon1.png");
              background-size: 100% 100%;
            }

            .icon2 {
              background: url("@/assets/screen/app/col1-block1_icon2.png");
              background-size: 100% 100%;
            }

            .icon3 {
              background: url("@/assets/screen/app/col1-block1_icon3.png");
              background-size: 100% 100%;
            }

            &_cont {
              width: 100%;
              padding: 0.4vw 0.2vw 0;
              background: url("@/assets/screen/app/col1-block1_bg1.png");
              background-size: 100% 100%;

              div {
                height: 1.2vw;
                line-height: 1.2vw;
                // border: 1px solid red;
                padding: 0 0.5vw;
                display: flex;
                align-items: center;
                gap: 0 0.2vw;

                span {
                  color: #4095e5;
                  font-size: 0.9vw;
                  // font-weight: bold;
                }
              }
            }

            &_title {
              background: url("@/assets/screen/app/col1-block1_title1.png");
              background-size: 100% 100%;
            }
          }

          .info-right {
            display: flex;
            flex-direction: column;
            gap: 1vw 0;
            flex: 1;

            &_icon {
              // border: 1px solid red;
              width: 2.5vw;
              height: 2.5vw;
            }

            .icon1 {
              background: url("@/assets/screen/app/col1-block1_icon4.png");
              background-size: 100% 100%;
            }

            .icon2 {
              background: url("@/assets/screen/app/col1-block1_icon5.png");
              background-size: 100% 100%;
            }

            &_cont {
              width: 100%;
              padding: 0.4vw 0.2vw 0;
              background: url("@/assets/screen/app/col1-block1_bg2.png");
              background-size: 100% 100%;

              div {
                height: 2vw;
                line-height: 2vw;
                // border: 1px solid red;
                padding: 0 0.5vw;
                display: flex;
                align-items: center;
                gap: 0 0.2vw;

                span {
                  color: #4095e5;
                  font-size: 0.9vw;
                  display: inline-block;
                  // font-weight: bold;
                }
              }
            }

            &_title {
              background: url("@/assets/screen/app/col1-block1_title2.png");
              background-size: 100% 100%;
            }
          }
        }

        &-block2 {
          //   border: 1px solid red;
          height: 10vw;

          .echart-item {
            height: 7.5vw;
            margin-top: 0.5vw;
            flex: 1;
          }
        }

        &-block3 {
          .battery {
            margin: 0.5vw 0;
            background: url("@/assets/screen/app/col1-block3_bg.png");
            background-size: 100% 100%;
            height: 4vw;
            width: 100%;
            text-align: center;
            line-height: 3.5vw;
            font-size: 1.7vw;
          }
        }

        .table {
          height: 10vw;
          font-size: 0.65vw;
          padding-top: 0.1vw;
          margin-top: 0.5vw;

          &-row {
            display: flex;
            padding: 0.25vw 0;
            text-align: center;
            margin-top: 0.2vw;

            &.data:hover {
              background-color: #061d47;
            }

            &_name {
              width: 9vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &_number {
              width: 2vw;
            }

            &_type {
              width: 5vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &_count {
              width: 3vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }

        .table2 {
          height: 9.5vw;
          //   border: 1px solid red;
          background: url("@/assets/screen/app/table-bg.png");
          background-size: 100% 100%;
          padding-top: 0.1vw;
          font-size: 0.6vw;

          .table-row {
            padding: 0.2vw 0;
            height: 1.1vw;
            line-height: 0.7vw;
            // border: 1px solid red;
            margin-bottom: 0.2vw;
          }

          .opt-title {
            margin-top: 0.5vw;
          }
        }
      }

      .col2 {
        // border: 1px solid red;
        width: 37vw;
        height: 52vw;

        &-block1 {
          // border: 1px solid red;
          height: 26vw;
          position: relative;
          // border: 1px solid red;
          width: 30vw;
          margin: 4vw 5vw 1vw 4vw;
          background: url("@/assets/screen/middle_banner.png");
          background-size: 100% 100%;

          .item-opt2 {
            width: 8vw;
            height: 1.5vw;
            position: absolute;
            cursor: pointer;
            font-size: 0.6vw;

            div {
              position: absolute;
              // border: 1px solid red;
              width: 6.5vw;
              height: 1.5vw;
              text-align: center;
              line-height: 1.6vw;
            }

            span {
              position: absolute;
              right: 0.4vw;
              top: 0.5vw;
            }

            &.right {
              background: url("@/assets/screen/middle_frame_right.png");
              background-size: 100% 100%;

              div {
                right: 0;
              }

              span {
                left: 0.4vw;
              }
            }

            &.left {
              background: url("@/assets/screen/middle_frame_left.png");
              background-size: 100% 100%;
            }

            &.opt1 {
              // display: none;
              // border: 1px solid red;
              top: -1.5vw;
              left: -4vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_left_top_line.png");
                background-size: 100% 100%;
                width: 4vw;
                height: 3vw;
                right: -3.9vw;
                top: -0.4vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_left_sel.png");
                background-size: 100% 100%;

                &::before {
                  right: -4.6vw;
                  top: -1.1vw;
                  width: 5vw;
                  height: 4vw;
                  background: url("@/assets/screen/middle_left_top_line_sel.png");
                  background-size: 100% 100%;
                }
              }
            }

            &.opt2 {
              // display: none;
              // border: 1px solid red;
              right: 2.5vw;
              top: -3.2vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_right_top_line.png");
                background-size: 100% 100%;
                width: 4vw;
                height: 3vw;
                left: -3.9vw;
                top: -0.6vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_right_sel.png");
                background-size: 100% 100%;

                &::before {
                  background: url("@/assets/screen/middle_right_top_line_sel.png");
                  background-size: 100% 100%;
                  width: 5vw;
                  height: 4vw;
                  left: -4.7vw;
                  top: -1vw;
                }
              }
            }

            &.opt3 {
              // border: 1px solid red;
              top: 5.8vw;
              left: -4vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_left_middle_line.png");
                background-size: 100% 100%;
                width: 3vw;
                height: 4vw;
                right: -0.7vw;
                top: 1.4vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_left_sel.png");
                background-size: 100% 100%;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_left_middle_line_sel.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 5vw;
                  right: -1.1vw;
                  top: 1.6vw;
                }
              }
            }

            &.opt4 {
              // border: 1px solid red;
              right: -3vw;
              bottom: 10.5vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_right_middle_line.png");
                background-size: 100% 100%;
                width: 3vw;
                height: 4vw;
                left: 3vw;
                top: -4vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_right_sel.png");
                background-size: 100% 100%;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_right_middle_line_sel.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 5vw;
                  left: 3vw;
                  top: -4.6vw;
                }
              }
            }

            &.opt5 {
              // border: 1px solid red;
              bottom: -1vw;
              left: -4vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_left_bottom_line.png");
                background-size: 100% 100%;
                width: 4vw;
                height: 3vw;
                right: -4vw;
                top: -1vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_left_sel.png");
                background-size: 100% 100%;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_left_bottom_line_sel.png");
                  background-size: 100% 100%;
                  width: 5vw;
                  height: 4vw;
                  right: -4.6vw;
                  top: -0.9vw;
                }
              }
            }

            &.opt6 {
              // border: 1px solid red;
              right: -2vw;
              bottom: -0.5vw;

              &::before {
                position: absolute;
                content: "";
                background: url("@/assets/screen/middle_right_bottom_line.png");
                background-size: 100% 100%;
                width: 4vw;
                height: 1.8vw;
                left: -3.9vw;
                top: -1.5vw;
              }

              &.active {
                background: url("@/assets/screen/middle_frame_right_sel.png");
                background-size: 100% 100%;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_right_bottom_line_sel.png");
                  background-size: 100% 100%;
                  width: 5.5vw;
                  height: 4vw;
                  left: -5.5vw;
                  top: -3.4vw;
                }
              }
            }
          }
        }

        &-block2 {
          // border: 1px solid red;
          display: grid;
          grid-template-columns: 18.25vw 18.25vw;
          // grid-template-rows: 11vw;
          grid-gap: 0 0.5vw;

          .grid-item {
            display: flex;
            flex-direction: column;

            .block {
              margin-top: 0.5vw;
              height: 6.8vw;
              padding: 0.5vw 0.2vw;

              &-right {
                font-size: 0.6vw !important;
                display: flex;
                flex-direction: column;
                justify-content: space-between;
              }
            }
          }

          .network {
            .block {
              padding: 0vw 0.2vw 0.7vw;
            }

            .block2 {
              background: url("@/assets/screen/app/address-bg.png");
              background-size: 100% 100%;
            }

            .address {
              // border: 1px solid red;
              height: 4vw;
              margin-top: 0.2vw;
              font-size: 0.6vw;
              display: flex;
              flex-direction: column;
              justify-content: center;
              align-items: center;
              gap: 0.3vw 0;

              div {
                font-size: 0.7vw;
                color: #fff;

                span {
                  font-size: 1.2vw;
                  letter-spacing: 0.1vw;
                }
              }
            }
          }
        }
      }

      .col3 {
        // border: 1px solid red;
        width: 20vw;
        height: 100%;

        &-block3 {
          .block {
            padding: 0vw 0.2vw 0.5vw;
            margin-top: 0.5vw;
            height: 7vw;
          }

          .block2 {
            height: 8.8vw;
          }
        }
      }

      .col4 {
        // border: 1px solid red;
        width: 20vw;
        height: 100%;

        &-block1 {
          margin-top: 1vw;
          width: 100%;
          display: flex;
          flex-direction: column;
          gap: 0.5vw 0;
          padding: 0.5vw 1vw;
          background: url("@/assets/screen/app/col4-block1_bg.png");
          background-size: 100% 100%;

          &_item {
            // border: 1px solid red;
            padding: 0 0.45vw 0 4.5vw;
            font-size: 0.6vw;

            .row {
              display: flex;
              justify-content: space-between;
              height: 1.5vw;
              line-height: 1.5vw;

              &-center {
                color: #fff;
                font-size: 0.7vw;

                span {
                  font-size: 0.7vw;
                  color: #b0c0e6;
                  display: inline-block;
                  padding-left: 0.1vw;
                }
              }

              .amount {
                color: #4095e5;

                span {
                  font-size: 0.8vw;
                  font-weight: bold;
                }
              }

              .icon {
                // border: 1px solid red;
                position: relative;
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 0 0.2vw;

                &::before {
                  content: "";
                  width: 1.1vw;
                  height: 0.9vw;
                }

                &.up {
                  color: #fe005d;

                  &::before {
                    background: url("@/assets/screen/app/trend-up.png");
                    background-size: 100% 100%;
                  }
                }

                &.low {
                  color: #2dcd5e;

                  &::before {
                    background: url("@/assets/screen/app/trend-low.png");
                    background-size: 100% 100%;
                  }
                }
              }
            }
          }

          .item1 {
            background: url("@/assets/screen/app/col4-block1_icon1.png");
            background-size: 100% 100%;
          }

          .item2 {
            background: url("@/assets/screen/app/col4-block1_icon2.png");
            background-size: 100% 100%;
          }

          .item3 {
            background: url("@/assets/screen/app/col4-block1_icon3.png");
            background-size: 100% 100%;
          }
        }

        &-block2 {
          // border: 1px solid red;
          height: 19.6vw;

          .block {
            margin-top: 0.5vw;
            height: 8.27vw;
          }
        }

        &-block3 {
          // border: 1px solid red;
          height: 20vw;

          .block {
            margin-top: 0.5vw;
            padding: 0.5vw 0.2vw;

            &-right {
              display: flex;
              flex-direction: column;
              justify-content: space-between;
              font-size: 0.6vw !important;

              .item-opt {
                height: 3vw;
              }
            }
          }

          .block2 {
            height: 7.5vw;
            padding: 0.2vw 0.2vw;
          }
        }
      }
    }
  }
}
</style>