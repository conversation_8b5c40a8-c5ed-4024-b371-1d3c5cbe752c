import request from "@/utils/request";

// 查询岗位列表
export function listPost(params) {
  return request({
    url: "/system/post/list",
    method: "get",
    params,
  });
}

// 下载岗位导入模板
export function downloadPostTemp() {
  return request({
    url: "/system/post/download/post-template",
    method: "get",
    responseType: "blob", // 确保设置响应类型为 blob
  });
}

// 批量导入
export function importPost(data) {
  return request({
    url: "/system/post/importData",
    method: "post",
    data,
    /* headers: {
      // 'Content-Type': 'multipart/form-data'  // 删除这行
    } */
  });
}

// 批量导出
export function exportPost(data) {
  return request({
    url: "/system/post/export",
    method: "post",
    data: data, // 直接传递数组
    responseType: "blob",
  });
}

// 新增岗位
export function addPost(data) {
  return request({
    url: "/system/post/addPost",
    method: "post",
    data,
  });
}

// 修改岗位
export function editPost(data) {
  return request({
    url: "/system/post/editPost",
    method: "post",
    data,
  });
}

// 删除岗位
export function deletePost(data) {
  return request({
    url: "/system/post/deletePost",
    method: "post",
    data,
  });
}
