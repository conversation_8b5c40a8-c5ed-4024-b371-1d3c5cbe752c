<template>
  <div class="unify">
    <div class="unify-main">
      <div class="unify-main_header">
        <div class="unify-main_header-title"></div>
        <div class="unify-main_header-btns">
          <div
            class="unify-main_header-btns_btn"
            :class="[
              curBtn == index ? 'active' : '',
              index > 1 ? 'right-btn' : 'left-btn',
            ]"
            v-for="(item, index) in headerBtns"
            :key="index"
            @click="curBtn = index"
          >
            {{ item }}
          </div>
        </div>
        <div class="unify-main_header-date">
          <div class="left-date">
            {{ proxy.parseTime(curTime, "{h}:{i}:{s}") }}
          </div>
          <div class="right-date">
            {{ proxy.parseTime(curTime, "{y}.{m}.{d}") }}
          </div>
        </div>
      </div>
      <div :class="curBtn != 2 ? 'unify-main_chart' : ''">
        <page1 v-if="curBtn == 0" />
        <page2 v-if="curBtn == 1" />
        <page3 v-if="curBtn == 2" />
      </div>
    </div>
  </div>
</template>
  
  <script setup name="unifyIndex">
import page1 from "./component/app/page1.vue";
import page2 from "./component/app/page2.vue";
import page3 from "./component/app/page3.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  headerBtns: ["资产数据大屏", "运维数据大屏", "集控孪生大屏"],
  curBtn: 0,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
});
const { headerBtns, curBtn, timer, timer2, curTime } = toRefs(state);

onMounted(() => {
  document.addEventListener("selectstart", function (e) {
    e.preventDefault();
  });
  state.timer = setInterval(() => {
    state.curTime += 1000;
  }, 1000);
  // state.timer2 = setInterval(() => {}, 60000);
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.timer2);
  document.removeEventListener("selectstart", function (e) {
    e.preventDefault();
  });
});
</script>
  
  <style lang="scss" scoped>
.unify {
  width: 100%;
  height: 100%;
  background-color: #020d20;
  // border: 1px solid red;
  color: #b0c0e6;
  overflow-y: hidden;

  &-main {
    width: 100vw;
    height: 56vw;
    // border: 1px solid red;
    // background: url("@/assets/screen/bg.png");
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/bg.png");
    background-size: 100% 100%;

    &_header {
      z-index: 11;
      position: relative;
      width: 100%;
      height: 4.2vw;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/header_bg.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      padding-bottom: 0.5vw;

      &-title {
        display: flex;
        padding-top: .3vw;
        &::before {
          content: "";
          // border: 1px solid red;
          width: 12vw;
          height: 4vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/header.png");
          background-size: 100% 100%;
        }
      }

      &-btns {
        position: absolute;
        display: flex;
        width: 100%;
        // border: 1px solid red;
        bottom: -0.2vw;
        font-size: 0.85vw;

        &_btn {
          // border: 1px solid red;
          color: #fff;
          cursor: pointer;
          text-align: center;
          width: 8.5vw;
          height: 2.2vw;
          line-height: 2.2vw;
          position: relative;
          overflow: hidden;

          &.left-btn {
            left: 21.2vw;
            padding-left: 0.4vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_left.png");
            background-size: 100% 100%;
            &.active {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_leftSel.png");
              background-size: 100% 100%;
            }
          }

          &.right-btn {
            left: 44.7vw;
            padding-right: 0.4vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_right.png");
            background-size: 100% 100%;
            &.active {
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/headerTab_rightSel.png");
              background-size: 100% 100%;
            }
          }

          &:hover {
            // transform: translateY(-2px);
          }
        }
      }

      &-date {
        position: absolute;
        left: 1vw;
        bottom: 0;
        text-align: right;
        .left-date {
          font-size: 1.7vw;
        }
        .right-date {
          font-size: 0.85vw;
        }
      }
    }

    &_chart {
      padding: 0 0.5vw;
    }
  }
}
</style>