{"compilerOptions": {"target": "es5", "module": "esnext", "lib": ["es2015", "dom"], "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "sourceMap": true, "baseUrl": "..", "paths": {"@/*": ["src/*"]}}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "tests/**/*.vue"], "exclude": ["node_modules", "dist"]}