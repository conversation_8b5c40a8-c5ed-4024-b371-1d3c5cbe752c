import request from '@/utils/request'

//应用类分类占比
export function queSoftwareRatio(params) {
    return request({
        url: '/system/device/queSoftwareRatio',
        method: 'get',
        params
    })
}

//查询所有学校的学生数量和
export function queStudentNum(params) {
    return request({
        url: '/system/tenant/queStudentNum',
        method: 'get',
        params
    })
}

//市级 学科占比
export function queSubjectRatio(params) {
    return request({
        url: '/system/device/queSubjectRatio',
        method: 'get',
        params
    })
}

//市级 多媒体教学率
export function queDeviceRatio(params) {
    return request({
        url: '/system/device/queDeviceRatio',
        method: 'get',
        params
    })
}

//男女比例
export function queTeacharSexRatio(params) {
    return request({
        url: '/system/teacher/queTeacharSexRatio',
        method: 'get',
        params
    })
}

//年龄比例
export function queTeacherAgeRatio(params) {
    return request({
        url: '/system/teacher/queTeacherAgeRatio',
        method: 'get',
        params
    })
}

//小学-初中比例
export function queDeviceTenantRatio(params) {
    return request({
        url: '/system/teacher/queDeviceTenantRatio',
        method: 'get',
        params
    })
}

//市级 学校-设备-老师-学生-设备运行比例
export function queCityDeviceInfo(params) {
    return request({
        url: '/system/device/queCityDeviceInfo',
        method: 'get',
        params
    })
}

//月度全区设备使用时长
export function queTenantDeviceTime(params) {
    return request({
        url: '/system/device/queTenantDeviceTime',
        method: 'get',
        params
    })
}

//市级 近7天设备交互时长
export function queTenantDeviceWeekRunTime(params) {
    return request({
        url: '/system/device/queTenantDeviceWeekRunTime',
        method: 'get',
        params
    })
}

//市级 学校平均宽带
export function queTenantNetwork(params) {
    return request({
        url: '/system/device/queTenantNetwork',
        method: 'get',
        params
    })
}