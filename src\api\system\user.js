import request from "@/utils/request";
import { parseStrEmpty } from "@/utils/ruoyi";

// 校验数职工是否上限
export function checkTeacherLimit(data) {
  return request({
    url: "/system/user/check/teacher/limit",
    method: "post",
    noMsg: true,
    data,
  });
}

// 获取绑定机构二维码信息
export function getCorpInfo(data) {
  return request({
    url: "/system/user/find/corp/info",
    method: "post",
    data,
  });
}

// 下载安装包
export function downloadPackage(params) {
  return request({
    url: "/system/package/download",
    method: "get",
    params,
    responseType: "blob",
  });
}

// 查看安装包列表
export function getPackageList(params) {
  return request({
    url: "/system/package/list",
    method: "get",
    params,
  });
}

// 修改解锁提示语
export function editCustomPrompt(data) {
  return request({
    url: "/system/config/customPrompt",
    method: "put",
    data,
  });
}

// 查看解锁提示语
export function getCustomPrompt(params) {
  return request({
    // url: "/system/config/configKey/customPrompt",
    url: "/system/config/customPrompt/info",
    method: "get",
    params,
  });
}

// 新增自定义解锁提示
export function addCustomPrompt(data) {
  return request({
    url: "/system/config/customPrompt",
    method: "post",
    data,
  });
}

// 查看教职工数量上限
export function teacherLimit() {
  return request({
    url: "/system/user/teacher/limit",
    method: "get",
  });
}

// 删除教职工
export function delTeacher(params) {
  return request({
    url: "/system/user/teacherStaff/delete",
    method: "delete",
    params,
  });
}

// 编辑教职工
export function updateTeacher(data) {
  return request({
    url: "/system/user/update",
    method: "put",
    data,
  });
}

// 新增教职工
export function addTeacher(data) {
  return request({
    url: "/system/user/teacherStaff/add",
    method: "post",
    data,
  });
}

// 导出教职工模板
export function teacherTemplate(data) {
  return request({
    url: "/system/user/export/model",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 导出教职工模板
export function teacherTemplate2(data) {
  return request({
    url: "/system/fileModel/downFile",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 导出教职工
export function exportTeacherStaff(data) {
  return request({
    url: "/system/user/exportTeacherStaff",
    method: "post",
    responseType: "blob",
    // isKey: true,
    data,
  });
}

// 获取导出教职工数量
export function getTeacherExportNum(params) {
  return request({
    url: "/system/user/teacherStaff/download/num",
    method: "get",
    params,
  });
}

// 查询教职工列表
export function getTeacherList(data) {
  return request({
    url: "/system/user/teacherStaff/list",
    method: "post",
    data,
  });
}

// 设备使用申请
export function addSchoolDeviceUse(data) {
  return request({
    url: "/system/schoolDeviceUse/add",
    method: "post",
    data,
  });
}

// 根据设备编码获取使用者
export function getDevicePermissions(params) {
  return request({
    url: "/system/device/permissions",
    method: "get",
    params,
  });
}

// 删除设备使用者
export function delSchoolDeviceUse(data) {
  return request({
    url: "/system/schoolDeviceUse/delete",
    method: "post",
    data,
  });
}
// 查询用户列表
export function getUserList(data) {
  return request({
    url: "/system/user/queList",
    method: "post",
    data,
  });
}

// 查询用户列表
export function listUser(data) {
  return request({
    url: "/system/user/listUser",
    method: "post",
    data,
  });
}

// 查询用户详细
export function getUser(userId) {
  return request({
    url: "/system/user/" + parseStrEmpty(userId),
    method: "get",
  });
}

// 新增用户
export function addUser(data) {
  return request({
    url: "/system/user/add",
    method: "post",
    data: data,
  });
}

// 修改用户
export function updateUser(data) {
  return request({
    url: "/system/user",
    method: "put",
    data: data,
  });
}

// 删除用户
export function delUser(userId) {
  return request({
    url: "/system/user/" + userId,
    method: "delete",
  });
}

// 用户密码重置
export function resetUserPwd(userId, password) {
  const data = {
    userId,
    password,
  };
  return request({
    url: "/system/user/resetPwd",
    method: "put",
    data: data,
  });
}

// 用户状态修改
export function changeUserStatus(userId, status) {
  const data = {
    userId,
    status,
  };
  return request({
    url: "/system/user/changeStatus",
    method: "put",
    data: data,
  });
}

// 查询用户个人信息
export function getUserProfile() {
  return request({
    url: "/system/user/profile",
    method: "get",
  });
}

// 修改用户个人信息
export function updateUserProfile(data) {
  return request({
    url: "/system/user/profile",
    method: "put",
    data: data,
  });
}

// 用户密码重置
export function updateUserPwd(oldPassword, newPassword) {
  const data = {
    oldPassword,
    newPassword,
  };
  return request({
    url: "/system/user/profile/updatePwd",
    method: "put",
    params: data,
  });
}

// 用户头像上传
export function uploadAvatar(data) {
  return request({
    url: "/system/user/profile/avatar",
    method: "post",
    headers: { "Content-Type": "multipart/form-data" },
    isKey: true,
    data: data,
  });
}

// 查询授权角色
export function getAuthRole(userId) {
  return request({
    url: "/system/user/authRole/" + userId,
    method: "get",
  });
}

// 保存授权角色
export function updateAuthRole(data) {
  return request({
    url: "/system/user/authRole",
    method: "put",
    params: data,
  });
}

// 查询部门下拉树结构
export function deptTreeSelect() {
  return request({
    url: "/system/user/deptTree",
    method: "get",
  });
}

// 加入运维人员 /system/user/addMaintain
export function addMaintain(data) {
  return request({
    url: "/system/user/addMaintain",
    method: "post",
    data: data,
  });
}

// 根据用户角色获取用户列表
export function getUserByRole(data) {
  return request({
    url: "/system/user/role/list",
    method: "post",
    data: data,
  });
}

// 批量导出用户 /system/user/export
export function exportUser(data) {
  return request({
    url: "/system/user/export",
    method: "post",
    responseType: "blob",
    data,
  });
}

// 下载用户导入模板 /system/user/download/importuser-template
export function downloadUserImportTemplate2() {
  return request({
    url: "/system/user/download/importuser-template",
    method: "get",
    responseType: "blob",
  });
}

// 下载用户导入模板 /system/user/download/importuser-template
export function downloadUserImportTemplate() {
  return request({
    url: "/system/user/manage/export/model",
    method: "post",
    responseType: "blob",
  });
}


// 批量导入用户 /system/user/importUserData
export function importUserData(params, data) {
  return request({
    url: "/system/user/importUserData",
    method: "post",
    data,
    params,
  });
}
