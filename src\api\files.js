import request from "@/utils/request";

// 下载安装包
export function downloadPackage(params) {
  return request({
    url: "/package/download",
    method: "get",
    params,
    headers: { "X-CORP-ID": "0" },
  });
}

// 安装包列表
export function getPackageList(params) {
  return request({
    url: "/package/list",
    method: "get",
    params,
    headers: { "X-CORP-ID": "0" },
  });
}

// 查询文件管理模块版本列表
export function getSchoolClientList(data) {
  return request({
    url: "/system/schoolClient/listByCondition",
    method: "post",
    data,
  });
}

// 查询大版本号 /system/schoolClient/listMainVersion/win
export function getMainVersionList(data, version) {
  return request({
    url: "/system/schoolClient/listMainVersion/" + version,
    method: "get",
    data,
  });
}

// 发布大版本补丁文件 /system/schoolClient/releasePatchFile/win/3.0
export function releasePatchFile(data, version, number) {
  return request({
    url: "/system/schoolClient/releasePatchFile/" + version + "/" + number,
    method: "post",
    data,
  });
}

// 查询文件列表 /system/schoolFileRelease/list
export function getSchoolFileReleaseList() {
  return request({
    url: "/system/schoolFileRelease/list",
    method: "get",
  });
}

// 发布文件 /system/schoolFileRelease/release/3
export function getSchoolFileReleaseRelease(id) {
  return request({
    url: "/system/schoolFileRelease/release/" + id,
    method: "get",
  });
}

// 获取总安装次数 /system/schoolClient/getAllInsCount
export function getAllInsCount() {
  return request({
    url: "/system/schoolClient/getAllInsCount",
    method: "get",
  });
}
