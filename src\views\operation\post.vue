<template>
  <div class="post app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">岗位管理</div>
      </template>
      <!-- 搜索区域 -->
      <el-form
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        class="search-list"
      >
        <el-form-item prop="name">
          <el-input
            v-model.trim="queryParams.postName"
            placeholder="请输入岗位名称搜索"
            clearable
            @keyup.enter="handleQuery"
            @clear="resetQuery"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item prop="number">
          <el-input
            v-model.trim="queryParams.postCode"
            placeholder="请输入岗位编号搜索"
            clearable
            @keyup.enter="handleQuery"
            @clear="resetQuery"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            clearable
            style="width: 200px"
            @change="handleQuery"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="dateRange">
          <el-date-picker
            v-model="queryParams.dateRange"
            type="daterange"
            placeholder="创建时间范围筛选"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            @change="setDateTime"
            style="width: 370px"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区 -->
      <div class="mb12">
        <el-button plain icon="Plus" type="primary" @click="handleAdd"
          >新建岗位</el-button
        >
        <el-button plain icon="Select" type="info" @click="handleSelectAll"
          >全选</el-button
        >
        <el-button
          plain
          icon="Delete"
          type="danger"
          :disabled="disableBatchDelete"
          @click="handleBatchDelete"
          >批量删除</el-button
        >
        <el-button plain icon="Upload" type="warning" @click="handleBatchImport"
          >批量导入</el-button
        >
        <el-button
          plain
          icon="Download"
          type="success"
          :disabled="disableBatchDelete"
          @click="handleBatchExport"
          >批量导出</el-button
        >
      </div>

      <!-- 表格区域 -->
      <div class="post-main">
        <el-table
          ref="tableRef"
          v-loading="loading"
          :data="tableList"
          border
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column
            label="岗位编号"
            prop="postCode"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="岗位名称"
            prop="postName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            label="备注"
            prop="remark"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column label="状态" align="center" min-width="100">
            <template #default="scope">
              <el-tag :type="scope.row.status == 0 ? 'success' : 'danger'">
                {{ scope.row.status == 0 ? "启用" : "停用" }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="创建时间" prop="createTime" min-width="160" />
          <el-table-column
            label="操作"
            width="150"
            align="center"
            fixed="right"
          >
            <template #default="scope">
              <el-button link type="primary" @click="handleEdit(scope)">
                <el-icon><Edit /></el-icon>
              </el-button>
              <el-button link type="danger" @click="handleDel(scope)">
                <el-icon><Delete /></el-icon>
              </el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 弹窗表单 -->
    <el-dialog
      class="custom-dialog"
      v-model="dialogVisible"
      :title="title"
      width="500"
    >
      <el-form
        ref="postRef"
        :model="postInfo"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent
      >
        <el-form-item
          v-if="title === '编辑岗位'"
          label="岗位编号"
          prop="postCode"
        >
          <el-input v-model="postInfo.postCode" disabled />
        </el-form-item>
        <el-form-item label="岗位名称" prop="postName">
          <el-input
            v-model.trim="postInfo.postName"
            maxlength="10"
            placeholder="请输入岗位名称"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <div style="display: flex; align-items: center">
            <el-switch
              v-model="postInfo.status"
              :active-value="0"
              :inactive-value="1"
            />
            <div
              style="margin-left: 10px"
              :style="{ color: postInfo.status ? '' : '#4095e5' }"
            >
              {{ postInfo.status ? "停用" : "启用" }}
            </div>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model.trim="postInfo.remark"
            type="textarea"
            :rows="3"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" @click="handleSubmit">
            {{ title === "新建岗位" ? "新建" : "保存" }}
          </el-button>
        </div>
      </template>
    </el-dialog>
    <!-- 修改导入对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="importDialogVisible"
      title="批量导入"
      width="400px"
    >
      <div class="import-result" v-if="importResult.show">
        <div class="result-header">
          <span class="result-title">本次导入：</span>
          <span class="success">成功：{{ importResult.success }}条</span>
          <span class="error">失败：{{ importResult.error }}条</span>
        </div>
        <div class="error-reason" v-if="importResult.errorReason">
          <div>失败原因如下：</div>
          <div>{{ importResult.errorReason }}</div>
        </div>
      </div>
      <div class="import-steps" v-else>
        <div class="step">
          <div class="step-title">第一步：下载模板</div>
          <el-button type="primary" @click="downloadTemplate"
            >点击下载</el-button
          >
        </div>
        <div class="step">
          <div class="step-title">第二步：填写信息后上传</div>
          <el-upload
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="true"
            accept=".xlsx,.xls"
            :limit="1"
            :before-remove="
              () => {
                state.importFile = null;
                return true;
              }
            "
          >
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">{{
            importResult.show ? "关闭" : "返回"
          }}</el-button>
          <el-button
            v-if="!importResult.show"
            type="primary"
            @click="handleImport"
            >导入</el-button
          >
          <el-button v-else type="primary" @click="handleRetry"
            >重新上传</el-button
          >
        </div>
      </template>
    </el-dialog>
    <!-- 添加导出确认对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="exportDialogVisible"
      title="提示"
      width="300px"
      align-center
    >
      <span>确定批量导出?</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelExport">返回</el-button>
          <el-button type="primary" @click="confirmExport">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="post">
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  watch,
  computed,
} from "vue";
import { downloadBlob } from "@/utils";
import {
  listPost,
  addPost,
  editPost,
  deletePost,
  importPost,
  exportPost,
  downloadPostTemp,
} from "@/api/system/post_new";
import { debounce } from "@/utils/debounce";
import { downloadFile } from "@/utils/fileUpload";

// import * as XLSX from 'xlsx'

const route = useRoute();
const { proxy } = getCurrentInstance();
const postRef = ref();
const state = reactive({
  title: "新增岗位",
  dialogVisible: false,
  total: 0,
  loading: false,
  selectedRows: [],
  postInfo: {
    postId: "",
    postCode: "",
    postName: "",
    status: 0,
    createTime: "",
    remark: "",
  },
  tableList_all: [],
  tableList: [],
  statusList: [
    { label: "启用", value: 0, type: "success" },
    { label: "停用", value: 1, type: "danger" },
  ],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    postName: "",
    postCode: "",
    status: "",
    beginTime: "",
    endTime: "",
  },
  rules: {
    postName: [
      { required: true, message: "岗位名称不能为空", trigger: "blur" },
    ],
    // remark: [{ required: true, message: '岗位备注不能为空', trigger: 'blur' }],
  },
  importDialogVisible: false,
  importFile: null,
  importResult: {
    show: false,
    success: 0,
    error: 0,
    errorReason: "",
  },
  exportDialogVisible: false,
});

const {
  title,
  rules,
  postInfo,
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  statusList,
  loading,
  total,
  importDialogVisible,
  importResult,
  exportDialogVisible,
  selectedRows,
} = toRefs(state);

const disableBatchDelete = computed(() => {
  return state.selectedRows.length === 0;
});

// 同步更改筛选条件的开始时间和结束时间
/* function setDateTime(val) {
    if (val) {
        state.queryParams.beginTime = val[0]
        state.queryParams.endTime = val[1]
    } else {
        state.queryParams.beginTime = ''
        state.queryParams.endTime = ''
    }
} */

const setDateTime = (val) => {
  if (val) {
    // 将日期格式转换为 yyyy-MM-dd 格式
    queryParams.value.beginTime = val[0];
    queryParams.value.endTime = val[1];
  } else {
    queryParams.value.beginTime = "";
    queryParams.value.endTime = "";
  }
  handleQuery();
};

function handleAdd() {
  state.title = "新增岗位";

  let newPostCode = "**********"; // 默认编号

  if (state.tableList && state.tableList.length > 0) {
    const lastPostCode = state.tableList[state.tableList.length - 1].postCode;
    const nextNum = (parseInt(lastPostCode.split("GW")[1]) + 1)
      .toString()
      .padStart(8, "0");
    newPostCode = `GW${nextNum}`;
  }

  state.postInfo = {
    postCode: newPostCode,
    postName: "",
    status: 0,
    createTime: "",
    remark: "",
  };
  state.dialogVisible = true;
}

function handleEdit({ row, $index }) {
  state.title = "编辑岗位";
  state.curIdx = $index;
  state.postInfo = {
    postId: row.postId,
    postCode: row.postCode,
    postName: row.postName,
    status: Number(row.status),
    remark: row.remark,
  };
  state.dialogVisible = true;
}

// 添加节流函数
const throttle = (fn, delay = 1000) => {
  let timer = null;
  let isExecuting = false; // 添加执行标志位

  return async function (...args) {
    if (timer || isExecuting) return; // 如果正在执行或等待中，直接返回

    isExecuting = true; // 设置执行标志
    timer = setTimeout(() => {
      timer = null;
    }, delay);

    try {
      await fn.apply(this, args);
    } finally {
      isExecuting = false; // 确保执行完成后重置标志位
    }
  };
};

// 修改提交函数
const handleSubmit = throttle(async () => {
  try {
    const valid = await postRef.value.validate();
    if (valid) {
      const addSubmitData = {
        postName: state.postInfo.postName,
        remark: state.postInfo.remark,
        status: state.postInfo.status,
      };
      const editSubmitData = {
        postId: state.postInfo.postId,
        postName: state.postInfo.postName,
        remark: state.postInfo.remark,
        status: state.postInfo.status,
      };

      const request =
        state.title === "新增岗位"
          ? addPost(addSubmitData)
          : editPost(editSubmitData);

      await request;
      proxy.$modal.msgSuccess("操作成功");
      handleCancel();
      await getList();
    }
  } catch (error) {
    // console.error('提交出错:', error);
  }
}, 1500); // 设置1.5秒的节流时间

function handleCancel() {
  postRef.value.resetFields();
  state.dialogVisible = false;
}

function handleDel({ row }) {
  proxy.$modal
    .confirm(`确定要删除"${row.postName}"岗位吗？`)
    .then(() => {
      // 将单个 id 包装成数组传递
      const postIds = [row.postId];
      console.log("postIds", postIds);

      deletePost(postIds)
        .then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("删除成功");
            getList();
          } else {
            // proxy.$modal.msgError(res.msg)
          }
        })
        .catch((error) => {
          // console.error('删除失败:', error)
          // proxy.$modal.msgError('删除失败')
        });
    })
    .catch(() => {}); // 用户点击取消时不做任何操作
}

function getList() {
  state.loading = true;
  console.log(state.queryParams, "查询参数");

  return new Promise((resolve, reject) => {
    listPost(state.queryParams)
      .then((res) => {
        console.log(res, "岗位列表数据");

        if (res.code == 200) {
          state.tableList = res.data.rows;
          state.total = res.data.total || 0;
          resolve(res);
        } else {
          state.tableList = [];
          state.total = 0;
          proxy.$modal.msgError(res.msg);
          reject(res);
        }
      })
      .catch((error) => {
        // console.error('获取岗位列表失败:', error)
        state.tableList = [];
        state.total = 0;
        // proxy.$modal.msgError('获取岗位列表失败')
        reject(error);
      })
      .finally(() => {
        state.loading = false;
      });
  });
}

getList();

// 查询按钮操作
const handleQuery = debounce(() => {
  state.queryParams.current = 1;
  getList();
}, 200);

function handleBatchDelete() {
  if (state.selectedRows.length === 0) {
    proxy.$modal.msgWarning("请至少选择一条记录");
    return;
  }
  const postIds = state.selectedRows.map((row) => row.postId);
  console.log(postIds, "批量删除传递的id");
  proxy.$modal
    .confirm("确定批量删除？")

    .then(() => {
      // 提取选中行的 postId，组成数组
      const postIds = state.selectedRows.map((row) => row.postId);
      console.log(postIds, "批量删除传递的id");

      // 直接传递 id 数组
      return deletePost(postIds);
    })
    .then((res) => {
      if (res.code === 200) {
        proxy.$modal.msgSuccess("批量删除成功");
        getList();
      } else {
        proxy.$modal.msgError(res.msg);
      }
    })
    .catch((error) => {
      // console.error('批量删除失败:', error)
      // proxy.$modal.msgError('批量删除失败')
    });
}

function handleBatchImport() {
  state.importDialogVisible = true;
  // 重置导入结果
  state.importResult = {
    show: false,
    success: 0,
    error: 0,
    errorReason: "",
  };
}

function handleBatchExport() {
  if (state.selectedRows.length === 0) {
    proxy.$modal.msgWarning("请至少选择一条数据");
    return;
  }
  proxy.$modal.confirm("确定批量导出？").then(() => confirmExport());
}

function handleSelectionChange(selection) {
  if (selection.length === 0) {
    isAllSelected.value = false;
  }
  // 如果是全选状态，保持所有数据的选中状态
  state.selectedRows = isAllSelected.value ? allData.value : selection;
}

// 重置按钮操作
/* function resetQuery() {
//   queryRef.value?.resetFields()

  handleQuery()
} */

const resetQuery = debounce(() => {
  (queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    postName: "",
    postCode: "",
    status: "",
    beginTime: "",
    endTime: "",
  }),
    getList();
}, 200);

// 添加 queryRef 的引用
const queryRef = ref();

// 下载模板
function downloadTemplate() {
  downloadPostTemp()
    .then((res) => {
      if (res instanceof Blob) {
        const blob = res;
        const objUrl = window.URL.createObjectURL(blob);
        console.log(blob);

        const a = document.createElement("a");
        a.setAttribute("href", objUrl);
        a.setAttribute("download", "岗位信息模板.xls");
        a.click();
        window.URL.revokeObjectURL(objUrl);

        proxy.$modal.msgSuccess("模板下载成功");
      } else {
        // console.error('下载失败：返回格式错误');
        proxy.$modal.msgError("模板下载失败");
      }
    })
    .catch((error) => {
      // console.log('模板下载失败:', error);
      proxy.$modal.msgError("模板下载失败");
    });
}

// 处理文件选择
function handleFileChange(file) {
  if (!file) {
    proxy.$modal.msgError("请选择文件");
    return false;
  }

  // 验证文件类型
  const isExcel =
    file.raw.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.raw.type === "application/vnd.ms-excel";
  if (!isExcel) {
    proxy.$modal.msgError("只能上传 Excel 文件!");
    return false;
  }

  // 保存文件对象，注意这里直接保存 file 而不是 file.raw
  state.importFile = file;
  return true;
}

// 处理文件导入
function handleImport() {
  if (!state.importFile) {
    proxy.$modal.msgWarning("请先选择要导入的文件");
    return;
  }
  /*  */
  const formData = new FormData();
  formData.append("file", state.importFile.raw);
  console.log("导入文件参数:", formData);

  importPost(formData)
    .then((res) => {
      console.log("导入响应:", res);
      if (res.code === 200) {
        state.importResult = {
          show: true,
          success: res.data?.successCount || 0,
          error: res.data?.errorCount || 0,
          errorReason: formatErrorReason(res.data?.errorInfoList),
        };
        if (res.data?.successCount > 0) {
          getList();
        }
        proxy.$modal.msgSuccess("导入完成");
      } else {
        proxy.$modal.msgError(res.msg || "导入失败");
      }
    })
    .catch((error) => {
      console.error("导入失败:", error);
      proxy.$modal.msgError("导入失败");
    });
}

// 添加一个格式化错误信息的函数
function formatErrorReason(errorList) {
  if (!errorList || errorList.length === 0) return "";

  return errorList
    .map((error) => {
      return `第${error.row}行: ${error.cause}`;
    })
    .join("\n");
}

// 添加关闭处理函数
function handleClose() {
  state.importDialogVisible = false;
  state.importResult.show = false;
  state.importFile = null;
}

// 添加重试处理函数
function handleRetry() {
  state.importResult.show = false;
  state.importFile = null;
}

// 确认导出
function confirmExport() {
  const postCodes = state.selectedRows.map((row) => row.postCode);
  console.log("要导出的岗位编号:", postCodes);

  exportPost(postCodes)
    .then((res) => {
      if (res instanceof Blob) {
        const blob = res;
        const filename = `岗位信息.xls`;

        // 创建下载链接并触发下载
        const link = document.createElement("a");
        link.href = window.URL.createObjectURL(blob);
        link.download = filename;
        link.click();
        window.URL.revokeObjectURL(link.href);

        state.exportDialogVisible = false;
        proxy.$modal.msgSuccess("导出成功");
      } else {
        proxy.$modal.msgError("导出失败：返回格式错误");
      }
    })
    .catch((error) => {
      console.error("导出失败:", error);
      proxy.$modal.msgError("导出失败");
    });
}

// 取消导出
function cancelExport() {
  state.exportDialogVisible = false;
}

// 添加全选相关的状态
const isAllSelected = ref(false);
const allData = ref([]);

// 获取所有数据的函数
function getAllData() {
  return new Promise((resolve, reject) => {
    const params = {
      ...state.queryParams,
      pageNum: 1,
      pageSize: 999999, // 设置一个足够大的数以获取所有数据
    };

    listPost(params)
      .then((res) => {
        if (res.code === 200) {
          console.log(res, "所有数据");
          allData.value = res.data.rows;
          resolve(res.data.rows);
        } else {
          resolve([]);
        }
      })
      .catch((error) => {
        console.error("获取所有数据失败:", error);
        proxy.$modal.msgError("获取数据失败");
        reject(error);
      });
  });
}

// 全选按钮处理函数
function handleSelectAll() {
  if (!isAllSelected.value) {
    getAllData()
      .then((allRows) => {
        state.selectedRows = allRows;
        isAllSelected.value = true;
        // 选中当前页的所有行
        const tableRef = proxy.$refs.tableRef;
        tableRef.toggleAllSelection(true);
      })
      .catch((error) => {
        // console.error('全选操作失败:', error)
      });
  } else {
    // 取消选择
    state.selectedRows = [];
    isAllSelected.value = false;
    const tableRef = proxy.$refs.tableRef;
    tableRef.toggleAllSelection(false);
  }
}
</script>

<style lang="scss" scoped>
.import-steps {
  padding: 20px 0;

  .step {
    margin-bottom: 20px;

    &-title {
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}

.import-result {
  padding: 20px;
  text-align: center;

  .result-header {
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 15px;

    .result-title {
      margin-right: 10px;
      font-weight: bold;
    }

    .success {
      color: #67c23a;
      margin-right: 10px;
    }

    .error {
      color: #f56c6c;
    }
  }

  .error-reason {
    text-align: left;
    color: #f56c6c;
    font-size: 14px;
    margin-top: 15px;
    padding: 10px;
    background-color: #fef0f0;
    border-radius: 4px;

    div:last-child {
      line-height: 1.8;
      white-space: pre-line;
    }
  }
}
</style>