import QRCode from "qrcode";
import JSZip from "jszip";
import { saveAs } from "file-saver";

export function generateQRCode(dataArray, isZip = true, fileName) {
  if (isZip) {
    const zip = new JSZip();
    const promises = dataArray.map((data, index) => {
      const { name, time } = data;

      // 构建提示文字
      const prompt = `${name}\n有效日期：${time}`;
      const maxWidth = 300; // 设置最大宽度
      const lineHeight = 15; // 行高
      // const promptLines = wrapText(ctx, prompt, maxWidth);

      return QRCode.toDataURL(time).then((url) => {
        // 创建 canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const img = new Image();

        return new Promise((resolve) => {
          img.onload = () => {
            // 设置画布大小
            canvas.width = img.width + 200;
            canvas.height = img.height + 30 + lineHeight * 3; // 留出空间放置提示文字
            // ctx.drawImage(img, 0, 0);

            // 添加提示文字背景
            const promptHeight = 40; // 背景高度
            ctx.fillStyle = "white"; // 背景颜色
            // ctx.fillRect(0, img.height, img.width, promptHeight); // 绘制背景矩形
            ctx.fillRect(0, 0, canvas.width, canvas.height); // 绘制背景矩形

            // 计算二维码和提示文字的居中位置
            const qrX = (canvas.width - img.width) / 2; // 水平居中
            const qrY = 10; // 垂直位置

            // 绘制二维码
            ctx.drawImage(img, qrX, qrY);

            // 添加提示文字
            ctx.fillStyle = "black";
            ctx.font = "12px Arial";
            // ctx.textAlign = "center";

            // 将 prompt 拆分为多行
            // const promptLines = prompt.split("\n");
            const promptLines = wrapText(ctx, prompt, maxWidth);
            promptLines.forEach((line, index) => {
              const textY = qrY + img.height + 20 + lineHeight * index;

              // 计算每行的宽度并使其水平居中
              const textWidth = ctx.measureText(line).width;
              const xPosition = (canvas.width - textWidth) / 2; // 计算居中位置

              // 检查宽度并添加省略号
              if (textWidth > maxWidth) {
                let truncatedLine = line;
                while (
                  ctx.measureText(truncatedLine + "...").width > maxWidth
                ) {
                  truncatedLine = truncatedLine.slice(0, -1); // 去掉最后一个字符
                }

                // 计算每行的宽度并使其水平居中
                const textWidth1 = ctx.measureText(truncatedLine).width;
                const xPosition1 = (canvas.width - textWidth1) / 2; // 计算居中位置

                ctx.fillText(truncatedLine + "...", xPosition1, textY); // 绘制带省略号的文本
              } else {
                // 计算每行的宽度并使其水平居中

                ctx.fillText(line, xPosition, textY); // 绘制正常文本
              }

              // ctx.fillText(line, xPosition, textY); // 确保文字水平居中
            });

            // 转为 Blob
            canvas.toBlob((blob) => {
              zip.file(`${deviceName}-设备二维码.png`, blob);
              resolve();
            }, "image/png");
          };
          img.src = url;
        });
      });
    });

    // 等待所有二维码生成完毕
    Promise.all(promises).then(() => {
      zip.generateAsync({ type: "blob" }).then((content) => {
        // 使用 FileSaver 保存 ZIP 文件
        saveAs(content, `设备二维码.zip`);
      });
    });
  } else {
    console.log(dataArray[0]);
    const { name, time, name2, id, timeStr } = dataArray[0];

    // 构建提示文字
    const prompt = time
      ? `【${name}】\n有效日期：${timeStr}`
      : `巡检名称：${name}\n巡检指标：${name2}`;
    const maxWidth = 300; // 设置最大宽度
    const lineHeight = 15; // 行高

    QRCode.toDataURL(JSON.stringify({ id, name, time }))
      .then((url) => {
        // 创建 canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const img = new Image();

        return new Promise((resolve) => {
          img.onload = () => {
            // 设置画布大小
            canvas.width = img.width + 200;
            canvas.height = img.height + 30 + lineHeight * 3; // 留出空间放置提示文字
            // ctx.drawImage(img, 0, 0);

            // 添加提示文字背景
            const promptHeight = 40; // 背景高度
            ctx.fillStyle = "white"; // 背景颜色
            // ctx.fillRect(0, img.height, img.width, promptHeight); // 绘制背景矩形
            ctx.fillRect(0, 0, canvas.width, canvas.height); // 绘制背景矩形

            // 计算二维码和提示文字的居中位置
            const qrX = (canvas.width - img.width) / 2; // 水平居中
            const qrY = 10; // 垂直位置

            // 绘制二维码
            ctx.drawImage(img, qrX, qrY);

            // 添加提示文字
            ctx.fillStyle = "black";
            ctx.font = "12px Arial";
            // ctx.textAlign = "center";

            // 将 prompt 拆分为多行
            // const promptLines = prompt.split("\n");
            const promptLines = wrapText(ctx, prompt, maxWidth);
            promptLines.forEach((line, index) => {
              const textY = qrY + img.height + 20 + lineHeight * index;

              // 计算每行的宽度并使其水平居中
              const textWidth = ctx.measureText(line).width;
              const xPosition = (canvas.width - textWidth) / 2; // 计算居中位置

              // 检查宽度并添加省略号
              if (textWidth > maxWidth) {
                let truncatedLine = line;
                while (
                  ctx.measureText(truncatedLine + "...").width > maxWidth
                ) {
                  truncatedLine = truncatedLine.slice(0, -1); // 去掉最后一个字符
                }

                // 计算每行的宽度并使其水平居中
                const textWidth1 = ctx.measureText(truncatedLine).width;
                const xPosition1 = (canvas.width - textWidth1) / 2; // 计算居中位置

                ctx.fillText(truncatedLine + "...", xPosition1, textY); // 绘制带省略号的文本
              } else {
                // 计算每行的宽度并使其水平居中

                ctx.fillText(line, xPosition, textY); // 绘制正常文本
              }

              // ctx.fillText(line, xPosition, textY); // 确保文字水平居中
            });

            resolve(canvas.toDataURL("image/png", 1.0));
          };
          img.src = url;
        });
      })
      .then((res) => {
        console.log(res);
        var blob = getBlob(res);
        var link = document.createElement("a");
        var href = window.URL.createObjectURL(blob);
        link.href = href;
        link.download = fileName || `日常考勤-二维码.png`; //a标签的下载属性
        document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
        link.click();
        // 延时保证下载成功执行，否则可能下载未找到文件的问题
        setTimeout(function () {
          window.URL.revokeObjectURL(href); // 释放Url对象
          document.body.removeChild(link);
        }, 100);
      });
  }
}

export function wrapText(ctx, text, maxWidth) {
  const lines = text.split("\n").flatMap((line) => {
    const words = line.split(" ");
    const wrappedLines = [];
    let currentLine = "";

    words.forEach((word) => {
      const testLine = currentLine + word + " ";
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine) {
        wrappedLines.push(currentLine);
        currentLine = word + " ";
      } else {
        currentLine = testLine;
      }
    });

    if (currentLine) {
      wrappedLines.push(currentLine);
    }

    return wrappedLines;
  });

  return lines;
}

function getBlob(base64) {
  var mimeString = base64.split(",")[0].split(":")[1].split(";")[0]; // mime类型
  var byteString = atob(base64.split(",")[1]); //base64 解码
  var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
  var intArray = new Uint8Array(arrayBuffer); //创建视图
  for (var i = 0; i < byteString.length; i += 1) {
    intArray[i] = byteString.charCodeAt(i);
  }
  return new Blob([intArray], {
    type: mimeString,
  });
}

// export function generateQRCode(Eltext, filename) {
//   // 获取输入的文本
//   // var text = document.getElementById("text").value;
//   var text = Eltext;
//   // 清空之前的二维码
//   document.getElementById("qrcode").innerHTML = "";
//   // Elqrcode.innerHTML = "";

//   // 添加提示文字
//   var promptElement = document.createElement("div");
//   promptElement.textContent = 'promptText'; // 设置提示文字
//   promptElement.style.marginTop = "10px"; // 设置与二维码的间距
//   promptElement.style.textAlign = "center"; // 设置文本居中
//   document.getElementById("qrcode").appendChild(promptElement); // 将提示文字添加到二维码容器中

//   // 生成新的二维码
//   let url = new QRCode(document.getElementById("qrcode"), text);
//   let base64Text = url._el.querySelector("canvas").toDataURL("image/png");
//   var blob = getBlob(base64Text);

//   var link = document.createElement("a");
//   var href = window.URL.createObjectURL(blob);
//   link.href = href;
//   link.download = `${filename}-签到码.png`; //a标签的下载属性
//   // document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
//   link.click();
//   // 延时保证下载成功执行，否则可能下载未找到文件的问题
//   setTimeout(function () {
//     window.URL.revokeObjectURL(href); // 释放Url对象
//     // document.body.removeChild(link);
//   }, 100);

// }

// function getBlob(base64) {
//   var mimeString = base64.split(",")[0].split(":")[1].split(";")[0]; // mime类型
//   var byteString = atob(base64.split(",")[1]); //base64 解码
//   var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
//   var intArray = new Uint8Array(arrayBuffer); //创建视图
//   for (var i = 0; i < byteString.length; i += 1) {
//     intArray[i] = byteString.charCodeAt(i);
//   }
//   return new Blob([intArray], {
//     type: mimeString,
//   });
// }
