<template>
  <div class="complaintInfo app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>
      <el-descriptions title="" border>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉单编号"
        >
          {{ complaintInfo.complaintsCode }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉人"
        >
          <span>{{ complaintInfo.name }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="联系方式"
        >
          <span>{{ complaintInfo.phone }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉类别"
        >
          <span>{{ complaintInfo.type }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="是否关联工单"
        >
          <span>{{ !!complaintInfo.troubleCode ? "是" : "否" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="工单编号"
        >
          <span>{{ complaintInfo.troubleCode || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          :span="2"
          label-class-name="label-width"
          class-name="value-width"
          label="投诉内容"
        >
          <span>{{ complaintInfo.complaintsContent }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="是否接受回访"
        >
          <span>{{ complaintInfo.access === 1 ? "接受" : "不接受" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          :span="3"
          label-class-name="label-width"
          class-name="value-width"
          label="投诉图片"
        >
          <span v-if="complaintsImages.length < 1">暂无图片</span>
          <el-image
            v-else
            v-for="(item, index) in complaintsImages"
            :key="index"
            style="width: 100px; height: 100px; margin-right: 10px"
            :src="item"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="complaintsImages"
            :initial-index="index"
            fit="contain"
          />
        </el-descriptions-item>
      </el-descriptions>

      <el-form
        class="complaintInfo-form"
        ref="replyRef"
        :model="complaintInfo"
        v-if="isEdit || !!complaintInfo.status"
        :rules="rules"
      >
        <el-descriptions title="" border :column="1" label-width="130">
          <el-descriptions-item
            label-class-name="label-width2"
            class-name="value-width2"
            label="回复内容"
          >
            <el-form-item label="" prop="replyContent">
              <el-input
                v-model="complaintInfo.replyContent"
                style="width: 100%"
                type="textarea"
                placeholder="请输入回复内容"
                :disabled="!!complaintInfo.status"
                maxlength="200"
              />
              <!-- <editor v-model="form.replyContent" :min-height="200" /> -->
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width2"
            class-name="value-width2"
            label="图片"
          >
            <el-form-item label="" prop="replyImages">
              <imgUpload
                class="complainImg"
                @update:modelValue="(url) => setImgUrl(url, 'replyImages')"
                :limit="2"
                :fileSize="5"
                className="w100"
                :uploadType="4"
                :modelValue="complaintInfo.replyImages"
                :disabled="!!complaintInfo.status"
                :isShowTip="isEdit"
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <div class="complaintInfo-btns">
        <el-button
          type="primary"
          @click="handleEdit"
          v-if="!complaintInfo.status"
          v-throttle
          >{{ !isEdit ? "处理" : "回复" }}</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import imgUpload from "@/components/ImageUpload";
import { complainInfo, complainReply } from "@/api/mediaTeach/trouble";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  replyRef: null,
  id: "",
  isEdit: false,
  fileList: [],
  complaintsImages: [],
  complaintInfo: {
    replyContent: "",
    replyImages: "",
  },
  rules: {
    replyContent: [
      { required: true, message: "回复内容不能为空", trigger: "blur" },
    ],
  },
  statusObj: {
    0: {
      type: "primary",
      name: "待认领",
      path: "/taskManage/taskCenter/claimInfo",
    },
    1: {
      type: "danger",
      name: "待处理",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
    2: {
      type: "warning",
      name: "待评价",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    3: {
      type: "success",
      name: "已完成",
      path: "/taskManage/taskCenter/handleTaskInfo",
    },
    4: {
      type: "info",
      name: "已挂起",
      path: "/taskManage/taskCenter/todoTaskInfo",
    },
  },
});

const {
  statusObj,
  replyRef,
  fileList,
  form,
  rules,
  complaintsImages,
  complaintInfo,
  isEdit,
} = toRefs(state);

const getData = () => {
  complainInfo(state.id).then((response) => {
    if (response.data) {
      console.log(response.data);
      const { complaintsImages, replyImages } = response.data;
      state.complaintInfo = response.data;
      state.complaintInfo.replyImages = replyImages
        ? JSON.parse(replyImages)
        : [];
      state.complaintsImages = complaintsImages
        ? JSON.parse(complaintsImages)
        : [];
      // state.complaintInfo.status = 0;
    }
  });
};

const setImgUrl = (val, name) => {
  state.complaintInfo[name] = val;
};

function handleEdit() {
  if (state.isEdit) {
    proxy.$refs["replyRef"].validate((valid) => {
      if (valid) {
        let d = {
          id: state.id,
          replyContent: state.complaintInfo.replyContent,
          replyImages: state.complaintInfo.replyImages.split(","),
        };
        console.log("回复传参", d);
        complainReply(d).then((response) => {
          if (response.code == 200) {
            proxy.$modal.msgSuccess("回复成功");
            state.complaintInfo.status = 1;
          }
        });
      }
    });
  } else {
    state.isEdit = !state.isEdit;
  }
}

function handleBack() {
  if (!!route.query.taskId) {
    proxy.$tab.closeOpenPage(
      `${state.statusObj[route.query.status].path}?id=${route.query.taskId}`
    );
  } else if (!!route.query.documentId) {
    proxy.$tab.closeOpenPage(
      `/fileManage/workOrderFileInfo?id=${route.query.documentId}`
    );
  } else if (route.query.visit == 1) {
    proxy.$tab.closeOpenPage("/visit?complaints=1");
  } else {
    proxy.$tab.closeOpenPage("repairIndex");
  }
}

onMounted(() => {
  state.isEdit = route.query.type == 1 && !route.query.taskId;
  state.id = route.query.id;
  getData();
});
</script>

<style lang="scss" scoped>
.complaintInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-form {
    margin-top: 40px;
  }
  &-btns {
    padding: 50px 0;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}
</style>