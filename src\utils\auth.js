import Cookies from "js-cookie";

const TokenKey = `YTWHYWPT_APP_Admin-Token${
  window.location.pathname.indexOf("/admin") == -1
    ? "_" + window.location.pathname.replace(/\//g, "")
    : ""
}`;

const ExpiresInKey = "YTWHYWPT_APP_Admin-Expires-In";

export function getToken() {
  return Cookies.get(TokenKey);
}

export function setToken(token) {
  return Cookies.set(TokenKey, token);
}

export function removeToken() {
  return Cookies.remove(TokenKey);
}

export function getExpiresIn() {
  return Cookies.get(ExpiresInKey) || -1;
}

export function setExpiresIn(time) {
  return Cookies.set(ExpiresInKey, time);
}

export function removeExpiresIn() {
  return Cookies.remove(ExpiresInKey);
}
