<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
                <el-form-item label="" prop="teacherName">
                    <el-input v-model="queryParams.teacherName" placeholder="请输入教师姓名" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="phone">
                    <el-input v-model="queryParams.phone" placeholder="请输入教师电话" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="subjects">
                    <el-input v-model="queryParams.subjects" placeholder="请输入科目" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="sex">
                    <el-select v-model="queryParams.sex" placeholder="请选择性别" @change="handleQuery" clearable filterable
                        style="width: 200px">
                        <el-option v-for="item in sexList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" prop="education">
                    <el-select v-model="queryParams.education" placeholder="请选择学历" @change="handleQuery" clearable
                        filterable style="width: 200px">
                        <el-option v-for="item in educationList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" prop="post">
                    <el-select v-model="queryParams.post" placeholder="请选择职称" @change="handleQuery" clearable filterable
                        style="width: 200px">
                        <el-option v-for="item in postList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-upload :action="uploadFileUrl" :headers="headers" :show-file-list="false"
                        :before-upload="beforeUpload" :on-success="handleUploadSuccess">
                        <el-button type="primary" plain icon="Upload">
                            导入教师信息
                        </el-button>
                    </el-upload>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="success" plain icon="Download" @click="handleDownload">
                        下载导入模板
                    </el-button>
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="teacherList" border>
                <el-table-column type="index" label="序号" width="90" align="center" fixed="left" />
                <el-table-column label="姓名" align="center" minWidth="120px" prop="teacherName" />
                <el-table-column label="性别" align="center" minWidth="120px">
                    <template #default="scope">
                        {{ !!scope.row.sex ? '女' : '男' }}
                    </template>
                </el-table-column>
                <el-table-column label="职称" align="center" minWidth="120px" prop="post" />
                <el-table-column label="学历" align="center" minWidth="120px" prop="education" />
                <el-table-column label="科目" align="center" minWidth="120px" prop="subjects" />
                <el-table-column label="教师电话" align="center" minWidth="120px" prop="phone" />
                <el-table-column label="教学时长" align="center" minWidth="120px" prop="deviceTime" />
                <el-table-column label="年龄" align="center" minWidth="120px" prop="age" width="180" />
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                v-model:limit="queryParams.size" @pagination="getList" />
            <el-main class="echarts-box" v-loading="echartLoaing">
                <div class="echarts-box_opts">
                    <el-input v-model="echartOpts.grade" placeholder="请输入年级" clearable style="width: 200px"
                        @change="getData" />
                    <el-input v-model="echartOpts.subjects" placeholder="请输入学科" clearable
                        style="width: 200px;margin: 0 15px;" @change="getData" />
                    <el-select v-model="echartOpts.status" placeholder="请选择范围" style="width: 200px;" @change="getData">
                        <el-option v-for="(item, index) in rangeList" :key="index" :label="item.label"
                            :value="item.value" />
                    </el-select>
                </div>
                <Echarts @setFontSize="setFontSize" id="chartDom" width="100%" height="45vh" :fullOptions="chartOption"
                    :loading="false" />
            </el-main>
        </el-card>
        <ExportRes :data="exportRes" :open="open" @setOpen="(val) => { open = val }" />
    </div>
</template>

<script setup name="Post">
import Echarts from '@/components/Echarts/index.vue'
import ExportRes from '@/components/ExportRes'
import { onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { getToken } from "@/utils/auth";
import { downloadBlob, transformSize } from '@/utils';
import { teacherPage, teacherTimeRank } from '@/api/teachInfo/teacher';
import { templateDownload } from '@/api/mediaTeach/ledger';
import { sm2Decrypt } from '@/utils/sm2encrypt'

const { proxy } = getCurrentInstance();
const route = useRoute()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/system/teacher/importData"); // 导入文件服务器地址
const headers = ref({ Authorization: "Bearer " + getToken() });

const echartLoaing = ref(false)
const echartOpts = ref({
    grade: '',
    subjects: '',
    status: 0
})
const rangeList = ref([
    { label: '总排名', value: 0 },
    { label: '月排名', value: 1 },
    { label: '周排名', value: 2 },
])
const teacherList = ref([]);
const loading = ref(false);
const showSearch = ref(true);
const total = ref(0);
const open = ref(false)
const exportRes = ref({})
const sexList = ref([
    { label: '男', value: 0 },
    { label: '女', value: 1 },
])
const educationList = ref([
    { label: '初中', value: '初中' },
    { label: '高中', value: '高中' },
    { label: '专科', value: '专科' },
    { label: '本科', value: '本科' },
    { label: '硕士', value: '硕士' },
    { label: '博士', value: '博士' },
])
const postList = ref([
    { label: '正高级教师', value: '正高级教师' },
    { label: '高级教师', value: '高级教师' },
    { label: '一级教师', value: '一级教师' },
    { label: '二级教师', value: '二级教师' },
    { label: '三级教师', value: '三级教师' },
])

let xData = ['语文', '数学', '英语', '政治', '地理', '历史', '生物', '物理', '化学']
let sData = [1, 2, 3, 4, 5, 6, 7, 8, 9]

const data = reactive({
    queryParams: {
        current: 1,
        size: 5,
        teacherName: '',
        phone: '',
        sex: null,
        subjects: '',
        post: '',
        education: ''
    }
});

const { queryParams } = toRefs(data);


/** 查询台账列表 */
function getList() {
    loading.value = true;
    teacherPage(queryParams.value).then(response => {
        teacherList.value = response.data.records
        total.value = response.data.total
        loading.value = false
    })
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.current = 1;
    getList()
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

function handleDownload() {
    templateDownload({ modelName: 'import_teacher' }).then(response => {
        downloadBlob(response, 'application/vnd.ms-excel', '教师导入模板')
    })
}

/** 获取数据 */
async function getData() {
    let opt = chartOption.options
    echartLoaing.value = true
    await teacherTimeRank(echartOpts.value).then(response => {
        // if (response.data.length > 0) {
        xData = [], sData = []
        response.data.reverse().map((item, index) => {
            xData[index] = item.teacherName
            sData[index] = item.useTime || 0
        })
        opt.yAxis.data = xData
        opt.series[0].data = sData
        echartLoaing.value = false
        // }
    })
}

const beforeUpload = (file) => {
    const Xls = file.name.split(".");
    if (Xls[1] !== "xls" && Xls[1] !== "xlsx") {
        proxy.$modal.msgError("请上传excel格式的文件!");
        return false;
    } else if (file.size / 1024 / 1024 > 250) {
        proxy.$modal.msgError("请上传250M以下的文件!");
        return false;
    }
    return true;
}

const handleUploadSuccess = (res, file) => {
    if (res.code === 200) {
        getList()
        exportRes.value = JSON.parse(sm2Decrypt(res.data))
        let { errorCount } = exportRes.value
        if (!errorCount) {
            proxy.$modal.msgSuccess('导入成功');
        } else {
            open.value = true
        }
    } else {
        proxy.$modal.msgError(res.msg);
    }
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(28)
}

function injectOption() {
    proxy.chartDom = chartOption
}

const chartOption = reactive({
    options:
    {
        title: {
            text: '教学设备使用Top5',
            textStyle: {
                fontSize: transformSize(28)
            },
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            height: '70%',
            bottom: '8%'
        },
        xAxis:
        {
            type: 'value',
            name: '使用深度',
            splitLine: { show: false },
        },
        yAxis:
        {
            type: 'category',
            data: [],
            axisPointer: {
                type: 'shadow'
            }
        },
        series: [
            {
                name: '使用时长',
                type: 'bar',
                barMaxWidth: '60%',
                data: [],
                itemStyle: {
                    color: '#5470c6'
                }
            },
        ]
    },
    init: false
})

injectOption()

onMounted(() => {
    getList()
    getData()
})
</script>

<style lang="scss" scoped>
.literacy-header {
    margin: 10px 0;
}

.card-header {
    padding: 10px 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .opts {
        display: flex;
        align-items: center;

        .item {
            cursor: pointer;
            margin-right: 20px;
            color: gray;
        }

        .item.active {
            color: dodgerblue;
        }
    }
}

.echarts-box {
    position: relative;
    padding: 50px 0;

    &_opts {
        position: absolute;
        right: 0;
        z-index: 9;
    }
}
</style>
