<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block3">
        <div class="flex">
          <div class="block">
            <div class="block-subTitle">信息资产类别占比</div>
            <div style="position: relative; padding: 0.2vw 0 0.52vw">
              <img
                class="pieData-bg"
                src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie5-bg.png"
              />
              <Echarts
                ref="pieDataRef"
                id="pieData"
                class="echart"
                width="12.5vw"
                height="10.5vw"
                :fullOptions="pieOption"
              />
            </div>
          </div>
          <div class="block">
            <div class="block-subTitle">信息资产类别TOP5</div>
            <div class="echart-item">
              <!-- <div class="export-btn" @click="exportDeptAssets">导出</div> -->
              <Echarts
                id="barData"
                width="100%"
                height="11.5vw"
                :fullOptions="barOption"
              />
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block2">
        <div class="block-title">
          <div class="titleText">资产覆盖率</div>
        </div>
        <div class="battery">
          <span>{{ objData.assetCoverageStatistics.coverage }}</span>
        </div>
      </div>

      <div class="col1-block4">
        <div class="block-title">
          <div class="titleText">部门资产总数</div>
        </div>
        <div class="table" style="min-height: 10.5vw">
          <div class="table-row opt-title">
            <div class="table-row_name table-row_head">部门名称</div>
            <div class="table-row_name table-row_head">部门人数</div>
            <div class="table-row_name table-row_head">资产总数</div>
            <div class="table-row_name table-row_head">操作</div>
          </div>
          <div
            class="table-row data"
            v-for="item in objData.assetCoverageStatistics.assetCoverageRanking"
            :key="item.deptName"
          >
            <div class="table-row_name">{{ item.deptName }}</div>
            <div class="table-row_name">
              {{ item.peopleNum > 99999 ? "99999+" : item.peopleNum }}
            </div>
            <div class="table-row_name">
              {{ item.assetsNum > 99999 ? "99999+" : item.assetsNum }}
            </div>
            <div
              class="table-row_name"
              style="cursor: pointer; color: #4095e5"
              @click="handleCheck(item)"
            >
              点击查看
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col col2">
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list.slice(0, 2)"
          :key="index"
        >
          <div class="card">
            <div class="card-left">{{ item.name }}</div>
            <div class="card-right">{{ item.num }}</div>
          </div>
          <div class="info flex">
            <img :src="item.icon" class="card-bottom" />
            <div class="tip">
              <div class="tip-ratio">
                同比
                <div
                  class="ratio"
                  :class="
                    item.increaseNum > 0
                      ? `up`
                      : item.increaseNum < 0
                      ? 'low'
                      : ''
                  "
                >
                  {{ item.increaseNum > 0 ? `+${item.ratio}` : item.ratio }}
                  <span v-if="index == 1" style="color: #fff">条</span>
                </div>
              </div>
              <div
                class="tip-change"
                :class="
                  item.increaseNum > 0
                    ? `up`
                    : item.increaseNum < 0
                    ? 'low'
                    : ''
                "
                v-if="index != 1"
              >
                <div v-if="item.increaseNum == 0">与昨日相同</div>
                <div v-if="item.increaseNum > 0">
                  与昨日相比多出<span>{{ item.increaseNum }}</span
                  >个
                </div>
                <div v-if="item.increaseNum < 0">
                  与昨日相比减少<span>{{ Math.abs(item.increaseNum) }}</span
                  >个
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col2-block2">
        <!-- <div class="block-title long">
          <div class="titleText">资产增长趋势图</div>
        </div> -->
        <div class="echart-item">
          <!-- <div class="rangeTab">
            <div
              v-for="item in rangeList2"
              :key="item.value"
              class="rangeTab_item"
              :class="{ active: curRange4 == item.value }"
              @click="handleRange2(0, item.value)"
            >
              {{ item.label }}
            </div>
          </div> -->
          <Echarts
            id="lineData2"
            width="100%"
            height="19.5vw"
            :fullOptions="lineOption2"
          />
        </div>
      </div>
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list.slice(2, 4)"
          :key="index"
        >
          <div class="card">
            <div class="card-left">{{ item.name }}</div>
            <div class="card-right">{{ item.num }}</div>
          </div>
          <div class="info flex">
            <img :src="item.icon" class="card-bottom" />
            <div class="tip">
              <div class="tip-ratio">
                同比
                <div
                  class="ratio"
                  :class="
                    item.increaseNum > 0
                      ? `up`
                      : item.increaseNum < 0
                      ? 'low'
                      : ''
                  "
                >
                  {{ item.increaseNum > 0 ? `+${item.ratio}` : item.ratio }}
                </div>
              </div>
              <div
                class="tip-change"
                :class="
                  item.increaseNum > 0
                    ? `up`
                    : item.increaseNum < 0
                    ? 'low'
                    : ''
                "
              >
                <div v-if="item.increaseNum == 0">与昨日相同</div>
                <div v-if="item.increaseNum > 0 && index == 1">
                  与昨日相比多出<span>{{ item.increaseNum }}</span
                  >个
                </div>
                <div v-if="item.increaseNum > 0 && index == 0">
                  与昨日相比多出<span>{{ item.increaseNum }}%</span>
                </div>
                <div v-if="item.increaseNum < 0">
                  与昨日相比减少<span>{{ Math.abs(item.increaseNum) }}</span
                  >个
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col2-block3">
        <div class="flex">
          <div style="flex: 1">
            <div class="block-title">
              <div class="titleText">收到工单总数/处理工单数</div>
              <div class="titleTab">
                <div
                  class="titleTab_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="{ active: curRange2 == item.value }"
                  @click="handleRange(1, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="flex" style="gap: 0">
              <Echarts
                style="flex: 1"
                id="lineData3"
                width="100%"
                height="10.7vw"
                :fullOptions="lineOption3"
              />
              <div class="block block-right" style="flex: 0.8">
                <div
                  class="item-opt"
                  v-for="(item, index) in optList2"
                  :key="index"
                  style="font-size: 0.8vw"
                >
                  {{ item.title }}
                  <div class="item-opt_row">
                    <span style="font-size: 0.65vw !important"
                      >处理工单数{{
                        item.num > 0 ? "增长" : item.num < 0 ? "下降" : "相同"
                      }}</span
                    >
                    <div
                      style="font-size: 0.7vw !important"
                      :class="
                        item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      "
                    >
                      {{ item.ratio.replace(/-/g, "") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div style="flex: 1">
            <div class="block-title">
              <div class="titleText">工单平均处理时间</div>
              <div class="titleTab">
                <div
                  class="titleTab_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="{ active: curRange3 == item.value }"
                  @click="handleRange(2, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="flex" style="gap: 0">
              <Echarts
                style="flex: 1"
                id="lineData4"
                width="100%"
                height="10.7vw"
                :fullOptions="lineOption4"
              />
              <div class="block block-right block-right2" style="flex: 0.8">
                <div
                  class="item-opt item-opt2"
                  v-for="(item, index) in optList4"
                  :key="index"
                >
                  <div class="center top" v-if="index == 0">
                    <div v-if="item.increase == 0">与昨日相同</div>
                    <div v-if="item.increase > 0">
                      与昨日相比增加<span>{{ item.increase }}</span
                      >分钟
                    </div>
                    <div v-if="item.increase < 0">
                      与昨日相比减少<span>{{ Math.abs(item.increase) }}</span
                      >分钟
                    </div>
                  </div>
                  <div class="center top" v-if="index == 1">
                    平均
                    <span>{{ item.fq.replace(" 天/次", "") }}</span>
                    天处理一次工单
                  </div>
                  <div class="bottom">
                    <div
                      v-if="index == 0"
                      style="
                        display: flex;
                        width: 100%;
                        justify-content: space-between;
                      "
                    >
                      {{ item.title }}
                      <div>
                        <span class="minute">{{ item.total }}</span
                        >分钟
                      </div>
                    </div>
                    <div v-if="index == 1">{{ item.title }}</div>
                    <div class="item-opt_row">
                      <div
                        v-if="index == 0"
                        style="
                          display: flex;
                          width: 100%;
                          justify-content: space-between;
                          padding-left: 0;
                        "
                      >
                        {{
                          item.num == 0
                            ? "与昨日相同"
                            : item.num > 0
                            ? `对比昨日同比增长`
                            : "对比昨日同比下降"
                        }}
                        <div
                          v-if="index == 0"
                          :class="
                            item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                          "
                        >
                          <span v-if="index == 0" style="font-size: 0.6vw">{{
                            item.ratio.replace(/-/g, "")
                          }}</span>
                        </div>
                      </div>
                      <div
                        v-if="index == 1"
                        style="width: 100%; text-align: center; padding-left: 0"
                      >
                        <span class="minute">{{ item.total }}</span
                        >分钟
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block2">
        <div class="block-title">
          <div class="titleText">考勤打卡偏差值</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in rangeList"
              :key="item.value"
              :class="{ active: curRangeTab == item.value }"
              @click="
                curRangeTab = item.value;
                getClockData();
              "
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <!-- <div class="col3-block2_tabs">
          <div
            class="left"
            :class="curRangeTab == 1 ? 'active' : ''"
            @click="
              curRangeTab = 1;
              getClockData();
            "
          >
            周
          </div>
          <div
            class="center"
            :class="curRangeTab == 2 ? 'active' : ''"
            @click="
              curRangeTab = 2;
              getClockData();
            "
          >
            月
          </div>
          <div
            class="right"
            :class="curRangeTab == 3 ? 'active' : ''"
            @click="
              curRangeTab = 3;
              getClockData();
            "
          >
            年
          </div>
        </div> -->
        <div
          class="col3-block2_item"
          :class="[`item${index + 1}`]"
          v-for="(item, index) in col3_block2_list"
          :key="item.name"
        >
          <div class="left">
            {{ item.name }}
            <div v-if="item.num !== 0">
              <span
                >{{ item.num > 0 ? `+${item.num}` : item.num
                }}{{ item.unit }}</span
              >
            </div>
            <div v-if="item.num === 0" style="color: #fff">无</div>
          </div>
        </div>
      </div>

      <div class="col3-block1">
        <div class="block-title">
          <div class="titleText">今日处理时间</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in todayTabList"
              :key="item.value"
              :class="{ active: curTodayTab == item.value }"
              @click="handleToday(item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="col3-block1_item">
          <div class="left">
            <div>
              <span>{{ handleInfo.day }}</span
              >天 <span>{{ handleInfo.hour }}</span
              >小时 <span>{{ handleInfo.minute }}</span
              >分钟
            </div>
          </div>
          <div
            class="right"
            @click="checkOrder(handleInfo.id, index)"
            v-if="handleInfo.id"
          >
            点击查看
          </div>
        </div>
      </div>

      <div class="col3-block4">
        <div class="block-title"><div class="titleText">今日巡检人员</div></div>
        <div class="table block">
          <div class="table-row opt-title">
            <div class="table-row_count table-row_head">人员名称</div>
            <div class="table-row_count table-row_head">巡检点位</div>
            <div class="table-row_count table-row_head">完成情况</div>
          </div>
          <div
            class="table-data"
            @mouseenter="setAnimate(false)"
            @mouseleave="setAnimate(true)"
          >
            <div :class="{ 'table-data_inner': animate1 }">
              <div
                class="table-row data"
                v-for="(item, index) in checkList"
                :key="index"
              >
                <div class="table-row_count">{{ item.name }}</div>
                <div
                  class="table-row_count"
                  :title="item.prointCodeList.join('、')"
                >
                  {{ item.prointCodeList.join("、") }}
                </div>
                <div
                  class="table-row_count status"
                  :class="{ finish: item.status == '1' }"
                >
                  {{ item.status == "1" ? "已完成" : "未完成" }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="col3-block3">
        <div class="block-title">
          <div class="titleText">运维考勤打卡情况</div>
          <div class="titleTab">
            <div
              class="titleTab_item"
              v-for="item in rangeList"
              :key="item.value"
              :class="{ active: curRange1 == item.value }"
              @click="handleRange(0, item.value)"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
        <div class="flex" style="gap: 0">
          <Echarts
            style="flex: 1"
            id="lineData5"
            width="100%"
            height="10.7vw"
            :fullOptions="lineOption5"
          />
          <div class="block block-right" style="flex: 0.8">
            <div class="item-opt center" v-for="item in optList5">
              {{ item.title }}
              <div class="item-opt_row">
                <span>{{ item.total || 0 }}</span>
                人
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      title="查看详情"
      v-model="dialogVisible"
      align-center
      width="500"
    >
      <el-descriptions title="" border :column="1">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门名称"
        >
          {{ deptInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门人数"
        >
          {{ deptInfo.peopleNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="资产总数"
        >
          {{ deptInfo.assetsNum }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="部门负责人"
        >
          {{ deptInfo.leaderName || "-" }}
        </el-descriptions-item>
      </el-descriptions>
    </el-dialog>

    <el-dialog
      class="custom-dialog"
      title="工单详情"
      v-model="dialogVisibleOrder"
      align-center
      width="1050"
    >
      <el-scrollbar max-height="500px">
        <OrderDetail
          :key="curOrderId"
          :showComplaintsBtn="false"
          :troubleId="curOrderId"
          :isHandled="true"
        />
      </el-scrollbar>
      <template #footer>
        <div style="display: flex; justify-content: center">
          <el-pagination
            layout="prev, pager, next"
            v-if="idsList.length > 0"
            :page-size="1"
            :total="idsList.length"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </el-dialog>
  </div>
</template>
      
      <script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import OrderDetail from "@/views/taskCenter/components/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getClockOffsetStatistics,
  getSchoolStatistics,
  getWorkOrderStatistics,
  getMaintainCountStatistics,
  getWorkOrderTimeStatistics,
  getWorkOrderTime,
  getAssetsTrend,
} from "@/api/unify";
import { schoolInspectionList } from "@/api/check";
import { fitChartSize, downloadBlob } from "@/utils";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  animate1: null,
  scrolltimer1: null,
  pieDataRef: null,
  handleInfo: {
    day: 0,
    hour: 0,
    minute: 0,
    id: "",
  },
  curTodayTab: 0,
  todayTabList: [
    { label: "最长处理时间", value: 0 },
    { label: "最短处理时间", value: 1 },
  ],
  curOrderId: "",
  dialogVisibleOrder: false,
  idsList: [],
  minList: [],
  maxList: [],
  checkList: [],
  rangeList2: [
    { label: "按年", value: 3 },
    { label: "按月", value: 2 },
    { label: "按日", value: 1 },
  ],
  rangeList: [
    { label: "按年", value: 3 },
    { label: "按月", value: 2 },
    { label: "按周", value: 1 },
  ],
  rangeList3: [
    { label: "周", value: 1 },
    { label: "月", value: 2 },
    { label: "年", value: 3 },
  ],
  curRange1: 1,
  curRange2: 1,
  curRange3: 1,
  curRange4: 3,
  curRangeTab: 1,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  timer2: null,
  curTime: new Date().getTime(),
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
});

const {
  animate1,
  scrolltimer1,
  pieDataRef,
  handleInfo,
  curTodayTab,
  todayTabList,
  minList,
  maxList,
  idsList,
  curOrderId,
  dialogVisibleOrder,
  checkList,
  rangeList,
  rangeList2,
  rangeList3,
  curRange1,
  curRange2,
  curRange3,
  curRange4,
  curRangeTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  curTime,
  timer,
  timer2,
} = toRefs(state);

const col3_block1_list = ref([
  {
    name: "今日最长处理时间",
    id: "",
    day: 0,
    hour: 0,
    minute: 0,
  },
  {
    name: "今日最短处理时间",
    id: "",
    day: 0,
    hour: 0,
    minute: 0,
  },
]);

const col3_block2_list = ref([
  {
    name: "考勤时间打卡偏差值",
    num: 0.026,
    unit: "h",
  },
  {
    name: "考勤位置打卡偏差值",
    num: 0.025,
    unit: "km",
  },
]);

const col2_block1_list = ref([
  {
    name: "当前工单量",
    num: 5936,
    ratio: "19.2%",
    increaseNum: 96,
    icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-icon1.png",
  },
  {
    name: "报障总次数",
    num: 5936,
    ratio: "553",
    increaseNum: 553,
    icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-icon2.png",
  },
  {
    name: "当前报障率",
    num: "35%",
    ratio: "9.2%",
    increaseNum: 9.2,
    icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-icon3.png",
  },
  {
    name: "今日新增工单量",
    num: 5936,
    ratio: "19.2%",
    increaseNum: 96,
    icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-icon4.png",
  },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "0", unit: "个" },
  { name: "开机率", num: "0", unit: "%" },
  { name: "报废数", num: "0", unit: "个" },
]);
const summaryRightList = ref([
  { name: "运维人员", num: "0", unit: "人" },
  { name: "今日执勤", num: "0", unit: "人" },
  { name: "今日轮休", num: "0", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "今日应到运维人员", total: 0, ratio: "0%", num: 0 },
  { title: "实到运维人员", total: 0, ratio: "0", num: 0 },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "同比",
    total: 0,
    ratio: "-100%",
    num: -100,
  },
  {
    title: "本年度对比上年度",
    tip: "同比",
    total: 5,
    ratio: "0%",
    num: 0,
  },
]);

const optList4 = ref([
  {
    title: "今日工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    increase: 15,
  },
  {
    title: "合计工单平均处理时间",
    total: 0,
    ratio: "0%",
    num: 0,
    fq: "2.45",
  },
]);

const optList5 = ref([
  { title: "今日应到运维人员", total: 0 },
  { title: "实到运维人员", total: 0 },
]);

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
}); // 存放数据

//资产类别占比柱状图
const barData1 = ref([100, 30, 40, 50, 60]); // 系列1的数据
const borderHeight1 = 2; // 底部边框的高度（数据单位）
const barColor1 = ref([
  {
    topColor: "rgba(19, 141, 255, 1)",
    bottomColor: "rgba(19, 141, 255, .6)",
    borderColor: "rgba(19, 141, 255, 1)",
  }, // 系列1的颜色
  {
    topColor: "rgba(15, 255, 242, 1)",
    bottomColor: "rgba(33, 231, 251, 0.6)",
    borderColor: "rgba(33, 231, 251, 1)",
  }, // 系列2的颜色
  {
    topColor: "rgba(70, 221, 148, 1)",
    bottomColor: "rgba(70, 221, 148, 0.6)",
    borderColor: "rgba(70, 221, 148, 1)",
  }, // 系列3的颜色
  {
    topColor: "rgba(221, 210, 70, 1)",
    bottomColor: "rgba(221, 210, 70, 0.6)",
    borderColor: "rgba(221, 210, 70, 1)",
  }, // 系列2的颜色
  {
    topColor: "rgba(221, 145, 70, 1)",
    bottomColor: "rgba(221, 145, 70, 0.6)",
    borderColor: "rgba(221, 145, 70, 1)",
  }, // 系列3的颜色
]);
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${Math.ceil(relVal.value)}`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "15%",
      left: "10%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#fff",
        formatter: (params) => {
          return params.length > 4 ? `${params.slice(0, 3)}...` : params;
        },
        rotate: 18,
        margin: fitChartSize(8),
        fontSize: fitChartSize(8.5),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(20),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: ["rgba(217, 231, 255, 0.2)"],
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        type: "bar",
        data: [
          {
            name: "其它",
            value: borderHeight1,
            itemStyle: {
              color: barColor1.value[0].borderColor,
            },
          },
          {
            name: "娱乐类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[1].borderColor },
          },
          {
            name: "办公类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[2].borderColor },
          },
          {
            name: "教学类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[3].borderColor },
          },
          {
            name: "应用类",
            value: borderHeight1,
            itemStyle: { color: barColor1.value[4].borderColor },
          },
        ], // 数据固定为边框高度
        barWidth: "50%", // 与主系列宽度一致
        stack: "border1", // 堆叠组名（需唯一）
        z: 1, // 确保边框系列在主系列下方
        tooltip: { show: false }, // 隐藏提示
      },
      {
        data: [
          {
            name: "其它",
            value: barData1.value[0] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[0].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[0].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "娱乐类",
            value: barData1.value[1] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[1].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[1].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "办公类",
            value: barData1.value[2] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[2].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[2].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "教学类",
            value: barData1.value[3] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[3].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[3].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
          {
            name: "应用类",
            value: barData1.value[4] - borderHeight1,
            itemStyle: {
              color: {
                type: "linear",
                legendColor: "#138CFE",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color: barColor1.value[4].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color: barColor1.value[4].bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          },
        ],
        type: "bar",
        barWidth: "50%",
        showBackground: true,
        backgroundStyle: {
          color: "rgba(196, 196, 196, 0.05)",
        },
        stack: "border1", // 堆叠组名（需唯一）
      },
    ],
  },
});

// 智能象限定位函数
function quadrantPosition(point, params, dom, rect, size) {
  // 获取tooltip尺寸
  const domWidth = dom.offsetWidth;
  const domHeight = dom.offsetHeight;

  // 计算图表中心点
  const centerX = size.viewSize[0] / 2;
  const centerY = size.viewSize[1] / 2;

  // 设置边距
  const padding = -10;

  // 根据象限返回不同位置
  if (point[0] >= centerX && point[1] <= centerY) {
    // 第一象限（右上）
    return [size.viewSize[0] - domWidth - padding, padding];
  } else if (point[0] < centerX && point[1] <= centerY) {
    // 第二象限（左上）
    return [padding, padding];
  } else if (point[0] < centerX && point[1] > centerY) {
    // 第三象限（左下）
    return [padding, size.viewSize[1] - domHeight - padding];
  } else {
    // 第四象限（右下）
    return [
      size.viewSize[0] - domWidth - padding,
      size.viewSize[1] - domHeight - padding,
    ];
  }
}
//资产类别占比饼图
const pieColor1 = ref([
  {
    topColor: "#00aaff",
    bottomColor: "#0252ef",
    borderColor: "#00AAFF",
  }, // 系列1的颜色
  {
    topColor: "#01e9ff",
    bottomColor: "#00aafe",
    borderColor: "#00EAFF",
  }, // 系列1的颜色
  {
    topColor: "#55cb69",
    bottomColor: "#79ed8d",
    borderColor: "#00CC03",
  }, // 系列1的颜色
  {
    topColor: "#fe4c01",
    bottomColor: "#ff5b00",
    borderColor: "#FF5501",
  }, // 系列1的颜色
  {
    topColor: "#ff9e1e",
    bottomColor: "#fef702",
    borderColor: "#FFFF00",
  }, // 系列1的颜色
]);
const pieOption = ref({
  options: {
    tooltip: {
      trigger: "item",
      backgroundColor: "transparent",
      borderColor: "transparent",
      position: quadrantPosition,
      formatter: function (params) {
        let { name, value } = params.data;
        return `
              <div class="tooltip-bg">
                <div class="tooltip-title">${
                  name.length > 11
                    ? name.substring(0, 5) +
                      "..." +
                      name.substring(name.length - 5, name.length)
                    : name
                }</div>
                <div class="tooltip-row">${value}
                </div>
              </div>
            `;
      }, // 可选：自定义提示内容
    },
    series: [
      {
        type: "pie",
        radius: ["50%", "55%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: (params) => {
            let name = params.name && params.name.match(/.{1,6}/g).join("\n");
            return `{d|${params.data.proportion}}\n{b|${name}}`;
          },
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(18),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(13),
              lineHeight: fitChartSize(13),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "类别1",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "类别2",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "类别3",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "类别4",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "类别5",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

//资产增长趋势折线图
const lineOption2 = ref({
  options: {
    color: ["#007CFF", "#00F6FF", "#E16262", "#DD9146"],
    tooltip: {
      trigger: "axis",
      // 自定义 tooltip 内容
      formatter: function (params) {
        let result = params[0].name + "<br>"; // 使用类目名称
        params.forEach((param) => {
          // 获取当前系列的数值，假设数据结构为单数值数组（如 [15, 25, 30]）
          let value = param.value;
          // 如果是多维数组（如 [[0, 15], [1, 25]]），则使用 param.value[1]

          // 判断是否为需要添加百分号的系列
          if (param.seriesName === "报障率") {
            // 或使用 param.seriesIndex === 1
            // 假设数值需要转换为百分比（如 0.15 → 15%）
            value = value + "%";
            // 若数值本身为整数（如 15 → 15%），直接添加%
            // value = value + '%';
          }
          // 拼接每个系列的信息
          result += `<div style="display: flex;justify-content:space-between;align-items:center;gap:0 20px;"><div>${param.marker} ${param.seriesName}</div><b>${value}</b></div>`;
        });
        return result;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      //   selectedMode: false,
      data: ["每日工单量", "报障率", "报障总次数", "新增工单量"],
      itemWidth: fitChartSize(12),
      itemHeight: fitChartSize(12),
      itemGap: fitChartSize(35),
      left: "3%",
      //   left: "center",
      top: "6%",
      icon: "roundRect",
      textStyle: {
        fontSize: fitChartSize(13),
        color: "inherit",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [
        "1月",
        "2月",
        "3月",
        "4月",
        "5月",
        "6月",
        "7月",
        "8月",
        "9月",
        "10月",
        "11月",
        "12月",
      ],
      axisLabel: {
        color: "#fff",
        // rotate: 20,
        formatter: function (value) {
          let val = value.length > 6 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(12),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
        align: "left",
        margin: fitChartSize(40),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#57CEEA",
          opacity: 0.1,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "22%",
      left: "5%",
      height: "68%",
      right: "3%",
    },
    series: [
      {
        name: "每日工单量",
        data: [120, 250, 200, 400, 450, 500, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 124, 255, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 124, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#007CFF",
        },
        lineStyle: {
          color: "#007CFF",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "报障率",
        data: [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(0, 246, 255, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(0, 246, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#00F6FF",
        },
        lineStyle: {
          color: "#00F6FF",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "报障总次数",
        data: [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(225, 98, 98, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(225, 98, 98, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#E16262",
        },
        lineStyle: {
          color: "#E16262",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "新增工单量",
        data: [200, 100, 300, 200, 400, 300, 200, 100, 300, 200, 400, 300],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(221, 145, 70, .8)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(221, 145, 70, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: "#DD9146",
        },
        lineStyle: {
          color: "#DD9146",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [480],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//工单统计1
const lineOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(15),
      itemWidth: fitChartSize(15),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "收到工单总量", itemStyle: { color: "#0783FA" } },
        { name: "处理工单量", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange2 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "15%",
      height: "65%",
      right: "4.5%",
    },
    series: [
      {
        name: "收到工单总量",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(19, 141, 255, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(19, 141, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#0783FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#0783FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "处理工单量",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//工单统计2
const lineOption4 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(15),
      itemWidth: fitChartSize(15),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [{ name: "今日工单", itemStyle: { color: "#F29339" } }],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange3 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(40),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "17%",
      height: "65%",
      right: "5%",
    },
    series: [
      {
        name: "今日工单",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(242, 147, 57, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(242, 147, 57, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#F29339",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#F29339",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//运维考勤打卡情况
const lineOption5 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(15),
      itemWidth: fitChartSize(15),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "应到人员", itemStyle: { color: "#FCEBC0" } },
        { name: "实到人员", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val =
            state.curRange1 == 1 ? value.substring(5, value.length) : value;
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(30),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "12%",
      height: "65%",
      right: "5%",
    },
    series: [
      {
        name: "应到人员",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(242, 147, 57, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(242, 147, 57, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#FCEBC0",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#FCEBC0",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "实到人员",
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [30],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

function handleCurrentChange(number) {
  state.curOrderId = state.idsList[number - 1];
}

// 查看工单详情
function checkOrder(id) {
  state.curOrderId = id;
  state.dialogVisibleOrder = true;
}

function handleToday(val) {
  state.curTodayTab = val;
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let { workOrderProcrssingTime } = res.data;
    if (workOrderProcrssingTime) {
      let {
        maxTroubleId,
        minTroubleId,
        totalMaxProcessingTime,
        totalMinProcessingTime,
      } = workOrderProcrssingTime;

      Object.assign(
        col3_block1_list.value[0],
        getDay_Hour_Minute(totalMaxProcessingTime || 0)
      );
      state.maxList = maxTroubleId || [];
      col3_block1_list.value[0].id = state.maxList[0] || "";
      Object.assign(
        col3_block1_list.value[1],
        getDay_Hour_Minute(totalMinProcessingTime || 0)
      );
      state.minList = minTroubleId || [];
      col3_block1_list.value[1].id = state.minList[0] || "";
      state.handleInfo = col3_block1_list.value[state.curTodayTab];
      console.log(col3_block1_list.value, "最长最短处理时间", state.handleInfo);
    }
  });
}

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getWorkOrderLine1();
  }
  if (type == 1) {
    state.curRange2 = tab;
    getWorkOrderLine2();
  }
  if (type == 2) {
    state.curRange3 = tab;
    getWorkOrderLine3();
  }
}

// 点击折线图 年月日 筛选
function handleRange2(type, tab) {
  if (type == 0) {
    state.curRange4 = tab;
    getWorkOrderStatistics({ range: state.curRange4 }).then((res) => {
      console.log(res, "工单统计");

      if (res.code == 200 && res.data) {
        let arr = [],
          data1 = [],
          data2 = [],
          data3 = [],
          data4 = [];
        let { workOrderAndDeviceTroubleVO } = res.data;
        if (workOrderAndDeviceTroubleVO) {
          workOrderAndDeviceTroubleVO.map((item) => {
            arr.push(item.date);
            data1.push(item.dailyPendingOrders || 0);
            data2.push(item.deviceFaultRate.replace("%", "") * 1 || 0);
            data3.push(item.cumulativeFaultCount || 0);
            data4.push(item.dailyNewOrders || 0);
          });
          lineOption2.value.options.xAxis.data = arr;
          lineOption2.value.options.series[0].data = data1;
          lineOption2.value.options.series[1].data = data2;
          lineOption2.value.options.series[2].data = data3;
          lineOption2.value.options.series[3].data = data4;
          const yMax =
            Math.max(...[...data1, ...data2, ...data3, ...data4]) * 1.2;
          lineOption2.value.options.series[4].data = [yMax];
        }
      }
    });
  }
}

function handleCheck(item) {
  state.deptInfo = {
    ...item,
  };
  state.dialogVisible = true;
}

function setAnimate(flag) {
  let timer = null;
  if (flag) {
    play();
  } else {
    window.clearInterval(state.scrolltimer1);
    timer = setTimeout(() => {
      state.animate1 = false;
      clearTimeout(timer);
    }, 1000);
  }
}

function play() {
  let timer = null;
  if (state.checkList.length >= 5 && state.animate1 == false) {
    clearTimeout(state.scrolltimer1);
    state.scrolltimer1 = setTimeout(() => {
      let arr = state.checkList;
      let obj = JSON.parse(JSON.stringify(arr[0]));
      arr.push(obj);
      state.animate1 = true;
      clearTimeout(timer);
      timer = setTimeout(() => {
        arr.shift();
        state.animate1 = false;
        play();
        clearTimeout(timer);
      }, 1000);
    }, 2000);
  }
}

//获取数据
function getData(flag = true) {
  window.clearInterval(state.scrolltimer1);
  state.animate1 = false;
  schoolInspectionList({ pageNum: 1, pageSize: 9999 }).then((res) => {
    console.log("巡检记录列表", res);
    if (res.data) {
      if (flag) {
        let timer = setTimeout(() => {
          state.checkList = JSON.parse(JSON.stringify(res.data.records)) || [];
          play();
          clearTimeout(timer);
        }, 1000);
      } else {
        state.checkList = JSON.parse(JSON.stringify(res.data.records)) || [];
        play();
      }
    }
  });

  handleRange2(0, state.curRange4);
  handleToday(state.curTodayTab);

  getSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        assetsTotal,
        restTotal,
        maintenanceTotal,
        dutyTotal,
        runTotal,
        scrapTotal,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
      } = res.data;
      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;

      objData.value = {
        ...objData.value,
        sparepartsUseStatistics: res.data.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      if (assetsTypeStatistics) {
        let arr = [],
          data1 = [],
          data2 = [];
        assetsTypeStatistics?.slice(0, 5).map((item, index) => {
          arr.push(item.assetsTypeName);
          data1.push(item.num);
          data2.push(item.proportion);
        });
        barOption.value.options.xAxis.data = arr;
        barData1.value = data1;
        const yMax = Math.max(...data1) * 1.2;
        const newHeight = yMax * borderHeight1 * 0.01;
        barOption.value.options.series[0].data = barData1.value.map(
          (val, index) => ({
            value: val > newHeight ? newHeight : 0,
            itemStyle: {
              color:
                barColor1.value[index % barColor1.value.length].borderColor,
            },
          })
        );
        barOption.value.options.series[1].data = barData1.value.map(
          (val, index) => ({
            name: arr[index],
            value: val > newHeight ? val - newHeight : val,
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color:
                      barColor1.value[index % barColor1.value.length].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color:
                      barColor1.value[index % barColor1.value.length]
                        .bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
            },
          })
        );

        // 资产类别占比饼图赋值
        pieOption.value.options.series[0].data = barData1.value.map(
          (val, index) => ({
            name: arr[index],
            value: val,
            proportion: data2[index],
            itemStyle: {
              color: {
                type: "linear",
                x: 0,
                y: 0,
                x2: 0,
                y2: 1,
                colorStops: [
                  {
                    offset: 0,
                    color:
                      pieColor1.value[index % pieColor1.value.length].topColor, // 0% 处的颜色
                  },
                  {
                    offset: 1,
                    color:
                      pieColor1.value[index % pieColor1.value.length]
                        .bottomColor, // 100% 处的颜色
                  },
                ],
                global: false, // 缺省为 false
              },
              bgColor:
                pieColor1.value[index % pieColor1.value.length].borderColor,
            },
          })
        );
      }
      console.log(pieOption.value, barOption.value, "资产类别111");

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];
        optList1.value[0].total = knowledgeBaseMonth;
        optList1.value[0].ratio = knowledgeBaseMonthRise;
        optList1.value[0].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;

        optList1.value[1].total = knowledgeBaseYear;
        optList1.value[1].ratio = knowledgeBaseYearRise;
        optList1.value[1].num =
          knowledgeBaseYearRise?.replace("%", "") * 1 || 0;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

function getWorkOrderLine1() {
  getMaintainCountStatistics({ range: state.curRange1 }).then((res) => {
    console.log(res, "运维统计");
    let { clockStatistics, shouldClockCountToday, clockedCountToday } =
      res.data;
    optList5.value[0].total = shouldClockCountToday;
    optList5.value[1].total = clockedCountToday;
    if (clockStatistics) {
      let data1 = [],
        data2 = [];
      lineOption5.value.options.xAxis.data =
        clockStatistics?.map((item) => {
          return item.dateLabel;
        }) || [];
      lineOption5.value.options.series[0].data = data1 =
        clockStatistics?.map((item) => item.shouldClockUsers) || [];
      lineOption5.value.options.series[1].data = data2 =
        clockStatistics?.map((item) => item.clockedUsers) || [];
      const yMax = Math.max(...[...data1, ...data2]) * 1.2;
      lineOption5.value.options.series[2].data = [yMax];
    }
  });
}

function getWorkOrderLine2() {
  getWorkOrderTimeStatistics({ range: state.curRange2 }).then((res) => {
    console.log(res, "收到工单数/处理工单数统计");
    let { workOrderStatistics } = res.data;
    if (workOrderStatistics) {
      let data1 = [],
        data2 = [];
      lineOption3.value.options.xAxis.data =
        workOrderStatistics?.map((item) => {
          return item.statisticsTime;
        }) || [];
      lineOption3.value.options.series[0].data = data1 =
        workOrderStatistics?.map((item) => item.workOrderTotal) || [];
      lineOption3.value.options.series[1].data = data2 =
        workOrderStatistics?.map((item) => item.processingWorkOrder) || [];
      const yMax = Math.max(...[...data1, ...data2]) * 1.2;
      lineOption3.value.options.series[2].data = [yMax];
    }
  });
}

function getWorkOrderLine3() {
  getWorkOrderTime({ range: state.curRange3 }).then((res) => {
    console.log(res, "工单平均处理时间统计");
    let { workOrderTimeStatistics } = res.data;
    if (workOrderTimeStatistics) {
      let data = [];
      lineOption4.value.options.xAxis.data =
        workOrderTimeStatistics?.map((item) => {
          return item.statisticsTime;
        }) || [];
      lineOption4.value.options.series[0].data = data =
        workOrderTimeStatistics?.map((item) => item.avgProcessingTime) || [];
      const yMax = Math.max(...data) * 1.2;
      lineOption4.value.options.series[1].data = [yMax];
    }
  });
}

function getClockData() {
  getClockOffsetStatistics({ range: state.curRangeTab }).then((res) => {
    console.log(res, "考勤统计");
    if (res.data) {
      const { totalTimeOffset, totalPlaceOffset } = res.data;
      col3_block2_list.value[0].num = Math.abs(totalTimeOffset);
      col3_block2_list.value[1].num = Math.abs(totalPlaceOffset);
    } else {
      col3_block2_list.value[0].num = 0;
      col3_block2_list.value[1].num = 0;
    }
  });
}

// 根据分钟获取 天、小时、分钟 的对象
function getDay_Hour_Minute(minute) {
  minute = Math.abs(minute);
  let obj = { day: 0, hour: 0, minute: 0 };
  if (minute < 24 * 60) {
    obj.day = 0;
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  } else {
    obj.day = Math.floor(minute / (24 * 60));
    minute = minute % (24 * 60);
    if (minute < 60) {
      obj.hour = 0;
      obj.minute = minute;
    } else {
      obj.hour = Math.floor(minute / 60);
      obj.minute = minute % 60;
    }
  }
  return obj;
}

function getOrderData() {
  getClockData();
  getWorkOrderLine1();
  getWorkOrderLine2();
  getWorkOrderLine3();
  getWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let {
        workOrderTotal,
        workOrderYesterdayTotal,
        workOrderTotalIncrease,
        workOrderTodayAddTotal,
        workOrderYesterdayAddTotal,
      } = workOrderProportion;

      col2_block1_list.value[0].num = workOrderTotal;
      col2_block1_list.value[0].ratio = workOrderTotalIncrease;
      col2_block1_list.value[0].increaseNum =
        workOrderTotal - workOrderYesterdayTotal;

      col2_block1_list.value[3].num = workOrderTodayAddTotal;
      col2_block1_list.value[3].ratio = workOrderYesterdayAddTotal
        ? (
            ((workOrderTodayAddTotal - workOrderYesterdayAddTotal) /
              workOrderYesterdayAddTotal) *
              100 || 0
          ).toFixed(2) + "%"
        : "0.00%";
      col2_block1_list.value[3].increaseNum =
        workOrderTodayAddTotal - workOrderYesterdayAddTotal;

      col2_block1_list.value[1].num = workOrderTotal;
      col2_block1_list.value[1].ratio =
        workOrderTotal - workOrderYesterdayTotal;
      col2_block1_list.value[1].increaseNum =
        workOrderTotal - workOrderYesterdayTotal;

      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      col2_block1_list.value[2].num = deviceFaultTotalIncrease;
      col2_block1_list.value[2].ratio =
        (
          (deviceFaultTotalIncrease?.replace("%", "") * 1 || 0) -
          (deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 || 0)
        ).toFixed(2) + "%";
      col2_block1_list.value[2].increaseNum =
        deviceFaultTotal - deviceFaultYesterdayTotal;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList2.value[0].total = processingWorkOrderMonth;
      optList2.value[0].ratio = processingWorkOrderMonthRise;
      optList2.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList2.value[1].total = processingWorkOrderYear;
      optList2.value[1].ratio = processingWorkOrderYearRise;
      optList2.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        allMaxProcessingTime,
        allMinProcessingTime,
        maxTroubleId,
        minTroubleId,
        totalMaxProcessingTime,
        totalMinProcessingTime,
        yesterdayAllMaxProcessingTime,
        yesterdayAverageProcessingTime,
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
        yesterdayTotalAverageProcessingTime,
      } = workOrderProcrssingTime;

      Object.assign(
        col3_block1_list.value[0],
        getDay_Hour_Minute(totalMaxProcessingTime || 0)
      );
      state.maxList = maxTroubleId || [];
      col3_block1_list.value[0].id = state.maxList[0] || "";
      Object.assign(
        col3_block1_list.value[1],
        getDay_Hour_Minute(totalMinProcessingTime || 0)
      );
      state.minList = minTroubleId || [];
      col3_block1_list.value[1].id = state.minList[0] || "";
      state.handleInfo = { ...col3_block1_list.value[state.curTodayTab] };

      optList4.value[0].total = optList4.value[1].total = Math.floor(
        todayAverageProcessingTime?.replace("分钟", "") * 1 || 0
      );
      optList4.value[0].ratio = riseOfProcessingTime || "0.00%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;
      optList4.value[0].increase = Math.floor(
        (todayAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
          (yesterdayAverageProcessingTime?.replace("分钟", "") * 1 || 0)
      );

      optList4.value[1].total = Math.floor(
        totalAverageProcessingTime?.replace("分钟", "") * 1 || 0
      );
      optList4.value[1].fq = averageTime;
      optList4.value[1].ratio =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (
              (((totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) /
                (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 ||
                  0)) *
              100
            ).toFixed(2) + "%"
          : "0.00%";
      optList4.value[1].num =
        yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0
          ? (totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) -
            (yesterdayTotalAverageProcessingTime?.replace("分钟", "") * 1 || 0)
          : 0;
    }
  });
}

const initPieFunc = (name) => {
  document.querySelectorAll(`.${name}-item`).forEach((div) => {
    div.addEventListener("mouseover", function (e) {
      const index = parseInt(this.dataset.index); // 获取对应的数据索引
      state[`${name}Ref`].dispatchAction({
        type: "highlight", // 触发高亮动作
        seriesIndex: 0, // 系列索引（第一个饼图）
        dataIndex: index, // 数据项索引
      });
    });

    div.addEventListener("mouseout", function () {
      const index = parseInt(this.dataset.index);
      state[`${name}Ref`].dispatchAction({
        type: "downplay", // 取消高亮
        seriesIndex: 0,
        dataIndex: index,
      });
    });
  });
};

onMounted(() => {
  proxy.$modal.loading();
  getData(false);
  getOrderData();
  initPieFunc("pieData");
  //   state.timer = setInterval(() => {
  //     state.curTime += 1000;
  //   }, 1000);
  state.timer2 = setInterval(() => {
    getData();
    getOrderData();
  }, 60000);
});

onBeforeUnmount(() => {
  window.clearInterval(state.scrolltimer1);
  //   clearInterval(state.timer);
  clearInterval(state.timer2);
});
</script>
      
      <style lang="scss" scoped>
@font-face {
  font-family: "HYYaKuHeiW";
  src: url("@/assets/fontFamily/HYYaKuHeiW.ttf") format("truetype");
  font-weight: normal;
  font-style: normal;
}
@font-face {
  font-family: "YouSheBiaoTiHei";
  //   src: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/ysbth.TTF")
  //     format("truetype");
  src: url("@/assets/fontFamily/ysbth.TTF") format("truetype");
  font-weight: normal;
  font-style: normal;
}

:deep(.tooltip-bg) {
  background-color: transparent;
  padding: 0.2vw;
  width: 5.5vw;
  height: 2.5vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/assets-type_frame.png");
  background-size: 100% 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
:deep(.tooltip-title) {
  font-size: 0.5vw;
  line-height: 0.6vw;
  width: 5vw;
  white-space: wrap;
  color: #fff;
}
:deep(.tooltip-row) {
  font-size: 0.8vw;
  font-weight: bold;
  line-height: 1;
  color: #0bf9fe;
}
::-webkit-scrollbar {
  width: 0px; /* 滚动条宽度 */
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  color: #b0c0e6;
  padding: 0.2vw 0vw 0.2vw 0.5vw;
  font-size: 0.7vw;
  position: relative;
  display: flex;
  align-items: center;
  justify-content: space-between;
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.4vw;
      color: #4095e5;
      &_item {
        border: 1px solid rgba(255, 255, 255, 0);
        padding: 0.05vw 0.3vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/range-bg.png");
          background-size: 100% 100%;
          border: 1px solid rgba(255, 255, 255, 0.1);
        }
      }
    }
  }
}
.unify-main_charts {
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  margin-top: -8vw;
  overflow: hidden;

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;

      &-subTitle {
        font-size: 0.65vw;
        height: 1.3vw;
        line-height: 1.3vw;
        padding-left: 2vw;
        margin-top: 1vw;
        color: #fff;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
        background-size: 100% 100%;
        text-shadow: 0px 0px 4px #0091ff;
      }
      &-title {
        font-size: 0.85vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1.5vw;
        position: relative;
        font-family: "HYYaKuHeiW";
        // margin-top: 1vw;
        .titleText {
          transform: scale(1, 0.993905) skew(-6.290719deg, 0deg);
          background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
            #ffffff;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent;
        }

        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg.png");
        background-size: 100% 100%;
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("@/assets/screen/app/trend-info_bg.png");
      background-size: 100% 100%;
      padding: 0.3vw 0.5vw 0.7vw;
      font-size: 0.6vw;
      color: #8495b6;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.5vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          // padding-right: 0.2vw;
          color: #0bf9fe;
        }

        div {
          padding-left: 0.5vw;
        }

        .up {
          //   color: #2dcd5e;
          color: #ff2200;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            // top: 0;
            // left: 0;
            // width: 0.5vw;
            // height: 0.58vw;
            // background: url("@/assets/screen/arrow_up.png");
            top: 0;
            left: -0.5vw;
            width: 1vw;
            height: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          //   color: #fe005d;
          color: #3be27e;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            // top: 0.05vw;
            // left: 0.05vw;
            // width: 0.5vw;
            // height: 0.58vw;
            // background: url("@/assets/screen/arrow_low.png");
            top: 0;
            left: -0.5vw;
            width: 1vw;
            height: 0.5vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #0bf9fe;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 26vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        font-family: "ZhengQingKeHuangYouTi";
        height: 6.5vw;
        width: 100%;
        text-align: center;
        line-height: 5.5vw;
        font-size: 1.8vw;
        color: #fff;
      }
    }

    &-block3 {
      .echart-item {
        position: relative;
        .export-btn {
          z-index: 11;
          cursor: pointer;
          position: absolute;
          font-size: 0.45vw;
          right: 0;
          top: 0.5vw;
          width: 2.8vw;
          height: 0.9vw;
          line-height: 1vw;
          padding-left: 0.5vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/export-bg.png");
          background-size: 100% 100%;
          color: #fff;
        }
      }
      .pieData {
        &-bg {
          position: absolute;
          top: 2.2vw;
          left: 3vw;
          width: 6.5vw;
          height: 6.5vw;
        }
      }
    }

    &-block4 {
      margin-top: 1vw;
      color: #fff;
      .table {
        font-size: 0.6vw;
        padding-bottom: 0.5vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/table-bg.png");
        background-size: 100% 100%;
        .opt-title {
          margin-bottom: 0.8vw;
          font-size: 0.75vw;
        }

        &-row {
          display: flex;
          padding: 0.25vw 0;
          text-align: center;
          margin-top: 0.3vw;

          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 6.5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 9vw;
          }
        }
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 45vw;
    .rangeTab {
      position: absolute;
      right: 0;
      top: 0;
      display: flex;
      font-size: 0.5vw;
      gap: 0 0.3vw;
      padding-top: 1vw;
      color: #b4c0cc;
      z-index: 11;
      &_item {
        cursor: pointer;
        padding: 0.2vw 0.5vw;
        background-color: #3a4356;
        &.active {
          color: #1fc6ff;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
          background-size: 100% 100%;
        }
      }
    }
    &-block1 {
      width: 100%;
      // border: 1px solid red;
      display: flex;
      justify-content: space-around;
      font-size: 0.8vw;
      color: #fff;
      //   gap: 0 4vw;
      &_item {
        // border: 1px solid red;
        // letter-spacing: 0.1vw;
        // gap: 0 0.5vw;
        .card {
          // border: 1px solid red;
          display: flex;
          align-items: center;
          font-size: 0.7vw;
          font-family: "HYYaKuHeiW";
          width: 100%;
          padding: 0.2vw 0.3vw 0.5vw;
          gap: 0 1vw;
          // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-title_bg.png");
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/maintain-info_titleBg.png");
          background-size: 100% 100%;
          margin-bottom: 0.8vw;
          &-right {
            font-family: "微软雅黑";
            font-size: 0.8vw;
            font-weight: bold;
          }
        }
        .info {
          img {
            width: 6vw;
            height: 3.5vw;
          }
          .tip {
            display: flex;
            flex-direction: column;
            gap: 0.35vw 0;
            &-ratio,
            &-change {
              display: flex;
              align-items: center;
              gap: 0 1vw;
              padding: 0 0.5vw;
              height: 1.6vw;
              min-width: 7vw;
              font-size: 0.6vw;
              //   justify-content: center;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/info-bg.png");
              background-size: 100% 100%;
              .ratio {
                color: #fff;
                font-size: 0.9vw;
                &.up {
                  color: #07f29e;
                }
                &.low {
                  color: #f22207;
                }
              }
            }
            &-ratio {
              font-size: 0.7vw;
              justify-content: space-between;
              // justify-content: flex-start;
              // align-items: flex-start;
            }
            &-change {
              color: #fff;
              span {
                font-size: 0.75vw;
              }
              &.up span {
                color: #07f29e;
              }
              &.low span {
                color: #f22207;
              }
            }
          }
        }
      }
    }

    &-block2 {
      // border: 1px solid red;
      margin: 1vw 0;
      .echart-item {
        position: relative;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/line-block_bg.png");
        background-size: 100% 100%;
      }
    }

    &-block3 {
      //   border: 1px solid red;
      margin-top: 1vw;
      .block-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        padding-top: 1.8vw;
      }
      .block-right2 {
        position: relative;
        padding-top: 0vw;
        gap: 0vw;
        &::after {
          //   position: absolute;
          //   content: "";
          //   display: block;
          //   width: 9vw;
          //   height: 0.1vw;
          //   color: #fff;
          //   top: 0;
          //   left: 0;
        }
      }
      .item-opt {
        text-align: center;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg.png");
        background-size: 100% 100%;
        padding: 0.5vw;
        color: #fff;

        &_row {
          margin: 0;
          padding: 0;
          padding-top: 0.5vw;
          span {
            color: #fff;
          }
        }
        .minute {
          color: #0bf9fe;
        }
      }
      .item-opt2 {
        background: none;
        color: #fff;
        padding: 0.4vw;
        .top {
          white-space: nowrap;
          font-size: 0.7vw;
          padding: 0.4vw 0.5vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-title_bg.png");
          background-size: 100% 100%;
        }
        .bottom {
          padding: 0.2vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg2.png");
          background-size: 100% 100%;
        }
      }
      .center {
        margin: 0.2vw auto;
        // color: #4095e5;
        text-align: left;
        span {
          color: #0bf9fe;
          font-size: 0.7vw;
          font-weight: bold;
        }
      }
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #b4c0cc;
        z-index: 11;
        &_item {
          cursor: pointer;
          padding: 0vw 0.5vw;
          background-color: #3a4356;
          height: 1.3vw;
          line-height: 1.3vw;
          &.active {
            color: #1fc6ff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
            background-size: 100% 100%;
          }
        }
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 22vw;
    height: 100%;

    &-block2 {
      display: flex;
      flex-direction: column;
      gap: 1vw 0;
      margin-bottom: 1vw;
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #b4c0cc;
        z-index: 11;
        &_item {
          cursor: pointer;
          padding: 0vw 0.5vw;
          background-color: #3a4356;
          height: 1.3vw;
          line-height: 1.3vw;
          &.active {
            color: #1fc6ff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
            background-size: 100% 100%;
          }
        }
      }
      &_tabs {
        // border: 1px solid red;
        height: 3vw;
        cursor: pointer;
        display: flex;
        align-items: center;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0.4vw;
        gap: 0 0.5vw;
        font-family: "HYYaKuHeiW";
        color: #fff;

        .left,
        .center,
        .right {
          flex: 1;
          height: 1.5vw;
          line-height: 1.5vw;
          font-size: 0.9vw;
          text-align: center;
          background-color: #000;
          &.active {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-item.png");
            background-size: 100% 100%;
          }
        }
      }
      &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.65vw;
        // border: 1px solid red;
        padding: 0 1vw 0 4.5vw;
        height: 3.8vw;
        position: relative;
        &::before {
          // border: 1px solid red;
          content: "";
          width: 2.6vw;
          height: 2.6vw;
          position: absolute;
          left: 1vw;
          top: 0.7vw;
        }
        .left {
          width: 100%;
          display: flex;
          align-items: center;
          gap: 0 1vw;
          padding: 0.8vw 0;
          font-size: 0.9vw;
          color: #fff;
          span {
            display: inline-block;
            font-size: 1.2vw;
            font-weight: bold;
            font-family: "YouSheBiaoTiHei";
          }
        }
        .right {
          text-decoration: underline;
          font-size: 0.75vw;
          cursor: pointer;
        }
        &.item1 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/blue-bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon1.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #4095e5;
          }
        }
        &.item2 {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/orange-bg.png");
          background-size: 100% 100%;
          &::before {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon3.png");
            background-size: 100% 100%;
          }
          .left span,
          .right {
            color: #fea41d;
          }
        }
      }
    }

    &-block1 {
      display: flex;
      flex-direction: column;
      gap: 1vw 0;
      &_item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        font-size: 0.65vw;
        // border: 1px solid red;
        padding: 1.2vw 1vw 1.2vw 4.5vw;
        position: relative;
        &::before {
          // border: 1px solid red;
          content: "";
          width: 2.6vw;
          height: 2.6vw;
          position: absolute;
          left: 1vw;
          top: 0.5vw;
        }
        .left {
          line-height: 1.3vw;
          color: #fff;
          font-size: 0.9vw;
          span {
            // font-size: 0.9vw;
            padding-right: 0.2vw;
            letter-spacing: 0.05vw;
          }
        }
        .right {
          text-decoration: underline;
          font-size: 0.9vw;
          font-weight: bold;
          cursor: pointer;
          color: #0bf9fe;
          padding-right: 1vw;
        }
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/handle-bg.png");
        background-size: 100% 100%;
        &::before {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/maintain/range-icon2.png");
          background-size: 100% 100%;
        }
      }
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #7fcff4;
        z-index: 11;
        font-family: "微软雅黑";
        &_item {
          cursor: pointer;
          height: 1.4vw;
          padding: 0 1vw;
          line-height: 1.4vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/network-btn.png");
          background-size: 100% 100%;
          &.active {
            color: #fff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/network-btn_checked.png");
            background-size: 100% 100%;
          }
        }
      }
    }

    &-block3 {
      .block-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        padding-top: 1.5vw;
        padding-left: 0.4vw;
      }
      .item-opt {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg3.png");
        background-size: 100% 100%;
        padding: 0.35vw 0.5vw 0.5vw;
        &_row {
          margin: 0;
          padding: 0;
          padding-top: 0.5vw;
        }
      }
      .center {
        margin: 0.2vw auto;
        color: #fff;
        font-size: 0.8vw;
        text-align: center;
      }
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #b4c0cc;
        z-index: 11;
        &_item {
          cursor: pointer;
          padding: 0vw 0.5vw;
          background-color: #3a4356;
          height: 1.3vw;
          line-height: 1.3vw;
          &.active {
            color: #1fc6ff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
            background-size: 100% 100%;
          }
        }
      }
    }

    &-block4 {
      margin: 1vw 0;
      color: #fff;
      .table-data {
        overflow: hidden;
        // border: 1px solid red;
        height: 9vw;
        &_inner {
          transition: all 0.5s ease-out;
          margin-top: -1.8vw;
        }
      }
      .table {
        font-size: 0.6vw;
        padding-bottom: 0.1vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/table-bg.png");
        background-size: 100% 100%;

        .opt-title {
          padding-top: 0.3vw;
          margin-bottom: 0.8vw;
          font-size: 0.75vw;
        }

        &-row {
          display: flex;
          height: 1.5vw;
          line-height: 1.5vw;
          text-align: center;
          //   border: 1px solid red;
          margin-bottom: 0.3vw;

          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 6.5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
          .status {
            color: #f22207;
            &.finish {
              color: #3be27e;
            }
          }
        }
      }
    }
  }
}
</style>