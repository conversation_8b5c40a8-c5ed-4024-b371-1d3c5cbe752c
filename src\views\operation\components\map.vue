<template>
  <div class="tencent_map">
    <el-form ref="form" label-suffix="：" label-width="100px">
      <el-row :gutter="10">
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="详细地址">
            <el-autocomplete
              style="width: 100%"
              popper-class="autoAddressClass"
              v-model="mapData.address"
              :fetch-suggestions="querySearch"
              :trigger-on-focus="false"
              placeholder="请输入详细地址"
              @select="handleSelect"
              clearable
            >
              <template #default="{ item }">
                <div class="autoAddressClass_item">
                  <ElIcon :size="20" color="balck">
                    <Search />
                  </ElIcon>
                  <div>
                    <div class="title">{{ item.title }}</div>
                    <span class="address ellipsis">{{ item.address }}</span>
                  </div>
                </div>
              </template>
            </el-autocomplete>
          </el-form-item>
        </el-col>
        <el-col :xs="24" :sm="12" :md="12" :lg="12" :xl="12">
          <el-form-item label="经纬度" prop="projectId">
            <el-input
              disabled
              placeholder="请选择经纬度"
              v-model="mapData.point"
              type="text"
              :readonly="true"
            ></el-input>
          </el-form-item>
        </el-col>
      </el-row>
      <div class="map-container">
        <div id="map" :style="{ width, height }" v-loading="!mapLoaded"></div>
        <!-- <div class="location-btn" @click="handleLocationClick">
                    <el-icon :size="20" color="#409EFF">
                        <Location />
                    </el-icon>
                </div> -->
      </div>
    </el-form>
  </div>
</template>

<script setup>
import { Search, Location } from "@element-plus/icons-vue";
import { ref, onMounted } from "vue";


// 添加坐标转换对象
const WGS84_TO_GCJ02 = {
  PI: 3.14159265358979324,
  a: 6378245.0,
  ee: 0.00669342162296594323,

  transformLat(x, y) {
    let ret = -100.0 + 2.0 * x + 3.0 * y + 0.2 * y * y + 0.1 * x * y + 0.2 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * this.PI) + 20.0 * Math.sin(2.0 * x * this.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(y * this.PI) + 40.0 * Math.sin(y / 3.0 * this.PI)) * 2.0 / 3.0;
    ret += (160.0 * Math.sin(y / 12.0 * this.PI) + 320 * Math.sin(y * this.PI / 30.0)) * 2.0 / 3.0;
    return ret;
  },

  transformLng(x, y) {
    let ret = 300.0 + x + 2.0 * y + 0.1 * x * x + 0.1 * x * y + 0.1 * Math.sqrt(Math.abs(x));
    ret += (20.0 * Math.sin(6.0 * x * this.PI) + 20.0 * Math.sin(2.0 * x * this.PI)) * 2.0 / 3.0;
    ret += (20.0 * Math.sin(x * this.PI) + 40.0 * Math.sin(x / 3.0 * this.PI)) * 2.0 / 3.0;
    ret += (150.0 * Math.sin(x / 12.0 * this.PI) + 300.0 * Math.sin(x / 30.0 * this.PI)) * 2.0 / 3.0;
    return ret;
  },

  transform(wgLat, wgLng) {
    if (this.outOfChina(wgLat, wgLng)) {
      return { lat: wgLat, lng: wgLng };
    }
    let dLat = this.transformLat(wgLng - 105.0, wgLat - 35.0);
    let dLng = this.transformLng(wgLng - 105.0, wgLat - 35.0);
    let radLat = wgLat / 180.0 * this.PI;
    let magic = Math.sin(radLat);
    magic = 1 - this.ee * magic * magic;
    let sqrtMagic = Math.sqrt(magic);
    dLat = (dLat * 180.0) / ((this.a * (1 - this.ee)) / (magic * sqrtMagic) * this.PI);
    dLng = (dLng * 180.0) / (this.a / sqrtMagic * Math.cos(radLat) * this.PI);
    return {
      lat: wgLat + dLat,
      lng: wgLng + dLng
    };
  },

  outOfChina(lat, lng) {
    if (lng < 72.004 || lng > 137.8347) return true;
    if (lat < 0.8293 || lat > 55.8271) return true;
    return false;
  }
};

// 修改props的默认值
const { width, height, zoom } = defineProps({
  width: {
    type: String,
    default: "960px",
  },
  height: {
    type: String,
    default: "400px",
  },
  zoom: {
    type: Number,
    default: 15, // 增大默认缩放级别以获得更好的细节
  },
});

const mapLoaded = ref(false);
const mapData = ref({
  address: "",
  point: "",
  lat: "",
  lng: "",
});
const emits = defineEmits(["getPoint"]);

let map = null;
let marker = null;
let geocoder = null;

// 加载腾讯地图API
function loadTencentMap() {
  return new Promise((resolve, reject) => {
    if (window.TMap) {
      resolve(window.TMap);
      return;
    }

    window.init = function() {
      // 确保所有服务都加载完成
      const checkService = setInterval(() => {
        if (window.TMap && window.TMap.service) {
          clearInterval(checkService);
          resolve(window.TMap);
        }
      }, 100);

      // 设置超时
      /* setTimeout(() => {
        clearInterval(checkService);
        reject(new Error('TMap service load timeout'));
      }, 1000); */
    };

    const script = document.createElement("script");
    script.type = "text/javascript";
    script.src = `https://map.qq.com/api/gljs?v=1.exp&key=T7GBZ-CWSW5-HXVIJ-IOR44-YLMQ5-ZCBE7&libraries=service&callback=init`;
    document.head.appendChild(script);

    script.onerror = () => reject(new Error('Failed to load TMap script'));
  });
}

// 获取当前位置
function getCurrentLocation() {
  return new Promise((resolve, reject) => {
    /* if (!navigator.geolocation) {
      resolve({ lat: 39.9042, lng: 116.4074 }); // 默认使用北京坐标
      return;
    } */

    navigator.geolocation.getCurrentPosition(
      (position) => {
        // 原始 WGS84 坐标
        const wgs84Coords = {
          lat: position.coords.latitude,
          lng: position.coords.longitude
        };
        
        console.log('原始位置信息(WGS84):', {
          ...wgs84Coords,
          accuracy: position.coords.accuracy,
          altitude: position.coords.altitude,
          altitudeAccuracy: position.coords.altitudeAccuracy,
          heading: position.coords.heading,
          speed: position.coords.speed,
          timestamp: new Date(position.timestamp).toLocaleString()
        });

        // 转换为 GCJ02 坐标
        // const gcj02Coords = WGS84_TO_GCJ02.transform(wgs84Coords.lat, wgs84Coords.lng);
        // console.log('转换后坐标(GCJ02):', gcj02Coords);
        
        // resolve(gcj02Coords);
        resolve(wgs84Coords);
      },
      (error) => {
        console.warn("获取位置失败，使用默认位置", error);
        alert("您的浏览器不支持地理位置功能");
        resolve({ lat: 39.9042, lng: 116.4074 }); // 默认使用北京坐标
      },
      {
        enableHighAccuracy: true,
        timeout: 1000,
        maximumAge: 0,
      }
    );
  });
}

onMounted(async () => {
  try {
    await loadTencentMap();
    
    // 确保 TMap 和服务都已加载
    if (!window.TMap || !TMap.service) {
      throw new Error('TMap or TMap.service not loaded');
    }

    mapLoaded.value = true;

    // 初始化地图
    map = new TMap.Map("map", {
      zoom: zoom,
      pitch: 0,
      rotation: 0,
    });

    // 初始化地理编码服务
    try {
      geocoder = new TMap.service.Geocoder();
    } catch (error) {
      console.error('Failed to initialize Geocoder:', error);
      return;
    }

    const currentPosition = await getCurrentLocation();
    const center = new TMap.LatLng(currentPosition.lat, currentPosition.lng);
    map.setCenter(center);

    // 创建标记
    marker = new TMap.MultiMarker({
      map: map,
      styles: {
        "marker": new TMap.MarkerStyle({
          width: 25,
          height: 35,
          anchor: { x: 12.5, y: 35 },
        })
      },
      geometries: [{
        id: 'marker',
        position: center,
        styleId: 'marker'
      }]
    });

    getAddrByPoint(center);

    // 修改地图点击事件处理
    map.on("click", function(evt) {
      const clickLatLng = evt.latLng;
      
      // 更新标记位置
      marker.updateGeometries([{
        id: 'marker',
        position: clickLatLng,
        styleId: 'marker'
      }]);
      
      // 将地图中心移动到点击位置
      map.panTo(clickLatLng);
      
      // 获取地址信息
      getAddrByPoint(clickLatLng);
    });

  } catch (error) {
    // console.error("腾讯地图加载失败:", error);
  }
});

function querySearch(str, cb) {
  if (!str) {
    cb([]);
    return;
  }

  // 确保 TMap 已加载
  if (!window.TMap || !TMap.service) {
    console.error('TMap service not loaded');
    cb([]);
    return;
  }

  // 使用 Suggestion 服务
  const suggestion = new TMap.service.Suggestion({
    pageSize: 10,
    region: '全国'
  });

  suggestion.getSuggestions({
    keyword: str,
    region: '全国'
  }).then(result => {
    if (result.status === 0) {
      const suggestions = result.data.map(item => ({
        value: item.title,
        title: item.title,
        address: item.address,
        point: {
          lat: Number(item.location?.lat),
          lng: Number(item.location?.lng)
        }
      }));
      cb(suggestions);
    } else {
      console.error('搜索建议请求失败:', result);
      cb([]);
    }
  }).catch(error => {
    console.error('搜索建议请求错误:', error);
    cb([]);
  });
}

function handleSelect(item) {
  if (!item || !item.point) {
    console.error('选择的地址数据无效');
    return;
  }

  const position = new TMap.LatLng(item.point.lat, item.point.lng);
  
  mapData.value = {
    address: item.address || item.title,
    point: `${item.point.lat},${item.point.lng}`,
    lat: item.point.lat,
    lng: item.point.lng,
  };

  // 更新标记位置
  if (marker) {
    marker.updateGeometries([{
      id: 'marker',
      position: position,
      styleId: 'marker'
    }]);

    map.setCenter(position);
    emits("getPoint", mapData.value);
  } else {
  }
}

// 修改 getAddrByPoint 函数，添加 panTo
function getAddrByPoint(latLng) {
  if (!geocoder) {
    return;
  }

  geocoder.getAddress({ 
    location: latLng,
    get_poi: 1
  }).then(result => {
    if (result.status === 0) {
      const address = result.result.address;
      mapData.value = {
        address: address,
        point: `${latLng.lat},${latLng.lng}`,
        lat: latLng.lat,
        lng: latLng.lng,
      };
      
      map.panTo(latLng);
      emits("getPoint", mapData.value);
    } else {
      console.error('获取地址失败:', result);
    }
  }).catch(error => {
    console.error("获取地址信息失败:", error);
  });
}

// 暴露需要的数据
defineExpose({ mapData });
</script>

<style scoped lang="scss">
.tencent_map {
  .map-container {
    position: relative;

    /*  .location-btn {
            position: absolute;
            right: 10px;
            bottom: 10px;
            width: 32px;
            height: 32px;
            background-color: white;
            border-radius: 2px;
            box-shadow: 0 2px 6px rgba(0,0,0,.2);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1;
            
            &:hover {
                background-color: #f5f5f5;
            }
        } */
  }
}

.autoAddressClass {
  li {
    .title {
      line-height: 30px;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .address {
      line-height: 1;
      font-size: 12px;
      color: #b4b4b4;
      margin-bottom: 5px;
    }
    .autoAddressClass_item {
      overflow: hidden;
      display: flex;
      align-items: center;
      .el-icon {
        margin-right: 20px;
      }
    }
  }
}
</style>
