import request from "@/utils/request";


// 记录用户操作功能 /system/schoolUserUseFunction/add
export function addSchoolUserUseFunctionBatch(data) {
  return request({
    url: "/system/schoolUserUseFunction/add/list",
    method: "post",
    data,
  })
}

// 工作台统计
export function workStatistics(params) {
  return request({
    url: "/system/schoolApprovalConfig/statistics",
    method: "get",
    params,
  });
}

// 电话录音列表
export function recordingPage(params) {
  return request({
    url: "/system/deviceTrouble/recording/list",
    method: "get",
    params,
  });
}

// 评价
export function addEvaluation(data) {
  return request({
    url: "/system/schoolEvaluation/add",
    method: "post",
    data,
  });
}

// 服务台统计
export function serviceStatistics(params) {
  return request({
    url: "/system/deviceTrouble/service/desk/statistics",
    method: "get",
    params,
  });
}

// 服务台提交的工单
export function repairPage(data) {
  return request({
    url: "/system/deviceTrouble/repair/list",
    method: "post",
    data,
  });
}
