<template>
  <el-dialog
    class="custom-dialog"
    :title="props.title"
    v-model="visible"
    width="400px"
    append-to-body
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form ref="fixRef" :model="fixForm" :rules="rules" label-width="100px">
      <el-form-item label="关机时间" prop="time">
        <el-date-picker
          v-model="fixForm.time"
          type="datetime"
          value-format="YYYY-MM-DDTHH:mm:ssZ"
          placeholder="请选择关机时间"
          :disabled-date="disabledDateFn"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="checkFixTime" v-throttle
          >确 定</el-button
        >
        <el-button @click="handleCancel">取 消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { handleBatchRemote, handleRemote } from "@/utils/control";
const emits = defineEmits(["cancel"]);
const { proxy } = getCurrentInstance();
const props = defineProps({
  tableAllSelectedId: {
    type: Array,
    default: [],
  },
  tableAllSelectedRow: {
    type: Array,
    default: [],
  },
  open: {
    type: Boolean,
    default: false,
  },
  title: {
    type: String,
    default: "定时关机",
  },
  isBatch: {
    type: Boolean,
    default: false,
  },
  form: {
    type: Object,
    default: () => ({
      isMqtt: 1,
      ipAddress: "",
      deviceCode: "",
      time: "",
    }),
  },
});

const state = reactive({
  fixRef: null,
  fixForm: {
    deviceCode: "",
    time: "",
  },
  visible: false,
  rules: {
    time: [
      {
        required: true,
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
  },
});

const { fixRef, visible, fixForm, rules } = toRefs(state);

watch(
  () => props.open,
  (val) => {
    if (val) {
      visible.value = val;
      nextTick(() => reset());
      Object.assign(fixForm.value, props.form);
    }
  }
);

const checkFixTime = () => {
  fixRef.value.validate((valid) => {
    if (valid) {
      const time = fixForm.value.time.replace("S", " ").replace("Z", "");
      if (new Date(time).getTime() < new Date().getTime()) {
        proxy.$modal.msgWarning("选择的时间不能小于当前时间");
        return;
      }
      props.isBatch
        ? handleBatchRemote(1, 3, {}, { ...props, time: fixForm.value.time })
        : handleRemote(fixForm.value, 3, "time");

      handleCancel();
    }
  });
};

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() + 86400000 < new Date().getTime()) {
    return true;
  }
  return false;
};

function validateTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择定时关机时间"));
  } else if (new Date(value).getTime() < new Date().getTime()) {
    callback(new Error("定时关机时间不能小于当前时间"));
  } else {
    callback();
  }
}

/** 表单重置 */
function reset() {
  fixForm.value = {
    ipAddress: "",
    time: null,
  };
  proxy.resetForm("fixRef");
}

const handleCancel = () => {
  reset();
  visible.value = false;
  emits("cancel");
};
</script>

<style lang="scss" scoped>
</style>