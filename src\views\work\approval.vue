<template>
  <div class="header">
      <div class="radioTab">
          <el-radio-group v-model="status" size="large" @change="statusChange">
              <el-radio-button label="pending" value="0">待审批</el-radio-button>
              <el-radio-button label="approved" value="1">已审批</el-radio-button>
          </el-radio-group>
      </div>
  </div>
  <div class="table">
      <el-table :data="tableData" empty-text="暂无审批数据" border style="width: 100%;" v-loading="loading">
          <el-table-column prop="approvalCode" label="审批单编号" align="center"></el-table-column>
          <el-table-column prop="module" label="功能模块" align="center"></el-table-column>
          <!-- <el-table-column prop="relatedDoc" label="相关单据" align="center">
          <template slot-scope="scope">
              <el-link type="primary" :underline="false" :href="scope.row.relatedDoc">{{ scope.row.relatedDoc }}</el-link>
          </template>
</el-table-column> -->
          <el-table-column label="相关单据" align="center">
              <template #default="{ row }">
                  <el-link type="primary" @click="handleDetail(row)">{{ row.eventCode }}</el-link>
              </template>
          </el-table-column>

          <el-table-column prop="reason" label="审批原因" align="center"></el-table-column>
          <el-table-column label="操作" align="center" fixed="right">

              <template #default="scope">
                  <el-button type="primary" size="mini" @click="handleDetail(scope.row)">查看</el-button>
                  <el-button type="success" size="mini" v-if="scope.row.status == 0" v-throttle
                      @click="handleApproval(1, scope.row)">审批通过</el-button>
                  <el-button type="danger" size="mini" @click="handleApproval(2, scope.row)" v-throttle
                      v-if="scope.row.status == 0">审批拒绝</el-button>
              </template>
          </el-table-column>
      </el-table>
      <pagination v-show="total > 0" :total="total" v-model:page="params.pageNum" v-model:limit="params.pageSize"
          @pagination="getData" />
  </div>
</template>

<script setup name="Audit">
import { onActivated, ref } from 'vue';
import { approvalPeopleList, updateApprovalPeople } from '@/api/approval'
import { useRouter } from 'vue-router'
import { repairInfo } from '@/api/mediaTeach/trouble'

const router = useRouter()
const { proxy } = getCurrentInstance()
const status = ref('0');
const tableData = ref([
  // 示例数据
  { id: '001', feature: '功能1', relatedDoc: '相关单据1', reason: '原因1' },
  { id: '002', feature: '功能2', relatedDoc: '相关单据2', reason: '原因2' },
]);
const params = ref({
  pageSize: 10,
  pageNum: 1,
  status: '0'
})
const total = ref(0)
const loading = ref(false)
const statusChange = (e) => {
  if (e == 0) {
      params.value.status = '0'
  } else {
      params.value.status = '1,2'
  }
  handleQuery()
}

const handleQuery = () => {
  params.value.pageNum = 1
  getData()
}

const handleDetail = async (row) => {
  console.log(row);
  const { eventId } = row
  let path = '';
  if (row.status == 0) {
    if( row.module == "工单管理"){
        try {
            const res = await repairInfo({
                troubleId: row.eventId,
                type: 0
            });

            if (res.code === 200 && res.data) {
                const { status } = res.data.repairVO;
                switch (status) {
                    case 0:
                        path = `/taskManage/taskCenter/claimInfo?id=${eventId}`;
                        break;
                    case 1:
                    case 4:
                        path = `/taskManage/taskCenter/todoTaskInfo?id=${eventId}&type=0`;
                        break;
                    case 2:
                    case 3:
                        path = `/taskManage/taskCenter/handleTaskInfo?id=${eventId}`;
                        break;
                    case 5:
                        path = `/taskManage/taskCenter/approvalInfo?id=${eventId}`;
                        break;
                    default:
                        proxy.$modal.msgError('未知的工单状态');
                        return;
                }
            } else {
                proxy.$modal.msgError(response.msg || '获取工单信息失败');
                return;
            }
        }   catch (error) {
            console.error('获取工单信息失败:', error);
            proxy.$modal.msgError('获取工单信息失败');
            return;
      }
        
    } else {
        path = `/deviceLedger/deviceInfo?id=${eventId}&type=0&from=2`;
    }
     /*  // proxy.$modal.msgError('审批中，暂不可查看')
      router.push({
          path: '/taskManage/taskCenter/todoTaskInfo',
          query: {
              id: row.eventId,
              type: 0
          }
      })
      return */
  } else {
      /* router.push({
          path: '/taskManage/taskCenter/handleTaskInfo',
          query: {
              id: row.eventId
          }
      }) */
     if( row.module == "工单管理"){
        try {
            const res = await repairInfo({
                troubleId: row.eventId,
                type: 0
            });

            if (res.code === 200 && res.data) {
                const { status } = res.data.repairVO;
                switch (status) {
                    case 0:
                        path = `/taskManage/taskCenter/claimInfo?id=${eventId}`;
                        break;
                    case 1:
                    case 4:
                        path = `/taskManage/taskCenter/todoTaskInfo?id=${eventId}&type=0`;
                        break;
                    case 2:
                    case 3:
                        path = `/taskManage/taskCenter/handleTaskInfo?id=${eventId}`;
                        break;
                    case 5:
                        path = `/taskManage/taskCenter/approvalInfo?id=${eventId}`;
                        break;
                    default:
                        proxy.$modal.msgError('未知的工单状态');
                        return;
                }
            } else {
                proxy.$modal.msgError(response.msg || '获取工单信息失败');
                return;
            }
        }   catch (error) {
            console.error('获取工单信息失败:', error);
            proxy.$modal.msgError('获取工单信息失败');
            return;
      }
        
    } else {
        path = `/deviceLedger/deviceInfo?id=${eventId}&type=0&from=2`;
    }
  }
  proxy.$router.push(path);
}



// 操作审批
const handleApproval = (status, row) => {
  console.log(status, row);
  let msg = status == 1 ? '审批通过' : '审批拒绝'
  proxy.$modal.confirm(`是否确认${msg}`).then(res => {
      console.log('确认');
      updateApprovalPeople({
          status,
          id: row.id
      }).then(res => {
          console.log(res);
          proxy.$modal.msgSuccess('操作成功')
          getData()
      }).catch(() => {
      })

  }).catch(() => {
      console.log('取消');

  })

}

function getData() {
  console.log(params.value);

  loading.value = true
  approvalPeopleList(params.value).then(res => {
      loading.value = false
      console.log(res, '审批');
      total.value = res.data.total
      tableData.value = res.data.records
  }).catch(() => {
      loading.value = false

  })
}

getData()

onActivated(() => {
  getData()

})
</script>

<style scoped>
.header {
  padding: 2vh;
  display: flex;
  flex-direction: column;
  margin: 2vh;
}

.radioTab {
  margin-bottom: 40rpx;
}

.table {
  width: 100%;
  padding: 2vh;

}
</style>
