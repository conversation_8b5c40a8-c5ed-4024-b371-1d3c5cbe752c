import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";
import { debounce } from "@/utils/debounce";
import { getToken } from "@/utils/auth";
import axios from "axios";
import { ElMessage, ElLoading } from "element-plus";

const BASEURL = import.meta.env.VITE_APP_BASE_API;

// 上传之前
export function beforeUpload(file, white, uploadRef) {
  // console.log(white);
  console.log(file);
  
  let whiteName = white;
  let msg = white.join('/')
  let filename = file.name;
  let index = filename.indexOf(".");
  let hzName = filename.substring(index + 1);
  if (whiteName.indexOf(hzName) != -1 && file.size < 5 * 1024 * 1024) {
    return;
  } else {
    ElMessage({
      message: `仅支持${msg}格式并且小于5mb`,
      type: "warning",
    });
    uploadRef.value.abort();
    uploadRef.value.clearFiles();
  }
  // return file
}



// 上传头像成功
// export function successUpload(res) {
//   logoForm.value.imgUrl = decodeURIComponent(sm2Decrypt(res.data));
//   uploadRef.value.clearFiles();
//   ElMessage({
//     message: "上传成功",
//     type: "success",
//   });
// }


export const downloadFile = (
  url,
  params = {},
  fileName,
  fileType,
  finallyType
) => {
  // proxy.$modal.loading();

  axios({
    method: "GET",
    url,
    responseType: "blob",
    headers: {
      Authorization: "Bearer " + getToken(),
    },
    params,
    // data: sm2Encrypt(JSON.stringify(downloadData.value)),
  }).then((res) => {
    console.log(res.data, "wenjian");
    let fileTypeList = [
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-excel",
    ];
    // const contentType = res.headers['content-type'];
    // console.log('文件类型:', contentType);
    if (fileType != "") {
      console.log(fileType, "查看");

      const blob = new Blob([res.data], {
        type: fileType,
      });

      const objUrl = window.URL.createObjectURL(blob);
      if (fileTypeList.indexOf(fileType) != -1) {
        window.open(
          `${window.location.protocol}//${window.location.hostname}:${window.location.port}/#/file?src=${objUrl}&finallyType=${finallyType}`,
          "_blank"
        );
        return;
      }

      window.open(objUrl, "_blank");

      return;
    } else {
      const blob = res.data;
      const objUrl = window.URL.createObjectURL(blob);

      const a = document.createElement("a");
      a.setAttribute("href", objUrl);
      a.setAttribute("download", `${fileName}`);
      a.click();
      window.URL.revokeObjectURL(objUrl);
    }
  });
};