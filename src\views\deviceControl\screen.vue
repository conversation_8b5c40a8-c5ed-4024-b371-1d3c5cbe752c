<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>

      <div
        class="monitor_flex"
        :class="{
          hasControlBtn: tableAllSelectedId.length > 0 && currentView === '1',
        }"
      >
        <locateSel
          class="monitor-position flex_1"
          ref="locateRef"
          @getPosition="getPosition"
          :maxHeight="posHeight"
          @handleQuery="handleQuery"
        />
        <div class="flex_2" ref="flexRightBox" v-loading="loading">
          <div class="view-switch">
            <el-radio-group v-model="currentView" @change="viewChange">
              <el-radio-button value="1" label="realtime"
                >设备实时桌面</el-radio-button
              >
              <el-radio-button value="2" label="gallery"
                >截图图库</el-radio-button
              >
            </el-radio-group>
          </div>
          <div v-if="currentView == '1'" class="recording-main">
            <div class="online-filter">
              <el-checkbox v-model="queryParams.isOff" @change="offChange">
                <div class="online-filter-content">只看在线设备</div>
              </el-checkbox>
              <el-checkbox
                class="checkAll"
                v-model="checkAll"
                :indeterminate="isIndeterminate"
                @change="handleCheckAllChange"
              >
                <div class="online-filter-content">全选</div>
              </el-checkbox>
            </div>
            <div class="recording-main_grid">
              <p class="normal" v-if="total == 0">暂无数据</p>
              <el-row :gutter="10" style="gap: 5px 0" v-else>
                <el-col
                  :span="8"
                  v-for="(item, index) in tableList"
                  :key="index"
                >
                  <div class="screen-wrapper">
                    <div
                      class="screen-card"
                      @click="handleDesktop2(item)"
                      style="cursor: pointer"
                      v-throttle
                    >
                      <div class="screen-checked" @click.native.stop>
                        <el-checkbox
                          label=""
                          v-model="item.checked"
                          @change="
                            (val) => {
                              handleCheckChange(val, item);
                            }
                          "
                          @click.native.stop
                        />
                      </div>
                      <div v-if="item.runStatus[0] == 0" class="img"></div>
                      <img
                        v-else
                        :src="item.runStatus[0] == 0 ? '' : item.screenshotUrl"
                        :alt="item.runStatus[0] == 0 ? '设备离线' : '暂无图片'"
                      />

                      <div class="screen-title">
                        <!-- <div>{{ item.deviceCode || "-" }}</div> -->
                        <div>{{ item.deviceName || "-" }}</div>
                        <div class="installation" :title="item.installAddress">
                          {{ item.installAddress || "默认位置" }}
                        </div>
                      </div>
                      <div
                        class="screen-status"
                        :class="
                          item.internetStatus == 0
                            ? 'online'
                            : item.internetStatus == 1
                            ? 'badline'
                            : 'offline'
                        "
                      >
                        {{ internetStatusObj[item.internetStatus].label }}
                      </div>
                    </div>
                  </div>
                </el-col>
              </el-row>
            </div>
            <el-pagination
              v-show="total > 0"
              :total="total"
              v-model:current-page="queryParams.current"
              v-model:page-size="queryParams.size"
              @current-change="getList"
              layout="total, prev, pager, next"
              background
              class="custom-pagination"
            />
          </div>
          <div v-else class="gallery-main recording-main">
            <div class="gallery-grid">
              <div class="recording-main_grid">
                <p class="normal" v-if="stotal == 0">暂无数据</p>
                <el-row :gutter="20" v-else>
                  <el-col
                    :span="8"
                    v-for="(item, index) in galleryList"
                    :key="index"
                  >
                    <div class="gallery-item">
                      <div
                        class="gallery-image"
                        v-throttle
                        @click="checkImg(item.imageLocalUrl)"
                      >
                        <img :src="item.imageLocalUrl" alt="暂无图片" />
                        <div class="gallery-title">
                          <div>
                            {{ item.deviceName || "-" }}
                          </div>
                          <div>
                            {{ item.deviceCode || "-" }}
                            <span>截图时间:</span>
                          </div>
                          <div>
                            <span
                              class="installation"
                              :title="item.installation"
                              >{{ item.installation || "默认位置" }}</span
                            >
                            <span>{{ item.createdTime }}</span>
                          </div>
                        </div>
                      </div>
                      <div
                        v-if="item.imageLocalUrl"
                        class="download-icon"
                        v-throttle
                        @click="downloadHttp(item.imageLocalUrl)"
                      >
                        <el-icon><Download /></el-icon>
                      </div>
                      <div
                        v-else-if="item.imageFile"
                        class="download-icon"
                        v-throttle
                        @click="
                          downloadBlob2(
                            getBlob(item.imageFile),
                            `${item.deviceCode}-截屏图片`
                          )
                        "
                      >
                        <el-icon><Download /></el-icon>
                      </div>
                    </div>
                  </el-col>
                </el-row>
              </div>

              <el-pagination
                v-show="stotal > 0"
                :total="stotal"
                v-model:current-page="shotParams.pageNum"
                v-model:page-size="shotParams.pageSize"
                @current-change="getImgList"
                layout="total, prev, pager, next"
                background
                class="custom-pagination"
              />
            </div>
          </div>
        </div>
      </div>
      <control-btns
        v-if="tableAllSelectedId.length > 0 && currentView === '1'"
        :tableAllSelectedId="tableAllSelectedId"
        :tableAllSelectedRow="tableAllSelectedRow"
      />
      <!-- <div
        class="monitor-bottom"
        v-show="currentView == '1' && tableAllSelectedId.length > 0"
      >
        <div class="monitor-btns">
          <el-button
            v-for="item in remoteList"
            :key="item.value"
            :type="item.type"
            :icon="item.icon"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleOpeartionRemote(item.value)"
            >{{ item.label }}</el-button
          >
        </div>
      </div> -->

      <!-- 实时桌面 -->
      <el-dialog
        v-model="dialogVisible"
        :title="`设备名称: ${currentDevice.deviceName || 'XXXXXXXX'}`"
        append-to-body
        @close="handleCloseWs2"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="90%"
        align-center
      >
        <div class="dialog-content">
          <div class="operation-area">
            <div class="scissors-icon" @click="handleScreenshot" v-throttle>
              <el-icon><Scissor /></el-icon> 截图
            </div>
          </div>
          <div
            class="screenshot-area"
            v-loading="desktopLoading"
            ref="screenDom"
          >
            <video
              id="videoPlayer"
              style="height: 75vh; outline: none"
              autoplay
              controlsList="nodownload nofullscreen noplaybackrate nopictureinpicture"
              disablePictureInPicture
              disableRemotePlayback
              tabindex="0"
            ></video>
          </div>
        </div>
      </el-dialog>

      <!-- 定时关机 -->
      <el-dialog
        class="custom-dialog"
        :title="title"
        v-model="open"
        width="400px"
        append-to-body
        :close-on-click-modal="false"
        @close="cancel"
      >
        <el-form
          ref="fixRef"
          :model="fixForm"
          :rules="rules"
          label-width="100px"
        >
          <el-form-item label="关机时间" prop="time">
            <el-date-picker
              v-model="fixForm.time"
              type="datetime"
              value-format="YYYY-MM-DDTHH:mm:ssZ"
              placeholder="请选择关机时间"
              :disabled-date="disabledDateFn"
            />
          </el-form-item>
        </el-form>
        <template #footer>
          <div class="dialog-footer">
            <el-button type="primary" @click="checkFixTime" v-throttle
              >确 定</el-button
            >
            <el-button @click="cancel">取 消</el-button>
          </div>
        </template>
      </el-dialog>

      <el-dialog
        v-model="screenshotDialogVisible"
        :show-close="false"
        :modal="true"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        width="400px"
        class="screenshot-dialog"
      >
        <div class="screenshot-placeholder">
          <div class="screenshot-success-message">
            <!-- <el-icon class="success-icon" color="#67C23A"><CircleCheckFilled /></el-icon> -->
            <span>已截图，保存至截图图库中</span>
          </div>
        </div>
      </el-dialog>

      <!-- 查看截图 -->
      <el-dialog
        v-model="imgDialogVisible"
        :show-close="false"
        :modal="true"
        width="80%"
        align-center
        class="screenshot-dialog"
      >
        <img :src="imgUrl" alt="" class="dialog_img" />
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="recording">
import controlBtns from "../deviceControl/components/controlBtns.vue";
import locateSel from "../deviceManage/components/locateSel.vue";
import { useRoute, useRouter } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onBeforeUnmount,
  onActivated,
  onMounted,
} from "vue";
import { createWebSocket, closeSock, sendSock } from "@/utils/socket";
import {
  treeToArray,
  getBlob,
  downloadBlob2,
  downloadHttp,
  sendPointRequest,
  sendPointRequestBatch,
} from "@/utils";
import keyToVKMap from "@/utils/keyToVKMap";
import { getPositionTree } from "@/api/mediaTeach/position";
import { devicePage } from "@/api/mediaTeach/ledger";
import {
  getSchoolScreenshotList,
  addSchoolScreenshot,
  addByMqtt,
  deviceCtl,
  deviceStartByWOL,
  deviceCtlMqtt,
  addSchoolDeviceLog,
} from "@/api/deviceControl";
import useUserStore from "@/store/modules/user";
import elementResizeDetectorMaker from "element-resize-detector";
import { nextTick } from "process";
import { ElMessage, genFileId } from "element-plus";

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
});
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const positionList = ref([]);
const positionTreeList = ref([]);
const videoElement = ref(null);
const erd = elementResizeDetectorMaker();
const state = reactive({
  controlBtnRef: null,
  delHeight: 260,
  loading: false,
  positionNodeList: [],
  internetStatusObj: {
    0: {
      label: "设备在线",
    },
    1: {
      label: "网络较差",
    },
    2: {
      label: "设备离线",
    },
  },
  fixRef: null,
  isRalay: false,
  checkAll: false,
  isIndeterminate: false,
  open: false,
  title: "",
  tableAllSelectedRow: [],
  tableAllSelectedId: [],
  refreshTimer: null,
  addTimer: null,
  addSecond: 0,
  rect: null,
  screenDom: null,
  demo: null,
  hasInitVideo: false,
  jmuxer: null,
  isProd: import.meta.env.VITE_APP_BASE_API == "/prod-api",
  desktopLoading: false,
  dialogVisible: false,
  total: 1,
  ips: [],
  tableData_all: [],
  tableList: [],
  typeList: [],
  statusList: [
    { label: "待处理", value: 0, type: "danger" },
    { label: "已处理", value: 1, type: "success" },
  ],
  fixForm: {
    deviceCode: "",
    time: "",
  },
  rules: {
    time: [
      {
        required: true,
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
  },
  queryParams: {
    current: 1,
    size: 9,
    typeId: "1",
    positionIds: [],
    smartScreen: 1,
    isDesktop: 1,
  },
  imgUrl: "",
  imgDialogVisible: false,
  currentView: "1",
  galleryList: [],
  onlyOnlineDevices: false,
  stotal: 0,
  shotParams: {
    pageNum: 1,
    pageSize: 9,
    typeId: "1",
    positionIds: [],
  },
  currentDevice: {
    isMqtt: 0,
    deviceName: "",
    screenshot: "",
  },
  screenshotDialogVisible: false,
  pic: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAnCAYAAACrDdDdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAABBSURBVGhD7c8BDQAwEMSg+Td908GnOODtsHKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKm7QNz9WRsCGQFWQAAAABJRU5ErkJggg==",
  remoteList: [
    { icon: "SwitchButton", type: "danger", label: "远程关机", value: 0 },
    { icon: "Monitor", type: "success", label: "远程开机", value: 1 },
    { icon: "RefreshRight", type: "primary", label: "远程重启", value: 2 },
    { icon: "AlarmClock", type: "warning", label: "远程定时关机", value: 3 },
    { icon: "SortUp", type: "success", label: "远程开启wifi", value: 4 },
    { icon: "SortDown", type: "info", label: "远程关闭wifi", value: 5 },
    { icon: "Brush", type: "danger", label: "远程清除缓存", value: 6 },
    { icon: "Lock", type: "warning", label: "锁屏", value: 7 },
    { icon: "Unlock", type: "primary", label: "解锁", value: 8 },
  ],
});

const {
  controlBtnRef,
  delHeight,
  positionNodeList,
  internetStatusObj,
  fixRef,
  isRalay,
  ips,
  tableData_all,
  checkAll,
  isIndeterminate,
  fixForm,
  rules,
  title,
  open,
  tableAllSelectedId,
  tableAllSelectedRow,
  remoteList,
  refreshTimer,
  addTimer,
  addSecond,
  rect,
  screenDom,
  demo,
  hasInitVideo,
  jmuxer,
  isProd,
  pic,
  desktopLoading,
  tableList,
  typeList,
  queryParams,
  dialogVisible,
  statusList,
  loading,
  total,
  stotal,
  shotParams,
  currentView,
  galleryList,
  onlyOnlineDevices,
  currentDevice,
  imgDialogVisible,
  screenshotDialogVisible,
  imgUrl,
} = toRefs(state);

onActivated(() => {
  controlBtnRef.value.getControl();
});

const handleCheckAllChange = (val) => {
  console.log(val, "全选");
  isIndeterminate.value = false;
  tableList.value = tableList.value.map((item) => {
    item.checked = val;
    return item;
  });
  tableAllSelectedId.value = val
    ? state.tableData_all.map((item) => item.deviceId)
    : [];
  tableAllSelectedRow.value = val ? state.tableData_all : [];
  console.log(
    tableAllSelectedId.value,
    tableAllSelectedRow.value,
    tableList.value,
    "全选"
  );
};

const handleCheckChange = (val, row) => {
  // tableData_all.value = tableData_all.value.map((item) => {
  //   if (item.deviceId == row.deviceId) {
  //     item.checked = val;
  //   }
  //   return item;
  // });
  let idx = tableAllSelectedId.value.findIndex((item) => item == row.deviceId);
  idx == -1
    ? tableAllSelectedId.value.push(row.deviceId)
    : tableAllSelectedId.value.splice(idx, 1);
  idx == -1
    ? tableAllSelectedRow.value.push(row)
    : tableAllSelectedRow.value.splice(idx, 1);
  console.log(val, tableList.value, tableData_all.value, "单独选");

  let total = tableAllSelectedId.value.length;
  checkAll.value = total == tableData_all.value.length;
  isIndeterminate.value = total > 0 && total < tableData_all.value.length;
  // tableAllSelectedId.value = tableData_all.value
  //   .filter((item) => item.checked)
  //   .map((item) => item.deviceId);
};

function handleOpeartionRemote(val) {
  console.log("远程操作", val);
  switch (val) {
    // 批量远程关机
    case 0:
      handleBatchRemote(1, 0);
      break;
    //批量远程开机
    case 1:
      handleBatchRemote(2, 0);
      break;
    // 批量远程重启
    case 2:
      handleBatchRemote(1, 1);
      break;
    // 批量远程定时关机
    case 3:
      handleBatchFixed();
      break;
    // 批量远程开启WIFI
    case 4:
      handleBatchWifi(1, 1);
      break;
    // 批量远程关闭WIFI
    case 5:
      handleBatchWifi(1, 0);
      break;
    // 批量远程清除缓存
    case 6:
      handleBatchClear(1);
      break;
    // 批量远程锁屏
    case 7:
      handleBatchLock(1, 0);
      break;
    // 批量远程解锁
    case 8:
      handleBatchLock(1, 1);
      break;
    default:
      break;
  }
}

function validateTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择定时关机时间"));
  } else if (new Date(value).getTime() < new Date().getTime()) {
    callback(new Error("定时关机时间不能小于当前时间"));
  } else {
    callback();
  }
}

/** 批量操作前的检测 */
const checkBatch = () => {
  let newArr = [];
  state.tableAllSelectedId.map((item) => {
    state.tableData_all.map((item2) => {
      if (item2.deviceId == item) {
        newArr.push(item2);
      }
    });
  });
  let arr = [],
    flag = false;
  ips.value = [];
  let narr = [],
    recoverArr = [];
  newArr.map((item) => {
    if (item.runStatus.indexOf(0) != -1) {
      // 关机
      narr.push(item.deviceName);
      flag = true;
    } else if (item.winStatus > 1) {
      recoverArr.push(item.deviceName);
    } else {
      arr.push(item.deviceName);
      ips.value.push(item.ipAddress);
    }
  });
  return { arr, narr, flag, recoverArr };
};

/** 取消按钮 */
function cancel() {
  open.value = false;
  fixForm.value = {
    ipAddress: "",
    time: null,
  };
  proxy.resetForm("fixRef");
}

/** 批量定时关机按钮操作 */
const handleBatchFixed = () => {
  const { flag, recoverArr } = checkBatch();
  if (flag || recoverArr.length > 0) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  // isBatch.value = true;
  title.value = "批量定时关机";
  open.value = true;
};

// 限制日期
const disabledDateFn = (date) => {
  //   const today = formatDate(new Date().getTime())
  if (date.getTime() + 86400000 < new Date().getTime()) {
    // console.log('date', date, date.getTime(), (date.getTime() + 86400000), new Date().getTime())
    return true;
  }
  return false;
};

const checkFixTime = () => {
  fixRef.value.validate((valid) => {
    if (valid) {
      const time = fixForm.value.time.replace("S", " ").replace("Z", "");
      if (new Date(time).getTime() < new Date().getTime()) {
        proxy.$modal.msgWarning("选择的时间不能小于当前时间");
        return;
      }
      handleBatchRemote(1, 3);
    }
  });
};

/** 批量重启/关闭/定时关闭按钮操作 */
const handleBatchRemote = async (isBatch, type, row) => {
  const { arr, narr, flag, recoverArr } = checkBatch();

  if (isBatch == 2) {
    if (arr.length > 0 || recoverArr.length > 0) {
      proxy.$modal.msgWarning(
        "设备存在开机状态，无法进行此操作"
      );
      return;
    }
    proxy.$modal
      .confirm(`是否确认批量开机设备名称为【${narr.join("、")}】的设备？`)
      .then(() => {
        proxy.$modal
          .confirm("请确认设备是否支持远程开机")
          .then(async () => {
            let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
            let sendData = {
              userEvents: rows.map((item) => {
                return {
                  event: "Click",
                  eventDescribe: "选中设备后点击下方悬浮按钮集控",
                  content: 0,
                  num: 1,
                  deviceCode: item.deviceCode,
                };
              }),
            };
            console.log(sendData, "批量埋点");
            sendPointRequestBatch(sendData);
            addSchoolDeviceLog({
              logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
              deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
              logContent: `远程开机`, // 日志内容
              deviceNum: rows.length, // 操作设备数量
            });
            let success = [],
              fail = [];
            for (let j = 0; j < rows.length; j++) {
              try {
                proxy.$modal.loading();
                await deviceStartByWOL({ deviceCode: rows[j].deviceCode })
                  .then((res) => {
                    console.log(res);
                    success.push(rows[j].deviceCode);
                  })
                  .catch(() => {
                    fail.push(rows[j].deviceCode);
                  })
                  .finally(() => {
                    proxy.$modal.closeLoading();
                  });
              } catch (error) {}
            }
            proxy.$modal.msgSuccess(
              fail.length == 0
                ? "操作成功"
                : `开机指令共发送${rows.length}条，成功${
                    success.length
                  }条，失败${fail.length}条，指令发送失败设备：${fail.join(
                    "、"
                  )}`
            );
          })
          .catch((err) => {});
      })
      .catch((err) => {});
    return;
  }

  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }

  if (type !== 3) {
    proxy.$modal
      .confirm(
        `是否确认批量${!!type ? "重启" : "关闭"}设备名称为【${arr.join(
          "、"
        )}】的设备？`
      )
      .then(async function () {
        let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
        let sendData = {
          userEvents: rows.map((item) => {
            return {
              event: "Click",
              eventDescribe: "选中设备后点击下方悬浮按钮集控",
              content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
              num: 1,
              deviceCode: item.deviceCode,
            };
          }),
        };
        console.log(sendData, "批量埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
          logContent: `远程${
            type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
          }`, // 日志内容
          deviceNum: rows.length, // 操作设备数量
        });
        for (let j = 0; j < rows.length; j++) {
          try {
            handleRemote(
              {
                ipAddress: rows[j].ipAddress,
                time: type === 3 ? fixForm.value.time : null,
                deviceCode: rows[j].deviceCode,
                isMqtt: rows[j].isMqtt,
              },
              type
            );
          } catch (error) {}
        }
      })
      .catch(() => {});
  } else {
    // 批量定时关机
    let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
    let sendData = {
      userEvents: rows.map((item) => {
        return {
          event: "Click",
          eventDescribe: "选中设备后点击下方悬浮按钮集控",
          content: 3,
          num: 1,
          deviceCode: item.deviceCode,
        };
      }),
    };
    console.log(sendData, "批量埋点");
    sendPointRequestBatch(sendData);
    addSchoolDeviceLog({
      logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
      deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
      logContent: `远程定时关机`, // 日志内容
      deviceNum: rows.length, // 操作设备数量
    });
    for (let j = 0; j < rows.length; j++) {
      try {
        handleRemote(
          {
            ipAddress: rows[j].ipAddress,
            time: type === 3 ? fixForm.value.time : null,
            deviceCode: rows[j].deviceCode,
            isMqtt: rows[j].isMqtt,
          },
          type
        );
      } catch (error) {}
    }
  }
  cancel();
};

/** 批量解锁/锁屏 type: 0锁屏 1解锁*/
const handleBatchLock = (isBatch, type, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  proxy.$modal
    .confirm(
      `是否确认批量对设备名称为【${arr.join("、")}】的设备进行${
        type == 0 ? "锁屏" : "解锁"
      }？`
    )
    .then(function () {
      let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
      let sendData = {
        userEvents: rows.map((item) => {
          return {
            event: "Click",
            eventDescribe: "选中设备后点击下方悬浮按钮集控",
            content: `${type == 0 ? 7 : 8}`,
            num: 1,
            deviceCode: item.deviceCode,
          };
        }),
      };
      console.log(sendData, "批量埋点");
      sendPointRequestBatch(sendData);
      for (let j = 0; j < rows.length; j++) {
        if (type == 1) {
          addSchoolDeviceLog({
            logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
            deviceCode: rows[j].deviceCode, // 操作设备编号
            logContent: `设备后台解锁`, // 日志内容
            deviceNum: 1, // 操作设备数量
          });
        }
        try {
          handleLock(
            {
              ipAddress: rows[j].ipAddress,
              deviceCode: rows[j].deviceCode,
              isMqtt: rows[j].isMqtt,
            },
            type
          );
        } catch (error) {}
      }
    })
    .catch(() => {});
};

/** 批量清除缓存 */
const handleBatchClear = (isBatch, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  proxy.$modal
    .confirm("是否确认批量清除设备名称为【" + arr.join("、") + "】的设备缓存？")
    .then(async function () {
      let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
      let sendData = {
        userEvents: rows.map((item) => {
          return {
            event: "Click",
            eventDescribe: "选中设备后点击下方悬浮按钮集控",
            content: 6,
            num: 1,
            deviceCode: item.deviceCode,
          };
        }),
      };
      console.log(sendData, "批量埋点");
      sendPointRequestBatch(sendData);
      addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
        logContent: `远程清除缓存`, // 日志内容
        deviceNum: rows.length, // 操作设备数量
      });
      for (let j = 0; j < rows.length; j++) {
        try {
          handleClear({
            ipAddress: rows[j].ipAddress,
            deviceCode: rows[j].deviceCode,
            isMqtt: rows[j].isMqtt,
          });
        } catch (error) {}
      }
    })
    .catch(() => {});
};

/** 批量开启/关闭 Wifi */
const handleBatchWifi = (isBatch, type, row) => {
  if (isBatch == 0 && row.winStatus > 1) {
    proxy.$modal.msgWarning("设备存在冰冻/解冻状态，无法进行此操作");
    return;
  }
  const { arr, flag, recoverArr } = checkBatch();
  if ((flag || recoverArr.length > 0) && isBatch == 1) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  proxy.$modal
    .confirm(
      `是否确认批量${!!type ? "开启" : "关闭"}设备名称为【${arr.join(
        "、"
      )}】的设备wifi？`
    )
    .then(async function () {
      let rows = JSON.parse(JSON.stringify(state.tableAllSelectedRow));
      let sendData = {
        userEvents: rows.map((item) => {
          return {
            event: "Click",
            eventDescribe: "选中设备后点击下方悬浮按钮集控",
            content: `${!!type ? 4 : 5}`,
            num: 1,
            deviceCode: item.deviceCode,
          };
        }),
      };
      console.log(sendData, "批量埋点");
      sendPointRequestBatch(sendData);
      addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
        logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
        deviceNum: rows.length, // 操作设备数量
      });
      for (let j = 0; j < rows.length; j++) {
        try {
          handleWifi(
            {
              ipAddress: rows[j].ipAddress,
              deviceCode: rows[j].deviceCode,
              isMqtt: rows[j].isMqtt,
            },
            type
          );
        } catch (error) {}
      }
    })
    .catch(() => {});
};

/** 解锁/锁屏 type: 0锁屏 1解锁*/
const handleLock = async (row, type) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleClear = async (row) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri: (isRalay.value ? ip2 : ip1) + `/api/Cockpit/ClearMemory`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/ClearMemory`,
      deviceCodeList: row.deviceCode,
    };
    console.log("传参", obj);
    res = await deviceCtlMqtt(obj);
  } else {
    console.log("传参", obj);
    res = await deviceCtl(obj);
  }
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleWifi = async (row, type) => {
  proxy.$modal.loading();
  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let obj = {
    method: "get",
    uri:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
    content: "",
  };
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();
};

const handleRemote = async (row, type, isTime) => {
  if (!!isTime) {
    // 操作单个设备的定时关机
    console.log(row, "row");
    try {
      let sendData = {
        userEvents: [
          {
            event: "Click",
            eventDescribe: "点击单个设备操作集控按钮",
            content: 3,
            num: 1,
            deviceCode: row.deviceCode,
          },
        ],
      };
      console.log(sendData, "单个埋点");
      sendPointRequestBatch(sendData);
      let res = await addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: row.deviceCode, // 操作设备编号
        logContent: `远程定时关机`, // 日志内容
        deviceNum: 1,
      });
      console.log(res, "操作记录");
    } catch (error) {}
  }

  proxy.$modal.loading();

  let { ip1, ip2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let data = { cmd: type === 3 ? 0 : type };
  if (type == 3) {
    data.time = row.time;
  } else {
    delete data.time;
  }
  let obj = {
    method: "post",
    uri: (isRalay.value ? ip2 : ip1) + `/api/Cockpit/Control`,
    content: JSON.stringify(data),
  };
  console.log("关机/重启/定时关机传参", obj);
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/Control`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
  }
  proxy.$modal.closeLoading();

  cancel();
};

function getVKCode(key) {
  return keyToVKMap[key] || 0; // 如果未找到对应的键码，返回 0
}

const global_callback2 = (nalUnit, err = false) => {
  console.log("websocket的回调函数收到服务器信息：", nalUnit);
  desktopLoading.value = false;
  if (err) {
    proxy.$modal.msgError(nalUnit);
    proxy.$modal.closeLoading();
    state.dialogVisible = false;
  } else if (nalUnit == "已断开") {
    proxy.$modal.closeLoading();
    state.dialogVisible = false;
  } else {
    // 喂给JMuxer
    if (state.jmuxer) {
      state.jmuxer.feed({
        video: nalUnit,
        duration: 0,
        // 实时模式设为0
      });
      if (videoElement.value.videoWidth && !state.hasInitVideo) {
        state.hasInitVideo = true;
        nextTick(() => {
          console.log("第二次初始化");
          initVideoPlayer();
        });
      }
    }
  }
};

function handleDesktop2(row) {
  console.log("clicked device:", row, state.jmuxer);
  router.push({
    path: "/deviceControl/desktop",
    query: {
      deviceId: row.deviceId,
      isMqtt: row.isMqtt,
    },
  });
  return;
  state.currentDevice = row || {
    isMqtt: 0,
    deviceName: "",
    deviceCode: "",
    screenshot: "",
  };
  proxy.$modal.loading();
  let { url1, ws } = getUrlIp(row.ipAddress);

  let uri = `wss://maintainapp.gzwinteam.com/wsflow/ServerHub?corpId=${
    isProd.value ? useUserStore().corpId : useUserStore().corpId || "00000123"
  }&${
    row.isMqtt ? `deviceCode=${row.deviceCode}` : `addr=ws://${url1}/ServerHub`
  }`;

  // let uri = `${window.location.protocol == "https:" ? "wss" : "ws"}://${
  //   window.location.host
  // }/wsflow/ServerHub?corpId=${
  //   isProd.value ? useUserStore().corpId : useUserStore().corpId || "00000123"
  // }&${
  //   row.isMqtt ? `deviceCode=${row.deviceCode}` : `addr=ws://${url1}/ServerHub`
  // }`;

  createWebSocket(global_callback2, uri).then((res) => {
    proxy.$modal.closeLoading();
    state.dialogVisible = true;
    desktopLoading.value = true;
    nextTick(() => {
      //初始化JMuxer
      state.jmuxer = new JMuxer({
        node: "videoPlayer",
        mode: "video",
        flushingTime: 0,
        //封装间隔（毫秒）
        fps: 30,
        // 需与实际帧率一致
        debug: true,
      });
      console.log("第一次初始化");
      videoElement.value = document.getElementById("videoPlayer");
      window.addEventListener("resize", cancelDebounce);
      erd.listenTo(screenDom.value, () => {
        cancelDebounce();
      });
    });
  });
}

async function handleCloseWs2() {
  await closeSock();
  hasInitVideo.value = false;
  if (state.jmuxer) state.jmuxer.destroy();
}

function getUrlIp(ip, url = "") {
  let str = "http",
    ws = "ws",
    href = window.location.href.split(":");
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `${href[0]}://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2.replace("s", "").replace("http://", "");
  } else {
    ip2 = `${href[0]}://` + ip2;
  }
  ws = href[0] == "https" ? "ws" : "ws";
  console.log("网站协议", href[0], ws, href[0] == "https");
  return { ip1, ip2, url1, url2, ws };
}

// 查看图片
function checkImg(url) {
  imgUrl.value = url;
  imgDialogVisible.value = true;
}

function offChange(e) {
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  getList();
}

function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.positionIds = d?._checkList;
  }
  queryParams.value.current = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

function getList(flag = true) {
  let obj = {
    ...queryParams.value,
  };
  obj.isOff ? (obj.isOff = 0) : delete obj?.isOff;
  console.log(obj, "设备实时桌面");
  if (flag) loading.value = true;
  devicePage(obj)
    .then((res) => {
      const { page, abnormalTerminal } = res.data;
      console.log(res, "大屏巡检");
      tableList.value = page.records;
      tableList.value.forEach((item) => {
        if (!!item.screenshotFile) {
          item.screenshotFile = "data:image/gif;base64," + item.screenshotFile;
        }
      });
      total.value = page.total;
      nextTick(() => {
        tableList.value.forEach((item) => {
          if (tableAllSelectedId.value.indexOf(item.deviceId) > -1) {
            item.checked = true;
          } else {
            item.checked = false;
          }
        });
      });
    })
    .finally((err) => {
      loading.value = false;
    });

  devicePage({
    ...obj,
    current: 1,
    size: 9999999,
  }).then((res) => {
    console.log("所有台账数据", res);
    tableData_all.value = res.data.page.records;
    let total = tableAllSelectedId.value.length;
    checkAll.value = total == tableData_all.value.length;
    isIndeterminate.value = total > 0 && total < tableData_all.value.length;
    console.log(tableData_all.value);
  });
}

// 获取截图列表
function getImgList() {
  console.log(shotParams.value, "shotParams");

  proxy.$modal.loading();
  getSchoolScreenshotList(shotParams.value)
    .then((res) => {
      proxy.$modal.closeLoading();
      galleryList.value = res.data.rows;
      galleryList.value.forEach((item) => {
        if (!!item.imageFile) {
          item.imageFile = "data:image/gif;base64," + item.imageFile;
        }
      });
      stotal.value = res.data.total;
      console.log(res, "截图");
    })
    .catch((err) => {
      proxy.$modal.closeLoading();
    });
}

function viewChange(e) {
  console.log(e);
  if (e == 1) {
    getList();
  } else {
    getImgList();
  }
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  shotParams.value.positionIds = obj.checkedKeys;
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  getList();
}

function handleScreenshot() {
  console.log(state.currentDevice.deviceCode);
  console.log({ deviceCode: state.currentDevice.deviceCode }, "截图");
  proxy.$modal.loading();
  if (state.currentDevice.isMqtt) {
    addByMqtt({ deviceCode: state.currentDevice.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
      })
      .finally(() => proxy.$modal.closeLoading());
  } else {
    addSchoolScreenshot({ deviceCode: state.currentDevice.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
      })
      .finally(() => proxy.$modal.closeLoading());
  }
}

function getPosition(d) {
  state.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

function getPositionTreeList() {
  getPositionTree({})
    .then((response) => {
      let tree = JSON.parse(JSON.stringify(response.data));
      positionTreeList.value = response.data;
      positionList.value = treeToArray(response.data);
      // data.positionNodeList = addAttr(tree);
      // console.log("positionList ==>", positionList.value);
    })
    .catch((err) => {});
}

function initVideoPlayer() {
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  rect.value = videoElement.value.getBoundingClientRect(); // 获取元素的边界框信息

  videoElement.value.addEventListener("mousemove", (event) => {
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    let x = event.clientX - rect.value.left + screenDom.value.scrollLeft;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标移动:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.videoWidth,
      `滚动条偏移量：${screenDom.value.scrollLeft}`
    );
    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X:
            (((videoWidth / rect.value.width) * x) /
              videoElement.value.videoWidth) *
            65535,
          Y:
            (((videoHeight / rect.value.height) * y) /
              videoElement.value.videoHeight) *
            65535,
        },
        Input: 2,
        Delta: 0,
        Key: 0,
      })
    );
  });
  videoElement.value.addEventListener("mousedown", (event) => {
    videoElement.value.focus(); // 设置焦点到video元素
    const button = event.button;
    let x = event.clientX - rect.value.left + screenDom.value.scrollLeft;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标按下:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.offsetWidth,
      videoElement.value.videoWidth
    );
    let Input = {
      Button: button,
      MousePoint: {
        X:
          (((videoWidth / rect.value.width) * x) /
            videoElement.value.videoWidth) *
          65535,
        Y:
          (((videoHeight / rect.value.height) * y) /
            videoElement.value.videoHeight) *
          65535,
      },
      Input: 1,
      Delta: 0,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标点击信息到服务器
    sendSock(JSON.stringify(Input));
  });
  videoElement.value.addEventListener("mouseup", (event) => {
    videoElement.value.focus(); // 设置焦点到video元素
    const button = event.button;
    let x = event.clientX - rect.value.left + screenDom.value.scrollLeft;
    let y = event.clientY - rect.value.top;
    console.log(
      "鼠标抬起:",
      (videoWidth / rect.value.width) * x,
      (videoHeight / rect.value.height) * y,
      videoElement.value.offsetWidth,
      videoElement.value.videoWidth
    );
    let Input = {
      Button: button,
      MousePoint: {
        X:
          (((videoWidth / rect.value.width) * x) /
            videoElement.value.videoWidth) *
          65535,
        Y:
          (((videoHeight / rect.value.height) * y) /
            videoElement.value.videoHeight) *
          65535,
      },
      Input: 0,
      Delta: 0,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标点击信息到服务器
    sendSock(JSON.stringify(Input));
  });
  videoElement.value.addEventListener("wheel", (event) => {
    console.log("鼠标滑轮:", event.deltaY);
    let wheelInput = {
      Button: 1,
      MousePoint: {
        X: 0,
        Y: 0,
      },
      Input: 3,
      Delta: event.deltaY,
      Key: 0,
    };
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送鼠标滑轮信息到服务器
    sendSock(JSON.stringify(wheelInput));
  });
  videoElement.value.addEventListener("keydown", (event) => {
    const vkCode = getVKCode(event.key);
    console.log("键盘按下:", event.key, "VK Code:", vkCode);
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X: 0,
          Y: 0,
        },
        Input: 4,
        Delta: 0,
        Key: vkCode,
      })
    );
  });
  videoElement.value.addEventListener("keyup", (event) => {
    const vkCode = getVKCode(event.key);
    console.log("键盘按下:", event.key, "VK Code:", vkCode);
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为

    sendSock(
      JSON.stringify({
        Button: 0,
        MousePoint: {
          X: 0,
          Y: 0,
        },
        Input: 5,
        Delta: 0,
        Key: vkCode,
      })
    );
  });
  videoElement.value.addEventListener("contextmenu", (event) => {
    console.log("右键菜单被阻止", event);
    let x = event.clientX - rect.value.left;
    let y = event.clientY - rect.value.top;
    event.stopPropagation(); // 阻止事件冒泡
    event.preventDefault(); // 阻止默认行为
    // 发送右键菜单信息到服务器
    sendSock(
      JSON.stringify({
        type: "contextmenu",
        // x: event.clientX,
        // y: event.clientY,
        x: (videoWidth / rect.value.width) * x,
        y: (videoHeight / rect.value.height) * y,
      })
    );
  });
  window.onload = () => {
    videoElement.value.focus();
  };
  // 禁止暂停播放
  videoElement.value.addEventListener("pause", () => {
    videoElement.value.play(); // 强制继续播放
  });
}

const resizeHandler = () => {
  rect.value = videoElement.value.getBoundingClientRect(); // 重新获取元素的边界框信息
  console.log("video盒子大小发生改变", rect.value);
};

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};

const cancelDebounce = debounce(resizeHandler, 50);

onMounted(() => {
  refreshTimer.value = setInterval(() => {
    getList(false);
  }, 60000);
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  // getPositionTreeList();
  getList();
  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - delHeight.value;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览大屏巡检页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  clearInterval(refreshTimer.value);
  if (hasInitVideo.value) handleCloseWs2();
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});
</script>

<style lang="scss" scoped>
:deep(.el-card__body) {
  padding: 20px 0 10px 10px !important;
}
.monitor {
  display: flex;
  align-items: flex-start;
  gap: 0 10px;
  height: calc(100vh - 260px);
  overflow-y: scroll;
  overflow-x: hidden;
  margin-bottom: 15px;
  padding: 0 20px 0 10px;
  &-bottom {
    position: fixed;
    width: 100%;
    display: flex;
    justify-content: center;
    bottom: 35px;
    left: 0;
    z-index: 2001;
    pointer-events: none;
  }
  &-btns {
    pointer-events: auto;
    // border: 1px solid red;
    display: flex;
    max-width: 90%;
    flex-wrap: wrap;
    justify-content: center;
    gap: 10px 0;
    background-color: #fff;
    padding: 10px;
    border-radius: 5px;
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.2);
  }
}

.recording-main {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;

  .normal {
    font-size: 18px;
    min-height: 380px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .online-filter {
    margin-bottom: 20px;
    display: flex;
    justify-content: space-between;

    .online-filter-content {
      display: flex;
      align-items: center;
      padding-left: 5px;
      color: #606266;
    }

    :deep(.el-checkbox__label) {
      padding-left: 0;
    }

    :deep(.el-checkbox__inner) {
      border-radius: 50%;
    }

    .checkAll {
      :deep(.el-checkbox__inner) {
        border-radius: 2px;
      }
    }
  }

  &_grid {
    margin-bottom: 20px;
    min-height: 380px;
  }

  .screen-wrapper {
    background-color: #e5e6ea;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 4px;
    display: flex;
    justify-content: center;
    align-items: center;
  }

  .screen-card {
    position: relative;
    width: 100%;
    aspect-ratio: 16/9;
    // height: 180px;
    border-radius: 4px;
    overflow: hidden;
    background-color: #fff;
    box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);

    .screen-checked {
      position: absolute;
      top: 0;
      left: 0;
      // border: 1px solid red;
      padding-top: 0.4vw;
      padding-left: 0.3vw;
      .el-checkbox {
        height: auto;
      }
      :deep(.el-checkbox__inner) {
        width: 1.5vw;
        height: 1.5vw;
        &::after {
          width: 0.5vw;
          height: 1vw;
          left: 0.45vw;
          top: 0vw;
        }
      }
    }
    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }

    .img {
      width: 100%;
      height: 100%;
      background-color: #333;
    }

    .screen-title {
      position: absolute;
      bottom: 0;
      left: 0;
      right: 0;
      padding: 0.3vw 0.5vw 0;
      background: rgba(0, 0, 0, 0.8);
      color: #fff;
      font-size: 0.8vw;
      line-height: 1.2vw;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    .screen-status {
      position: absolute;
      top: 0;
      right: 0;
      padding: 0.4vw 0.8vw;
      color: #fff;
      border-radius: 4px;
      background-color: #81b337;
      &.offline {
        background-color: #bd3124;
      }
      &.badline {
        background-color: #e99d42;
      }
    }
  }

  .custom-pagination {
    margin-top: 20px;
    display: flex;
    justify-content: center;

    :deep(.el-pagination__total) {
      display: none;
    }

    :deep(.el-pager li) {
      background-color: #f4f4f5;
      border: none;

      &.is-active {
        background-color: #409eff;
        color: #fff;
      }
    }
  }
}

.view-switch {
  margin-bottom: 10px;
}

:deep(.el-checkbox__input.is-checked .el-checkbox__inner) {
  //   background-color: black;
  //   border-color: black;
}

.dialog-content {
  .screenshot-area {
    text-align: center;
    // border: 1px solid red;
    margin-bottom: 20px;
    overflow-x: scroll;

    img {
      object-fit: contain;
    }
  }

  .operation-area {
    text-align: center;
    margin-bottom: 20px;

    .scissors-icon {
      display: inline-flex;
      align-items: center;
      gap: 5px;
      cursor: pointer;
      padding: 8px 16px;
      background-color: #f5f7fa;
      border-radius: 4px;
      transition: background-color 0.3s;

      &:hover {
        background-color: #e4e7ed;
      }
    }
  }
}

:deep(.screenshot-dialog) {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .screenshot-placeholder {
    background-color: #c4c4c5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .screenshot-success-message {
    /* display: flex;
        align-items: center;
        justify-content: center;
        gap: 10px; */
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 20px;
  }
}

.dialog_img {
  width: 100%;
}

.gallery-main {
  .gallery-grid {
    // padding: 20px;

    .gallery-item {
      position: relative;
      background-color: #333;
      margin-bottom: 40px;
      cursor: pointer;
      border-radius: 4px;

      .gallery-image {
        position: relative;
        width: 100%;
        border-radius: 4px;
        aspect-ratio: 16/9;
        display: flex;
        align-items: center;
        justify-content: center;
        overflow: hidden;

        img {
          max-width: 100%;
          max-height: 100%;
          object-fit: contain;
        }
      }

      .gallery-title {
        position: absolute;
        bottom: 0;
        left: 0;
        right: 0;
        padding: 0.3vw 0.5vw;
        background: rgba(0, 0, 0, 0.8);
        color: #fff;
        font-size: 0.8vw;
        line-height: 1.2vw;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
        div {
          display: flex;
          justify-content: space-between;
        }
      }

      .download-icon {
        position: absolute;
        bottom: -35px;
        right: -5px;
        cursor: pointer;
        background-color: #fff;
        padding: 8px;
        border-radius: 4px;
        box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
        transition: all 0.3s;
        z-index: 1;

        &:hover {
          background-color: #409eff;
          color: #fff;
        }
      }
    }
  }
}
.monitor-title {
  font-size: 16px;
  padding: 10px 20px;
}
.monitor_flex {
  display: flex;
  align-items: flex-start;
  gap: 0 10px;
  margin-bottom: 15px;
  padding: 0 20px 0 10px;
  &.hasControlBtn {
    height: calc(100vh - 260px);
    overflow-y: scroll;
    overflow-x: hidden;
  }
  .flex_1 {
    width: 18%;
    margin-right: 10px;
    border: 1px solid #f1f1f1;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }

  .flex_2 {
    width: 82%;
  }
  .installation {
    display: inline-block;
    // width: 13.5vw;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
</style>
