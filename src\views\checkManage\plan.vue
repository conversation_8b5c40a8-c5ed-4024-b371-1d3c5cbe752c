<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <div style="display: flex; gap: 0 50px">
        <div class="count_p">
          今日巡检任务完成巡检指标: {{ checkPointNum }}处
        </div>
        <div class="count_p">漏检: {{ unCheckPointNum }}处</div>
      </div>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入姓名搜索"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb12">
        <el-col
          :span="1.5"
          style="display: flex; justify-content: space-between; width: 100%"
        >
          <div>
            <el-button type="warning" plain icon="Plus" @click="handleSelect"
              >选择人员</el-button
            >
            <el-button type="success" plain icon="Setting" @click="handleSet"
              >设置巡检频率规则</el-button
            >
          </div>
          <el-button
            class="floatBtn float1"
            :type="isEdit ? 'info' : 'primary'"
            plain
            :icon="isEdit ? 'Close' : 'Edit'"
            @click="isEdit = !isEdit"
            :disabled="tableData.length < 1"
            >{{ isEdit ? "返回" : "编辑" }}</el-button
          >
          <el-button
            class="floatBtn"
            type="danger"
            plain
            icon="Notification"
            @click="handleBatchNotice"
            :disabled="tableData.length < 1"
            >一键通知</el-button
          >
        </el-col>
      </el-row>
      <el-table
        v-loading="loading"
        :data="tableData"
        border
        style="width: 100%"
      >
        <!-- 第一组列 -->
        <el-table-column :label="currentDate" align="center">
          <el-table-column
            prop="name"
            label="姓名"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column label="性别" min-width="80" show-overflow-tooltip>
            <template #default="{ row }">
              {{ row.six || "-" }}
            </template>
          </el-table-column>
        </el-table-column>

        <!-- 其他列 -->
        <el-table-column label="点位" min-width="150" align="center">
          <template #default="{ row }">
            <div class="table-link">
              {{ row.prointCodeList ? row.prointCodeList.join(",") : "" }}
              <div
                v-show="isEdit"
                class="checklink"
                @click="handleDialog(row, '分配点位')"
              >
                分配点位
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="巡检频率"
          min-width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="table-link">
              {{ row.rulesName }}
              <div
                v-show="isEdit"
                class="checklink"
                @click="handleDialog(row, '更改频率')"
              >
                更改频率
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column
          label="巡检指标"
          min-width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="table-link">
              {{ row.indicatorsName }}
              <div
                v-show="isEdit"
                class="checklink"
                @click="handleDialog(row, '更改指标')"
              >
                更改指标
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="巡检情况" min-width="120" align="center">
          <template #default="{ row }">
            <div class="table-link">
              {{ row.checkPointList ? row.checkPointList.join(",") : "-" }}
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检状态"
          min-width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="table-link">
              <span :style="{ color: statusList[row.status].color }">{{
                `${statusList[row.status].name}${
                  row.status == 2 ? row.uncheckPointNum + "处" : ""
                }`
              }}</span>
              <div
                v-show="row.status != 1"
                class="checklink"
                @click="handleDialog(row, '提醒')"
              >
                提醒
              </div>
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="100"
          align="center"
          fixed="right"
        >
          <template #default="{ row }">
            <el-button link icon="Delete" type="danger" @click="handleDel(row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 选择人员对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="personDialogVisible"
      :title="title"
      width="800px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
      @close="handleCancelDialog(0)"
    >
      <div style="margin-bottom: 20px" v-if="tableAllSelectedId.length > 0">
        已选人员：{{
          tableAllSelectedRow.map((item) => item.nickName).join("、")
        }}
      </div>
      <el-form
        class="search-list mb12"
        :model="queryParamsDialog"
        ref="queryDialogRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="nickName">
          <el-input
            v-model="queryParamsDialog.nickName"
            placeholder="请输入姓名搜索"
            clearable
            style="width: 200px"
            @keyup.enter="handleQueryDialog"
          />
        </el-form-item>
        <el-form-item label="" prop="deptId">
          <el-tree-select
            v-model="queryParamsDialog.deptId"
            :data="deptTree"
            :props="{ label: 'deptName', children: 'children' }"
            node-key="deptId"
            check-strictly
            clearable
            placeholder="请选择部门"
            @change="handleQueryDialog"
            :render-after-expand="false"
            style="width: 200px"
          />
        </el-form-item>
        <el-form-item label="" prop="sex">
          <el-select
            v-model="queryParamsDialog.sex"
            placeholder="请选择性别"
            clearable
            style="width: 140px"
            @change="handleQueryDialog"
          >
            <el-option label="男" :value="1" />
            <el-option label="女" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQueryDialog"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQueryDialog">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table
        v-loading="loadingDialog"
        ref="personTableRef"
        :data="personList"
        border
        max-height="300"
        @row-click="
          (row) => {
            rowClick(row, 'personTableRef', 'userId');
          }
        "
        @selection-change="
          (val) => {
            selectionChange(val, 'userId');
          }
        "
        @select="
          (rows, row) => {
            onTableSelect(rows, row, 'userId');
          }
        "
        @select-all="
          (selection) => {
            selectSingleTableAll(selection, 'personList', 'userId');
          }
        "
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="nickName" label="姓名" min-width="120" />
        <el-table-column prop="sex" label="性别" min-width="120">
          <template #default="{ row, $index }">
            {{ row.sex == 0 ? "女" : row.sex == 1 ? "男" : "-" }}
          </template>
        </el-table-column>
        <el-table-column prop="deptName" label="部门" min-width="120" />
      </el-table>
      <pagination
        v-show="totalDialog > 0"
        :total="totalDialog"
        v-model:page="queryParamsDialog.pageNum"
        v-model:limit="queryParamsDialog.pageSize"
        @pagination="getListDialog"
        layout="total, prev, pager, next"
        :background="false"
        :autoScroll="false"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDialog(0)">返回</el-button>
          <el-button type="primary" @click="handleConfirmDialog(0)" v-throttle
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 分配点位对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="dotDialogVisible"
      :title="title"
      width="500px"
      align-center
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-table
        ref="dotTableRef"
        :data="dotList"
        border
        highlight-current-row
        @row-click="
          (row) => {
            rowClick(row, 'dotTableRef', 'pointCode');
          }
        "
        @selection-change="
          (val) => {
            selectionChange(val, 'pointCode');
          }
        "
        @select="
          (rows, row) => {
            onTableSelect(rows, row, 'pointCode');
          }
        "
        @select-all="
          (selection) => {
            selectSingleTableAll(selection, 'dotList', 'pointCode');
          }
        "
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="pointCode" label="点位编号" min-width="120" />
        <el-table-column prop="name" label="点位名称" min-width="120" />
      </el-table>

      <pagination
        v-show="totalDialog > 0"
        :total="totalDialog"
        v-model:page="queryParamsDialog.pageNum"
        v-model:limit="queryParamsDialog.pageSize"
        @pagination="getListDialog"
        layout="total, prev, pager, next"
        :background="false"
        :autoScroll="false"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDialog(1)">返回</el-button>
          <el-button type="primary" @click="handleConfirmDialog(1)" v-throttle
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 发送提醒对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="reminderDialogVisible"
      title="发送漏检提醒"
      width="400px"
    >
      <div class="reminder-content">
        <p class="mb-4">将向以下人员发送漏检提醒：</p>
        <p class="text-lg font-bold mb-4">{{ currentRemindPerson?.name }}</p>

        <div class="missed-points mb-4">
          <p class="font-bold mb-2">漏检点位：</p>
          <ul class="list-disc pl-4">
            <li
              v-for="pointId in getMissedPointIds(currentRemindPerson)"
              :key="pointId"
            >
              {{ getPointName(pointId) }}
            </li>
          </ul>
        </div>

        <el-form ref="reminderRef" :model="reminderForm" label-width="80px">
          <el-form-item label="提醒方式">
            <el-radio-group v-model="reminderForm.method">
              <el-radio label="email">邮件提醒</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="备注">
            <el-input
              v-model="reminderForm.remarks"
              type="textarea"
              rows="3"
              placeholder="请输入备注信息"
            />
          </el-form-item>
        </el-form>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDialog(2)">取消</el-button>
          <el-button type="primary" @click="handleConfirmDialog(2)" v-throttle
            >发送提醒</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 更改频率/更改指标 -->
    <el-dialog
      class="custom-dialog"
      v-model="editDialogVisible"
      :title="title"
      width="400"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleCancelDialog(3)"
      align-center
    >
      <el-form ref="editRef" :rules="editRules" :model="rowInfo">
        <el-form-item
          v-if="title == '更改频率'"
          label="巡检频率"
          prop="rulesId"
        >
          <el-select
            placeholder="请选择频率"
            v-model="rowInfo.rulesId"
            style="width: 250px"
            clearable
          >
            <el-option
              v-for="item in frequencyList"
              :key="item.id"
              :label="item.rules"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="title == '更改指标'"
          label="巡检指标"
          prop="indicatorsId"
        >
          <el-select
            placeholder="请选择指标"
            v-model="rowInfo.indicatorsId"
            style="width: 250px"
            clearable
          >
            <el-option
              v-for="item in pointList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            />
          </el-select>
        </el-form-item>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDialog(3)">返回</el-button>
          <el-button type="primary" @click="handleConfirmDialog(3)" v-throttle
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 巡检频率规则 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="ruleDialogVisible"
      width="600"
      top="3vh"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
    >
      <el-form ref="ruleRef" :model="ruleForm">
        <div class="rule-block">
          <div class="rule-block_title">每日一次：</div>
          <el-form-item
            label="时间范围设置"
            prop="ruleList[0].time"
            :rules="
              ruleForm.ruleList[0].checked
                ? {}
                : {
                    required: true,
                    message: '请选择时间',
                    trigger: ['change', 'blur'],
                  }
            "
          >
            <el-time-picker
              v-model="ruleForm.ruleList[0].time"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              clearable
              :disabled="ruleForm.ruleList[0].checked"
            />
          </el-form-item>
          <div class="rule-block_check">
            <el-checkbox
              v-model="ruleForm.ruleList[0].checked"
              @change="
                (val) => {
                  handleCheck(val, '0');
                }
              "
              >无限制</el-checkbox
            >
          </div>
          <el-divider />
        </div>
        <div class="rule-block">
          <div class="rule-block_title">每日两次：</div>
          <el-form-item
            label="时间范围设置1"
            prop="ruleList[1].time"
            :rules="
              ruleForm.ruleList[1].checked
                ? {}
                : {
                    required: true,
                    message: '请选择时间',
                    trigger: ['change', 'blur'],
                  }
            "
          >
            <el-time-picker
              v-model="ruleForm.ruleList[1].time"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              clearable
              :disabled="ruleForm.ruleList[1].checked"
            />
          </el-form-item>
          <el-form-item
            label="时间范围设置2"
            prop="ruleList[1].time2"
            :rules="
              ruleForm.ruleList[1].checked
                ? {}
                : {
                    required: true,
                    message: '请选择时间',
                    trigger: ['change', 'blur'],
                  }
            "
          >
            <el-time-picker
              v-model="ruleForm.ruleList[1].time2"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              clearable
              :disabled="ruleForm.ruleList[1].checked"
            />
          </el-form-item>
          <div class="rule-block_check">
            <el-checkbox
              v-model="ruleForm.ruleList[1].checked"
              @change="
                (val) => {
                  handleCheck(val, 1);
                }
              "
              >无限制</el-checkbox
            >
          </div>
          <el-divider />
        </div>
        <div class="rule-block">
          <div class="rule-block_title">每周一次：</div>
          <el-form-item
            label="时间范围设置"
            prop="ruleList[2].time"
            :rules="
              ruleForm.ruleList[2].checked
                ? {}
                : {
                    required: true,
                    message: '请选择时间',
                    trigger: ['change', 'blur'],
                  }
            "
          >
            <el-time-picker
              v-model="ruleForm.ruleList[2].time"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              clearable
              :disabled="ruleForm.ruleList[2].checked"
            />
          </el-form-item>
          <div class="rule-block_check">
            <el-checkbox
              v-model="ruleForm.ruleList[2].checked"
              @change="
                (val) => {
                  handleCheck(val, 2);
                }
              "
              >无限制</el-checkbox
            >
          </div>
          <el-divider />
        </div>
        <div class="rule-block">
          <div class="rule-block_title">每周多次：</div>
          <el-form-item
            label="时间范围设置"
            prop="ruleList[3].time"
            :rules="
              ruleForm.ruleList[3].checked
                ? {}
                : {
                    required: true,
                    message: '请选择时间',
                    trigger: ['change', 'blur'],
                  }
            "
          >
            <el-time-picker
              v-model="ruleForm.ruleList[3].time"
              is-range
              range-separator="-"
              start-placeholder="最早打卡时间"
              end-placeholder="最迟打卡时间"
              value-format="HH:mm:ss"
              format="HH:mm:ss"
              clearable
              :disabled="ruleForm.ruleList[3].checked"
            />
          </el-form-item>
          <div class="rule-block_check">
            <el-checkbox
              v-model="ruleForm.ruleList[3].checked"
              @change="
                (val) => {
                  handleCheck(val, 3);
                }
              "
              >无限制</el-checkbox
            >
          </div>
          <div class="rule-block_subtit">请选择巡检日期：</div>
          <el-checkbox-group v-model="ruleForm.ruleList[3].checkList">
            <el-checkbox label="周一" value="1" />
            <el-checkbox label="周二" value="2" />
            <el-checkbox label="周三" value="3" />
            <el-checkbox label="周四" value="4" />
            <el-checkbox label="周五" value="5" />
            <el-checkbox label="周六" value="6" />
            <el-checkbox label="周日" value="7" />
          </el-checkbox-group>
        </div>
      </el-form>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancelDialog(4)">返回</el-button>
          <el-button type="primary" @click="handleConfirmDialog(4)" v-throttle
            >确定</el-button
          >
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="checkPlan">
import {
  ref,
  computed,
  onMounted,
  getCurrentInstance,
  reactive,
  toRefs,
} from "vue";
import { useRoute } from "vue-router";
import { findIndexInObejctArr } from "@/utils";
import {
  getMaintainList,
  getRoleList,
  deleteInspectionPlan,
} from "@/api/distribution/member";

import {
  schoolInspectionList,
  addSchoolInspection,
  userInspectionList,
  addSchoolInspectionPoint,
  addSchoolInspectionIndicators,
  addSchoolInspectionRules,
  schoolInspectionRulesList,
  schoolInspectionStatistics,
  schoolInspectionRulesInfo,
  addAndUpdate,
  schoolIndicatorsList,
  schoolPointList,
  inspectionNotice,
} from "@/api/check";
import { treeDept } from "@/api/system/dept";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
const route = useRoute();

const state = reactive({
  currentDate: (() => {
    const date = new Date();
    const day = date.getDay();
    const weekDays = ["日", "一", "二", "三", "四", "五", "六"];
    return `${date.getMonth() + 1}.${date.getDate()} (周${weekDays[day]})`;
  })(),
  curPatrolPlanId: "",
  isEdit: false,
  loading: false,
  loadingDialog: false,
  personQueryRef: null,
  personTableRef: null,
  queryDialogRef: null,
  dotTableRef: null,
  reminderRef: null,
  editRef: null,
  ruleRef: null,
  checkPointNum: 0,
  unCheckPointNum: 0,
  total: 0,
  totalDialog: 0,
  title: "更改频率",
  personDialogVisible: false,
  dotDialogVisible: false,
  ruleDialogVisible: false,
  editDialogVisible: false,
  reminderDialogVisible: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: "",
  },
  queryParamsDialog: {
    pageNum: 1,
    pageSize: 5,
    nickName: "",
    sex: "",
    deptId: "",
    roleKey: ["inspection"],
  },
  reminderForm: {
    method: "email",
    remarks: "",
  },
  ruleForm: {
    ruleList: [
      { time: null, time2: null, checked: false, checkList: [] },
      { time: null, time2: null, checked: false, checkList: [] },
      { time: null, time2: null, checked: false, checkList: [] },
      { time: null, time2: null, checked: false, checkList: [] },
    ],
  },
  rowInfo: {
    indicatorsId: "",
    rulesId: "",
  },
  deptTree: [],
  tableData: [],
  pointList: [], // 指标下拉列表
  personList: [],
  personList_all: [],
  curPlanObj: {
    prointCodeList: [],
  },
  dotList: [],
  dotList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  frequencyList: [], // 频率下拉列表
  statusList: [
    {
      name: "未开始",
      color: "#F56C6C",
    },
    {
      name: "完成巡检",
      color: "#67C23A",
    },
    {
      name: "漏检",
      color: "#F56C6C",
    },
  ],
  editRules: {
    indicatorsId: [
      { required: true, message: "请选择指标", trigger: "change" },
    ],
    rulesId: [{ required: true, message: "请选择频率", trigger: "change" }],
  },
});
const {
  deptTree,
  ruleList,
  editRules,
  curPlanObj,
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  loading,
  loadingDialog,
  currentDate,
  curPatrolPlanId,
  isEdit,
  queryDialogRef,
  personQueryRef,
  personTableRef,
  dotTableRef,
  editRef,
  reminderRef,
  ruleRef,
  statusList,
  checkPointNum,
  unCheckPointNum,
  total,
  totalDialog,
  title,
  queryParams,
  queryParamsDialog,
  ruleForm,
  rowInfo,
  reminderForm,
  tableData,
  pointList,
  dotList,
  dotList_all,
  personList,
  dotDialogVisible,
  personDialogVisible,
  ruleDialogVisible,
  editDialogVisible,
  reminderDialogVisible,
  frequencyList,
} = toRefs(state);

// 校验频率规则表单
function handleCheck(val, index) {
  console.log("check", val);
  if (index == 1) {
    if (val) {
      state.ruleRef.clearValidate(`ruleList[1].time`);
      state.ruleRef.clearValidate(`ruleList[1].time2`);
    } else {
      state.ruleRef.validateField(`ruleList[1].time`);
      state.ruleRef.validateField(`ruleList[1].time2`);
    }
  } else {
    val
      ? state.ruleRef.clearValidate(`ruleList[${index}].time`)
      : state.ruleRef.validateField(`ruleList[${index}].time`);
  }
}

// 删除计划人员
function handleDel(row) {
  proxy.$modal
    .confirm(`确定从巡检计划中删除姓名为${row.name}的人员？`)
    .then(() => {
      deleteInspectionPlan({ id: row.patrolPlanId }).then((res) => {
        proxy.$modal.msgSuccess("删除成功");
        getList();
      });
    });
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 搜索按钮操作 */
function handleQueryDialog() {
  state.queryParamsDialog.pageNum = 1;
  getListDialog();
}
/** 重置按钮操作 */
function resetQueryDialog() {
  proxy.resetForm("queryDialogRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  handleQueryDialog();
}

function getList() {
  state.loading = true;
  console.log("巡检计划传参", state.queryParams);
  schoolInspectionList(state.queryParams).then((response) => {
    console.log(response);
    if (response.code == 200) {
      const { records, total } = response.data;
      state.tableData = records || [];
      state.total = total || 0;
      state.loading = false;
    }
  });
  schoolInspectionStatistics().then((response) => {
    // console.log(response);
    if (response.code == 200) {
      const { checkPointNum, unCheckPointNum } = response.data;
      state.checkPointNum = checkPointNum;
      state.unCheckPointNum = unCheckPointNum;
    }
  });
}

// 弹窗里的分页
function getListDialog() {
  if (state.title == "分配点位") {
    getDotList(1);
  }
  if (state.title == "选择人员") {
    getPersonList(1);
  }
}

// 点击弹窗取消按钮
function handleCancelDialog(val) {
  if (val == 0) {
    resetQueryDialog();
    state.personDialogVisible = false;
    return;
  }

  if (val == 1) {
    state.dotDialogVisible = false;
  }
  if (val == 2) {
    state.reminderDialogVisible = false;
  }
  if (val == 3) {
    editRef.value.resetFields();
    state.editDialogVisible = false;
  }
  if (val == 4) {
    state.ruleDialogVisible = false;
  }
}

// 点击弹窗确认按钮
function handleConfirmDialog(val) {
  if (val == 0) {
    if (state.tableAllSelectedRow.length < 1) {
      proxy.$modal.msgWarning("请至少选择一个人员");
      return;
    }
    console.log("提交的人员", state.tableAllSelectedRow);

    let obj = {
      userList: state.tableAllSelectedRow.map((item) => {
        return {
          userId: item.userId,
          sex: item.sex == 0 ? "女" : item.sex == 1 ? "男" : null,
          name: item.nickName,
        };
      }),
    };

    console.log(obj);
    proxy.$modal.loading();
    addSchoolInspection(obj)
      .then((res) => {
        console.log(res);
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("更改成功");
        state.tableAllSelectedRow = [];
        state.tableAllSelectedId = [];
        state.queryParamsDialog.pageNum = 1;
        getList();
        state.personDialogVisible = false;
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }
  // 提交更新点位
  if (val == 1) {
    if (state.tableAllSelectedId.length < 1) {
      proxy.$modal.msgWarning("请至少选择一个点位");
      return;
    }
    let obj = {
      patrolPlanId: state.curPlanObj.patrolPlanId,
      pointList: state.tableAllSelectedRow.map((item) => {
        return {
          pointId: item.id,
          pointCode: item.pointCode,
          pointName: item.name,
        };
      }),
    };
    console.log("提交的点位", obj);
    proxy.$modal.loading();
    addSchoolInspectionPoint(obj)
      .then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("更改成功");
          proxy.$modal.closeLoading();
          getList();
          state.dotDialogVisible = false;
        }
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }
  if (val == 2) {
  }
  // 提交更新频率/指标
  if (val == 3) {
    state.editRef.validate((valid) => {
      if (valid) {
        if (state.title == "更改指标") {
          const { name, id } = state.pointList.find(
            (_) => _.id == state.rowInfo.indicatorsId
          );
          let obj = {
            patrolPlanId: state.curPlanObj.patrolPlanId,
            indicatorsId: id,
            indicatorsName: name,
          };
          proxy.$modal.loading();
          addSchoolInspectionIndicators(obj)
            .then((resp) => {
              if (resp.code == 200) {
                proxy.$modal.msgSuccess("更改成功");
                proxy.$modal.closeLoading();
                getList();
                state.editDialogVisible = false;
              }
            })
            .catch(() => {
              proxy.$modal.closeLoading();
            });
        }
        if (state.title == "更改频率") {
          const { number, id } = state.frequencyList.find(
            (_) => _.id == state.rowInfo.rulesId
          );
          let obj = {
            patrolPlanId: state.curPlanObj.patrolPlanId,
            rulesId: id,
            number,
          };
          proxy.$modal.loading();
          addSchoolInspectionRules(obj)
            .then((resp) => {
              if (resp.code == 200) {
                proxy.$modal.msgSuccess("更改成功");
                proxy.$modal.closeLoading();
                getList();
                state.editDialogVisible = false;
              }
            })
            .catch(() => {
              proxy.$modal.closeLoading();
            });
        }
      }
    });
  }
  // 提交更新频率规则
  if (val == 4) {
    if (
      state.ruleForm.ruleList[3].checkList.length < 1 &&
      !state.ruleForm.ruleList[3].checked
    ) {
      proxy.$modal.msgWarning("请选择每周多次的巡检日期");
      return;
    }
    let arr = [];
    state.ruleForm.ruleList.map((item, index) => {
      let obj = {
        number: index + 1,
        timeRules: item.checked ? 0 : 1,
        week: item.checkList.join(","),
      };
      let punchCardStartTime = "",
        punchCardEndTime = "";
      if (index == 1) {
        punchCardStartTime = item.time?.join(",") || "";
        punchCardEndTime = item.time2?.join(",") || "";
      } else {
        punchCardStartTime = item.time[0] || "";
        punchCardEndTime = item.time[1] || "";
      }
      arr.push({
        ...obj,
        punchCardStartTime,
        punchCardEndTime,
      });
    });
    console.log(arr);
    proxy.$modal.loading();
    addAndUpdate({ rulesList: arr })
      .then((response) => {
        console.log(response);
        proxy.$modal.closeLoading();
        if (response.code == 200) {
          proxy.$modal.msgSuccess("设置成功");
          state.ruleDialogVisible = false;
        }
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }
}

// 设置频率规则
function handleSet() {
  state.title = "设置巡检频率规则";

  schoolInspectionRulesInfo().then((response) => {
    if (response.code == 200 && !!response.data) {
      if (response.data.length > 0) {
        state.ruleForm.ruleList = response.data.reduce((res, cur) => {
          let time = null;
          if (
            cur.number != 2 &&
            cur.punchCardStartTime &&
            cur.punchCardEndTime
          ) {
            time = [cur.punchCardStartTime, cur.punchCardEndTime];
          }
          res.push({
            ...cur,
            time: cur.number == 2 ? cur.punchCardStartTime.split(",") : time,
            time2: cur.number == 2 ? cur.punchCardEndTime.split(",") : null,
            checked: cur.timeRules == 1 ? false : true,
            checkList: !!cur.week ? cur.week.split(",") : [],
          });
          return res;
        }, []);
      } else {
        state.ruleForm.ruleList = [];
        for (let i = 0; i < 4; i++) {
          let obj = {
            number: i + 1,
            checked: false,
            time: null,
            time2: null,
            checkList: [],
          };
          state.ruleForm.ruleList.push(obj);
        }
      }
      console.log("rules", response, state.ruleForm.ruleList);
    }
  });
  state.ruleDialogVisible = true;
}

// 打开选择人员弹窗
function handleSelect() {
  state.title = "选择人员";
  state.tableRadio = [];
  state.tableAllSelectedRow = [];
  state.tableAllSelectedId = [];
  state.queryParamsDialog.pageNum = 1;
  state.queryParamsDialog.pageSize = 5;
  getPersonList();
  getDeptTree();
  state.personDialogVisible = true;
}

async function getPersonList(type) {
  console.log(state.queryParamsDialog, "state.queryParamsDialog");
  state.loadingDialog = true;
  const { deptId } = state.queryParamsDialog;
  await getRoleList({
    ...state.queryParamsDialog,
    deptIds: deptId ? [deptId] : [],
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["inspection"],
  }).then((resp) => (state.personList_all = resp.data?.records || []));
  await getRoleList({
    ...state.queryParamsDialog,
    deptIds: deptId ? [deptId] : [],
    roleKey: ["inspection"],
  }).then((resp) => {
    if (resp.code == 200) {
      console.log("人员列表", resp);
      const { total, records } = resp.data;
      state.personList = records;
      state.totalDialog = total;
      // if (!!type) {
      nextTick(() => {
        state.personList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.userId) > -1) {
            state.personTableRef.toggleRowSelection(item, true);
          } else {
            state.personTableRef.toggleRowSelection(item, false);
          }
        });
        state.loadingDialog = false;
        console.log(
          "首次打开弹窗",
          state.tableAllSelectedId,
          state.tableAllSelectedRow
        );
      });
      // }
    }
  });
}

async function getDeptTree() {
  await treeDept({ status: 0 }).then((resp) => {
    if (resp.code == 200) {
      console.log("部门列表", resp);
      state.deptTree = resp.data || [];
    }
  });
}

async function getDotList(type) {
  await schoolPointList({ ...state.queryParamsDialog, pageNum: 1, pageSize: 999999 }).then(
    (resp) => (state.dotList_all = resp.data?.records || [])
  );
  schoolPointList(state.queryParamsDialog).then((response) => {
    console.log("response", response);
    if (response.code == 200) {
      const { records, total } = response.data;
      state.dotList = records || [];
      state.totalDialog = total || 0;
      state.dotDialogVisible = true;
      if (!type) {
        nextTick(() => {
          state.curPlanObj.prointCodeList?.map((item) => {
            const idx = state.dotList_all.findIndex((_) => _.pointCode == item);
            if (idx != -1) {
              state.tableAllSelectedId.push(item);
              state.tableAllSelectedRow.push(state.dotList_all[idx]);
              // state.dotTableRef.toggleRowSelection(state.dotList_all[idx]);
            }
          }) || [];
          state.dotList.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item.pointCode) > -1) {
              state.dotTableRef.toggleRowSelection(item, true);
            } else {
              state.dotTableRef.toggleRowSelection(item, false);
            }
          });
          console.log(
            "首次打开弹窗",
            state.tableAllSelectedId,
            state.tableAllSelectedRow
          );
        });
      } else {
        nextTick(() => {
          state.dotList.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item.pointCode) > -1) {
              state.dotTableRef.toggleRowSelection(item, true);
            } else {
              state.dotTableRef.toggleRowSelection(item, false);
            }
          });
          console.log(
            "分页",
            state.tableAllSelectedId,
            state.tableAllSelectedRow
          );
        });
      }
    }
  });
}

// 从列表里打开弹窗
function handleDialog(row, title) {
  state.title = title;
  state.tableRadio = [];
  state.tableAllSelectedRow = [];
  state.tableAllSelectedId = [];
  state.curPatrolPlanId = row.patrolPlanId;
  state.curPlanObj = JSON.parse(JSON.stringify(row));
  state.queryParamsDialog.pageNum = 1;
  state.queryParamsDialog.pageSize = 5;
  console.log("row", row);

  if (title == "分配点位") {
    getDotList();
  }

  if (title == "更改频率") {
    state.editDialogVisible = true;
    schoolInspectionRulesList().then((response) => {
      console.log("频率列表", response);
      if (response.code == 200) {
        state.frequencyList = response.data || [];
        state.rowInfo.rulesId = row.rulesId || "";
        state.editDialogVisible = true;
      }
    });
  }

  if (title == "更改指标") {
    schoolIndicatorsList({ pageNum: 1, pageSize: 999999 }).then((response) => {
      console.log(response);
      if (response.code == 200) {
        const { records } = response.data;
        state.pointList = records || [];
        state.rowInfo.indicatorsId = row.indicatorsId || "";
        state.editDialogVisible = true;
      }
    });
  }

  if (title == "提醒") {
    inspectionNotice({ ids: [row.patrolPlanId] }).then((res) => {
      proxy.$modal.msgSuccess("提醒成功");
    });
  }
}

// 一键通知
function handleBatchNotice() {
  inspectionNotice({}).then((res) => {
    console.log(res);
    proxy.$modal.msgSuccess("一键通知成功");
  });
}

/** 单击某行 */
function rowClick(row, tableRefName, idName) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      idName
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state[tableRefName].setCurrentRow(null);
      state[tableRefName].toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row[idName]);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state[tableRefName].setCurrentRow(row);
    }
  }
  // else if (state.tableAllSelectedId.length < 5) {
  //   proxy.$modal.msgWarning('一次最多选择5个')
  // }
  else {
    state.tableRadio = row;
    state[tableRefName].setCurrentRow(row);
    state[tableRefName].toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val, idName) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item[idName]) === -1) {
      state.tableAllSelectedId.push(item[idName]);
      state.tableAllSelectedRow.push(item);
    }
  });
  // console.log(
  //   "多选事件",
  //   val,
  //   state.tableAllSelectedId,
  //   state.tableAllSelectedRow
  // );
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row, idName) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  // console.log("点击表格勾选触发的事件", rows, row, selected, rows.indexOf(row));
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row[idName]);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection, curListName, idName) {
  // 获取当前页码所显示的数据
  const a = state[curListName];
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state[`${curListName}_all`].forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item[idName]) === -1) {
        state.tableAllSelectedId.push(item[idName]); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

// 初始化
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.floatBtn {
  position: fixed;
  right: 35px;
  top: 270px;
  z-index: 11;
}
.float1 {
  right: 155px;
}
.checklink {
  font-size: 14px;
  color: #4095e5;
  text-decoration: underline;
  cursor: pointer;
}
.el-checkbox {
  display: block;
  margin-bottom: 15px;
}

.rule-block {
  &_title {
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    color: #333;
  }
  &_check {
    .el-checkbox {
      margin-bottom: 0;
      height: 0;
      display: flex;
      align-items: center;
      justify-content: flex-end;
    }
  }
  .el-checkbox-group {
    display: flex;
    flex-wrap: wrap;
    margin-top: 10px;
    .el-checkbox {
      width: 24%;
      margin-bottom: 0;
      display: flex;
      align-items: center;
    }
  }
}

.count_p {
  color: #bd3124;
  font-size: 20px;
  margin-bottom: 20px;
}

.person-list {
  min-height: 200px;
  max-height: 300px;
  overflow-y: auto;
}

.button-container {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
  margin-top: 16px;
}

.select-person-btn {
  display: inline-flex;
  align-items: center;
}

.inspection-status {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.reminder-content {
  .missed-points {
    background-color: #f5f7fa;
    padding: 12px;
    border-radius: 4px;

    ul {
      margin: 0;
    }
  }
}
</style>
