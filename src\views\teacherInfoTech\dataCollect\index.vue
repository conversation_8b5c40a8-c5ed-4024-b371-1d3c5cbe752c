<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>

            <el-main class="eBox" v-loading="loading">
                <div class="eBox-opt opt1">
                    <div class="icon">
                        <Monitor />
                    </div>
                    <div class="info">
                        <div class="name">多媒体教学率</div>
                        <div class="value">{{ deviceConfig.teachRatio }}%</div>
                    </div>
                </div>
                <div class="eBox-item item2">
                    <Echarts @setFontSize="setFontSize" id="syqsfx" width="100%" height="100%" :fullOptions="syqsfxOption"
                        :loading="false" />

                </div>
                <div class="eBox-opt opt2">
                    <div class="icon">
                        <Clock />
                    </div>
                    <div class="info">
                        <div class="name">多媒体教学时长</div>
                        <div class="value">{{ deviceConfig.teachTime }}小时</div>
                    </div>
                </div>

            </el-main>
        </el-card>
    </div>
</template>

<script setup name="Post">
import Echarts from '@/components/Echarts/index.vue'
import { onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { queDeviceRatio } from '@/api/city'
import { queTeacherMonth } from '@/api/teachInfo/teacher';
import { transformSize } from '@/utils';

const { proxy } = getCurrentInstance();
const route = useRoute()

const loading = ref(false);
const showSearch = ref(true);
const deviceConfig = ref({
    teachRatio: 0,
    teachTime: 0
})

//年龄使用趋势分析
let syqsfxXData = ['1月', '2月', '3月', '4月', '5月', '6月']
let syqsfxSData = [100, 140, 230, 100, 130, 200, 150]

const data = reactive({
    queryParams: {
        date: []
    }
});

const { queryParams } = toRefs(data);

/** 使用趋势分析 */
const syqsfxOption = reactive({
    options: {
        title: {
            text: '使用时长趋势分析',
            textStyle: {
                fontSize: transformSize(28)
            },
            top: '4%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            bottom: '15%',
            height: '55%',
            left: '7%',
            right: '4%'
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: [],
            type: 'line'
        }]
    },
    init: false
})

let opt1 = syqsfxOption.options

/** 获取数据 */
function setData() {
    opt1.xAxis.data = syqsfxXData
    opt1.series[0].data = syqsfxSData
}

async function getData() {
    await queDeviceRatio().then(response => {
        let { ratio, useTime } = response.data
        deviceConfig.value.teachTime = useTime
        deviceConfig.value.teachRatio = (ratio * 1).toFixed(2) || 0
    })
    await queTeacherMonth().then(response => {
        if (response.data.monthVoList.length > 0) {
            syqsfxXData = [], syqsfxSData = []
            response.data.monthVoList.map((item, index) => {
                syqsfxXData[index] = item.month
                syqsfxSData[index] = item.runTime || 0
            })
            opt1.xAxis.data = syqsfxXData
            opt1.series[0].data = syqsfxSData
        }
    })
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(28)
}

function injectOption() {
    proxy.syqsfx = syqsfxOption
}

injectOption()

onMounted(() => {
    setData()
    getData()
})
</script>

<style lang="scss" scoped>
.eBox {
    padding: 20px 0;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 22vh 22vh;
    grid-gap: 2.5vh 1.2vw;

    &-opt,
    &-item {
        border: 1px solid #e8e8e8;
    }

    .opt1>.icon {
        background-color: #91cc75;
    }

    .opt2>.icon {
        background-color: #5470c6;
    }

    &-opt {
        display: flex;

        .icon {
            width: 33%;
            height: 100%;
            display: flex;
            padding: 0 1.8vw;
            color: #fff;
        }

        .info {
            width: 70%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;

            .name,
            .value {
                font-size: 1.6vw;
            }

            .value {
                margin-top: 1vh;
                font-weight: bold;
            }
        }


    }

    .item2 {
        grid-row-start: span 2;
        grid-column-start: 2;
        grid-column-end: 4;
    }
}
</style>
