import { setTimeout } from "core-js";
import { parseTime } from "./ruoyi";
import modal from "@/plugins/modal";
import { getToken } from "@/utils/auth";
import { sm2Encrypt } from "@/utils/sm2encrypt.js";

/**
 * 在树形数据中根据ID查找节点
 * @param {Array} tree 树形数据数组
 * @param {string|number} targetId 要查找的节点ID
 * @returns {Object|null} 找到的节点对象（包含子节点），未找到返回null
 */
export function findTreeNode(tree, targetId) {
  if (!tree || !tree.length) return null;
  
  for (const node of tree) {
    if (node.id === targetId) return node;
    if (node.children) {
      const found = findTreeNode(node.children, targetId);
      if (found) return found;
    }
  }
  return null;
}

export function isValidUrl(domain) {
  const domainRegex = /^(?![.-])[a-z\d-]{1,63}(?<![.-])$/i;
  return domainRegex.test(domain);
}

export function isIPv4(str) {
  const parts = str.split(".");
  if (parts.length !== 4) return false;
  return parts.every((part) => {
    // 必须是数字，不能有前导0（除非是0本身），范围 0-255
    return (
      /^\d+$/.test(part) &&
      Number(part) >= 0 &&
      Number(part) <= 255 &&
      String(Number(part)) === part
    );
  });
}

export function extractPropertyFromTree(tree, property) {
  const results = [];

  function traverse(node) {
    if (node[property] !== undefined) {
      results.push(node[property]);
    }
    if (node.children && Array.isArray(node.children)) {
      node.children.forEach((child) => traverse(child));
    }
  }

  // 处理单节点或多根节点树
  if (Array.isArray(tree)) {
    tree.forEach((root) => traverse(root));
  } else {
    traverse(tree);
  }

  return results;
}

// Echarts图表字体、间距自适应
export const fitChartSize = (size, defalteWidth = 1920) => {
  const width = document.documentElement.clientWidth;
  const ratio = width / 1920;
  return parseInt(size * ratio);
};

// 异步数据埋点
export const sendPointRequestAsync = (data = {}, isLogin = false) => {
  return new Promise((resolve, reject) => {
    console.log("进入数据埋点", data, isLogin);
    console.log("使用fetch");
    // 使用 fetch + keepalive
    let headers = {
      "Content-Type": "application/json",
    };
    isLogin
      ? (headers["X-Auth-Token"] = 1)
      : (headers["Authorization"] = `Bearer ${getToken()}`);
    fetch(`${import.meta.env.VITE_APP_BASE_API}/system/userEvent/add`, {
      method: "post",
      headers, // 请求头
      body: sm2Encrypt(JSON.stringify(data)), // 请求数据
      keepalive: true,
    })
      .then((response) => {
        if (!response.ok) {
          throw new Error("Network response was not ok");
        }
        return response.json(); // 解析JSON格式的响应体
      })
      .then((data) => resolve())
      .catch(() => reject());
  });
};

// 数据埋点
export const sendPointRequest = (data = {}, isLogin = false) => {
  console.log("进入数据埋点", data, isLogin);
  console.log("使用fetch");
  // 使用 fetch + keepalive
  let headers = {
    "Content-Type": "application/json",
  };
  isLogin
    ? (headers["X-Auth-Token"] = 1)
    : (headers["Authorization"] = `Bearer ${getToken()}`);
  fetch(`${import.meta.env.VITE_APP_BASE_API}/system/userEvent/add`, {
    method: "post",
    headers, // 请求头
    body: sm2Encrypt(JSON.stringify(data)), // 请求数据
    keepalive: true,
  });
};

// 批量数据埋点
export const sendPointRequestBatch = (data = {}, isLogin = false) => {
  console.log("进入数据埋点", data, isLogin);
  console.log("使用fetch");
  // 使用 fetch + keepalive
  let headers = {
    "Content-Type": "application/json",
  };
  isLogin
    ? (headers["X-Auth-Token"] = 1)
    : (headers["Authorization"] = `Bearer ${getToken()}`);
  fetch(`${import.meta.env.VITE_APP_BASE_API}/system/userEvent/device/add`, {
    method: "post",
    headers, // 请求头
    body: sm2Encrypt(JSON.stringify(data)), // 请求数据
    keepalive: true,
  });
};

export const scrollToErrorField = () => {
  const isError = document.getElementsByClassName("is-error");
  if (isError[0]) {
    isError[0].scrollIntoView({
      behavior: "smooth",
      block: "center",
    });
  }
};

// 下载http的不安全文件
export const downloadHttp = (url) => {
  let arr = url.split(":");
  arr[0] = arr[0] == "https" ? arr[0] : "https";
  console.log("新路径", arr);
  window.open(arr.join(":"));
};

export function extractNumbers(str) {
  const pattern = /[\d\.]+/g;
  const matches = str.match(pattern);
  console.log(matches);
  return matches?.join("") * 1 || 0;
}

export function extractWords(str) {
  const pattern = /[a-zA-Z]/g;
  const matches = str.match(pattern);
  console.log(matches);
  return matches?.join("") || "GB";
}

/**
 * 校验字符串是否为合法的Windows绝对路径
 * @param {string} path 待校验的路径
 * @returns {boolean}
 */
export function isValidWindowsAbsolutePath2(path) {
  // 定义Windows绝对路径正则表达式
  const winAbsPathRegex =
    /^([a-zA-Z]:\\|\\\\(?:[^\\/:*?"<>|\r\n]+\\)+)(?:[^\\/:*?"<>|\r\n\\]+\\)*[^\\/:*?"<>|\r\n\\]*$/;

  // 基础校验
  if (!winAbsPathRegex.test(path)) return false;

  // 额外排除纯根目录（如 C:\ 或 \\server\share\ 是合法的）
  return path.length > 3 || (path.startsWith("\\\\") && path.length > 4);
}

export function isValidRelativePath(path) {
  // 空路径或空白符路径无效
  if (!path || /^\s*$/.test(path)) return false;

  // 检查是否以绝对路径符号或盘符开头
  if (/^[\\/]/.test(path) || /^[a-zA-Z]:[\\/]/.test(path)) return false;

  // 检查是否包含非法字符（Windows非法字符：<>:"|?*）
  if (/[<>:"|?*]/.test(path)) return false;

  // 拆分路径为各组成部分
  const parts = path.split(/[\\/]/);

  for (const part of parts) {
    // 允许空字符串仅当路径为当前目录（如'.'或'./'）
    if (part === "") return false; // 拒绝连续或尾随斜杠

    // 检查部分以空格或点结尾（Windows不允许）
    if (/(?:\s|\.)$/.test(part)) return false;

    // 检查保留文件名（如CON、PRN等）
    const reservedNames = /^(con|prn|aux|nul|com[0-9]|lpt[0-9])$/i;
    if (reservedNames.test(part)) return false;
  }

  return true;
}

export function isValidWindowsAbsolutePath(path) {
  const regex = /^[A-Za-z]:\\(?:[^\\/:*?"<>|\r\n]+\\)*[^\\/:*?"<>|\r\n]*$/;
  return regex.test(path);
}

// 限制日期
export function disabledDateFn(date) {
  //   const today = formatDate(new Date().getTime())
  if (date.getTime() > new Date().getTime()) {
    // console.log('date', date, date.getTime(), (date.getTime() + 86400000), new Date().getTime())
    return true;
  }
  return false;
}

// 限制小时
export function disabledHours() {
  const a = [];

  for (let i = 0; i < 24; i++) {
    if (new Date().getHours() < i) {
      a.push(i);
    }
  }
  return a;
}

// 限制分钟
export function disabledMinutes() {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getMinutes() < i) {
      a.push(i);
    }
  }
  return a;
}

// 限制毫秒
export function disabledSeconds() {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getSeconds() < i) {
      a.push(i);
    }
  }
  return a;
}

// 查找对象在对象数组中的位置
export function findIndexInObejctArr(arr, obj, valName) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i][valName] === obj[valName]) {
      return i;
    }
  }
  return -1;
}

export function compare(property) {
  return function (a, b) {
    var value1 = a[property];
    var value2 = b[property];
    return value1 - value2;
  };
}

export function tranListToTreeData(arr, idName, pidName) {
  const treeArr = [];

  // 帮助快速确定上级
  const map = {};
  arr.forEach((item) => {
    item.children = [];
    map[item[idName]] = item;
  });
  console.log(map);
  // 写代码
  arr.forEach((item) => {
    // 对arr进行循环，对每一个元素item，如果
    // 1. item有上级元素pItem, 把item添加到pItem.children
    // 2. item没有上级元素（根据item.pid去找，找不到元素）， 添加到treeArr
    const pItem = map[item[pidName]];
    if (pItem) {
      pItem.children.push(item);
    } else {
      treeArr.push(item);
    }
  });
  return treeArr;
}

export async function copyContent(content) {
  // 复制结果
  let copyResult = true;
  // 设置想要复制的文本内容
  const text = content || "复制内容为空哦~";
  // 判断是否支持clipboard方式
  if (!!window.navigator.clipboard) {
    // 利用clipboard将文本写入剪贴板（这是一个异步promise）
    await window.navigator.clipboard
      .writeText(text)
      .then((res) => {
        console.log("复制成功");
        modal.msgSuccess("复制成功");
      })
      .catch((err) => {
        console.log("复制失败--采取第二种复制方案", err);
        // clipboard方式复制失败 则采用document.execCommand()方式进行尝试
        copyResult = copyContent2(text);
      });
  } else {
    // 不支持clipboard方式 则采用document.execCommand()方式
    copyResult = copyContent2(text);
  }
  // 返回复制操作的最终结果
  return copyResult;
}

// 复制文本内容方法二
function copyContent2(text) {
  // 复制结果
  let copyResult = true;
  // 创建一个input元素
  let inputDom = document.createElement("textarea");
  // 设置为只读 防止移动端手机上弹出软键盘
  inputDom.setAttribute("readonly", "readonly");
  // 给input元素赋值
  inputDom.value = text;
  // 将创建的input添加到body
  document.body.appendChild(inputDom);
  // 选中input元素的内容
  inputDom.select();
  // 执行浏览器复制命令
  // 复制命令会将当前选中的内容复制到剪切板中（这里就是创建的input标签中的内容）
  // Input要在正常的编辑状态下原生复制方法才会生效
  const result = document.execCommand("copy");
  // 判断是否复制成功
  if (result) {
    console.log("复制成功");
    modal.msgSuccess("复制成功");
  } else {
    console.log("复制失败");
    modal.msgError("复制失败");
    copyResult = false;
  }
  // 复制操作后再将构造的标签 移除
  document.body.removeChild(inputDom);
  return copyResult;
}

// 指定格式的echart图表的文字
export function formatTextofEchart(value) {
  const arr = value.split("");
  let str = "";
  if (arr.length > 8) {
    let str1 = arr.slice(0, 4),
      str2 = arr.splice(arr.length - 4, 4);
    str = str1.join("") + "..." + str2.join("");
  } else {
    str = value;
  }
  return str;
}

//树级结构扁平化
export function treeToArray(tree) {
  var res = [];
  for (const item of tree) {
    const { children, ...i } = item;
    if (children && children.length) {
      res = res.concat(treeToArray(children));
    }
    res.push(i);
  }
  return res;
}

// 向上查找所有父节点
export function treeFindPath(tree, func, path = [], idName = "id") {
  if (!tree) return [];
  for (const data of tree) {
    // 这里按照你的需求来存放最后返回的内容吧
    path.push(data[idName]);
    if (func(data)) return path;
    if (data.children) {
      const findChildren = treeFindPath(data.children, func, path, idName);
      if (findChildren.length) return findChildren;
    }
    path.pop();
  }
  return [];
}

/**
 * @description 格式化时间
 * @param {String|Number} dateTime 需要格式化的时间戳
 * @param {String} fmt 格式化规则 yyyy:mm:dd|yyyy:mm|yyyy年mm月dd日|yyyy年mm月dd日 hh时MM分等,可自定义组合 默认yyyy-mm-dd
 * @returns {string} 返回格式化后的字符串
 */
export function timeFormat(dateTime = null, formatStr = "yyyy-mm-dd") {
  let date;
  // 若传入时间为假值，则取当前时间
  if (!dateTime) {
    date = new Date();
  }
  // 若为unix秒时间戳，则转为毫秒时间戳（逻辑有点奇怪，但不敢改，以保证历史兼容）
  else if (/^\d{10}$/.test(dateTime?.toString().trim())) {
    date = new Date(dateTime * 1000);
  }
  // 若用户传入字符串格式时间戳，new Date无法解析，需做兼容
  else if (typeof dateTime === "string" && /^\d+$/.test(dateTime.trim())) {
    date = new Date(Number(dateTime));
  }
  // 处理平台性差异，在Safari/Webkit中，new Date仅支持/作为分割符的字符串时间
  // 处理 '2022-07-10 01:02:03'，跳过 '2022-07-10T01:02:03'
  else if (
    typeof dateTime === "string" &&
    dateTime.includes("-") &&
    !dateTime.includes("T")
  ) {
    date = new Date(dateTime.replace(/-/g, "/"));
  }
  // 其他都认为符合 RFC 2822 规范
  else {
    date = new Date(dateTime);
  }

  const timeSource = {
    y: date.getFullYear().toString(), // 年
    m: (date.getMonth() + 1).toString().padStart(2, "0"), // 月
    d: date.getDate().toString().padStart(2, "0"), // 日
    h: date.getHours().toString().padStart(2, "0"), // 时
    M: date.getMinutes().toString().padStart(2, "0"), // 分
    s: date.getSeconds().toString().padStart(2, "0"), // 秒
    // 有其他格式化字符需求可以继续添加，必须转化成字符串
  };

  for (const key in timeSource) {
    const [ret] = new RegExp(`${key}+`).exec(formatStr) || [];
    if (ret) {
      // 年可能只需展示两位
      const beginIndex = key === "y" && ret.length === 2 ? 2 : 0;
      formatStr = formatStr.replace(ret, timeSource[key].slice(beginIndex));
    }
  }

  return formatStr;
}

/**
 * 表格时间格式化
 */
export function formatDate(cellValue) {
  if (cellValue == null || cellValue == "") return "";
  var date = new Date(cellValue);
  var year = date.getFullYear();
  var month =
    date.getMonth() + 1 < 10
      ? "0" + (date.getMonth() + 1)
      : date.getMonth() + 1;
  var day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
  var hours = date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
  var minutes =
    date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
  var seconds =
    date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
  return (
    year + "-" + month + "-" + day + " " + hours + ":" + minutes + ":" + seconds
  );
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (("" + time).length === 10) {
    time = parseInt(time) * 1000;
  } else {
    time = +time;
  }
  const d = new Date(time);
  const now = Date.now();

  const diff = (now - d) / 1000;

  if (diff < 30) {
    return "刚刚";
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + "分钟前";
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + "小时前";
  } else if (diff < 3600 * 24 * 2) {
    return "1天前";
  }
  if (option) {
    return parseTime(time, option);
  } else {
    return (
      d.getMonth() +
      1 +
      "月" +
      d.getDate() +
      "日" +
      d.getHours() +
      "时" +
      d.getMinutes() +
      "分"
    );
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url;
  const search = url.substring(url.lastIndexOf("?") + 1);
  const obj = {};
  const reg = /([^?&=]+)=([^?&=]*)/g;
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1);
    let val = decodeURIComponent($2);
    val = String(val);
    obj[name] = val;
    return rs;
  });
  return obj;
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length;
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i);
    if (code > 0x7f && code <= 0x7ff) s++;
    else if (code > 0x7ff && code <= 0xffff) s += 2;
    if (code >= 0xdc00 && code <= 0xdfff) i--;
  }
  return s;
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = [];
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i]);
    }
  }
  return newArray;
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return "";
  return cleanArray(
    Object.keys(json).map((key) => {
      if (json[key] === undefined) return "";
      return encodeURIComponent(key) + "=" + encodeURIComponent(json[key]);
    })
  ).join("&");
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split("?")[1]).replace(/\+/g, " ");
  if (!search) {
    return {};
  }
  const obj = {};
  const searchArr = search.split("&");
  searchArr.forEach((v) => {
    const index = v.indexOf("=");
    if (index !== -1) {
      const name = v.substring(0, index);
      const val = v.substring(index + 1, v.length);
      obj[name] = val;
    }
  });
  return obj;
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement("div");
  div.innerHTML = val;
  return div.textContent || div.innerText;
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== "object") {
    target = {};
  }
  if (Array.isArray(source)) {
    return source.slice();
  }
  Object.keys(source).forEach((property) => {
    const sourceProperty = source[property];
    if (typeof sourceProperty === "object") {
      target[property] = objectMerge(target[property], sourceProperty);
    } else {
      target[property] = sourceProperty;
    }
  });
  return target;
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return;
  }
  let classString = element.className;
  const nameIndex = classString.indexOf(className);
  if (nameIndex === -1) {
    classString += "" + className;
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length);
  }
  element.className = classString;
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === "start") {
    return new Date().getTime() - 3600 * 1000 * 24 * 90;
  } else {
    return new Date(new Date().toDateString());
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result;

  const later = function () {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp;

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last);
    } else {
      timeout = null;
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args);
        if (!timeout) context = args = null;
      }
    }
  };

  return function (...args) {
    context = this;
    timestamp = +new Date();
    const callNow = immediate && !timeout;
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait);
    if (callNow) {
      result = func.apply(context, args);
      context = args = null;
    }

    return result;
  };
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== "object") {
    throw new Error("error arguments", "deepClone");
  }
  const targetObj = source.constructor === Array ? [] : {};
  Object.keys(source).forEach((keys) => {
    if (source[keys] && typeof source[keys] === "object") {
      targetObj[keys] = deepClone(source[keys]);
    } else {
      targetObj[keys] = source[keys];
    }
  });
  return targetObj;
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr));
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + "";
  const randomNum = parseInt((1 + Math.random()) * 65536) + "";
  return (+(randomNum + timestamp)).toString(32);
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp("(\\s|^)" + cls + "(\\s|$)"));
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += " " + cls;
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp("(\\s|^)" + cls + "(\\s|$)");
    ele.className = ele.className.replace(reg, " ");
  }
}

export function makeMap(str, expectsLowerCase) {
  const map = Object.create(null);
  const list = str.split(",");
  for (let i = 0; i < list.length; i++) {
    map[list[i]] = true;
  }
  return expectsLowerCase ? (val) => map[val.toLowerCase()] : (val) => map[val];
}

export const exportDefault = "export default ";

export const beautifierConf = {
  html: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "separate",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: false,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
  js: {
    indent_size: "2",
    indent_char: " ",
    max_preserve_newlines: "-1",
    preserve_newlines: false,
    keep_array_indentation: false,
    break_chained_methods: false,
    indent_scripts: "normal",
    brace_style: "end-expand",
    space_before_conditional: true,
    unescape_strings: false,
    jslint_happy: true,
    end_with_newline: true,
    wrap_line_length: "110",
    indent_inner_html: true,
    comma_first: false,
    e4x: true,
    indent_empty_lines: true,
  },
};

// 首字母大小
export function titleCase(str) {
  return str.replace(/( |^)[a-z]/g, (L) => L.toUpperCase());
}

// 下划转驼峰
export function camelCase(str) {
  return str.replace(/_[a-z]/g, (str1) => str1.substr(-1).toUpperCase());
}

export function isNumberStr(str) {
  return /^[+-]?(0|([1-9]\d*))(\.\d+)?$/g.test(str);
}

/** echarts里的字体大小、宽高边距等自适应 */
export function transformSize(size) {
  const width = document.documentElement.clientWidth;
  const ratio = width / 1920;
  return parseInt(size * ratio);
}

// base64图片转为流
export function getBlob(base64) {
  var mimeString = base64.split(",")[0].split(":")[1].split(";")[0]; // mime类型
  var byteString = atob(base64.split(",")[1]); //base64 解码
  var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
  var intArray = new Uint8Array(arrayBuffer); //创建视图
  for (var i = 0; i < byteString.length; i += 1) {
    intArray[i] = byteString.charCodeAt(i);
  }
  return new Blob([intArray], {
    type: mimeString,
  });
}

/** 下载base64流图片 */
export function downloadBlob2(blob, fileName) {
  modal.loading();
  const downloadElement = document.createElement("a");
  const href = window.URL.createObjectURL(blob); // 创建下载的链接
  downloadElement.href = href;
  downloadElement.download = fileName; // 下载后文件名
  document.body.appendChild(downloadElement);
  downloadElement.click(); // 点击下载
  document.body.removeChild(downloadElement); // 下载完成移除元素
  window.URL.revokeObjectURL(href); // 释放掉blob对象
  setTimeout(() => modal.closeLoading(), 200);
}

/** 下载流文件 */
export function downloadBlob(file, type, fileName) {
  try {
    modal.loading();
    const blob = new Blob([file], { type });
    const downloadElement = document.createElement("a");
    const href = window.URL.createObjectURL(blob); // 创建下载的链接
    console.log(href);
    downloadElement.href = href;
    downloadElement.download = fileName; // 下载后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); // 点击下载
    document.body.removeChild(downloadElement); // 下载完成移除元素
    window.URL.revokeObjectURL(href); // 释放掉blob对象
    setTimeout(() => modal.closeLoading(), 200);
  } catch (e) {
    console.log(e);
  }
}
