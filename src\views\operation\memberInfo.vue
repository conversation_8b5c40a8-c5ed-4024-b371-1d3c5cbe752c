<template>
  <div class="member-info app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          人员详细信息
          <el-button icon="Back" @click="goBack">返回</el-button>
        </div>
      </template>

      <div class="member-info_tit">基本信息</div>
      <el-descriptions class="margin-top" :column="3" border>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="工号"
        >
          {{ memberInfo.number }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="姓名"
        >
          {{ memberInfo.name }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="性别"
        >
          {{ memberInfo.sex }}
        </el-descriptions-item>
        <!-- <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="所属机构"
        >
          {{ memberInfo.institutionName }}
        </el-descriptions-item> -->
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="所属部门"
        >
          {{ memberInfo.deptName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="岗位"
        >
          {{ memberInfo.postName }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="手机号码"
        >
          {{ memberInfo.phone }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="邮箱"
        >
          {{ memberInfo.email }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="状态"
        >
          <el-tag :type="memberInfo.status === 0 ? 'success' : 'danger'">
            {{ memberInfo.status === 0 ? "启用" : "停用" }}
          </el-tag>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ memberInfo.createTime }}
        </el-descriptions-item>
        <!-- <el-descriptions-item label="备注">
                    {{ memberInfo.remark || '-' }}
                </el-descriptions-item> -->
      </el-descriptions>

      <div class="member-info_tit">巡检记录</div>
      <el-table
        :data="checkList"
        border
        v-loading="checkLoading"
        element-loading-text="加载中..."
      >
        <el-table-column
          label="巡检人员"
          prop="name"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检范围"
          prop="pointCodeList"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.pointCodeList.join(', ') || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="巡检时间"
          prop="date"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检指标"
          prop="indicatorsName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检状态"
          prop="status"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="row.status === 0 ? 'info' : row.status === 2 ? 'success' : 'danger'">
              {{ row.status === 0 ? "未完成" : row.status === 2 ? "已完成" : "其他状态" }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120">
          <template #default="scope">
            <el-button
              link
              icon="Search"
              type="primary"
              @click="handleViewInspection(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <div class="pagination-container">
        <el-pagination
          v-model:current-page="checkQueryParams.pageNum"
          v-model:page-size="checkQueryParams.pageSize"
          :total="checkTotal"
          :page-sizes="[10, 20, 30, 50]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
      
      <div class="member-info_tit">日常考勤</div>
      <el-table
        :data="clockList"
        border
        v-loading="loading"
        element-loading-text="加载中..."
      >
        <el-table-column
          label="打卡人"
          prop="userName"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="打卡日期"
          prop="workDate"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="打卡类型"
          prop="clockType"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="打卡时间"
          prop="clockTime"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.clockTime || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="打卡地点"
          prop="clockPlace"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            {{ row.clockPlace || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="打卡状态"
          prop="isClock"
          min-width="120"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="row.isClock === '未打卡' ? 'danger' : 'success'">
              {{ row.isClock }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
      <div class="pagination-container">
        <el-pagination
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          :total="attendanceTotal"
          :page-sizes="[10, 20, 30, 50]"
          background
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup name="memberInfo">
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { getMaintainInfo, listClock } from "@/api/distribution/member";
import {
  schoolInspectionRecordInfo,
  schoolInspectionRecordList,
} from "@/api/check";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const memberInfo = ref({});
const checkList = ref([]);
const clockList = ref([]);
const total = ref(0);
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userId: null,
});
const loading = ref(false);
const checkLoading = ref(false);
const checkQueryParams = ref({
  pageNum: 1,
  pageSize: 10,
  userId: null,
});
const checkTotal = ref(0);
const attendanceTotal = ref(0);

onMounted(() => {
  const { id } = route.query;
  if (id) {
    queryParams.value.userId = id;
    checkQueryParams.value.userId = id;
    getMemberInfo(id);
    getClockList();
    getInspectionList();
  }
});

// 获取人员详细信息
function getMemberInfo(id) {
  getMaintainInfo(id)
    .then((res) => {
      if (res.code === 200) {
        memberInfo.value = {
          number: res.data.workId || "-",
          name: res.data.nickName || "-",
          // sex: res.data.sex === "0" ? "男" : "女",
          sex: res.data.sex === "0" ? "男" : res.data.sex === "1" ? "女" : "-",
          deptName: res.data.deptName || "-",
          postName: res.data.postName || "-",
          phone: res.data.phoneNumber || "-",
          email: res.data.email || "-",
          status: parseInt(res.data.status),
          createTime: res.data.createTime || "-",
          remark: res.data.remark || "-",
          isBindWx: res.data.isBindWx || "-",
        };
      }
    })
    .catch((error) => {
      console.error("Failed to get member info:", error);
    });
}

// 获取考勤记录列表
function getClockList() {
  loading.value = true;
  listClock(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        console.log("Clock list:", res.data);
        clockList.value = res.data.rows;
        attendanceTotal.value = res.data.total;
      }
    })
    .catch((error) => {
      console.error("Failed to get clock list:", error);
    })
    .finally(() => {
      loading.value = false;
    });
}

// 获取巡检记录列表
function getInspectionList() {
  checkLoading.value = true;
  schoolInspectionRecordList(checkQueryParams.value)
    .then((res) => {
      if (res.code === 200) {
        console.log("Inspection list:", res.data);
        checkTotal.value = res.data.total;
        checkList.value = res.data.records || [];
      }
    })
    .catch((error) => {
      console.error("Failed to get inspection list:", error);
    })
    .finally(() => {
      checkLoading.value = false;
    });
}

// 查看巡检详情
function handleViewInspection(row) {
  // 实现查看巡检详情的逻辑
  console.log("View:", row);
  router.push({
    path: "/checkManage/recordInfo",
    query: {
      id: row.patrolPlanId,
    },
  });
}

// 处理每页显示数量变化
function handleSizeChange(size) {
  queryParams.value.pageSize = size;
  getClockList();
}

// 处理页码变化
function handleCurrentChange(page) {
  queryParams.value.pageNum = page;
  getClockList();
}

function goBack() {
  if (route.query.url) {
    proxy.$tab.closeOpenPage({
      path: route.query.url,
    });
  } else {
    proxy.$tab.closeOpenPage();
  }
}
</script>

<style lang="scss" scoped>
.member-info {
  &_tit {
    color: #333;
    font-size: 16px;
    font-weight: bold;
    margin: 15px 0;
    &:first-child {
      margin-top: 0;
    }
  }
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  .page-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .pagination-container {
    margin-top: 20px;
    display: flex;
    justify-content: flex-end;
  }
}
</style>
