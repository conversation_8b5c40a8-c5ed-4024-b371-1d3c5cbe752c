
import { sm2 } from 'sm-crypto'

let privateKey = "4e8e840cf91fbd024c6c47cecd86bcd1e13b27f6eeeba833e65a3929ba883e6a"; //解密使用
let publicKey = "04fe4f6192ac8267d553410eb1371f7419e1544b2a115f966a99f0d4c663f9db40e61d4eac83fa654b9747516b44e105f890162c0d37e0c179cc23bea0e3f128e8"; //加密使用

const cipherMode = 1; //默认是1

//加密
export function sm2Encrypt(encrText) {
    return '04' + sm2.doEncrypt(encrText, publicKey, cipherMode)
}

//解密
export function sm2Decrypt(encrText) {
    return sm2.doDecrypt(encrText.replace('04', ''), privateKey, cipherMode)
}