<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
            <el-form-item label="" prop="tenantName">
                <el-input v-model="queryParams.tenantName" placeholder="请输入园区名称" clearable style="width: 200px"
                    @keyup.enter="handleQuery" />
            </el-form-item>
            <el-form-item label="" prop="tenantStatus">
                <el-select v-model="queryParams.tenantStatus" placeholder="请选择园区状态" @change="handleQuery" clearable
                    filterable style="width: 200px">
                    <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
                    </el-option>
                </el-select>
            </el-form-item>
            <el-form-item>
                <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
        </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增园区</el-button>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="info" plain icon="Sort" @click="toggleExpandAll">展开/折叠</el-button>
                </el-col>
                <!-- <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
            </el-row>

            <el-table v-loading="loading" :data="tableData" v-if="refreshTable" row-key="tenantId"
                :default-expand-all="isExpandAll" :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">

                <el-table-column label="园区名称" minWidth="120px" prop="tenantName" />
                <el-table-column label="园区排序" minWidth="120px" prop="sort" />
                <el-table-column label="状态" align="center" minWidth="120px">
                    <template #default="scope">
                        <el-tag v-if="statusList[scope.row.tenantStatus]" effect="dark"
                            :type="statusList[scope.row.tenantStatus].type">{{
                                statusList[scope.row.tenantStatus].label }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" minWidth="120px" align="center" prop="createTime" min-width="180">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d} {h}:{i}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="180" align="center" fixed="right"
                    class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                        <el-button link type="primary" icon="Plus" @click="handleAdd(scope.row)">新增</el-button>
                        <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                v-model:limit="queryParams.size" @pagination="getList" />
        </el-card>

        <!-- 添加或修改岗位对话框 -->
        <el-dialog :title="title" v-model="open" width="500px" append-to-body :close-on-click-modal="false">
            <el-form ref="ledgerRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="上级园区" prop="parentId">
                    <el-tree-select v-model="form.parentId" :data="tenantOptions"
                        :props="{ value: 'tenantId', label: 'tenantName', children: 'children' }" value-key="tenantId"
                        placeholder="选择上级园区" check-strictly style="width: 100%;" />
                </el-form-item>
                <el-form-item label="园区名称" prop="tenantName">
                    <el-input v-model="form.tenantName" :readonly="readonly" placeholder="请输入园区名称" />
                </el-form-item>
                <el-form-item label="排序" prop="sort">
                    <el-input-number v-model="form.sort" controls-position="right" :min="0" />
                </el-form-item>
                <el-form-item label="园区状态" prop="tenantStatus">
                    <el-select v-model="form.tenantStatus" placeholder="请选择园区状态" :disabled="readonly" filterable
                        style="width: 100%">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="parkManage">
import { useRoute } from "vue-router";
import { tenantPage, tenantAdd, tenantDel, tenantEdit, tenantTree } from "@/api/park";

const { proxy } = getCurrentInstance();
const route = useRoute()

const tableData = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const readonly = ref(false);
const statusList = ref([
    { label: '正常', value: 0 },
    { label: '异常', value: 1 }
]);
const total = ref(0);
const title = ref("");
const tenantOptions = ref([
//     {
//     tenantName: '主类目',
//     tenantId: '0',
//     children: []
// }
]);
const isExpandAll = ref(true);
const refreshTable = ref(true);

const data = reactive({
    form: {},
    queryParams: {
        tenantName: '',
        tenantStatus: ''
    },
    rules: {
        tenantName: [{ required: true, message: "园区名称不能为空", trigger: "blur" }],
        tenantStatus: [{ required: true, message: "园区状态不能为空", trigger: ["blur", "change"] }],
        sort: [{ required: true, message: "园区排序不能为空", trigger: ["blur", "change"] }],
    }
});

const { queryParams, form, rules } = toRefs(data);

/** 展开/折叠操作 */
function toggleExpandAll() {
    refreshTable.value = false;
    isExpandAll.value = !isExpandAll.value;
    nextTick(() => {
        refreshTable.value = true;
    });
}
/** 查询园区列表 */
function getList() {
    loading.value = true;
    tenantTree(queryParams.value).then(response => {
        console.log(response.data, Array.isArray(response.data))
        tableData.value = Array.isArray(response.data) ? response.data :  response.data && !!response.data.tenantId ? [response.data] : []
        tenantOptions.value = Array.isArray(response.data) ? response.data :  response.data && !!response.data.tenantId ? [response.data] : []
        // total.value = response.data.total
        loading.value = false
    })
}
/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        parentId: '',
        tenantId: '',
        tenantName: '',
        tenantStatus: 0,
        sort: 0
    };
    proxy.resetForm("ledgerRef");
}
/** 搜索按钮操作 */
function handleQuery() {
    // queryParams.value.current = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
    reset();
    if (row != undefined) {
        form.value.parentId = row.tenantId;
    }
    open.value = true;
    title.value = "添加园区";
}

/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    title.value = "修改园区";
    form.value = JSON.parse(JSON.stringify(row))
    open.value = true;
}

/** 提交按钮 */
function submitForm() {
    proxy.$refs["ledgerRef"].validate(valid => {
        if (valid) {
            if (!!form.value.tenantId) {
                tenantEdit(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    getList();
                    open.value = false
                })
            } else {
                tenantAdd(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    getList();
                    open.value = false
                })
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row) {
    proxy.$modal.confirm('是否确认删除园区名称为' + row.tenantName + '"的园区数据？').then(function () {
        return tenantDel({ id: row.tenantId })
    }).then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
    }).catch(() => { });
}

getList();
</script>
