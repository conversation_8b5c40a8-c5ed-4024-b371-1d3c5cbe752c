import axios from "axios";
import {
  ElNotification,
  ElMessageBox,
  ElMessage,
  ElLoading,
} from "element-plus";
import { getToken } from "@/utils/auth";
import errorCode from "@/utils/errorCode";
import { tansParams, blobValidate } from "@/utils/ruoyi";
import cache from "@/plugins/cache";
import { saveAs } from "file-saver";
import useUserStore from "@/store/modules/user";
import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";

let downloadLoadingInstance;
let noMsg = false;
// 是否显示重新登录
export let isRelogin = { show: false };

axios.defaults.headers["Content-Type"] = "application/json;charset=utf-8";
// axios.defaults.headers['client'] = 'pc-client'
// 创建axios实例
const service = axios.create({
  // axios中请求配置有baseURL选项，表示请求URL公共部分
  baseURL: import.meta.env.VITE_APP_BASE_API,
  // baseURL: '/prod-api'
  // 超时
  timeout: 300000,
});

// request拦截器
service.interceptors.request.use(
  (config) => {
    // 添加判断，如果数据是 FormData 类型，就不要设置 Content-Type
    if (!(config.data instanceof FormData)) {
      config.headers["Content-Type"] = "application/json;charset=utf-8";
    }

    noMsg = !!config.noMsg;

    // 是否需要设置 token
    const isToken = (config.headers || {}).isToken === false;
    // 是否请求中控端
    const isControl = (config.headers || {}).isControl === true;

    // 是否需要防止数据重复提交
    const isRepeatSubmit = (config.headers || {}).repeatSubmit === false;
    if (getToken() && !isToken) {
      config.headers["Authorization"] = "Bearer " + getToken();
    }
    if (isControl) {
      config.headers["X-CORP-ID"] = "0";
    }
    // get请求映射params参数
    if (config.method === "get" && config.params) {
      let url = config.url + "?" + tansParams(config.params);
      url = url.slice(0, -1);
      config.params = {};
      config.url = url;
    }
    if (
      !isRepeatSubmit &&
      (config.method === "post" || config.method === "put")
    ) {
      const requestObj = {
        url: config.url,
        data:
          typeof config.data === "object"
            ? JSON.stringify(config.data)
            : config.data,
        time: new Date().getTime(),
      };
      const requestSize = Object.keys(JSON.stringify(requestObj)).length; // 请求数据大小
      const limitSize = 5 * 1024 * 1024; // 限制存放数据5M
      if (requestSize >= limitSize) {
        console.warn(
          `[${config.url}]: ` +
            "请求数据大小超出允许的5M限制，无法进行防重复提交验证。"
        );
        return config;
      }
      const sessionObj = cache.session.getJSON("sessionObj");
      if (
        sessionObj === undefined ||
        sessionObj === null ||
        sessionObj === ""
      ) {
        cache.session.setJSON("sessionObj", requestObj);
      } else {
        const s_url = sessionObj.url; // 请求地址
        const s_data = sessionObj.data; // 请求数据
        const s_time = sessionObj.time; // 请求时间
        // const interval = 1000; // 间隔时间(ms)，小于此时间视为重复提交
        const interval = 0; // 间隔时间(ms)，小于此时间视为重复提交
        if (
          s_data === requestObj.data &&
          requestObj.time - s_time < interval &&
          s_url === requestObj.url
        ) {
          // const message = "数据正在处理，请勿重复提交";
          // console.warn(`[${s_url}]: ` + message);
          // return Promise.reject(new Error(message));
          return;
        } else {
          cache.session.setJSON("sessionObj", requestObj);
        }
      }
    }
    // 修改这里的加密逻辑，如果是 FormData 则不进行加密
    config.data =
      !!config.data && !config.isKey && !(config.data instanceof FormData)
        ? sm2Encrypt(JSON.stringify(config.data))
        : config.data;
    // 如果是 FormData 格式，让浏览器自动处理 boundary
    if (config.data instanceof FormData) {
      // 不要删除或修改 Content-Type，保持原样
      // delete config.headers['Content-Type'];
    }

    // 获取当前路由路径
    const currentPath = window.location.hash.replace("#", "");

    // 修改判断条件，使用includes判断路径中是否包含特定字符串
    if (
      currentPath.includes("/system/user") ||
      currentPath.includes("/system/role") ||
      currentPath.includes("/deviceLedger/ledger") ||
      currentPath.includes("/taskManage/taskCenter/claim") ||
      currentPath.includes("/taskManage/taskCenter/todo") ||
      currentPath.includes("/taskManage/taskCenter/handled")
    ) {
      const selectedParkData = localStorage.getItem("selectedParkData");

      // 只有当 selectedParkData 存在且有值时才添加headers
      if (selectedParkData && selectedParkData.trim() !== "") {
        try {
          const parkData = JSON.parse(selectedParkData);
          if (parkData.corpId) {
            config.headers["X-CHILD-CORP-ID"] = parkData.corpId;
          }
          if (parkData.corpSecret) {
            config.headers["X-CHILD-CORP-SECRET"] = parkData.corpSecret;
          }
          if (parkData.corpUrl) {
            config.headers["X-CHILD-CORP-URL"] = parkData.corpUrl;
          }
        } catch (error) {
          // console.error("解析 selectedParkData 失败:", error);
        }
      }
    }

    // console.log("最终 request headers:", config.headers);
    return config;
  },
  (error) => {
    console.log(error);
    return Promise.reject(error);
  }
);

// 响应拦截器
service.interceptors.response.use(
  (res) => {
    // 如果响应是字符串，直接返回成功
    if (typeof res.data === "string") {
      return Promise.resolve({
        code: 200,
        msg: res.data,
      });
    }

    // 未设置状态码则默认成功状态
    const code = res.data.code || 200;
    // 获取错误信息
    const msg = errorCode[code] || res.data.msg || errorCode["default"];
    // 二进制数据则直接返回
    if (
      res.request.responseType === "blob" ||
      res.request.responseType === "arraybuffer"
    ) {
      return res.data;
    }
    if (code === 401) {
      if (!isRelogin.show) {
        isRelogin.show = true;
        ElMessageBox.confirm(
          "登录状态已过期，您可以继续留在该页面，或者重新登录",
          "系统提示",
          {
            confirmButtonText: "重新登录",
            cancelButtonText: "取消",
            type: "warning",
            closeOnClickModal: false,
            closeOnPressEscape: false,
            showClose: false,
          }
        )
          .then(() => {
            isRelogin.show = false;
            useUserStore()
              .logOut()
              .then(() => {
                const name = window.location.pathname.replace(/\//g, "");
                window.location.href =
                  import.meta.env.VITE_APP_BASE_API == "/djg-prod-api"
                    ? `/djgAdmin`
                    : "/" + name;
                window.location.reload();
              });
          })
          .catch(() => {
            isRelogin.show = false;
          });
      }
      return Promise.reject("无效的会话，或者会话已过期，请重新登录。");
    } else if (code === 500) {
      if (!noMsg) ElMessage({ message: msg, type: "error" });
      return Promise.reject(msg);
    } else if (code === 601) {
      ElMessage({
        message:
          res.data?.msg == "该机构已被停用"
            ? res.data?.msg
            : "扫码登录失败，请使用手机验证码登录",
        type: "error",
      });
      return Promise.reject(res.data.msg);
    } else if (code !== 200) {
      ElNotification.error({ title: msg });
      return Promise.reject("error");
    } else {
      // 只在 data 存在且为加密字符串时进行解密
      if (typeof res.data.data === "string") {
        try {
          res.data.data = JSON.parse(sm2Decrypt(res.data.data));
        } catch (error) {
          try {
            res.data.data = sm2Decrypt(res.data.data);
          } catch (error) {
            console.warn("数据解密失败或非加密数据:", error);
          }
        }
      }
      return Promise.resolve(res.data);
    }
  },
  (error) => {
    console.log("err" + error);
    let { message } = error;
    if (message == "Network Error") {
      message = "后端接口连接异常";
    } else if (message.includes("timeout")) {
      message = "系统接口请求超时";
    } else if (message.includes("Request failed with status code")) {
      message = "系统接口" + message.substr(message.length - 3) + "异常";
    }
    if (!noMsg)
      ElMessage({ message: message, type: "error", duration: 5 * 1000 });
    return Promise.reject(error);
  }
);

// 通用下载方法
export function download(url, params, filename, config) {
  downloadLoadingInstance = ElLoading.service({
    text: "正在下载数据，请稍候",
    background: "rgba(0, 0, 0, 0.7)",
  });

  return service
    .post(url, params, {
      transformRequest: [
        (params) => {
          return tansParams(params);
        },
      ],
      headers: { "Content-Type": "application/x-www-form-urlencoded" },
      responseType: "blob",
      ...config,
    })
    .then(async (data) => {
      const isBlob = blobValidate(data);
      if (isBlob) {
        const blob = new Blob([data]);
        saveAs(blob, filename);
      } else {
        const resText = await data.text();
        const rspObj = JSON.parse(resText);
        const errMsg =
          errorCode[rspObj.code] || rspObj.msg || errorCode["default"];
        ElMessage.error(errMsg);
      }
      downloadLoadingInstance.close();
    })
    .catch((r) => {
      console.error(r);
      ElMessage.error("下载文件出现错误，请联系管理员！");
      downloadLoadingInstance.close();
    });
}

export default service;
