<template>
  <div class="operation app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">{{ route.meta.title }}</div>
      </template>
      <div class="operation-main">
        <div class="operation-main_right">
          <el-form
            ref="queryRef"
            :model="queryParams"
            :inline="true"
            class="search-list"
          >
            <el-form-item prop="nickName">
              <el-input
                v-model.trim="queryParams.nickName"
                placeholder="请输入姓名查询"
                clearable
                @keyup.enter="handleQuery"
                @clear="handleQuery"
                style="width: 200px"
              />
            </el-form-item>
            <el-form-item prop="phone">
              <el-input
                v-model.trim="queryParams.phone"
                placeholder="请输入联系方式查询"
                clearable
                @keyup.enter="handleQuery"
                @clear="handleQuery"
                style="width: 200px"
              />
            </el-form-item>
            <!-- <el-form-item prop="gender">
              <el-select
                v-model="queryParams.gender"
                placeholder="请选择性别"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <el-option
                  v-for="item in genderOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item> -->
            <el-form-item prop="isCurrentCorp">
              <el-select
                v-model="queryParams.isCurrentCorp"
                placeholder="请选择绑定机构类型"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <el-option label="当前机构" :value="1" />
                <el-option label="其他机构" :value="0" />
                <el-option label="未绑定机构" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择审批状态"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <el-option label="审批中" :value="0" />
                <el-option label="审批通过" :value="1" />
                <el-option label="审批拒绝" :value="2" />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12" style="gap: 12px 0px">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Check"
                @click="handleBatchApprove"
                :disabled="tableAllSelectedId.length < 1"
                >批量通过</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Close"
                @click="handleBatchReject"
                :disabled="tableAllSelectedId.length < 1"
                >批量拒绝</el-button
              >
            </el-col>
          </el-row>

          <el-table
            ref="tableRef"
            v-loading="loading"
            :data="tableList"
            border
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
          >
            <el-table-column
              type="selection"
              label=""
              width="80"
              align="center"
              fixed="left"
              :selectable="(row) => row.status === 0"
            />
            <el-table-column
              label="审核单编号"
              minWidth="120px"
              prop="code"
              show-overflow-tooltip
            />
            <el-table-column
              label="姓名"
              align="center"
              minWidth="120px"
              prop="nickName"
              show-overflow-tooltip
            />
            <!-- <el-table-column
              label="性别"
              align="center"
              minWidth="80"
              prop="sex"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                {{ row.sex || "-" }}
              </template>
            </el-table-column> -->
            <el-table-column
              label="申请设备编码"
              align="center"
              minWidth="120px"
              prop="deviceCode"
              show-overflow-tooltip
            />
            <el-table-column
              label="联系方式"
              align="center"
              minWidth="120px"
              prop="phone"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.phone || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="身份证号"
              align="center"
              minWidth="180px"
              prop="idCard"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.idCard || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="有无职工岗位"
              min-width="120"
              show-overflow-tooltip
              align="center"
            >
              <template #default="scope">
                {{ scope.row.employeesPostId ? "有" : "无" }}
              </template>
            </el-table-column>
            <el-table-column
              label="绑定机构"
              min-width="120"
              show-overflow-tooltip
              align="center"
            >
              <template #default="{ row }">
                {{
                  row.isCurrentCorp === 1
                    ? "当前机构"
                    : row.isCurrentCorp === 0
                    ? "其他机构"
                    : "未绑定机构"
                }}
              </template>
            </el-table-column>
            <el-table-column
              label="状态"
              align="center"
              minWidth="100px"
              prop="status"
              show-overflow-tooltip
            >
              <template #default="{ row }">
                <el-tag :type="getStatusType(row.status)">{{
                  getStatusText(row.status)
                }}</el-tag>
              </template>
            </el-table-column>
            <el-table-column
              label="审批意见"
              align="center"
              minWidth="120px"
              prop="opinions"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.opinions || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="250"
              align="center"
              fixed="right"
              v-if="queryParams.status != 1 && queryParams.status != 2"
            >
              <template #default="scope">
                <template v-if="scope.row.status == 0">
                  <el-button
                    link
                    type="primary"
                    icon="Check"
                    @click.stop="handleApprove(scope)"
                    >审批通过</el-button
                  >
                  <el-button
                    link
                    type="danger"
                    icon="Close"
                    @click.stop="handleReject(scope)"
                    >审批拒绝</el-button
                  >
                </template>
                <span v-else>-</span>
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>
    </el-card>

    <!-- 添加审批对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="approveDialogVisible"
      title="审批通过"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancelApprove"
    >
      <el-form
        ref="approveFormRef"
        :model="approveForm"
        :rules="approveRules"
        label-width="80px"
      >
        <el-form-item label="审批意见" prop="opinions">
          <el-input
            v-model="approveForm.opinions"
            type="textarea"
            placeholder="请输入"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item
          v-if="approveForm.isBatch"
          label="职工岗位"
          prop="position"
        >
          <el-select
            v-model="approveForm.position"
            placeholder="请选择职工岗位"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in postList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>
        <el-form-item
          v-if="
            approveForm.isCurrentCorp !== 0 &&
            !approveForm.employeesPostId &&
            !approveForm.isBatch
          "
          label="职工岗位"
          prop="position"
        >
          <el-select
            v-model="approveForm.position"
            placeholder="请选择职工岗位"
            clearable
            style="width: 100%"
          >
            <el-option
              v-for="item in postList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
              :disabled="item.disabled"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-throttle type="primary" @click="confirmApprove"
            >确 定</el-button
          >
          <el-button @click="cancelApprove">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加拒绝对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="rejectDialogVisible"
      title="审批拒绝"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancelReject"
    >
      <el-form
        ref="rejectFormRef"
        :model="rejectForm"
        :rules="rejectRules"
        label-width="80px"
      >
        <el-form-item label="审批意见" prop="opinions">
          <el-input
            v-model="rejectForm.opinions"
            type="textarea"
            placeholder="请输入"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="confirmReject" v-throttle
            >确 定</el-button
          >
          <el-button @click="cancelReject">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="operation">
import { useRoute } from "vue-router";
import { listPost } from "@/api/system/post_new";
import {
  treeToArray,
  treeFindPath,
  findIndexInObejctArr,
  downloadBlob,
} from "@/utils";
import { getToken } from "@/utils/auth";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import {
  getDeviceApplyList,
  auditDeviceApply,
  getEmployeesPostList,
} from "@/api/teacher/index";

const route = useRoute();
const { proxy } = getCurrentInstance();
const state = reactive({
  tableRef: null,
  formRef: null,
  title: "",
  total: 0,
  loading: false,
  tableList: [],
  tableList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  postList: [],
  queryParams: {
    current: 1,
    pageSize: 10,
    nickName: "",
    phone: "",
    gender: "",
  },
  approveDialogVisible: ref(false),
  approveFormRef: ref(),
  approveForm: reactive({
    isBatch: false,
    employeesPostId: "",
    opinions: "",
    position: "1",
    isCurrentCorp: 0,
    employeesPostId: "",
    rowId: null,
  }),
  approveRules: {
    opinions: [{ required: true, message: "请输入审批意见", trigger: "blur" }],
    position: [
      { required: true, message: "请选择职工岗位", trigger: "change" },
    ],
  },
  rejectDialogVisible: ref(false),
  rejectFormRef: ref(),
  rejectForm: reactive({
    isBatch: false,
    isCurrentCorp: 0,
    opinions: "",
    rowId: null,
  }),
  rejectRules: {
    opinions: [{ required: true, message: "请输入审批意见", trigger: "blur" }],
  },
});
const {
  tableRef,
  tableRadio,
  tableList_all,
  tableAllSelectedRow,
  tableAllSelectedId,
  tableList,
  postList,
  queryParams,
  loading,
  total,
  approveDialogVisible,
  approveFormRef,
  approveForm,
  approveRules,
  rejectDialogVisible,
  rejectFormRef,
  rejectForm,
  rejectRules,
} = toRefs(state);

// 添加性别选项
const genderOptions = [
  { value: "0", label: "男" },
  { value: "1", label: "女" },
];

// 审批通过处理函数
function handleApprove(scope) {
  approveForm.value.isBatch = false;
  approveForm.value.rowId = scope.row.id || null;
  approveForm.value.isCurrentCorp = scope.row.isCurrentCorp;
  approveForm.value.opinions = "";
  approveForm.value.employeesPostId = scope.row.employeesPostId || "";
  approveForm.value.position =
    scope.row.isCurrentCorp !== 0 && !scope.row.employeesPostId ? "1" : "";
  approveDialogVisible.value = true;
}

// 拒绝处理函数
function handleReject(scope) {
  rejectForm.value.isBatch = false;
  rejectForm.value.rowId = scope.row.id || null;
  rejectForm.value.isCurrentCorp = scope.row.isCurrentCorp;
  rejectForm.value.opinions = "";
  rejectDialogVisible.value = true;
}

// 添加状态映射函数
const getStatusText = (status) => {
  const statusMap = {
    0: "审批中",
    1: "审批通过",
    2: "审批拒绝",
  };
  return statusMap[status] || "未知状态";
};

// 修改状态标签类型判断函数
const getStatusType = (status) => {
  const typeMap = {
    0: "warning",
    1: "success",
    2: "danger",
  };
  return typeMap[status] || "info";
};

// 修改批量通过处理函数
function handleBatchApprove() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一条记录");
    return;
  }

  // 检查是否所有选中的记录都是待审核状态
  const hasProcessed = state.tableAllSelectedRow.some(
    (row) => row.status !== 0
  );
  if (hasProcessed) {
    proxy.$modal.msgWarning("只能对待审核的记录进行批量操作");
    return;
  }

  const hasAbnormal = state.tableAllSelectedRow.some(
    (row) => row.isCurrentCorp === 0 || !!row.employeesPostId
  );
  if (hasAbnormal) {
    proxy.$modal.msgWarning("存在其他机构/已绑定职工岗位的记录，批量操作失败");
    return;
  }

  // 重置表单并打开对话框
  approveForm.value.isBatch = true;
  approveForm.value.rowId = state.tableAllSelectedId;
  approveForm.value.opinions = "";
  approveForm.value.position = "1";
  approveDialogVisible.value = true;
}

// 修改批量拒绝处理函数
function handleBatchReject() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一条记录");
    return;
  }

  // 检查是否所有选中的记录都是待审核状态
  const hasProcessed = state.tableAllSelectedRow.some(
    (row) => row.status !== 0
  );
  if (hasProcessed) {
    proxy.$modal.msgWarning("只能对待审核的记录进行批量操作");
    return;
  }

  // 重置表单并打开对话框
  rejectForm.value.isBatch = true;
  rejectForm.value.rowId = state.tableAllSelectedId;
  rejectForm.value.opinions = "";
  rejectDialogVisible.value = true;
}

function getList() {
  state.loading = true;
  getDeviceApplyList(state.queryParams)
    .then((res) => {
      if (res.code === 200) {
        console.log("设备使用列表", res.data);

        state.tableList = res.data.records;
        state.total = res.data.total;
        nextTick(() => {
          state.tableList.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item.id) > -1) {
              state.tableRef.toggleRowSelection(item, true);
            } else {
              state.tableRef.toggleRowSelection(item, false);
            }
          });
        });
      }
    })
    .catch(() => {
      state.tableList = [];
      state.total = 0;
    })
    .finally(() => {
      state.loading = false;
    });
  getDeviceApplyList({
    ...state.queryParams,
    pageNum: 1,
    pageSize: 999999,
  }).then((response) => {
    const { records } = response.data;
    state.tableList_all = records || [];
  });
}

// 搜索方法
function handleQuery() {
  state.queryParams.current = 1; // 重置到第一页
  getList();
}

// 重置搜索
function resetQuery() {
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  proxy.$refs["queryRef"].resetFields();
  state.queryParams = {
    current: 1,
    pageSize: 10,
    nickName: "",
    phone: "",
    gender: "",
  };
  getList();
}

// 确认审批通过函数以支持批量操作
async function confirmApprove() {
  approveFormRef.value.validate(async (valid) => {
    if (valid) {
      proxy.$modal.loading();
      const isBatch = Array.isArray(approveForm.value.rowId);

      try {
        if (isBatch) {
          const params = {
            auditUsers: state.tableAllSelectedRow.map((item) => {
              return {
                id: item.id,
                isCurrentCorp: item.isCurrentCorp,
              };
            }),
            status: 1,
            opinions: approveForm.value.opinions,
            employeesPostId: approveForm.value.position,
          };
          console.log("批量审批通过传参", params);
          await auditDeviceApply(params);
        } else {
          const params = {
            auditUsers: [
              {
                id: approveForm.value.rowId,
                isCurrentCorp: approveForm.value.isCurrentCorp,
              },
            ],
            status: 1,
            opinions: approveForm.value.opinions,
            employeesPostId: approveForm.value.position,
          };
          console.log("单个审批通过传参", params);
          await auditDeviceApply(params);
        }

        getList();
        proxy.$modal.msgSuccess(isBatch ? "批量通过成功" : "审批通过成功");
        // 重置表单和选择状态
        approveFormRef.value.resetFields();
        state.tableAllSelectedId = [];
        state.tableAllSelectedRow = [];
        state.tableRef.clearSelection();
      } catch (error) {
        proxy.$modal.msgError("审批失败");
      } finally {
        proxy.$modal.closeLoading();
        approveDialogVisible.value = false;
      }
    }
  });
}

// 取消审批
function cancelApprove() {
  approveFormRef.value.resetFields();
  approveForm.value.rowId = null;
  approveForm.value.opinions = "";
  approveForm.value.position = "";
  state.tableAllSelectedId = [];
  state.tableAllSelectedRow = [];
  state.tableRef.clearSelection();
  approveDialogVisible.value = false;
}

// 确认拒绝函数以支持批量操作
async function confirmReject() {
  rejectFormRef.value.validate(async (valid) => {
    if (valid) {
      proxy.$modal.loading();
      const isBatch = Array.isArray(rejectForm.value.rowId);

      try {
        if (isBatch) {
          const params = {
            auditUsers: state.tableAllSelectedRow.map((item) => {
              return {
                id: item.id,
                isCurrentCorp: item.isCurrentCorp,
              };
            }),
            status: 2,
            opinions: rejectForm.value.opinions,
          };
          console.log("批量审批拒绝传参", params);
          await auditDeviceApply(params);
        } else {
          const params = {
            auditUsers: [
              {
                id: rejectForm.value.rowId,
                isCurrentCorp: rejectForm.value.isCurrentCorp,
              },
            ],
            status: 2,
            opinions: rejectForm.value.opinions,
          };
          console.log("单个审批拒绝传参", params);
          await auditDeviceApply(params);
        }

        getList();
        proxy.$modal.msgSuccess(isBatch ? "批量拒绝成功" : "审批拒绝成功");
        // 重置表单和选择状态
        rejectFormRef.value.resetFields();
        state.tableAllSelectedId = [];
        state.tableAllSelectedRow = [];
        state.tableRef.clearSelection();
      } catch (error) {
        proxy.$modal.msgError("审批失败");
      } finally {
        proxy.$modal.closeLoading();
        rejectDialogVisible.value = false;
      }
    }
  });
}

// 取消拒绝
function cancelReject() {
  rejectFormRef.value.resetFields();
  rejectForm.value.rowId = null;
  rejectForm.value.opinions = "";
  state.tableAllSelectedId = [];
  state.tableAllSelectedRow = [];
  state.tableRef.clearSelection();
  rejectDialogVisible.value = false;
}

// 添加获取岗位列表函数
async function getPostList() {
  await getEmployeesPostList({ current: 1, pageSize: 999999 }).then((resp) => {
    if (resp.code == 200) {
      state.postList = resp.data?.records.map((item) => {
        item.disabled = item.id == 2 || item.id == 3;
        return item;
      });
      console.log("岗位列表", state.postList);
    }
  });
}

onMounted(() => {
  getList();
  getPostList(); // 添加获取岗位列表
});

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "id"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.id);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      console.log("高亮");
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else if (row.status == 0) {
    // 只有待审核的记录才能被选中
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 只保存待审核的记录
  val.forEach((item) => {
    if (item.status === 0 && state.tableAllSelectedId.indexOf(item.id) === -1) {
      // 只有待审核的记录才能被选中
      state.tableAllSelectedId.push(item.id);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.id);
    if (index > -1) {
      state.tableAllSelectedId.splice(index, 1);
      state.tableAllSelectedRow.splice(index, 1);
    }
  } else if (row.status === 0) {
    // 只有待审核的记录才能被选中
    if (state.tableAllSelectedId.indexOf(row.id) === -1) {
      state.tableAllSelectedId.push(row.id);
      state.tableAllSelectedRow.push(row);
    }
  }
}

// 表格全选触发的事件
function selectSingleTableAll(selection) {
  const a = state.tableList.filter((_) => _.status === 0);
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      // 改用 id 而不是 userId
      flag_inCurrentPage = true;
      return;
    }
  });

  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (
        state.tableAllSelectedId.indexOf(item.id) === -1 &&
        item.status === 0
      ) {
        // 改用 id 而不是 userId
        state.tableAllSelectedId.push(item.id);
        state.tableAllSelectedRow.push(item);
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = [];
    state.tableAllSelectedRow = [];
  }
}
</script>

<style lang="scss" scoped>
.dialog-footer {
  text-align: center;
}
</style>
