<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
      >
        <el-form-item label="" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入位置名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择位置状态"
            @change="handleQuery"
            clearable
            style="width: 200px"
          >
            <el-option
              v-for="dict in statusList"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="Plus"
            @click="handleAdd"
            v-hasPermi="['system:Position:add']"
            >新增</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button type="info" plain icon="Sort" @click="toggleExpandAll"
            >展开/折叠</el-button
          >
        </el-col>
        <!-- <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>

      <el-table
        v-if="refreshTable"
        v-loading="loading"
        :data="positionList"
        row-key="id"
        :default-expand-all="isExpandAll"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
        min-height="400"
      >
        <el-table-column
          prop="name"
          label="位置名称"
          min-width="200"
        ></el-table-column>
        <el-table-column
          prop="sort"
          label="排序"
          min-width="200"
          align="center"
        ></el-table-column>
        <el-table-column
          prop="status"
          label="状态"
          min-width="100"
          align="center"
        >
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.status]"
              effect="dark"
              :type="statusList[scope.row.status].type"
              >{{ statusList[scope.row.status].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          prop="createTime"
          min-width="200"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          align="center"
          min-width="220"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <div v-if="scope.row.name != '默认位置'">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click="handleUpdate(scope.row)"
                v-hasPermi="['system:Position:update']"
                >修改</el-button
              >
              <el-button
                link
                type="success"
                icon="Plus"
                @click="handleAdd(scope.row)"
                v-hasPermi="['system:Position:add']"
                >新增</el-button
              >
              <el-button
                v-if="scope.row.id != 0"
                link
                type="danger"
                icon="Delete"
                @click="handleDelete(scope.row)"
                v-hasPermi="['system:Position:delete']"
                >删除</el-button
              >
            </div>
            <div v-else style="color: gray">默认位置无法操作</div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加或修改位置对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="cancel"
    >
      <el-form
        ref="positionRef"
        :model="form"
        :rules="rules"
        label-width="80px"
      >
        <el-row>
          <el-col :span="24">
            <el-form-item label="上级位置" prop="parentId">
              <el-tree-select
                v-model="form.parentId"
                :data="positionTree"
                :props="{ value: 'id', label: 'name', children: 'children' }"
                value-key="id"
                placeholder="选择上级位置"
                check-strictly
                clearable
                :disabled="Treedisabled"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="位置名称" prop="name">
              <el-input
                v-model="form.name"
                placeholder="请输入位置名称"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="显示排序" prop="sort">
              <el-input-number
                v-model="form.sort"
                controls-position="right"
                :min="0"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="位置状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="dict in statusList"
                  :key="dict.value"
                  :value="dict.value"
                  >{{ dict.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitForm"
            v-throttle
            :disabled="submitting"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
 
 <script setup name="Dept">
import {
  addPosition,
  checkPosition,
  delPosition,
  getPositionTree,
  updatePosition,
  positionInfo,
} from "@/api/mediaTeach/position.js";
import { useRoute } from "vue-router";
import { onMounted, onBeforeUnmount } from "vue";
import { sendPointRequest } from "@/utils";

const { proxy } = getCurrentInstance();

const route = useRoute();
const positionList = ref([]);
const positionTree = ref([
  //  {
  //    name: '所有位置',
  //    id: 0,
  //    children: []
  //  }
]);
const open = ref(false);
const loading = ref(true);
const Treedisabled = ref(false);
const tableTreeOption = ref([]);
const showSearch = ref(true);
const title = ref("");
const isExpandAll = ref(false);
const refreshTable = ref(true);
const statusList = ref([
  { label: "正常", value: 0, type: "primary" },
  { label: "停用", value: 1, type: "danger" },
]);

const data = reactive({
  addTimer: null,
  addSecond: 0,
  submitting: false,
  form: {},
  queryParams: {
    name: "",
    status: "",
  },
  rules: {
    //   parentId: [{ required: true, message: "上级位置不能为空", trigger: "blur" }],
    name: [
      { required: true, message: "位置名称不能为空", trigger: "blur" },
      { max: 20, message: "位置名称最多输入20个字符", trigger: "blur" },
    ],
    sort: [{ required: true, message: "显示排序不能为空", trigger: "blur" }],
  },
});

const { addTimer, addSecond, submitting, queryParams, form, rules } =
  toRefs(data);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

/** 查询位置列表 */
function getList() {
  loading.value = true;
  getPositionTree(queryParams.value).then((response) => {
    console.log("安装位置", response.data);
    positionList.value = response.data || [];
    positionTree.value = response.data || [];
    loading.value = false;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    id: "",
    parentId: "",
    name: "",
    sort: 0,
    status: 0,
  };
  proxy.resetForm("positionRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}
/** 新增按钮操作 */
function handleAdd(row) {
  if(row.tier >= 3){
    proxy.$modal.msgError("不能超过三个级别")
    return
  };
  data.submitting = false;
  reset();
  if (row != undefined) {
    form.value.parentId = row.id;
  }
  open.value = true;
  title.value = "添加位置";
  Treedisabled.value = false;
  positionTree.value = positionList.value
  positionTree.value = recursionOption(positionList.value, 3);
}
/** 展开/折叠操作 */
function toggleExpandAll() {
  refreshTable.value = false;
  isExpandAll.value = !isExpandAll.value;
  nextTick(() => {
    refreshTable.value = true;
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  data.submitting = false;
  reset();
  positionInfo(row.id).then((response) => {
    console.log(response);
    form.value = response.data;
    form.value.parentId = response.data.parentId || "";
    open.value = true;
    title.value = "修改位置";
  });
  Treedisabled.value = false;
  positionTree.value = positionList.value
  filterTableTree(row);
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["positionRef"].validate((valid) => {
    if (valid) {
      data.submitting = true;
      if (!!form.value.id) {
        updatePosition({ ...form.value, parentId: form.value.parentId || 0 })
          .then((response) => {
            open.value = false;
            proxy.$modal.msgSuccess("修改成功");
            getList();
          })
          .finally(() => (data.submitting = false));
      } else {
        addPosition(form.value)
          .then((response) => {
            open.value = false;
            proxy.$modal.msgSuccess("新增成功");
            getList();
          })
          .finally(() => (data.submitting = false));
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  checkPosition(row.id).then((res) => {
    console.log("检查", res);
    if (!!res.data) {
      proxy.$modal
        .confirm(`${row.name}位置已有设备，确认删除?`)
        .then(function () {
          return delPosition(row.id);
        })
        .then(() => {
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    } else {
      proxy.$modal
        .confirm('是否确认删除位置名称为"' + row.name + '"的数据项?')
        .then(function () {
          return delPosition(row.id);
        })
        .then(() => {
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    }
  });
}
/* 过滤树形选择器的选项 */
function filterTableTree(row) {
  const { tier } = row;
  console.log(row,'row');
  
  if (tier === 1) {
    Treedisabled.value = true;
    return;
  }
  positionTree.value = recursionOption(positionList.value, tier);
}
/* 递归过滤 */
function recursionOption(option, tier) {
  return option.map(item => {
    let newItem = { ...item };
    if (newItem.tier >= tier) {
      newItem.disabled = true;
    }
    if (Array.isArray(newItem.children) && newItem.children.length > 0) {
      newItem.children = recursionOption(newItem.children, tier);
    }
    return newItem;
  });
}
onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  getList();
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览设备页面基础数据管理",
    content: "设备安装位置管理",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
});
</script>
 