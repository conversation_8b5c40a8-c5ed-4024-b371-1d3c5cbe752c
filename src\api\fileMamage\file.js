import request from "@/utils/request";

// 校验文档名称
export function checkDocumentNameUnique(params) {
  return request({
    url: "/system/schoolDocument/checkDocumentNameUnique",
    method: "get",
    params,
  });
}


// 查询文档列表 /system/schoolDocument
export function getSchoolDocumentList(params) {
  return request({
    url: "/system/schoolDocument",
    method: "get",
    params,
  });
}

// 查询文档详情 /system//schoolDocument/1877985906008641537
export function getSchoolDocumentDetail(id) {
  return request({
    url: "/system/schoolDocument/" + id,
    method: "get",
  });
}

// 新增文档信息 /system//schoolDocument
export function addSchoolDocument(data) {
  return request({
    url: "/system/schoolDocument",
    method: "post",
    data,
  });
}

// 更新文档信息 /system/schoolDocument
export function editSchoolDocument(data) {
  return request({
    url: "/system/schoolDocument",
    method: "put",
    data,
  });
}

// 删除文档信息 /system/schoolDocument
export function deleteSchoolDocument(params) {
  return request({
    url: "/system/schoolDocument",
    method: "delete",
    params,
  });
}