import request from '@/utils/request'

// 查询该类型是否有设备绑定
export function checkDeviceType(id) {
    return request({
        url: `/system/deviceType/find/device/${id}`,
        method: 'get',
        id
    })
}


// 设备类型列表
export function getDeviceType(data) {
    return request({
        url: '/system/deviceType/queList',
        method: 'post',
        data
    })
}

// 设备类型分页
export function deviceTypePage2(data) {
    return request({
        url: '/system/deviceType/quePage',
        method: 'post',
        data
    })
}

// 设备类型分页 -新的
export function deviceTypePage(data) {
    return request({
        url: '/system/deviceType/find/page',
        method: 'post',
        data
    })
}

// 新增设备类型
export function deviceTypeAdd(data) {
    return request({
        url: '/system/deviceType/add',
        method: 'post',
        data
    })
}

// 修改设备类型
export function deviceTypeEdit(data) {
    return request({
        url: '/system/deviceType/edit',
        method: 'post',
        data
    })
}

// 删除设备类型
export function deviceTypeDel(data) {
    return request({
        url: '/system/deviceType/delById',
        method: 'post',
        data
    })
}