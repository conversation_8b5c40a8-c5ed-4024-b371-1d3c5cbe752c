<!-- components/emergency/RelatedPartsTable.vue -->
<template>
  <div class="related-parts">
    <div class="taskInfo-tit">关联备件</div>
    <!-- 操作区域插槽 -->
    <div v-if="$slots.operations" class="table-operations">
      <slot name="operations" />
    </div>
    <el-table :data="parts" border style="width: 100%; margin-top: 10px">
      <el-table-column prop="partId" label="备件编号" width="250" align="center"/>
      <el-table-column prop="partName" label="备件名称" width="250" align="center"/>
      <el-table-column label="消耗量" width="200" align="center">
        <template #default="scope">
          <template v-if="!readonly">
            <el-input-number 
              v-model="scope.row.quantity" 
              :min="1" 
              :max="scope.row.stock"
              @change="(value) => handleQuantityChange(scope.row, value)"
            />
          </template>
          <template v-else>
            {{ scope.row.quantity }}
          </template>
        </template>
      </el-table-column>
      <el-table-column label="剩余库存" width="200" align="center">
        <template #default="scope">
          {{ scope.row.stock }}
        </template>
      </el-table-column>
      <el-table-column prop="useDate" label="消耗日期" align="center">
        <template #default="scope">
          {{ formatDate(scope.row.useDate) }}
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" width="220" align="center">
        <template #default="scope">
          <el-button
            v-if="!readonly"
            type="danger"
            @click="handleDelete(scope.row)"
          >
            删除
          </el-button>
        </template>
      </el-table-column> -->
      <!-- 额外列插槽 -->
      <slot name="columns" />
    </el-table>
  </div>
</template>

<script setup>
const props = defineProps({
  parts: {
    type: Array,
    required: true
  },
  readonly: {
    type: Boolean,
    default: true  // 默认为只读状态
  }
})

const emit = defineEmits(['update:parts'])

// 处理数量变化
const handleQuantityChange = (row, value) => {
  const newParts = [...props.parts]
  const index = newParts.findIndex(item => item.partId === row.partId)
  if (index !== -1) {
    newParts[index].quantity = value
    emit('update:parts', newParts)
  }
}

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};
/*  */
// 处理删除
/* const handleDelete = (row) => {
  const newParts = props.parts.filter(item => item.partId !== row.partId)
  emit('update:parts', newParts)
} */
</script>

<style scoped lang="scss">
.related-parts {
  .taskInfo-tit {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0;
  }

  .table-operations {
    margin-bottom: 10px;
  }
}
</style>