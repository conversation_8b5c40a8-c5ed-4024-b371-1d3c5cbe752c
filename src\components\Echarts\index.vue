<template>
  <div
    :id="id"
    ref="chartDom"
    :className="className"
    :style="{ width, height }"
  ></div>
</template>
<script setup>
import * as echarts from "echarts";
import elementResizeDetectorMaker from "element-resize-detector";
import { onMounted, ref, onBeforeUnmount, watch } from "vue";
import getGuangZhouData from "@/api/gz.json";
import "echarts-gl";

const erd = elementResizeDetectorMaker();
const chartDom = ref("");
const yMax = ref(1);
const emit = defineEmits(["setFontSize", "getClickIdx"]);
const props = defineProps({
  id: {
    type: String,
    default: "myChart",
    required: true,
  },
  className: {
    type: String,
    default: "",
  },
  width: {
    type: String,
    default: "100%",
  },
  height: {
    type: String,
    default: "300px",
  },
  loading: {
    type: Boolean,
    default: false,
  },
  getYMax: {
    type: Boolean,
    default: false,
  },
  fullOptions: {
    type: Object,
    default: () => ({}),
    required: true,
  },
  mapConfig: {
    type: Object,
    default: () => ({
      name: "",
      data: {},
    }),
  },
});

let myChart;

const resizeHandler = () => {
  emit("setFontSize", props.id);
  myChart.resize();
};

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};

const cancelDebounce = debounce(resizeHandler, 50);

onMounted(() => {
  myChart = echarts.init(document.getElementById(props.id), {
    renderer: "svg",
  });
  if (!!props.mapConfig.name) {
    echarts.registerMap(props.mapConfig.name, props.mapConfig.data);
  }
  myChart.showLoading({
    text: "",
    color: "#409eff",
    textColor: "#000",
    maskColor: "rgba(255, 255, 255, .1)",
    zlevel: 0,
    lineWidth: 2,
  });
  if (!props.loading) {
    const opt = JSON.parse(JSON.stringify(props.fullOptions.options));
    myChart.hideLoading();
    myChart.setOption(props.fullOptions.options, true);
    myChart.getZr().on("click", (params) => {
      let pointInPixel = [params.offsetX, params.offsetY];
      let idx = opt.yAxis && opt.yAxis.type === "category" ? 1 : 0;
      if (myChart.containPixel("grid", pointInPixel)) {
        let index = myChart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[
          idx
        ];
        emit("getClickIdx", index);
      }
    });
  }
  window.addEventListener("resize", cancelDebounce);
  erd.listenTo(chartDom.value, () => {
    cancelDebounce();
  });
});

watch(
  () => [props.fullOptions.options, props.loading],
  () => {
    if (myChart && !props.loading) {
      myChart.hideLoading();
      myChart.setOption(props.fullOptions.options, true);
      if (props.getYMax) {
        const yAxisModel = myChart.getModel().getComponent("yAxis", 0);
        if (yAxisModel) {
          const axis = yAxisModel.axis;
          const [min, max] = axis.scale.getExtent();
          // console.log("自动计算的最大值:", max);
          yMax.value = max;
        }
      }
    }
  },
  {
    deep: true,
  }
);

const unMountFunc = () => {
  window.removeEventListener("resize", cancelDebounce);
  echarts.dispose(myChart);
};

const dispatchAction = (action) => {
  myChart.dispatchAction(action);
};

const getYAxisMax = () => {
  return yMax.value;
};

onBeforeUnmount(() => {
  unMountFunc();
});

defineExpose({
  getYAxisMax,
  unMountFunc,
  dispatchAction,
});
</script>