<template>
  <div class="file app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">{{ route.meta.title }}</div>
      </template>
      <div class="file-main">
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="fileNumber">
            <el-input
              v-model="queryParams.fileNumber"
              placeholder="请输入编号/名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="fileType">
            <el-select
              v-model="queryParams.fileType"
              placeholder="请选择模板类型"
              @change="handleQuery"
              clearable
              filterable
              style="width: 200px"
            >
              <el-option
                v-for="item in typeList"
                :key="item.templateTypeId"
                :label="item.templateTypeName"
                :value="item.templateTypeId"
              >
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
        <div class="mb12">
          <el-button icon="Plus" plain type="primary" @click="handleAdd"
            >新增文档</el-button
          >
        </div>

        <div class="file-main_table">
          <el-table ref="tableRef" v-loading="loading" :data="tableList" border>
            <el-table-column
              label="文档编号"
              align="center"
              prop="documentNo"
              minWidth="120px"
              show-overflow-tooltip
            />
            <el-table-column
              label="文档名称"
              align="center"
              minWidth="120px"
              prop="documentName"
              show-overflow-tooltip
            />
            <el-table-column
              label="模板名称"
              align="center"
              minWidth="120px"
              prop="templateName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.templateName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="模板类型"
              align="center"
              minWidth="120px"
              prop="templateTypeName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.templateTypeName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="文档来源"
              align="center"
              minWidth="120px"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.source || "文档管理" }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              minWidth="150px"
              prop="createdTime"
              show-overflow-tooltip
            />
            <el-table-column
              label="修改时间"
              align="center"
              minWidth="150px"
              prop="updatedTime"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.updatedTime || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="创建人"
              align="center"
              minWidth="150px"
              prop="createdByName"
              show-overflow-tooltip
            />
            <el-table-column
              label="修改人"
              align="center"
              minWidth="150px"
              prop="updatedByName"
              show-overflow-tooltip
            >
              <template #default="scope">
                {{ scope.row.updatedByName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              min-width="220"
              align="center"
              fixed="right"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleCheck(scope.row, true)"
                  >查看</el-button
                >
                <el-button
                  link
                  type="warning"
                  icon="Edit"
                  v-if="scope.row.source != '工单管理'"
                  @click.stop="handleCheck(scope.row, false)"
                  >修改</el-button
                >
                <el-button
                  link
                  type="danger"
                  icon="Delete"
                  @click.stop="handleDel(scope.row)"
                  >删除</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="getList"
          />
        </div>
      </div>

      <!-- 新增文档弹窗 -->
      <el-dialog
        class="custom-dialog"
        :title="active == 0 ? '请选择创建方式' : '请选择模板'"
        v-model="dialogVisible"
        :width="active == 0 ? 500 : 700"
        @close="handleCancel"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
      >
        <el-form
          class="search-list"
          ref="formRef"
          :model="fileInfo"
          :rules="rules"
          :inline="active == 1"
        >
          <el-form-item prop="createType" label="创建方式" v-if="active == 0">
            <el-radio-group v-model="fileInfo.createType">
              <el-radio :label="0" :value="0">选择模板</el-radio>
              <el-radio :label="1" :value="1">新建文档</el-radio>
            </el-radio-group>
          </el-form-item>
          <template v-if="fileInfo.createType == 0 && active == 1">
            <el-form-item prop="fileType" label="">
              <el-select
                v-model="fileInfo.fileType"
                placeholder="请选择模板类型"
                clearable
                filterable
                style="width: 200px"
                @change="handleQueryDialog"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.templateTypeId"
                  :label="item.templateTypeName"
                  :value="item.templateTypeId"
                >
                </el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="" prop="templateName">
              <el-input
                v-model="fileInfo.templateName"
                placeholder="请输入模板名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQueryDialog"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQueryDialog"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQueryDialog"
                >重置</el-button
              >
            </el-form-item>
          </template>
        </el-form>

        <el-table
          :data="templateList"
          ref="tempRef"
          border
          v-loading="dloading"
          max-height="300"
          v-if="active == 1"
          @row-click="rowSingleClick"
        >
          <el-table-column align="center" width="70">
            <template #default="scope">
              <el-checkbox
                v-model="scope.row.checked"
                @change="(val) => handleSingleChange(val, scope.$index)"
                @click.native.stop=""
              />
            </template>
          </el-table-column>
          <el-table-column
            label="模板编号"
            prop="templateNo"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="模板类别"
            prop="templateTypeName"
            min-width="100"
            show-overflow-tooltip
          />
          <el-table-column
            label="模板名称"
            prop="templateName"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column label="操作" min-width="100" align="center">
            <template #default="scope">
              <el-button
                link
                icon="Search"
                type="primary"
                @click.stop="handleReview(scope.row)"
                >预览</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-if="fileInfo.createType == 0 && active == 1"
          v-show="templateTotal > 0"
          :total="templateTotal"
          v-model:page="fileInfo.pageNum"
          v-model:limit="fileInfo.pageSize"
          @pagination="getTemplateList"
          layout="total,prev,pager,next"
          :background="false"
          :autoScroll="false"
        />
        <template #footer>
          <el-button @click.stop="handleCancel">取消</el-button>
          <el-button v-if="active == 1" type="primary" @click.stop="handlePrev"
            >上一步</el-button
          >
          <el-button type="primary" @click.stop="handleNext">下一步</el-button>
        </template>
      </el-dialog>

      <el-dialog
        custom-class="custom-dialog"
        :title="`${reviewTitle}的模板内容`"
        v-model="reviewDialogVisible"
        align-center
        width="1000px"
      >
        <CanvasEditor
          ref="canvasEditor"
          :parentContent="parentContent"
          :view="view"
          :key="editorKey"
          type="1"
        />
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="file">
import { useRoute, useRouter } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import CanvasEditor from "@/views/canvas-editor/index.vue";
import {
  getSchoolDocumentList,
  deleteSchoolDocument,
} from "@/api/fileMamage/file";
import { getSchoolTemplateTypeList } from "@/api/fileMamage/templateCategory";
import { getSchoolTemplateList } from "@/api/fileMamage/template";
import { debounce } from "@/utils/debounce";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const formRef = ref(null);
const { nickName, roles } = useUserStore(); // 获取当前登录的用户信息
const view = ref("parent");
const parentContent = ref(undefined);
const canvasEditor = ref(null);
const state = reactive({
  templateTotal: 0,
  editorKey: 0,
  reviewTitle: "",
  tempRef: null,
  curTemp: {},
  dialogVisible: false,
  reviewDialogVisible: false,
  total: 0,
  active: 0,
  loading: false,
  dloading: false,
  showSearch: true,
  tableList_all: [],
  fileInfo: {
    pageSize: 5,
    pageNum: 1,
    createType: 0,
    fileNumber: "",
    templateName: "",
    fileType: "",
    fileRemark: "",
    fileContent: "",
    createTime: "",
    updateTime: "",
    templateNumber: "",
  },
  tableList: [],
  templateList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    fileName: "",
    fileNumber: "",
    fileType: "",
    documentNoOrName: "",
    templateTypeId: "",
  },
  typeList: [],
  recordList: [],
});

const {
  templateTotal,
  editorKey,
  reviewTitle,
  reviewDialogVisible,
  tempRef,
  curTemp,
  active,
  templateList,
  rules,
  dialogVisible,
  typeList,
  fileInfo,
  recordList,
  tableList,
  queryParams,
  dloading,
  loading,
  total,
} = toRefs(state);

function handleSingleChange(val, index) {
  console.log(val, index, state.templateList);
  if (val) {
    clearCheck();
    state.templateList[index].checked = true;
    state.curTemp = JSON.parse(JSON.stringify(state.templateList[index]));
    console.log("check", val);
  } else {
    state.curTemp = {};
  }
}

function rowSingleClick(row) {
  const idx = state.templateList.findIndex(
    (_) => _.templateId == row.templateId
  );
  if (row.checked) {
    state.templateList[idx].checked = false;
    state.curTemp = {};
  } else {
    clearCheck();
    state.templateList[idx].checked = true;
    state.curTemp = JSON.parse(JSON.stringify(row));
  }
}

function clearCheck() {
  state.templateList = state.templateList.map((item) => {
    item.checked = false;
    return item;
  });
}

function handleReview(item) {
  reviewTitle.value = item.templateName;
  state.reviewDialogVisible = true;
  state.editorKey++;
  nextTick(() => {
    parentContent.value = JSON.parse(item.templateContent);
  });
}

function handlePrev() {
  state.active = 0;
}

function handleNext() {
  if (state.fileInfo.createType == 1) {
    router.push("/addFile");
  } else if (state.active == 0) {
    state.active = 1;
    getTemplateList();
  } else if (!state.curTemp.templateId) {
    proxy.$modal.msgWarning("请选择模板");
  } else {
    router.push(`/addFile?id=${state.curTemp.templateId}`);
  }
}

/** 列表的详情按钮 */
function handleCheck(item, type) {
  console.log(item, type);
  if (item.source == "工单管理") {
    router.push(`/fileManage/workOrderFileInfo?id=${item.documentId}`);
  } else {
    router.push(
      `/fileInfo?id=${item.documentId}${type ? "&type=1" : "&type=2"}`
    );
  }
}

function handleAdd() {
  state.fileInfo = {
    pageSize: 5,
    pageNum: 1,
    createType: 0,
    fileNumber: "",
    templateName: "",
    fileType: "",
    fileRemark: "",
    fileContent: "",
    createTime: "",
    updateTime: "",
    templateNumber: "",
  };

  state.dialogVisible = true;
}

function handleDel(item) {
  proxy.$modal
    .confirm(`确定要删除编号为${item.documentNo}的文档信息？`)
    .then(() => {
      deleteSchoolDocument({ documentId: item.documentId }).then((resp) => {
        proxy.$modal.msgSuccess("操作成功");
        getList();
      });
    });
}

/** 弹窗的取消按钮 */
function handleCancel() {
  formRef.value.resetFields();
  state.dialogVisible = false;
  state.active = 0;
}

const handleQuery = debounce(function () {
  state.queryParams.pageNum = 1;
  getList();
}, 200);

const resetQuery = debounce(function () {
  proxy.resetForm("queryRef");
  state.queryParams.documentNoOrName = "";
  state.queryParams.templateTypeId = "";
  state.queryParams.pageSize = 10;
  handleQuery();
}, 200);

function getList() {
  state.loading = true;
  const params = {
    pageNum: state.queryParams.pageNum,
    pageSize: state.queryParams.pageSize,
    documentNoOrName: state.queryParams.fileNumber,
    templateTypeId: state.queryParams.fileType,
  };
  console.log(params, "传递的参数");

  getSchoolDocumentList(params)
    .then((res) => {
      if (res.code === 200) {
        console.log(res, "文档列表数据");
        state.tableList = res.data.rows || [];
        state.total = res.data.total;
      } else {
        proxy.$modal.msgError(res.msg);
      }
    })
    .catch((err) => {
      console.error("获取文档列表失败:", err);
      proxy.$modal.msgError("获取文档列表失败");
    })
    .finally(() => {
      state.loading = false;
    });
}

// 获取模板类型列表
const getTemplateTypeList = () => {
  getSchoolTemplateTypeList()
    .then((res) => {
      if (res.code === 200) {
        state.typeList = res.data.rows || [];
        if (
          state.typeList.length > 0 &&
          (!state.typeList[0].templateTypeId ||
            !state.typeList[0].templateTypeName)
        ) {
          console.warn(
            "API返回的数据结构可能不正确，请检查templateTypeId和templateTypeName字段"
          );
        }
      } else {
        proxy.$modal.msgError(res.msg);
      }
    })
    .catch((err) => {
      console.error("获取模板类型列表失败:", err);
      proxy.$modal.msgError("获取模板类型列表失败");
    });
};

// Add this new function to get template list
const getTemplateList = () => {
  const params = {
    pageNum: state.fileInfo.pageNum,
    pageSize: state.fileInfo.pageSize,
    templateName: state.fileInfo.templateName,
    templateTypeId: state.fileInfo.fileType,
  };

  dloading.value = true;
  getSchoolTemplateList(params)
    .then((res) => {
      dloading.value = false;
      console.log("模板列表", res);
      if (res.data) {
        state.templateList =
          res.data.rows?.map((item) => {
            item.checked = state.curTemp.templateId == item.templateId;
            return item;
          }) || [];
        state.templateTotal = res.data.total;
      } else {
        proxy.$modal.msgError(res.msg);
      }
    })
    .catch((err) => {
      console.error("获取模板列表失败:", err);
      dloading.value = false;
      proxy.$modal.msgError("获取模板列表失败");
    });
};

// Add these functions for dialog search functionality
const handleQueryDialog = debounce(function () {
  state.fileInfo.pageNum = 1;
  getTemplateList();
}, 200);

const resetQueryDialog = debounce(function () {
  state.fileInfo.templateName = "";
  state.fileInfo.fileType = "";
  state.fileInfo.pageNum = 1;
  state.curTemp = {};
  getTemplateList();
}, 200);

onMounted(() => {
  getList();
  getTemplateTypeList();
});
</script>
