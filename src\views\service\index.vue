<template>
  <div class="service">
    <div class="service-title">服务台</div>
    <div class="service-header">
      <div class="service-header_left">
        <div class="service-header_left-list1">
          <div
            class="service-header_left-item"
            v-for="item in leftList"
            :key="item.name"
            @click="handleDialog(item.name)"
          >
            <img :src="item.icon" />
            {{ item.name }}
          </div>
        </div>
        <div class="service-header_left-list2">
          <div
            class="service-header_left-item"
            v-for="item in rightList"
            :key="item.name"
            @click="goPage(item.path)"
          >
            <img :src="item.icon" />
            {{ item.name }}
          </div>
        </div>
      </div>
      <div class="service-header_right">
        <div class="service-subTitle jrtq">今日天气</div>
        <div>
          <img :src="weather.img" />
          <div>
            天气：{{ weather.info }}<br /><span
              >{{ week[new Date().getDay()] }}
              {{ timeFormat(nowTime, "hh:MM:ss") }}</span
            >
          </div>
        </div>
      </div>
    </div>
    <div class="service-main">
      <div class="service-main_one">
        <div class="service-subTitle gywd">关于我的</div>
        <div class="service-main_one-top">
          据统计，您已使用维护服务平台{{ aboutMe.useDay }}天。
        </div>
        <div class="service-main_one-center">
          共报障{{ aboutMe.repairNum }}次，其中您的报障偏好为：
          <Echarts
            id="repair"
            :fullOptions="repairOption"
            :loading="false"
            width="100%"
            height="10vw"
          />
        </div>
        <div class="service-main_one-bottom">
          共计对我们的服务做出评价{{
            aboutMe.commentNum
          }}次，<br />总体评价满意度为为：
          <Echarts
            id="satisfaction"
            :fullOptions="satisfactionOption"
            :loading="false"
            width="100%"
            height="10vw"
          />
        </div>
      </div>
      <div class="service-main_two">
        <div class="service-subTitle wtjdgd">我提交的工单</div>
        <el-table
          :data="repairList"
          v-loading="loading"
          border
          min-height="380"
        >
          <el-table-column
            label="工单编号"
            prop="workOrderCode"
            show-overflow-tooltip
            align="center"
            min-width="80"
          />
          <el-table-column
            label="工单来源"
            prop="resource"
            show-overflow-tooltip
            align="center"
            min-width="80"
          />
          <el-table-column
            label="设备编号"
            prop="deviceCode"
            show-overflow-tooltip
            align="center"
            min-width="80"
          >
            <template #default="{ row }">
              {{ row.deviceCode || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="设备名称"
            prop="deviceName"
            show-overflow-tooltip
            align="center"
            min-width="100"
          />
          <el-table-column
            label="安装位置"
            prop="installAddress"
            show-overflow-tooltip
            align="center"
            min-width="100"
          >
            <template #default="{ row }">
              {{ row.installAddress || row.installAddressStr || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="提交时间"
            prop="submittedTime"
            show-overflow-tooltip
            align="center"
            min-width="100"
          />
          <el-table-column
            label="工单状态"
            align="center"
            show-overflow-tooltip
            min-width="85px"
          >
            <template #default="{ row }">
              <el-tag :type="statusObj[row.troubleStatus]?.type">{{
                statusObj[row.troubleStatus]?.label
              }}</el-tag>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getRepairList"
          style="padding: 0 !important"
          layout="total,prev,pager,next"
          :autoScroll="false"
        />
      </div>
      <div class="service-main_three">
        <div class="xxtx">
          <div class="service-subTitle_left">
            消息提醒<span class="msgBadge" v-if="msgList.length > 0">{{
              msgList.length > 99 ? "99+" : msgList.length
            }}</span>
          </div>
          <div class="msgTip" v-if="msgList.length > 0">
            您有<span>{{ msgList.length > 99 ? "99+" : msgList.length }}</span>
            条新消息提醒，可点击查看
          </div>
        </div>
        <div
          v-if="msgList.length < 1"
          style="text-align: center; margin-top: 200px"
        >
          暂无消息
        </div>
        <div
          class="service-main_three-list"
          v-if="msgList.length > 0 && !isMore"
        >
          <div
            class="service-main_three-list_item"
            v-for="(item, index) in msgList.slice(0, 7)"
            :key="index"
            @click="handleMsg(item)"
          >
            <div class="info">{{ item.msgTitle }}</div>
            <div class="time">{{ item.createTime }}</div>
          </div>
        </div>
        <div
          class="service-main_three-more"
          v-if="msgList.length > 7 && !isMore"
          @click="isMore = true"
        >
          查看更多
        </div>
        <el-scrollbar max-height="380" v-if="isMore && msgList.length > 7">
          <div class="service-main_three-list">
            <div
              class="service-main_three-list_item"
              v-for="(item, index) in msgList"
              :key="index"
              @click="handleMsg(item)"
            >
              <div class="info">{{ item.msgTitle }}</div>
              <div class="time">{{ item.createTime }}</div>
            </div>
          </div>
        </el-scrollbar>
      </div>
    </div>

    <el-dialog
      :title="title"
      v-model="dialogVisible"
      :align-center="title == '电话报障' ? false : true"
      :width="title == '电话报障' ? 800 : 600"
    >
      <div v-if="title == '电话报障'" class="repair">
        <div class="repair-search"></div>
        <el-table :data="dialogList" border>
          <el-table-column label="单位编号" prop="number" />
          <el-table-column label="承建单位" prop="name" />
          <el-table-column label="报障电话">
            <template #default="scope">
              {{ scope.row.phone }}
              <span
                style="
                  display: inline-block;
                  padding-left: 20px;
                  color: #4095e5;
                  cursor: pointer;
                "
                @click="handleCopy(scope.row)"
                >一键复制</span
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="dialogList.length > 0"
          :total="dialogList.length"
          v-model:page="queryParams.current"
          v-model:limit="queryParams.size"
          @pagination="handleQuery"
        />
      </div>
      <div v-if="title == '小程序报障'" class="minipro">
        请通过扫描二维码登录小程序，进行报障
        <img style="width: 10vw; height: 10vw" :src="getCodeUrl()" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup name="service">
import { timeFormat, sendPointRequest } from "@/utils";
import {
  getCurrentInstance,
  onBeforeUnmount,
  onMounted,
  reactive,
  toRefs,
} from "vue";
import { serviceStatistics, repairPage } from "@/api/service";
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import { getMsg, readMsg } from "@/api/park";
import { schoolPlanWorkOrderList } from "@/api/emergency";
import { isIPv4 } from "@/utils";

const { proxy } = getCurrentInstance();
const router = useRouter();
const state = reactive({
  codeUrlObj: {
    "maintainapp.gzwinteam.com": `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/minicode_pro.jpg`,
    "maintainapptest.gzwinteam.com": `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/minicode_test.jpg`,
    "maintainappdemo.gzwinteam.com": `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/minicode_demo.jpg`,
    "maintainapppre.gzwinteam.com": `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/minicode_pre.jpg`,
  },
  isMore: false,
  timer: null,
  stayTimer: null,
  stayTime: 0,
  nowTime: new Date().getTime(),
  week: ["星期日", "星期一", "星期二", "星期三", "星期四", "星期五", "星期六"],
  aboutMe: {
    useDay: 1,
    repairNum: 0,
    commentNum: 0,
  },
  title: "小程序报障",
  dialogVisible: false,
  loading: false,
  total: 0,
  repairList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 8,
  },
  weather: {
    img: "",
    info: "晴",
  },
  leftList: [
    {
      name: "电话报障",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/dhbz.png",
    },
    {
      name: "小程序报障",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/xcxbz.png",
    },
  ],
  rightList: [
    {
      path: "/satisfaction",
      name: "满意度评价",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/mydpj.png",
    },
    {
      path: "/recording",
      name: "电话录音查询",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/dhlycx.png",
    },
    {
      path: "/visit",
      name: "工单回访",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/gzthf.png",
    },
  ],
  msgList: [],
  dialogList: [],
  statusObj: {
    0: {
      label: "待认领",
      type: "primary",
    },
    1: {
      label: "待处理",
      type: "danger",
    },
    2: {
      label: "待评价",
      type: "warning",
    },
    3: {
      label: "已完成",
      type: "success",
    },
    4: {
      label: "已挂起",
      type: "info",
    },
    5: {
      label: "待审核",
      type: "info",
    },
  },
  msgObj: {
    1: "/deviceLedger/ledger",
    2: "/taskManage/taskCenter/todo",
    3: "/taskManage/taskCenter/handled",
    4: "/taskManage/taskCenter/todo",
    5: "/taskManage/taskCenter/handled",
    6: "/emergencyManage/TaskCenter",
  },
});

const {
  codeUrlObj,
  isMore,
  msgObj,
  statusObj,
  loading,
  timer,
  stayTimer,
  stayTime,
  nowTime,
  repairList,
  week,
  aboutMe,
  title,
  total,
  dialogVisible,
  dialogList,
  queryParams,
  weather,
  leftList,
  rightList,
  msgList,
} = toRefs(state);

// 报障偏好配置
const repairOption = ref({
  options: {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}",
    },
    series: [
      {
        name: "报障偏好",
        type: "pie",
        radius: ["35%", "55%"],
        center: ["50%", "55%"],
        color: ["#60c1de", "#3cb27d", "#4e7aee", "#fac858"],
        label: {
          show: true,
          position: "outside",
          formatter: "{b}\n{c}",
          color: "#000",
          fontSize: 12,
          lineHeight: 20,
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 10,
          lineStyle: {
            color: "#000",
          },
        },
        data: [
          { value: 0, name: "小程序报障" },
          { value: 0, name: "电话报障" },
          { value: 0, name: "管理员主动报障" },
        ],
      },
    ],
  },
});

// 满意度配置
const satisfactionOption = ref({
  options: {
    tooltip: {},
    radar: {
      center: ["50%", "60%"],
      radius: "70%",
      nameGap: 10,
      indicator: [
        { name: "处理速度", max: 5 },
        // { name: "解决能力", max: 5 },
        { name: "技术能力", max: 5 },
        // { name: "回访情况", max: 5 },
        { name: "服务态度", max: 5 },
      ],
    },
    series: [
      {
        name: "总体评价满意度",
        type: "radar",
        color: ["#60c1de", "#3cb27d", "#4e7aee", "#fac858"],
        label: {},
        labelLine: {},
        data: [{ value: [0, 0, 0, 0, 0], name: "总体评价满意度" }],
      },
    ],
  },
});

function getCodeUrl() {
  let name = window.location.hostname
  if (import.meta.env.VITE_APP_BASE_API == "/djg-prod-api")
    return `https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/minicode.jpg`
  if (!isIPv4(name)) {
    return (
      codeUrlObj.value[name] || codeUrlObj.value["maintainapp.gzwinteam.com"]
    );
  }
  return codeUrlObj.value["maintainapp.gzwinteam.com"];
}

function handleQuery() {}

function handleMsg(item) {
  console.log("handleMsg ==>", item);
  sendPointRequest({
    event: "Click",
    eventDescribe: "点击消息提醒",
    content: "",
    num: 1,
  });
  if (item.msgType === 6) {
    // 处理应急工单消息
    handleEmergencyMsg(item);
  } else {
    // 处理其他类型消息
    readMsg({ msgId: item.msgId }).then((res) => {
      console.log("readMsg ==>", res);
      if (res.code == 200) {
        getMsgList();
        router.push(`${msgObj.value[item.msgType]}`);
      }
    });
  }
}

// 处理应急工单消息
function handleEmergencyMsg(item) {
  // 从消息标题中提取应急工单编号 - 优化正则表达式以匹配实际格式
  const codeRegex = /应急工单编号([A-Za-z0-9]+)未归档/;
  const match = item.msgTitle.match(codeRegex);

  if (match && match[1]) {
    const workOrderCode = match[1];

    // 调用接口获取工单ID
    proxy.$modal.loading("正在查询工单信息...");

    // 构建查询参数
    const params = {
      pageNum: 1,
      pageSize: 10,
      workOrderCode: workOrderCode,
    };

    console.log("查询应急工单参数:", params);

    schoolPlanWorkOrderList(params)
      .then((res) => {
        console.log("查询应急工单结果:", res);
        proxy.$modal.closeLoading();

        if (res.code === 200) {
          if (res.data && res.data.records && res.data.records.length > 0) {
            // 从records数组中获取第一个元素
            const workOrderId = res.data.records[0].id;
            console.log("找到应急工单ID:", workOrderId);

            // 标记消息为已读
            readMsg({ msgId: item.msgId }).then((readRes) => {
              if (readRes.code == 200) {
                getMsgList();
                // 跳转到工单归档页
                router.push(`/emergencyManage/archive?id=${workOrderId}`);
              }
            });
          } else {
            // 如果未找到工单，仍然标记为已读并跳转到工单中心
            readMsg({ msgId: item.msgId }).then((res) => {
              if (res.code == 200) {
                getMsgList();
                router.push(`/emergencyManage/TaskCenter`);
              }
            });
          }
        } else {
        }
      })
      .catch((error) => {});
  } else {
    // 如果无法提取编号，仍然标记为已读并跳转到默认页面
    readMsg({ msgId: item.msgId }).then((res) => {
      if (res.code == 200) {
        getMsgList();
        router.push(`${msgObj.value[6]}`);
      }
    });
  }
}

const handleDialog = (name) => {
  state.title = name;
  if (name == "小程序报障") {
    state.dialogVisible = true;
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击小程序报障",
      content: "",
      num: 1,
    });
  } else {
    proxy.$modal.msgWarning("该模块正在开发中");
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击电话报障",
      content: "",
      num: 1,
    });
  }
};

const goPage = (path) => {
  if (path == "/satisfaction") {
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击满意度评价",
      content: "",
      num: 1,
    });
  } else if (path == "/recording") {
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击电话录音查询",
      content: "",
      num: 1,
    });
  } else if (path == "/visit") {
    sendPointRequest({
      event: "Click",
      eventDescribe: "点击工单回访",
      content: "",
      num: 1,
    });
  }
  router.push(path);
};

const getData = () => {
  serviceStatistics().then((resp) => {
    console.log("服务台统计", resp);
    if (resp.data) {
      const { joinDay, reportPreferences = {}, satisfaction = {} } = resp.data;
      state.aboutMe = {
        useDay: joinDay || 0,
        repairNum: reportPreferences.count || 0,
        commentNum: satisfaction.evaluationCount || 0,
      };

      const { admin, phone, app } = reportPreferences;
      repairOption.value.options.series[0].data[0].value = app || 0;
      repairOption.value.options.series[0].data[1].value = phone || 0;
      repairOption.value.options.series[0].data[2].value = admin || 0;

      const {
        processingSpeed,
        serviceAttitude,
        technicalAbility,
        followUpStatus,
        feedback,
        resolved,
      } = satisfaction;
      satisfactionOption.value.options.series[0].data[0].value = [
        processingSpeed > 5 ? 5 : processingSpeed || 0,
        // resolved > 5 ? 5 : resolved || 0,
        technicalAbility > 5 ? 5 : technicalAbility || 0,
        // feedback > 5 ? 5 : feedback || 0,
        serviceAttitude > 5 ? 5 : serviceAttitude || 0,
      ];
    }
  });
};

const getRepairList = () => {
  state.loading = true;
  repairPage(state.queryParams)
    .then((resp) => {
      console.log("工单列表", resp);
      if (resp.data) {
        const { total = 0, repairListVOList = [] } = resp.data;
        state.total = total;
        state.repairList = repairListVOList.reduce((res, cur) => {
          res.push({
            ...cur,
            installAddressStr: cur.deviceContent
              ? JSON.parse(cur.deviceContent).installAddress || "-"
              : "-",
          });
          return res;
        }, []);
        state.loading = false;
      }
    })
    .catch(() => (state.loading = false));
};

const getMsgList = () => {
  getMsg().then((resp) => {
    console.log("消息列表", resp);
    if (resp.data) {
      state.msgList = resp.data || [];
    }
  });
};

const getTime = () => {
  state.timer = setInterval(() => {
    state.nowTime = new Date().getTime();
  }, 1000);
};

const startStayTimer = () => {
  state.stayTimer = setInterval(() => {
    state.stayTime++;
  }, 1000);
};

onMounted(() => {
  getData();
  getRepairList();
  getMsgList();
  getTime();
  startStayTimer();
});

onBeforeUnmount(() => {
  clearInterval(state.timer);
  clearInterval(state.stayTimer);

  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览服务台",
    content: "",
    num: state.stayTime,
  });
});
</script>

<style lang="scss" scoped>
.service {
  min-height: 670px;
  padding: 20px;
  background-color: #f3f3f3;
  &-title {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    font-size: 24px;
    color: #4095e5;
    font-weight: bold;
    gap: 0 10px;
    &::before {
      content: "";
      display: inline-block;
      width: 40px;
      height: 40px;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-title.png");
      background-size: 100% 100%;
    }
  }
  &-subTitle {
    display: flex;
    align-items: center;
    gap: 0 10px;
    font-size: 16px;
    font-weight: bold;
    margin-bottom: 10px;
    &::before {
      width: 25px;
      height: 25px;
      content: "";
      display: inline-block;
    }
    &.jrtq::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-title_icon1.png");
      background-size: 100% 100%;
    }
    &.gywd::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-title_icon2.png");
      background-size: 100% 100%;
    }
    &.wtjdgd::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-title_icon3.png");
      background-size: 100% 100%;
    }
    &_left {
      display: flex;
      align-items: center;
      gap: 0 10px;
      font-size: 16px;
      font-weight: bold;
      &::before {
        width: 25px;
        height: 25px;
        content: "";
        display: inline-block;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-title_icon4.png");
        background-size: 100% 100%;
      }

      .msgBadge {
        font-size: 12px;
        font-weight: bold;
        color: #ff3b30;
        padding: 2px 5px;
        border-radius: 15px;
        background-color: #ffd8d6;
      }
    }
  }
  .xxtx {
    display: flex;
    flex-direction: column;
    gap: 10px;
    // justify-content: space-between;
    // align-items: center;
    .msgTip {
      font-size: 12px;
      font-weight: normal;
      cursor: pointer;
      span {
        font-weight: bold;
        color: #ff3b30;
      }
    }
  }
  &-header {
    display: flex;
    gap: 0 1vw;
    &_left {
      width: 75%;
      padding: 0.5vw 0.8vw;
      background-color: #fff;
      display: flex;
      border-radius: 5px;
      &-list1,
      &-list2 {
        display: flex;
        align-items: center;
        gap: 0 1.5vw;
        font-size: 1vw;
        &::before {
          width: 1.8vw;
          padding: 0.4vw 0;
          text-align: center;
          background-color: #4095e5;
          color: #fff;
          border-radius: 3px;
        }
      }
      &-list1 {
        padding-right: 3vw;
        border-right: 1px solid #ddd;
        &::before {
          content: "报障功能";
        }
      }
      &-list2 {
        padding-left: 3vw;
        &::before {
          content: "其它功能";
        }
      }
      &-item {
        display: flex;
        align-items: center;
        gap: 0 0.5vw;
        background-color: #f3f3f3;
        padding: 0.6vw 1vw;
        border-radius: 4px;
        letter-spacing: 0.1vw;
        cursor: pointer;
        white-space: nowrap;
        img {
          width: 2.7vw;
          height: 2.7vw;
        }
      }
    }
    &_right {
      background-color: #fff;
      width: 25%;
      border-radius: 5px;
      padding: 1vw;
    }
  }
  &-main {
    display: flex;
    gap: 0 1vw;
    margin-top: 1vw;
    min-height: 460px;
    &_one {
      background-color: #fff;
      padding: 0.7vw;
      width: 26vw;
      // flex: 0.85;
      border-radius: 5px;
      color: #4095e5;
      &-top {
        font-size: 0.9vw;
        position: relative;
        height: 60px;
        display: flex;
        align-items: center;
        padding: 0 10px 0 70px;
        letter-spacing: 0.3px;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-bg.png");
        background-size: 100% 100%;
        &::before {
          position: absolute;
          left: 0;
          top: 0;
          content: "";
          display: inline-block;
          width: 60px;
          height: 60px;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-banner.png");
          background-size: 100% 100%;
        }
      }
      &-center {
        margin-top: 15px;
      }
    }
    &_two {
      background-color: #fff;
      padding: 10px;
      // flex: 1.6;
      width: 49vw;
      border-radius: 5px;
    }
    &_three {
      background-color: #fff;
      padding: 10px;
      // flex: 0.95;
      width: 25vw;
      border-radius: 5px;
      &-list {
        padding: 10px 0;
        margin-top: 8px;
        border-top: 1px solid #ddd;
        // border-bottom: 1px solid #ddd;
        display: flex;
        flex-direction: column;
        gap: 12px 0;
        &_item {
          font-size: 12px;
          display: flex;
          justify-content: space-between;
          align-items: center;
          background-color: #f3f3f3;
          padding: 5px 18px;
          border-radius: 4px;
          position: relative;
          height: 40px;
          cursor: pointer;
          .info {
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-box-orient: vertical;
            line-clamp: 2;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            // min-width: 2vw;
          }
          .time {
            white-space: nowrap;
            margin-left: 15px;
          }
          &::before {
            position: absolute;
            content: "";
            display: inline-block;
            left: 6px;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background-color: #ff3b30;
          }
          &::after {
            position: absolute;
            content: "";
            display: inline-block;
            right: 6px;
            width: 5px;
            height: 10px;
            border-radius: 50%;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/service-arrow_right.png");
            background-size: 100% 100%;
          }
        }
      }
      &-more {
        text-align: center;
        padding: 10px;
        color: #4095e5;
        border-top: 1px solid #ddd;
        cursor: pointer;
      }
    }
  }

  .minipro {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
  }
}
</style>
