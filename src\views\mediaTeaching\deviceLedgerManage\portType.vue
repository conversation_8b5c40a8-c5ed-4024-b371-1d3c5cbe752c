<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <div>
            <el-button v-if="route.query.type" @click="handleBack"
              >返回工作台</el-button
            >
          </div>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="portCodeOrName">
          <el-input
            v-model="queryParams.portCodeOrName"
            placeholder="请输入端口类别编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建端口类别</el-button
          >
        </el-col>
      </el-row>

      <el-table v-loading="loading" :data="tableList" border>
        <el-table-column
          prop="portCode"
          label="端口类别编号"
          width="150"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="端口类别名称"
          align="center"
          minWidth="120px"
          prop="name"
          show-overflow-tooltip
        />
        <el-table-column
          label="备注"
          align="center"
          minWidth="90px"
          prop="remark"
          show-overflow-tooltip
        />
        <el-table-column
          label="操作"
          min-width="150"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope)"
              >修改</el-button
            >
            <el-button
              link
              v-if="scope.row.id != 1"
              type="danger"
              icon="Delete"
              @click="handleDelete(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改类别对话框 -->
    <el-dialog
      class="custom-dialog"
      title="新建端口类别"
      v-model="open"
      width="450px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="portRef" :model="form" :rules="rules">
        <el-form-item label="端口类别编号" prop="portCode" v-if="!!form.id">
          <el-input
            v-model="form.portCode"
            placeholder="-"
            readonly
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="端口类别名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入端口类别名称"
            style="width: 100%"
            :readonly="form.id == 1"
            maxlength="20"
          />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            placeholder="请输入备注"
            style="width: 100%"
            maxlength="50"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="typeManage">
import { useRoute } from "vue-router";
import { tranListToTreeData, timeFormat, compare } from "@/utils";
import {
  addSchoolAssetsPortType,
  schoolAssetsPortTypeList,
  updateSchoolAssetsPortType,
  delSchoolAssetsPortType,
} from "@/api/mediaTeach/assets";

const route = useRoute();
const { proxy } = getCurrentInstance();

const state = reactive({
  open: false,
  loading: false,
  readonly: false,
  title: '',
  total: 0,
  form: {
    portCode: '',
    name: "",
    reamrk: "",
  },
  tableList_all: [],
  tableList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    portCodeOrName: "",
  },
  rules: {
    name: [
      { required: true, message: "端口类别名称不能为空", trigger: "blur" },
      { max: 20, message: "端口类别名称最多输入20个字符", trigger: "blur" },
    ],
  },
});

const { loading, title, readonly, open, total, levelList, tableList, queryParams, form, rules } = toRefs(state);

/** 查询类别列表 */
function getList() {
  state.loading = true;
  schoolAssetsPortTypeList(state.queryParams).then(response => {
    if (response.code == 200) {
      console.log(response)
      const { records, total } = response.data;
      state.tableList = records;
      state.total = total;
      state.loading = false;
    }
  })
}

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

/** 表单重置 */
function reset() {
  state.form = {
    portCode: "",
    name: "",
    remark: "",
  };
  proxy.resetForm("portRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  title.value = "新增类别";
  open.value = true;
}

/** 修改按钮操作 */
function handleUpdate({ row, $index }) {
  reset();
  title.value = "编辑类别";
  state.form = JSON.parse(JSON.stringify(row));
  open.value = true;
}

/** 查看按钮操作 */
function handleCheck(row) {
  reset();
  title.value = "查看类别";
  readonly.value = true;
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["portRef"].validate((valid) => {
    if (valid) {
      if (!!state.form.id) {
        updateSchoolAssetsPortType(state.form).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.open = false;
        });
      } else {
        addSchoolAssetsPortType(state.form).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          state.open = false;
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete({ row, $index }) {
  proxy.$modal
    .confirm(`确定要删除名称为${row.name}的端口信息？`)
    .then(async function () {
      await delSchoolAssetsPortType(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

getList();
</script>