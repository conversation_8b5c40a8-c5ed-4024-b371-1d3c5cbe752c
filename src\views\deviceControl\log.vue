<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <!-- <el-button @click="handleBack">返回{{ route.query.type ? '工作' : '服务' }}台</el-button> -->
        </div>
      </template>

      <div class="view-switch">
        <el-radio-group v-model="queryParams.logType" @change="changeType">
          <el-radio-button value="1" label="operation"
            >操作日志</el-radio-button
          >
          <el-radio-button value="2" label="message"
            >发布消息记录</el-radio-button
          >
          <el-radio-button value="3" label="device"
            >设备集控记录</el-radio-button
          >
          <el-radio-button value="5" label="message"
            >设备使用管理</el-radio-button
          >
          <el-radio-button value="4" label="device"
            >设备解锁操作记录</el-radio-button
          >
        </el-radio-group>
      </div>

      <!-- 操作日志内容 -->
      <div v-if="queryParams.logType != '5'" class="recording-main">
        <el-form
          class="search-list"
          :model="queryParams"
          ref="queryRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="createdUserName">
            <el-input
              v-model="queryParams.createdUserName"
              placeholder="请输入操作人"
              style="width: 200px"
              clearable
              @keyup.enter="handleQuery"
              @clear="handleQuery"
            />
          </el-form-item>
          <el-form-item
            label=""
            prop="deviceCode"
            v-if="queryParams.logType == 4"
          >
            <el-input
              v-model="queryParams.deviceCode"
              placeholder="请输入设备编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQueryOff"
            />
          </el-form-item>
          <el-form-item
            label=""
            prop="addressStr"
            v-if="queryParams.logType == 4"
          >
            <el-tree-select
              v-model="queryParams.addressStr"
              :props="positionProps"
              :data="positionTreeList"
              placeholder="请选择安装位置"
              check-strictly
              :render-after-expand="false"
              style="width: 200px"
              clearable
              @change="
                (val) => {
                  handleQueryPosition(val, 2);
                }
              "
            />
          </el-form-item>
          <el-form-item
            label=""
            prop="createTimeQueryList"
            v-if="queryParams.logType != 4"
          >
            <el-date-picker
              v-model="queryParams.createTimeQueryList"
              type="daterange"
              range-separator="至"
              start-placeholder="操作开始日期"
              end-placeholder="操作结束日期"
              format="YYYY-MM-DD"
              value-format="YYYY-MM-DD"
              @change="handleQuery"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQuery"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table
          v-loading="loading"
          :data="tableList"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="createdUserName"
            label="操作人姓名"
            align="center"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType != '4'"
            prop="createdTime"
            label="操作时间"
            align="center"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType == '2'"
            label="操作类型"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.operationType || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            v-if="queryParams.logType == '4'"
            prop="createdTime"
            label="解锁时间"
            align="center"
            min-width="180"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType != '4'"
            prop="logContent"
            label="操作内容"
            align="center"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType == '3'"
            label="集控台数"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.deviceNum || 1 }}
            </template></el-table-column
          >
          <el-table-column
            v-if="queryParams.logType == '4'"
            prop="deviceCode"
            label="设备编码"
            align="center"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType == '4'"
            prop="deviceName"
            label="设备名称"
            align="center"
            min-width="150"
            show-overflow-tooltip
          />
          <el-table-column
            v-if="queryParams.logType == '4'"
            prop="installAddress"
            label="安装位置"
            align="center"
            min-width="150"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.installAddress || "-" }}</template
            >
          </el-table-column>
        </el-table>

        <el-pagination
          :total="total"
          v-model:current-page="queryParams.pageNum"
          v-model:page-size="queryParams.pageSize"
          @current-change="getList"
          layout="total,prev, pager, next"
          background
          class="pagination"
        />
      </div>

      <div v-if="queryParams.logType == '5'" class="recording-main">
        <div class="deviceUseManage-tit">设备开关机记录</div>
        <el-form
          class="search-list"
          :model="queryOffParams"
          ref="queryOffRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="deviceCode">
            <el-input
              v-model="queryOffParams.deviceCode"
              placeholder="请输入设备编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQueryOff"
            />
          </el-form-item>
          <el-form-item label="" prop="addressStr">
            <el-tree-select
              v-model="queryOffParams.addressStr"
              :props="positionProps"
              :data="positionTreeList"
              placeholder="请选择安装位置"
              check-strictly
              :render-after-expand="false"
              style="width: 200px"
              clearable
              @change="
                (val) => {
                  handleQueryPosition(val, 0);
                }
              "
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQueryOff"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQueryOff">重置</el-button>
          </el-form-item>
        </el-form>

        <el-table v-loading="loadingOff" :data="offList" border>
          <el-table-column
            type="index"
            label="序号"
            width="90"
            align="center"
          />
          <el-table-column
            label="设备编号"
            align="center"
            minWidth="120px"
            prop="deviceCodeStr"
            show-overflow-tooltip
          />
          <el-table-column
            label="安装位置"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="addressStr"
          />
          <el-table-column
            label="设备此次开机时间"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="startTimeStr"
          />
          <el-table-column
            label="设备此次关机时间"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="endTimeStr"
          />
          <el-table-column
            label="设备此次使用时长（h）"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="runTimeStr"
          />
        </el-table>

        <pagination
          v-show="totalOff > 0"
          :autoScroll="false"
          :total="totalOff"
          :pageSizes="[5, 10, 20, 30, 50]"
          v-model:page="queryOffParams.current"
          v-model:limit="queryOffParams.size"
          @pagination="getOffList"
        />

        <div class="deviceUseManage-tit" style="margin-top: 40px">
          设备USB使用记录
        </div>
        <el-form
          class="search-list"
          :model="queryUsbParams"
          ref="queryUsbRef"
          :inline="true"
          @submit.native.prevent
        >
          <el-form-item label="" prop="deviceCode">
            <el-input
              v-model="queryUsbParams.deviceCode"
              placeholder="请输入设备编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQueryUsb"
            />
          </el-form-item>
          <el-form-item label="" prop="addressStr">
            <el-tree-select
              v-model="queryUsbParams.addressStr"
              :props="positionProps"
              :data="positionTreeList"
              placeholder="请选择安装位置"
              check-strictly
              :render-after-expand="false"
              style="width: 200px"
              clearable
              @change="
                (val) => {
                  handleQueryPosition(val, 1);
                }
              "
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="Search" @click="handleQueryUsb"
              >搜索</el-button
            >
            <el-button icon="Refresh" @click="resetQueryUsb">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table v-loading="loadingUsb" :data="usbList" border>
          <el-table-column
            type="index"
            label="序号"
            width="90"
            align="center"
          />
          <el-table-column
            label="设备编码"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="deviceCodeStr"
          />
          <el-table-column
            label="安装位置"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="addressStr"
          />
          <el-table-column
            label="USB大小"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="usbSizeStr"
          />
          <el-table-column
            label="USB型号"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="usbModelStr"
          />
          <el-table-column
            label="USB序列号"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="usbSerialStr"
          />
          <el-table-column
            label="USB接入时间"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="startTimeStr"
          />
          <el-table-column
            label="USB拔出时间"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="endTimeStr"
          />
        </el-table>

        <pagination
          v-show="totalUsb > 0"
          :autoScroll="false"
          :total="totalUsb"
          :pageSizes="[5, 10, 20, 30, 50]"
          v-model:page="queryUsbParams.current"
          v-model:limit="queryUsbParams.size"
          @pagination="getUsbList"
        />
      </div>
    </el-card>
  </div>
</template>
  
  <script setup>
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onActivated,
  onDeactivated,
  onMounted,
  onBeforeUnmount,
} from "vue";
import { getSchoolDeviceLogList } from "@/api/deviceControl";
import { queUsbPage, queOffPage } from "@/api/mediaTeach/use";
import { getPositionTree } from "@/api/mediaTeach/position";
import { treeToArray, treeFindPath, sendPointRequest } from "@/utils";

const route = useRoute();
const { proxy } = getCurrentInstance();
const state = reactive({
  addTimer: null,
  addSecond: 0,
  dialogVisible: false,
  total: 0,
  totalOff: 0,
  totalUsb: 0,
  loading: false,
  tableList_all: [],
  tableList: [],
  offList: [],
  usbList: [],
  positionList: [],
  loadingOff: false,
  loadingUsb: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    logType: "1",
    deviceCode: "",
    createTimeQueryList: [],
    createTimeQueryList: null,
  },
  queryPositionResult: {
    names: [],
    ids: [],
  },
  queryOffPositionResult: {
    names: [],
    ids: [],
  },
  queryUsbPositionResult: {
    names: [],
    ids: [],
  },
  positionTreeList: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  queryOffParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
  queryUsbParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
  queryPositionResult: {
    names: [],
    ids: [],
  },
  queryOffPositionResult: {
    names: [],
    ids: [],
  },
  queryUsbPositionResult: {
    names: [],
    ids: [],
  },
  positionTreeList: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  queryOffParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
  queryUsbParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
});

const {
  addTimer,
  addSecond,
  positionList,
  totalUsb,
  totalOff,
  loadingOff,
  loadingUsb,
  offList,
  usbList,
  positionTreeList,
  queryPositionResult,
  queryOffPositionResult,
  queryUsbPositionResult,
  positionProps,
  queryOffParams,
  queryUsbParams,
  tableList,
  queryParams,
  loading,
  total,
} = toRefs(state);

function changeType(val) {
  if (val != 5) {
    handleQuery();
  } else {
    handleQueryPosition("", 0);
    handleQueryPosition("", 1);
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQueryPosition("", 2);
}

/** 搜索按钮操作 */
function handleQueryOff() {
  queryOffParams.value.current = 1;
  getOffList();
}
function handleQueryUsb() {
  queryUsbParams.value.current = 1;
  getUsbList();
}
/** 重置按钮操作 */
function resetQueryOff() {
  proxy.resetForm("queryOffRef");
  queryOffParams.value.size = 5;
  handleQueryPosition("", 0);
}
function resetQueryUsb() {
  proxy.resetForm("queryUsbRef");
  queryUsbParams.value.size = 5;
  handleQueryPosition("", 1);
}

function handleQueryPosition(val, type) {
  if (type == 0) {
    // queryOffPositionResult.value = {
    //   ids: [],
    //   names: [],
    // };
    // queryOffPositionResult.value.ids = treeFindPath(
    //   positionTreeList.value,
    //   (d) => d.id == val
    // );
    // const arr = queryOffPositionResult.value.ids;
    // for (let i = 0; i < arr.length; i++) {
    //   queryOffPositionResult.value.names[i] = positionList.value.find(
    //     (node) => node.id == arr[i]
    //   ).name;
    // }
    // console.log("queryOffPositionResult ==> ", state.queryOffPositionResult);
    // state.queryOffParams.positionIds =
    //   state.queryOffPositionResult.ids.join(",");
    state.queryOffParams.positionIds = val;
    handleQueryOff();
  }
  if (type == 1) {
    // queryUsbPositionResult.value = {
    //   ids: [],
    //   names: [],
    // };
    // queryUsbPositionResult.value.ids = treeFindPath(
    //   positionTreeList.value,
    //   (d) => d.id == val
    // );
    // const arr = queryUsbPositionResult.value.ids;
    // for (let i = 0; i < arr.length; i++) {
    //   queryUsbPositionResult.value.names[i] = positionList.value.find(
    //     (node) => node.id == arr[i]
    //   ).name;
    // }
    // console.log("queryUsbPositionResult ==> ", state.queryUsbPositionResult);
    // state.queryUsbParams.positionIds =
    //   state.queryUsbPositionResult.ids.join(",");
    state.queryUsbParams.positionIds = val;
    handleQueryUsb();
  }
  if (type == 2) {
    // queryPositionResult.value = {
    //   ids: [],
    //   names: [],
    // };
    // queryPositionResult.value.ids = treeFindPath(
    //   positionTreeList.value,
    //   (d) => d.id == val
    // );
    // const arr = queryPositionResult.value.ids;
    // for (let i = 0; i < arr.length; i++) {
    //   queryPositionResult.value.names[i] = positionList.value.find(
    //     (node) => node.id == arr[i]
    //   ).name;
    // }
    // console.log("queryPositionResult ==> ", state.queryPositionResult);
    // state.queryParams.positionId = state.queryPositionResult.ids.join(",");
    state.queryParams.positionId = val;
    handleQuery();
  }
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    positionTreeList.value = response.data;
    positionList.value = treeToArray(response.data);
    console.log("positionList ==>", positionList.value);
  });
}

/** 查询类型列表 */
function getOffList() {
  loadingOff.value = true;
  queOffPage(queryOffParams.value).then((response) => {
    console.log(queryOffParams.value, response.data);
    offList.value = response.data.records.reduce((res, cur) => {
      let { address, deviceCode, endTime, model, runTime, startTime } = cur;
      // console.log("runtime", runTime);
      res.push({
        ...cur,
        addressStr: address || "-",
        deviceCodeStr: deviceCode || "-",
        endTimeStr: endTime || "-",
        runTimeStr: runTime?.replace("-", "") || "-",
        startTimeStr: startTime || "-",
      });
      return res;
    }, []);
    totalOff.value = response.data.total;
    loadingOff.value = false;
  });
}

/** 查询类型列表 */
function getUsbList() {
  loadingUsb.value = true;
  queUsbPage(queryUsbParams.value).then((response) => {
    console.log(queryUsbParams.value, response.data);
    usbList.value = response.data.records.reduce((res, cur) => {
      let {
        address,
        deviceCode,
        endTime,
        usbModel,
        usbSize,
        usbSerial,
        startTime,
      } = cur;
      res.push({
        ...cur,
        usbModelStr: usbModel || "-",
        usbSerialStr: usbSerial == "\u001f" ? "-" : usbSerial || "-",
        usbSizeStr: usbSize || "-",
        addressStr: address || "-",
        deviceCodeStr: deviceCode || "-",
        endTimeStr: endTime || "-",
        startTimeStr: startTime || "-",
      });
      return res;
    }, []);
    totalUsb.value = response.data.total;
    loadingUsb.value = false;
  });
}

function getList() {
  //   state.tableList = JSON.parse(JSON.stringify(state.tableList_all));
  console.log(queryParams.value, "queryParams");
  loading.value = true;
  getSchoolDeviceLogList(queryParams.value)
    .then((res) => {
      loading.value = false;
      tableList.value = res.data.rows;
      total.value = res.data.total;
      console.log(res, "操作日志");
    })
    .catch((err) => {
      loading.value = false;
    });
}

onMounted(() => {
  console.log("首次进入页面");
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  getList();
  getPositionTreeList();
});

onActivated(() => {
  console.log("组件激活");
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  getList();
  getPositionTreeList();
});

onDeactivated(() => {
  console.log("组件失活");
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览操作日志页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
});

onBeforeUnmount(() => {
  console.log("页面被销毁");
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览操作日志页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
});
</script>
  
  <style lang="scss" scoped>
.deviceUseManage-tit {
  font-size: 18px;
  margin: 10px 0;
}

.view-switch {
  margin-bottom: 10px;
}

.recording-main {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.el-form-item__label) {
  color: #606266;
}

:deep(.el-table) {
  margin-top: 15px;
}
</style>
  