import { defineConfig, loadEnv } from "vite";
import path from "path";
import createVitePlugins from "./vite/plugins";
import vue from "@vitejs/plugin-vue";
import vueSetupExtend from "vite-plugin-vue-setup-extend";
// import legacy from "@vitejs/plugin-legacy";

// https://vitejs.dev/config/
export default defineConfig(({ mode, command }) => {
  const env = loadEnv(mode, process.cwd());
  const { VITE_APP_ENV } = env;
  return {
    // 部署生产环境和开发环境下的URL。
    // 默认情况下，vite 会假设你的应用是被部署在一个域名的根路径上
    // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
    base: VITE_APP_ENV === "production" ? "./" : "./",
    // plugins: createVitePlugins(env, command === "build"),
    plugins: [
      // vue(),
      vueSetupExtend,
      // 使用自定义的插件集合
      createVitePlugins(env, command === "build"),

      // 添加 legacy 插件来支持旧版浏览器
      // legacy({
      //   targets: ["defaults", "ie >= 11", "Chrome >= 60"], // 支持现代浏览器以及 IE 11 及以上版本
      //   additionalLegacyPolyfills: ["regenerator-runtime/runtime"], // 添加 polyfills
      // }),
    ],

    // build: {
    //   target: "es2015", // 设置构建目标为 ES2015
    // },
    resolve: {
      // https://cn.vitejs.dev/config/#resolve-alias
      alias: {
        // 设置路径
        "~": path.resolve(__dirname, "./"),
        // 设置别名
        "@": path.resolve(__dirname, "./src"),
      },
      // https://cn.vitejs.dev/config/#resolve-extensions
      extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"],
    },
    // vite 相关配置
    server: {
      port: 80,
      host: true,
      open: true,
      // https: true,
      proxy: {
        ws: false,
        // https://cn.vitejs.dev/config/#server-proxy
        "/demo-api": {
          target: "https://maintainappdemo.gzwinteam.com/demo-api",
          changeOrigin: true,
          secure: false,
          rewrite: (p) => p.replace(/^\/demo-api/, ""),
        },
        "/pre-api": {
          target: "https://maintainapppre.gzwinteam.com/pre-api",
          changeOrigin: true,
          secure: false,
          rewrite: (p) => p.replace(/^\/pre-api/, ""),
        },
        "/test-api": {
          // target: "https://maintainapp.gzwinteam.com/prod-api",
          target: "https://maintainapptest.gzwinteam.com/test-api",
          // target: "https://baozhang.gzwinteam.com/djg-prod-api",
          // target: "http://************/prod-api",

          changeOrigin: true,
          secure: false,
          rewrite: (p) => p.replace(/^\/test-api/, ""),
        },
        "/prod-api": {
          target:
            VITE_APP_ENV === "production"
              ? "https://maintainapp.gzwinteam.com/prod-api"
              : "/prod-api",
          // target: "https://maintainappdemo.gzwinteam.com/prod-api",
          changeOrigin: true,
          secure: false,
          rewrite: (p) => p.replace(/^\/prod-api/, ""),
        },
        // "/djg-prod-api": {
        //   // target: "https://maintainapp.gzwinteam.com/prod-api",
        //   target: "https://baozhang.gzwinteam.com/djg-prod-api",
        //   // target: "http://************/prod-api",
        //   // target: "http://10.168.4.182/prod-api",
        //   // target: "http://192.168.0.185/prod-api",
        //   changeOrigin: true,
        //   secure: false,
        //   rewrite: (p) => p.replace(/^\/djg-prod-api/, ""),
        // },
        "/oss": {
          target: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com",
          changeOrigin: true, // 允许跨域
          rewrite: (path) => path.replace(/^\/oss/, ""),
        },
      },
    },
    //fix:error:stdin>:7356:1: warning: "@charset" must be the first rule in the file
    css: {
      postcss: {
        plugins: [
          {
            postcssPlugin: "internal:charset-removal",
            AtRule: {
              charset: (atRule) => {
                if (atRule.name === "charset") {
                  atRule.remove();
                }
              },
            },
          },
        ],
      },
    },
  };
});
