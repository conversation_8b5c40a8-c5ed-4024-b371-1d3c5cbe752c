<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <div>
            <el-button v-if="route.query.type" @click="handleBack"
              >返回工作台</el-button
            >
          </div>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="assetsCode">
          <el-input
            v-model="queryParams.assetsCode"
            placeholder="请输入类别编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入类别名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建类别</el-button
          >
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tableTree"
        border
        row-key="id"
        :default-expand-all="true"
        :tree-props="{ children: 'children', hasChildren: 'hasChildren' }"
      >
        <el-table-column
          prop="assetsCode"
          label="类别编号"
          width="150"
          align="center"
          show-overflow-tooltip
        />
        <el-table-column
          label="类别名称"
          align="center"
          minWidth="120px"
          prop="name"
          show-overflow-tooltip
        />
        <el-table-column
          label="类别等级"
          align="center"
          minWidth="90px"
          prop="level"
          show-overflow-tooltip
        />
        <el-table-column
          label="状态"
          align="center"
          minWidth="100px"
          show-overflow-tooltip
        >
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.status]"
              effect="dark"
              :type="statusList[scope.row.status].type"
              >{{ statusList[scope.row.status].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="排序"
          align="center"
          minWidth="90px"
          prop="sort"
          show-overflow-tooltip
        />
        <el-table-column
          label="创建时间"
          align="center"
          minWidth="150px"
          prop="createTime"
          show-overflow-tooltip
        />
        <el-table-column
          label="上属类别"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
        <template #default="scope">
          {{ scope.row.parentCode || '无' }}
        </template>
      </el-table-column>
        <el-table-column
          label="操作"
          min-width="180"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              :icon="scope.row.id != 1 ? 'Edit' : 'Search'"
              @click="scope.row.id != 1 ? handleUpdate(scope) : handleCheck(scope)"
              >{{ scope.row.id != 1 ? '修改' : '查看' }}</el-button
            >
            <el-button
              link
              type="success"
              icon="Plus"
              @click="handleAdd(scope.row)"
              >新增</el-button
            >
            <el-button
            v-if="scope.row.id != 1"
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <!-- <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      /> -->
    </el-card>

    <!-- 添加或修改类别对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="typeRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="上级类别" prop="parentId">
          <el-tree-select
            v-model="form.parentId"
            :data="tableTree"
            :props="{ value: 'id', label: 'name', children: 'children' }"
            value-key="id"
            placeholder="选择上级类别"
            check-strictly
            clearbale
            style="width: 250px"
            :disabled="readonly"
          />
        </el-form-item>
        <el-form-item label="类别等级" prop="level">
          <el-select v-model="form.level" style="width: 250px" :disabled="readonly">
            <el-option
              v-for="(item, index) in levelList"
              :key="index"
              :label="item"
              :value="item"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="类别名称" prop="name">
          <el-input
            v-model="form.name"
            placeholder="请输入类别名称"
            style="width: 250px"
            :disabled="readonly"
          />
        </el-form-item>
        <el-form-item label="显示排序" prop="sort">
          <el-input-number v-model="form.sort" :disabled="readonly" />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status" :disabled="readonly">
            <el-radio
              v-for="item in statusList"
              :key="item.value"
              :label="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" v-if="!readonly" @click="submitForm">确定</el-button>
          <el-button @click="cancel">{{ readonly ? '返回' : '取消' }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="typeManage">
import { useRoute } from "vue-router";
import { tranListToTreeData, timeFormat, compare } from "@/utils";
import {
  addSchoolAssetsType,
  schoolAssetsTypeTree,
  updateSchoolAssetsType,
  delSchoolAssetsType,
} from "@/api/mediaTeach/assets";
import { onMounted } from "vue";

const route = useRoute();
const { proxy } = getCurrentInstance();

const state = reactive({
  open: false,
  loading: false,
  readonly: false,
  title: "",
  levelList: ["低级", "中级", "高级"],
  statusList: [
    { label: "正常", value: 0, type: "success" },
    { label: "禁用", value: 1, type: "danger" },
  ],
  form: {
    parentId: "",
    name: "",
    sort: 0,
    status: 0,
    level: "",
  },
  tableTree: [],
  queryParams: {
    pageNum: 1,
    pageSize: 999999,
    assetsCode: "",
    name: "",
  },
  rules: {
    name: [
      { required: true, message: "类别名称不能为空", trigger: "blur" },
      { max: 20, message: "类别名称最多输入20个字符", trigger: "blur" },
    ],
    level: [
      { required: true, message: '类别等级不能为空', trigger: 'change' }
    ]
  },
});

const {
  open,
  loading,
  readonly,
  title,
  levelList,
  statusList,
  tableTree,
  queryParams,
  form,
  rules,
} = toRefs(state);

/** 查询类别列表 */
function getList() {
  schoolAssetsTypeTree(state.queryParams).then(response => {
    if (response.code == 200) {
      console.log(response)
      const { tree } = response.data
      state.tableTree = tree || response.data
    }
  })
}

/** 取消按钮 */
function cancel() {
  state.open = false;
  reset();
}

/** 表单重置 */
function reset() {
  state.form = {
    parentId: "",
    name: "",
    sort: 0,
    status: 0,
    level: "",
  };
  proxy.resetForm("typeRef");
}

/** 搜索按钮操作 */
function handleQuery() {
  // queryParams.value.pageNum = 1;
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd(row) {
  reset();
  state.title = "新增类别";
  !!row ? state.form.parentId = row.id : '' 
  state.readonly = false;
  state.open = true;
}

/** 修改按钮操作 */
function handleUpdate({ row, $index }) {
  reset();
  state.readonly = false;
  state.title = "编辑类别";
  state.form = JSON.parse(JSON.stringify(row));
  state.form.parentId = state.form.parentId == '0' ? '' : state.form.parentId
  state.open = true;
}

/** 查看按钮操作 */
function handleCheck({ row, $index }) {
  reset();
  state.title = "查看类别";
  state.readonly = true;
  state.form = JSON.parse(JSON.stringify(row));
  state.form.parentId = state.form.parentId == '0' ? '' : state.form.parentId
  state.open = true;
}

/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeRef"].validate((valid) => {
    if (valid) {
      state.form.parentId = state.form.parentId || '0'
      if (!!state.form.id) {
        updateSchoolAssetsType(state.form).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.open = false;
        });
      } else {
        addSchoolAssetsType(state.form).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          state.open = false;
        });
      }
    }
  });
}

/** 删除按钮操作 */
function handleDelete({ row, $index }) {
  if (row.children && row.children.length > 0) {
    proxy.$modal.msgWarning("该类别存在子类别，不能删除");
    return;
  }
  proxy.$modal
    .confirm(`确定要删除名称为${row.name}的类别信息？`)
    .then(async function () {
      await delSchoolAssetsType(row.id);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

onMounted(() => getList())

</script>