import {
  createWebHistory,
  createWebHashHistory,
  createRouter,
} from "vue-router";
import useUserStore from "@/store/modules/user";
/* Layout */
import Layout from "@/layout";
import App from "@/layout/components/AppMain.vue";
/**
 * Note: 路由配置项
 *
 * hidden: true                     // 当设置 true 的时候该路由不会再侧边栏出现 如401，login等页面，或者如一些编辑页面/edit/1
 * alwaysShow: true                 // 当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式--如组件页面
 *                                  // 只有一个时，会将那个子路由当做根路由显示在侧边栏--如引导页面
 *                                  // 若你想不管路由下面的 children 声明的个数都显示你的根路由
 *                                  // 你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，一直显示根路由
 * redirect: noRedirect             // 当设置 noRedirect 的时候该路由在面包屑导航中不可被点击
 * name:'router-name'               // 设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
 * query: '{"id": 1, "name": "ry"}' // 访问路由的默认传递参数
 * roles: ['admin', 'common']       // 访问路由的角色权限
 * permissions: ['a:a:a', 'b:b:b']  // 访问路由的菜单权限
 * meta : {
    noCache: true                   // 如果设置为true，则不会被 <keep-alive> 缓存(默认 false)
    title: 'title'                  // 设置该路由在侧边栏和面包屑中展示的名字
    icon: 'svg-name'                // 设置该路由的图标，对应路径src/assets/icons/svg
    breadcrumb: false               // 如果设置为false，则不会在breadcrumb面包屑中显示
    activeMenu: '/system/user'      // 当路由设置了该属性，则会高亮相对应的侧边栏。
  }
 */

if (window.location.pathname.indexOf("/teacher") != -1) {
  var getFlag = () => {
    return Layout;
  };

  var getMainPage = () => {
    return import("@/views/teacher/courseware.vue");
  };
} else {
  var getFlag = () => {
    let rolesStr = useUserStore().roles.toString();
    return rolesStr.indexOf("schoolCabin") != -1 ||
      rolesStr.indexOf("districtCabin") != -1 ||
      rolesStr.indexOf("cityCabin") != -1
      ? App
      : Layout;
  };

  var getMainPage = () => {
    console.log("roles", useUserStore().roles);
    let rolesStr = useUserStore().roles.toString();
    return rolesStr.indexOf("schoolCabin") != -1 ||
      rolesStr.indexOf("districtCabin") != -1 ||
      rolesStr.indexOf("cityCabin") != -1
      ? useUserStore().isManage
        ? import("@/views/unifyManage/adminScreen2.vue")
        : import("@/views/unifyManage/appScreen3.vue")
      : rolesStr.indexOf("maintain") != -1 ||
        rolesStr.indexOf("schoolManage") != -1
      ? import("@/views/work/index.vue")
      : import("@/views/service/index.vue");
  };
}

// 公共路由
export const constantRoutes = [
  {
    path: "/redirect",
    component: Layout,
    hidden: true,
    children: [
      {
        path: "/redirect/:path(.*)",
        component: () => import("@/views/redirect/index.vue"),
      },
    ],
  },
  {
    path: "/login",
    component: () => import("@/views/login"),
    hidden: true,
  },
  {
    path: "/register",
    component: () => import("@/views/register"),
    hidden: true,
  },
  {
    path: "/:pathMatch(.*)*",
    component: () => import("@/views/error/404"),
    hidden: true,
  },
  {
    path: "/401",
    component: () => import("@/views/error/401"),
    hidden: true,
  },
  {
    path: "/user",
    component: Layout,
    hidden: true,
    redirect: "noredirect",
    children: [
      {
        path: "profile",
        component: () => import("@/views/system/user/profile/index"),
        name: "Profile",
        meta: { title: "个人中心", icon: "user" },
      },
    ],
  },
  // {
  //   path: "",
  //   redirect: "/mainPageIndex",
  //   component: async () => {
  //     return (await getFlag()) ? App : Layout;
  //   },
  //   children: [
  //     {
  //       path: "/mainPageIndex",
  //       component: getMainPage(),
  //       name: "MainPageIndex",
  //       meta: { title: "首页", icon: "build", affix: true },
  //     },
  //   ],
  // },
  {
    path: "",
    redirect: "/mainPageIndex",
    component: async () => await getFlag(),
    children: [
      {
        path: "/mainPageIndex",
        component: () => getMainPage(),
        name: "Index",
        meta: { title: "主页", icon: "dashboard", affix: true },
      },
    ],
  },
  {
    path: "/repairInfo_new",
    component: Layout,
    children: [
      {
        path: "/repairInfo_new",
        hidden: true,
        component: () => import("@/views/repair/info_new"),
        name: "Index5",
        meta: { title: "投诉详情", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/screen-test",
    component: Layout,
    children: [
      {
        path: "/screen-test",
        component: () => import("@/views/deviceControl/screen-test.vue"),
        name: "screenTest",
        meta: { title: "大屏巡检test", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/service",
    component: Layout,
    children: [
      {
        path: "/service",
        component: () => import("@/views/service/index"),
        name: "Index4",
        meta: { title: "服务台", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/courseware",
    component: Layout,
    children: [
      {
        path: "/courseware",
        component: () => import("@/views/teacher/courseware"),
        name: "Index41",
        meta: { title: "课件上传", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/visit",
    component: Layout,
    children: [
      {
        path: "/visit",
        hidden: true,
        component: () => import("@/views/service/visit"),
        name: "Index1",
        meta: { title: "工单回访", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/recording",
    component: Layout,
    children: [
      {
        path: "/recording",
        hidden: true,
        component: () => import("@/views/service/recording"),
        name: "Index2",
        meta: { title: "电话录音查询", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/satisfaction",
    component: Layout,
    children: [
      {
        path: "/satisfaction",
        hidden: true,
        component: () => import("@/views/service/satisfaction"),
        name: "Index3",
        meta: { title: "满意度评价", icon: "dashboard" },
      },
    ],
  },
  {
    path: "/appScreen",
    component: () => import("@/views/unifyManage/appScreen2"),
    hidden: true,
  },
  {
    path: "/appScreen1",
    component: () => import("@/views/unifyManage/appScreen3"),
    hidden: true,
  },
  {
    path: "/adminScreen",
    component: () => import("@/views/unifyManage/adminScreen2"),
    hidden: true,
  },
  {
    path: "/adminScreen1",
    component: () => import("@/views/unifyManage/adminScreen-test"),
    hidden: true,
  },
  // {
  //   path: "/newUniManager",
  //   component: () => import("@/views/work/newUni/unifyManage"),
  // }
  // {
  //   path: "/approval",
  //   redirect: "/approval",
  //   component: Layout,
  //   children: [
  //     {
  //       path: "/approval",
  //       component: () => import("@/views/work/approval"),
  //       name: "Index",
  //       meta: { title: "主页", icon: "dashboard", affix: true },
  //     },
  //   ],
  // },
];

// 动态路由，基于用户权限动态去加载
export const dynamicRoutes = [
  {
    path: "/system/user-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:user:edit"],
    children: [
      {
        path: "role/:userId(\\d+)",
        component: () => import("@/views/system/user/authRole"),
        name: "AuthRole",
        meta: { title: "分配角色", activeMenu: "/system/user" },
      },
    ],
  },
  {
    path: "/system/role-auth",
    component: Layout,
    hidden: true,
    permissions: ["system:role:edit"],
    children: [
      {
        path: "user/:roleId(\\d+)",
        component: () => import("@/views/system/role/authUser"),
        name: "AuthUser",
        meta: { title: "分配用户", activeMenu: "/system/role" },
      },
    ],
  },
  {
    path: "/system/dict-data",
    component: Layout,
    hidden: true,
    permissions: ["system:dict:list"],
    children: [
      {
        path: "index/:dictId(\\d+)",
        component: () => import("@/views/system/dict/data"),
        name: "Data",
        meta: { title: "字典数据", activeMenu: "/system/dict" },
      },
    ],
  },
  {
    path: "/monitor/job-log",
    component: Layout,
    hidden: true,
    permissions: ["monitor:job:list"],
    children: [
      {
        path: "index/:jobId(\\d+)",
        component: () => import("@/views/monitor/job/log"),
        name: "JobLog",
        meta: { title: "调度日志", activeMenu: "/monitor/job" },
      },
    ],
  },
  {
    path: "/tool/gen-edit",
    component: Layout,
    hidden: true,
    permissions: ["tool:gen:edit"],
    children: [
      {
        path: "index/:tableId(\\d+)",
        component: () => import("@/views/tool/gen/editTable"),
        name: "GenEdit",
        meta: { title: "修改生成配置", activeMenu: "/tool/gen" },
      },
    ],
  },
];

const router = createRouter({
  history: createWebHashHistory("/maintainAdmin/"),
  routes: constantRoutes,
  scrollBehavior(to, from, savedPosition) {
    if (savedPosition) {
      return savedPosition;
    } else {
      return { top: 0 };
    }
  },
});

export default router;
