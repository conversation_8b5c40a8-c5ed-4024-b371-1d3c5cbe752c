import request from '@/utils/request'

//设备报废
export function scrapDevice(params) {
    return request({
        url: '/system/device/scrap',
        method: 'get',
        params
    })
}

// 关联设备列表
export function deviceRelatePage(params) {
    return request({
        url: '/system/device/find/list',
        method: 'get',
        params
    })
}

// 设备关联备件使用情况
export function deviceSparePage(params) {
    return request({
        url: '/system/spareParts/use',
        method: 'get',
        params
    })
}

// 市级 app使用次数-时长
export function queAppUseNum(params) {
    return request({
        url: '/system/device/queAppUseNum',
        method: 'get',
        params
    })
}

// 导出设备编码
export function exportDeviceCode(data) {
    return request({
        url: '/system/device/exportDeviceCode',
        method: 'post',
        responseType: 'blob',
        data
    })
}

export function devicePage(data) {
    return request({
        url: '/system/device/quePage',
        method: 'post',
        data
    })
}

// 设备详情
export function deviceInfo(data) {
    return request({
        url: '/system/device/getInfo',
        method: 'post',
        data
    })
}

export function deviceAdd(data) {
    return request({
        url: '/system/device/add',
        method: 'post',
        data
    })
}

export function deviceDel(data) {
    return request({
        url: '/system/device/delId',
        method: 'post',
        data
    })
}

export function deviceEdit(data) {
    return request({
        url: '/system/device/edit',
        method: 'post',
        data
    })
}

export function deviceExport(data) {
    return request({
        url: '/system/device/export',
        method: 'post',
        responseType: 'blob',
        data
    })
}

export function deviceImport(data) {
    return request({
        url: '/system/device/importData',
        method: 'post',
        headers: {
            'Content-Type': "multipart/form-data"
        },
        data
    })
}

export function deviceRemove(data) {
    return request({
        url: '/system/device/removeIds',
        method: 'post',
        data
    })
}

export function templateDownload(data) {
    return request({
        url: '/system/device/export/model',
        method: 'post',
        responseType: 'blob',
        data
    })
}


export function templateDownload2(data) {
    return request({
        url: '/system/fileModel/downFile',
        method: 'post',
        responseType: 'blob',
        data
    })
}

// 设备恢复和彻底删除
export function recoverDevice(params) {
    return request({
      url: "/system/device/recovery",
      method: "put",
      params,
    });
}