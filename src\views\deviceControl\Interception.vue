<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>
      <el-radio-group
        v-model="active"
        style="margin-bottom: 12px"
        @change="handleChangeActive"
      >
        <el-radio-button label="弹窗拦截" :value="0" />
        <el-radio-button label="白名单" :value="1" />
      </el-radio-group>
      <div v-if="active === 0">
        <div class="flex">
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="appName">
              <el-input
                v-model="queryParams.appName"
                placeholder="请输入应用名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="interceptStatus">
              <el-select
                v-model="queryParams.interceptStatus"
                @change="handleQuery"
                placeholder="请选择拦截状态"
                clearable
                style="width: 200px"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="12" class="mb12 btn-list">
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Open"
                :disabled="checkedRows.length < 1"
                @click="handleBatch(1)"
                >批量开启拦截</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="TurnOff"
                :disabled="checkedRows.length < 1"
                @click="handleBatch(2)"
                >批量关闭拦截</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Plus"
                :disabled="checkedRows.length < 1"
                @click="handleBatch(3)"
                >批量加入白名单</el-button
              >
            </el-col>
          </el-row>
        </div>

        <el-table
          ref="tableRef"
          header-row-class-name="disable-header-checkbox"
          v-loading="loading"
          border
          :data="tableList"
          row-key="id"
          :expand-row-keys="expandRowKeys"
          :span-method="objectSpanMethod"
          @expand-change="handleExpandChange"
          @row-click="handleRowClick"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            fixed="left"
          >
            <template #default="{ row }">
              <div v-if="isRootNode(row)">
                <el-checkbox
                  v-model="row.checked"
                  :disabled="row.disabled"
                  @change="setCheckDisabled"
                  @click.stop
                />
              </div>
              <div v-else></div>
            </template>
          </el-table-column>
          <el-table-column
            class-name="flexColumn"
            label="应用名称"
            align="center"
            minWidth="200px"
          >
            <template #default="scope">
              <div class="logo">
                <img
                  v-if="scope.row.logo"
                  :src="scope.row.logo"
                  @error="handleError"
                  alt="应用图标"
                />
                {{ scope.row.name }}
              </div>
            </template>
          </el-table-column>
          <el-table-column align="center" minWidth="140px">
            <template #header>
              <div
                style="padding-left: 5px; cursor: pointer"
                @click="handleSortDate(1, 1)"
              >
                风险数量
                <span class="caret-wrapper">
                  <i
                    class="sort-caret ascending"
                    :class="queryParams.riskSort == 'ASC' ? 'active' : ''"
                  ></i>
                  <i
                    class="sort-caret descending"
                    :class="queryParams.riskSort == 'DESC' ? 'active' : ''"
                  ></i>
                </span>
              </div>
            </template>
            <template #default="scope">
              <span>疑似风险窗口{{ scope.row.riskWindowNum || 0 }}个</span>
            </template>
          </el-table-column>
          <el-table-column align="center" minWidth="120px">
            <template #header>
              <div
                style="padding-left: 5px; cursor: pointer"
                @click="handleSortDate(2, 1)"
              >
                上报次数
                <span class="caret-wrapper">
                  <i
                    class="sort-caret ascending"
                    :class="queryParams.reportSort == 'ASC' ? 'active' : ''"
                  ></i>
                  <i
                    class="sort-caret descending"
                    :class="queryParams.reportSort == 'DESC' ? 'active' : ''"
                  ></i>
                </span>
              </div>
            </template>
            <template #default="scope">
              <span>累计上报{{ scope.row.reportedNum || 0 }}次</span>
            </template>
          </el-table-column>
          <el-table-column align="center" minWidth="180px">
            <template #header>
              <div
                style="padding-left: 5px; cursor: pointer"
                @click="handleSortDate(3, 1)"
              >
                拦截次数（成功率）
                <span class="caret-wrapper">
                  <i
                    class="sort-caret ascending"
                    :class="queryParams.interceptSort == 'ASC' ? 'active' : ''"
                  ></i>
                  <i
                    class="sort-caret descending"
                    :class="queryParams.interceptSort == 'DESC' ? 'active' : ''"
                  ></i>
                </span>
              </div>
            </template>
            <template #default="scope">
              <span
                >累计拦截{{ scope.row.interceptNum || 0 }}次（{{
                  scope.row.interceptSuccessRate || 0
                }}%）</span
              >
            </template>
          </el-table-column>
          <el-table-column
            label="拦截状态"
            align="center"
            fixed="right"
            minWidth="120"
          >
            <template #default="scope">
              <div v-if="scope.row.children && scope.row.children.length > 0">
                <el-checkbox
                  v-model="scope.row.interceptStatus"
                  :true-value="1"
                  :false-value="0"
                  @click="handleCheckIntercept(scope.row)"
                  label="全选"
                />
                <div
                  style="font-size: 12px"
                  v-if="getInterceptionNum(scope.row) > 0"
                >
                  已拦截{{ getInterceptionNum(scope.row) }}个子项
                </div>
              </div>
              <div v-else>
                <el-switch
                  v-model="scope.row.interceptStatus"
                  :active-value="1"
                  :inactive-value="0"
                  @click="handleSwitch(scope.row)"
                  @click.stop
                ></el-switch>
                {{ scope.row.interceptStatus === 0 ? "关闭" : "开启" }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            minWidth="200"
            fixed="right"
            align="center"
          >
            <template #default="{ row }">
              <div v-if="isRootNode(row)">
                <el-button
                  link
                  type="success"
                  icon="Plus"
                  @click.stop="handleWhiteList(row)"
                  >加入白名单</el-button
                >
                <el-button
                  link
                  type="primary"
                  icon="Search"
                  @click.stop="handleView(row)"
                  >详情</el-button
                >
              </div>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total > 0"
          :total="total"
          v-model:page="queryParams.pageNum"
          v-model:limit="queryParams.pageSize"
          @pagination="getList"
        />
      </div>
      <div v-if="active === 1">
        <div class="flex">
          <el-form
            class="search-list"
            :model="queryParams2"
            ref="queryRef2"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="appName">
              <el-input
                v-model="queryParams2.appName"
                placeholder="请输入应用名称"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @change="handleQuery"
              />
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>
          <el-row :gutter="12" class="mb12 btn-list">
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="tableAllSelectedId.length < 1"
                @click="handleBatch(4)"
                >批量移除白名单</el-button
              >
            </el-col>
          </el-row>
        </div>
        <el-table
          ref="tableRef2"
          v-loading="loading"
          :data="tableList2"
          border
          highlight-current-row
          @row-click="rowClick"
          @selection-change="selectionChange"
          @select="onTableSelect"
          @select-all="selectSingleTableAll"
        >
          <el-table-column
            type="selection"
            width="55"
            align="center"
            fixed="left"
          />
          <el-table-column
            label="应用名称"
            align="center"
            minWidth="180px"
            prop="appName"
          />
          <el-table-column label="操作" min-width="120" align="center">
            <template #default="scope">
              <el-button
                link
                type="danger"
                icon="Delete"
                @click.stop="handleWhiteList(scope.row, 1)"
                >移除</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total2 > 0"
          :total="total2"
          v-model:page="queryParams2.pageNum"
          v-model:limit="queryParams2.pageSize"
          @pagination="getList"
        />
      </div>
    </el-card>

    <el-dialog
      v-model="open"
      title="弹窗详情"
      width="1000px"
      align-center
      @close="handleClose"
    >
      <el-table
        :data="infoList"
        v-loading="loading2"
        border
        highlight-current-row
        max-height="600"
      >
        <el-table-column label="设备名称" align="center" min-width="200px">
          <template #default="scope">
            {{ scope.row.name || "-" }}
          </template>
        </el-table-column>
        <el-table-column align="center" minWidth="120px">
          <template #header>
            <div
              style="padding-left: 5px; cursor: pointer"
              @click="handleSortDate(1)"
            >
              风险数量
              <span class="caret-wrapper">
                <i
                  class="sort-caret ascending"
                  :class="queryParams3.riskSort == 'ASC' ? 'active' : ''"
                ></i>
                <i
                  class="sort-caret descending"
                  :class="queryParams3.riskSort == 'DESC' ? 'active' : ''"
                ></i>
              </span>
            </div>
          </template>
          <template #default="scope">
            <span>疑似风险窗口{{ scope.row.riskWindowNum || 0 }}个</span>
          </template>
        </el-table-column>
        <el-table-column align="center" minWidth="120px">
          <template #header>
            <div
              style="padding-left: 5px; cursor: pointer"
              @click="handleSortDate(2)"
            >
              上报次数
              <span class="caret-wrapper">
                <i
                  class="sort-caret ascending"
                  :class="queryParams3.reportSort == 'ASC' ? 'active' : ''"
                ></i>
                <i
                  class="sort-caret descending"
                  :class="queryParams3.reportSort == 'DESC' ? 'active' : ''"
                ></i>
              </span>
            </div>
          </template>
          <template #default="scope">
            <span>累计上报{{ scope.row.reportedNum || 0 }}次</span>
          </template>
        </el-table-column>
        <el-table-column align="center" minWidth="150px">
          <template #header>
            <div
              style="padding-left: 5px; cursor: pointer"
              @click="handleSortDate(3)"
            >
              拦截次数（成功率）
              <span class="caret-wrapper">
                <i
                  class="sort-caret ascending"
                  :class="queryParams3.interceptSort == 'ASC' ? 'active' : ''"
                ></i>
                <i
                  class="sort-caret descending"
                  :class="queryParams3.interceptSort == 'DESC' ? 'active' : ''"
                ></i>
              </span>
            </div>
          </template>
          <template #default="scope">
            <span
              >累计拦截{{ scope.row.interceptNum || 0 }}次（{{
                scope.row.interceptSuccessRate || 0
              }}%）</span
            >
          </template>
        </el-table-column>
        <el-table-column
          label="弹窗日期"
          align="center"
          minWidth="160px"
          prop="createTime"
        />
      </el-table>
      <pagination
        v-show="total3 > 0"
        :total="total3"
        v-model:page="queryParams3.pageNum"
        v-model:limit="queryParams3.pageSize"
        @pagination="getDetail"
        style="padding: 0 0 20px !important"
      />
    </el-dialog>
  </div>
</template>

<script setup name="recording">
import { useRoute } from "vue-router";
import { ref, reactive, toRefs, getCurrentInstance } from "vue";
import { findIndexInObejctArr, treeFindPath, findTreeNode } from "@/utils";
import {
  getWindowInterceptInfo,
  getWindowIntercept,
  updateWindowIntercept,
  getWindowInterceptTree,
} from "@/api/deviceControl/interception.js";

const route = useRoute();
const { proxy } = getCurrentInstance();
const defaultImg =
  "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/page/processIcon.png";
const state = reactive({
  curChangeCheckedRow: {},
  initList_all: [],
  initList: [],
  expandRowKeys: [],
  open: false,
  active: 0,
  queryRef: null,
  queryRef2: null,
  tableRef: null,
  tableRef2: null,
  tableRadio: [],
  tableAllSelectedRow: [],
  tableAllSelectedId: [],
  dialogVisible: false,
  total: 0,
  total2: 0,
  total3: 0,
  loading: false,
  loading2: false,
  infoList: [],
  tableList_all: [],
  tableList: [],
  tableList_all2: [],
  tableList2: [],
  typeList: [],
  statusList: [
    { label: "关闭", value: 0, type: "danger" },
    { label: "开启", value: 1, type: "success" },
  ],
  queryParams: {
    whitelistStatus: 0,
    pageNum: 1,
    pageSize: 10,
    appName: "",
    riskSort: "",
    reportSort: "",
    interceptSort: "",
    interceptStatus: "",
  },
  queryParams2: {
    whitelistStatus: 1,
    pageNum: 1,
    pageSize: 10,
    appName: "",
  },
  queryParams3: {
    appName: "",
    interceptSort: "",
    riskSort: "",
    reportSort: "",
    pageNum: 1,
    pageSize: 10,
  },
});

const {
  initList_all,
  curChangeCheckedRow,
  initList,
  expandRowKeys,
  infoList,
  open,
  queryRef,
  queryRef2,
  active,
  total2,
  total3,
  tableRef,
  tableRef2,
  tableRadio,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableList,
  tableList_all,
  tableList2,
  tableList_all2,
  typeList,
  queryParams,
  queryParams2,
  queryParams3,
  dialogVisible,
  statusList,
  loading,
  loading2,
  total,
} = toRefs(state);

const isQuery = computed(() => {
  const { appName, interceptStatus } = queryParams.value;
  return !!appName || interceptStatus === 0 || interceptStatus === 1;
});
const checkedRows = computed(() => tableList.value.filter((_) => !!_.checked));

onMounted(() => getList());

function getList(checked = false) {
  loading.value = true;
  if (!!state.active) {
    getWindowInterceptTree(queryParams2.value)
      .then((response) => {
        console.log("白名单列表", response);
        const { records, total } = response.data;
        state.tableList2 = records || [];
        state.total2 = total || 0;
        nextTick(() => {
          state.tableList2.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item.appName) > -1) {
              state.tableRef2.toggleRowSelection(item, true);
            } else {
              state.tableRef2.toggleRowSelection(item, false);
            }
          });
        });
      })
      .finally(() => {
        state.loading = false;
      });
    getWindowInterceptTree({
      ...queryParams2.value,
      pageNum: 1,
      pageSize: 999999,
    }).then((response) => {
      const { records } = response.data;
      state.tableList_all2 = records;
    });
  } else {
    if (isQuery.value) {
      let params = { ...queryParams.value, appName: "", interceptStatus: "" };
      getWindowInterceptTree(params).then((res) => {
        const { records } = res.data;
        initList_all.value = records || [];
      });
    }
    getWindowInterceptTree(queryParams.value)
      .then((response) => {
        console.log(
          "查询传参",
          queryParams.value,
          "拦截列表",
          response,
          state.curChangeCheckedRow,
          checkedRows.value
        );
        let { records, total } = response.data;
        if (checked) {
          records.forEach((item) => {
            if (
              checkedRows.value.findIndex((_) => _.appName === item.appName) !=
              -1
            ) {
              item.checked =
                item.appName === state.curChangeCheckedRow.appName &&
                item.interceptStatus !==
                  state.curChangeCheckedRow.interceptStatus
                  ? false
                  : true;
            } else {
              item.checked = false;
            }
          });
        } else {
          records = records.map((item) => {
            item.checked = false;
            return item;
          });
        }
        state.initList = JSON.parse(JSON.stringify(records || []));
        state.tableList = records || [];
        state.total = total || 0;
        console.log(checked, checkedRows.value, "已选应用");
        nextTick(() => {
          if (isQuery.value) setExpandKeys();
          if (checked) setCheckDisabled();
        });
      })
      .finally(() => {
        state.loading = false;
      });
  }
}

const getInterceptionNum = (row) => {
  let obj = findTreeNode(
    state[isQuery.value ? "initList_all" : "initList"],
    row.id
  );
  let sum = 0;
  if (!!obj?.appName) {
    obj?.children?.map((item) => {
      if (item.children && item.children.length > 0) {
        sum +=
          item.children?.filter((_) => _.interceptStatus === 1)?.length || 0;
      } else if (item.interceptStatus === 1) {
        sum += 1;
      }
    });
  } else {
    sum = obj?.children?.filter((_) => _.interceptStatus === 1).length || 0;
  }
  return sum;
};

const setExpandKeys = () => {
  let { appName, interceptStatus } = queryParams.value;
  // appName = appName.toLowerCase();
  let arr = [];
  const checkTreeQuery = (data) => {
   
    if (data.children && data.children.length > 0) {
      let arr2 = data.children.map((item) => checkTreeQuery(item));
      console.log(arr2, data, 'hhhhhhhh');
      if (arr2.some((item) => !!item)) {
        arr.push(data.id);
        return true;
      } else if (!!data.appName) {
        return false;
      }
    }

    // 只查询名称的情况
    let flag = false;

    if (!!appName && interceptStatus !== 0 && interceptStatus !== 1) {
      if (data.name.indexOf(appName) > -1) {
        console.log(
          "只查询名称的情况",
          data
          // data.name.toLowerCase().indexOf(appName) > -1
        );
        flag = true;
      }
    }
    // 只查询状态的情况
    if (!appName && (interceptStatus === 0 || interceptStatus === 1)) {
      if (data.interceptStatus === interceptStatus) {
        flag = true;
        console.log("只查询状态的情况", data);
      }
    }
    // 查询名称和状态的情况
    if (!!appName && (interceptStatus === 0 || interceptStatus === 1)) {
      if (
        data.interceptStatus === interceptStatus &&
        data.name.indexOf(appName) > -1
      ) {
        console.log("查询名称和状态的情况", data);
        flag = true;
      }
    }

    return flag;
  };
  tableList.value.forEach((item) => {
    if (checkTreeQuery(item)) {
      arr.push(item.id);
    }
  });
  state.expandRowKeys = [...new Set(arr)];
  console.log(state.expandRowKeys, "展开的id");
};

const setCheckDisabled = () => {
  // console.log(checkedRows.value);
  tableList.value.map((item) => {
    item.disabled =
      checkedRows.value.length > 0
        ? item.interceptStatus != checkedRows.value[0].interceptStatus
        : false;
    return item;
  });
};

const handleRowClick = (row, column) => {
  if (column.getColumnIndex() === 0 && !row.disabled) {
    const arr = treeFindPath(tableList.value, (d) => d.id === row.id);
    const idx = tableList.value.findIndex((_) => _.id == arr[0]);
    tableList.value[idx].checked = !tableList.value[idx].checked;
    setCheckDisabled();
  }
};

// 判断是否为根节点
const isRootNode = (row) => {
  return !!row.appName;
};

function objectSpanMethod({ row, column, rowIndex, columnIndex }) {
  // 只合并指定列（如第一列和最后一列）
  if (columnIndex === 0 || columnIndex === 0) {
    // 有子节点则合并子节点
    if (row.children && row.children.length > 0 && row.isExpand) {
      // console.log(row, "有子节点");
      return {
        rowspan: row.children.length + 1, // 合并行数 = 子节点数 + 自身
        colspan: 1,
      };
    } else {
      // console.log(row, "无子节点");
      return {
        rowspan: 1,
        colspan: 1,
      };
    }
  }
}

function handleCheckChange(row) {
  // console.log(row.interceptStatus);
  let val = row.interceptStatus;
  if (row.children && row.children.length > 0) {
    row.children = row.children?.map((item) => {
      item.interceptStatus = val;
      if (item.children && item.children.length > 0) {
        item.children = item.children?.map((item2) => {
          item2.interceptStatus = val;
          return item2;
        });
      }
      return item;
    });
    if (!row.appName) handleStatusChange(row);
  }
  // console.log(row.children);
}

/** 拦截开关状态修改  */
function handleStatusChange(row) {
  const checkTreeChecked = (node) => {
    // 如果节点不存在子节点，直接返回（叶子节点）
    if (!node.children || node.children.length === 0) {
      return;
    }
    // 递归处理所有子节点
    node.children.forEach((child) => checkTreeChecked(child));
    // 检查子节点状态：只要有一个子节点status=0，当前节点设为0
    const hasInactiveChild = node.children.some(
      (child) => child.interceptStatus === 0
    );
    node.interceptStatus = hasInactiveChild ? 0 : 1;
    return node;
  };
  const ids = treeFindPath(tableList.value, (d) => d.id === row.id);
  if (ids.length > 1) {
    const idx1 = tableList.value.findIndex((_) => _.id == ids[0]);
    tableList.value[idx1] = checkTreeChecked(tableList.value[idx1]);
  }
}

const handleCheckIntercept = (row) => {
  const ids = treeFindPath(initList.value, (d) => d.id === row.id);
  const idx = initList.value.findIndex((_) => _.id == ids[0]);
  console.log(initList.value[idx].interceptStatus);
  if (tableList.value[idx].checked) {
    state.curChangeCheckedRow = {
      ...initList.value[idx],
    };
  } else {
    state.curChangeCheckedRow = {};
  }
  let switchText = row.interceptStatus === 0 ? "开启" : "关闭";
  let text = !!row.appName
    ? `是否确认${switchText}应用名称为【${row.appName}】的拦截状态，${
        switchText == "开启" ? "开启后应用将无法正常使用" : "允许此应用发布弹窗"
      }`
    : `是否确认${switchText}拦截状态${
        switchText == "关闭" ? "，允许其发布弹窗" : ""
      }`;
  proxy.$modal
    .confirm(text)
    .then(async () => {
      let val = switchText === "关闭" ? 0 : 1;
      const arr = treeFindPath(
        tableList.value,
        (d) => d.id === row.id,
        [],
        "name"
      );
      let obj = {
        names: [arr?.join(",")],
        interceptStatus: val,
      };
      console.log("全选拦截传参", obj);
      await updateWindowIntercept(obj);
    })
    .then(() => {
      getList(true);
      proxy.$modal.msgSuccess(switchText + "成功");
    })
    .catch(() => {
      const arr = treeFindPath(initList.value, (d) => d.id === row.id);
      const idx = initList.value.findIndex((_) => _.id == arr[0]);
      let checked = tableList.value[idx].checked;
      tableList.value[idx] = JSON.parse(JSON.stringify(initList.value[idx]));
      tableList.value[idx].checked = checked;
    });
};

const handleSwitch = (row) => {
  const ids = treeFindPath(initList.value, (d) => d.id === row.id);
  const idx = initList.value.findIndex((_) => _.id == ids[0]);
  console.log(initList.value[idx].interceptStatus);
  if (tableList.value[idx].checked) {
    state.curChangeCheckedRow = {
      ...initList.value[idx],
    };
  } else {
    state.curChangeCheckedRow = {};
  }
  let text = row.interceptStatus === 0 ? "关闭" : "开启";
  proxy.$modal
    .confirm(
      text == "开启"
        ? "是否确认开启拦截状态"
        : "是否确认关闭拦截状态，允许其发布弹窗"
    )
    .then(async () => {
      const arr = treeFindPath(
        tableList.value,
        (d) => d.id === row.id,
        [],
        "name"
      );
      let obj = {
        names: [arr?.join(",")],
        interceptStatus: row.interceptStatus,
      };
      console.log("单个拦截传参", obj);
      await updateWindowIntercept(obj);
    })
    .then(() => {
      getList(true);
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(() => {
      row.interceptStatus = row.interceptStatus === 0 ? 1 : 0;
      // handleStatusChange(row);
    });
};

const handleExpandChange = (row, expandStatus) => {
  const childKeys =
    row.children
      ?.filter((_) => _.children && _.children.length > 0)
      ?.map((_) => _.id) || [];
  if (!!row.appName && !expandStatus) {
    expandRowKeys.value = expandRowKeys.value.filter(
      (_) => !childKeys.includes(_)
    );
  }
  if(!!expandStatus) {
    expandRowKeys.value.push(row.id)
  } else {
    expandRowKeys.value = expandRowKeys.value.filter((_) => _ != row.id);
  }
  console.log(row, expandStatus, childKeys, expandRowKeys.value, "rowIndex");
};

const handleError = (event) => {
  event.target.src = defaultImg;
};

/** 批量操作前的检测 */
const checkBatch = () => {
  let newArr = [];
  if (state.active === 1) {
    state.tableAllSelectedId.map((item) => {
      state.tableList_all.map((item2) => {
        if (item2.appName == item) {
          newArr.push(item2);
        }
      });
    });
  } else {
    checkedRows.value.map((item) => {
      state.tableList.map((item2) => {
        if (item2.appName == item.appName) {
          newArr.push(item2);
        }
      });
    });
  }

  let arr = [],
    narr = [],
    allArr = [];
  newArr.map((item) => {
    if (item.interceptStatus === 0) {
      narr.push(item.appName);
    } else {
      arr.push(item.appName);
    }
    allArr.push(item.appName);
  });
  return { arr, narr, allArr };
};

const handleChangeActive = () => {
  state.tableRadio = [];
  state.tableAllSelectedRow = [];
  state.tableAllSelectedId = [];
  state.queryParams2 = {
    whitelistStatus: 1,
    pageNum: 1,
    pageSize: 10,
    appName: "",
  };
  state.queryParams = {
    whitelistStatus: 0,
    pageNum: 1,
    pageSize: 10,
    appName: "",
    riskSort: "",
    reportSort: "",
    interceptSort: "",
    interceptStatus: "",
  };
  getList();
};

function handleSortDate(type, val = 0) {
  const name = !!val ? "queryParams" : "queryParams3";
  if (type === 1) {
    if (state[name].riskSort === "ASC") {
      state[name].riskSort = "DESC";
    } else {
      state[name].riskSort = "ASC";
    }
    state[name].reportSort = "";
    state[name].interceptSort = "";
  }
  if (type === 2) {
    if (state[name].reportSort === "ASC") {
      state[name].reportSort = "DESC";
    } else {
      state[name].reportSort = "ASC";
    }
    state[name].riskSort = "";
    state[name].interceptSort = "";
  }
  if (type === 3) {
    if (state[name].interceptSort === "ASC") {
      state[name].interceptSort = "DESC";
    } else {
      state[name].interceptSort = "ASC";
    }
    state[name].riskSort = "";
    state[name].reportSort = "";
  }

  !!val ? getList() : getDetail();
}

function handleBatch(val) {
  let obj = {};
  if (val === 1) {
    const { arr, narr } = checkBatch();
    if (arr.length > 0) {
      proxy.$modal.msgWarning("存在已开启拦截应用，操作失败");
      return;
    }
    proxy.$modal
      .confirm(
        `是否确认批量开启应用名称为【${narr.join(
          "、"
        )}】的拦截状态，开启后应用将无法正常使用`
      )
      .then(async () => {
        obj = {
          names: narr,
          interceptStatus: 1,
        };
        console.log("批量开启拦截传参", obj);
        loading.value = true;
        await updateWindowIntercept(obj);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  }
  if (val === 2) {
    const { narr, arr } = checkBatch();
    if (narr.length > 0) {
      proxy.$modal.msgWarning("存在已关闭拦截应用，操作失败");
      return;
    }
    proxy.$modal
      .confirm(
        `是否确认批量关闭应用名称为【${arr.join(
          "、"
        )}】的拦截状态，允许应用发布弹窗`
      )
      .then(async () => {
        obj = {
          names: arr,
          interceptStatus: 0,
        };
        console.log("批量关闭拦截传参", obj);
        loading.value = true;
        await updateWindowIntercept(obj);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  }
  if (val === 3) {
    const { arr } = checkBatch();
    proxy.$modal
      .confirm(
        (arr.length > 0
          ? "部分应用拦截状态为已开启，加入白名单后自动关闭拦截状态，"
          : "") + `是否确认加入白名单？`
      )
      .then(async () => {
        obj = {
          names: checkedRows.value.map((item) => item.appName),
          whitelistStatus: 1,
        };
        console.log("批量加入白名单传参", obj);
        loading.value = true;
        await updateWindowIntercept(obj);
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  }
  if (val === 4) {
    proxy.$modal
      .confirm(`是否确认批量移除白名单？`)
      .then(async () => {
        obj = {
          names: state.tableAllSelectedId,
          whitelistStatus: 0,
        };
        console.log("批量移除白名单传参", obj);
        loading.value = true;
        await updateWindowIntercept(obj);
      })
      .then((res) => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  }
}

function handleWhiteList(row, val = 0) {
  let obj = {
    names: [row.appName],
    whitelistStatus: val === 1 ? 0 : 1,
  };
  if (val === 1) {
    proxy.$modal
      .confirm(`是否确认移除白名单？`)
      .then(async () => {
        loading.value = true;
        console.log("移除白名单传参", obj);
        await updateWindowIntercept(obj);
      })
      .then((res) => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  } else {
    proxy.$modal
      .confirm(
        row.interceptStatus === 1
          ? "是否确认加入白名单，允许此应用发布弹窗并关闭拦截？"
          : `是否确认加入白名单？`
      )
      .then(async () => {
        loading.value = true;
        console.log("加入白名单传参", obj);
        await updateWindowIntercept(obj);
      })
      .then((res) => {
        getList();
        proxy.$modal.msgSuccess("操作成功");
      })
      .catch(() => (loading.value = false));
  }
}

const getDetail = () => {
  loading2.value = true;
  getWindowInterceptInfo(queryParams3.value)
    .then((res) => {
      console.log(res);
      const { records, total } = res.data;
      state.infoList = records || [];
      state.total3 = total || 0;
      state.open = true;
    })
    .finally(() => (loading2.value = false));
};

function handleView(row) {
  queryParams3.value.appName = row.appName;
  getDetail();
}

const handleClose = () => {
  queryParams3.value = {
    appName: "",
    interceptSort: "",
    riskSort: "",
    reportSort: "",
    pageNum: 1,
    pageSize: 10,
  };
};

/** 搜索按钮操作 */
const handleQuery = () => {
  expandRowKeys.value = [];
  state.tableAllSelectedId = [];
  state.tableAllSelectedRow = [];
  state[!!state.active ? "queryParams2" : "queryParams"].pageNum = 1;
  getList();
};

/** 重置按钮操作 */
const resetQuery = () => {
  proxy.resetForm(!!state.active ? "queryRef2" : "queryRef");
  if (!!state.active) {
    state.queryParams2 = {
      whitelistStatus: 1,
      pageNum: 1,
      pageSize: 10,
      appName: "",
    };
  } else {
    state.queryParams = {
      whitelistStatus: 0,
      pageNum: 1,
      pageSize: 10,
      appName: "",
      riskSort: "",
      reportSort: "",
      interceptSort: "",
      interceptStatus: "",
    };
  }
  handleQuery();
};

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      !!state.active ? "appName" : "appName"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state[!!state.active ? "tableRef2" : "tableRef"].setCurrentRow(null);
      state[!!state.active ? "tableRef2" : "tableRef"].toggleRowSelection(
        row,
        false
      );
      const index = state.tableAllSelectedId.indexOf(
        row[!!state.active ? "appName" : "appName"]
      );
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除appName
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state[!!state.active ? "tableRef2" : "tableRef"].setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state[!!state.active ? "tableRef2" : "tableRef"].setCurrentRow(row);
    state[!!state.active ? "tableRef2" : "tableRef"].toggleRowSelection(
      row,
      true
    );
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的appName存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (
      state.tableAllSelectedId.indexOf(
        item[!!state.active ? "appName" : "appName"]
      ) === -1
    ) {
      state.tableAllSelectedId.push(
        item[!!state.active ? "appName" : "appName"]
      );
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(
      row[!!state.active ? "appName" : "appName"]
    );
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除appName
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state[!!state.active ? "tableList2" : "tableList"];
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (
      item[!!state.active ? "appName" : "appName"] ===
      a[0][!!state.active ? "appName" : "appName"]
    ) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state[!!state.active ? "tableList_all2" : "tableList_all"].forEach(
      (item) => {
        if (
          state.tableAllSelectedId.indexOf(
            item[!!state.active ? "appName" : "appName"]
          ) === -1
        ) {
          state.tableAllSelectedId.push(
            item[!!state.active ? "appName" : "appName"]
          ); // 如果点击全选就保存全部的id
          state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
        }
      }
    );
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}
</script>

<style lang="scss" scoped>
:deep(.disable-header-checkbox) .el-checkbox {
  display: none;
}
:deep(.flexColumn) .cell {
  display: flex;
  align-items: center;
}
.flex {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}
.ascending.active {
  border-bottom-color: #4095e5;
}
.descending.active {
  border-top-color: #4095e5;
}
.app-collapse {
  border: none !important;
  background-color: transparent !important;
  :deep(.el-collapse-item__header),
  :deep(.el-collapse-item__wrap) {
    border: none !important;
    background-color: transparent !important;
  }
  &.center {
    :deep(.el-collapse-item__header) {
      justify-content: center;
    }
  }
  :deep(.el-collapse-item__header) {
    gap: 8px;
  }
  :deep(.el-collapse-item__arrow) {
    margin-left: 0;
  }
  .process-item {
    text-align: left;
    // text-indent: 60px;
    padding-left: 60px;
  }
}
.logo {
  line-height: 1;
  text-align: left;
  // white-space: pre-wrap;
  word-break: break-all;
  order: 1;
  display: flex;
  align-items: center;
  gap: 0 10px;
  font-size: 14px;
  img {
    width: 20px;
    height: 20px;
  }
}
</style>