import request from '@/utils/request'

// 查询该安装位置是否有设备绑定
export function checkPosition(id) {
    return request({
        url: `/system/position/find/device/${id}`,
        method: 'get',
        id
    })
}

// 添加安装位置
export function addPosition(data) {
    return request({
        url: '/system/position/create',
        method: 'post',
        data
    })
}

// 查询安装位置
export function getPositionTree(data) {
    return request({
        url: '/system/position/tree',
        method: 'post',
        data
    })
}

// 修改安装位置
export function updatePosition(data) {
    return request({
        url: `/system/position/update`,
        method: 'post',
        data
    })
}

// 删除安装位置
export function delPosition(id) {
    return request({
        url: `/system/position/delete/${id}`,
        method: 'delete'
    })
}

// 查看安装位置详情
export function positionInfo(id) {
    return request({
        url: `/system/position/view/${id}`,
        method: 'get'
    })
}

// 查询安装位置列表（免登录无加密）
export function getPositionList(id) {
    return request({
        url: `/system/position/list/${id}`,
        method: 'get'
    })
}

// 查询安装位置列表
export function getPositionListAuth() {
    return request({
        url: `/system/position/web/list`,
        method: 'get'
    })
}

// 根据当前id查找下一级位置（免登录无加密）
export function findPosition(id) {
    return request({
        url: `/system/position/web/find/children/${id}`,
        method: 'get'
    })
}