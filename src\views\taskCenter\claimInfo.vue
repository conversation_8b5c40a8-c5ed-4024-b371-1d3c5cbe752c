<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <Detail
        :troubleId="route.query.id"
        :isHandled="false"
        :readonly="true"
        @changeTroubleStatus="changeTroubleStatus"
      />

      <!-- 按钮区域，根据readonly状态显示不同按钮 -->
      <div class="taskInfo-btns">
        <el-button
          v-if="!(isKnowledge || isSpare)"
          type="warning"
          @click="handlePickup"
          v-throttle
          >认领</el-button
        >
        <el-button
          v-if="!(isKnowledge || isSpare)"
          type="success"
          @click="handleToKonwledge"
          v-throttle
          >转入知识库</el-button
        >
        <el-button
          v-if="!(isKnowledge || isSpare)"
          type="info"
          @click="handleToMaintain"
          v-throttle
          >无需维修</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>
  
  <script setup>
import { useRoute, useRouter } from "vue-router";
import Detail from "@/views/taskCenter/components/index.vue";
import {
  addKnowledgeBase,
  troubleUpdate,
  deviceMaintenance,
} from "@/api/mediaTeach/trouble";
import { addPointObj } from "@/utils/addPoint";
import { sendPointRequest } from "@/utils";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const isKnowledge = ref(route.query.knowledge == 1);
const isSpare = ref(!!route.query.spareId);
let troubleStatus = ref(0);

const changeTroubleStatus = (status) => {
  troubleStatus.value = status;
};

const handlePickup = () => {
  proxy.$modal
    .confirm(`确定认领此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: route.query.id,
        status: 1,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("认领成功");
          handleBack();
        }
      });
    })
    .catch();
};

const handleToKonwledge = () => {
  proxy.$modal.confirm(`确定将此工单转入知识库？`).then(() => {
    addKnowledgeBase({
      troubleId: route.query.id,
    }).then((resp) => {
      if (resp.code == 200) {
        sendPointRequest({
          event: "Click",
          eventDescribe: "点击转入知识库",
          content:
            troubleStatus.value > -1
              ? addPointObj[troubleStatus.value].status
              : "",
          num: 1,
        });
        proxy.$modal.msgSuccess("转入成功");
      }
    });
  });
};

const handleToMaintain = () => {
  proxy.$modal.confirm(`确定此工单无需维修？`).then(() => {
    deviceMaintenance({
      troubleIds: [route.query.id],
    }).then((resp) => {
      if (resp.code == 200) {
        proxy.$modal.msgSuccess("操作成功");
        handleBack();
      }
    });
  });
};

// 取消/返回处理
function handleBack() {
  if (isKnowledge.value) {
    proxy.$tab.closeOpenPage("/knowledgeManage/knowledge");
  } else if (isSpare.value) {
    proxy.$tab.closeOpenPage(
      `/spareManage/spareInfo?id=${route.query.spareId}`
    );
  } else {
    router.go(-1);
    proxy.$tab.closeOpenPage();
  }
}
</script>
  
  <style scoped lang="scss">
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }
  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.taskInfo {
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }

  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }
  &-btns {
    padding: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.upload-area {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .file-info {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
  