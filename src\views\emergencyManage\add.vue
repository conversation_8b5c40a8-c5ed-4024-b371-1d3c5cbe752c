<template>
  <div class="addEmergency app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>新建预案</span>
        </div>
      </template>

      <el-form ref="formRef" :model="form" :rules="rules">
        <el-descriptions title="" border style="margin-top: 20px" :column="3">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案类型"
          >
            <el-form-item label="" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请选择预案类型"
                style="width: 100%"
              >
                <el-option label="硬件故障" value="硬件故障" />
                <el-option label="网络故障" value="网络故障" />
                <el-option label="软件故障" value="软件故障" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案文件"
          >
            <el-form-item label="" prop="fileUrl">
              <fileUpload
                @update:modelValue="
                  (url) => {
                    form.fileUrl = url;
                  }
                "
                :showFileList="true"
                :type="5"
                :fileSize="20"
                :limit="1"
                :fileType="['doc', 'docx', 'xls', 'xlsx', 'txt']"
                :modelValue="form.fileUrl"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案名称"
          >
            <el-form-item label="" prop="name">
              <el-input
                v-model="form.name"
                style="width: 100%"
                placeholder="请输入预案名称"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="预案内容"
          >
            <el-form-item label="" prop="content">
              <el-input
                v-model="form.content"
                style="width: 100%"
                type="textarea"
                placeholder="请输入预案内容"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <!-- 人员表格 -->
      <!-- <div class="addEmergency-tit">
        关联人员
        <el-button type="primary" plain icon="Plus" @click="handleSelect"
          >点击选择关联人员</el-button
        >
      </div>
      <el-table :data="memberList" border>
        <el-table-column
          label="工号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="number"
        />
        <el-table-column
          label="名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="name"
        />
        <el-table-column
          label="关联时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceType"
        />
      </el-table> -->

      <div class="addEmergency-btns">
        <el-button type="primary" @click="handleSubmit" v-throttle>新建</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>

    <!-- 选择人员弹窗 -->
    <!-- <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="800"
      @close="handleCancel"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
      >
        <el-form-item prop="name">
          <el-input
            v-model="queryParams.name"
            placeholder="请输入人员名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loadingMember"
        ref="tableRef"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column
          type="selection"
          align="center"
          width="70"
          fixed="left"
        />
        <el-table-column
          label="人员编号"
          prop="number"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="人员姓名"
          prop="name"
          show-overflow-tooltip
          min-width="120"
        />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getMemberList"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog> -->
  </div>
</template>

<script setup>
import { ref, onMounted, reactive, toRefs, getCurrentInstance } from "vue";
import { useRouter, useRoute } from "vue-router";
import { findIndexInObejctArr } from "@/utils";
import { addSchoolPlan } from "@/api/emergency";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const state = reactive({
  formRef: null,
  loadingMember: false,
  memberList: [],
  tableList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  title: "选择关联人员",
  dialogVisible: false,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    name: "",
  },
  total: 0,
  isEdit: false,
  tableList: [],
  uploadRef: null,
  form: {
    name: "",
    type: "",
    content: "",
    fileUrl: "",
    userIds:[]
  },
  rules: {
    type: [{ required: true, message: "预案类型不能为空", trigger: "change" }],
    name: [{ required: true, message: "预案名称不能为空", trigger: "blur" }],
    fileUrl: [
      { required: true, message: "预案文件不能为空", trigger: "change" },
    ],
    content: [{ required: true, message: "预案内容不能为空", trigger: "blur" }],
  },
});
const {
  rules,
  formRef,
  total,
  title,
  memberList,
  loadingMember,
  queryParams,
  dialogVisible,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableRadio,
  tableList_all,
  tableList,
  uploadRef,
  form,
} = toRefs(state);

const getMemberList = () => {

}

// 关闭弹窗
const handleCancel = () => {
  resetQuery();
  state.dialogVisible = false;
  console.log("form", state.form);
};

// 提交弹窗表单
function submitForm() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请至少选择一项");
    return;
  }
  state.form.userIds = state.tableAllSelectedId;
  handleCancel();
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  state.tableRadio = [];
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getMemberList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQuery();
}

// 返回
const handleBack = () => {
  proxy.$tab.closeOpenPage("/emergencyManage/operation");
};

const handleSelect = () => {
  state.dialogVisible = true
};

// 提交
const handleSubmit = () => {
  state.formRef.validate((valid) => {
    if (valid) {
      addSchoolPlan(state.form).then(response => {
        proxy.$modal.msgSuccess('新建成功')
        handleBack()
      })
    }
  });
};

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      checkName.value
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
      state.tableAllSelectedId.push(item[checkName.value]);
      state.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item[checkName.value] === a[0][checkName.value]) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
        state.tableAllSelectedId.push(item[checkName.value]); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', state.tableList_all)
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}


// 初始化
onMounted(() => {
  
});
</script>

<style lang="scss" scoped>
.addEmergency {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}
</style>