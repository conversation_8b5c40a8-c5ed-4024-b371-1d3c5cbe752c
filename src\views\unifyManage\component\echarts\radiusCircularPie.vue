<template>
  <div class="pieData">
    <div class="pieData-chart">
      <img
        class="pieData-bg"
        src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/pie5-bg.png"
      />
      <Echarts
        :ref="props.id + 'Ref'"
        :id="props.id"
        class="echart"
        width="100%"
        height="6vw"
        :fullOptions="props.option"
      />
    </div>
    <div class="pieData-list">
      <div
        class="pieData-item"
        :class="[`${props.id}-item`]"
        v-for="(item, index) in props.option.options.series[0].data"
        :data-index="index"
      >
        <div class="pieData-name">
          <div
            :style="{
              backgroundColor: item.itemStyle.bgColor,
            }"
            class="item-dot"
          ></div>
          {{ item.name }}
        </div>
        <div class="pieData-count">
          {{ item.value > 999 ? "999+" : item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Echarts from "@/components/Echarts/index.vue";
import { toRefs } from "vue";

const props = defineProps({
  id: {
    type: String,
    default: "pieData",
  },
  option: {
    type: Object,
    default: {
      options: {
        series: [
          {
            type: "pie",
            radius: ["58%", "64%"],
            center: ["50%", "50%"],
            avoidLabelOverlap: false,
            padAngle: 5,
            emphasis: {
              scaleSize: 2.5,
              label: {
                show: true,
              },
            },
            label: {
              show: false,
              position: "center",
              formatter: "{d|{d}%}\n{b|{b}}",
              rich: {
                d: {
                  color: "#fff",
                  fontSize: 10,
                  lineHeight: 14,
                  fontWeight: "bold",
                },
                b: {
                  color: "#D4EDFF",
                  fontSize: 8,
                },
              },
            },
            labelLine: {
              show: false,
              length: 2,
              length2: 5,
              lineStyle: {
                color: "rgba(81, 131, 255, .2)",
              },
            },
            itemStyle: {
              borderRadius: 30, // 内外不同圆角
            },
            data: [
              {
                name: "8G以下",
                value: 10,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#00aaff",
                      },
                      {
                        offset: 1,
                        color: "#0252ef",
                      },
                    ],
                  },
                  bgColor: "#00AAFF",
                },
              },
              {
                name: "8G-16G",
                value: 30,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#01e9ff",
                      },
                      {
                        offset: 1,
                        color: "#00aafe",
                      },
                    ],
                  },
                  bgColor: "#00EAFF",
                },
              },
              {
                name: "16G-32G",
                value: 20,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#55cb69",
                      },
                      {
                        offset: 1,
                        color: "#79ed8d",
                      },
                    ],
                  },
                  bgColor: "#00CC03",
                },
              },
              {
                name: "32G-64G",
                value: 40,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#fe4c01",
                      },
                      {
                        offset: 1,
                        color: "#ff5b00",
                      },
                    ],
                  },
                  bgColor: "#FF5501",
                },
              },
              {
                name: "64G及以上",
                value: 40,
                itemStyle: {
                  color: {
                    type: "linear",
                    x: 0, // 渐变起始点 x 坐标（0-1）
                    y: 0, // 渐变起始点 y 坐标
                    x2: 1, // 渐变结束点 x 坐标
                    y2: 1, // 渐变结束点 y 坐标
                    colorStops: [
                      {
                        offset: 0,
                        color: "#ff9e1e",
                      },
                      {
                        offset: 1,
                        color: "#fef702",
                      },
                    ],
                  },
                  bgColor: "#FFFF00",
                },
              },
            ],
          },
        ],
      },
    },
  },
});

const state = reactive({
  pieData1Ref: null,
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData5Ref: null,
  pieData6Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData14Ref: null,
  pieData15Ref: null,
});
const {
  pieData1Ref,
  pieData2Ref,
  pieData3Ref,
  pieData4Ref,
  pieData5Ref,
  pieData6Ref,
  pieData7Ref,
  pieData8Ref,
  pieData9Ref,
  pieData10Ref,
  pieData11Ref,
  pieData12Ref,
  pieData13Ref,
  pieData14Ref,
  pieData15Ref,
} = toRefs(state);

const instance = getCurrentInstance();

const initPieFunc = () => {
  let timer = null;
  function waitForRef() {
    clearTimeout(timer);
    const echarts3DRef = instance?.proxy?.$refs[props.id + "Ref"];
    if (echarts3DRef) {
      document.querySelectorAll(`.${props.id}-item`).forEach((div) => {
        div.addEventListener("mouseover", function (e) {
          const index = parseInt(this.dataset.index); // 获取对应的数据索引
          echarts3DRef.dispatchAction({
            type: "highlight", // 触发高亮动作
            seriesIndex: 0, // 系列索引（第一个饼图）
            dataIndex: index, // 数据项索引
          });
        });

        div.addEventListener("mouseout", function () {
          const index = parseInt(this.dataset.index);
          echarts3DRef.dispatchAction({
            type: "downplay", // 取消高亮
            seriesIndex: 0,
            dataIndex: index,
          });
        });
      });
    } else {
      timer = setTimeout(waitForRef, 100);
    }
  }
  nextTick(waitForRef);
};

onBeforeUnmount(() => {
  if (state[`${props.id}Ref`]) {
    state[`${props.id}Ref`].unMountFunc();
  }
});

defineExpose({
  initPieFunc,
});
</script>

<style lang="scss" scoped>
.pieData {
  display: flex;
  align-items: center;
  margin-top: 0.5vw;
  &-chart {
    position: relative;
    flex: 1;
  }
  &-bg {
    position: absolute;
    top: 0.95vw;
    left: 0.82vw;
    width: 4.1vw;
    height: 4.1vw;
  }
  &-list {
    flex: 1;
    font-size: 0.55vw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    gap: 0.05vw 0;
  }
  &-item {
    cursor: pointer;
    color: #fff;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 1vw;
    line-height: 1.1vw;
    padding: 0 0.3vw;
    border-width: 1px;
    border-style: solid;
    border-image-source: linear-gradient(0deg, #c7d6ff4d 0%, #c7d6ff00 28.9%);
    border-image-slice: 1;
    box-sizing: border-box;
    background: radial-gradient(
      50% 71.05% at 50% 100%,
      #a3cdff40 0%,
      #0020c700 100%
    );
  }
  &-name {
    display: flex;
    align-items: center;
    gap: 0 0.2vw;
    color: #d4deffff;
    .item-dot {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 0.3vw;
      height: 0.3vw;
      border-radius: 50%;
      margin-right: 0.1vw;
      i {
        display: flex;
        width: 0.15vw;
        height: 0.15vw;
      }
    }
  }
}
</style>