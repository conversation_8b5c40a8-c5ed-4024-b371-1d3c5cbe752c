<template>
  <div class="taskInfo app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">新建任务单</div>
      </template>

      <taskDetail ref="detailRef" :projectId="projectId" />

      <div class="taskInfo-btns">
        <el-button type="primary" @click="handleSubmit" v-throttle
          >新建</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>

  </div>
</template>

<script setup>
import { reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { useRoute } from "vue-router";
import taskDetail from "./components/taskDetail.vue";

const route = useRoute();

const state = reactive({
  projectId: '',
  detailRef: null,
  loading: false,
  isAdd: true,
  isEdit: true,
});

const { detailRef, projectId, isEdit, loading, isAdd } = toRefs(state);

function handleSubmit() {
  state.detailRef.handleSubmit()
}

function handleBack() {
  state.detailRef.handleBack(1)
}

onMounted(() => state.projectId = route.query.projectId || '')

</script>

<style lang="scss" scoped>
.taskInfo {
  &-btns {
    padding: 50px 0;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}
</style>