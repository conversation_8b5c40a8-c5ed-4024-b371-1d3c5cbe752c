<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <el-row :gutter="20">
        <!--用户数据-->
        <el-col :span="24" :xs="24">
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            v-show="showSearch"
            label-width="68px"
          >
            <el-form-item label="" prop="userCode">
              <el-input
                v-model.trim="queryParams.userCode"
                placeholder="请输入用户编号"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="nickName">
              <el-input
                v-model.trim="queryParams.nickName"
                placeholder="请输入用户姓名"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="phoneNumber">
              <el-input
                v-model.trim="queryParams.phoneNumber"
                placeholder="请输入手机号码"
                clearable
                style="width: 200px"
                @keyup.enter="handleQuery"
                @clear="handleQuery"
              />
            </el-form-item>
            <el-form-item label="" prop="status">
              <el-select
                v-model="queryParams.status"
                placeholder="请选择用户状态"
                clearable
                style="width: 200px"
                @change="handleQuery"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button type="primary" icon="Search" @click="handleQuery"
                >搜索</el-button
              >
              <el-button icon="Refresh" @click="resetQuery">重置</el-button>
            </el-form-item>
          </el-form>

          <el-row :gutter="10" class="mb12" v-if="!isExternalPark">
            <el-col :span="1.5">
              <el-button
                type="primary"
                plain
                icon="Plus"
                @click="handleAdd"
                v-hasPermi="['system:user:add']"
                >新增</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="danger"
                plain
                icon="Delete"
                :disabled="multiple"
                @click="handleDelete"
                v-hasPermi="['system:user:remove']"
                >删除</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="warning"
                plain
                icon="Upload"
                @click="handleBatchImport"
                >导入</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                plain
                icon="Download"
                :disabled="multiple"
                @click="handleBatchExport"
                >导出</el-button
              >
            </el-col>
            <el-col :span="1.5">
              <el-button
                type="success"
                color="#626aef"
                plain
                icon="FullScreen"
                @click="handleQrCode"
                >生成绑定机构二维码</el-button
              >
            </el-col>
          </el-row>

          <el-table
            v-loading="loading"
            :data="userList"
            @selection-change="handleSelectionChange"
            row-key="userId"
            :reserve-selection="true"
            border
          >
            <el-table-column
              type="selection"
              width="50"
              align="center"
              :selectable="(row) => !isExternalPark"
            />
            <!-- <el-table-column
              label="用户编号"
              key="userId"
              prop="userId"
              min-width="80"
            /> -->
            <el-table-column
              label="用户编号"
              key="userCode"
              prop="userCode"
              min-width="80"
            />
            <!-- <el-table-column
              label="用户名称"
              key="userName"
              prop="userName"
              min-width="100"
              :show-overflow-tooltip="true"
            /> -->
            <el-table-column
              label="用户姓名"
              key="nickName"
              prop="nickName"
              min-width="100"
              show-overflow-tooltip
            />
            <el-table-column
              label="部门"
              key="deptName"
              prop="sysDept.deptName"
              min-width="100"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                {{ scope.row.sysDept?.deptName || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="手机号码"
              align="left"
              key="phoneNumber"
              prop="phoneNumber"
              min-width="100"
            >
              <template #default="scope">
                {{ scope.row.phoneNumber || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="用户角色"
              key="roleName"
              prop="roleName"
              min-width="100"
              :show-overflow-tooltip="true"
            >
              <template #default="scope">
                {{ scope.row.roleName || "-" }}
              </template>
            </el-table-column>

            <el-table-column
              label="状态"
              align="center"
              key="status"
              width="90"
            >
              <template #default="scope">
                <el-switch
                  v-model="scope.row.status"
                  active-value="0"
                  inactive-value="1"
                  @change="handleStatusChange(scope.row)"
                  :disabled="isExternalPark"
                ></el-switch>
              </template>
            </el-table-column>
            <el-table-column
              label="创建时间"
              align="center"
              prop="createTime"
              width="120"
              show-overflow-tooltip
            >
              <template #default="scope">
                <span>{{
                  scope.row.createTime
                    ? parseTime(scope.row.createTime, "{y}-{m}-{d}")
                    : "-"
                }}</span>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              align="center"
              min-width="150"
              class-name="small-padding fixed-width"
            >
              <template #default="scope">
                <template v-if="!isExternalPark">
                  <el-tooltip
                    content="修改"
                    placement="top"
                    v-if="scope.row.userId !== 1"
                  >
                    <el-button
                      link
                      type="primary"
                      icon="Edit"
                      @click="handleUpdate(scope.row)"
                      v-hasPermi="['system:user:edit']"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    content="删除"
                    placement="top"
                    v-if="scope.row.userId !== 1"
                  >
                    <el-button
                      link
                      type="danger"
                      icon="Delete"
                      @click="handleDelete(scope.row)"
                      v-hasPermi="['system:user:remove']"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    content="重置密码"
                    placement="top"
                    v-if="scope.row.userId !== 1"
                  >
                    <el-button
                      link
                      type="warning"
                      icon="Key"
                      @click="handleResetPwd(scope.row)"
                      v-hasPermi="['system:user:resetPwd']"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    content="分配角色"
                    placement="top"
                    v-if="scope.row.userId !== 1"
                  >
                    <el-button
                      link
                      type="success"
                      icon="CircleCheck"
                      @click="handleAuthRole(scope.row)"
                      v-hasPermi="['system:user:edit']"
                    ></el-button>
                  </el-tooltip>
                  <el-tooltip
                    content="加入运维人员表"
                    placement="top"
                    v-if="scope.row.userId !== 1"
                  >
                    <el-button
                      link
                      type="primary"
                      icon="CirclePlus"
                      @click="handleJoin(scope.row)"
                      v-hasPermi="['system:user:remove']"
                    ></el-button>
                  </el-tooltip>
                </template>
                <template v-else>
                  <span>-</span>
                </template>
              </template>
            </el-table-column>
          </el-table>
          <pagination
            v-show="total > 0"
            :total="total"
            v-model:page="queryParams.current"
            v-model:limit="queryParams.size"
            @pagination="getList"
          />
        </el-col>
      </el-row>
    </el-card>

    <!-- 添加或修改用户配置对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="700px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form :model="form" :rules="rules" ref="userRef" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="用户编号" prop="userName">
              <el-input
                v-model="form.userName"
                placeholder="请输入用户编号"
                maxlength="30"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工号" prop="workId">
              <el-input
                v-model="form.workId"
                placeholder="请输入工号"
                maxlength="20"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户姓名" prop="nickName">
              <el-input
                v-model.trim="form.nickName"
                placeholder="请输入用户姓名"
                maxlength="25"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="12">
            <el-form-item label="用户账号" prop="userName">
              <el-input v-model.trim="form.userName" placeholder="请输入用户账号" maxlength="30" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户密码" prop="password" v-if="!form.userId">
              <el-input v-model.trim="form.password" placeholder="请输入用户密码" type="password" maxlength="20" show-password />
            </el-form-item>
          </el-col>
          -->
          <el-col :span="12">
            <el-form-item label="所属部门" prop="deptId">
              <el-tree-select
                v-model="form.deptId"
                :data="deptList"
                :props="{
                  value: 'deptId',
                  label: 'deptName',
                  children: 'children',
                  disabled: 'disabled',
                }"
                placeholder="请选择所属部门"
                check-strictly
                clearable
                filterable
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="岗位" prop="postId">
              <el-select
                v-model="form.postId"
                placeholder="请选择岗位"
                clearable
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in postList"
                  :key="item.postId"
                  :label="item.postName"
                  :value="item.postId"
                  :disabled="item.disabled"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户角色" prop="roleIds">
              <el-select
                v-model="form.roleIds"
                multiple
                filterable
                placeholder="请选择用户角色"
                style="width: 100%"
                clearable
                @change="handleRoleSelectionChange"
              >
                <el-option
                  v-for="item in roleList"
                  :key="item.roleId"
                  :label="item.roleName"
                  :value="item.roleId"
                  :disabled="item.disabled || item.status === '1'"
                >
                  <span style="float: left">{{ item.roleName }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{
                    item.roleKey
                  }}</span>
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="手机号码" prop="phonenumber">
              <el-input
                v-model.trim="form.phonenumber"
                placeholder="请输入手机号码"
                maxlength="11"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="邮箱" prop="email">
              <el-input
                v-model.trim="form.email"
                placeholder="请输入邮箱"
                maxlength="50"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="用户性别">
              <el-select
                v-model="form.sex"
                placeholder="请选择性别"
                style="width: 100%"
              >
                <el-option
                  v-for="item in sexList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态">
              <el-radio-group v-model="form.status">
                <el-radio
                  v-for="item in statusList"
                  :key="item.value"
                  :value="item.value"
                  >{{ item.label }}</el-radio
                >
              </el-radio-group>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm">确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 修改导入对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="importDialogVisible"
      title="批量导入"
      width="400px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      @close="handleClose"
    >
      <div class="import-result" v-if="importResult.show">
        <div class="result-header">
          <span class="result-title">本次导入：</span>
          <span class="success">成功：{{ importResult.success }}条</span>
          <span class="error">失败：{{ importResult.error }}条</span>
        </div>
        <div class="error-reason" v-if="importResult.errorReason">
          <div style="line-height: 1.8">失败原因如下：</div>
          <div>{{ importResult.errorReason }}</div>
        </div>
        <div class="warn-reason" v-if="importResult.warnReason">
          <div>提示：</div>
          <div>{{ importResult.warnReason }}</div>
        </div>
      </div>
      <div class="import-steps" v-else>
        <div class="step">
          <div class="step-title">第一步：下载模板</div>
          <el-button type="primary" @click="downloadTemplate"
            >点击下载</el-button
          >
        </div>
        <div class="step">
          <div class="step-title">第二步：选择部门</div>
          <el-tree-select
            v-model="selectedDeptId"
            :data="deptList"
            :props="{
              value: 'deptId',
              label: 'deptName',
              children: 'children',
            }"
            placeholder="请选择部门"
            check-strictly
            clearable
            filterable
            style="width: 100%"
          />
        </div>
        <div class="step">
          <div class="step-title">第三步：填写信息后上传</div>
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            action="#"
            :auto-upload="false"
            :on-change="handleFileChange"
            :show-file-list="true"
            accept=".xlsx,.xls"
            :limit="1"
            :before-remove="
              () => {
                importFile = null;
                return true;
              }
            "
          >
            <el-button type="primary">上传文件</el-button>
          </el-upload>
        </div>
      </div>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">{{
            importResult.show ? "关闭" : "返回"
          }}</el-button>
          <el-button
            v-if="!importResult.show"
            type="primary"
            @click="handleImport"
            >导入</el-button
          >
          <el-button v-else type="primary" @click="handleRetry"
            >重新上传</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 生成机构绑定二维码 -->
    <el-dialog
      class="qrcodeDialog"
      v-model="qrcodeDialogVisible"
      width="300px"
      append-to-body
      :align-center="true"
      :show-close="false"
    >
      <div class="qrcode-refresh" @click="createQrCode(true, false)">
        <el-icon size="20"><RefreshRight /></el-icon>刷新
      </div>
      <div class="qrcode-container" v-loading="loadingCode">
        <div class="qrcode-wrapper">
          <img
            v-show="!loadingCode"
            :src="currentQrCode"
            alt="机构绑定二维码"
            style="max-width: 240px"
          />
        </div>
      </div>
      <div class="qrcode-text">
        机构名称：{{ useUserStore().corpName || "-" }}
      </div>
      <div class="qrcode-row">
        请选择绑定角色
        <el-select
          v-model="codeRoleIds"
          multiple
          placeholder="请选择"
          style="width: 160px"
          clearable
          collapse-tags
          collapse-tags-tooltip
          @change="handleCodeRoleSelectionChange"
        >
          <el-option
            v-for="item in codeRoleList"
            :key="item.roleId"
            :label="item.roleName"
            :value="item.roleId"
            :disabled="item.disabled || item.status === '1'"
          >
            <span style="float: left">{{ item.roleName }}</span>
            <span style="float: right; color: #8492a6; font-size: 13px">{{
              item.roleKey
            }}</span>
          </el-option>
        </el-select>
      </div>
    </el-dialog>
    <div id="qrcode" style="display: none"></div>
  </div>
</template>

<script setup name="User">
import imgUpload from "@/components/ImageUpload";
import { onMounted, nextTick, computed, onUnmounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { tenantTree as getTenantTree } from "@/api/park";
import {
  getCorpInfo,
  changeUserStatus,
  listUser,
  resetUserPwd,
  delUser,
  getUser,
  updateUser,
  addUser,
  addMaintain,
  exportUser,
  downloadUserImportTemplate,
  importUserData,
  checkTeacherLimit,
} from "@/api/system/user";
import { listTree } from "@/api/system/distribution";
import { listPost } from "@/api/system/post_new";
import { debounce } from "@/utils/debounce";
import useUserStore from "@/store/modules/user";
import QRCode from "qrcode";
import { ElMessageBox } from "element-plus";

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();

const codeRoleList = ref([]);
const statusList = ref([
  { label: "正常", value: "0" },
  { label: "停用", value: "1" },
]);
const sexList = ref([
  { label: "男", value: "0" },
  { label: "女", value: "1" },
  // { label: "未知", value: "2" },
]);
const currentQrCode = ref("");
const codeRoleIds = ref([]);
const userList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const initPassword = ref(undefined);
const roleOptions = ref([]);
const roleRef = ref(null);
const roleList = ref([]);
const isExternalPark = ref(false);

// 添加一个存储所有选中行的集合
const selectedRows = ref(new Set());

const state = reactive({
  uploadRef: null,
  loadingCode: false,
  qrcodeDialogVisible: false,
  postList: [],
  deptList: [],
  form: {},
  queryParams: {
    current: 1,
    size: 10,
    userName: undefined,
    phoneNumber: undefined,
    status: undefined,
  },
});

const rules = ref({
  nickName: [
    { required: true, message: "用户姓名不能为空", trigger: "blur" },
    {
      pattern: /^[\u4e00-\u9fa5]{1,25}$/,
      message: "用户姓名只能输入中文汉字",
      trigger: ["blur", "change"],
    },
  ],
  email: [
    {
      type: "email",
      message: "请输入正确的邮箱地址",
      trigger: ["blur", "change"],
    },
  ],
  deptId: [
    {
      required: true,
      message: "所属部门不能为空",
      trigger: ["blur", "change"],
    },
  ],
  tenantId: [
    {
      required: false,
      message: "所属机构不能为空",
      trigger: ["blur", "change"],
    },
  ],
  phonenumber: [
    { required: true, message: "手机号码不能为空", trigger: "blur" },
    {
      pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
      message: "请输入正确的手机号码",
      trigger: "blur",
    },
  ],
  workId: [
    {
      // pattern: /^[A-Za-z0-9]{20}$/,
      // message: "请输入20位数字+英文组合",
      pattern: /^[A-Za-z0-9]/,
      message: "请输入数字+英文组合",
      trigger: ["blur"],
    },
  ],
});

const {
  uploadRef,
  deptList,
  postList,
  queryParams,
  form,
  qrcodeDialogVisible,
  loadingCode,
} = toRefs(state);

// 修改角色选择变更处理函数
function handleCodeRoleSelectionChange(value) {
  codeRoleIds.value = value;
  createQrCode(true, true);
  // 重新生成二维码
}

async function handleQrCode() {
  await getUser().then((response) => {
    // 过滤状态为0的角色
    codeRoleList.value = response.data.roles.filter(
      (role) => role.status === "0"
    );
  });
  qrcodeDialogVisible.value = true;
  nextTick(() => createQrCode());
}

async function createQrCode(flag = false, clear = false) {
  try {
    loadingCode.value = true;
    const d = {
      refresh: flag,
      roleClear: clear,
      roleIds: clear ? codeRoleIds.value : [],
    };
    console.log("传参", d);
    let res = await getCorpInfo(d);
    codeRoleIds.value = res.data?.roles?.map((_) => _.roleId) || [];
    console.log(
      res,
      codeRoleIds.value,
      `&corpId=${useUserStore().corpId}&roleIds=${codeRoleIds.value.join(
        ","
      )}&key=${res.data?.key}`,
      "codekey"
    );
    QRCode.toDataURL(
      `&corpId=${useUserStore().corpId}&roleIds=${codeRoleIds.value.join(
        ","
      )}&key=${res.data?.key}`,
      {
        width: 200, // 调整为更小的尺寸
        margin: 1, // 减小边距
        scale: 4, // 保持清晰度
      }
    ).then((url) => {
      console.log(url);
      currentQrCode.value = url;
      loadingCode.value = false;
    });
  } catch (error) {
    console.log(error);
  }
}

/** 查询用户列表 */
function getList() {
  loading.value = true;
  console.log(queryParams.value, "查询参数");

  listUser(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        console.log(res.data, "用户列表");

        userList.value = res.data.records;
        total.value = res.data.total;
      } else {
        userList.value = [];
        total.value = 0;
      }
    })
    .catch((error) => {
      // 处理接口调用失败的情况
      userList.value = [];
      total.value = 0;
      loading.value = false;
    })
    .finally(() => {
      // 无论成功还是失败，都需要关闭loading状态
      loading.value = false;
    });
}
/** 搜索按钮操作 */
const handleQuery = debounce(() => {
  queryParams.value.current = 1;
  getList();
}, 500);

/** 重置按钮操作 */
const resetQuery = debounce(() => {
  proxy.resetForm("queryRef");
  queryParams.value = {
    current: 1,
    size: 10,
    userName: undefined,
    phoneNumber: undefined,
    status: undefined,
    userCode: undefined,
  };
  getList();
}, 500);

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.userId || ids.value;
  let userNames = "";

  if (row.userId) {
    // 单个删除
    userNames = row.userCode;
  } else {
    // 批量删除，获取所有选中用户的userName
    const selectedUsers = userList.value.filter((item) =>
      ids.value.includes(item.userId)
    );
    if (selectedUsers.length === 0) {
      proxy.$modal.msgWarning("请选择要删除的用户");
      return;
    }
    userNames = selectedUsers.map((item) => item.userCode).join("、");
  }

  proxy.$modal
    .confirm('是否确认删除用户编号为"' + userNames + '"的数据项？')
    .then(function () {
      return delUser(userIds);
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
}
/** 加入运维按钮操作 */
function handleJoin(row) {
  console.log(row);

  // 检查用户是否已经有运维人员角色
  if (row.roleName?.includes("运维人员")) {
    proxy.$modal.msgWarning("该用户已经是运维人员，无需重复添加");
    return;
  }

  const userIds = row.userId || ids.value;
  proxy.$modal
    .confirm(
      '是否确认将用户编号为"' + row.userCode + '"的用户加入运维人员列表？'
    )
    .then(() => {
      addMaintain(userIds).then((res) => {
        console.log(res, "加入运维人员返回的参数");
        if (!!res.data) {
          console.log(res.data, "教职工检测失败");
          ElMessageBox.alert(res.data, "系统提示", {
            type: "warning",
            confirmButtonText: "确认",
            callback: (action) => {},
          });
        }
        getList();
        proxy.$modal.msgSuccess("加入成功");
      });
    })
    .catch(() => {});
}
/** 用户状态修改  */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal
    .confirm('确认要"' + text + '""' + row.userName + '"用户吗?')
    .then(function () {
      return changeUserStatus(row.userId, row.status);
    })
    .then(() => {
      proxy.$modal.msgSuccess(text + "成功");
    })
    .catch(function () {
      row.status = row.status === "0" ? "1" : "0";
    });
}
/** 跳转角色分配 */
function handleAuthRole(row) {
  const userId = row.userId;
  router.push("/system/user-auth/role/" + userId);
}
/** 重置密码按钮操作 */
function handleResetPwd(row) {
  proxy
    .$prompt('请输入"' + row.userName + '"的新密码', "提示", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      closeOnClickModal: false,
      inputPattern: /^[A-Za-z0-9!@#$%^&*()_+\-=\[\]{};:'",.<>/?]{5,20}$/,
      inputErrorMessage: "密码必须为5-20位数字、字母或特殊字符",
    })
    .then(({ value }) => {
      resetUserPwd(row.userId, value).then((response) => {
        proxy.$modal.msgSuccess("修改成功，新密码是：" + value);
      });
    })
    .catch(() => {});
}
/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.userId);
  single.value = selection.length !== 1;
  multiple.value = !selection.length;
}

/** 重置操作表单 */
function reset() {
  form.value = {
    userId: undefined,
    deptId: undefined,
    // userName: undefined,
    nickName: undefined,
    // password: undefined,
    phonenumber: undefined,
    email: undefined,
    sex: undefined,
    status: "0",
    // remark: undefined,
    postId: undefined,
    roleIds: [],
  };
  proxy.resetForm("userRef");
  // 清除角色选择
  if (roleRef.value) {
    roleRef.value.clearSelection();
  }
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  getUser().then((response) => {
    // 过滤状态为0的角色
    roleList.value = response.data.roles.filter((role) => role.status === "0");
    getDeptList();
    getPostList();
    const idx = roleList.value.findIndex((_) => _.roleKey == "maintain");
    form.value.roleIds = idx != -1 ? [roleList.value[idx].roleId] : [];
    open.value = true;
    title.value = "添加用户";
    console.log(roleList.value, "角色列表");
  });
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  const userId = row.userId || ids.value;
  getUser(userId).then((response) => {
    console.log(response, "修改用户获取的数据");

    form.value = response.data.data;

    // 处理角色列表
    // 1. 先过滤出状态为0的角色
    roleList.value = response.data.roles.filter((role) => role.status === "0");

    // 2. 检查用户当前角色是否在roleList中，如果不在则添加并设置为禁用状态
    if (response.data.userRoles && response.data.userRoles.length > 0) {
      response.data.userRoles.forEach((userRole) => {
        const existingRole = roleList.value.find(
          (role) => role.roleId === userRole.roleId
        );
        if (!existingRole) {
          roleList.value.unshift({
            ...userRole,
            disabled: true, // 设置为禁用状态
          });
        }
      });
    }

    form.value.roleIds = response.data.roleIds;

    // 获取部门和岗位列表
    getDeptList().then(() => {
      // 检查用户当前部门是否在deptList中
      if (response.data.data.dept) {
        const userDept = response.data.data.dept;
        // 递归检查部门树
        const findDept = (deptList) => {
          for (let dept of deptList) {
            if (dept.deptId === userDept.deptId) {
              return true;
            }
            if (dept.children && dept.children.length) {
              if (findDept(dept.children)) {
                return true;
              }
            }
          }
          return false;
        };

        // 如果当前部门不在列表中，添加到顶层并设置为禁用状态
        if (!findDept(deptList.value)) {
          deptList.value.unshift({
            ...userDept,
            disabled: true,
          });
        }
      }
    });

    // 获取岗位列表
    getPostList().then(() => {
      // 在获取完岗位列表后，处理用户当前岗位
      if (response.data.userPosts && response.data.userPosts.length > 0) {
        const userPost = response.data.userPosts[0];

        // 检查该岗位是否已存在于postList中
        const existingPost = postList.value.find(
          (post) => post.postId === userPost.postId
        );

        // 如果不存在，则添加到postList并设置为禁用
        if (!existingPost) {
          postList.value.unshift({
            ...userPost,
            disabled: true,
          });
        }

        // 设置表单的postId
        form.value.postId = userPost.postId;
      }
    });

    open.value = true;
    title.value = "修改用户";
    form.value.password = "";
  });
}

// 添加节流函数
const throttle = (fn, delay = 1000) => {
  let timer = null;
  let isExecuting = false; // 添加执行标志位

  return async function (...args) {
    if (timer || isExecuting) return; // 如果正在执行或等待中，直接返回

    isExecuting = true; // 设置执行标志
    timer = setTimeout(() => {
      timer = null;
    }, delay);

    try {
      await fn.apply(this, args);
    } finally {
      isExecuting = false; // 确保执行完成后重置标志位
    }
  };
};

/** 提交按钮 */
const submitForm = throttle(async () => {
  try {
    const valid = await proxy.$refs["userRef"].validate();
    if (valid) {
      const submitData = { ...form.value };

      if (submitData.postId) {
        submitData.postIds = [submitData.postId];
      }

      if (form.value.userId != undefined) {
        updateUser(submitData).then((res) => {
          console.log(res, "提交的参数", submitData);
          if (res.code == 200) {
            if (!!res.data) {
              console.log(res.data, "教职工检测失败");
              ElMessageBox.alert(res.data, "系统提示", {
                type: "warning",
                confirmButtonText: "确认",
                callback: (action) => {},
              });
            }
            proxy.$modal.msgSuccess("修改成功");
            open.value = false;
            getList();
          }
        });
      } else {
        addUser(submitData).then((res) => {
          console.log(res, "提交的参数", submitData);
          if (res.code == 200) {
            if (!!res.data) {
              console.log(res.data, "教职工检测失败");
              ElMessageBox.alert(res.data, "系统提示", {
                type: "warning",
                confirmButtonText: "确认",
                callback: (action) => {},
              });
            }
            proxy.$modal.msgSuccess("新增成功");
            open.value = false;
            getList();
          }
        });
        // const { nickName, roleIds, phonenumber } = submitData;
        // let obj = {
        //   nickName,
        //   roleIds,
        //   phonenumber,
        // };
        // console.log("检测传参", obj);
        // await checkTeacherLimit(obj)
        //   .then((res) => {
        //     console.log(res, "教职工检测成功");
        //   })
        //   .catch((err) => {
        //     console.log(err, "教职工检测失败");
        //     ElMessageBox.alert(err, "系统提示", {
        //       type: "warning",
        //       confirmButtonText: "确认",
        //       callback: (action) => {},
        //     });
        //   })
        //   .finally(async () => {
        //     open.value = false;
        //     await addUser(submitData);
        //     proxy.$modal.msgSuccess("新增成功");
        //   });
        // getList();
      }
    }
  } catch (error) {
    // console.error('提交出错:', error);
  }
}, 2500); // 设置1.5秒的节流时间

function getDeptList() {
  let params = {
    deptName: "",
    status: "0",
  };
  return listTree(params).then((res) => {
    if (res.code === 200) {
      deptList.value = addAttr(res.data);
      console.log("获取部门列表:", deptList.value);
    }
    return res;
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

function getPostList() {
  const params = {
    pageNum: 1,
    pageSize: 999999, // 获取所有岗位
    status: "0",
  };
  return listPost(params).then((res) => {
    if (res.code === 200) {
      postList.value = res.data.rows;
    }
    return res;
  });
}

// 修改角色选择变更处理函数
function handleRoleSelectionChange(value) {
  form.value.roleIds = value;
  form.value.roles = value.map((roleId) => {
    return roleList.value.find((_) => _.roleId == roleId);
  });
}

// 修改监听函数
function handleParkChange(event) {
  if (route.path === "/system/user") {
    if (event.detail.type === "select") {
      isExternalPark.value = true;
    } else if (event.detail.type === "clear") {
      isExternalPark.value = false;
    }
    getList();
  }
}

// 初始化时检查状态
function initParkState() {
  const selectedParkData = localStorage.getItem("selectedParkData");
  isExternalPark.value = !!selectedParkData;
}

onMounted(() => {
  initParkState();
  getList();
  window.addEventListener("parkChange", handleParkChange);
});

onUnmounted(() => {
  window.removeEventListener("parkChange", handleParkChange);
});

// 修改表格的default-checked属性，使其在页面切换时保持选中状态
function isRowSelected(row) {
  return selectedRows.value.has(row.userId);
}

// 修改导出函数
function handleBatchExport() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning("请至少选择一条数据");
    return;
  }
  proxy.$modal.confirm("确定批量导出？").then(() => {
    const userIds = [...ids.value];
    exportUser(userIds)
      .then((res) => {
        if (res instanceof Blob) {
          const blob = res;
          const filename = `用户信息.xls`;

          // 创建下载链接并触发下载
          const link = document.createElement("a");
          link.href = window.URL.createObjectURL(blob);
          link.download = filename;
          link.click();
          window.URL.revokeObjectURL(link.href);

          proxy.$modal.msgSuccess("导出成功");
        } else {
          proxy.$modal.msgError("导出失败：返回格式错误");
        }
      })
      .catch((error) => {
        proxy.$modal.msgError("导出失败");
      });
  });
}

// 添加导入相关的响应式数据
const importDialogVisible = ref(false);
const importFile = ref(null);
const importResult = ref({
  show: false,
  success: 0,
  error: 0,
  errorReason: "",
});

// 添加部门选择相关的响应式数据
const selectedDeptId = ref("");

// 导入按钮处理函数
function handleBatchImport() {
  console.log("Opening import dialog");
  importDialogVisible.value = true;
  // 重置导入结果
  importResult.value = {
    show: false,
    success: 0,
    error: 0,
    errorReason: "",
  };
  // 重置其他相关状态
  importFile.value = null;
  selectedDeptId.value = "";

  // 加载部门数据
  getDeptList();
}

// 修改处理文件导入函数
function handleImport() {
  if (!selectedDeptId.value) {
    proxy.$modal.msgWarning("请先选择部门");
    return;
  }

  if (!importFile.value) {
    proxy.$modal.msgWarning("请先选择要导入的文件");
    return;
  }

  proxy.$modal.loading("正在导入数据，请稍候...");

  const formData = new FormData();
  formData.append("file", importFile.value.raw);

  const params = {
    deptId: selectedDeptId.value,
  };

  importUserData(params, formData)
    .then((res) => {
      if (res.code === 200) {
        console.log("导入结果:", res);

        importResult.value = {
          show: true,
          success: res.data?.successCount || 0,
          error: res.data?.errorCount || 0,
          errorReason: formatErrorReason(res.data?.errorInfoList),
          warnReason: res.data?.successMsg,
        };
        if (res.data?.successCount > 0) {
          getList();
        }
      }
    })
    .catch(() => {
      proxy.$modal.msgError("导入失败");
    })
    .finally(() => {
      // 使用封装好的关闭 loading
      proxy.$modal.closeLoading();
    });
}

// 修改关闭对话框函数,重置部门选择
function handleClose() {
  if (uploadRef.value) uploadRef.value.clearFiles();
  importDialogVisible.value = false;
  importResult.value.show = false;
  importFile.value = null;
  selectedDeptId.value = ""; // 重置部门选择
}

// 修改重试上传函数,重置部门选择
function handleRetry() {
  importResult.value.show = false;
  importFile.value = null;
  selectedDeptId.value = ""; // 重置部门选择
}

// 下载模板
function downloadTemplate() {
  downloadUserImportTemplate()
    .then((res) => {
      if (res instanceof Blob) {
        const blob = res;
        const objUrl = window.URL.createObjectURL(blob);

        const a = document.createElement("a");
        a.setAttribute("href", objUrl);
        a.setAttribute("download", "用户信息模板.xls");
        a.click();
        window.URL.revokeObjectURL(objUrl);

        proxy.$modal.msgSuccess("模板下载成功");
      } else {
        proxy.$modal.msgError("模板下载失败");
      }
    })
    .catch(() => {
      proxy.$modal.msgError("模板下载失败");
    });
}

// 处理文件选择
function handleFileChange(file) {
  if (!file) {
    proxy.$modal.msgError("请选择文件");
    return false;
  }

  // 验证文件类型
  const isExcel =
    file.raw.type ===
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet" ||
    file.raw.type === "application/vnd.ms-excel";
  if (!isExcel) {
    proxy.$modal.msgError("只能上传 Excel 文件!");
    return false;
  }

  importFile.value = file;
  return true;
}

// 格式化错误信息
function formatErrorReason(errorList) {
  if (!errorList || errorList.length === 0) return "";

  return errorList
    .map((error) => {
      return `第${error.row}行: ${error.cause}`;
    })
    .join("\n");
}
</script>

<style lang="scss">
.qrcodeDialog {
  .el-dialog__header {
    display: none !important;
  }
}
</style>
<style lang="scss" scoped>
.qrcode {
  &-refresh,
  &-text {
    display: flex;
    align-items: center;
    justify-content: center;
  }

  &-text {
    margin: 0 0 20px;
  }

  &-refresh {
    margin: 0 auto;
    width: 40%;
    color: #4095e5;
    cursor: pointer;
    line-height: 1;
    gap: 0 5px;
    font-size: 16px;
  }

  &-row {
    display: flex;
    gap: 0 10px;
    align-items: center;
  }
}
.qrcode-container {
  width: 200px;
  height: 200px;
  margin: 0 auto 20px;
  padding: 8px 0; /* 继续减小内边距 */
}

.qrcode-wrapper {
  text-align: center;
  width: fit-content;
  max-width: 100%;
}

.qrcode-wrapper img {
  width: auto;
  max-width: 100%;
  height: auto;
}
.el-table {
  margin: 0;

  :deep(.el-table__body-wrapper) {
    overflow-x: hidden;
  }
}

.import-steps {
  padding: 20px 0;

  .step {
    margin-bottom: 20px;

    &-title {
      margin-bottom: 10px;
      font-weight: bold;
    }
  }
}

.import-result {
  padding: 20px;
  text-align: center;

  .result-header {
    display: flex;
    align-items: center;
    // justify-content: center;
    margin-bottom: 15px;

    .result-title {
      margin-right: 10px;
      font-weight: bold;
    }

    .success {
      color: #67c23a;
      margin-right: 10px;
    }

    .error {
      color: #f56c6c;
    }
  }

  .error-reason {
    text-align: left;
    color: #f56c6c;
    font-size: 14px;
    margin-top: 15px;
    padding: 10px;
    background-color: #fef0f0;
    border-radius: 4px;

    div:last-child {
      line-height: 1.8;
      white-space: pre-line;
    }
  }

  .warn-reason {
    text-align: left;
    color: #e6a23c;
    font-size: 14px;
    margin-top: 15px;
    padding: 10px;
    background-color: #fdf6ec;
    border-radius: 4px;

    div:last-child {
      white-space: pre-line;
    }
  }
}
</style>
