<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        @submit.native.prevent
      >
        <el-form-item label="" prop="codeAndName">
          <el-input
            v-model="queryParams.codeAndName"
            placeholder="请输入类别编号/名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择状态"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新建类别</el-button
          >
          <el-button
            type="danger"
            plain
            icon="Delete"
            @click="handleBatchDel"
            :disabled="tableAllSelectedId.length < 1"
            >批量删除</el-button
          >
        </el-col>
      </el-row>

      <el-table
        ref="typeTableRef"
        v-loading="loading"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column type="selection" label="" width="70" align="center" />
        <el-table-column
          label="类别编号"
          align="center"
          minWidth="100px"
          prop="code"
          show-overflow-tooltip
        />
        <el-table-column
          label="类别名称"
          align="center"
          minWidth="120px"
          prop="name"
          show-overflow-tooltip
        />
        <el-table-column
          label="类别描述"
          align="center"
          minWidth="150px"
          show-overflow-tooltip
        >
          <template #default="scope">
            {{ scope.row.description || "-" }}
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" minWidth="100px">
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.status]"
              effect="dark"
              :type="statusList[scope.row.status].type"
              >{{ statusList[scope.row.status].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          minWidth="150px"
          prop="createTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.createTime || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="修改时间"
          align="center"
          minWidth="150px"
          prop="createTime"
          show-overflow-tooltip
        >
          <template #default="scope">
            <span>{{ scope.row.updateTime || "-" }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="230"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="success"
              icon="Search"
              @click="handleCheck(scope.row)"
              >查看</el-button
            >
            <el-button
              v-if="scope.row.name != '初始类别'"
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >编辑</el-button
            >
            <el-button
              v-if="scope.row.name != '初始类别'"
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="typeRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent
      >
        <el-form-item v-if="form.id" label="类别编号" prop="code">
          <el-input v-model="form.code" disabled placeholder="-" />
        </el-form-item>
        <el-form-item label="类别名称" prop="name">
          <el-input
            v-model="form.name"
            :readonly="readonly"
            placeholder="请输入类别名称"
            maxlength="10"
          />
        </el-form-item>
        <el-form-item label="类别描述" prop="description">
          <el-input
            v-model="form.description"
            type="textarea"
            :readonly="readonly"
            placeholder="请输入类别描述"
            maxlength="100"
          />
        </el-form-item>
        <el-form-item label="状态" prop="status">
          <el-radio-group
            v-model="form.status"
            :disabled="readonly || (form.typeName == '智慧大屏' && !!form.id)"
          >
            <el-radio
              v-for="item in statusList"
              :key="item.value"
              :value="item.value"
              >{{ item.label }}</el-radio
            >
          </el-radio-group>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            v-if="!readonly"
            type="primary"
            @click="submitForm"
            v-throttle
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { findIndexInObejctArr } from "@/utils";
import {
  sparePartsCategoryPage,
  addSparePartsCategory,
  updateSparePartsCategory,
  delSparePartsCategory,
} from "@/api/spare";

const route = useRoute();
const { proxy } = getCurrentInstance();

const state = reactive({
  typeTableRef: null,
  typeRef: null,
  open: false,
  loading: false,
  readonly: false,
  total: 0,
  title: "",
  tableList_all: [],
  tableList: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  form: {},
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    typeName: "",
    status: "",
  },
  rules: {
    name: [
      { required: true, message: "类别名称不能为空", trigger: "blur" },
      { max: 10, message: "类别名称最多输入10个字符", trigger: "blur" },
    ],
    description: [
      { max: 100, message: "类别描述最多输入100个字符", trigger: "blur" },
    ],
  },
  statusList: [
    { label: "禁用", value: 0, type: "danger" },
    { label: "启用", value: 1, type: "success" },
  ],
});

const {
  typeTableRef,
  typeRef,
  open,
  title,
  loading,
  readonly,
  total,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableList,
  tableList_all,
  tableRadio,
  queryParams,
  form,
  rules,
  statusList,
} = toRefs(state);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

/** 查询类别列表 */
function getList() {
  state.loading = true;
  sparePartsCategoryPage(state.queryParams).then((response) => {
    console.log("type", response);
    if (response.code == 200) {
      const { records, total } = response.data;
      state.tableList = records || [];
      state.total = total || 0;
      state.loading = false;
      nextTick(() => {
        state.tableList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.id) > -1) {
            state.typeTableRef.toggleRowSelection(item, true);
          } else {
            state.typeTableRef.toggleRowSelection(item, false);
          }
        });
      });
    }
  });
  sparePartsCategoryPage({
    ...state.queryParams,
    pageNum: 1,
    pageSize: 999999,
  }).then((response) => {
    console.log("type", response);
    if (response.code == 200) {
      const { records } = response.data;
      state.tableList_all = records || [];
    }
  });
}
/** 取消按钮 */
function cancel() {
  state.open = false;
  reset();
}
/** 表单重置 */
function reset() {
  state.form = {
    id: "",
    name: "",
    description: "",
    status: 1,
    createTime: "",
  };
  proxy.resetForm("typeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
  state.queryParams.pageSize = 10;
  handleQuery();
}

/** 新增按钮操作 */
function handleAdd() {
  reset();
  state.open = true;
  state.readonly = false;
  state.title = "添加类别";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  state.title = "修改类别";
  state.readonly = false;
  state.form = JSON.parse(JSON.stringify(row));
  state.open = true;
}
/** 查看按钮操作 */
function handleCheck(row) {
  reset();
  state.title = "查看类别";
  state.readonly = true;
  state.form = JSON.parse(JSON.stringify(row));
  state.open = true;
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["typeRef"].validate((valid) => {
    if (valid) {
      if (!!state.form.id) {
        updateSparePartsCategory(state.form).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          state.open = false;
        });
      } else {
        addSparePartsCategory(state.form).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          state.open = false;
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  //   checkDeviceType(row.id).then((res) => {
  //     console.log("检查", res);
  //     if (!!res.data) {
  //       proxy.$modal
  //         .confirm(`${row.typeName}类别已存在设备，确认删除?`)
  //         .then(function () {
  //           return deviceTypeDel({ id: row.id });
  //         })
  //         .then(() => {
  //           getList();
  //           proxy.$modal.msgSuccess("删除成功");
  //         })
  //         .catch(() => {});
  //     } else {
  proxy.$modal
    .confirm('是否确认删除类别名称为"' + row.name + '"的数据项？')
    .then(async function () {
      await delSparePartsCategory({ ids: row.id });
    })
    .then(() => {
      getList();
      proxy.$modal.msgSuccess("删除成功");
    })
    .catch(() => {});
  // }
  //   });
}

// 批量删除操作
function handleBatchDel() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请选择至少一个点位");
    return;
  } else if (
    state.tableAllSelectedRow.findIndex((_) => _.name == "初始类别") != -1
  ) {
    proxy.$modal.msgWarning("初始类别不能删除");
  } else {
    proxy.$modal
      .confirm("确定批量删除？")
      .then(async function () {
        await delSparePartsCategory({
          ids: state.tableAllSelectedId.join(","),
        });
      })
      .then(() => {
        getList();
        proxy.$modal.msgSuccess("删除成功");
      })
      .catch(() => {});
  }
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "id"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.typeTableRef.setCurrentRow(null);
      state.typeTableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.id);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.typeTableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.typeTableRef.setCurrentRow(row);
    state.typeTableRef.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.id) === -1) {
      state.tableAllSelectedId.push(item.id);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.id);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.id === a[0].id) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.id) === -1) {
        state.tableAllSelectedId.push(item.id); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

getList();
</script>
