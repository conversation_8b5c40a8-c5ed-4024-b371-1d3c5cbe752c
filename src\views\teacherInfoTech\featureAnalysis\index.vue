<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>

            <el-main class="eBox" v-loading="loading">
                <div class="eBox-top">
                    <div class="eBox-opt opt1">
                        <div class="icon">
                            <Monitor />
                        </div>
                        <div class="info">
                            <div class="name">多媒体教学率</div>
                            <div class="value">{{ deviceConfig.teachRatio }}%</div>
                        </div>
                    </div>
                    <div class="eBox-item item1">
                        <Echarts @setFontSize="setFontSize" id="nlsyqs" width="100%" height="100%"
                            :fullOptions="nlsyqsOption" :loading="false" />
                    </div>
                    <div class="eBox-opt opt2">
                        <div class="icon">
                            <Clock />
                        </div>
                        <div class="info">
                            <div class="name">多媒体教学时长</div>
                            <div class="value">{{ deviceConfig.teachTime }}小时</div>
                        </div>
                    </div>
                </div>

                <div class="eBox-item item2">
                    <Echarts @setFontSize="setFontSize" id="xbjxl" width="100%" height="100%" :fullOptions="xbjxlOption"
                        :loading="false" />
                </div>
                <div class="eBox-item item3">
                    <Echarts @setFontSize="setFontSize" id="xljxsc" width="100%" height="100%" :fullOptions="xljxscOption"
                        :loading="false" />
                </div>
                <div class="eBox-item item4">
                    <Echarts @setFontSize="setFontSize" id="zcsyqs" width="100%" height="100%" :fullOptions="zcsyqsOption"
                        :loading="false" />
                </div>
            </el-main>
        </el-card>
    </div>
</template>

<script setup name="Post">
import Echarts from '@/components/Echarts/index.vue'
import { onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { queTeacherSexUseTime, queAgeUseTime, queEducationUseTime, quePostUseTime } from '@/api/teachInfo/teacher';
import { queDeviceRatio } from '@/api/city'
import { transformSize } from '@/utils';

const { proxy } = getCurrentInstance();
const route = useRoute()

const loading = ref(false);
const showSearch = ref(true);
const deviceConfig = ref({
    teachRatio: 0,
    teachTime: 0
})

//年龄使用趋势分析
let nlsyqsXData = ['25岁-30岁', '30岁-35岁', '35岁-40岁', '40岁-45岁', '45岁以上']
let nlsyqsSData = [100, 140, 230, 100, 130]

//性别教学率对比
let xbjxlXData = ['男', '女']
let xbjxlSData = [100, 140]

//学历教学时长
let xljxscXData = ['博士', '硕士', '本科', '专科', '高中', '初中']
let xljxscSData = [100, 140, 230, 100, 130, 200]

/** 职称使用时长趋势 */
let zcsyqsXData = ['正高级教师', '高级教师', '一级教师', '二级教师', '三级教师']
let zcsyqsSData = [12, 13, 10, 13, 9, 23]

const data = reactive({
    queryParams: {
        date: []
    }
});

const { queryParams } = toRefs(data);

/** 年龄使用趋势分析 */
const nlsyqsOption = reactive({
    options: {
        title: {
            text: '年龄使用时长趋势分析',
            textStyle: {
                fontSize: transformSize(28)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            bottom: '15%',
            height: '55%',
            left: '5%',
            right: '4%'
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: [],
            type: 'line',
            itemStyle: {
                color: '#cbc547'
            }
        }]
    },
    init: false
})
//性别教学率对比
const xbjxlOption = reactive({
    options:
    {
        title: {
            text: '性别教学率对比',
            textStyle: {
                fontSize: transformSize(28)
            },
            left: '2%',
            top: '3%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            height: '55%',
            bottom: '10%',
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value',
        },
        series: [
            {
                name: '教学率',
                type: 'bar',
                barMaxWidth: '30%',
                data: [],
                itemStyle: {
                    color: '#f05b72'
                }
            },
        ]
    },
    init: false
})
//学历教学时长对比
const xljxscOption = reactive({
    options:
    {
        title: {
            text: '学历教学时长对比',
            textStyle: {
                fontSize: transformSize(28)
            },
            left: '2%',
            top: '3%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'cross',
                crossStyle: {
                    color: '#999'
                }
            }
        },
        grid: {
            height: '55%',
            bottom: '10%'
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value',
        },
        series: [
            {
                name: '教学时长',
                type: 'bar',
                barMaxWidth: '40%',
                data: [],
                itemStyle: {
                    color: '#fcaf17'
                }
            },
        ]
    },
    init: false
})
/** 职称使用时长趋势分析 */
const zcsyqsOption = reactive({
    options: {
        title: {
            text: '职称使用时长趋势分析',
            textStyle: {
                fontSize: transformSize(28)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        legend: {
            data: [],
            right: '4%',
            top: '4%'
        },
        grid: {
            bottom: '15%',
            height: '55%',
            left: '5%',
            right: '4%'
        },
        xAxis: {
            type: 'category',
            data: []
        },
        yAxis: {
            type: 'value'
        },
        series: [{
            data: [],
            type: 'line',
            itemStyle: {
                color: '#00ae9d'
            }
        }]
    },
    init: false
})

let opt1 = nlsyqsOption.options
let opt2 = xbjxlOption.options
let opt3 = xljxscOption.options
let opt4 = zcsyqsOption.options

/** 获取数据 */
function setDatas() {
    opt1.xAxis.data = nlsyqsXData
    opt1.series[0].data = nlsyqsSData

    opt2.xAxis.data = xbjxlXData
    opt2.series[0].data = xbjxlSData

    opt3.xAxis.data = xljxscXData
    opt3.series[0].data = xljxscSData

    opt4.xAxis.data = zcsyqsXData
    opt4.series[0].data = zcsyqsSData
}

async function getDatas() {
    await queDeviceRatio().then(response => {
        let { ratio, useTime } = response.data
        deviceConfig.value.teachTime = useTime
        deviceConfig.value.teachRatio = (ratio * 1).toFixed(2) || 0
    })
    await queAgeUseTime().then(response => {
        if (response.data.ageUseTimes.length > 0) {
            nlsyqsXData = [], nlsyqsSData = []
            response.data.ageUseTimes.map((item, index) => {
                nlsyqsXData[index] = item.age
                nlsyqsSData[index] = item.useTime * 1 || 0
            })
            opt1.xAxis.data = nlsyqsXData
            opt1.series[0].data = nlsyqsSData
        }
    })
    await queTeacherSexUseTime().then(response => {
        if (response.data.length > 0) {
            xbjxlXData = [], xbjxlSData = []
            response.data.map((item, index) => {
                xbjxlXData[index] = item.gender
                xbjxlSData[index] = item.useTime || 0
            })
            opt2.xAxis.data = xbjxlXData
            opt2.series[0].data = xbjxlSData
        }
    })
    await queEducationUseTime().then(response => {
        if (response.data.timeList.length > 0) {
            xljxscXData = [], xljxscSData = []
            response.data.timeList.map((item, index) => {
                xljxscXData[index] = item.education
                xljxscSData[index] = item.useTime || 0
            })
            opt3.xAxis.data = xljxscXData
            opt3.series[0].data = xljxscSData
        }
    })
    await quePostUseTime().then(response => {
        if (response.data.postTimeList.length > 0) {
            zcsyqsXData = [], zcsyqsSData = []
            response.data.postTimeList.map((item, index) => {
                zcsyqsXData[index] = item.post
                zcsyqsSData[index] = item.useTime * 1 || 0
            })
            opt4.xAxis.data = zcsyqsXData
            opt4.series[0].data = zcsyqsSData
        }
    })
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.pageNum = 1;
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(28)
}

function injectOption() {
    proxy.nlsyqs = nlsyqsOption
    proxy.xbjxl = xbjxlOption
    proxy.xljxsc = xljxscOption
    proxy.zcsyqs = zcsyqsOption
}

injectOption()

onMounted(() => {
    setDatas()
    getDatas()
})
</script>

<style lang="scss" scoped>
.eBox {
    padding: 20px 0;
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-template-rows: 42vh 40vh 40vh;
    grid-gap: 4vh 1vw;

    &-top {
        grid-column-start: 1;
        grid-column-end: 5;
        display: grid;
        grid-template-columns: repeat(3, 1fr);
        grid-template-rows: 20vh 20vh;
        grid-gap: 2.5vh 1vw;
    }

    &-opt,
    &-item {
        border: 1px solid #e8e8e8;
    }

    .opt1>.icon {
        background-color: #91cc75;
    }

    .opt2>.icon {
        background-color: #5470c6;
    }

    .opt3>.icon {
        background-color: #4095e5;
    }

    .opt4>.icon {
        background-color: #fac858;
    }

    &-opt {
        display: flex;

        .icon {
            width: 33%;
            height: 100%;
            display: flex;
            padding: 0 1.8vw;
            color: #fff;
        }

        .info {
            width: 60%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: #333;

            .name,
            .value {
                font-size: 1.6vw;
            }

            .value {
                margin-top: 1vh;
                font-weight: bold;
            }
        }


    }

    .item1 {
        grid-row-start: span 2;
        grid-column-start: 2;
        grid-column-end: 4;
    }

    .item2 {
        grid-column-start: 1;
        grid-column-end: 3;
    }

    .item3 {
        grid-column-start: 3;
        grid-column-end: 5;
    }

    .item4 {
        grid-column-start: 1;
        grid-column-end: 5;
    }
}
</style>
