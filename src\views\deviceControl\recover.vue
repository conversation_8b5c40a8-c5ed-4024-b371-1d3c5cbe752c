<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <!-- <el-button @click="handleBack"
            >返回{{ route.query.type ? "工作" : "服务" }}台</el-button
          > -->
        </div>
      </template>

      <div class="flex_container">
        <!-- <div class="monitor-position flex_1">
          <div class="monitor-title">安装位置筛选</div>
          <el-scrollbar :max-height="500">
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              show-checkbox
              node-key="id"
              expand-on-click-node
              default-expand-all
              :props="positionProps"
              @check="treeChange"
              ref="treeRef"
            />
          </el-scrollbar>
        </div> -->
        <locateSel
          class="monitor-position flex_1"
          ref="locateRef"
          @getPosition="getPosition"
          :maxHeight="posHeight"
          @handleQuery="handleQuery"
        />
        <div class="flex_2" ref="flexRightBox">
          <div class="filter-container">
            <div class="left-filters">
              <el-select
                v-model="queryParams.isOff"
                placeholder="请选择状态"
                clearable
                style="width: 200px"
                @change="
                  () => {
                    queryParams.current = 1;
                    getList();
                  }
                "
              >
                <el-option label="开机" :value="0" />
                <el-option label="关机" :value="1" />
              </el-select>
              <el-button icon="refresh" @click="reset">重置</el-button>
            </div>

            <div class="right-buttons">
              <el-button
                type="danger"
                icon="RefreshRight"
                plain
                :disabled="noBatch || !tableAllSelectedId.length"
                @click.stop="handleBatchRemote"
                >批量重启</el-button
              >
              <el-button
                type="primary"
                icon="HelpFilled"
                plain
                :disabled="noBatch || !tableAllSelectedId.length"
                @click.stop="handleConfirm(0)"
                >批量冻结</el-button
              >
              <el-button
                type="success"
                icon="Help"
                plain
                :disabled="noBatch || !tableAllSelectedId.length"
                @click.stop="handleConfirm(1)"
                >批量解冻</el-button
              >
            </div>
          </div>

          <!-- 表格区域 -->
          <el-table
            v-loading="loading"
            :data="tableData"
            border
            ref="tableRef"
            highlight-current-row
            @row-click="rowClick"
            @selection-change="selectionChange"
            @select="onTableSelect"
            @select-all="selectSingleTableAll"
            style="width: 100%"
          >
            <el-table-column
              type="selection"
              min-width="55"
              show-overflow-tooltip
            />
            <el-table-column
              prop="deviceCode"
              label="设备编码"
              align="center"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="deviceName"
              label="设备名称"
              align="center"
              min-width="120"
              show-overflow-tooltip
            />
            <el-table-column
              prop="isOff"
              label="开机状态"
              align="center"
              min-width="120"
              show-overflow-tooltip
            >
              <template #default="{ row, $index }">
                <span>{{ row.isOff == 0 ? "开机" : "关机" }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="internetStatus"
              label="网络状态"
              align="center"
              min-width="120"
              show-overflow-tooltip
            >
              <template #default="{ row, $index }">
                <span>{{ internetStatusObj[row.internetStatus].label }}</span>
              </template>
            </el-table-column>
            <el-table-column
              prop="installAddress"
              label="安装位置"
              align="center"
              min-width="150"
              show-overflow-tooltip
            >
              <template #default="{ row, $index }">
                {{ row.installAddress || "-" }}
              </template>
            </el-table-column>
            <el-table-column
              label="冰冻状态"
              align="center"
              min-width="80"
              show-overflow-tooltip
            >
              <template #default="scope">
                <div class="freeze-status">
                  <el-tag
                    effect="light"
                    :type="statusObj[scope.row.winStatus].type"
                    >{{ statusObj[scope.row.winStatus].label }}</el-tag
                  >
                  <!-- <el-tag
                    :type="scope.row.freezeStatus.C ? 'primary' : 'info'"
                    effect="plain"
                    >C盘</el-tag
                  >
                  <el-tag
                    :type="scope.row.freezeStatus.D ? 'primary' : 'info'"
                    effect="plain"
                    >D盘</el-tag
                  > -->
                </div>
              </template>
            </el-table-column>
            <el-table-column
              label="操作"
              class-name="fixedRightBorder"
              align="center"
              fixed="right"
              width="150"
            >
              <template #default="scope">
                <el-button
                  link
                  type="primary"
                  :disabled="
                    scope.row.isOff == 1 ||
                    scope.row.winStatus == 0 ||
                    scope.row.winStatus > 1
                  "
                  @click.stop="handleConfirm(0, scope.row)"
                  icon="HelpFilled"
                  >冰冻</el-button
                >
                <el-button
                  link
                  type="success"
                  :disabled="
                    scope.row.isOff == 1 ||
                    scope.row.winStatus == 1 ||
                    scope.row.winStatus > 1
                  "
                  icon="Help"
                  @click.stop="handleConfirm(1, scope.row)"
                  >解冻</el-button
                >
              </template>
            </el-table-column>
          </el-table>

          <el-pagination
            :total="total"
            v-model:current-page="queryParams.current"
            v-model:page-size="queryParams.size"
            @current-change="getList"
            layout="total, prev, pager, next"
            background
            class="custom-pagination"
          />
        </div>
      </div>

      <el-dialog
        class="custom-dialog"
        v-model="dialogVisible"
        :title="dialogType === 'freeze' ? '冰冻' : '解冻'"
        width="400px"
      >
        <div class="disk-selection">
          <div class="disk-item">
            <el-checkbox
              v-model="diskSelection.C"
              :disabled="
                dialogType === 'freeze' ? diskSelection.D : diskSelection.D
              "
              @change="handleDiskCSelection"
            >
              <div class="disk-tag">
                C盘
                <div class="check-icon" v-show="diskSelection.C">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
            </el-checkbox>
          </div>
          <div class="disk-item">
            <el-checkbox
              v-model="diskSelection.D"
              @change="handleDiskDSelection"
              :disabled="dialogType === 'unfreeze' ? diskSelection.C : false"
            >
              <div class="disk-tag">
                D盘
                <div class="check-icon" v-show="diskSelection.D">
                  <el-icon><Check /></el-icon>
                </div>
              </div>
            </el-checkbox>
          </div>
        </div>
        <template #footer>
          <span class="dialog-footer">
            <el-button
              class="confirm-btn"
              type="primary"
              @click="handleConfirm"
              v-throttle
              >{{ dialogType === "freeze" ? "冰冻" : "解冻" }}</el-button
            >
            <el-button class="cancel-btn" @click="dialogVisible = false"
              >取消</el-button
            >
          </span>
        </template>
      </el-dialog>

      <!-- 今日提醒弹窗 -->
      <el-dialog
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        v-model="todayDialogVisible"
        width="350px"
        append-to-body
        :show-close="false"
        align-center
        title=""
      >
        <template #default>
          <div class="tipText">
            <div class="tipText-center">
              若进行冰点还原操作，设备将会自动重启，请确认设备空闲
            </div>
            <el-checkbox v-model="isSkip">
              <div class="tipText-check">今日不再提醒</div>
            </el-checkbox>
          </div>
        </template>
        <template #footer>
          <div class="tipText-footer">
            <el-button type="primary" @click="handleToday">
              我已知晓
            </el-button>
          </div>
        </template>
      </el-dialog>
    </el-card>
  </div>
</template>

<script setup name="recover">
import locateSel from "../deviceManage/components/locateSel.vue";
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
} from "vue";
import { Check } from "@element-plus/icons-vue";
import { getPositionTree } from "@/api/mediaTeach/position";
import { devicePage } from "@/api/mediaTeach/ledger";
import { debounce } from "@/utils/debounce";
import {
  postWinDDK,
  addSchoolDeviceLog,
  deviceCtl,
  deviceCtlMqtt,
  checkRemind,
  skipToday,
} from "@/api/deviceControl";
import {
  findIndexInObejctArr,
  sendPointRequest,
  sendPointRequestBatch,
} from "@/utils";
import { ElMessage } from "element-plus";

const route = useRoute();
const { proxy } = getCurrentInstance();

let resizeObserver = null;
const posHeight = ref("calc(100vh - 268px)");
const flexRightBox = ref(null);
const positionList = ref([]);
const positionTreeList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
});
const state = reactive({
  internetStatusObj: {
    0: {
      label: "良好",
    },
    1: {
      label: "较差",
    },
    2: {
      label: "离线",
    },
  },
  isSkip: false,
  todayDialogVisible: false,
  refreshTimer: null,
  addTimer: null,
  addSecond: 0,
  noBatch: false,
  tableRef: null,
  loading: false,
  total: 0,
  isBatch: false,
  curRow: null,
  curVal: 1,
  queryParams: {
    current: 1,
    size: 10,
    typeId: "1",
    smartScreen: 1,
    status: [],
    positionIds: [],
  },
  locationOptions: [{ label: "教学楼A二楼二年三班", value: "A2-2-3" }],
  tableData: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  tableData_all: [], // 表格的全部数据
  dialogVisible: false,
  dialogType: "freeze",
  currentRow: null,
  diskSelection: {
    C: false,
    D: false,
  },
  statusObj: {
    0: {
      label: "冰冻",
      type: "primary",
    },
    1: {
      label: "解冻",
      type: "success",
    },
    2: {
      label: "正在冰冻",
      type: "info",
    },
    3: {
      label: "正在解冻",
      type: "info",
    },
  },
});

const {
  internetStatusObj,
  curVal,
  curRow,
  isSkip,
  todayDialogVisible,
  refreshTimer,
  addTimer,
  addSecond,
  noBatch,
  statusObj,
  tableRef,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableRadio,
  tableData_all,
  loading,
  isBatch,
  queryParams,
  total,
  locationOptions,
  tableData,
  selectedRows,
  dialogVisible,
  dialogType,
  diskSelection,
} = toRefs(state);

const handleToday = () => {
  const { arr, rows } = checkBatch(curVal.value, "winStatus");
  if (isSkip.value) {
    skipToday();
  }
  console.log(isSkip.value, "isSkip");
  todayDialogVisible.value = false;
  handlePostWinDDK(curVal.value, arr, curRow.value, rows);
};

/** 批量操作前的检测 */
const checkBatch = (val, valName) => {
  let newArr = [];
  state.tableAllSelectedId.map((item) => {
    state.tableData_all.map((item2) => {
      if (item2.deviceId == item) {
        newArr.push(item2);
      }
    });
  });
  let arr = [],
    flag = false,
    flag2 = false,
    rows = [];
  newArr.map((item) => {
    if (item.isOff == 1 || item.winStatus > 1) {
      flag = true;
    } else if (valName && item[valName] == val) {
      flag2 = true;
    } else {
      arr.push(item.deviceName);
      rows.push(item);
    }
  });
  return { arr, rows, flag, flag2 };
};

async function handleConfirm(val, row) {
  const { flag, arr, rows, flag2 } = checkBatch(val, "winStatus");
  curRow.value = row;
  curVal.value = val;

  if (flag && !row) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }

  if (flag2 && !row) {
    proxy.$modal.msgWarning(
      `设备存在${!!val ? "解冻" : "冰冻"}状态，无法进行此操作`
    );
    return;
  }

  checkRemind().then((res) => {
    console.log(res, "checkRemind");
    if (res.data) {
      todayDialogVisible.value = true;
    } else {
      handlePostWinDDK(val, arr, row, rows);
    }
  });
}

function handlePostWinDDK(val, arr, row, rows) {
  let deviceCodeList = !!row
    ? [row.deviceCode]
    : rows.map((item) => item.deviceCode);

  proxy.$modal
    .confirm(
      `是否确认${!!row ? "" : "批量"}${!!val ? "解冻" : "冰冻"}设备名称为【${
        !!row ? row.deviceName : arr
      }】的设备？`
    )
    .then(async function () {
      console.log(
        {
          deviceCodeList,
          winDDKStatus: val,
        },
        "冰点还原传参"
      );
      proxy.$modal.loading();
      addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: deviceCodeList.join("、") || "", // 操作设备编号
        logContent: `${!!val ? "解冻" : "冻结"}`, // 日志内容
        deviceNum: deviceCodeList.length, // 操作设备数量
      });

      let timer = setTimeout(() => {
        postWinDDK({
          deviceCodeList,
          winDDKStatus: val,
        })
          .then((resp) => {
            proxy.$modal.msgSuccess(`已成功发送${!!val ? "解冻" : "冰冻"}指令`);
            let sendData = {
              userEvents: deviceCodeList.map((item) => {
                return {
                  event: "Click",
                  eventDescribe: `点击${!!val ? "解冻" : "冰冻"}功能`,
                  content: "",
                  num: 1,
                  deviceCode: item,
                };
              }),
            };
            console.log(sendData, "批量/单个埋点");
            sendPointRequestBatch(sendData);
            getList();
          })
          .catch((e) => {
            console.log(e, "冰点还原错误提示");
            if (e.indexOf("离线") != -1 || e.indexOf("不具备") != -1) {
              proxy.$modal.alert(e);
              return;
            }
            proxy.$modal.alert("冰冻/解冻操作失败，请重试");
          })
          .finally(() => proxy.$modal.closeLoading());
        clearTimeout(timer);
      }, 3000);
    })
    .catch(() => {});
}

function getList(load = true) {
  load && (loading.value = true);

  let obj = {
    ...queryParams.value,
  };
  //   obj.isOff ? (obj.isOff = 1) : delete obj?.isOff;
  console.log(obj, "冰点还原");
  //   proxy.$modal.loading();
  devicePage(obj)
    .then((res) => {
      const { page, abnormalTerminal } = res.data;
      // proxy.$modal.closeLoading();
      console.log(res, "冰点还原");
      tableData.value = page.records;
      total.value = page.total;

      nextTick(() => {
        tableData.value.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item.deviceId) > -1) {
            tableRef.value?.toggleRowSelection(item, true);
          } else {
            tableRef.value?.toggleRowSelection(item, false);
          }
        });
      });
    })
    .finally(() => {
      loading.value = false;
    });

  devicePage({
    ...obj,
    current: 1,
    size: 9999999,
  }).then((res) => {
    console.log("所有设备数据", res);
    state.tableData_all = res.data.page.records.filter(
      (_) => _.deviceType == "智慧大屏"
    );
    console.log(state.tableData_all);
  });
}

onMounted(() => {
  refreshTimer.value = setInterval(() => {
    getList();
  }, 10000);
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  // getPositionTreeList();
  getList();

  resizeObserver = new ResizeObserver((entries) => {
    let pageHeight = document.documentElement.clientHeight - 215;
    for (let entry of entries) {
      let val =
        (pageHeight > Math.round(entry.contentRect.height)
          ? pageHeight
          : Math.round(entry.contentRect.height)) -
        proxy.$refs.locateRef.topDivRef.offsetHeight;
      posHeight.value = val + "px";
    }
  });
  if (flexRightBox.value) {
    let pageHeight = document.documentElement.clientHeight - 215;
    resizeObserver.observe(flexRightBox.value);
    // 获取初始高度
    let val =
      (pageHeight >
      Math.round(flexRightBox.value.getBoundingClientRect().height)
        ? pageHeight
        : Math.round(flexRightBox.value.getBoundingClientRect().height)) -
      proxy.$refs.locateRef.topDivRef.offsetHeight;
    posHeight.value = val + "px";
  }
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览冰点还原页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  clearInterval(refreshTimer.value);
  if (resizeObserver && flexRightBox.value) {
    resizeObserver.unobserve(flexRightBox.value);
  }
});

function getUrlIp(ip) {
  let str = "http",
    ws = "ws",
    href = window.location.href.split(":");
  let ip1 = ip,
    url1 = ip;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `http://` + ip1;
  }
  ws = href[0] == "https" ? "wss" : "ws";
  return { ip1, url1 };
}

const handleRemote = async (row) => {
  proxy.$modal.loading();
  let { ip1 } = getUrlIp(row.ipAddress);
  let data = { cmd: 1 };

  let obj = {
    method: "post",
    uri: ip1 + `/api/Cockpit/Control`,
    content: JSON.stringify(data),
  };
  console.log("重启传参", obj);
  let res = null;
  if (row.isMqtt) {
    obj = {
      ...obj,
      uri: `/api/Cockpit/Control`,
      deviceCodeList: row.deviceCode,
    };
    res = await deviceCtlMqtt(obj);
  } else {
    res = await deviceCtl(obj);
  }
  console.log("传参", obj);
  if (res.code == 200) {
    ElMessage.closeAll();
    proxy.$modal.msgSuccess("操作成功");
    getList();
  }
  proxy.$modal.closeLoading();
};

const handleBatchRemote = () => {
  const { flag, arr, rows } = checkBatch();

  if (flag) {
    proxy.$modal.msgWarning(
      "设备存在关机状态，无法进行此操作"
    );
    return;
  }
  proxy.$modal
    .confirm("是否确认批量重启设备名称为【" + arr + '"】的设备？')
    .then(async function () {
      console.log(rows, "选中的行");
      let sendData = {
        userEvents: rows.map((item) => {
          return {
            event: "Click",
            eventDescribe: "选中设备后点击下方悬浮按钮集控",
            content: 2,
            num: 1,
            deviceCode: item.deviceCode,
          };
        }),
      };
      console.log(sendData, "批量埋点");
      sendPointRequestBatch(sendData);
      addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: rows.map((item) => item.deviceCode)?.join("、") || "", // 操作设备编号
        logContent: `远程重启`, // 日志内容
        deviceNum: rows.length, // 操作设备数量
      });
      for (let j = 0; j < rows.length; j++) {
        try {
          handleRemote(rows[j]);
        } catch (error) {}
      }
    })
    .catch(() => {});
};

function getPosition(d) {
  state.positionNodeList = d.positionNodeList;
  positionList.value = d.positionList;
}

// 重置
const reset = debounce(() => {
  queryParams.value = {
    current: 1,
    size: 10,
    typeId: "1",
    smartScreen: 1,
    status: [],
    positionIds: [],
  };
  proxy.$refs.locateRef.resetQuery();
  // proxy.$refs.treeRef.setCheckedNodes([]);
  // tableAllSelectedId.value = []; // 点击查询按钮后，保存的勾选的id要清空
  // tableAllSelectedRow.value = []; // 点击查询按钮后，保存的勾选的数据要清空
  // getList();
}, 250);

function handleQuery(d) {
  if (d?._checkList) {
    queryParams.value.positionIds = d?._checkList;
  }
  queryParams.value.current = 1;
  tableAllSelectedId.value = []; // 点击查询按钮后，保存的勾选的id要清空
  tableAllSelectedRow.value = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

// 安装位置筛选
function treeChange(data, obj) {
  console.log(data, obj.checkedKeys);
  queryParams.value.positionIds = obj.checkedKeys;
  getList();
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    positionTreeList.value = response.data;
    // positionList.value = treeToArray(response.data);
    // data.positionNodeList = addAttr(tree);
    // console.log("positionList ==>", positionList.value);
  });
}

function handleBack() {
  proxy.$tab.closeOpenPage(route.query.type ? "/work" : "/service");
}

// 冰冻
function handleFreeze(row) {
  console.log(row);
  isBatch.value = false;
  state.dialogType = "freeze";
  state.currentRow = row;
  state.diskSelection = { C: false, D: false };
  state.dialogVisible = true;
  proxy.$refs.tableRef.clearSelection();
}

function handleUnfreeze(row) {
  isBatch.value = false;
  state.dialogType = "unfreeze";
  state.currentRow = row;
  state.diskSelection = { C: false, D: false };
  state.dialogVisible = true;
  proxy.$refs.tableRef.clearSelection();
}

function handleConfirm2() {
  //   if (!state.currentRow) return;

  if (isBatch.value) {
    // 批量
    console.log("批量");
    let rows = proxy.$refs.tableRef.getSelectionRows();
    if (state.dialogType == "freeze") {
      console.log(rows, "冰冻");
      for (let i = 0; i < rows.length; i++) {
        postWinDDK({
          deviceCode: rows[i].deviceCode,
          winDDKStatus: 0, // 0-冰冻 1-解冻
        })
          .then((res) => {
            console.log(res, "winDDK");
            // 关闭对话框并重置选择
            state.dialogVisible = false;
            state.diskSelection = { C: false, D: false };
            state.currentRow = null;
            if (i == rows.length - 1) {
              proxy.$modal.msgSuccess("冰冻成功");
            }
          })
          .catch((err) => {});
      }
    } else {
      console.log(rows, "解冻");

      for (let i = 0; i < rows.length; i++) {
        postWinDDK({
          deviceCode: rows[i].deviceCode,
          winDDKStatus: 1, // 0-冰冻 1-解冻
        })
          .then((res) => {
            console.log(res, "winDDK");
            // 关闭对话框并重置选择
            state.dialogVisible = false;
            state.diskSelection = { C: false, D: false };
            state.currentRow = null;
            if (i == rows.length - 1) {
              proxy.$modal.msgSuccess("解冻成功");
            }
          })
          .catch((err) => {});
      }
    }
  } else {
    console.log("单个");

    if (state.dialogType == "freeze") {
      console.log(state.currentRow, "冰冻");
      proxy.$modal.loading();
      postWinDDK({
        deviceCode: state.currentRow.deviceCode,
        winDDKStatus: 0, // 0-冰冻 1-解冻
      })
        .then((res) => {
          proxy.$modal.closeLoading();
          console.log(res, "winDDK");
          // 关闭对话框并重置选择
          state.dialogVisible = false;
          state.diskSelection = { C: false, D: false };
          state.currentRow = null;
          proxy.$modal.msgSuccess("冰冻成功");
        })
        .catch((err) => {
          proxy.$modal.closeLoading();
        });
    } else {
      console.log("解冻");
      proxy.$modal.loading();
      postWinDDK({
        deviceCode: state.currentRow.deviceCode,
        winDDKStatus: 1, // 0-冰冻 1-解冻
      })
        .then((res) => {
          console.log(res, "winDDK");
          proxy.$modal.closeLoading();
          // 关闭对话框并重置选择
          state.dialogVisible = false;
          state.diskSelection = { C: false, D: false };
          state.currentRow = null;
          // 显示操作成功提示
          proxy.$modal.msgSuccess("解冻成功");
        })
        .catch((err) => {
          proxy.$modal.closeLoading();
        });
    }
  }
}

function handleDiskDSelection(checked) {
  if (state.dialogType === "freeze" && checked) {
    state.diskSelection.C = true;
  }
}

function handleDiskCSelection(checked) {
  if (state.dialogType === "unfreeze" && checked) {
    state.diskSelection.D = true;
  }
}

// 添加批量冻结函数
function handleBatchFreeze() {
  if (!state.tableAllSelectedId.length) {
    proxy.$modal.msgError("请选择要操作的设备");
    return;
  }
  state.dialogType = "freeze";
  isBatch.value = true;

  state.dialogVisible = true;
  state.diskSelection = { C: false, D: false };
  state.currentRow = null;

  //   for (let i = 0; i < rows.length; i++) {

  //     postWinDDK({
  //       deviceCode: state.currentRow.deviceCode,
  //       winDDKStatus: 1, // 0-冰冻 1-解冻
  //     })
  //       .then((res) => {
  //         console.log(res, "winDDK");
  //         proxy.$modal.closeLoading();
  //         // 关闭对话框并重置选择
  //         // state.dialogVisible = false;
  //         // state.diskSelection = { C: false, D: false };
  //         // state.currentRow = null;
  //         // 显示操作成功提示
  //         proxy.$modal.msgSuccess("解冻成功");
  //       })
  //       .catch((err) => {
  //         proxy.$modal.closeLoading();
  //       });

  //   }

  // 更新选中行的冰冻状态
  //   state.tableAllSelectedRow.forEach((row) => {
  //     row.freezeStatus = {
  //       C: true,
  //       D: true,
  //     };
  //   });

  //   // 强制更新表格
  //   state.tableData = [...state.tableData];

  //   // 显示操作成功提示
  //   proxy.$modal.msgSuccess("批量冻结成功");
}

// 添加批量解冻函数
function handleBatchUnfreeze() {
  if (!state.tableAllSelectedId.length) {
    proxy.$modal.msgError("请选择要操作的设备");
    return;
  }
  state.dialogType = "unfreeze";
  isBatch.value = true;

  state.dialogVisible = true;
  state.diskSelection = { C: false, D: false };
  state.currentRow = null;

  //   // 更新选中行的冰冻状态
  //   state.tableAllSelectedRow.forEach((row) => {
  //     row.freezeStatus = {
  //       C: false,
  //       D: false,
  //     };
  //   });

  //   // 强制更新表格
  //   state.tableData = [...state.tableData];

  //   // 显示操作成功提示
  //   proxy.$modal.msgSuccess("批量解冻成功");
}

/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      "deviceId"
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row.deviceId);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      state.tableAllSelectedId.push(item.deviceId);
      state.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row.deviceId);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableData;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableData_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        state.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', state.tableData_all)
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}
</script>

<style lang="scss" scoped>
:deep(.fixedRightBorder) {
  border-left: 1px solid #ebeef5;
}
.filter-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 12px;
  flex-wrap: wrap;
  gap: 10px 0;
  .left-filters {
    display: flex;
    gap: 10px;
    flex: 1;
  }

  .right-buttons {
    display: flex;
    gap: 10px 0;
  }
}

.freeze-status {
  display: flex;
  justify-content: center;
  gap: 10px;
}

:deep(.el-tag) {
  min-width: 60px;

  &.el-tag--plain {
    &.el-tag--primary {
      background-color: #409eff;
      border-color: #409eff;
      color: #fff;
    }

    &.el-tag--info {
      background-color: #f0f2f5;
      border-color: #d9d9d9;
      color: #909399;
    }
  }
}

.disk-selection {
  display: flex;
  gap: 50px;
  justify-content: center;
  margin: 30px 0;

  .disk-item {
    :deep(.el-checkbox) {
      margin-right: 0;
      height: auto;

      .el-checkbox__input {
        display: none;
      }

      .el-checkbox__label {
        padding: 0;
      }
    }
  }

  .disk-tag {
    position: relative;
    background-color: #7367f0;
    color: white;
    padding: 8px 30px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;

    .check-icon {
      position: absolute;
      top: -8px;
      right: -8px;
      background-color: #000;
      border-radius: 50%;
      padding: 2px;

      .el-icon {
        font-size: 12px;
        color: white;
      }
    }
  }
}

:deep(.el-dialog) {
  border-radius: 8px;

  .el-dialog__header {
    // margin: 0;
    // padding: 20px;
    // border-bottom: 1px solid #eee;
  }

  /* .el-dialog__body {
        padding: 20px;
    } */

  .el-dialog__footer {
    // padding: 20px;
    // border-top: 1px solid #eee;
  }
}

.dialog-footer {
  width: 100%;
  // display: flex;
  // justify-content: center;
  // gap: 20px;

  .cancel-btn {
    width: 70px;
  }

  .confirm-btn {
    width: 70px;
    // background: #7367f0;
    // border: 1px solid #7367f0;

    // &:hover {
    //   background: darken(#7367f0, 10%);
    // }
  }
}

.flex_container {
  display: flex;
  align-items: flex-start;
  gap: 10px;
  min-height: calc(100vh - 215px);
  .monitor-title {
    font-size: 16px;
    padding: 10px 20px;
  }
  .flex_1 {
    width: 18%;
    margin-right: 10px;
    // border: 1px solid #f1f1f1;
    // border: 1px solid #dcdfe6;
    :deep(.treeNode) {
      .el-tree-node__content {
        height: auto;
      }
      .el-tree-node__label {
        white-space: wrap;
        padding: 3px 3px 3px 0;
      }
    }
  }
  .flex_2 {
    width: 82%;
    min-width: 400px;
  }
}

.custom-pagination {
  margin-top: 20px;
  display: flex;
  justify-content: end;
}

.tipText {
  padding: 0 15px;
  &-center {
    text-align: center;
    font-weight: bold;
    margin-bottom: 20px;
  }
  &-check {
    display: flex;
    align-items: center;
    padding-left: 5px;
    font-size: 13px;
    color: #606266;
  }
  &-footer {
    padding-top: 0;
    text-align: center;
  }

  :deep(.el-checkbox__label) {
    padding-left: 0;
  }

  :deep(.el-checkbox__inner) {
    border-radius: 50%;
  }
}
</style>
