<!-- views/emergency/workOrderHandle.vue -->
<template>
  <div class="taskInfo">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <!-- 预案信息 -->
      <el-descriptions title="预案信息" border :column="3" v-if="planInfo">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案编号"
        >
          {{ planInfo?.code || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案类型"
        >
          {{ planInfo?.type || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案文件"
        >
          <el-button
            v-if="planInfo?.fileUrl"
            type="primary"
            @click="downloadPlanFile"
          >
            点击查看
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案名称"
        >
          {{ planInfo?.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          :span="2"
          label="预案内容"
        >
          {{ planInfo?.content || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="修改时间"
        >
          {{ planInfo?.updateTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ planInfo?.createTime || "-" }}
        </el-descriptions-item>
      </el-descriptions>

      <!-- 工单基础信息 -->
      <order-base-info
        :work-order-data="workOrderData"
        :show-report="true"
        :readonly="false"
        :is-processing="isProcessing"
        @update:work-order-data="workOrderData = $event"
        @uploadReport="uploadDialogVisible = true"
      >
        <!-- 自定义应急预案插槽 -->
        <template #emergencyPlan>
          {{ workOrderData.emergencyPlan || "-" }}
        </template>
        <!-- 添加自定义处理报告插槽 -->
        <template #report>
          <div class="report-container">
            <div class="upload-btn-wrapper">
              <el-button type="primary" @click="uploadDialogVisible = true">
                上传报告
              </el-button>
            </div>
            <div class="report-list">
              <template
                v-if="workOrderData.reports && workOrderData.reports.length > 0"
              >
                <div
                  v-for="(report, index) in workOrderData.reports"
                  :key="index"
                  class="report-item"
                >
                  <el-button
                    type="primary"
                    link
                    @click="downloadFile(report.fileUrl, report.fileName)"
                  >
                    {{ report.fileName }}
                  </el-button>
                </div>
              </template>
              <span v-else>-</span>
            </div>
          </div>
        </template>
      </order-base-info>

      <!-- 关联备件表格 -->
      <related-parts-table
        v-model:parts="relatedParts"
        :readonly="!isProcessing"
      >
        <template #operations>
          <el-button
            color="#bd3124"
            type="primary"
            @click="handleSelect(false)"
            v-if="!readonly && isProcessing"
          >
            点击选择关联备件
          </el-button>
        </template>
      </related-parts-table>

      <!-- 关联人员表格 -->
      <related-personnel-table :personnel="relatedPersonnel">
        <template #operations>
          <el-button
            color="#bd3124"
            type="primary"
            @click="handleSelect(true)"
            v-if="!readonly && isProcessing"
          >
            点击选择关联人员
          </el-button>
        </template>
      </related-personnel-table>

      <!-- 处理按钮区域 -->
      <div class="taskInfo-btns">
        <template v-if="isProcessing">
          <el-button
            type="primary"
            @click="submitProcessForm"
            style="margin-right: 40px"
            v-throttle
            >点击提交</el-button
          >
          <el-button @click="goBack">返回</el-button>
        </template>
      </div>
    </el-card>
    <!-- 选择应急预案对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="showEmergencyPlanDialog"
      title="选择应急预案"
      width="600px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-table
        :data="emergencyPlans"
        border
        style="width: 100%"
        @selection-change="handleEmergencyPlanSelectionChange"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column prop="id" label="预案编号" />
        <el-table-column prop="name" label="预案名称" />
        <el-table-column prop="type" label="预案类型">
          <template #default="scope">
            {{ getEmergencyPlanTypeName(scope.row.type) }}
          </template>
        </el-table-column>
      </el-table>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="showEmergencyPlanDialog = false">取 消</el-button>
          <el-button type="primary" @click="handleSelectEmergencyPlan"
            >确 定</el-button
          >
        </span>
      </template>
    </el-dialog>

    <!-- 选择备件/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      v-model="dialogVisible"
      :title="title"
      width="900px"
      destroy-on-close
      @close="handleDialogClose"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
        @submit.native.prevent
      >
        <el-form-item>
          <el-input
            v-model="queryParams.faultDesc"
            :placeholder="
              title === '请选择关联备件'
                ? '请输入备件编号/名称搜索'
                : '请输入工号/姓名搜索'
            "
            clearable
            style="width: 200px"
            @keyup.enter="handleSearch"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <div
        v-if="title === '请选择关联备件' && tableAllSelectedRow.length > 0"
        style="margin-bottom: 10px"
      >
        <span style="color: red">已选择备件：</span>
        <div class="t_item">
          <p v-for="(item, index) in tableAllSelectedRow" :key="index">
            {{ item.name }}
          </p>
        </div>
      </div>
      <div
        v-else-if="title === '请选择关联人员' && tableAllSelectedRow.length > 0"
        style="margin-bottom: 10px"
      >
        <span style="color: red">已选择人员：</span>
        <div class="t_item">
          <p v-for="(item, index) in tableAllSelectedRow" :key="index">
            {{ item.nickName }}
          </p>
        </div>
      </div>
      <el-table
        v-loading="loading"
        :data="ledgerList"
        border
        @selection-change="handleSelectionChange"
        @select="onTableSelect"
        @select-all="onTableSelectAll"
        @row-click="handleRowClick"
        :row-key="getRowKey"
        ref="tableRef"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="isSelectable"
        />
        <!-- 备件表格列 -->
        <template v-if="title === '请选择关联备件'">
          <el-table-column prop="code" label="备件编号" />
          <el-table-column prop="name" label="备件名称" />
          <el-table-column prop="typeName" label="备件类别" />
          <el-table-column prop="specifications" label="规格" />
          <el-table-column prop="stock" label="库存" />
        </template>
        <!-- 人员表格列 -->
        <template v-else>
          <el-table-column prop="workId" label="工号" />
          <el-table-column prop="nickName" label="姓名" />
          <el-table-column prop="sex" label="性别">
            <template #default="scope">
              {{ scope.row.sex === "0" ? "男" : "女" }}
            </template>
          </el-table-column>
          <el-table-column prop="deptName" label="所属部门" />
          <el-table-column prop="postName" label="岗位">
            <template #default="scope">
              {{ scope.row.postName || "-" }}
            </template>
          </el-table-column>
        </template>
      </el-table>

      <!-- 修改分页组件 -->
      <div class="pagination-container">
        <el-pagination
          v-show="total > 0"
          :total="total"
          v-model:current-page="queryParams.current"
          v-model:page-size="queryParams.size"
          layout="total, prev, pager, next"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
          :background="false"
          :autoScroll="false"
        />
      </div>

      <template #footer>
        <span class="dialog-footer">
          <el-button @click="dialogVisible = false">取 消</el-button>
          <el-button type="primary" @click="submitForm">确 定</el-button>
        </span>
      </template>
    </el-dialog>

    <!-- 上传报告对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="uploadDialogVisible"
      title="上传报告"
      width="400"
      :close-on-click-modal="false"
    >
      <div class="upload-content">
        <el-upload
          class="upload-demo"
          :action="uploadAction"
          :before-upload="beforeUpload"
          :on-change="handleFileChange"
          :file-list="fileList"
          :limit="1"
          :auto-upload="false"
          accept=".doc,.docx,.xls,.xlsx"
        >
          <el-button type="primary">点击上传文件</el-button>
          <template #tip>
            <div class="el-upload__tip">
              只允许上传word、excel格式文件，大小不超过20M
            </div>
          </template>
        </el-upload>
      </div>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="uploadDialogVisible = false">取消</el-button>
          <el-button type="primary" @click="confirmUpload">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
// 导入公共组件
import OrderBaseInfo from "@/views/emergencyManage/components/OrderBaseInfo.vue";
import RelatedPartsTable from "@/views/emergencyManage/components/RelatedPartsTable.vue";
import RelatedPersonnelTable from "@/views/emergencyManage/components/RelatedPersonnelTable.vue";
// 导入API
import {
  schoolPlanWorkOrderInfo,
  schoolPlanInfo,
  updateSchoolPlanWorkOrder,
  schoolPlanSpareInfo,
  uploadSchoolPlanWorkOrder, // 添加这个导入
} from "@/api/emergency";
import { sparePartsPage } from "@/api/spare";
import { getMaintainList } from "@/api/distribution/member";

const router = useRouter();
const route = useRoute();

// 页面状态
const readonly = ref(false);
const isProcessing = ref(true);

// 对话框显示状态
const showEmergencyPlanDialog = ref(false);
const dialogVisible = ref(false);
const title = ref("");

// 选中的数据
const selectedEmergencyPlan = ref(null);
const selectedParts = ref([]);
const selectedPersonnel = ref([]);

// 数据
const workOrderData = ref({});
const relatedParts = ref([]);
const relatedPersonnel = ref([]);
const emergencyPlans = ref([]);
const parts = ref([]);
const personnel = ref([]);

// 表格选择
const emergencyPlanSelection = ref([]);
const partsSelection = ref([]);
const personnelSelection = ref([]);

// 添加预案信息的响应式对象
const planInfo = ref({
  id: "",
  code: "",
  name: "",
  type: "",
  content: "",
  fileName: "",
  fileUrl: "",
  createTime: "",
  updateTime: "",
});

// 上传相关的响应式变量
const uploadDialogVisible = ref(false);
const fileList = ref([]);
const uploadAction =
  import.meta.env.VITE_APP_BASE_API +
  "/emergency/schoolPlan/uploadSchoolPlanWorkOrder";

// 添加新的响应式变量
const ledgerList = ref([]);
const total = ref(0);
const loading = ref(false);
const tableRef = ref(null);
const multipleSelection = ref([]);
const queryParams = ref({
  current: 1,
  size: 5,
  codeAndName: "",
  faultDesc: "",
  status: "0",
});

// 添加临时选择数据的响应式变量
const tempSelection = ref([]);

// 添加全局选中数据存储
const tableAllSelectedId = ref([]);
const tableAllSelectedRow = ref([]);

// 获取预案类型名称
const getEmergencyPlanTypeName = (type) => {
  const typeMap = {
    natural: "自然灾害",
    accident: "事故灾难",
    health: "公共卫生",
    security: "社会安全",
  };
  return typeMap[type] || type;
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "-";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 添加下载预案文件方法
const downloadPlanFile = () => {
  if (!planInfo.value?.fileUrl) {
    ElMessage.warning("预案文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let fileUrl = planInfo.value.fileUrl;
    if (fileUrl.startsWith("http:")) {
      fileUrl = fileUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = fileUrl;
    link.setAttribute("download", planInfo.value.fileName || "预案文件");
    link.setAttribute("target", "_blank"); // 添加target属性
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 获取预案详情
const getPlanDetail = async (planId) => {
  try {
    const res = await schoolPlanInfo(planId);
    if (res.code == 200) {
      const currentPlan = res.data;
      planInfo.value = {
        id: currentPlan.id || "",
        code: currentPlan.code || "",
        name: currentPlan.name || "",
        type: currentPlan.type || "",
        content: currentPlan.content || "",
        fileName: currentPlan.fileName || "",
        fileUrl: currentPlan.fileUrl || "",
        createTime: currentPlan.createTime || "",
        updateTime: currentPlan.updateTime || "",
      };
    } else {
      ElMessage.warning("未找到相关预案信息");
    }
  } catch (error) {
    console.error("获取预案详情失败:", error);
    ElMessage.error("获取预案详情失败");
  }
};

// 添加工单状态映射函数
const getStatusName = (status) => {
  const statusMap = {
    0: "待上传",
    1: "待归档",
    2: "已归档",
    pending: "待处理",
    waiting_upload: "待上传",
    waiting_archive: "待归档",
    archived: "已归档",
  };
  return statusMap[status] || status;
};

// 获取工单数据
const getWorkOrderData = () => {
  try {
    const orderId = route.query.id;
    if (!orderId) {
      ElMessage.error("工单ID不能为空");
      router.back();
      return;
    }

    schoolPlanWorkOrderInfo(orderId)
      .then((res) => {
        if (res.code === 200) {
          console.log("工单详情数据:", res.data);
          const {
            planWorkOrderInfo,
            planWorkOrderPeopleInfo,
            planInfo: responsePlanInfo,
          } = res.data;

          // 处理报告和上传人的显示逻辑
          let reports = [];
          let uploaderName = "-";

          // 如果创建者上传了报告，只显示创建者的报告
          if (
            planWorkOrderInfo.reportFileName &&
            planWorkOrderInfo.reportFileUrl
          ) {
            reports.push({
              fileName: planWorkOrderInfo.reportFileName,
              fileUrl: planWorkOrderInfo.reportFileUrl,
            });
            uploaderName = planWorkOrderInfo.uploadUser;
          } else {
            // 创建者未上传报告时，显示关联人的报告（如果有）
            const relatedPersonsWithReport =
              planWorkOrderPeopleInfo?.filter(
                (person) => person.reportFileName && person.reportFileUrl
              ) || [];

            if (relatedPersonsWithReport.length > 0) {
              relatedPersonsWithReport.forEach((person) => {
                reports.push({
                  fileName: person.reportFileName,
                  fileUrl: person.reportFileUrl,
                });
              });
              uploaderName = relatedPersonsWithReport
                .map((person) => person.nickName)
                .join("、");
            }
          }

          // 设置工单基础信息
          workOrderData.value = {
            orderId: planWorkOrderInfo.code,
            orderType: responsePlanInfo?.type || "-",
            description: planWorkOrderInfo.remark,
            emergencyPlan: responsePlanInfo?.name || "-",
            emergencyPlanId: responsePlanInfo?.id,
            status:
              planWorkOrderInfo.status == 0
                ? "待上传"
                : planWorkOrderInfo.status == 1
                ? "已归档"
                : "待上传",
            reports: reports.length > 0 ? reports : [],
            uploaderName: uploaderName,
            level: planWorkOrderInfo.level || "-",
            createTime: planWorkOrderInfo.createTime,
            handleTime: planWorkOrderInfo.processingTime,
          };

          // 设置预案信息
          if (responsePlanInfo) {
            planInfo.value = {
              id: responsePlanInfo.id,
              code: responsePlanInfo.code || "",
              name: responsePlanInfo.name || "",
              type: responsePlanInfo.type || "",
              content: responsePlanInfo.content || "",
              fileName: responsePlanInfo.fileName || "",
              fileUrl: responsePlanInfo.fileUrl || "",
              createTime: responsePlanInfo.createTime || "",
              updateTime: responsePlanInfo.updateTime || "",
            };
          }

          // 设置关联人员数据，添加报告相关字段
          if (planWorkOrderPeopleInfo && planWorkOrderPeopleInfo.length > 0) {
            relatedPersonnel.value = planWorkOrderPeopleInfo.map((person) => ({
              workerId: person.userId,
              workId: person.workId || "-",
              name: person.nickName,
              department: person.sysDept?.deptName || "-",
              relateTime: person.associatedTime,
              reportFileName: person.reportFileName || "",
              reportFileUrl: person.reportFileUrl || "",
              notifyTime: person.notifyTime,
            }));
          }

          // 获取关联备件数据
          getRelatedParts(orderId);
        } else {
          ElMessage.error(res.msg || "获取工单详情失败");
          router.back();
        }
      })
      .catch((error) => {
        console.error("获取工单详情失败:", error);
        ElMessage.error("获取数据失败");
        router.back();
      });
  } catch (error) {
    console.error("获取工单数据失败:", error);
    ElMessage.error("获取数据失败");
  }
};

// 修改获取关联备件数据的方法
const getRelatedParts = async (planWorkOrderId) => {
  const params = {
    planWorkOrderId,
    pageNum: 1,
    pageSize: 10,
  };

  try {
    const res = await schoolPlanSpareInfo(params);
    if (res.code === 200) {
      console.log("关联备件数据:", res.data);

      // 获取所有备件的详细信息，包括库存
      const partsDetails = await Promise.all(
        res.data.records.map(async (item) => {
          // 获取单个备件的详细信息
          const detailRes = await sparePartsPage({
            pageNum: 1,
            pageSize: 1,
            codeAndName: item.sparePartsCode,
          });
          return detailRes.code === 200 && detailRes.data.records.length > 0
            ? detailRes.data.records[0]
            : null;
        })
      );

      // 合并数据，添加库存信息
      relatedParts.value = res.data.records.map((item, index) => ({
        id: item.sparePartsId,
        partId: item.sparePartsCode,
        partName: item.name,
        quantity: item.num || 1,
        useDate: item.createTime,
        stock: Math.max(1, partsDetails[index]?.stock || 1), // 确保库存至少为1
      }));
    } else {
      ElMessage.warning(res.msg || "获取关联备件数据失败");
    }
  } catch (error) {
    console.error("获取关联备件数据失败:", error);
    ElMessage.warning("获取关联备件数据失败");
  }
};

// 获取应急预案数据
/* const getEmergencyPlans = () => {
  try {
    const plans = JSON.parse(localStorage.getItem('emergencyPlans') || '[]')
    if (plans.length === 0) {
      const defaultPlans = [
        {
          id: 'EP001',
          name: '设备故障应急预案',
          type: 'accident'
        },
        {
          id: 'EP002',
          name: '系统异常应急预案',
          type: 'security'
        }
      ]
      localStorage.setItem('emergencyPlans', JSON.stringify(defaultPlans))
      emergencyPlans.value = defaultPlans
    } else {
      emergencyPlans.value = plans
    }
  } catch (error) {
    console.error('获取应急预案数据失败:', error)
  }
} */

// 获取备件数据
/* const getParts = () => {
  try {
    const spareList = JSON.parse(localStorage.getItem('WHYWPT_SPARELIST_ALL') || '[]')
    if (spareList.length === 0) {
      const defaultSpares = [
        {
          spareNumber: 'SP001',
          spareName: '螺丝',
          spareStore: 100,
          updateTime: new Date().toISOString()
        },
        {
          spareNumber: 'SP002',
          spareName: '齿轮',
          spareStore: 50,
          updateTime: new Date().toISOString()
        }
      ]
      localStorage.setItem('WHYWPT_SPARELIST_ALL', JSON.stringify(defaultSpares))
      parts.value = defaultSpares.map(item => ({
        partId: item.spareNumber,
        partName: item.spareName,
        quantity: item.spareStore,
        updateTime: item.updateTime
      }))
    } else {
      parts.value = spareList.map(item => ({
        partId: item.spareNumber,
        partName: item.spareName,
        quantity: item.spareStore,
        updateTime: item.updateTime
      }))
    }
  } catch (error) {
    console.error('获取备件数据失败:', error)
  }
} */

// 获取人员数据
/* const getPersonnel = () => {
  personnel.value = [
    {
      workerId: 'EMP001',
      name: '张三',
      department: '维修部',
    },
    {
      workerId: 'EMP002',
      name: '李四',
      department: '设备部',
    },
    {
      workerId: 'EMP003',
      name: '王五',
      department: '维修部',
    }
  ]
} */

// 选择变更处理函数
const handleEmergencyPlanSelectionChange = (selection) => {
  emergencyPlanSelection.value = selection;
};

const handlePartsSelectionChange = (selection) => {
  partsSelection.value = selection;
};

const handlePersonnelSelectionChange = (selection) => {
  personnelSelection.value = selection;
};

// 选择确认处理函数
const handleSelectEmergencyPlan = () => {
  if (emergencyPlanSelection.value.length === 0) {
    ElMessage.warning("请选择应急预案");
    return;
  }
  if (emergencyPlanSelection.value.length > 1) {
    ElMessage.warning("只能选择一个应急预案");
    return;
  }

  selectedEmergencyPlan.value = emergencyPlanSelection.value[0];
  workOrderData.value.emergencyPlan = selectedEmergencyPlan.value.name;
  workOrderData.value.emergencyPlanId = selectedEmergencyPlan.value.id;

  showEmergencyPlanDialog.value = false;
};

const handleSelectParts = () => {
  if (partsSelection.value.length === 0) {
    ElMessage.warning("请选择关联备件");
    return;
  }
  selectedParts.value = partsSelection.value;
  relatedParts.value = selectedParts.value.map((part) => ({
    partId: part.partId,
    partName: part.partName,
    quantity: part.quantity,
    useDate: part.updateTime || new Date().toISOString(),
  }));
  dialogVisible.value = false;
};

const handleSelectPersonnel = () => {
  if (personnelSelection.value.length === 0) {
    ElMessage.warning("请选择关联人员");
    return;
  }
  selectedPersonnel.value = personnelSelection.value;
  relatedPersonnel.value = selectedPersonnel.value.map((person) => ({
    workerId: person.workerId,
    name: person.name,
    department: person.department,
    relateTime: new Date().toISOString(),
  }));
  dialogVisible.value = false;
};

// 上传前的验证
const beforeUpload = (file) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split(".").pop();
  const allowedExtensions = ["doc", "docx", "xls", "xlsx"];

  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error("只能上传Word或Excel格式的文件!");
    return false;
  }
  if (!isLt20M) {
    ElMessage.error("文件大小不能超过20MB!");
    return false;
  }
  return true;
};

// 修改文件改变处理函数
const handleFileChange = (file, uploadFileList) => {
  // 获取文件扩展名
  const extension = file.name.toLowerCase().split(".").pop();
  const allowedExtensions = ["doc", "docx", "xls", "xlsx"];

  const isValidExtension = allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  if (!isValidExtension) {
    ElMessage.error("只能上传Word或Excel格式的文件!");
    fileList.value = uploadFileList.filter((f) =>
      allowedExtensions.includes(f.name.toLowerCase().split(".").pop())
    );
    return false;
  }
  if (!isLt20M) {
    ElMessage.error("文件大小不能超过20MB!");
    fileList.value = uploadFileList.filter((f) => f.size / 1024 / 1024 < 20);
    return false;
  }

  fileList.value = uploadFileList;
};

// 确认上传
const confirmUpload = async () => {
  if (!fileList.value.length) {
    ElMessage.warning("请先上传文件");
    return;
  }

  try {
    const formData = new FormData();
    formData.append("file", fileList.value[0].raw);
    formData.append("planWorkOrderId", route.query.id);
    formData.append("type", "5");
    formData.append("isUpdate", "true");

    const res = await uploadSchoolPlanWorkOrder(formData);

    if (res.code === 200) {
      ElMessage.success("报告上传成功");
      uploadDialogVisible.value = false;
      fileList.value = []; // 清空文件列表
      // 重新获取工单数据
      getWorkOrderData();
    } else {
      // ElMessage.error(res.msg || '上传失败')
    }
  } catch (error) {
    // console.error('上传失败:', error)
    // ElMessage.error('上传失败')
  }
};

// 修改处理完毕按钮的处理函数
const submitProcessForm = async () => {
  try {
    const params = {
      id: route.query.id,
      remark: workOrderData.value.description || "",
      status: 0,
      planId: workOrderData.value.emergencyPlanId || "",

      // 处理关联人员id数组 - 确保转换为数字类型
      userIds: relatedPersonnel.value
        .map((person) => person.workerId)
        .filter((id) => id)
        .map((id) => Number(id)),

      // 处理关联备件数组 - 使用最新的消耗量
      sparePartsDetails: relatedParts.value.map((part) => ({
        sparePartsId: part.id,
        type: 0,
        resourceType: 2,
        resource: "应急事件",
        num: Number(part.quantity), // 使用组件中最新的消耗量
      })),
    };

    console.log("提交的参数：", params);

    // 调用更新接口
    const res = await updateSchoolPlanWorkOrder(params);
    if (res.code === 200) {
      ElMessage.success("处理成功");
      router.back();
    } else {
      // ElMessage.error(res.msg || '处理失败')
    }
  } catch (error) {
    // console.error('处理失败:', error)
    // ElMessage.error('处理失败')
  }
};

// 表格全选事件处理
const onTableSelectAll = (selection) => {
  console.log("全选事件触发，当前选中数据：", selection);
  const idField = title.value === "请选择关联备件" ? "id" : "userId";

  // 获取当前页的所有行的 ID
  const currentPageIds = ledgerList.value.map((row) => row[idField]);
  console.log("当前页所有ID：", currentPageIds);

  if (selection.length > 0) {
    // 全选时，将当前页的数据添加到全局选中数组
    currentPageIds.forEach((id) => {
      if (!tableAllSelectedId.value.includes(id)) {
        const row = ledgerList.value.find((item) => item[idField] === id);
        if (row) {
          tableAllSelectedId.value.push(id);
          tableAllSelectedRow.value.push(row);
        }
      }
    });
  } else {
    // 取消全选时，从全局选中数组中移除当前页的数据
    tableAllSelectedId.value = tableAllSelectedId.value.filter(
      (id) => !currentPageIds.includes(id)
    );
    tableAllSelectedRow.value = tableAllSelectedRow.value.filter(
      (row) => !currentPageIds.includes(row[idField])
    );
  }
  console.log("全选后全局选中ID：", tableAllSelectedId.value);
  console.log("全选后全局选中行数据：", tableAllSelectedRow.value);
};

// 修改选择变更处理函数
const handleSelectionChange = (selection) => {
  // 不在这里更新全局选中数组，因为已经在 onTableSelect 和 onTableSelectAll 中处理了
  tempSelection.value = selection;
};

// 修改表格选择/取消选择事件处理
const onTableSelect = (selection, row) => {
  const idField = title.value === "请选择关联备件" ? "id" : "userId";
  const rowId = row[idField];

  console.log("单行选择事件触发：", {
    selection,
    row,
    idField,
    rowId,
  });

  // 判断是选中还是取消选中
  const isSelected = selection.some((item) => item[idField] === rowId);

  if (isSelected) {
    // 选中时，如果不在全局选中数组中则添加
    if (!tableAllSelectedId.value.includes(rowId)) {
      tableAllSelectedId.value.push(rowId);
      tableAllSelectedRow.value.push(row);
      console.log("添加选中：", rowId);
    }
  } else {
    // 取消选中时，从全局选中数组中移除
    const index = tableAllSelectedId.value.indexOf(rowId);
    if (index !== -1) {
      tableAllSelectedId.value.splice(index, 1);
      tableAllSelectedRow.value.splice(index, 1);
      console.log("移除选中：", rowId);
    }
  }

  console.log("选择后全局选中ID：", tableAllSelectedId.value);
  console.log("选择后全局选中行数据：", tableAllSelectedRow.value);
};

// 修改提交选择的处理
const submitForm = () => {
  console.log("提交时全局选中ID：", tableAllSelectedId.value);
  console.log("提交时全局选中行数据：", tableAllSelectedRow.value);

  // 如果是人员选择对话框
  if (title.value === "请选择关联人员") {
    // 创建一个映射来保存原有人员的信息
    const existingPersonnelMap = {};
    relatedPersonnel.value.forEach((person) => {
      existingPersonnelMap[person.workerId] = {
        ...person,
      };
    });

    // 使用全局选中数组中的数据更新关联人员列表，同时保留原有数据的信息
    const newPersonnel = tableAllSelectedRow.value.map((item) => {
      const existingPerson = existingPersonnelMap[item.userId];
      return {
        workerId: item.userId,
        workId: item.workId || "-",
        name: item.nickName,
        department: item.sysDept?.deptName || "-",
        // 如果是已存在的人员，保留原有的时间和报告信息
        relateTime: existingPerson
          ? existingPerson.relateTime
          : formatDate(new Date()),
        reportFileName: existingPerson ? existingPerson.reportFileName : "",
        reportFileUrl: existingPerson ? existingPerson.reportFileUrl : "",
        notifyTime: existingPerson ? existingPerson.notifyTime : null,
      };
    });

    relatedPersonnel.value = newPersonnel;
  } else {
    // 创建一个映射来保存原有备件的信息
    const existingPartsMap = {};
    relatedParts.value.forEach((part) => {
      existingPartsMap[part.id] = {
        ...part,
      };
    });

    // 使用全局选中数组中的数据更新关联备件列表，同时保留原有数据的信息
    const updatedParts = tableAllSelectedRow.value.map((item) => {
      const existingPart = existingPartsMap[item.id];
      return {
        id: item.id,
        partId: item.code,
        partName: item.name,
        // 如果是已存在的备件，保留原有的数量和使用日期
        quantity: existingPart ? existingPart.quantity : 1,
        useDate: existingPart ? existingPart.useDate : new Date().toISOString(),
        stock: Math.max(1, item.stock || 1),
      };
    });

    relatedParts.value = updatedParts;
  }

  console.log(
    "更新后的数据：",
    title.value === "请选择关联人员"
      ? relatedPersonnel.value
      : relatedParts.value
  );

  // 关闭对话框并清理临时数据
  dialogVisible.value = false;
  ledgerList.value = [];
  tempSelection.value = [];
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
};

// 修改对话框关闭时的处理
const handleDialogClose = () => {
  ledgerList.value = [];
  tempSelection.value = [];
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
};

// 获取行的唯一标识
const getRowKey = (row) => {
  return title.value === "请选择关联备件" ? row.id : row.userId;
};

// 判断行是否可选
const isSelectable = (row) => {
  if (title.value === "请选择关联备件") {
    // 检查备件是否已存在，如果已存在则返回true（允许选择）
    return true;
  } else {
    // 检查人员是否已存在，如果已存在则返回true（允许选择）
    return true;
  }
};

// 修改表格行的样式
const tableRowClassName = ({ row }) => {
  if (!isSelectable(row)) {
    return "disabled-row";
  }
  return "";
};

// 修改返回按钮处理函数
const goBack = () => {
  router.back();
};

// 修改分页处理函数
const handleCurrentChange = (val) => {
  console.log("当前页改变：", val);
  queryParams.value.current = val;

  const loadData =
    title.value === "请选择关联备件" ? getSparePartsList : getList;

  loadData().then(() => {
    nextTick(() => {
      console.log(
        "切换页码后恢复选中状态，全局选中ID：",
        tableAllSelectedId.value
      );
      console.log("当前页数据：", ledgerList.value);

      // 重新设置选中状态
      tableRef.value?.clearSelection();
      ledgerList.value.forEach((row) => {
        const rowId = title.value === "请选择关联备件" ? row.id : row.userId;
        if (tableAllSelectedId.value.includes(rowId)) {
          console.log("设置行选中状态：", row);
          tableRef.value?.toggleRowSelection(row, true);
        }
      });
    });
  });
};

// 修改页大小改变处理函数
const handleSizeChange = (val) => {
  console.log("页大小改变：", val);
  queryParams.value.size = val;
  queryParams.value.current = 1;

  const loadData =
    title.value === "请选择关联备件" ? getSparePartsList : getList;

  loadData().then(() => {
    nextTick(() => {
      console.log(
        "切换页大小后恢复选中状态，全局选中ID：",
        tableAllSelectedId.value
      );
      // 重新设置选中状态
      tableRef.value?.clearSelection();
      ledgerList.value.forEach((row) => {
        const rowId = title.value === "请选择关联备件" ? row.id : row.userId;
        if (tableAllSelectedId.value.includes(rowId)) {
          tableRef.value?.toggleRowSelection(row, true);
        }
      });
    });
  });
};

const downloadFile = (fileUrl, fileName) => {
  if (!fileUrl) {
    ElMessage.warning("文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let downloadUrl = fileUrl;
    if (downloadUrl.startsWith("http:")) {
      downloadUrl = downloadUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = downloadUrl;
    link.setAttribute("download", fileName || "未命名文件");
    link.setAttribute("target", "_blank");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 修改 handleSelect 函数
const handleSelect = (isPersonnel) => {
  title.value = isPersonnel ? "请选择关联人员" : "请选择关联备件";
  dialogVisible.value = true;

  // 重置查询参数
  queryParams.value = {
    current: 1,
    size: 5,
    faultDesc: "",
  };

  // 初始化全局选中数组
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];

  console.log(
    "当前已选人员/备件：",
    isPersonnel ? relatedPersonnel.value : relatedParts.value
  );

  // 根据当前已选数据初始化全局选中数组
  if (isPersonnel) {
    // 初始化人员选中状态
    relatedPersonnel.value.forEach((person) => {
      // 注意：这里使用 Number 转换确保 ID 类型一致
      const workerId = Number(person.workerId);
      console.log("添加已选人员到全局数组：", {
        workerId,
        person,
      });

      tableAllSelectedId.value.push(workerId);
      tableAllSelectedRow.value.push({
        userId: workerId,
        workId: person.workId || "-",
        nickName: person.name,
        sysDept: {
          deptName: person.department,
        },
      });
    });

    getList().then(() => {
      nextTick(() => {
        console.log("人员列表数据：", ledgerList.value);
        console.log("全局选中ID：", tableAllSelectedId.value);

        if (tableRef.value) {
          tableRef.value.clearSelection();
          ledgerList.value.forEach((row) => {
            // 注意：这里也使用 Number 转换确保类型一致
            const rowUserId = Number(row.userId);
            if (tableAllSelectedId.value.includes(rowUserId)) {
              console.log("设置人员行选中状态：", row);
              tableRef.value.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  } else {
    // 初始化备件选中状态（保持不变）
    relatedParts.value.forEach((part) => {
      tableAllSelectedId.value.push(part.id);
      tableAllSelectedRow.value.push({
        id: part.id,
        code: part.partId,
        name: part.partName,
        stock: part.stock,
        num: part.quantity,
      });
    });
    getSparePartsList().then(() => {
      console.log("备件列表数据：", ledgerList.value);
      nextTick(() => {
        if (tableRef.value) {
          tableRef.value.clearSelection();
          ledgerList.value.forEach((row) => {
            if (tableAllSelectedId.value.includes(row.id)) {
              tableRef.value.toggleRowSelection(row, true);
            }
          });
        }
      });
    });
  }
};

// 添加搜索相关函数
const handleSearch = () => {
  queryParams.value.current = 1;
  if (title.value === "请选择关联备件") {
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  } else {
    getList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  }
};

// 添加重置查询函数
const resetQuery = () => {
  queryParams.value = {
    current: 1,
    size: 5,
    codeAndName: "",
    faultDesc: "",
    status: "0",
  };

  if (title.value === "请选择关联备件") {
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  } else {
    getList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  }
};

// 修改获取备件列表数据函数
const getSparePartsList = async () => {
  loading.value = true;
  try {
    const params = {
      pageNum: queryParams.value.current,
      pageSize: queryParams.value.size,
      codeAndName: queryParams.value.faultDesc,
      status: "0",
    };

    const res = await sparePartsPage(params);
    if (res.code === 200) {
      ledgerList.value = res.data.records;
      total.value = res.data.total;
    }
  } catch (error) {
    console.error("获取备件列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 修改获取人员列表数据函数
const getList = async () => {
  loading.value = true;
  try {
    const params = {
      current: queryParams.value.current,
      size: queryParams.value.size,
      nickNameAndWorkId: queryParams.value.faultDesc,
    };

    const res = await getMaintainList(params);
    if (res.code === 200) {
      ledgerList.value = res.data.records;
      total.value = res.data.total;

      // 在数据加载完成后，恢复选中状态
      nextTick(() => {
        console.log("人员列表数据加载完成，当前数据：", ledgerList.value);
        console.log("当前全局选中ID：", tableAllSelectedId.value);

        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            console.log("设置人员行选中状态：", row);
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    }
  } catch (error) {
    console.error("获取人员列表失败:", error);
  } finally {
    loading.value = false;
  }
};

// 修改行点击处理函数
const handleRowClick = (row) => {
  if (!isSelectable(row)) {
    return; // 如果行不可选，直接返回
  }

  // 获取当前行的ID
  const idField = title.value === "请选择关联备件" ? "id" : "userId";
  const rowId = row[idField];

  // 检查是否已经选中
  const isSelected = tableAllSelectedId.value.includes(rowId);

  // 切换选中状态
  if (isSelected) {
    // 如果已选中，则取消选中
    tableRef.value.toggleRowSelection(row, false);
    // 从全局选中数组中移除
    const index = tableAllSelectedId.value.indexOf(rowId);
    if (index !== -1) {
      tableAllSelectedId.value.splice(index, 1);
      tableAllSelectedRow.value.splice(index, 1);
    }
  } else {
    // 如果未选中，则选中
    tableRef.value.toggleRowSelection(row, true);
    // 添加到全局选中数组
    if (!tableAllSelectedId.value.includes(rowId)) {
      tableAllSelectedId.value.push(rowId);
      tableAllSelectedRow.value.push(row);
    }
  }
};

// 在组件挂载时获取保存的页码
onMounted(() => {
  getWorkOrderData();
  /* getEmergencyPlans()
  getParts()
  getPersonnel() */
});
</script>

<style scoped lang="scss">
.taskInfo {
  padding: 20px;

  :deep(.el-descriptions) {
    .label-width {
      width: 130px;
      height: 60px;
    }
    .value-width {
      width: 270px;
      min-height: 60px;
    }
  }
}

.taskInfo-btns {
  padding: 20px;
  text-align: center;
  margin-top: 20px;
}

:deep(.el-descriptions__body) {
  .el-descriptions__table.is-bordered .el-descriptions__cell {
    // padding: 30px 11px;
    /* min-width: 120px;
    max-width: 500px; */
    word-break: break-all; // 让内容超出列宽时自动换行显示
    word-wrap: break-word;
  }
}

.upload-content {
  padding: 20px;

  .el-upload__tip {
    margin-top: 10px;
    color: #909399;
  }
}

.custom-dialog {
  :deep(.el-dialog__body) {
    padding: 10px 20px;
  }
}

:deep(.disabled-row) {
  background-color: #f5f7fa;
  color: #c0c4cc;
  cursor: not-allowed;
}

// 添加新的样式
.report-container {
  display: flex;
  align-items: center;
  gap: 20px;

  .upload-btn-wrapper {
    flex-shrink: 0;
  }

  .report-list {
    flex-grow: 1;
  }

  .uploader-name {
    margin-left: 8px;
    color: #909399;
    font-size: 14px;
  }
}

.pagination-container {
  margin-top: 15px;
  padding: 10px 0;
  display: flex;
  justify-content: flex-end;
}

.report-item {
  margin: 5px 0;
  &:first-child {
    margin-top: 0;
  }
  &:last-child {
    margin-bottom: 0;
  }
}

// 添加新的样式
.t_item {
  display: flex;
  flex-wrap: wrap;
  align-items: center;

  p {
    padding: 0;
    margin: 0;
    margin-right: 10px;
    margin-top: 5px;
  }
}
</style>
