<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <Detail
        ref="detailRef"
        :key="detailKey"
        :troubleId="route.query.id"
        :isHandled="false"
        :readonly="!isEdit"
        @changeTroubleStatus="changeTroubleStatus"
        @changeDeviceStatus="changeDeviceStatus"
        @changeEditStatus="changeEditStatus"
        @getRecord="getRecord"
        @handleBack="handleBack"
      />

      <!-- 按钮区域，根据readonly状态显示不同按钮 -->
      <div class="taskInfo-btns">
        <div v-if="state.canEdit && !(isKnowledge || isSpare)">
          <div v-if="state.troubleStatus == 1" style="display: flex">
            <el-button type="danger" @click="handleRollBack" v-throttle
              >回退</el-button
            >
            <div v-if="route.query.type == 0" style="display: flex">
              <el-button type="primary" v-show="!isEdit" @click="handleDeal"
                >处理工单</el-button
              >

              <el-button
                type="warning"
                v-throttle
                v-show="isEdit"
                @click="handleHangUp"
                >挂起</el-button
              >
              <el-button
                color="#626aef"
                v-throttle
                v-show="isEdit"
                @click="handleRevert"
                >扭转</el-button
              >
              <el-button
                type="primary"
                v-throttle
                v-show="isEdit"
                @click="handleSave"
                >保存</el-button
              >
              <el-button
                v-if="!isDeviceDelete"
                type="primary"
                v-throttle
                v-show="isEdit"
                @click="handleSaveAndUpdate"
                >保存并同步设备信息</el-button
              >
            </div>
            <div v-if="route.query.type == 1" style="display: flex">
              <el-button type="warning" @click="handleHangUp" v-throttle
                >挂起</el-button
              >
              <el-button color="#626aef" @click="handleRevert" v-throttle
                >扭转</el-button
              >
              <el-button type="primary" @click="handleSave" v-throttle
                >保存</el-button
              >
              <el-button
                v-if="!isDeviceDelete"
                type="primary"
                @click="handleSaveAndUpdate"
                v-throttle
                >保存并同步设备信息</el-button
              >
            </div>
            <el-button type="success" @click="handleToKonwledge" v-throttle
              >转入知识库</el-button
            >
          </div>
          <el-button
            v-if="state.troubleStatus == 4"
            type="success"
            @click="handleRecover"
            v-throttle
            >恢复</el-button
          >
        </div>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <div v-if="recordList.length > 0">
        <div class="taskInfo-tit">变更记录</div>
        <div
          class="taskInfo-record"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role || ""} 于${item.operationTime} ${
              item.remark
            }`
          }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import Detail from "@/views/taskCenter/components/index.vue";
import { addKnowledgeBase, troubleUpdate } from "@/api/mediaTeach/trouble";
import { onMounted, reactive, toRefs } from "vue";
import { addPointObj } from "@/utils/addPoint";
import { sendPointRequest } from "@/utils";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const isKnowledge = ref(route.query.knowledge == 1);
const isSpare = ref(!!route.query.spareId);
const isDaily = ref(!!route.query.isDaily);

const state = reactive({
  canEdit: false,
  recordList: [],
  troubleStatus: 1,
  isDeviceDelete: false,
  detailRef: null,
  isEdit: false,
  detailKey: 1,
});
const {
  canEdit,
  isEdit,
  detailKey,
  detailRef,
  troubleStatus,
  isDeviceDelete,
  recordList,
} = toRefs(state);

const changeEditStatus = (editStatus) => {
  state.canEdit = editStatus;
};

const changeTroubleStatus = (troubleStatus) => {
  state.troubleStatus = troubleStatus;
};

const changeDeviceStatus = (isDeviceDelete) => {
  state.isDeviceDelete = isDeviceDelete;
};

const getRecord = (recordList) => {
  state.recordList = recordList;
};

const handleDeal = () => {
  state.isEdit = true;
  state.detailKey++;
};

const handleSave = () => {
  state.detailRef.handleSave();
};

const handleSaveAndUpdate = () => {
  state.detailRef.handleSaveAndUpdate();
};

const handleHangUp = () => {
  proxy.$modal
    .confirm(`确定挂起此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: route.query.id,
        status: 4,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("操作成功");
          state.detailKey++;
          router.push({
            path: "/taskManage/taskCenter/todo",
          });
          // emits("handleBack");
        }
      });
    })
    .catch();
};

const handleRecover = () => {
  proxy.$modal
    .confirm(`确定恢复此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: route.query.id,
        status: 6,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("恢复成功");
          state.detailKey++;
        }
      });
    })
    .catch();
};

const handleRevert = () => {
  state.detailRef.handleDialog2(1);
};

const handleRollBack = () => {
  proxy.$modal
    .confirm(`确定回退此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: route.query.id,
        status: 5,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("操作成功");
          handleBack();
        }
      });
    })
    .catch();
};

const handleToKonwledge = () => {
  proxy.$modal.confirm(`确定将此工单转入知识库？`).then(() => {
    addKnowledgeBase({
      troubleId: route.query.id,
    }).then((resp) => {
      if (resp.code == 200) {
        sendPointRequest({
          event: "Click",
          eventDescribe: "点击转入知识库",
          content:
            troubleStatus.value > -1
              ? addPointObj[troubleStatus.value].status
              : "",
          num: 1,
        });
        proxy.$modal.msgSuccess("转入成功");
      }
    });
  });
};

// 取消/返回处理
function handleBack() {
  if (isKnowledge.value) {
    proxy.$tab.closeOpenPage("/knowledgeManage/knowledge");
  } else if (isSpare.value) {
    proxy.$tab.closeOpenPage(
      `/spareManage/spareInfo?id=${route.query.spareId}`
    );
  } else if (isDaily.value) {
    proxy.$tab.closeOpenPage(
      `/taskManage/daily?pageNum=${route.query.pageNum}&tab=${route.query.tab}`
    );
  } else {
    router.go(-1);
    proxy.$tab.closeOpenPage();
  }
}

onMounted(() => {
  state.isEdit = route.query.type == 1 && !(isKnowledge.value || isSpare.value);
});
</script>

<style scoped lang="scss">
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }
  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.taskInfo {
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0 10px;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-record {
    font-size: 14px;
    margin-bottom: 5px;
  }

  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }
  &-btns {
    display: flex;
    padding: 20px;
    text-align: center;
    justify-content: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.upload-area {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .file-info {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
