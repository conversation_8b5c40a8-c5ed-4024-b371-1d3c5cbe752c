<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>巡检记录</span>
        </div>
      </template>
      <el-table v-loading="loading" :data="recordList" border>
        <el-table-column
          label="巡检人员"
          align="center"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="{ row }">
            <div>{{ row.name }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检范围"
          align="center"
          show-overflow-tooltip
          min-width="200"
        >
          <template #default="{ row }">
            <div>{{ row.pointCodeList?.join("、") || "-" }}</div>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检时间"
          align="center"
          show-overflow-tooltip
          min-width="180"
          prop="date"
        >
        </el-table-column>
        <el-table-column
          label="巡检指标"
          align="center"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="{ row }">
            {{ row.indicatorsName || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="巡检结果"
          align="center"
          show-overflow-tooltip
          min-width="120"
        >
          <template #default="{ row }">
            <span class="checklink" @click="handleCheck(row)">点击查看</span>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检状态"
          min-width="120"
          align="center"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <div class="table-link">
              <span :style="{ color: statusList[row.status].color }">{{
                `${statusList[row.status].name}${
                  row.status == 2 ? row.uncheckPointNum + "处" : ""
                }`
              }}</span>
            </div>
          </template>
        </el-table-column>
        <el-table-column label="操作" min-width="120" align="center" fixed="right">
          <template #default="scope">
            <el-button link type="primary" @click="handleDetail(scope.row)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>
    <el-dialog class="custom-dialog" title="查看巡检结果" v-model="dialogVisible">
      <el-table :data="resultList" border max-height="800">
        <el-table-column
          label="点位编号"
          prop="pointCode"
          min-width="150"
          show-overflow-tooltip
        />
        <el-table-column
          label="点位"
          prop="pointName"
          min-width="100"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检结果"
          prop="result"
          min-width="180"
          show-overflow-tooltip
        >
          <template #default="{ row }">
            <el-tag :type="statusObj[row.result].type">{{ row.result }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检时间"
          prop="inspectionTime"
          min-width="160"
          show-overflow-tooltip
        />
      </el-table>
      <pagination
        v-show="totalDialog > 0"
        :total="totalDialog"
        v-model:page="queryParamsDialog.pageNum"
        v-model:limit="queryParamsDialog.pageSize"
        @pagination="getListDialog"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="checkRecord">
import { ref, onMounted, reactive, toRefs } from "vue";
import { useRouter } from "vue-router";
import { downloadBlob } from "@/utils";
import { schoolInspectionRecordList, schoolInspectionResultList } from "@/api/check";

const { proxy } = getCurrentInstance(); // 添加proxy定义
const router = useRouter();

const state = reactive({
  loading: false,
  dialogVisible: false,
  total: 0,
  totalDialog: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  queryParamsDialog: {
    pageNum: 1,
    pageSize: 10,
  },
  statusObj: {
    无异常: {
      type: "primary",
    },
    发现异常: {
      type: "danger",
    },
    已维修: {
      type: "success",
    },
    "-": {
      type: "",
    },
  },
  recordList: [],
  resultList: [],
  statusList: [
    {
      name: "未开始",
      color: "#F56C6C",
    },
    {
      name: "完成巡检",
      color: "#67C23A",
    },
    {
      name: "漏检",
      color: "#F56C6C",
    },
  ],
});
const {
  loading,
  dialogVisible,
  total,
  totalDialog,
  queryParamsDialog,
  queryParams,
  recordList,
  resultList,
  statusList,
  statusObj,
} = toRefs(state);

const getList = () => {
  schoolInspectionRecordList(state.queryParams).then((response) => {
    if (response.code == 200) {
      const { records, total } = response.data;
      console.log("record", response);
      state.recordList = records || [];
      state.total = total || 0;
    }
  });
};

const handleCancel = () => {
  state.dialogVisible = false;
  state.resultList = [];
};

const getListDialog = () => {
  schoolInspectionResultList(state.queryParamsDialog).then((response) => {
    if (response.code == 200) {
      console.log(response);
      const { records, total } = response.data;
      state.resultList =
        records?.reduce((res, cur) => {
          res.push({
            ...cur,
            result: cur.result || "-",
            inspectionTime: cur.inspectionTime || "-",
          });
          return res;
        }, []) || [];
      state.totalDialog = total || 0;
    }
  });
};

const handleCheck = (row) => {
  state.queryParamsDialog.patrolPlanId = row.patrolPlanId;
  state.queryParamsDialog.pageNum = 1;
  state.queryParamsDialog.pageSize = 10;
  getListDialog();
  state.dialogVisible = true;
};

// 查看详情方法
const handleDetail = (row) => {
  router.push({
    path: "/checkManage/recordInfo",
    query: {
      id: row.patrolPlanId,
    },
  });
};

// 页面加载时获取数据
onMounted(() => {
  getList();
});
</script>

<style lang="scss" scoped>
.checklink {
  font-size: 14px;
  color: #4095e5;
  text-decoration: underline;
  cursor: pointer;
}
.check-result {
  &.success {
    color: #67c23a;
  }

  &.warning {
    color: #e6a23c;
  }

  &.danger {
    color: #f56c6c;
  }
}
</style>
