<template>
  <div class="login" id="login" v-loading="userStore.ssoLoading">
    <div class="login-left"></div>
    <div class="login-right">
      <el-form
        ref="loginRef"
        :model="loginForm"
        :rules="loginRules"
        class="login-form"
      >
        <div class="title">
          {{ title }}
        </div>
        <el-tabs v-model="activeName" class="demo-tabs" @tab-change="handleTab">
          <el-tab-pane label="账号密码登录" name="first">
            <div class="login-form_label">账号</div>
            <el-form-item prop="username" v-if="activeName == 'first'">
              <el-input
                v-model="loginForm.username"
                type="text"
                size="large"
                auto-complete="off"
                placeholder="请输入您的编号/手机号"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="user"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
            <div class="login-form_label">密码</div>
            <el-form-item prop="password" v-if="activeName == 'first'">
              <el-input
                v-model="loginForm.password"
                type="password"
                size="large"
                auto-complete="off"
                placeholder="请输入您的密码"
                @keyup.enter="handleLogin"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="password"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
            </el-form-item>
            <div class="login-form_label">验证码</div>
            <el-form-item prop="code" v-if="captchaEnabled" class="item_code">
              <el-input
                v-model="loginForm.code"
                size="large"
                auto-complete="off"
                placeholder="验证码"
                maxlength="6"
                style="width: 100%"
                @keyup.enter="handleLogin"
              >
                <template #prefix
                  ><svg-icon
                    icon-class="validCode"
                    class="el-input__icon input-icon"
                /></template>
              </el-input>
              <div class="login-code">
                <img :src="codeUrl" @click="getCode" class="login-code-img" />
              </div>
            </el-form-item>
          </el-tab-pane>
          <el-tab-pane label="扫码验证登录" name="third" v-if="showCodeLogin">
            <div id="loginContainer" v-if="codeSuccess"></div>
            <div v-if="!codeSuccess">
              <div style="padding: 20px">
                扫码登录失败，请使用手机验证码登录
              </div>
              <div class="login-form_label">手机号</div>
              <el-form-item
                v-if="activeName == 'third'"
                prop="phone"
                :rules="phoneRules"
              >
                <el-input
                  v-model="loginForm.phone"
                  type="text"
                  size="large"
                  auto-complete="off"
                  placeholder="请输入您的手机号"
                >
                  <template #prefix
                    ><svg-icon
                      icon-class="user"
                      class="el-input__icon input-icon"
                  /></template>
                </el-input>
              </el-form-item>
              <div class="login-form_label">验证码</div>
              <el-form-item prop="code">
                <div class="item-code">
                  <el-input
                    v-model="loginForm.code"
                    size="large"
                    auto-complete="off"
                    placeholder="请输入您的验证码"
                    maxlength="6"
                    style="width: 100%"
                    @keyup.enter="handleLogin"
                  >
                    <template #prefix
                      ><svg-icon
                        icon-class="validCode"
                        class="el-input__icon input-icon"
                    /></template>
                  </el-input>
                  <el-button
                    color="#0082CD"
                    type="primary"
                    @click.prevent="getValidCode"
                    v-throttle
                  >
                    {{
                      countDown < 60
                        ? `${countDown} 秒后重新获取`
                        : "获取验证码"
                    }}</el-button
                  >
                </div>
              </el-form-item>
            </div>
          </el-tab-pane>
        </el-tabs>
        <el-form-item
          style="width: 100%"
          v-if="activeName == 'first' || !codeSuccess"
        >
          <el-button
            color="#0082CD"
            :loading="loading"
            size="large"
            v-throttle
            type="primary"
            style="width: 100%; margin-top: 0.3vw"
            @click.prevent="handleLogin"
          >
            <span v-if="!loading">立即登录</span>
            <span v-else>登 录 中...</span>
          </el-button>
          <div style="float: right" v-if="register">
            <router-link class="link-type" :to="'/register'"
              >立即注册</router-link
            >
          </div>
        </el-form-item>
        <!-- <el-checkbox v-model="loginForm.rememberMe" style="margin:0px 0px 25px 0px;">记住密码</el-checkbox> -->
      </el-form>
    </div>

    <!--  底部  -->
    <div class="el-login-footer">
      <!-- <span>Copyright © 2018-2023 ruoyi.vip All Rights Reserved.</span> -->
    </div>
  </div>
</template>

<script setup>
import { getCodeImg, wxLogin, wxLoginByUnionId, sendSms } from "@/api/login";
import Cookies from "js-cookie";
import { encrypt, decrypt } from "@/utils/jsencrypt";
import { ElMessage } from "element-plus";
import useUserStore from "@/store/modules/user";
import { onUnmounted, onBeforeUnmount, onMounted } from "vue";
import { setToken } from "@/utils/auth";
import { sendPointRequest, sendPointRequestAsync, isIPv4 } from "@/utils/index";
import { nextTick } from "process";
console.log(isIPv4(window.location.hostname), "是否合法ip");
const title = ref("");
const userStore = useUserStore();
const route = useRoute();
const router = useRouter();
const { proxy } = getCurrentInstance();
const showCodeLogin = ref(
  import.meta.env.VITE_APP_BASE_API != "/djg-prod-api" &&
    !isIPv4(window.location.hostname)
);
const codeSuccess = ref(true);
const unionId = ref("");
const loginRef = ref(null);
const timer = ref(null);
const timer2 = ref(null);
const countDown = ref(60);
const phoneRules = ref([
  { required: true, trigger: "blur", message: "请输入您的手机号" },
  {
    pattern: /^1[3|4|5|6|7|8|9][0-9]\d{8}$/,
    message: "请输入正确的手机号码",
    trigger: "blur",
  },
]);
let pathName = window.location.pathname;
const keyMap = ref({
  teacher: "teacherStaff",
});
console.log(keyMap.value[pathName.replace(/\//g, "")], "字符标识");
const loginCodeObj = ref({
  "maintainapp.gzwinteam.com": {
    url: `https://maintainapp.gzwinteam.com${pathName}#/login`,
    appId: "wx10ab51f4c07b263f",
  },
  "maintainapptest.gzwinteam.com": {
    url: `https://maintainapptest.gzwinteam.com${pathName}#/login`,
    appId: "wx45c086c3cdd35435",
  },
  "maintainappdemo.gzwinteam.com": {
    url: `https://maintainappdemo.gzwinteam.com${pathName}#/login`,
    appId: "wxd21025397f1dbfc6",
  },

  "maintainapppre.gzwinteam.com": {
    url: `https://maintainapppre.gzwinteam.com${pathName}#/login`,
    appId: "wx82f41bb50843e08b",
  },
});
console.log(
  loginCodeObj.value[window.location.hostname] ||
    `https://maintainapptest.gzwinteam.com${pathName}#/login`
);
const loginForm = ref({
  phone: "",
  username: "",
  password: "",
  rememberMe: false,
  code: "",
  uuid: "",
});
const activeName = ref("first");
const loginRules = {
  username: [
    { required: true, trigger: "blur", message: "请输入您的编号/手机号" },
  ],
  password: [{ required: true, trigger: "blur", message: "请输入您的密码" }],
  code: [{ required: true, trigger: "change", message: "请输入验证码" }],
};

const codeUrl = ref("");
const loading = ref(false);
// 验证码开关
const captchaEnabled = ref(true);
// 注册开关
const register = ref(false);
const redirect = ref(undefined);

const stayTimer = ref(null);
const stayTime = ref(0);

watch(
  route,
  (newRoute) => {
    redirect.value = newRoute.query && newRoute.query.redirect;
  },
  { immediate: true }
);

// 监听特定查询参数变化
// watch(
//   () => route.query.forceRefresh,
//   (newVal) => {
//     if (newVal) {
//       //执行组件刷新逻辑
//       // 2. 重置表单
//       loginForm.value = {
//         phone: "",
//         username: "",
//         password: "",
//         rememberMe: false,
//         code: "",
//         uuid: "",
//       };
//       activeName.value = "first";
//       loading.value = false;
//       loginRef.value.resetFields();
//       // 4. 清除查询参数避免循环
//       router.replace({ query: null });
//     }
//   }
// );

const getTitle = () => {
  title.value =
    window.location.pathname.indexOf("/teacher") != -1
      ? "教师管理平台"
      : "智控管理平台";
};
const getSSOCode = () => {
  if (route.fullPath.indexOf("sso_id=") == -1) return;
  const ssoCode = route.fullPath.substr(route.fullPath.indexOf("sso_id=") + 7);
  userStore
    .loginBySSO(ssoCode)
    .then(() => {
      const query = route.query;
      const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
        if (cur !== "redirect") {
          acc[cur] = query[cur];
        }
        return acc;
      }, {});
      ElMessage({
        message: "登录成功",
        type: "success",
        duration: 2000,
        onClose: () => {
          sendPointRequestAsync({
            event: "PageView",
            eventDescribe: "浏览访问登录页",
            content: "",
            num: stayTime.value,
          }).finally(() => {
            if (!!route.query.code) {
              console.log("路径带有code参数");
              router.replace({
                path: "/mainPageIndex",
                query: { t: Date.now() },
              }); // 添加时间戳强制刷新
            } else {
              router.push({ path: "" });
            }
          });
        },
      });
      localStorage.setItem("isLogin", 1);
    })
    .catch(() => {
      loading.value = false;
      // 重新获取验证码
      console.log("sso码错误");
      //sso错误清除错误的sso码
      router.push({ path: "" });
    })
    .finally(() => {
      userStore.ssoLoading = false;
    });
};
onBeforeUnmount(() => {
  clearInterval(timer.value);
  clearInterval(timer2.value);
  clearInterval(stayTimer.value);
});

function handleTab(val) {
  proxy.resetForm("loginRef");
  if (val == "third") {
    codeSuccess.value = true;
    nextTick(() => {
      wxLoginCode();
    });
  }
}

function wxLoginCode() {
  console.log(loginCodeObj.value[window.location.hostname]);
  new WxLogin({
    self_redirect: false,
    id: "loginContainer",
    appid:
      loginCodeObj.value[window.location.hostname]?.appId ||
      "wx45c086c3cdd35435",
    scope: "snsapi_login",
    redirect_uri: encodeURIComponent(
      loginCodeObj.value[window.location.hostname]?.url ||
        `https://maintainapptest.gzwinteam.com${pathName}#/login`
    ), // 编码重定向地址
    state: "bind",
    style: "black",
    href: "",
  });
}

// 监听popstate事件
window.addEventListener("popstate", function (event) {
  console.log("URL changed:", window.location.href);
  // 你可以在这里做一些其他的处理

  const code = route.query.code;
  // console.log("进入页面", route.query);
  if (route.query.code) {
    // 如果 code 存在，表示授权成功，可以继续后续处理
    console.log("微信授权成功，code:", code);
    proxy.$modal.loading();
    wxLogin({
      code,
      permission: keyMap.value[pathName.replace(/\//g, "")] || "",
    })
      .then((res) => {
        proxy.$modal.closeLoading();
        // console.log(res, "res");
        if (res.data.access_token) {
          console.log("存在token");
          codeSuccess.value = true;
          setToken(res.data.access_token);
          userStore.token = res.data.access_token;
          ElMessage({
            message: "登录成功",
            type: "success",
            duration: 2000,
            onClose: () => {
              sendPointRequestAsync({
                event: "PageView",
                eventDescribe: "浏览访问登录页",
                content: "",
                num: stayTime.value,
              }).finally(() => router.push({ path: "" }));
            },
          });
          localStorage.setItem("isLogin", 1);
        }
      })
      .catch((e) => {
        console.log("微信授权失败", e);
        unionId.value = e;
        proxy.$modal.closeLoading();
        codeSuccess.value = false;
        if (e == "该机构已被停用") {
          codeSuccess.value = true;
          nextTick(() => {
            wxLoginCode();
          });
        }
      });
    // 这里你可以把 code 传给后端，后端去微信服务器获取 access_token 和 openid
    // 然后根据需要做用户登录等处理
  }
});

onMounted(() => {
  getTitle();
  getSSOCode();
  timer2.value = setInterval(() => {
    getCode();
  }, 119000);
  stayTimer.value = setInterval(() => {
    stayTime.value++;
  }, 1000);
});

function getValidCode() {
  loginRef.value.validateField("phone", (valid) => {
    if (valid) {
      if (countDown.value < 60) {
        return;
      }
      sendSms({ phone: loginForm.value.phone }).then((res) => {
        proxy.$modal.msgSuccess("验证码已发送");
      });
      countDown.value--;
      timer.value = setInterval(() => {
        countDown.value--;
        if (countDown.value == 0) {
          clearInterval(timer.value);
          countDown.value = 60;
        }
      }, 1000);
    }
  });
}

function handleLogin() {
  // router.push({ path: redirect.value || "/mainPageIndex" });
  proxy.$refs.loginRef.validate((valid) => {
    if (valid) {
      loading.value = true;
      // 勾选了需要记住密码设置在 cookie 中设置记住用户名和密码
      if (loginForm.value.rememberMe) {
        Cookies.set("username", loginForm.value.username, { expires: 30 });
        Cookies.set("password", encrypt(loginForm.value.password), {
          expires: 30,
        });
        Cookies.set("rememberMe", loginForm.value.rememberMe, { expires: 30 });
      } else {
        // 否则移除
        Cookies.remove("username");
        Cookies.remove("password");
        Cookies.remove("rememberMe");
      }
      // 调用action的登录方法
      if (activeName.value == "first") {
        userStore
          .login(loginForm.value)
          .then(() => {
            const query = route.query;
            const otherQueryParams = Object.keys(query).reduce((acc, cur) => {
              if (cur !== "redirect") {
                acc[cur] = query[cur];
              }
              return acc;
            }, {});
            ElMessage({
              message: "登录成功",
              type: "success",
              duration: 2000,
              onClose: () => {
                sendPointRequestAsync({
                  event: "PageView",
                  eventDescribe: "浏览访问登录页",
                  content: "",
                  num: stayTime.value,
                }).finally(() => {
                  if (!!route.query.code) {
                    console.log("路径带有code参数");
                    router.replace({
                      path: "/mainPageIndex",
                      query: { t: Date.now() },
                    }); // 添加时间戳强制刷新
                  } else {
                    router.push({ path: "" });
                  }
                });
              },
            });
            localStorage.setItem("isLogin", 1);
          })
          .catch(() => {
            loading.value = false;
            // 重新获取验证码
            if (captchaEnabled.value) {
              getCode();
            }
          });
      } else if (activeName.value == "third" && !codeSuccess.value) {
        const { phone, code } = loginForm.value;
        // console.log({ unionId: unionId.value, code, phone }, "传参");
        wxLoginByUnionId({
          unionId: unionId.value,
          code,
          phone,
          permission: keyMap.value[pathName.replace(/\//g, "")] || "",
        })
          .then((res) => {
            // console.log(res, "res");
            if (res.data.access_token) {
              console.log("存在token");
              setToken(res.data.access_token);
              userStore.token = res.data.access_token;
              ElMessage({
                message: "登录成功",
                type: "success",
                duration: 2000,
                onClose: () => {
                  console.log(router);
                  sendPointRequestAsync({
                    event: "PageView",
                    eventDescribe: "浏览访问登录页",
                    content: "",
                    num: stayTime.value,
                  }).finally(() => router.push({ path: "" }));
                },
              });
              localStorage.setItem("isLogin", 1);
            }
          })
          .catch(() => {
            loading.value = false;
            loginForm.value.code = "";
          });
      }
    }
  });
}

function getCode() {
  getCodeImg().then((res) => {
    captchaEnabled.value =
      res.captchaEnabled === undefined ? true : res.captchaEnabled;
    if (captchaEnabled.value) {
      codeUrl.value = "data:image/gif;base64," + res.data.img;
      loginForm.value.uuid = res.data.uuid;
    }
  });
}

function getCookie() {
  const username = Cookies.get("username");
  const password = Cookies.get("password");
  const rememberMe = Cookies.get("rememberMe");
  loginForm.value = {
    username: username === undefined ? loginForm.value.username : username,
    password:
      password === undefined ? loginForm.value.password : decrypt(password),
    rememberMe: rememberMe === undefined ? false : Boolean(rememberMe),
  };
}

getCode();
getCookie();
</script>

<style lang="scss" scoped>
.login {
  position: relative;
  display: flex;
  height: 100vh;
  background-image: url("../assets/images/login_bg.png");
  background-size: 100% 100%;
  .item-code {
    display: flex;
    gap: 10px;
    width: 100%;
    .el-button {
      width: 60%;
      height: 38px;
    }
  }
  &-left {
    // border: 1px solid red;
    margin: 3vw;
    width: 45vw;
    height: 40vw;
    // min-height: 540px;
    position: relative;
    background-image: url("../assets/images/login_banner.png");
    background-size: 100% 100%;
    // &::before{
    //   position: absolute;
    //   content: '';
    //   top: 0;
    //   left: 0;
    //   width: 10vw;
    //   height: 7.5vw;
    //   background-image: url("../assets/images/login_logo_text.png");
    //   background-size: 100% 100%;
    // }
  }
  &-right {
    top: 0;
    right: 0;
    position: absolute;
    // border: 1px solid red;
    width: 35vw;
    // height: 50.9vw;
    // height: 46vw;
    height: 100vh;
    background-color: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(0.5vw);
    display: flex;
    justify-content: center;
    align-items: center;
  }
}

.title {
  // border: 1px solid red;
  display: inline-block;
  padding: 1vw 0 1vw 4vw;
  position: relative;
  font-weight: bold;
  font-size: 1.5vw;
  color: #303234;
  letter-spacing: 0.1vw;
  margin-bottom: 1vw;
  &::before {
    position: absolute;
    content: "";
    top: 0.35vw;
    left: 0;
    width: 3.5vw;
    height: 3.5vw;
    background-image: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/login_logo.png");
    background-size: 100% 100%;
  }
}

.login-form {
  text-align: center;
  border-radius: 6px;
  // border: 1px solid red;
  min-width: 350px;
  padding: 25px 25px 5px 25px;
  :deep(.el-form-item--default) {
    margin-bottom: 1vw;
  }
  &_label {
    text-align: left;
    margin-bottom: 0.7vw;
  }
  .el-input {
    height: 40px;

    input {
      height: 40px;
    }
  }

  .input-icon {
    height: 39px;
    width: 14px;
    margin-left: 0px;
  }
}

.login-tip {
  font-size: 13px;
  text-align: center;
  color: #bfbfbf;
}

.login-code {
  position: absolute;
  right: 0.1vw;
  top: 0.1vw;
  img {
    width: 8vw;
    max-height: 2.2vw;
    // max-height: 1vw;
    // height: 90%;
    cursor: pointer;
    vertical-align: middle;
  }
}

.login-code-img {
  height: 30px;
  // height: 90%;
  padding-left: 12px;
}
.el-login-footer {
  height: 40px;
  line-height: 40px;
  position: fixed;
  bottom: 0;
  width: 100%;
  text-align: center;
  color: #fff;
  font-family: Arial;
  font-size: 12px;
  letter-spacing: 1px;
}
</style>
