import request from '@/utils/request'

// 查询机构列表
export function queryCorpList() {
  return request({
    url: '/system/schoolCorp/queryCorpList',
    method: 'get',
  })
}

// 查询机构的子机构列表
export function queryCorpChildrenList(params) {
  return request({
    url: '/system/schoolCorp/queryCorpChildrenList',
    method: 'get',
    params
  })
}

// 添加下级机构
export function addCorp(data) {
    return request({
      url: '/system/schoolCorp/addCorp',
      method: 'post',
      data
    })
}

// 编辑下级机构
export function updateCorp(data) {
    return request({
      url: '/system/schoolCorp/updateCorp',
      method: 'post',
      data
    })
}

// 删除机构
export function deleteCorp(data) {
    return request({
      url: '/system/schoolCorp/deleteCorp',
      method: 'post',
      data
    })
}

// 查询部门树
export function listTree(params) {
    return request({
      url: '/system/dept/listTree',
      method: 'get',
      params
    })
}
  
// 新增部门
export function addDept(data) {
    return request({
        url: '/system/dept/addDept',
        method: 'post',
        data
    })
}
  
// 编辑部门
export function updateDept(data) {
    return request({
      url: "/system/dept/editDept",
      method: "post",
      data,
    });
}
  
// 删除部门
export function delDept(id) {
  return request({
    url: `/system/dept/deleteDept/${id}`,
    method: "get",
  });
}

// 查询部门详细 /system/dept/63
export function getDept(deptId) {
  return request({
    url: `/system/dept/${deptId}`,
    method: 'get'
  })
}