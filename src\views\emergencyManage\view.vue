<template>
  <div class="viewEmergency app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>预案详情</span>
        </div>
      </template>

      <el-form :model="form" ref="formRef" :rules="rules">
        <el-descriptions title="" border style="margin-top: 20px" :column="3">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案编号"
          >
            <el-form-item label="" prop="code">
              <el-input
                v-model="form.code"
                style="width: 100%"
                readonly
                placeholder="-"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案类型"
          >
            <el-form-item label="" prop="type">
              <el-select
                v-model="form.type"
                placeholder="请选择预案类型"
                style="width: 100%"
                :disabled="!isEditing"
                clearable
              >
                <el-option label="硬件故障" value="硬件故障" />
                <el-option label="网络故障" value="网络故障" />
                <el-option label="软件故障" value="软件故障" />
                <el-option label="其他" value="其他" />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案文件"
          >
            <el-form-item label="" prop="fileUrl">
              <div v-if="form.fileName && !isEditing" class="file-display">
                <a :href="form.fileUrl" target="_blank">{{ form.fileName }}</a>
              </div>
              <template v-if="isEditing">
                <div v-if="form.fileName" class="file-display">
                  <a :href="form.fileUrl" target="_blank">{{
                    form.fileName
                  }}</a>
                  <el-button type="danger" link @click="handleDeleteFile">
                    删除
                  </el-button>
                </div>
                <fileUpload
                  v-if="!form.fileName"
                  @update:modelValue="
                    (url) => {
                      form.fileUrl = url;
                    }
                  "
                  :showFileList="true"
                  :type="5"
                  :fileSize="20"
                  :limit="1"
                  :fileType="[
                    'doc',
                    'docx',
                    'xls',
                    'xlsx',
                    'txt',
                  ]"
                  :modelValue="form.fileUrl"
                />
              </template>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="预案名称"
          >
            <el-form-item label="" prop="name">
              <el-input
                v-model="form.name"
                style="width: 100%"
                :readonly="!isEditing"
                placeholder="-"
                maxlength="30"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="预案内容"
          >
            <el-form-item label="" prop="content">
              <el-input
                v-model="form.content"
                style="width: 100%"
                type="textarea"
                :readonly="!isEditing"
                placeholder="-"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="修改时间"
          >
            {{ form.updateTime }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="创建时间"
          >
            {{ form.createTime }}
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <div class="viewEmergency-btns">
        <el-button type="primary" @click="handleSubmit">{{
          isEditing ? "保存" : "编辑"
        }}</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { ElMessage, ElMessageBox } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { schoolPlanInfo, updateSchoolPlan } from "@/api/emergency";
import fileUpload from "@/components/FileUpload/index.vue";

const router = useRouter();
const route = useRoute();
const uploadRef = ref(null);
const mode = ref(route.query.mode || "view"); // 'view' 或 'edit'
const isEditing = ref(false); // 默认不是编辑状态
const errors = ref({});
const formRef = ref(null)
// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  code: "",
  fileName: "",
  fileUrl: "",
  status: null,
  createTime: "",
  updateTime: "",
});

const rules = ref({
  type: [{ required: true, message: "预案类型不能为空", trigger: "change" }],
  name: [{ required: true, message: "预案名称不能为空", trigger: "blur" }],
  fileUrl: [{ required: true, message: "预案文件不能为空", trigger: "change" }],
  content: [{ required: true, message: "预案内容不能为空", trigger: "blur" }],
});

// 获取预案类型名称
const getTypeName = (type) => {
  const typeMap = {
    natural: "自然灾害",
    accident: "事故灾难",
    health: "公共卫生",
    security: "社会安全",
  };
  return typeMap[type] || type;
};

// 格式化日期
const formatDate = (dateStr) => {
  if (!dateStr) return "";
  const date = new Date(dateStr);
  return `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(
    2,
    "0"
  )}-${String(date.getDate()).padStart(2, "0")} ${String(
    date.getHours()
  ).padStart(2, "0")}:${String(date.getMinutes()).padStart(2, "0")}`;
};

// 文件上传前的验证
const beforeUpload = (file) => {
  const allowedTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
  ];
  const allowedExtensions = ["doc", "docx", "xls", "xlsx", "txt"];
  const extension = file.name.split(".").pop().toLowerCase();
  const isValidType =
    allowedTypes.includes(file.type) || allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  errors.value.file = "";

  if (!isValidType) {
    errors.value.file = "只能上传doc、docx、xls、xlsx、txt格式的文件！";
    return false;
  }
  if (!isLt20M) {
    errors.value.file = "文件大小不能超过20MB！";
    return false;
  }
  return true;
};

// 文件改变时的处理
const handleFileChange = (file) => {
  if (file) {
    form.value.file = file.raw;
    errors.value.file = "";
  }
};

// 返回
const handleBack = () => {
  router.back();
};

// 处理提交
const handleSubmit = () => {
  if (isEditing.value) {
    handleSave();
  } else {
    handleEdit();
  }
};

// 进入编辑模式
const handleEdit = () => {
  isEditing.value = true;
};

// 验证表单
const validateForm = () => {
  errors.value = {};

  if (!form.value.name) {
    errors.value.name = "请输入预案名称";
    ElMessage.error("请输入预案名称");
    return false;
  }
  if (!form.value.type) {
    errors.value.type = "请选择预案类型";
    ElMessage.error("请选择预案类型");
    return false;
  }
  if (!form.value.content) {
    errors.value.content = "请输入预案内容";
    ElMessage.error("请输入预案内容");
    return false;
  }
  // 修改文件验证逻辑：检查 fileUrl
  if (!form.value.fileUrl) {
    errors.value.file = "请上传预案文件";
    ElMessage.error("请上传预案文件");
    return false;
  }

  return true;
};

// 保存
const handleSave = async () => {
  // if (!validateForm()) return;
  try {
    formRef.value.validate((valid) => {
      if (valid) {
        updateSchoolPlan(form.value)
          .then((res) => {
            if (res.code === 200) {
              ElMessage.success("保存成功");
              isEditing.value = false;
              // 重新获取最新数据
              schoolPlanInfo(route.query.id).then((res) => {
                console.log(res)
                if (res.code === 200 && res.data) {
                  form.value = {
                    ...res.data,
                    file: null,
                    createTime: formatDate(res.data.createTime),
                    updateTime: formatDate(res.data.updateTime),
                  };
                }
              });
            } else {
              ElMessage.error(res.msg || "保存失败");
            }
          })
          .catch((error) => {
            console.error("保存失败:", error);
            ElMessage.error("保存失败");
          });
      }
    });
  } catch (error) {
    console.error("保存失败:", error);
    ElMessage.error("保存失败");
  }
};

// 删除文件
const handleDeleteFile = () => {
  ElMessageBox.confirm("确定要删除该文件吗？", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      form.value.fileName = "";
      form.value.fileUrl = "";
      ElMessage.success("删除成功");
    })
    .catch(() => {
      // 取消删除操作
    });
};

// 初始化
onMounted(() => {
  if (route.query.id) {
    schoolPlanInfo(route.query.id)
      .then((res) => {
        if (res.code === 200 && res.data) {
          form.value = {
            ...res.data,
            file: null, // 保持file字段为null，因为文件需要单独处理
          };
          // 更新显示在界面上的数据
          errors.value = {
            number: res.data.code,
            name: res.data.name,
            content: res.data.content,
            createTime: formatDate(res.data.createTime),
            updateTime: formatDate(res.data.updateTime),
          };
        } else {
          ElMessage.error(res.msg || "获取预案详情失败");
          router.back();
        }
      })
      .catch((error) => {
        console.error("获取预案详情失败:", error);
        ElMessage.error("获取预案详情失败");
        router.back();
      });
  } else {
    ElMessage.error("参数错误");
    router.back();
  }
});
</script>

<style lang="scss" scoped>
.viewEmergency {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
}

.upload-demo {
  .upload-tip {
    line-height: 1.2;
    margin-top: 5px;
    font-size: 12px;
    color: #909399;
  }
}

.file-display {
  display: flex;
  align-items: center;
  gap: 10px;

  a {
    color: #409eff;
    text-decoration: none;

    &:hover {
      text-decoration: underline;
    }
  }
}
</style>