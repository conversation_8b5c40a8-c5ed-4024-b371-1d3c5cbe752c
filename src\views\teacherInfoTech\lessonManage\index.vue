<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                </div>
            </template>
            <div class="scedule">
                <el-row :gutter="10" class="mb8">
                    <el-col :span="1.5">
                        <el-button type="primary" plain icon="Edit" @click="open2 = true">
                            设置课表时间
                        </el-button>
                    </el-col>
                </el-row>
                <el-table v-loading="loading2" :data="classData" border>
                    <el-table-column v-for="(item, index) in classNum" :key="index"
                        :label="`第${week[index] ? week[index] : (index + 1)}节课`" align="center" minWidth="120px"
                        :prop="`startTime${index}`" />
                </el-table>
            </div>

            <el-form :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch">
                <el-form-item label="" prop="name">
                    <el-input v-model="queryParams.name" placeholder="请输入教师姓名" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="grade">
                    <el-select v-model="queryParams.grade" placeholder="请选择年级" @change="handleQuery" clearable filterable
                        style="width: 200px">
                        <el-option v-for="item in gradeList" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item label="" prop="clbum">
                    <el-select v-model="queryParams.clbum" placeholder="请选择班级" @change="handleQuery" clearable filterable
                        style="width: 200px">
                        <el-option v-for="item in classList" :key="item" :label="item" :value="item">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb8">
                <el-col :span="1.5">
                    <el-upload :action="uploadFileUrl" :headers="headers" :show-file-list="false"
                        :before-upload="beforeUpload" :on-success="handleUploadSuccess">
                        <el-button type="warning" plain icon="Upload">
                            导入课表安排
                        </el-button>
                    </el-upload>
                </el-col>
                <el-col :span="1.5">
                    <el-button type="success" plain icon="Download" @click="handleDownload">
                        下载导入模板
                    </el-button>
                </el-col>
                <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar>
            </el-row>

            <el-table v-loading="loading" :data="classInfoList" border>
                <el-table-column label="时间（日期）" align="center" minWidth="120px" prop="weekDay" />
                <el-table-column label="节数" align="center" minWidth="120px" prop="pitchNumber" />
                <el-table-column label="教师姓名" align="center" minWidth="120px" prop="teacherName" />
                <el-table-column label="联系方式" align="center" minWidth="120px" prop="teacherPhone" />
                <el-table-column label="年级" align="center" minWidth="120px" prop="grade" />
                <el-table-column label="班级" align="center" minWidth="120px" prop="clbum" />
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.current"
                v-model:limit="queryParams.size" @pagination="getList" />
        </el-card>
        <ExportRes :data="exportRes" :open="open" @setOpen="(val) => { open = val }" />
        <el-dialog title="设置课表时间" v-model="open2" width="500px" append-to-body :close-on-click-modal="false">
            <el-form ref="lessonRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="课时时长" prop="duration">
                    <el-select v-model="form.duration" placeholder="请先选择课时时长" :disabled="form.timeList.length > 1"
                        style="width: 230px;">
                        <el-option v-for="item in hourList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option></el-select>
                </el-form-item>
                <el-form-item v-for="(item, index) in form.timeList" :key="index" :label="`第${index + 1}节课`"
                    :prop="`timeList.${index}.startTime`" :rules="{
                        required: true,
                        message: '开始时间不能为空',
                        trigger: 'blur',
                    }">
                    <el-time-picker v-model="form.timeList[index].startTime" format="HH:mm" placeholder="请选择开始时间"
                        @blur="form.timeList[index].endTime = getTime(form.timeList[index].startTime, form.duration)"
                        :disabled-hours="() => disabledTime(null, index)"
                        :disabled-minutes="(hour) => disabledTime(hour, index)"
                        :disabled="index !== form.timeList.length - 1 || !form.duration"
                        style="width: 230px;margin-right: 20px;"></el-time-picker>
                    <el-button v-if="form.timeList.length > 1 && index === form.timeList.length - 1" class="mt-2"
                        @click.prevent="removeItem(index)" type="danger" plain icon="Delete" circle></el-button>
                </el-form-item>
                <el-form-item v-if="form.duration && form.timeList[0].startTime">
                    <el-button @click="addItem" type="primary" plain icon="Plus" circle></el-button>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="Post">
import ExportRes from '@/components/ExportRes'
import { onMounted, reactive } from 'vue';
import { useRoute } from 'vue-router';
import { getToken } from "@/utils/auth";
import { downloadBlob } from '@/utils';
import { parseTime } from '@/utils/ruoyi'
import { queClassPage, queClassTime, queGradeClassList, addClassTime } from '@/api/teachInfo/class'
import { templateDownload } from '@/api/mediaTeach/ledger';
import { sm2Decrypt } from '@/utils/sm2encrypt'


const { proxy } = getCurrentInstance();
const route = useRoute()
const uploadFileUrl = ref(import.meta.env.VITE_APP_BASE_API + "/system/classSchedule/importClassSchedule");
const headers = ref({ Authorization: "Bearer " + getToken() });

const classData = ref([])
const classNum = ref(0)
const week = ref(['一', '二', '三', '四', '五', '六', '七', '八', '九', '十'])
const classInfoList = ref([]);
const loading = ref(false);
const loading2 = ref(false);
const showSearch = ref(true);
const total = ref(0);
const open = ref(false)
const open2 = ref(false)
const exportRes = ref({})
const gradeList = ref([])
const classList = ref([])
const hourList = ref([
    { label: '35分钟', value: 35 },
    { label: '40分钟', value: 40 },
    { label: '45分钟', value: 45 },
    { label: '50分钟', value: 50 },
    { label: '55分钟', value: 55 },
])

const data = reactive({
    form: {
        duration: null,
        timeList: [{ startTime: null, endTime: null }]
    },
    queryParams: {
        current: 1,
        size: 5,
        name: '',
        grade: '',
        clbum: '',
    },
    rules: {
        duration: [{ required: true, message: '请选择课时时长', trigger: ['blur', 'change'] }]
    }
});

const { queryParams, form, rules } = toRefs(data);


/** 查询课表列表 */
function getList() {
    loading.value = true;
    queClassPage(queryParams.value).then(response => {
        classInfoList.value = response.data.records
        total.value = response.data.total
        loading.value = false
    })
}

function getClassGrade() {
    queGradeClassList().then(response => {
        gradeList.value = response.data.gradeList
        classList.value = response.data.classList
    })
}

/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.current = 1;
    getList()
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}

function handleDownload() {
    templateDownload({ modelName: 'import_class' }).then(response => {
        downloadBlob(response, 'application/vnd.ms-excel', '课表导入模板')
    })
}
/** 取消按钮 */
function cancel() {
    open2.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        duration: null,
        timeList: [{ startTime: null, endTime: null }]
    };
    proxy.resetForm("lessonRef");
}

function getClassTime() {
    loading2.value = true;
    queClassTime().then(response => {
        if (response.data.duration !== 0) {
            classData.value[0] = {}
            classNum.value = 0
            form.value = response.data
            form.value.timeList.forEach((item, index) => {
                classNum.value = classNum.value + 1
                classData.value[0][`startTime${index}`] = item.startTime
                item.startTime = new Date(`2023-12-12 ${item.startTime}`)
            })
        }
        loading2.value = false;
    })
}

function addItem() {
    form.value.timeList.push({ startTime: null, endTime: null })
}

function removeItem(index) {
    if (form.value.timeList.length === 1) {
        proxy.$modal.msgWarning("至少保留一节课");
    } else {
        form.value.timeList.splice(index, 1)
    }
}

/** 提交表单 */
function submitForm() {
    proxy.$refs["lessonRef"].validate(valid => {
        if (valid) {
            let data = JSON.parse(JSON.stringify(form.value))
            data.timeList.forEach(item => {
                item.startTime = parseTime(item.startTime, '{h}:{i}')
            })
            // console.log(data)
            addClassTime(data).then(response => {
                proxy.$modal.msgSuccess("设置成功！");
                getClassTime()
                open2.value = false
            })
        }
    });
}

const getTime = (time, dur) => {
    let sTime = typeof time === 'string' ? time : parseTime(time, '{h}:{i}')
    let sArr = sTime.split(':')
    if (sArr[1] * 1 + dur < 60) {
        return `${sArr[0]}:${sArr[1] * 1 + dur}`
    } else {
        let h = sArr[0] * 1 + 1
        let over = (sArr[1] * 1 + dur) - 60
        return `${h > 9 ? h : ('0' + h)}:${over > 9 ? over : ('0' + over)}`
    }
}

const makeRange = (start, end) => {
    const result = []
    for (let i = start; i <= end; i++) {
        result.push(i)
    }
    return result
}

const disabledTime = (hour = null, index) => {
    let len = form.value.timeList.length
    if (len > 1 && index === len - 1) {
        let rangeTime = getTime(form.value.timeList[len - 2].endTime, 10)
        let rArr = rangeTime.split(':')
        if (hour !== null && rArr[0] * 1 === hour * 1 && rArr[1] * 1 - 1 > 0) {
            return makeRange(0, rArr[1] * 1 - 1)
        }
        if (hour === null) return makeRange(0, rArr[0] * 1 - 1)
    }
}

const beforeUpload = (file) => {
    const Xls = file.name.split(".");
    if (Xls[1] !== "xls" && Xls[1] !== "xlsx") {
        proxy.$modal.msgError("请上传excel格式的文件!");
        return false;
    } else if (file.size / 1024 / 1024 > 250) {
        proxy.$modal.msgError("请上传250M以下的文件!");
        return false;
    }
    return true;
}

const handleUploadSuccess = (res, file) => {
    if (res.code === 200) {
        getList()
        getClassGrade()
        exportRes.value = JSON.parse(sm2Decrypt(res.data))
        let { errorCount } = exportRes.value
        if (!errorCount) {
            proxy.$modal.msgSuccess('导入成功');
        } else {
            open.value = true
        }
    } else {
        proxy.$modal.msgError(res.msg);
    }
}

onMounted(() => {
    getList()
    getClassTime()
    getClassGrade()
})
</script>

<style lang="scss" scoped>
.literacy-header {
    margin: 10px 0;
}

.scedule {
    margin-bottom: 60px;
}

.card-header {
    padding: 10px 10px 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;

    .opts {
        display: flex;
        align-items: center;

        .item {
            cursor: pointer;
            margin-right: 20px;
            color: gray;
        }

        .item.active {
            color: dodgerblue;
        }
    }
}

.echarts-box {
    padding: 20px 0;
}
</style>
