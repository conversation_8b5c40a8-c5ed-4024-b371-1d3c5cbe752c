<template>
  <div class="deviceUnlock" v-loading="loading">
    <div class="deviceUnlock-list">
      <div
        class="deviceUnlock-item"
        v-for="(item, index) in formList"
        :key="item.id"
      >
        <div style="min-width: 500px">
          <div class="deviceUnlock-header">
            <div class="flex" style="gap: 0">
              <span>自动解锁规则</span>
              <div class="flex">
                <el-tooltip
                  class="box-item"
                  effect="dark"
                  content="在下述生效规则下，生效设备重启后按设定规则自动解锁状态"
                  placement="top-start"
                >
                  <el-icon :size="16" style="margin-left: 5px"
                    ><QuestionFilled
                  /></el-icon>
                </el-tooltip>
                <el-tooltip content="修改" placement="top">
                  <el-button
                    v-show="!item.isEdit"
                    type="primary"
                    style="font-size: 20px; margin-left: 10px"
                    icon="Edit"
                    link
                    :disabled="item.disabled"
                    @click="handleEdit(index)"
                  ></el-button>
                </el-tooltip>
              </div>
            </div>
            <el-tooltip content="删除" placement="top">
              <el-button
                v-show="!item.isEdit && !item.id && formList.length > 1"
                type="danger"
                style="font-size: 20px"
                icon="Delete"
                link
                :disabled="item.disabled"
                @click="handleDel(index)"
              ></el-button>
            </el-tooltip>
          </div>
          <div
            v-show="item.isEdit || !!item.id"
            class="deviceUnlock-content"
            :class="{ 'deviceUnlock-content_unedit': !item.isEdit }"
          >
            <div>
              <el-form
                :ref="(el) => setFormRef(el, index)"
                :model="item"
                :label-width="item.isEdit ? '92px' : '82px'"
              >
                <el-form-item
                  :label="item.isEdit ? '启动计划：' : '启用状态：'"
                  prop="isAutoLock"
                  label-width="82px"
                >
                  <el-switch
                    v-if="item.isEdit"
                    v-model="item.isAutoLock"
                    :active-value="true"
                    :inactive-value="false"
                    @change="(val) => handleStatusChange(val, index)"
                  />
                  <span v-else>{{
                    item.isAutoLock ? "已启用" : "已禁用"
                  }}</span>
                </el-form-item>
                <el-form-item
                  v-if="item.isEdit"
                  prop="isLock"
                  label-width="147px"
                >
                  <template #label>
                    <div class="flex">
                      <span>是否允许手动锁定</span>
                      <el-tooltip
                        class="box-item"
                        effect="dark"
                        content="关闭后，在指定解锁时间段内无法手动锁定"
                        placement="top-start"
                      >
                        <el-icon :size="18"><QuestionFilled /></el-icon>
                      </el-tooltip>
                    </div>
                  </template>
                  <el-switch
                    v-model="item.isLock"
                    :active-value="true"
                    :inactive-value="false"
                    :disabled="
                      (!!item.id && !item.isAutoLock && item.isEdit) ||
                      !item.isEdit
                    "
                  />
                </el-form-item>
                <div v-if="!item.isEdit" style="padding: 5px 0">
                  {{ item.isLock ? "允许手动锁定" : "不允许手动锁定" }}
                </div>
                <el-form-item
                  v-for="(item2, index2) in item.timeRangeList"
                  :key="index2"
                  :prop="`timeRangeList.${index2}`"
                  :label-width="item.isEdit ? '52' : '32'"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validTimeRange(rule, value, callback, index),
                    trigger: 'change',
                  }"
                  :class="{ formitem_unedit: !item.isEdit }"
                >
                  <template #label>
                    <div
                      class="timeIndex"
                      :class="{ timeIndex_unedit: !item.isEdit }"
                    >
                      {{ index2 + 1 }}
                    </div>
                  </template>
                  <div v-if="item.isEdit" class="timeRow">
                    <el-time-picker
                      v-model="item.timeRangeList[index2]"
                      is-range
                      range-separator="~"
                      start-placeholder="开始时间"
                      end-placeholder="结束时间"
                      format="HH:mm:ss"
                      value-format="HH:mm:ss"
                      style="
                        margin-bottom: 5px;
                        width: 100%;
                        flex-grow: inherit;
                      "
                      :disabled="!!item.id && !item.isAutoLock"
                      @change="changeTimeList(index)"
                    />
                    <el-button
                      v-if="
                        item.timeRangeList.length > 1 &&
                        (!item.id || item.isAutoLock)
                      "
                      type="danger"
                      icon="Minus"
                      circle
                      @click="handleDelTime(item2, index2, index)"
                    />
                  </div>
                  <span v-else>{{ item2.join(" ~ ") || "-" }}</span>
                </el-form-item>

                <div
                  class="timeAdd"
                  @click="handleAddTime(index)"
                  v-if="
                    item.timeRangeList.length < 10 &&
                    item.isEdit &&
                    (!item.id || item.isAutoLock)
                  "
                >
                  <el-button type="primary" icon="Plus" color="#333" circle />
                  新增时间段，最多添加十个
                </div>

                <el-form-item
                  label="生效设备："
                  prop="selectedDevices"
                  :rules="{
                    required: item.isEdit,
                    validator: (rule, value, callback) =>
                      validSelectedDevices(rule, value, callback, index),
                    trigger: 'change',
                  }"
                >
                  <deviceSel
                    v-model="item.selectedDevices"
                    :isEdit="item.isEdit"
                    :selectedAllCodes="selectedAllCodes"
                    :device-codes="item.deviceCodes"
                    :disabled="!!item.id && item.isAutoLock === false"
                    :btnDisabled="item.disabled"
                    @valid="handleValid(index)"
                    @change="(val) => handleChangeSelectedDevices(val, index)"
                  />
                </el-form-item>
              </el-form>
              <div class="deviceUnlock-footer" v-if="item.isEdit">
                <el-button type="primary" @click="submitForm(index)" v-throttle
                  >保存</el-button
                >
                <el-button @click="handleCancel(index)">取消</el-button>
              </div>
            </div>
            <div v-show="!item.isEdit && (!!item.id || formList.length > 1)">
              <el-tooltip content="删除" placement="top">
                <el-button
                  type="danger"
                  style="font-size: 20px"
                  icon="Delete"
                  link
                  :disabled="item.disabled"
                  @click="handleDel(index)"
                ></el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="deviceUnlock-add"
      v-if="formList.length > 0 && showAdd"
      @click="handleAdd"
    >
      <el-icon><Plus /></el-icon> 新增计划，最多五个计划
    </div>
  </div>
</template>

<script setup>
import deviceSel from "../deviceSel.vue";
import { ref, reactive, toRefs, getCurrentInstance, onMounted } from "vue";
import { timeFormat } from "@/utils";
import { addSchoolDeviceLog } from "@/api/deviceControl";
import {
  getAllRules,
  deleteLockScreenRule,
  lockScreenRuleBatch_nomsg,
} from "@/api/deviceControl/plan";

const { proxy } = getCurrentInstance();

const formRefs = ref({});
// 设置表单引用的函数
const setFormRef = (el, index) => {
  if (el) {
    formRefs.value[index] = el;
  } else {
    delete formRefs.value[index];
  }
};
const showAdd = computed(() => {
  let flag = state.formList.every((_) => !!_.id && !_.isEdit);
  return state.formList.length < 5 && flag;
});
const defaultForm = {
  isEdit: false,
  disabled: false,
  id: "",
  isLock: false,
  isAutoLock: false,
  timeRangeList: [["08:00:00", "18:00:00"]],
  selectedDevices: "",
  deviceCodes: [],
};
const state = reactive({
  selectedAllCodes: [],
  loading: false,
  initFormList: [],
  formList: [],
});
const { initFormList, loading, formList, selectedAllCodes } = toRefs(state);

watch(
  () => state.formList,
  (val) => {
    let flag = val.every((_) => !_.isEdit);
    console.log("flag", flag);
    if (flag) {
      state.formList.forEach((item) => {
        item.disabled = false;
      });
    } else {
      state.formList.forEach((item) => {
        if (!item.isEdit) {
          item.disabled = true;
        }
      });
    }
  },
  {
    deep: true,
  }
);

onMounted(() => getList());

const handleValid = (index) => {
  formRefs.value[index].validateField(`selectedDevices`);
};

const handleChangeSelectedDevices = (val, index) => {
  state.formList[index].selectedDevices = val?.join(",");
};

function getList() {
  loading.value = true;
  // formList.value = [];
  getAllRules()
    .then((res) => {
      console.log(res, "解锁计划列表");
      if (res.code === 200) {
        if (res.data?.length > 0) {
          formList.value = res.data?.map((item) => {
            let obj = {
              ...item,
              disabled: false,
              isEdit: false,
              selectedDevices: item.deviceCodes.join(","),
              timeRangeList: item.timeRangeList?.map((item2) => [
                item2.startTime,
                item2.endTime,
              ]) || [["08:00:00", "18:00:00"]],
            };
            return obj;
          });
          selectedAllCodes.value = [
            ...new Set(res.data?.flatMap((item) => item.deviceCodes)),
          ];
          console.log(selectedAllCodes.value);
        } else {
          formList.value = [defaultForm];
        }

        initFormList.value = JSON.parse(JSON.stringify(formList.value));
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

// 时间字符串转分钟数
const timeToMinutes = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes] = timeStr.split(":").map(Number);
  return hours * 60 + minutes;
};

// 时间字符串转秒数
const timeToSeconds = (timeStr) => {
  if (!timeStr) return 0;
  const [hours, minutes, seconds] = timeStr.split(":").map(Number);
  return hours * 60 * 60 + minutes * 60 + seconds;
};

// 校验时间段重叠
const validateTimeRanges = (index) => {
  if (!formRefs.value[index]) return;

  state.formList[index].timeRangeList.forEach((_, index2) => {
    formRefs.value[index].validateField(`timeRangeList.${index2}`);
  });
};

// 检测时间段是否重叠
const hasOverlap = (rangeA, rangeB) => {
  if (!rangeA || !rangeB || rangeA.length !== 2 || rangeB.length !== 2)
    return false;

  const [startA, endA] = rangeA.map(timeToMinutes);
  const [startB, endB] = rangeB.map(timeToMinutes);

  return (startA < endB && endA > startB) || (startA == startB && endA > endB);
};

// 校验时间范围
const validTimeRange = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (
    state.formList[index].isAutoLock === false &&
    !!state.formList[index].id
  ) {
    callback();
    return;
  }

  console.log("value", value, "index", index);
  if (!value || value.length !== 2) {
    callback(new Error("请选择完整时间段"));
    return;
  }

  const [start, end] = value;
  const startMin = timeToSeconds(start);
  const endMin = timeToSeconds(end);

  // 检查开始时间是否小于结束时间
  if (startMin >= endMin) {
    return callback(new Error("开始时间必须小于结束时间"));
  }

  const currentIndex = Number(rule.field.split(".")[1]);
  const hasDuplicate = state.formList[index].timeRangeList.some(
    (range, index) => {
      return index !== currentIndex && hasOverlap(value, range);
    }
  );
  console.log("进入检验", hasDuplicate);

  hasDuplicate
    ? callback(new Error("注：生效时间包含重复时间段，请重新选择"))
    : callback();
};

// 校验接收设备
const validSelectedDevices = (rule, value, callback, index) => {
  // 如果状态为禁用，跳过验证
  if (
    state.formList[index].isAutoLock === false &&
    !!state.formList[index].id
  ) {
    callback();
    return;
  }

  if (!value) {
    callback(new Error("请选择生效设备"));
  } else {
    callback();
  }
};

const handleStatusChange = (val, index) => {
  let obj = state.formList[index];
  obj.isEdit = true;
  if (!val) formRefs.value[index]?.clearValidate();
};

const handleAdd = () => {
  state.formList.push(JSON.parse(JSON.stringify(defaultForm)));
};

const handleEdit = (index) => {
  state.formList[index].isEdit = true;
  if (!state.formList[index].id) state.formList[index].isAutoLock = true;
};

const handleDel = (index) => {
  if (state.formList[index].id) {
    proxy.$modal
      .confirm("删除后已设置的设备重启后失效，是否确认删除？")
      .then((res) => {
        loading.value = true;
        console.log("删除传参", {
          type: 1,
          id: state.formList[index].id,
        });
        deleteLockScreenRule({
          type: 1,
          id: state.formList[index].id,
        })
          .then((res) => {
            if (res.code === 200) {
              state.formList.splice(index, 1);
              proxy.$modal.msgSuccess("删除成功");
              getList(); // 重新获取列表数据
            }
          })
          .catch(() => {
            // proxy.$modal.msgError('删除失败')
          })
          .finally(() => {
            loading.value = false;
          });
      });
  } else {
    if (index == 0 && state.formList.length == 1) {
      resetForm(index);
    } else {
      state.formList.splice(index, 1);
    }
  }
};

const handleCancel = (index) => {
  if (formRefs.value[index]) {
    formRefs.value[index].resetFields();
  }
  // 清空展开状态
  // currentExpandedKeys.value = [];
  state.formList[index].isEdit = false;
  if (!state.formList[index].id) {
    resetForm(index);
  } else {
    state.formList[index] = JSON.parse(
      JSON.stringify(initFormList.value[index])
    );
  }
};

function changeTimeList(idx) {
  validateTimeRanges(idx);
}

const handleAddTime = (index) => {
  if (state.formList[index].timeRangeList.length < 10) {
    state.formList[index].timeRangeList.push(["08:00:00", "18:00:00"]);
  } else {
    proxy.$modal.msgWarning("最多只能添加十个时间段");
  }
};

const handleDelTime = (item, index, idx) => {
  state.formList[idx].timeRangeList.splice(index, 1);
  validateTimeRanges(idx);
};

const submitForm = (index) => {
  if (state.formList[index].isAutoLock === false && !state.formList[index].id) {
    proxy.$modal.alert("请先开启启动计划再保存");
    return;
  }
  console.log(formRefs.value[index]);
  if (!formRefs.value[index]) return;
  nextTick(() => {
    formRefs.value[index]?.validate((valid) => {
      if (valid) {
        //     // 构造提交数据
        const {
          isAutoLock,
          version,
          isLock,
          timeRangeList,
          selectedDevices,
          id,
        } =
          state[
            !state.formList[index].isAutoLock && !!state.formList[index].id
              ? "initFormList"
              : "formList"
          ][index];
        const submitData = {
          lockId: id,
          type: 1,
          version: version || 1,
          timeList:
            timeRangeList?.map((item) => ({
              startTime: item[0],
              endTime: item[1],
            })) || [],
          isLock,
          isAutoLock: state.formList[index].isAutoLock,
          deviceCodes: selectedDevices?.split(",") || [],
        };

        console.log("最终提交的数据:", submitData, JSON.stringify(submitData));

        loading.value = true;
        lockScreenRuleBatch_nomsg(submitData)
          .then(async (res) => {
            if (res.code === 200) {
              // 添加操作日志
              try {
                const logPromises = [];

                // 为每个设备创建一个解锁日志
                submitData.deviceCodes.forEach((deviceCode) => {
                  logPromises.push(
                    addSchoolDeviceLog({
                      logType: 4,
                      deviceCode: deviceCode,
                      logContent: "设备后台解锁",
                      deviceNum: 1,
                    })
                  );
                });

                // 等待所有日志添加完成
                await Promise.all(logPromises);
              } catch (error) {
                console.error("添加操作日志失败:", error);
              }

              proxy.$modal.msgSuccess("操作成功");
              // 操作成功后立即重新获取最新数据
              getList();
            } else {
              proxy.$modal.msgError(res.msg || "操作失败");
            }
          })
          .catch((e) => {
            console.log(e, "保存失败");
            proxy.$modal.msgError(e.response?.data?.msg);
            getList();
          })
          .finally(() => (loading.value = false));
      }
    });
  });
};

const resetForm = (index) => {
  state.formList[index] = JSON.parse(JSON.stringify(defaultForm));
};
</script>

<style scoped lang="scss">
.flex {
  display: flex;
  align-items: center;
  gap: 0 5px;
}
.deviceUnlock {
  font-size: 14px;

  &-list {
    display: flex;
    flex-direction: column;
    gap: 15px 0;
  }

  &-add {
    border: 1px solid #e5e5e5;
    border-top: none;
    padding: 10px;
    color: rgb(152, 152, 152);
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0 10px;
    &:hover {
      color: #7e96ae;
      background-color: #f8faff;
    }
  }

  &-item {
    border: 1px solid #e5e5e5;
    .timeIndex {
      background-color: #4095e5;
      color: #fff;
      border-radius: 50%;
      width: 30px;
      height: 30px;
      display: flex;
      align-items: center;
      justify-content: center;
      &_unedit {
        width: 20px;
        height: 20px;
        font-size: 12px;
      }
    }
    .formitem_unedit {
      :deep(.el-form-item__label) {
        display: flex;
        align-items: center;
      }
    }
    .timeRow {
      width: 100%;
      display: flex;
      gap: 0 10px;
    }
    .timeAdd {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      gap: 10px;
      cursor: pointer;
      width: 200px;
      margin: 10px auto;
    }
  }

  &-content {
    padding: 10px 20px 15px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    &_unedit {
      padding-bottom: 10px;
      :deep(.el-form-item--default) {
        margin-bottom: 0px;
      }
    }
  }

  &-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    font-size: 16px;
    padding: 10px 20px;
    border-bottom: 1px solid #e5e5e5;
  }

  &-footer {
    min-width: 500px;
    text-align: center;
  }
}
</style>