<template>
  <div class="desktop" v-if="!dDialog">
    <div class="desktop_box">
      <div class="back" @click="back">
        <img src="@/assets/icons/back.png" alt="" /> 返回
      </div>
      <div class="desktop_content">
        <div class="desktop_left">
          <div class="desktop_header">
            <div class="desktop_header_title">
              <img src="@/assets/icons/desktop.png" alt="" /> 实时桌面
            </div>
            <div
              class="desktop_header_screenshot"
              :class="
                !isOff || isChecking || isStandby || videoLoading
                  ? 'disabled-btn'
                  : ''
              "
              @click="
                isOff && !isChecking && !isStandby && !videoLoading
                  ? handleScreenshot()
                  : ''
              "
            >
              <img src="@/assets/icons/image.png" alt="" /> 截图
            </div>
          </div>
          <div
            class="desktop_screen"
            style="background-color: #333"
            ref="screenDom"
            v-loading="videoLoading"
          >
            <video
              id="videoPlayer"
              style="width: 100%; height: 100%; outline: none"
              autoplay
              controlsList="nodownload nofullscreen noplaybackrate nopictureinpicture"
              disablePictureInPicture
              disableRemotePlayback
              tabindex="0"
              v-if="isOff && !isChecking && !isStandby"
            ></video>
            <div v-else style="color: #fff; font-size: 2vw">
              {{
                !isOff
                  ? "设备已离线"
                  : isStandby
                  ? "设备已熄屏"
                  : isChecking
                  ? "该电脑正在被巡视中，请稍后"
                  : "加载中"
              }}
            </div>
          </div>
        </div>
        <div class="desktop_right">
          <div class="desktop_data">
            <div class="desktop_data_title">
              <img src="@/assets/icons/data.png" alt="" /> 设备信息
            </div>
            <div class="desktop_data_content">
              <div>
                <p class="desktop_data_content_title">设备编号</p>
                <p class="desktop_data_content_value">
                  {{ deviceInfoData.deviceCode }}
                </p>
              </div>
              <div>
                <p class="desktop_data_content_title">设备名称</p>
                <p class="desktop_data_content_value">
                  {{ deviceInfoData.deviceName }}
                </p>
              </div>
              <div>
                <p class="desktop_data_content_title">安装位置</p>
                <p class="desktop_data_content_value">
                  {{ deviceInfoData.installAddress }}
                </p>
              </div>
            </div>
             
            <div
              class="desktop_data_bottom"
              @click="
                () => {
                  router.push({
                    path: '/deviceLedger/deviceInfo',
                    query: {
                      id: deviceInfoData.deviceId,
                      type: 0,
                      from: 5,
                      isMqtt: deviceInfoData.isMqtt,
                    },
                  });
                }
              "
            >
                            点击查看详细参数            
            </div>
          </div>
          <div class="desktop_user">
            <div class="desktop_user_top">
              <img
                class="user_group"
                src="@/assets/icons/user_group.png"
                alt=""
              />
              <div class="user_add" @click="handleOpenAddUser">
                <img src="@/assets/icons/user_add.png" alt="" />
                <p>添加</p>
              </div>
            </div>
            <div class="desktop_user_content">
              <div class="content_title">设备使用者</div>
              <div class="content_name" :title="permissionsStr">
                {{ permissionsStr }}
              </div>
            </div>
            <div class="desktop_user_bottom">
              <div v-for="item in permissionsChar" :key="item">
                <div class="user_tag">{{ item }}</div>
              </div>
              <div
                v-if="
                  permissions.nickNames && permissions.nickNames.length > 10
                "
              >
                <div class="user_tag">...</div>
              </div>
              <div class="user_add_bottom" @click="handleOpenAddUser">
                <img src="@/assets/icons/user_add2.png" alt="" />
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="desktop_bottom">
        <div
          class="remote_desktop"
          :class="
            !isOff || isChecking || isStandby || videoLoading
              ? 'disabled-btn__dark'
              : ''
          "
          @click="
            isOff && !isChecking && !isStandby && !videoLoading
              ? operateDesk()
              : ''
          "
        >
          <!-- <div
          class="remote_desktop"
          :class="!isOff  ? 'disabled-btn__dark' : ''"
          @click="isOff ? operateDesk() : ''"
        > -->
          <img src="@/assets/icons/remote.png" alt="" /> 远程桌面
        </div>
        <div class="remote_operate">
          <el-button
            plain
            v-for="item in remoteData"
            :key="item.value"
            style="width: 7.5vw; height: 100%; font-size: 1.1vw"
            :color="item.color || ''"
            :type="item.type || ''"
            :disabled="
              (item.label != '远程开机' && !isOff) ||
              (isOff && item.label == '远程开机') ||
              isChecking ||
              videoLoading
            "
            @click="
              (isOff && !isChecking && !videoLoading) ||
              (item.label == '远程开机' && !isOff)
                ? handleOpeartionRemote(item.value)
                : ''
            "
          >
            {{ item.label }}
          </el-button>
        </div>
      </div>
    </div>

    <el-dialog
      v-model="timeDialog"
      title="选择时间"
      :show-close="false"
      :close-on-click-modal="false"
    >
      <el-date-picker
        v-model="timeData"
        type="datetime"
        value-format="YYYY-MM-DDTHH:mm:ssZ"
        placeholder="请选择关机时间"
        :disabled-date="disabledDateFn"
        style="width: 100%"
      />
      <template #footer>
        <el-button type="info" @click="timeDialog = false">取消</el-button>
        <el-button type="primary" @click="handleBatchFixed">确定</el-button>
      </template>
    </el-dialog>

    <el-dialog
      v-model="screenshotDialogVisible"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="400px"
      class="screenshot-dialog"
    >
      <div class="screenshot-placeholder">
        <div class="screenshot-success-message">
          <span>已截图，保存至截图图库中</span>
        </div>
      </div>
    </el-dialog>

    <el-dialog
      v-model="userDialogVisible"
      title="添加设备使用者"
      :show-close="false"
      :modal="true"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      width="800px"
      class="user_dialog"
      align-center
    >
      <div class="change_user_list">
        <div style="width: 180px; white-space: nowrap">已选择：</div>
        <div class="user_tag_list">
          <el-tag
            v-for="(tag, tagIndex) in permissions.nickNames"
            class="user_tag"
            :key="tag"
            closable
            @close="handleCloseTag(tag, tagIndex)"
            type="success"
          >
            {{ tag }}
          </el-tag>
        </div>
      </div>
      <div class="search_user">
        <el-input
          class="search_input"
          v-model="queryParams.name"
          placeholder="请输入姓名搜索"
          @keyup.enter="handleQuery"
        ></el-input>
        <el-button type="primary" icon="Search" @click="handleQuery"
          >搜索</el-button
        >
        <el-button icon="Refresh" @click="resetQuery">重置</el-button>
      </div>
      <el-table
        :data="tableList"
        v-loading="userLoading"
        style="width: 100%"
        max-height="300"
        @selection-change="handleSelectionChange"
        @select="handleSelection"
        ref="tableRef"
      >
        <el-table-column
          type="selection"
          width="55"
          :selectable="handleSelectable"
        />
        <el-table-column prop="nickName" label="姓名" />
        <el-table-column prop="employeesPostName" label="岗位">
          <template #default="scope">
            <span>{{ scope.row.employeesPostName || "-" }}</span>
          </template>
        </el-table-column>
      </el-table>
      <!-- <el-pagination
        background
        layout="prev, pager, next"
        :total="1000"
        :page-size="20"
        :current-page="1"
      /> -->
      <div class="user_dialog_footer">
        <el-button type="primary" @click="handleAddUser">保存</el-button>
        <el-button type="info" @click="handleCancel">取消</el-button>
      </div>
    </el-dialog>
  </div>
  <teleport to="body" v-if="dDialog">
    <div
      id="dialog_desk"
      ref="dragArea"
      class="dialog_desk"
      :class="dDialog ? 'dialog_desk' : ''"
    >
      <div
        class="close_desk"
        @click="
          dDialog = false;
          state.hasInitVideo = false;
        "
      >
        <el-icon size="20" color="white"><Close /></el-icon>
      </div>
      <div ref="screenDom2" class="desktop_screen2" v-loading="videoLoading">
        <video
          id="videoPlayer2"
          style="height: 100%; outline: none"
          autoplay
          controlsList="nodownload nofullscreen noplaybackrate nopictureinpicture"
          disablePictureInPicture
          disableRemotePlayback
          webkit-playsinline
          tabindex="0"
          v-if="isOff"
        ></video>
        <div v-else style="color: #fff; font-size: 2vw">
          {{ !isStandby ? "设备已离线" : "网络不稳定，请刷新后重连" }}
        </div>
      </div>
      <div
        id="taskManageBtn"
        class="taskManage-btn btn-1"
        :style="{ transform: `translate(${btn1.x}px, ${btn1.y}px)` }"
        @mousedown="startDrag($event, 'btn1')"
        @touchstart="startDrag($event, 'btn1')"
        v-show="true"
      >
        ctrl+alt+del
      </div>
    </div>
  </teleport>
</template>

<script setup>
import useAppStore from "@/store/modules/app";
import { useRoute, useRouter } from "vue-router";
const appStore = useAppStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
import useUserStore from "@/store/modules/user";
import { createWebSocket, closeSock, sendSock } from "@/utils/socket";
import elementResizeDetectorMaker from "element-resize-detector";
import { deviceInfo } from "@/api/mediaTeach/ledger";
import useSettingsStore from "@/store/modules/settings";
import {
  addSchoolDeviceLog,
  deviceStartByWOL,
  deviceCtlMqtt,
  deviceCtl,
  checkDeviceCode,
  sendMessage,
  addByMqtt,
  addSchoolScreenshot,
  checkDeviceOnline,
} from "@/api/deviceControl";
import {
  getTeacherList,
  addSchoolDeviceUse,
  getDevicePermissions,
  delSchoolDeviceUse,
} from "@/api/system/user";
import {
  sendPointRequest,
  sendPointRequestBatch,
  extractNumbers,
  isIPv4,
} from "@/utils";
import { nextTick } from "process";
import keyToVKMap from "@/utils/keyToVKMap";

const isRela = isIPv4(window.location.hostname); // 判断是否内网环境
console.log(isRela, "是否内网环境");
// 拖拽区域引用
const dragArea = ref(null);
// 按钮位置状态
const btn1 = ref({
  x: 0,
  y: 0,
});
// 拖拽状态
const dragState = ref({
  isDragging: false,
  currentBtn: null,
  startX: 0,
  startY: 0,
  offsetX: 0,
  offsetY: 0,
});
const btnContainerEl = ref(null);
const isOff = ref(true); // 是否关机
const isChecking = ref(false); // 是否被巡视中
const isStandby = ref(false); // 是否熄屏状态
const isDk = ref(false); // 是否返回已断开
const isConnecting = ref(false); // 是否正在连接
const settingsStore = useSettingsStore();
const dDialog = ref(false);
const screenDom2 = ref(null);
const screenDom = ref(null);
const refreshTimer = ref(null);
const remoteData = ref([
  {
    label: "远程关机",
    value: 0,
    type: "danger",
    bg: "remote_red",
  },
  {
    label: "远程开机",
    value: 1,
    type: "primary",
    bg: "remote_blur",
  },
  {
    label: "远程重启",
    value: 2,
    type: "success",
    bg: "remote_green",
  },
  {
    label: "定时关机",
    value: 3,
    type: "danger",
    bg: "remote_red",
  },
  {
    label: "开启WIFI",
    value: 4,
    type: "primary",
    bg: "remote_blur",
  },
  {
    label: "关闭WIFI",
    value: 5,
    color: "#AF52DE",
    bg: "remote_purple",
  },
  {
    label: "清理缓存",
    value: 6,
    type: "warning",
    bg: "remote_yellow",
  },
  {
    label: "锁定",
    value: 7,
    color: "#FFCC00",
    bg: "remote_thinYellow",
  },
  {
    label: "解锁",
    value: 8,
    type: "primary",
    bg: "remote_blur",
  },
]);
const timeDialog = ref(false);
const timeData = ref("");
onMounted(() => {
  appStore.toggleSideBarHide(true);
  getDeviceInfo();
  clearInterval(refreshTimer.value);
  refreshTimer.value = setInterval(() => {
    console.log("刷新一次");
    deviceInfoApi();
  }, 180000);
});

watch(
  () => dDialog.value,
  (newVal, oldVal) => {
    console.log(newVal, oldVal, "newVal, oldVal");
    isStandby.value = false;
    if (!dDialog.value) {
      // settingsStore.ifFooter = true;
      closeSock();
      getDeviceInfo();
    } else {
      console.log("监听到了");
      closeSock();
      handleDesktop2(deviceInfoData.value, 2);

      nextTick(() => {
        btnContainerEl.value = document.getElementById("dialog_desk");
        const btn = document.getElementById("taskManageBtn");
        btn1.value = {
          x: btnContainerEl.value.clientWidth - btn.clientWidth - 40,
          y: -(btnContainerEl.value.clientHeight - btn.clientHeight - 10),
        };
        window.addEventListener("resize", cancelDebounce);
      });
    }
    // isStandby.value = false;
  }
);

// 开始拖拽
const startDrag = (e, btnName) => {
  videoElement.value.removeEventListener("mousemove", moveEvent);
  videoElement.value.removeEventListener("mousedown", downEvent);
  videoElement.value.removeEventListener("mouseup", upEvent);
  e.preventDefault();

  // 设置拖拽状态
  dragState.value = {
    isDragging: true,
    currentBtn: btnName,
    startX: e.clientX || e.touches[0].clientX,
    startY: e.clientY || e.touches[0].clientY,
    offsetX: getBtnPosition(btnName).x,
    offsetY: getBtnPosition(btnName).y,
  };

  // 添加事件监听
  document.addEventListener("mousemove", dragMove);
  document.addEventListener("mouseup", endDrag);
  document.addEventListener("touchmove", dragMove, { passive: false });
  document.addEventListener("touchend", endDrag);
};

// 拖拽移动
const dragMove = (e) => {
  if (!dragState.value.isDragging) return;
  e.preventDefault();

  try {
    const clientX = e.clientX || e?.touches[0]?.clientX;
    const clientY = e.clientY || e?.touches[0]?.clientY;

    // 计算移动距离
    const dx = clientX - dragState.value.startX;
    const dy = clientY - dragState.value.startY;

    // 更新按钮位置
    const newX = dragState.value.offsetX + dx;
    const newY = dragState.value.offsetY + dy;

    // 边界检查 - 防止移出拖拽区域
    const areaRect = dragArea.value.getBoundingClientRect();
    const btn = document.getElementById("taskManageBtn");
    const btnRect = { width: btn.offsetWidth, height: btn.offsetHeight };

    // 计算边界
    const minX = 0;
    const maxX = areaRect.width - btnRect.width;
    const minY = -areaRect.height;
    const maxY = -btnRect.height;

    // 应用边界限制
    const clampedX = Math.max(minX, Math.min(newX, maxX));
    const clampedY = Math.max(minY, Math.min(newY, maxY));

    // 更新按钮位置
    if (dragState.value.currentBtn === "btn1") {
      btn1.value = { x: clampedX, y: clampedY };
    }
  } catch (error) {
    console.log(error, "error");
  }
};

// 结束拖拽
const endDrag = async (e) => {
  dragState.value.isDragging = false;
  if (
    dragState.value.startX == e.clientX &&
    dragState.value.startY == e.clientY
  ) {
    //点击事件
    console.log("点击事件");
    let { ip1, ip2 } = getUrlIp(
      deviceInfoData.value.ipAddress,
      deviceInfoData.value.ralayHost
    );
    let obj = {};
    obj = {
      method: "post",
      uri: ip1 + `/api/Cockpit/SendSas`,
      content: "",
    };
    try {
      let res = null;
      if (deviceInfoData.value.isMqtt) {
        obj = {
          ...obj,
          uri: `/api/Cockpit/SendSas`,
          deviceCodeList: deviceInfoData.value.deviceCode,
        };
        console.log("ctrl+alt+del的传参", obj);
        res = await deviceCtlMqtt(obj);
      } else {
        console.log("ctrl+alt+del的传参", obj);
        res = await deviceCtl(obj);
      }
    } catch (error) {
      console.log(error, "error");
    }
  }

  // 移除事件监听
  document.removeEventListener("mousemove", dragMove);
  document.removeEventListener("mouseup", endDrag);
  document.removeEventListener("touchmove", dragMove);
  document.removeEventListener("touchend", endDrag);

  videoElement.value.addEventListener("mousemove", moveEvent);
  videoElement.value.addEventListener("mousedown", downEvent);
  videoElement.value.addEventListener("mouseup", upEvent);
};

// 获取按钮当前位置
const getBtnPosition = (btnName) => {
  if (btnName === "btn1") return btn1.value;
  return { x: 0, y: 0 };
};

const deviceInfoData = ref({});
function getDeviceInfo() {
  proxy.$modal.loading();
  console.log(route.query.deviceId, "route.query.deviceCode");
  deviceInfoApi();
}

function deviceInfoApi(flag = false) {
  if (!route.query.deviceId) return;
  deviceInfo({ id: route.query.deviceId })
    .then(async (response) => {
      deviceInfoData.value = response.data;
      deviceInfoData.value.isMqtt = route.query.isMqtt;
      getPermissions();
      console.log(response.data, "response.data");
      isOff.value = response.data.runStatus[0] == 0 ? false : true;
      isStandby.value = isOff.value && flag;
      console.log("是否熄屏", isStandby.value, "flag", flag);
      if (!isConnecting.value && !flag) {
        try {
          console.log("checkDeviceOnline传参", {
            deviceCode: deviceInfoData.value.deviceCode,
          });
          let obj = isRela
            ? {
                deviceCode: deviceInfoData.value.deviceCode,
              }
            : null;
          let res = await checkDeviceOnline(obj);
          console.log(res, "checkDeviceOnline");
          isChecking.value =
            res.data[isRela ? "deviceSize" : "receiveSize"] > 0 ? true : false;
        } catch (error) {
          console.log(error, "error");
        }
        isStandby.value = false;
        handleDesktop2(deviceInfoData.value);
      }
    })
    .catch((error) => {
      console.log(error, "error");
    })
    .finally(() => {
      proxy.$modal.closeLoading();
    });
}

const back = () => {
  proxy.$tab.closeOpenPage("/deviceControl/screen");
};
const state = reactive({
  jmuxer: null,
  hasInitVideo: false,
});
const global_callback2 = (nalUnit, err = false) => {
  console.log("websocket的回调函数收到服务器信息：", nalUnit);
  videoLoading.value = false;
  isDk.value = false;
  if (err) {
    proxy.$modal.msgError(nalUnit);
  } else if (nalUnit == "已断开") {
    console.log("已断开", dDialog.value);
    clearInterval(refreshTimer.value);
    refreshTimer.value = setInterval(() => {
      console.log("刷新一次");
      deviceInfoApi();
    }, 180000);
    isConnecting.value = false;
    isStandby.value = false;
    isChecking.value = false;
    videoLoading.value = false;
    isDk.value = true;
    deviceInfoApi(true);
  } else {
    videoLoading.value = false;
    // 喂给JMuxer
    if (state.jmuxer) {
      state.jmuxer.feed({
        video: nalUnit,
        // duration: 0,
        // 实时模式设为0
      });
      isConnecting.value = true;
      if (
        videoElement.value.videoWidth &&
        !state.hasInitVideo &&
        dDialog.value
      ) {
        state.hasInitVideo = true;
        nextTick(() => {
          console.log("第二次初始化");
          initVideoPlayer();
        });
      }
    }
  }
};

const videoElement = ref(null);
const videoLoading = ref(false);
const erd = elementResizeDetectorMaker();
function handleDesktop2(row, type = 1) {
  if (isChecking.value || !isOff.value) return;
  // if (!isOff.value) return;
  console.log("设备未离线", isOff.value);
  state.currentDevice = row || {
    isMqtt: 0,
    deviceName: "",
    deviceCode: "",
    screenshot: "",
  };
  let { url1, ws } = getUrlIp(row.ipAddress);
  const isProd = ref(import.meta.env.VITE_APP_ENV != "development");
  console.log(isProd.value, "isProd.value");
  let uri = "";
  if (isProd.value) {
    uri = `${window.location.protocol == "https:" ? "wss" : "ws"}://${
      window.location.host
    }/wsflow/ServerHub?corpId=${useUserStore().corpId}&${
      row.isMqtt
        ? `deviceCode=${row.deviceCode}`
        : `addr=ws://${url1}/ServerHub`
    }`;
  } else {
    uri = `wss://maintainapptest.gzwinteam.com/wsflow/ServerHub?corpId=${
      useUserStore().corpId || "00000123"
    }&${
      row.isMqtt
        ? `deviceCode=${row.deviceCode}`
        : `addr=ws://${url1}/ServerHub`
    }`;
  }

  console.log(row, "handleDesktop2");
  let timer = null;
  timer = setTimeout(() => {
    clearTimeout(timer);
    createWebSocket(global_callback2, uri).then((res) => {
      videoLoading.value = true;
      nextTick(() => {
        //初始化JMuxer
        state.jmuxer = new JMuxer({
          node: type == 1 ? "videoPlayer" : "videoPlayer2",
          mode: "video",
          // flushingTime: 100,
          //封装间隔（毫秒）
          // fps: 20,
          // 需与实际帧率一致
          flushingTime: 0,
          readFpsFromTrack: true,
          debug: false,
        });
        if (type == 1) {
          videoElement.value = document.getElementById("videoPlayer");
        } else {
          videoElement.value = document.getElementById("videoPlayer2");
        }
        // videoElement.value = document.getElementById("videoPlayer");
        console.log("第一次初始化", videoElement);
        window.addEventListener("resize", cancelDebounce);
        if (!dDialog.value) {
          erd.listenTo(proxy.$refs.screenDom, () => {
            cancelDebounce();
          });
        } else {
          erd.listenTo(proxy.$refs.screenDom2, () => {
            cancelDebounce();
          });
        }
      });
    });
  }, 200);
}

const debounce = (func, delay) => {
  let timer;
  return function () {
    if (timer) {
      clearTimeout(timer);
    }
    timer = setTimeout(() => {
      func();
    }, delay);
  };
};
const rect = ref(null);
const resizeHandler = () => {
  rect.value = videoElement.value.getBoundingClientRect(); // 重新获取元素的边界框信息
  // console.log("video盒子大小发生改变", rect.value);
  btnContainerEl.value = document.getElementById("dialog_desk");
  const btn = document.getElementById("taskManageBtn");

  if (btnContainerEl.value && btn) {
    let arr = btn.style.transform.split(",");
    let btnLeft = extractNumbers(arr[0]),
      btnTop = extractNumbers(arr[1]);
    if (btnContainerEl.value.clientWidth < btnLeft + btn.clientWidth) {
      btn1.value = {
        x: btnContainerEl.value.clientWidth - btn.clientWidth - 40,
        y: -btnTop,
      };
    }

    if (btnContainerEl.value.clientHeight < btnTop + btn.clientHeight) {
      btn1.value = {
        x: btnLeft,
        y: -(btnContainerEl.value.clientHeight - btn.clientHeight - 10),
      };
    }
  }
};
const cancelDebounce = debounce(resizeHandler, 50);

const disabledDateFn = (date) => {
  if (date.getTime() + 86400000 < new Date().getTime()) {
    return true;
  }
  return false;
};

function getUrlIp(ip, url = "") {
  let str = "http",
    ws = "ws",
    href = window.location.href.split(":");
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `${href[0]}://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2.replace("s", "").replace("http://", "");
  } else {
    ip2 = `${href[0]}://` + ip2;
  }
  ws = href[0] == "https" ? "ws" : "ws";
  console.log("网站协议", href[0], ws, href[0] == "https");
  return { ip1, ip2, url1, url2, ws };
}

// 远程控制桌面
const operateDesk = () => {
  console.log("远程控制桌面");
  dDialog.value = true;
  // settingsStore.ifFooter = false;
  console.log(settingsStore, "settingsStore");
};

function handleOpeartionRemote(val) {
  console.log("远程操作", val);
  switch (val) {
    // 远程关机
    case 0:
      handleBatchRemote(0);
      break;
    //远程开机
    case 1:
      handleBatchRemote(2);
      break;
    // 远程重启
    case 2:
      handleBatchRemote(1);
      break;
    // 远程定时关机
    case 3:
      timeDialog.value = true;
      break;
    // 远程开启WIFI
    case 4:
      handleBatchWifi(true);
      break;
    // 远程关闭WIFI
    case 5:
      handleBatchWifi(false);
      break;
    // 远程清除缓存
    case 6:
      handleBatchClear(0);
      break;
    // 远程锁屏
    case 7:
      handleBatchLock(0);
      break;
    // 远程解锁
    case 8:
      handleBatchLock(1);
      break;
    default:
      break;
  }
}

// 定时关机
const handleBatchFixed = () => {
  console.log(timeData.value, "timeData");
  if (!timeData.value) {
    proxy.$modal.msgWarning("请选择关机时间");
    return;
  }
  if (new Date(timeData.value).getTime() < new Date().getTime()) {
    proxy.$modal.msgWarning("关机时间不能小于当前时间");
    return;
  }
  handleBatchRemote(3);
};

/** 批量重启/关闭/定时关闭按钮操作  type: 0关机   1重启 2开机 3定时关机  */
const handleBatchRemote = async (type) => {
  if (type == 2) {
    // 开机

    if (isOff.value) {
      proxy.$modal.msgWarning("设备已开机");
      return;
    }

    try {
      proxy.$modal.confirm("请确认设备是否支持远程开机").then(() => {
        proxy.$modal.loading();
        let sendData = {
          userEvents: [
            {
              event: "Click",
              eventDescribe: "点击单个设备操作集控按钮",
              content: 0,
              num: 1,
              deviceCode: deviceInfoData.value.deviceCode,
            },
          ],
        };
        console.log(sendData, "单个埋点");
        sendPointRequestBatch(sendData);
        addSchoolDeviceLog({
          logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
          deviceCode: deviceInfoData.value.deviceCode, // 操作设备编号
          logContent: `远程开机`, // 日志内容
          deviceNum: 1,
        });

        deviceStartByWOL({ deviceCode: deviceInfoData.value.deviceCode });
        clearInterval(refreshTimer.value);
        refreshTimer.value = setInterval(() => {
          console.log("刷新一次");
          deviceInfoApi();
        }, 180000);
        proxy.$modal.closeLoading();
        proxy.$modal.msgSuccess("操作成功");
      });
    } catch (error) {
      proxy.$modal.closeLoading();
    }
  }

  if (type == 1 || type == 0 || type == 3) {
    if (!isOff.value) {
      proxy.$modal.msgWarning("设备已关机");
      return;
    }

    try {
      //  0关机   1重启   3定时关机
      let obj = {};
      let sendData = {
        userEvents: [
          {
            event: "Click",
            eventDescribe: "点击单个设备操作集控按钮",
            content: `${type == 0 ? 1 : type == 1 ? 2 : 3}`,
            num: 1,
            deviceCode: deviceInfoData.value.deviceCode,
          },
        ],
      };
      console.log(sendData, "单个埋点");
      sendPointRequestBatch(sendData);
      addSchoolDeviceLog({
        logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
        deviceCode: deviceInfoData.value.deviceCode, // 操作设备编号
        logContent: `远程${
          type == 0 ? "关机" : type == 1 ? "重启" : "定时关机"
        }`, // 日志内容
        deviceNum: 1, // 操作设备数量
      });
      if (deviceInfoData.value.isMqtt) {
        // 判断是否是mqtt
        obj = {
          method: "post",
          uri: `/api/Cockpit/Control`,
          content: JSON.stringify({ cmd: type == 1 ? 1 : 0 }),
          deviceCodeList: deviceInfoData.value.deviceCode,
          time: type == 3 ? timeData.value : null,
        };
        proxy.$modal.loading();
        await deviceCtlMqtt(obj);
        proxy.$modal.closeLoading();
        type == 3 && (timeDialog.value = false) && (timeData.value = "");
        type == 0 && (isOff.value = false);
        // getDeviceInfo();
      } else {
        let { ip1, ip2 } = getUrlIp(
          deviceInfoData.value.ipAddress,
          deviceInfoData.value.ralayHost
        );
        obj = {
          method: "post",
          uri: ip1 + `/api/Cockpit/Control`,
          content: JSON.stringify({ cmd: 1 }),
          time: type == 3 ? timeData.value : null,
        };
        proxy.$modal.loading();
        await deviceCtl(obj);
        proxy.$modal.closeLoading();
        type == 3 && (timeDialog.value = false) && (timeData.value = "");
        type == 0 && (isOff.value = false);
        // getDeviceInfo();
      }
      proxy.$modal.msgSuccess("操作成功");
    } catch (error) {
      proxy.$modal.closeLoading();
    }
  }
};

/** 批量锁屏/解锁  type: 0锁屏   1解锁 */
const handleBatchLock = async (type) => {
  let { ip1, ip2 } = getUrlIp(
    deviceInfoData.value.ipAddress,
    deviceInfoData.value.ralayHost
  );
  console.log(ip1, deviceInfoData.value, "锁屏、解锁");
  let obj = {};
  obj = {
    method: "get",
    uri: ip1 + `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
    content: "",
  };
  let res = null;
  let sendData = {
    userEvents: [
      {
        event: "Click",
        eventDescribe: "点击单个设备操作集控按钮",
        content: `${type == 0 ? 7 : 8}`,
        num: 1,
        deviceCode: deviceInfoData.value.deviceCode,
      },
    ],
  };
  console.log(sendData, "单个埋点");
  sendPointRequestBatch(sendData);
  if (type == 1) {
    addSchoolDeviceLog({
      logType: 4, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
      deviceCode: deviceInfoData.value.deviceCode, // 操作设备编号
      logContent: `设备后台解锁`, // 日志内容
      deviceNum: 1, // 操作设备数量
    });
  }
  try {
    if (deviceInfoData.value.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/${type == 0 ? "StartLockForm" : "StopLockForm"}`,
        deviceCodeList: deviceInfoData.value.deviceCode,
      };
      proxy.$modal.loading();
      res = await deviceCtlMqtt(obj);
      console.log(res, "resss");
      proxy.$modal.closeLoading();
    } else {
      proxy.$modal.loading();
      res = await deviceCtl(obj);
      proxy.$modal.closeLoading();
    }
    proxy.$modal.msgSuccess("操作成功");
  } catch (error) {
    proxy.$modal.closeLoading();
  }
};

/** 批量清除缓存 */
const handleBatchClear = async () => {
  let { ip1, ip2 } = getUrlIp(
    deviceInfoData.value.ipAddress,
    deviceInfoData.value.ralayHost
  );
  let obj = {};
  obj = {
    method: "get",
    uri: ip1 + `/api/Cockpit/ClearMemory`,
    content: "",
  };
  let sendData = {
    userEvents: [
      {
        event: "Click",
        eventDescribe: "点击单个设备操作集控按钮",
        content: 6,
        num: 1,
        deviceCode: deviceInfoData.value.deviceCode,
      },
    ],
  };
  console.log(sendData, "单个埋点");
  sendPointRequestBatch(sendData);
  addSchoolDeviceLog({
    logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
    deviceCode: deviceInfoData.value.deviceCode, // 操作设备编号
    logContent: `远程清除缓存`, // 日志内容
    deviceNum: 1, // 操作设备数量
  });
  try {
    let res = null;
    if (deviceInfoData.value.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/ClearMemory`,
        deviceCodeList: deviceInfoData.value.deviceCode,
      };
      console.log("传参", obj);
      proxy.$modal.loading();
      res = await deviceCtlMqtt(obj);
      proxy.$modal.closeLoading();
    } else {
      console.log("传参", obj);
      proxy.$modal.loading();
      res = await deviceCtl(obj);
      proxy.$modal.closeLoading();
    }
    proxy.$modal.msgSuccess("操作成功");
  } catch (error) {
    proxy.$modal.closeLoading();
  }
};

/** 批量开启/关闭 Wifi type: 1开启   0关闭 */
const handleBatchWifi = async (type) => {
  //   EnabledNetsh ：开启wifi
  // DisabledNetsh :关闭wifi
  let { ip1, ip2 } = getUrlIp(
    deviceInfoData.value.ipAddress,
    deviceInfoData.value.ralayHost
  );
  let obj = {};
  obj = {
    method: "get",
    uri: ip1 + `/api/Cockpit/${type ? "EnabledNetsh" : "DisabledNetsh"}`,
    content: "",
  };
  let sendData = {
    userEvents: [
      {
        event: "Click",
        eventDescribe: "点击单个设备操作集控按钮",
        content: `${!!type ? 4 : 5}`,
        num: 1,
        deviceCode: deviceInfoData.value.deviceCode,
      },
    ],
  };
  console.log(sendData, "单个埋点");
  sendPointRequestBatch(sendData);
  addSchoolDeviceLog({
    logType: 3, // 日志类型：1-操作日志，2-发布消息记录，3-设备管控记录
    deviceCode: deviceInfoData.value.deviceCode, // 操作设备编号
    logContent: `远程${!!type ? "开启" : "关闭"}WIFI`, // 日志内容
    deviceNum: 1, // 操作设备数量
  });
  let res = null;
  try {
    if (deviceInfoData.value.isMqtt) {
      obj = {
        ...obj,
        uri: `/api/Cockpit/${type ? "EnabledNetsh" : "DisabledNetsh"}`,
        deviceCodeList: deviceInfoData.value.deviceCode,
      };
      proxy.$modal.loading();
      res = await deviceCtlMqtt(obj);
      proxy.$modal.closeLoading();
    } else {
      proxy.$modal.loading();
      res = await deviceCtl(obj);
      proxy.$modal.closeLoading();
    }
    proxy.$modal.msgSuccess("操作成功");
  } catch (error) {
    proxy.$modal.closeLoading();
  }
};

const permissions = ref({});
const permissionsStr = ref("");
const permissionsChar = ref([]);
async function getPermissions() {
  let userList = [];
  try {
    await getTeacherList({ pageNum: 1, pageSize: 9999999 }).then((response) => {
      if (response.code == 200) {
        console.log(response.data, "教职工列表");
        userList = response.data.records;
      }
    });
  } catch (error) {
    console.log(error, "error");
  }
  getDevicePermissions({ deviceCode: deviceInfoData.value.deviceCode }).then(
    (response) => {
      console.log("设备权限用户列表", response);
      if (response.code == 200) {
        let { devicePermissions } = response.data;
        let nameArr = [],
          idArr = [],
          phoneArr = [];
        devicePermissions?.map((item) => {
          nameArr.push(item.nickName);
          idArr.push(item.userId);
          phoneArr.push(item.phone);
        });
        console.log("nickNames", nameArr, "userIds", idArr);
        permissions.value = {
          nickNames: nameArr,
          userIds: idArr,
          phones: phoneArr,
        };
        permissionsStr.value = nameArr?.join("、");
        permissionsChar.value = nameArr?.slice(0, 10).map((item) => item[0]);
      }
    }
  );
}

const queryParams = ref({
  pageNum: 1,
  pageSize: 999999,
  name: "",
});
const tableList = ref([]);
const userLoading = ref(false);
function getList() {
  userLoading.value = true;
  getTeacherList(queryParams.value).then((response) => {
    if (response.code == 200) {
      console.log(response.data, "教职工列表");
      userLoading.value = false;
      if (permissions.value.userIds) {
        response.data.records.forEach((item) => {
          permissions.value.userIds.forEach((item2) => {
            if (item.userId == item2) {
              item.checked = true;
              nextTick(() => {
                proxy.$refs.tableRef.toggleRowSelection(item, true);
              });
            } else {
              item.checked = false;
            }
          });
        });
      }
      const { records, total } = response.data;
      tableList.value = records || [];
      // total.value = total || 0;
    }
  });
}

// 搜索方法
function handleQuery() {
  getList();
}

// 重置方法
function resetQuery() {
  queryParams.value.name = "";
  getList();
}

const screenshotDialogVisible = ref(false);

function handleScreenshot() {
  console.log(deviceInfoData.value.deviceCode);
  console.log({ deviceCode: deviceInfoData.value.deviceCode }, "截图");

  if (!isOff.value) {
    proxy.$modal.msgError("设备已关机");
    return;
  }

  proxy.$modal.loading();
  if (deviceInfoData.value.isMqtt) {
    addByMqtt({ deviceCode: deviceInfoData.value.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
        screenshotDialogVisible.value = true;
        setTimeout(() => {
          screenshotDialogVisible.value = false;
        }, 2000);
      })
      .finally(() => proxy.$modal.closeLoading());
  } else {
    addSchoolScreenshot({ deviceCode: deviceInfoData.value.deviceCode })
      .then((res) => {
        console.log(res);
        proxy.$modal.msgSuccess("截图成功");
        screenshotDialogVisible.value = true;
        setTimeout(() => {
          screenshotDialogVisible.value = false;
        }, 2000);
      })
      .finally(() => proxy.$modal.closeLoading());
  }
}

const userDialogVisible = ref(false);

async function handleOpenAddUser() {
  try {
    await getPermissions();
  } catch (error) {
    console.log(error, "error");
  }
  userDialogVisible.value = true;
  getList();
}

const selectedUser = ref([]);
const handleSelectionChange = (val) => {
  let nickNames = [],
    phones = [],
    userIds = [];
  selectedUser.value = val?.map((item) => {
    nickNames.push(item.nickName);
    phones.push(item.phoneNumber);
    userIds.push(item.userId);
    const { userId, nickName, phoneNumber, idCard } = item;
    return { userId, nickName, phone: phoneNumber, idCard };
  });
  console.log(val, selectedUser.value, permissions.value);
  permissions.value.nickNames = nickNames;
  permissions.value.userIds = userIds;
  permissions.value.phones = phones;
};

const handleSelection = (val, item) => {
  item.checked = !item.checked;
  if (item.checked) {
    permissions.value.nickNames.push(item.nickName);
    permissions.value.userIds.push(item.userId);
    permissions.value.phones.push(item.phone);
  } else {
    permissions.value.nickNames.splice(
      permissions.value.nickNames.indexOf(item.nickName),
      1
    );
    permissions.value.userIds.splice(
      permissions.value.userIds.indexOf(item.userId),
      1
    );
    permissions.value.phones.splice(
      permissions.value.phones.indexOf(item.phone),
      1
    );
  }
};

const handleSelectable = (row) => {
  return !permissions.value.userIds?.some((item) => item == row.userId);
};

function handleAddUser() {
  let flag = true;
  selectedUser.value.forEach((item) => {
    if (!permissions.value.userIds.includes(item.userId)) {
      flag = false;
    }
  });
  console.log(
    selectedUser.value,
    "selectedUser",
    permissions.value.userIds,
    deleteType.value,
    flag
  );
  // if (
  //   (selectedUser.value.length == 0 || flag) &&
  //   deleteType.value
  //   // selectedUser.value.length == permissions.value.userIds.length
  // ) {
  //   proxy.$modal.msgError("请选择新用户");
  //   return;
  // }
  const params = {
    deviceCode: [deviceInfoData.value.deviceCode],
    userInfos: selectedUser.value,
    isRepeated: true,
    isMultipleCodes: false,
  };
  console.log("添加设备使用者传参", params);
  addSchoolDeviceUse(params).then(async (res) => {
    if (res.code == 200) {
      try {
        await getPermissions();
      } catch (error) {
        console.log(error, "error");
      }
      proxy.$modal.msgSuccess("操作成功");
      handleCancel();
    }
  });
}

function handleCancel() {
  userDialogVisible.value = false;
  resetQuery();
}

const deleteType = ref(true);
function handleCloseTag(tag, index) {
  const item = tableList.value.find(
    (item) => item.userId == permissions.value.userIds[index]
  );
  permissions.value.nickNames.splice(index, 1);
  permissions.value.userIds.splice(index, 1);
  permissions.value.phones.splice(index, 1);
  if (item) {
    nextTick(() => {
      item.checked = false;
      proxy.$refs.tableRef.toggleRowSelection(item, false);
      deleteType.value = false;
    });
  }
  console.log("删除传参", item);
  // const phones = permissions.value.phones[index];
  // const obj = {
  //   deviceCode: deviceInfoData.value.deviceCode,
  //   phones: [phones],
  // };
  // delSchoolDeviceUse(obj).then(async (res) => {
  //   if (res.code == 200) {
  //     proxy.$modal.msgSuccess("删除成功");
  //     try {
  //       await getPermissions();
  //     } catch (error) {
  //       console.log(error, "error");
  //     }
  //     getList();
  //   }
  // });
}

function moveEvent(event) {
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  event.stopPropagation(); // 阻止事件冒泡
  event.preventDefault(); // 阻止默认行为
  console.log(event, "event");

  let x = dDialog.value
    ? event.clientX - rect.value.left + screenDom2.value.scrollLeft
    : event.clientX - rect.value.left + screenDom.value.scrollLeft;
  let y = event.clientY - rect.value.top;
  // console.log(
  //   "鼠标移动:",
  //   (videoWidth / rect.value.width) * x,
  //   (videoHeight / rect.value.height) * y,
  //   videoElement.value.videoWidth,
  //   `滚动条偏移量：${screenDom.value.scrollLeft}`
  // );
  sendSock(
    JSON.stringify({
      Button: 0,
      MousePoint: {
        X:
          (((videoWidth / rect.value.width) * x) /
            videoElement.value.videoWidth) *
          65535,
        Y:
          (((videoHeight / rect.value.height) * y) /
            videoElement.value.videoHeight) *
          65535,
      },
      Input: 2,
      Delta: 0,
      Key: 0,
    })
  );
}

function downEvent(event) {
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  videoElement.value.focus(); // 设置焦点到video元素
  const button = event.button;
  let x = dDialog.value
    ? event.clientX - rect.value.left + screenDom2.value.scrollLeft
    : event.clientX - rect.value.left + screenDom.value.scrollLeft;
  let y = event.clientY - rect.value.top;
  console.log(
    "鼠标按下:",
    (videoWidth / rect.value.width) * x,
    (videoHeight / rect.value.height) * y,
    videoElement.value.offsetWidth,
    videoElement.value.videoWidth
  );
  let Input = {
    Button: button,
    MousePoint: {
      X:
        (((videoWidth / rect.value.width) * x) /
          videoElement.value.videoWidth) *
        65535,
      Y:
        (((videoHeight / rect.value.height) * y) /
          videoElement.value.videoHeight) *
        65535,
    },
    Input: 1,
    Delta: 0,
    Key: 0,
  };
  event.stopPropagation(); // 阻止事件冒泡
  event.preventDefault(); // 阻止默认行为
  // 发送鼠标点击信息到服务器
  sendSock(JSON.stringify(Input));
}

function upEvent(event) {
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  videoElement.value.focus(); // 设置焦点到video元素
  const button = event.button;
  let x = dDialog.value
    ? event.clientX - rect.value.left + screenDom2.value.scrollLeft
    : event.clientX - rect.value.left + screenDom.value.scrollLeft;
  let y = event.clientY - rect.value.top;
  console.log(
    "鼠标抬起:",
    (videoWidth / rect.value.width) * x,
    (videoHeight / rect.value.height) * y,
    videoElement.value.offsetWidth,
    videoElement.value.videoWidth
  );
  let Input = {
    Button: button,
    MousePoint: {
      X:
        (((videoWidth / rect.value.width) * x) /
          videoElement.value.videoWidth) *
        65535,
      Y:
        (((videoHeight / rect.value.height) * y) /
          videoElement.value.videoHeight) *
        65535,
    },
    Input: 0,
    Delta: 0,
    Key: 0,
  };
  event.stopPropagation(); // 阻止事件冒泡
  event.preventDefault(); // 阻止默认行为
  // 发送鼠标点击信息到服务器
  sendSock(JSON.stringify(Input));
}

function initVideoPlayer() {
  const videoWidth = videoElement.value.videoWidth;
  const videoHeight = videoElement.value.videoHeight;
  rect.value = videoElement.value.getBoundingClientRect(); // 获取元素的边界框信息
  console.log(1112, "鼠标");

  console.log(videoWidth, videoHeight, "event");
  console.log(
    videoElement.value.clientWidth,
    videoElement.value.clientHeight,
    "event"
  );

  nextTick(() => {
    videoElement.value.addEventListener("mousemove", moveEvent);
    videoElement.value.addEventListener("mousedown", downEvent);
    videoElement.value.addEventListener("mouseup", upEvent);
    videoElement.value.addEventListener("wheel", (event) => {
      console.log("鼠标滑轮:", event.deltaY);
      let wheelInput = {
        Button: 1,
        MousePoint: {
          X: 0,
          Y: 0,
        },
        Input: 3,
        // Delta: -event.deltaY,
        Delta: -event.deltaY > 0 ? 1 : -1, // 滚轮向下为正，向上为负
        Key: 0,
      };
      event.stopPropagation(); // 阻止事件冒泡
      event.preventDefault(); // 阻止默认行为
      // 发送鼠标滑轮信息到服务器
      sendSock(JSON.stringify(wheelInput));
    });
    videoElement.value.addEventListener("keydown", (event) => {
      const vkCode = getVKCode(event.key);
      console.log("键盘按下:", event.key, "VK Code:", vkCode);
      event.stopPropagation(); // 阻止事件冒泡
      event.preventDefault(); // 阻止默认行为

      sendSock(
        JSON.stringify({
          Button: 0,
          MousePoint: {
            X: 0,
            Y: 0,
          },
          Input: 4,
          Delta: 0,
          Key: vkCode,
        })
      );
    });
    videoElement.value.addEventListener("keyup", (event) => {
      const vkCode = getVKCode(event.key);
      console.log("键盘抬起:", event.key, "VK Code:", vkCode);
      event.stopPropagation(); // 阻止事件冒泡
      event.preventDefault(); // 阻止默认行为

      sendSock(
        JSON.stringify({
          Button: 0,
          MousePoint: {
            X: 0,
            Y: 0,
          },
          Input: 5,
          Delta: 0,
          Key: vkCode,
        })
      );
    });
    videoElement.value.addEventListener("contextmenu", (event) => {
      console.log("右键菜单被阻止", event);
      let x = event.clientX - rect.value.left;
      let y = event.clientY - rect.value.top;
      event.stopPropagation(); // 阻止事件冒泡
      event.preventDefault(); // 阻止默认行为
      // 发送右键菜单信息到服务器
      sendSock(
        JSON.stringify({
          type: "contextmenu",
          // x: event.clientX,
          // y: event.clientY,
          x: (videoWidth / rect.value.width) * x,
          y: (videoHeight / rect.value.height) * y,
        })
      );
    });
    window.onload = () => {
      videoElement.value.focus();
    };
    // 禁止暂停播放
    videoElement.value.addEventListener("pause", () => {
      videoElement.value.play(); // 强制继续播放
    });
  });
}

function getVKCode(key) {
  return keyToVKMap[key] || 0; // 如果未找到对应的键码，返回 0
}

onBeforeUnmount(() => {
  isOff.value && !isChecking.value && !isStandby.value ? closeSock() : "";
  window.removeEventListener("resize", cancelDebounce);
});

onUnmounted(() => {
  clearInterval(refreshTimer.value);
});
</script>

<style lang="scss" scoped>
.taskManage-btn {
  position: fixed;
  // border: 1px solid red;
  cursor: pointer;
  border-radius: 0.2vw;
  background-color: #fff;
  width: 80px;
  text-align: center;
  height: 20px;
  line-height: 20px;
  box-shadow: #dddddd 0px 0px 5px 0px;
  font-size: 14px;
  /* 火狐 */
  -moz-user-select: none;
  /* Safari 和 欧朋 */
  -webkit-user-select: none;
  /* IE10+ and Edge */
  -ms-user-select: none;
  /* Standard syntax 标准语法(谷歌) */
  user-select: none;
}
.desktop {
  display: flex;
  flex-direction: column;
  //   height: calc(100vh - 55px);
  background: #e8ecef;
  padding: 30px;

  .desktop_box {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: #f3f3f3;
    padding: 20px;
    border-radius: 10px;

    .back {
      width: 10vw;
      margin-bottom: 10px;
      display: flex;
      align-items: center;
      cursor: pointer;
      img {
        width: 30px;
        height: 30px;
        margin-right: 8px;
      }
    }

    .desktop_content {
      display: flex;
      justify-content: space-between;
      height: 650px;

      .desktop_left {
        width: calc(100% - 25% - 20px);
        height: 100%;
        background: #fff;
        border-radius: 8px;
        padding: 16px;
      }

      .desktop_header {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;
        align-items: center;

        .desktop_header_title {
          display: flex;
          align-items: center;

          img {
            width: 30px;
            height: 30px;
          }
        }

        .desktop_header_screenshot {
          display: flex;
          align-items: center;
          cursor: pointer;
          border: 1px solid #4095e5;
          border-radius: 4px;
          background: #ecf5fd;
          text-align: center;
          padding: 8px 15px;
          color: #4095e5;

          img {
            width: 16px;
            height: 16px;
          }
        }

        img {
          width: 30px;
          height: 30px;
          margin-right: 8px;
        }
      }

      .desktop_screen {
        width: 100%;
        height: 90%;
        display: flex;
        justify-content: center;
        align-items: center;
        // background: #0a9cf5;
      }

      .desktop_right {
        width: 25%;
        // height: 97%;

        .desktop_data {
          margin-bottom: 20px;
          height: 65%;
          border-radius: 8px;
          padding: 16px;
          background: #fff;
          position: relative;

          .desktop_data_title {
            font-size: 16px;
            font-weight: 700;
            color: #000000;
            border-bottom: 1px solid #00000033;
            padding-bottom: 15px;
            display: flex;
            align-items: center;

            img {
              width: 30px;
              height: 30px;
              margin-right: 8px;
            }
          }

          .desktop_data_content {
            margin-top: 20px;

            div {
              display: flex;
              height: 34px;
              line-height: 34px;
              margin-bottom: 20px;

              p {
                margin: 0;
              }

              .desktop_data_content_title {
                font-size: 14px;
                font-weight: 700;
                margin-right: 10px;
                width: 74px;
              }

              .desktop_data_content_value {
                font-size: 14px;
                font-weight: 400;
                border: 1px solid #dcdcdc;
                border-radius: 4px;
                padding: 0 12px;
                background: #f3f3f3;
                width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
              }
            }
          }

          .desktop_data_bottom {
            width: calc(100% - 32px);
            height: 45px;
            text-align: center;
            line-height: 45px;
            border-top: 1px solid #00000033;
            position: absolute;
            bottom: 0;
            font-size: 14px;
            left: 16px;
            color: #4095e5;
            cursor: pointer;
          }
        }

        .desktop_user {
          height: calc(100% - 65% - 20px);
          border-radius: 8px;
          padding: 16px;
          background: #fff;

          .desktop_user_top {
            display: flex;
            align-items: center;
            justify-content: space-between;
            height: 56px;
            border-bottom: 1px solid #00000033;
            padding-bottom: 16px;

            .user_group {
              width: 56px;
              height: 56px;
            }

            div {
              width: 82px;
              height: 32px;
              background: #4095e5;
              border-radius: 4px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: #fff;
              font-size: 14px;
              cursor: pointer;

              img {
                width: 16px;
                height: 16px;
                margin-right: 8px;
              }
            }
          }

          .desktop_user_content {
            .content_title {
              color: #000000e6;
              font-weight: 700;
              font-size: 16px;
              line-height: 24px;
              margin: 8px 0;
            }
            .content_name {
              color: #00000099;
              font-size: 14px;
              line-height: 20px;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }
          }
        }
      }
    }

    .desktop_bottom {
      height: 64px;
      display: flex;
      justify-content: space-between;
      margin-top: 20px;

      .remote_desktop {
        padding: 1vw;
        width: 15vw;
        height: 100%;
        background: #0a9cf5;
        border-radius: 5vw;
        text-align: center;
        color: #fff;
        font-size: 1.5vw;
        font-weight: 700;
        display: flex;
        align-items: center;
        justify-content: center;
        cursor: pointer;

        img {
          width: 2vw;
          height: 2vw;
          margin-right: 0.5vw;
        }
      }

      .remote_operate {
        width: 85vw;
        background: #fff;
        border-radius: 8px;
        padding: 12px 0.8vw;
        display: flex;
        justify-content: space-between;
        margin-left: 1.5vw;

        div {
          height: 40px;
          width: 8vw;
          text-align: center;
          line-height: 40px;
          border-radius: 4px;
          font-size: 1.2vw;
          cursor: pointer;
        }

        .remote_red {
          border: 1px solid #ff3b30;
          background: #ff3b3033;
          color: #ff3b30;
        }

        .remote_blur {
          border: 1px solid #007aff;
          background: #007aff1a;
          color: #007aff;
        }

        .remote_green {
          border: 1px solid #34c759;
          background: #34c7591a;
          color: #34c759;
        }

        .remote_purple {
          border: 1px solid #af52de;
          background: #af52de1a;
          color: #af52de;
        }

        .remote_yellow {
          border: 1px solid #ff9500;
          background: #ff95001a;
          color: #ff9500;
        }

        .remote_thinYellow {
          border: 1px solid #ffcc00;
          background: #ffcc001a;
          color: #ffcc00;
        }
      }
    }
  }
}

.disabled-btn {
  cursor: not-allowed !important;
  background-color: #ebf4fc !important;
  border-color: #d8e9f9 !important;
  color: #9fcaf2 !important;
  img {
    opacity: 0.5;
  }
  &__dark {
    cursor: not-allowed !important;
    color: #fff;
    background-color: #9fcaf2 !important;
    border-color: #9fcaf2 !important;
  }
}

.desktop_user_bottom {
  display: flex;
  position: relative;
  margin-top: 16px;

  > div {
    position: relative;
    margin-right: -10px;
    z-index: 1;
    background-color: #4095e5ff;
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #fff;
    font-size: 16px;
    border: 2px solid #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    @for $i from 1 through 20 {
      &:nth-child(#{$i}) {
        z-index: $i;
      }
    }

    img {
      width: 100%;
      height: 100%;
      border-radius: 50%;
      border: 2px solid #fff;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
  }

  .user_add_bottom {
    z-index: 99;
    background-color: #a1d7f5;
    cursor: pointer;
  }
}

.screenshot-dialog {
  .el-dialog__header {
    display: none;
  }

  .el-dialog__body {
    padding: 0;
  }

  .screenshot-placeholder {
    background-color: #c4c4c5;
    min-height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .screenshot-success-message {
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    padding: 20px;
  }
}

.user_dialog {
  .change_user_list {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .user_tag {
      border: 1px solid #e1f3d8;
      display: flex;
      align-items: center;
      padding: 2px 5px 2px 9px;
      gap: 0 6px;
      border-radius: 3px;
      font-size: 14px;
      color: #67c23a;
      background-color: #f0f9eb;
      .close-icon {
        cursor: pointer;
        font-size: 12px;
        &:hover {
          color: #fff;
          background-color: #67c23a;
          border-radius: 50%;
        }
      }
      // margin: 2px 10px 2px 0;
    }

    .user_tag_list {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      border: 1px solid #dcdcdc;
      padding: 6px 10px;
      border-radius: 4px;
      min-width: 710px;
      min-height: 30px;
    }
  }
  .search_user {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
    .search_input {
      margin-right: 20px;
      width: 200px;
    }
  }
  .user_dialog_footer {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }
}
.dialog_desk {
  background-color: rgba(0, 0, 0, 0.829);
  position: fixed;
  width: 100vw;
  height: 100vh;
  top: 0;
  left: 0;
  z-index: 9999;
}

.close_desk {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  line-height: 1;
  position: absolute;
  right: 10px;
  cursor: pointer;
  z-index: 999;
}

.desktop_screen2 {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  // background: #0a9cf5;
  // background-color: pink;
}
</style>
