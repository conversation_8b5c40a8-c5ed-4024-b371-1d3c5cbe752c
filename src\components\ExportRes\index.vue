<template>
  <el-dialog
    title="导入结果信息"
    v-model="open"
    width="600px"
    @close="emit('setOpen', false)"
    align-center
  >
    <div style="font-size: 16px">
      总条数：{{ data.total }}； 成功条数： {{ data.successCount }}； 失败条数：
      {{ data.errorCount }}。
    </div>
    <el-table
      v-if="data.errorInfoList?.length > 0"
      :data="data.errorInfoList"
      border
      style="margin-top: 20px; max-height: 400px"
    >
      <el-table-column
        label="失败行数"
        align="center"
        minWidth="120px"
        prop="row"
      />
      <el-table-column
        label="失败原因"
        align="center"
        minWidth="120px"
        prop="cause"
      />
    </el-table>

    <div class="warn-reason" v-if="!!data.successMsg">
      <div>提示：</div>
      <div>{{ data.successMsg }}</div>
    </div>
  </el-dialog>
</template>

<script setup>
import { computed } from "vue";

const emit = defineEmits(["setOpen"]);
const open = computed({
  get: () => props.open,
  set: (value) => {},
});
const props = defineProps({
  data: {
    type: Object,
    default: {
      total: 0,
      successCount: 0,
      errorCount: 0,
      errorInfoList: [],
    },
    required: true,
  },
  open: {
    type: Boolean,
    default: false,
  },
});
</script>

<style lang="scss" scoped>
.warn-reason {
  text-align: left;
  color: #e6a23c;
  font-size: 14px;
  margin-top: 15px;
  padding: 10px;
  background-color: #fdf6ec;
  border-radius: 4px;

  div:last-child {
    white-space: pre-line;
  }
}
</style>