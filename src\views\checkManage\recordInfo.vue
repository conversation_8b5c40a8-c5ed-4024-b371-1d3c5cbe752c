<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card" v-loading="loadingPage">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
          <el-button type="warning" plain icon="download" @click="handleExport"
            >导出巡检报告</el-button
          >
        </div>
      </template>

      <el-descriptions title="" border :column="3">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检人员"
        >
          {{ tableData.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检时间"
        >
          <span>{{ tableData.date || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检范围"
        >
          <span>{{ tableData.pointCodeList?.join("、") || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检指标"
        >
          <span>{{ tableData.indicators || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检结果"
        >
          <span class="checklink" @click="handleCheck('查看巡检结果')"
            >点击查看</span
          >
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检状态"
        >
          <div class="table-link">
            <span
              :style="{ color: statusList[tableData.status]?.color || '#fff' }"
              >{{
                `${statusList[tableData.status]?.name || ""}${
                  tableData.status == 2
                    ? tableData.uncheckPoint.length + "处"
                    : ""
                }`
              }}</span
            >
          </div>
        </el-descriptions-item>
        <!-- <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检现场情况"
        >
          <span class="checklink" @click="handleCheck('查看巡检现场情况')"
            >点击查看</span
          >
        </el-descriptions-item> -->
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="巡检频率"
        >
          <span>{{ tableData.rules }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="已检查点位"
        >
          <template v-if="isEdit">
            <el-checkbox-group
              v-model="tempCompletedPointIds"
              @change="handleCompletedPointsChange"
            >
              <div
                v-for="point in tableData.checkPoint"
                :key="point"
                class="point-checkbox"
              >
                <el-checkbox :label="point" :value="point">{{
                  point
                }}</el-checkbox>
              </div>
            </el-checkbox-group>
          </template>
          <template v-else>
            <div>{{ tableData.checkPoint?.join("、") || "-" }}</div>
          </template>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="未检查点位"
        >
          <div>{{ tableData.uncheckPoint?.join("、") || "-" }}</div>
          <div
            v-if="!isEdit && tableData.uncheckPoint.length > 0"
            class="warning-text"
          >
            当前有{{ tableData.uncheckPoint.length }}处点位未扫描!
          </div>
        </el-descriptions-item>
      </el-descriptions>

      <div class="checkImg">
        <div class="checkImg-title">巡检图片</div>
        <div class="checkImg-list">
          <div v-if="imgList.length < 1" style="font-size: 16px">暂无图片</div>
          <div
            v-else
            class="checkImg-list_item"
            v-for="(item, index) in imgList"
            :key="index"
          >
            <el-image
              v-if="item.imageUrl"
              style="width: 100%; height: 7vw"
              :src="item.imageUrl"
              fit="contain"
              :preview-src-list="imgList.map((item) => item.imageUrl)"
              :initial-index="index"
            >
            </el-image>
            <div v-else class="noImg">未上传图片</div>
            <div class="info">
              点位编号：{{ item.pointCode }}<br />
              点位名称：{{ item.pointName }}<br />
              <div
                style="
                  display: flex;
                  align-items: center;
                  justify-content: center;
                "
              >
                巡检结果：<el-tag :type="statusObj[item.result || '-'].type">{{
                  item.result || "-"
                }}</el-tag>
              </div>
              拍摄时间：{{ item.inspectionTime }}<br />
            </div>
          </div>
        </div>
      </div>

      <pagination
        v-show="totalImg > 0"
        :total="totalImg"
        v-model:page="queryParamsImg.pageNum"
        v-model:limit="queryParamsImg.pageSize"
        @pagination="getImgList"
      />

      <div class="button-group">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>

    <el-dialog class="custom-dialog" :title="title" v-model="dialogVisible">
      <el-table :data="resultList" border :max-height="700">
        <el-table-column
          label="点位编号"
          prop="pointCode"
          min-width="120"
          show-overflow-tooltip
        />
        <el-table-column
          label="点位名称"
          prop="pointName"
          min-width="140"
          show-overflow-tooltip
        />
        <el-table-column
          label="巡检结果"
          prop="result"
          min-width="100"
          show-overflow-tooltip
          align="center"
        >
          <template #default="{ row }">
            <el-tag :type="statusObj[row.result].type">{{ row.result }}</el-tag>
          </template>
        </el-table-column>
        <el-table-column
          label="巡检时间"
          prop="inspectionTime"
          min-width="160"
          show-overflow-tooltip
        />
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :background="false"
        :autoScorll="false"
        layout="total,prev,pager,next"
      />

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="RecordInfo">
import {
  ref,
  computed,
  onMounted,
  watch,
  reactive,
  toRefs,
  getCurrentInstance,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import {
  schoolInspectionRecordInfo,
  schoolInspectionResultList,
  schoolInspectionImageList,
} from "@/api/check";
import { downloadBlob } from "@/utils";
import { inspectionRecordExport } from "@/api/inspection"; // 需要添加这个API

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  loading: false,
  tempCompletedPointIds: [],
  loadingPage: false,
  isEdit: false,
  total: 0,
  totalImg: 0,
  dialogVisible: false,
  title: "查看巡检结果",
  resultList: [],
  imgList: [],
  queryParams: {
    pageNum: 1,
    pageSize: 10,
  },
  queryParamsImg: {
    pageNum: 1,
    pageSize: 10,
  },
  tableData: {
    name: "",
    checkPoint: [],
    status: "",
    rules: "",
    date: "",
    patrolPlanId: "",
    indicators: "",
    pointCodeList: [],
    uncheckPoint: [],
  },
  statusObj: {
    无异常: {
      type: "primary",
    },
    发现异常: {
      type: "danger",
    },
    已维修: {
      type: "success",
    },
    "-": {
      type: "",
    },
  },
  statusList: [
    {
      name: "未开始",
      color: "#F56C6C",
    },
    {
      name: "完成巡检",
      color: "#67C23A",
    },
    {
      name: "漏检",
      color: "#F56C6C",
    },
  ],
});

const {
  statusList,
  statusObj,
  imgList,
  tempCompletedPointIds,
  loading,
  loadingPage,
  totalImg,
  queryParamsImg,
  isEdit,
  total,
  dialogVisible,
  title,
  resultList,
  queryParams,
  tableData,
} = toRefs(state);

const handleExport = () => {
  proxy.$modal.loading();
  inspectionRecordExport(route.query.id)
    .then((res) => {
      downloadBlob(
        res,
        "application/vnd.ms-excel",
        `${state.tableData.name}${state.tableData.date}的巡检记录`
      );
    })
    .finally(() => proxy.$modal.closeLoading());
};

const getList = () => {
  schoolInspectionResultList(state.queryParams).then((response) => {
    if (response.code == 200) {
      console.log(response);
      const { records, total } = response.data;
      state.resultList =
        records?.reduce((res, cur) => {
          res.push({
            ...cur,
            result: cur.result || "-",
            inspectionTime: cur.inspectionTime || "-",
          });
          return res;
        }, []) || [];
      state.total = total || 0;
    }
  });
};

const handleCheck = (val) => {
  state.title = val;
  state.queryParams.pageNum = 1;
  state.queryParams.pageSize = 10;
  state.queryParams.patrolPlanId = state.tableData.patrolPlanId;
  getList();
  state.dialogVisible = true;
};

const handleCancel = () => {
  state.dialogVisible = false;
};
const getImgList = () => {
  schoolInspectionImageList(state.queryParamsImg).then((response) => {
    if (response.code == 200) {
      console.log(response);
      const { records, total } = response.data;
      state.imgList = records || [];
      state.totalImg = total || 0;
    }
  });
};

const getData = () => {
  state.loadingPage = true;
  schoolInspectionRecordInfo(route.query.id)
    .then((response) => {
      if (response.code == 200) {
        console.log(response);
        state.tableData = response.data;
        state.queryParamsImg.patrolPlanId = state.tableData.patrolPlanId;
        getImgList();
      }
    })
    .finally(() => (state.loadingPage = false));
};

// 处理已完成点位变化
const handleCompletedPointsChange = (value) => {
  // 仅更新临时数据
  tempCompletedPointIds.value = value;
};

const handleBack = () => {
  proxy.$tab.closeOpenPage("record");
};

onMounted(() => {
  getData();
});
</script>

<style lang="scss" scoped>
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }
  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
  .checklink {
    color: #4095e5;
    text-decoration: underline;
    cursor: pointer;
  }
}

.checkImg {
  font-size: 14px;
  .info {
    margin-top: 10px;
  }
  &-title {
    font-size: 18px;
    font-weight: bold;
    margin: 20px 0 10px;
  }
  &-list {
    display: flex;
    flex-wrap: wrap;
    gap: 40px;
    &_item {
      width: 17%;
      // border: 1px solid red;
      text-align: center;
      line-height: 20px;
    }
  }
}

.noImg{
  text-align: center;
  line-height: 7vw;
  width: 100%;
  height: 7vw;
  color: #333;
  background-color: #efefef;
}

.warning-text {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 8px;
}

.button-group {
  margin-top: 40px;
  text-align: center;
}

.point-checkbox {
  margin-bottom: 8px;
}

:deep(.el-textarea__inner) {
  min-height: 80px !important;
}
</style>