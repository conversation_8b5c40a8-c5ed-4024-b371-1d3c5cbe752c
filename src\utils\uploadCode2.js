import html2canvas from "html2canvas";
// import QRCode from "qrcodejs2-fix";
import QRCode from "qrcode";
import J<PERSON>Z<PERSON> from "jszip";
import { saveAs } from "file-saver";
import modal from "@/plugins/modal.js";

export function generateQRCode(dataArray, isZip = true, fileName) {
  if (isZip) {
    modal.loading();
    const zip = new JSZip();
    const promises = dataArray.map((data, index) => {
      const { deviceCode, deviceName, installAddress } = data;

      // 构建提示文字
      const prompt = `设备名称：${deviceName}\n设备编码：${deviceCode}\n安装位置：${
        installAddress || "-"
      }`;
      const maxWidth = 300; // 设置最大宽度
      const lineHeight = 15; // 行高
      // const promptLines = wrapText(ctx, prompt, maxWidth);

      return QRCode.toDataURL(deviceCode).then((url) => {
        // 创建 canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const img = new Image();

        return new Promise((resolve) => {
          img.onload = () => {
            // 设置画布大小
            canvas.width = img.width + 200;
            canvas.height = img.height + 30 + lineHeight * 3; // 留出空间放置提示文字
            // ctx.drawImage(img, 0, 0);

            // 添加提示文字背景
            const promptHeight = 40; // 背景高度
            ctx.fillStyle = "white"; // 背景颜色
            // ctx.fillRect(0, img.height, img.width, promptHeight); // 绘制背景矩形
            ctx.fillRect(0, 0, canvas.width, canvas.height); // 绘制背景矩形

            // 计算二维码和提示文字的居中位置
            const qrX = (canvas.width - img.width) / 2; // 水平居中
            const qrY = 10; // 垂直位置

            // 绘制二维码
            ctx.drawImage(img, qrX, qrY);

            // 添加提示文字
            ctx.fillStyle = "black";
            ctx.font = "12px Arial";
            // ctx.textAlign = "center";

            // 将 prompt 拆分为多行
            // const promptLines = prompt.split("\n");
            const promptLines = wrapText(ctx, prompt, maxWidth);
            promptLines.forEach((line, index) => {
              const textY = qrY + img.height + 20 + lineHeight * index;

              // 计算每行的宽度并使其水平居中
              const textWidth = ctx.measureText(line).width;
              const xPosition = (canvas.width - textWidth) / 2; // 计算居中位置

              // 检查宽度并添加省略号
              if (textWidth > maxWidth) {
                let truncatedLine = line;
                while (
                  ctx.measureText(truncatedLine + "...").width > maxWidth
                ) {
                  truncatedLine = truncatedLine.slice(0, -1); // 去掉最后一个字符
                }

                // 计算每行的宽度并使其水平居中
                const textWidth1 = ctx.measureText(truncatedLine).width;
                const xPosition1 = (canvas.width - textWidth1) / 2; // 计算居中位置

                ctx.fillText(truncatedLine + "...", xPosition1, textY); // 绘制带省略号的文本
              } else {
                // 计算每行的宽度并使其水平居中

                ctx.fillText(line, xPosition, textY); // 绘制正常文本
              }

              // ctx.fillText(line, xPosition, textY); // 确保文字水平居中
            });

            // 转为 Blob
            canvas.toBlob((blob) => {
              zip.file(`${deviceName}-${deviceCode}-设备二维码.png`, blob);
              resolve();
            }, "image/png");
          };
          img.src = url;
        });
      });
    });

    // 等待所有二维码生成完毕
    Promise.all(promises).then(() => {
      zip
        .generateAsync({ type: "blob" })
        .then((content) => {
          // 使用 FileSaver 保存 ZIP 文件
          saveAs(content, `设备二维码.zip`);
        })
        .finally(() => {
          modal.closeLoading();
        });
    });
  } else {
    console.log(dataArray[0]);
    const { deviceCode, deviceName, installAddress } = dataArray[0];

    // 构建提示文字
    const prompt = `设备名称：${deviceName}\n设备编码：${deviceCode}\n安装位置：${
      installAddress || "-"
    }`;
    const maxWidth = 300; // 设置最大宽度
    const lineHeight = 15; // 行高

    QRCode.toDataURL(deviceCode)
      .then((url) => {
        // 创建 canvas
        const canvas = document.createElement("canvas");
        const ctx = canvas.getContext("2d");
        const img = new Image();

        return new Promise((resolve) => {
          img.onload = () => {
            // 设置画布大小
            canvas.width = img.width + 200;
            canvas.height = img.height + 30 + lineHeight * 3; // 留出空间放置提示文字
            // ctx.drawImage(img, 0, 0);

            // 添加提示文字背景
            const promptHeight = 40; // 背景高度
            ctx.fillStyle = "white"; // 背景颜色
            // ctx.fillRect(0, img.height, img.width, promptHeight); // 绘制背景矩形
            ctx.fillRect(0, 0, canvas.width, canvas.height); // 绘制背景矩形

            // 计算二维码和提示文字的居中位置
            const qrX = (canvas.width - img.width) / 2; // 水平居中
            const qrY = 10; // 垂直位置

            // 绘制二维码
            ctx.drawImage(img, qrX, qrY);

            // 添加提示文字
            ctx.fillStyle = "black";
            ctx.font = "12px Arial";
            // ctx.textAlign = "center";

            // 将 prompt 拆分为多行
            // const promptLines = prompt.split("\n");
            const promptLines = wrapText(ctx, prompt, maxWidth);
            promptLines.forEach((line, index) => {
              const textY = qrY + img.height + 20 + lineHeight * index;

              // 计算每行的宽度并使其水平居中
              const textWidth = ctx.measureText(line).width;
              const xPosition = (canvas.width - textWidth) / 2; // 计算居中位置

              // 检查宽度并添加省略号
              if (textWidth > maxWidth) {
                let truncatedLine = line;
                while (
                  ctx.measureText(truncatedLine + "...").width > maxWidth
                ) {
                  truncatedLine = truncatedLine.slice(0, -1); // 去掉最后一个字符
                }

                // 计算每行的宽度并使其水平居中
                const textWidth1 = ctx.measureText(truncatedLine).width;
                const xPosition1 = (canvas.width - textWidth1) / 2; // 计算居中位置

                ctx.fillText(truncatedLine + "...", xPosition1, textY); // 绘制带省略号的文本
              } else {
                // 计算每行的宽度并使其水平居中

                ctx.fillText(line, xPosition, textY); // 绘制正常文本
              }

              // ctx.fillText(line, xPosition, textY); // 确保文字水平居中
            });

            resolve(canvas.toDataURL("image/png", 1.0));
          };
          img.src = url;
        });
      })
      .then((res) => {
        console.log(res);
        var blob = getBlob(res);
        var link = document.createElement("a");
        var href = window.URL.createObjectURL(blob);
        link.href = href;
        link.download = fileName || `日常考勤-二维码.png`; //a标签的下载属性
        document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
        link.click();
        // 延时保证下载成功执行，否则可能下载未找到文件的问题
        setTimeout(function () {
          window.URL.revokeObjectURL(href); // 释放Url对象
          document.body.removeChild(link);
        }, 100);
      });
  }
}

export function wrapText(ctx, text, maxWidth) {
  const lines = text.split("\n").flatMap((line) => {
    const words = line.split(" ");
    const wrappedLines = [];
    let currentLine = "";

    words.forEach((word) => {
      const testLine = currentLine + word + " ";
      const metrics = ctx.measureText(testLine);
      const testWidth = metrics.width;

      if (testWidth > maxWidth && currentLine) {
        wrappedLines.push(currentLine);
        currentLine = word + " ";
      } else {
        currentLine = testLine;
      }
    });

    if (currentLine) {
      wrappedLines.push(currentLine);
    }

    return wrappedLines;
  });

  return lines;
}

export function generateQRCode2(Eltext, filename, isDownload = true) {
  // 获取输入的文本
  // var text = document.getElementById("text").value;
  var text = Eltext;
  // 清空之前的二维码
  if (!isDownload) document.getElementById("qrcode").innerHTML = "";
  // Elqrcode.innerHTML = "";
  // 生成新的二维码
  let url = new QRCode(document.getElementById("qrcode"), text);
  if (isDownload) {
    let base64Text = url._el.querySelector("canvas").toDataURL("image/png");
    var blob = getBlob(base64Text);
    var link = document.createElement("a");
    var href = window.URL.createObjectURL(blob);
    link.href = href;
    link.download = `${filename}-二维码.png`; //a标签的下载属性
    document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
    link.click();
    // 延时保证下载成功执行，否则可能下载未找到文件的问题
    setTimeout(function () {
      window.URL.revokeObjectURL(href); // 释放Url对象
      document.body.removeChild(link);
    }, 100);
  } else {
    let base64 = url._el.querySelector("canvas").toDataURL("image/png");
    console.log("生成的二维码url", base64);
    return base64;
  }
}

function getBlob(base64) {
  var mimeString = base64.split(",")[0].split(":")[1].split(";")[0]; // mime类型
  var byteString = atob(base64.split(",")[1]); //base64 解码
  var arrayBuffer = new ArrayBuffer(byteString.length); //创建缓冲数组
  var intArray = new Uint8Array(arrayBuffer); //创建视图
  for (var i = 0; i < byteString.length; i += 1) {
    intArray[i] = byteString.charCodeAt(i);
  }
  return new Blob([intArray], {
    type: mimeString,
  });
}

// 截取某个dom为图片并下载
export async function autoPicture(el, options, isDownload = true, fileName = "点位二维码.png") {
  let {
    scale = 1,
    allowTaint = false,
    useCORS = true,
    width = "375",
    height = "649",
    backgroundColor = null,
  } = options;
  const id = document.getElementById(el);
  const canvas = await html2canvas(id, {
    scale, //缩放比例,默认为1
    allowTaint, //是否允许跨域图像污染画布
    useCORS, //是否尝试使用CORS从服务器加载图像
    width, //画布的宽度
    height, //画布的高度
    backgroundColor, //画布的背景色，默认为透明
  });
  if (isDownload) {
    let base64Text = canvas.toDataURL("image/png");
    var blob = getBlob(base64Text);
    var link = document.createElement("a");
    var href = window.URL.createObjectURL(blob);
    link.href = href;
    link.download = fileName; //a标签的下载属性
    // document.body.appendChild(link); // firefox需要把a添加到dom才能正确执行click
    link.click();
    // 延时保证下载成功执行，否则可能下载未找到文件的问题
    setTimeout(function () {
      window.URL.revokeObjectURL(href); // 释放Url对象
      document.body.removeChild(link);
    }, 100);
  } else {
    return canvas.toDataURL("image/png");
  }
}
