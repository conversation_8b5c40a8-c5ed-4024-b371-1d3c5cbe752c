{"name": "ruoyi", "version": "3.6.3", "description": "后台管理系统", "author": "ruoyi", "license": "MIT", "scripts": {"dev": "vite --port 3000", "build:prod": "vite build", "build:dev": "vite build --mode development", "build:stage": "vite build --mode staging", "preview": "vite preview", "build:rela": "vite build --mode relative", "build:demo": "vite build --mode demo", "build:pre": "vite build --mode pre"}, "repository": {"type": "git", "url": "https://gitee.com/y_project/RuoYi-Cloud.git"}, "dependencies": {"@element-plus/icons-vue": "2.3.1", "@hufe921/canvas-editor": "^0.9.86", "@kjgl77/datav-vue3": "^1.7.1", "@rollup/plugin-typescript": "^10.0.1", "@types/node": "16.18.96", "@types/prismjs": "^1.26.0", "@typescript-eslint/eslint-plugin": "5.62.0", "@typescript-eslint/parser": "5.62.0", "@vitejs/plugin-legacy": "^2.0.0", "@vitejs/plugin-vue": "^2.0.4", "@vueup/vue-quill": "1.1.0", "@vueuse/core": "9.5.0", "ali-oss": "^6.22.0", "axios": "0.27.2", "canvas-editor": "^1.0.0", "core-js": "^3.39.0", "cypress": "13.6.0", "cypress-file-upload": "^5.0.8", "echarts": "5.6.0", "echarts-gl": "^2.0.9", "element-plus": "2.9.0", "element-resize-detector": "^1.2.4", "eslint": "7.32.0", "fetch": "^1.1.0", "file-saver": "^2.0.5", "fuse.js": "6.6.2", "html2canvas": "^1.4.1", "jmuxer": "^2.0.7", "js-cookie": "3.0.1", "jsencrypt": "3.3.1", "jszip": "^3.10.1", "nprogress": "0.2.0", "pinia": "2.0.22", "pinia-plugin-persistedstate": "^3.2.0", "qrcode": "^1.5.4", "qrcodejs2": "^0.0.2", "qrcodejs2-fix": "^0.0.1", "qs": "^6.11.2", "regenerator-runtime": "^0.14.1", "simple-git-hooks": "^2.8.1", "sm-crypto": "^0.3.12", "terser": "^5.36.0", "ts-loader": "^9.5.1", "typescript": "4.8.4", "vite-plugin-css-injected-by-js": "^2.1.1", "vite-plugin-vue-setup-extend": "^0.4.0", "vue": "3.2.45", "vue-cropper": "1.0.3", "vue-router": "4.1.4"}, "devDependencies": {"@babel/core": "^7.26.0", "@babel/preset-env": "^7.26.0", "@vitejs/plugin-vue": "3.1.0", "@vue/compiler-sfc": "3.2.45", "babel-loader": "^9.2.1", "prismjs": "^1.27.0", "sass": "1.56.1", "unplugin-auto-import": "0.11.4", "unplugin-vue-setup-extend-plus": "0.4.9", "vite": "3.2.3", "vite-plugin-compression": "0.5.1", "vite-plugin-svg-icons": "2.0.1"}}