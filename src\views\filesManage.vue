<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          文件管理
        </div>
      </template>

      <div>
        <h2 class="h2">客户端版本更新</h2>
        <p class="count_p">
          总安装次数：{{ installTotal }}次<span>(数据因安装问题可能存在误差)</span>
        </p>
        <el-button plain icon="Upload" type="primary" @click="uploadFile('更新版本补丁')"
          >更新版本补丁</el-button
        >
        <div class="select_main">
          <span class="select_left">请选择系统</span>
          <div class="select_right">
            <span :class="tabIndex == 0 ? 'acColor_1' : 'acColor'" @click="tabIndexFn(0)"
              >Windows版本</span
            >
            <span :class="tabIndex == 1 ? 'acColor_1' : 'acColor'" @click="tabIndexFn(1)"
              >Linux版本</span
            >
          </div>
        </div>
        <el-table
          :data="tableUpdate"
          v-loading="loading1"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column prop="sysInfo" label="系统" minwidth="120" align="center" show-overflow-tooltip />
          <el-table-column
            prop="mainVersion"
            label="主版本号"
            minWidth="120"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="version" label="最新补丁号" minWidth="120" align="center" show-overflow-tooltip />
          <el-table-column prop="fileName" label="文件名" minWidth="150" align="center" show-overflow-tooltip />
          <el-table-column prop="downloadUrl" label="下载地址" minWidth="200" align="center" show-overflow-tooltip />
          <el-table-column prop="uploadTime" label="上传时间" minWidth="160" align="center" show-overflow-tooltip />
          <el-table-column
            prop="sumInstallationCount"
            label="已安装次数"
            minWidth="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column prop="status" label="发布状态" width="auto" align="center">
            <template #default="{ row, $index }">
              {{ row.status == 0 ? "未发布" : "已发布" }}
            </template>
          </el-table-column>
          <el-table-column label="操作" min-width="100" fixed="right" align="center">
            <template #default="{ row, $index }">
              <el-button
                text
                type="primary"
                size="small"
                @click="releaseFn(row.mainVersion, 0)"
                v-if="row.status == 0"
                >发布</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>

      <div class="item">
        <h2 class="h2">发布文件</h2>
        <el-button plain icon="Plus" type="primary" @click="uploadFile('上传文件')">上传文件</el-button>
        <el-table
          :data="tableUpload"
          v-loading="loading2"
          style="width: 100%; margin-top: 20px"
        >
          <el-table-column prop="fileName" label="文件名" min-width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="showName" label="文件说明" min-width="200" align="center" show-overflow-tooltip />
          <el-table-column prop="downloadUrl" label="下载地址" min-width="300" align="center" show-overflow-tooltip />
          <el-table-column prop="createTime" label="上传时间" min-width="160" align="center" show-overflow-tooltip />
          <el-table-column prop="status" label="发布状态" min-width="100" align="center">
            <template #default="{ row, $index }">
              {{ row.status == 0 ? "未发布" : "已发布" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="pushCount"
            label="已推送台数"
            min-width="100"
            align="center"
            show-overflow-tooltip
          />
          <el-table-column label="操作" fixed="right" min-width="100" align="center">
            <template #default="{ row, $index }">
              <el-button text type="primary" size="small" @click="releaseFn(row.id, 1)" v-if="row.status == 0"
                >发布</el-button
              >
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-card>
    
    <!-- 弹窗 -->
    <el-dialog class="custom-dialog" v-model="dialogVisible" :title="title" width="500" @close="handlerClose">
      <el-form :model="form" label-width="auto" ref="formRef" :rules="rules">
        <el-form-item
          label="文件说明："
          label-width="auto"
          prop="filename"
          v-if="title == '上传文件'"
        >
          <el-input
            v-model="form.filename"
            placeholder="请输入文件说明"
            autocomplete="off"
          />
        </el-form-item>
        <el-form-item label="版本号：" v-else prop="version">
          <el-select
            v-model="form.version"
            placeholder="请选择版本号"
            style="width: 240px"
          >
            <el-option
              v-for="item in vOptions"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <!-- <el-input
            v-model="form.version"
            placeholder="请输入版本号"
            autocomplete="off"
          /> -->
        </el-form-item>
        <el-form-item label="文件：" prop="uploadName">
          <el-upload
            ref="uploadRef"
            class="upload-demo"
            :headers="headers"
            action="#"
            :limit="1"
            :on-exceed="handleExceed"
            :auto-upload="false"
            :before-upload="beforeUpload"
            :on-change="changeFile"
            :show-file-list="false"
            :http-request="httpRequestFn"
          >
            <template #trigger>
              <el-button type="primary">选择文件</el-button>
            </template>
          </el-upload>
          {{ form.uploadName }}
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitUpload"> 确定 </el-button>
          <el-button @click="dialogVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute } from "vue-router";
import { ref } from "vue";
import { sm2Decrypt, sm2Encrypt } from "@/utils/sm2encrypt.js";
import { ElMessage, genFileId, ElMessageBox, ElLoading } from "element-plus";
import { getToken } from "@/utils/auth";
import axios from "axios";
import {
  getSchoolClientList,
  getMainVersionList,
  releasePatchFile,
  getSchoolFileReleaseList,
  getSchoolFileReleaseRelease,
  getAllInsCount,
} from "@/api/files";
import { debounce } from "@/utils/debounce";

// const actionUrl = ref(BASEURL + "/system/schoolClient/uploadPatchFile/win/3.0");
const BASEURL = import.meta.env.VITE_APP_BASE_API;
const actionUrl = ref("");
const headers = ref({
  Authorization: "Bearer " + getToken(),
  "Content-Type": "multipart/form-data",
});
const dialogVisible = ref(false);
const form = ref({}); // 文件弹窗
const title = ref("更新版本补丁");
const uploadRef = ref(null);
const formRef = ref(null);
const fileList = ref([]);
const tabIndex = ref(0);
const rules = {
  filename: [
    { required: true, message: "请输入文件名", trigger: "blur" },
    {
      pattern: /^[\u4e00-\u9fa5a-zA-Z]+$/,
      message: "请输入中英文字符串",
      trigger: "blur",
    },
    { min: 1, max: 20, message: "长度不超过20位", trigger: "blur" },
  ],
  version: [
    { required: true, message: "请选择版本号", trigger: "change" },
    // {
    //   pattern: /^[a-zA-Z0-9]{1,20}$/,
    //   message: "只能输入英文和数字，且长度不超过20",
    //   trigger: "blur",
    // },
  ],
  uploadName: [{ required: true, message: "请选择上传文件", trigger: "change" }],
};
const fileName = ref("");

const route = useRoute();
const tableUpdate = ref([]); // 客户端版本更新表格
const current1 = ref(0);
const total1 = ref(0);

const tableUpload = ref([]); // 发布文件表格
const current2 = ref(0);
const total2 = ref(0);
const installTotal = ref(0);

const sysVal = ref("win");
const loading1 = ref(false);
const loading2 = ref(false);

const vOptions = ref([]);

// 获取版本列表
function getSchoolListData() {
  loading1.value = true;
  getSchoolClientList({ sys: sysVal.value }).then((res) => {
    loading1.value = false;
    tableUpdate.value = res.data;

    getMainVersionList({ sys: sysVal.value }, sysVal.value).then((res) => {
      vOptions.value = res.data.map((item) => {
        return {
          value: item,
          label: item,
        };
      });
      console.log(res);
    });
    console.log(res);
  });
}
getSchoolListData();

// 获取文件列表
function getFileListData() {
  loading2.value = true;
  getSchoolFileReleaseList().then((res) => {
    loading2.value = false;
    tableUpload.value = res.data;
    console.log(res,'查询的文件列表');
  });
}
getFileListData();

// 获取安装次数
getAllInsCount().then((res) => {
  console.log(res,'安装总数');
  installTotal.value = res.data;
});

// 发布
const releaseFn = (mainVersion, type) => {
  ElMessageBox.confirm("是否确认发布?", "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    type: "warning",
  })
    .then(() => {
      console.log(type);

      if (type == 0) {
        const loadingInstance1 = ElLoading.service({ fullscreen: true });
        releasePatchFile({ sys: sysVal.value }, sysVal.value, mainVersion)
          .then((res) => {
            loadingInstance1.close();
            console.log(res);
            ElMessage({
              type: "success",
              message: "发布成功",
            });
            getSchoolListData();
          })
          .catch(() => {
            loadingInstance1.close();
          });
      } else {
        const loadingInstance2 = ElLoading.service({ fullscreen: true });

        getSchoolFileReleaseRelease(mainVersion).then((res) => {
          console.log(res);
          loadingInstance2.close();
          ElMessage({
            type: "success",
            message: "发布成功",
          });
          getFileListData();
        });
      }
    })
    .catch(() => {
      loadingInstance2.close();
    });
};

// 选择版本
const tabIndexFn = debounce((index) => {
  let type = ["win", "linux"];
  tabIndex.value = index;
  sysVal.value = type[index];
  getSchoolListData();
}, 250);

// 点击上传
const uploadFile = (name) => {
  if (name != "上传文件") {
    if (tabIndex.value == 0) {
      title.value = "上传Windows版本补丁";
    } else {
      title.value = "上传Linux版本补丁";
    }
  } else {
    title.value = name;
  }
  dialogVisible.value = true;
};

// 确定上传
const submitUpload = () => {
  let type = ["win", "linux"];

  formRef.value.validate((valid) => {
    console.log(valid);
    if (valid) {
      if (title.value != "上传文件") {
        actionUrl.value =
          BASEURL +
          `/system/schoolClient/uploadPatchFile/${type[tabIndex.value]}/${
            form.value.version
          }`;

        console.log(tableUpdate.value);
        let result = tableUpdate.value.filter(
          (item) => item.mainVersion == form.value.version
        );
        console.log(result, "mainVersion");

        if (result[0].status == 0) {
          ElMessageBox.alert("在该大版本下有未发布的补丁，请先发布", "提示", {
            confirmButtonText: "关闭",
            callback: (action) => {},
          });
          return;
        }
      } else {
        actionUrl.value =
          BASEURL +
          `/system/schoolFileRelease/uploadFile?showName=${form.value.filename}`;
      }
      console.log(actionUrl.value);

      uploadRef.value.submit();
    }
  });
};

// 覆盖文件
const handleExceed = (files) => {
  uploadRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadRef.value.handleStart(file);
  form.value.uploadName = file.name;
};

// 弹窗消失
const handlerClose = () => {
  formRef.value.resetFields();
};

// 文件发生变化
const changeFile = (file, files) => {
  console.log(file, files, "change");

  // console.log(fileList.value);
  if (file.status == "ready") {
    form.value.uploadName = file.name;
  }
};

// 文件上传之前
const beforeUpload = (file) => {
  console.log(file, "before");
  console.log(fileList.value);

  const fileName = file.name;
  const fileExtension = fileName
    .slice(((fileName.lastIndexOf(".") - 1) >>> 0) + 2)
    .toLowerCase();

  const isZipOrRar =
    title.value != "上传文件" && (fileExtension === "zip" || fileExtension === "rar");
  const isSizeLimitForNewVersion =
    title.value != "上传文件" && file.size / 1024 / 1024 < 250; // 5MB
  const isWithinSizeLimit = title.value === "上传文件" && file.size / 1024 / 1024 < 250; // 5MB

  if (title.value != "上传文件") {
    if (!isZipOrRar) {
      ElMessage.error("上传文件只能是 zip 或 rar 格式!");
      formRef.value.resetFields();

      return false;
    }
    if (!isSizeLimitForNewVersion) {
      ElMessage.error("上传文件大小不能超过 5MB!");
      formRef.value.resetFields();
      return false;
    }
  }

  if (title.value === "上传文件" && !isWithinSizeLimit) {
    ElMessage.error("上传文件大小不能超过 5MB!");
    formRef.value.resetFields();
    return false;
  }

  // 清除之前的文件，确保只保留一个文件
  uploadRef.value.clearFiles();

  return true;
};

// 自定义上传
const httpRequestFn = async (options) => {
  console.log(options, "自定义上传");
  const formData = new FormData();
  formData.append("file", options.file);

  const loadingInstance = ElLoading.service(options);
  return axios
    .post(actionUrl.value, formData, {
      headers: options.headers,
    })
    .then((res) => {
      // 处理上传成功的响应
      loadingInstance.close();
      // console.log(sm2Decrypt(res.data.data), "成功");
      formRef.value.resetFields();
      dialogVisible.value = false;
      ElMessageBox.alert("上传成功", "提示", {
        callback: (action) => {
          getSchoolListData();
          getFileListData();
        },
      });
    })
    .catch((error) => {
      // 处理上传失败的情况
      loadingInstance.close();
      formRef.value.resetFields();
      dialogVisible.value = false;
      ElMessageBox.alert("上传失败", "提示", {
        callback: (action) => {},
      });
    });
};

// 文件上传成功
const fileUploadSuccess = (res, file, files) => {
  console.log(res, file, files, "fanhuisss");
};
</script>

<style lang="scss" scoped>
.item {
  margin-top: 40px;
}
.select_main {
  display: flex;
  gap: 20px;
  align-items: center;
  margin-top: 16px;
  .select_left {
    font-weight: bold;
    color: #5e5e5e;
  }
  .select_right {
    border: 1px solid #1684fc;
    display: flex;
    justify-content: space-around;
    align-items: center;
    width: 300px;
    border-radius: 4px;

    span {
      flex: 1;
      text-align: center;
      padding: 4px 0;
      cursor: pointer;
      font-size: 14px;

      &:first-child {
        border-radius: 4px 0 0 4px;
      }

      &:last-child {
        border-radius: 0 4px 4px 0;
      }
    }
  }
}

.acColor_1 {
  color: white;
  background-color: #1684fc;
}

.acColor {
  color: #1684fc;
  background-color: white;
}

.pagination {
  display: flex;
  justify-content: end;
  margin-top: 10px;
}
.h2 {
  font-size: 20px;
}
.count_p {
  color: #bd3124;
  font-size: 20px;

  span {
    font-size: 14px;
    margin-left: 10px;
  }
}
.upload-demo {
  width: 100%;
}
</style>
