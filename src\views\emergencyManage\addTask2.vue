<template>
  <div class="addEmergency app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>新建应急工单</span>
        </div>
      </template>

      <el-descriptions title="预案信息" border :column="3" v-if="planInfo">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案编号"
        >
          {{ planInfo?.id || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案类型"
        >
          {{ planInfo?.type || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案文件"
        >
          <el-button v-if="planInfo?.fileUrl" type="primary" @click="downloadPlanFile">
            点击查看
          </el-button>
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="预案名称"
        >
          {{ planInfo?.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          :span="2"
          label="预案内容"
        >
          {{ planInfo?.content || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="修改时间"
        >
          {{ planInfo?.updateTime || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="创建时间"
        >
          {{ planInfo?.createTime || "-" }}
        </el-descriptions-item>
      </el-descriptions>

      <el-form :model="errors">
        <el-descriptions title="应急工单信息" border style="margin-top: 20px" :column="2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="应急工单编号"
          >
            <el-form-item label="" prop="number">
              <el-input
                v-model="form.taskNumber"
                style="width: 100%"
                readonly
                :placeholder="form.taskNumber || '系统自动'"
                :disabled="true"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="工单状态"
          >
            <el-form-item label="" prop="status">
              <el-input
                v-model="form.status"
                style="width: 100%"
                readonly
                placeholder="待上传"
                :disabled="true"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="应急工单描述"
          >
            <el-form-item label="" prop="description">
              <el-input
                v-model="form.description"
                style="width: 100%"
                type="textarea"
                :rows="3"
                placeholder="请输入工单描述"
                maxlength="200"
                show-word-limit
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            :span="2"
            label="应急事件等级"
          >
            <el-form-item label="" prop="level">
              <el-select
                v-model="form.level"
                style="width: 30%"
                placeholder="请选择事件等级"
              >
                <el-option
                  v-for="item in levelOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>

      <!-- 备件表格 -->
      <div class="addEmergency-tit">
        关联备件
        <el-button type="primary" plain icon="Plus" @click="handleSelect(0)"
          >点击选择关联备件</el-button
        >
      </div>
      <el-table :data="selectedSparePartsList" border>
        <el-table-column
          label="备件编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="code"
        />
        <el-table-column
          label="备件名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="name"
        />
        <el-table-column label="消耗数量" align="center" min-width="120px">
          <template #default="{ row }">
            <el-input-number
              v-model="row.num"
              :min="getMinQuantity(row.stock)"
              :max="getMaxQuantity(row.stock)"
              @change="(value) => handleNumChange(row, value)"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="消耗日期"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
        >
          <template #default="{ row }">
            {{
              row.consumeTime ||
              new Date()
                .toLocaleString("zh-CN", {
                  year: "numeric",
                  month: "2-digit",
                  day: "2-digit",
                  hour: "2-digit",
                  minute: "2-digit",
                  second: "2-digit",
                })
                .replace(/\//g, "-")
            }}
          </template>
        </el-table-column>
      </el-table>

      <!-- 人员表格 -->
      <div class="addEmergency-tit">
        关联人员
        <el-button type="primary" plain icon="Plus" @click="handleSelect(1)"
          >点击选择关联人员</el-button
        >
      </div>
      <el-table :data="selectedPersonnelList" border>
        <el-table-column
          label="工号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="workId"
        >
          <template #default="scope">
            {{ scope.row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="nickName"
        />
        <el-table-column
          label="关联时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="createTime"
        />
      </el-table>

      <div class="addEmergency-btns">
        <el-button type="primary" @click="handleSubmit" v-throttle>点击提交</el-button>
        <el-button @click="handleBack">返回</el-button>
      </div>

      <!-- <div class="addEmergency-record">
        <div class="title">变更信息</div>
        <div
          class="spare-main_record-item"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role} 于 ${item.time} ${item.type}了${item.spareName}的现库存`
          }}
        </div>
      </div> -->
    </el-card>

    <!-- 选择备件/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      :width="title == '请选择关联备件' ? 600 : 800"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <el-form class="search-list" :inline="true" :model="queryParams" ref="queryRef">
        <el-form-item>
          <el-input
            v-model.trim="queryParams.faultDesc"
            :placeholder="
              title == '请选择关联备件'
                ? '请输入备件编号/名称搜索'
                : '请输入工号/姓名搜索'
            "
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleSearch">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 备件表格 -->
      <el-table
        v-if="title == '请选择关联备件'"
        ref="tableRef"
        :data="ledgerList"
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @select="onTableSelect"
      >
        <el-table-column type="selection" align="center" width="70" />
        <el-table-column
          label="备件编号"
          prop="code"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="备件名称"
          prop="name"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="备件类别"
          prop="typeName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="规格"
          prop="specifications"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column label="库存" prop="stock" show-overflow-tooltip min-width="80" />
      </el-table>

      <!-- 人员表格 -->
      <!-- <el-table
        v-if="title != '请选择关联备件'"
        ref="tableRef"
        :data="ledgerList"
        border
        v-loading="loading"
        @selection-change="handleSelectionChange"
        @select="onTableSelect"
      >
        <el-table-column type="selection" align="center" width="70" />
        <el-table-column label="工号" prop="workId" show-overflow-tooltip min-width="100">
          <template #default="scope">
            {{ scope.row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="nickName"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column label="性别" prop="sex" show-overflow-tooltip min-width="90">
          <template #default="scope">
            {{ scope.row.sex === "0" ? "男" : "女" }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          prop="deptName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="岗位"
          prop="postName"
          show-overflow-tooltip
          min-width="100"
        >
          <template #default="scope">
            {{ scope.row.postName || "-" }}
          </template>
        </el-table-column>
      </el-table> -->

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getSparePartsList"
      />

      <!-- <pagination
        style="margin-top: 0"
        :total="sfczTotal"
        v-model:page="sfczParams.pageNum"
        v-model:limit="sfczParams.pageSize"
        @pagination="getSfczData"
      /> -->
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">返回</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from "vue";
import { ElMessage } from "element-plus";
import { useRouter, useRoute } from "vue-router";
import { schoolPlanInfo, addSchoolPlanWorkOrder } from "@/api/emergency"; // 引入获取预案列表的接口
import { getUserList } from "@/api/system/user";
import { sparePartsPage } from "@/api/spare";
import { getMaintainList } from "@/api/distribution/member";

const router = useRouter();
const route = useRoute();

const uploadRef = ref(null);
const isEdit = ref(false);
const dialogVisible = ref(false);
const errors = ref({}); // 改为对象形式存储每个字段的错误信息

// 表单数据
const form = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  file: null,
  taskNumber: "", // 工单编号
  status: "待上传", // 工单状态
  description: "", // 工单描述
  level: "", // 应急事件等级
});

const title = ref(""); // 弹窗标题
const ledgerList = ref([]); // 备件/人员列表
const recordList = ref([]); // 变更记录列表
const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  current: 1,
  size: 10,
  codeAndName: "", // 添加备件编号/名称搜索字段
  nickNameAndWorkId: "", // 添加人员姓名/工号搜索字段
  faultDesc: "",
  status: "0", // 只查询正常状态的用户
});
const total = ref(0);

// 初始化 planInfo 为空对象
const planInfo = ref({
  id: "",
  name: "",
  type: "",
  content: "",
  fileName: "",
  fileUrl: "",
  createTime: "",
  updateTime: "",
});

const loading = ref(false); // 添加loading变量定义

// 分别定义备件和人员的列表
const sparePartsList = ref([]); // 备件列表
const personnelList = ref([]); // 人员列表
const selectedSparePartsList = ref([]); // 已选择的备件列表，每项包含 id, num 等信息
const selectedPersonnelList = ref([]); // 已选择的人员列表

// 添加表格ref
const tableRef = ref(null);

// 添加多选数组和相关处理函数
const multipleSelection = ref([]);
const tableAllSelectedId = ref([]); // 保存所有选中项的ID
const tableAllSelectedRow = ref([]); // 保存所有选中项的完整数据

// 添加事件等级选项
const levelOptions = [
  {
    label: "非常紧急",
    value: "非常紧急",
  },
  {
    label: "紧急",
    value: "紧急",
  },
  {
    label: "一般",
    value: "一般",
  },
  {
    label: "轻松",
    value: "轻松",
  },
];

const handleSelect = (type) => {
  title.value = type ? "请选择关联人员" : "请选择关联备件";
  dialogVisible.value = true;
  // queryParams.value = {
  //   current: 1,
  //   size: 5,
  //   codeAndName: "",
  //   faultDesc: "",
  //   status: "0",
  // };

  // 初始化选中ID和行
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];

  // 将已选择的项添加到tableAllSelectedId和tableAllSelectedRow
  if (type) {
    selectedPersonnelList.value.forEach((item) => {
      tableAllSelectedId.value.push(item.userId);
      tableAllSelectedRow.value.push(item);
    });
  } else {
    selectedSparePartsList.value.forEach((item) => {
      tableAllSelectedId.value.push(item.id);
      tableAllSelectedRow.value.push(item);
    });
  }

  if (type) {
    return getList()
      .then(() => {
        nextTick(() => {
          // 清除所有选中状态
          if (tableRef.value) {
            tableRef.value.clearSelection();
            // 设置已选人员的选中状态
            ledgerList.value.forEach((row) => {
              if (tableAllSelectedId.value.includes(row.userId)) {
                tableRef.value.toggleRowSelection(row, true);
              }
            });
          }
        });
      })
      .catch((error) => {
        console.error("获取人员列表失败:", error);
        ElMessage.error("获取人员列表失败");
      });
  } else {
    getSparePartsList();
    // return getSparePartsList()
    //   .then(() => {
    //     nextTick(() => {
    //       // 清除所有选中状态
    //       if (tableRef.value) {
    //         tableRef.value.clearSelection();
    //         // 设置已选备件的选中状态
    //         ledgerList.value.forEach((row) => {
    //           if (tableAllSelectedId.value.includes(row.id)) {
    //             tableRef.value.toggleRowSelection(row, true);
    //           }
    //         });
    //       }
    //     });
    //   })
    //   .catch((error) => {
    //     console.error("获取备件列表失败:", error);
    //     ElMessage.error("获取备件列表失败");
    //   });
  }
};

// 获取备件列表数据
const getSparePartsList = () => {
  loading.value = true;
  const params = {
    // ...queryParams.value,
    // pageNum: queryParams.value.current,
    // pageSize: queryParams.value.size,
    codeAndName: queryParams.value.codeAndName,
    pageNum: queryParams.value.pageNum,
    pageSize: queryParams.value.pageSize,
    status: "0",
    faultDesc: queryParams.value.faultDesc,
  };
  console.log("获取备件列表参数:", params);
  sparePartsPage(params)
    .then((res) => {
      console.log(res);

      loading.value = false;
      if (res.code == 200) {
        console.log("获取备件列表成功:", res.data);
        ledgerList.value = res.data.records;
        total.value = res.data.total;

        // 在数据加载完成后，重新设置选中状态
        // return nextTick(() => {
        //   tableRef.value?.clearSelection();
        //   ledgerList.value.forEach((row) => {
        //     if (tableAllSelectedId.value.includes(row.id)) {
        //       tableRef.value?.toggleRowSelection(row, true);
        //     }
        //   });
        // });
      } else {
        throw new Error("获取备件列表失败");
      }
    })
    .catch((err) => {
      loading.value = false;
      console.error("获取备件列表失败:", err);
    });
};

// 获取人员列表数据
const getList = () => {
  loading.value = true;
  console.log("获取人员列表参数:", queryParams.value);
  const params = {
    current: queryParams.value.current,
    size: queryParams.value.size,
    nickNameAndWorkId: queryParams.value.faultDesc,
  };
  console.log("调用API参数:", params);
  return getMaintainList(params)
    .then((res) => {
      loading.value = false;
      if (res.code == 200) {
        console.log("获取人员列表成功:", res.data);
        ledgerList.value = res.data.records;
        total.value = res.data.total;

        // 在数据加载完成后，重新设置选中状态
        return nextTick(() => {
          tableRef.value?.clearSelection();
          ledgerList.value.forEach((row) => {
            if (tableAllSelectedId.value.includes(row.userId)) {
              tableRef.value?.toggleRowSelection(row, true);
            }
          });
        });
      } else {
        // console.error('获取人员列表失败:', res);
      }
    })
    .catch((err) => {
      loading.value = false;
      // console.error('获取人员列表失败:', err);
    });
};

// 文件上传前的验证
const beforeUpload = (file) => {
  const allowedTypes = [
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
    "application/vnd.ms-excel",
    "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
    "text/plain",
  ];
  const allowedExtensions = ["doc", "docx", "xls", "xlsx", "txt"];
  const extension = file.name.split(".").pop().toLowerCase();
  const isValidType =
    allowedTypes.includes(file.type) || allowedExtensions.includes(extension);
  const isLt20M = file.size / 1024 / 1024 < 20;

  errors.value.file = "";

  if (!isValidType) {
    errors.value.file = "只能上传doc、docx、xls、xlsx、txt格式的文件！";
    return false;
  }
  if (!isLt20M) {
    errors.value.file = "文件大小不能超过20MB！";
    return false;
  }
  return true;
};

// 文件改变时的处理
const handleFileChange = (file) => {
  if (file) {
    form.value.file = file.raw;
    errors.value.file = ""; // 清除文件错误提示
  }
};

// 返回
const handleBack = () => {
  router.back();
};

// 搜索
const handleQuery = () => {
  loading.value = true;
  queryParams.value.pageNum = 1;
  if (title.value === "请选择关联备件") {
    // 备件搜索时使用codeAndName
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList();
  } else {
    queryParams.value.nickNameAndWorkId = queryParams.value.faultDesc;
    getList();
  }
};

// 重置
const resetQuery = () => {
  queryParams.value = {
    current: 1,
    size: 5,
    codeAndName: "",
    faultDesc: "",
    status: "0",
  };

  if (title.value === "请选择关联备件") {
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  } else {
    getList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  }
};

// 搜索
const handleSearch = () => {
  queryParams.value.current = 1;
  if (title.value === "请选择关联备件") {
    queryParams.value.codeAndName = queryParams.value.faultDesc;
    getSparePartsList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  } else {
    getList().then(() => {
      nextTick(() => {
        // 重新设置选中状态
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  }
};

const handleSelectionChange = (selection) => {
  // 将获取到的id存入tableAllSelectedId数组
  selection.forEach((item) => {
    const idField = title.value === "请选择关联备件" ? "id" : "userId";
    if (tableAllSelectedId.value.indexOf(item[idField]) === -1) {
      tableAllSelectedId.value.push(item[idField]);
      tableAllSelectedRow.value.push(item);
    }
  });
};

// 添加表格选择/取消选择事件处理
const onTableSelect = (rows, row) => {
  const idField = title.value === "请选择关联备件" ? "id" : "userId";
  // 判断是点击了表格勾选还是取消勾选
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = tableAllSelectedId.value.indexOf(row[idField]);
    if (index !== -1) {
      tableAllSelectedId.value.splice(index, 1); // 取消勾选，则删除id
      tableAllSelectedRow.value.splice(index, 1); // 取消勾选，则删除数据
    }
  }
};

// 修改提交选择函数
const submitForm = () => {
  if (title.value === "请选择关联备件") {
    // 创建一个映射来保存现有备件的数量信息
    const existingPartsMap = {};
    selectedSparePartsList.value.forEach((item) => {
      existingPartsMap[item.id] = {
        num: item.num || 1,
        consumeTime:
          item.consumeTime ||
          new Date()
            .toLocaleString("zh-CN", {
              year: "numeric",
              month: "2-digit",
              day: "2-digit",
              hour: "2-digit",
              minute: "2-digit",
              second: "2-digit",
            })
            .replace(/\//g, "-"),
      };
    });

    // 更新选中的备件列表，使用所有选中的行
    selectedSparePartsList.value = tableAllSelectedRow.value.map((item) => ({
      ...item,
      // 确保库存为0的情况下也能正常设置数量
      stock: Math.max(0, item.stock || 0), // 确保stock至少为0
      num: existingPartsMap[item.id]?.num || 1, // 如果存在原有数量则使用，否则默认为1
      consumeTime:
        existingPartsMap[item.id]?.consumeTime ||
        new Date()
          .toLocaleString("zh-CN", {
            year: "numeric",
            month: "2-digit",
            day: "2-digit",
            hour: "2-digit",
            minute: "2-digit",
            second: "2-digit",
          })
          .replace(/\//g, "-"),
    }));
  } else {
    // 人员选择使用所有选中的行
    selectedPersonnelList.value = tableAllSelectedRow.value;
  }
  dialogVisible.value = false;
};

// 修改取消选择函数
const handleCancel = () => {
  dialogVisible.value = false;
  ledgerList.value = [];
  tableAllSelectedId.value = [];
  tableAllSelectedRow.value = [];
};

// 获取预案详情
const getPlanDetail = async (planId) => {
  try {
    const res = await schoolPlanInfo(planId);
    if (res.code == 200) {
      const currentPlan = res.data;
      planInfo.value = {
        id: currentPlan.code || "",
        name: currentPlan.name || "",
        type: currentPlan.type || "",
        content: currentPlan.content || "",
        fileName: currentPlan.fileName || "",
        fileUrl: currentPlan.fileUrl || "",
        createTime: currentPlan.createTime || "",
        updateTime: currentPlan.updateTime || "",
      };
    } else {
      ElMessage.warning("未找到相关预案信息");
    }
  } catch (error) {
    console.error("获取预案详情失败:", error);
    ElMessage.error("获取预案详情失败");
  }
};

// 修改提交工单函数
const handleSubmit = async () => {
  // 基本表单验证
  if (!form.value.description) {
    ElMessage.warning("请输入工单描述");
    return;
  }

  if (!form.value.level) {
    ElMessage.warning("请选择应急事件等级");
    return;
  }

  if (selectedPersonnelList.value.length === 0) {
    ElMessage.warning("请选择关联人员");
    return;
  }

  // 添加备件验证
  if (selectedSparePartsList.value.length === 0) {
    ElMessage.warning("请选择关联备件");
    return;
  }

  try {
    // 构造备件详情数据
    const sparePartsDetails = selectedSparePartsList.value.map((item) => ({
      sparePartsId: item.id, // 备件ID
      type: 0, // 固定值：出库
      resourceType: 2, // 固定值：应急事件
      resource: "应急事件", // 固定值
      num: item.num, // 消耗数量
    }));

    const params = {
      planId: route.query.planId,
      remark: form.value.description,
      level: form.value.level, // 添加事件等级参数
      userIds: selectedPersonnelList.value.map((person) => person.userId),
      sparePartsDetails, // 添加备件详情
    };

    const res = await addSchoolPlanWorkOrder(params);
    if (res.code === 200) {
      ElMessage.success("工单创建成功");
      router.push("/emergencyManage/TaskCenter");
    } else {
      // ElMessage.error(res.msg || '工单创建失败');
    }
  } catch (error) {
    // console.error('创建工单失败:', error);
    // ElMessage.error('创建工单失败');
  }
};

// 添加备件消耗数量修改方法
const handleNumChange = (row, value) => {
  const index = selectedSparePartsList.value.findIndex((item) => item.id === row.id);
  if (index !== -1) {
    selectedSparePartsList.value[index].num = value;
  }
};

// 添加一个方法来获取备件的最小数量
const getMinQuantity = (stock) => {
  // 允许设置为0，这样即使库存为0也能选择
  return 0;
};

// 添加一个方法来获取备件的最大数量
const getMaxQuantity = (stock) => {
  // 确保stock是数字且不小于0
  return Math.max(0, stock || 0);
};

// 修改下载文件方法
const downloadPlanFile = () => {
  if (!planInfo.value?.fileUrl) {
    ElMessage.warning("预案文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let fileUrl = planInfo.value.fileUrl;
    if (fileUrl.startsWith("http:")) {
      fileUrl = fileUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = fileUrl;
    link.setAttribute("download", planInfo.value.fileName || "预案文件");
    link.setAttribute("target", "_blank"); // 添加target属性
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 分页处理函数
const handlePagination = (val) => {
  queryParams.value.current = val.page;
  queryParams.value.size = val.limit;

  if (title.value === "请选择关联备件") {
    if (queryParams.value.faultDesc) {
      queryParams.value.codeAndName = queryParams.value.faultDesc;
    }

    getSparePartsList().then(() => {
      nextTick(() => {
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.id)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  } else {
    if (queryParams.value.faultDesc) {
      queryParams.value.nickNameAndWorkId = queryParams.value.faultDesc;
    }

    getList().then(() => {
      nextTick(() => {
        tableRef.value?.clearSelection();
        ledgerList.value.forEach((row) => {
          if (tableAllSelectedId.value.includes(row.userId)) {
            tableRef.value?.toggleRowSelection(row, true);
          }
        });
      });
    });
  }
};

onMounted(() => {
  const planId = route.query.planId;
  if (planId) {
    getPlanDetail(planId);
  }
});
</script>

<style lang="scss" scoped>
.addEmergency {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
:deep(.el-descriptions__body) {
  .el-descriptions__table.is-bordered .el-descriptions__cell {
    // padding: 30px 11px;
    /* min-width: 120px;
    max-width: 500px; */
    word-break: break-all; // 让内容超出列宽时自动换行显示
    word-wrap: break-word;
  }
}
</style>
