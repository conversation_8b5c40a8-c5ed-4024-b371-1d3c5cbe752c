<template>
  <div class="unify">
    <div class="unify-main">
      <div class="unify-main_header">
        <div class="unify-main_header-title">数字资产可视化数据大屏</div>
        <div class="unify-main_header-btns">
          <div
            class="unify-main_header-btns_btn"
            :class="curBtn == index ? 'active' : ''"
            v-for="(item, index) in headerBtns"
            :key="index"
            @click="handleBtnClick(index)"
          >
            {{ item.name }}
          </div>
        </div>
        <div class="unify-main_header-date">
          {{ proxy.parseTime(curTime, "{y}年{m}月{d}日 {h}:{i}") }}
        </div>
      </div>
      <div class="unify-main_charts">
        <div class="col col1">
          <div class="col1-top2">
            <div class="item-opt">
              <span>{{ objData.deviceTotal || 0 }}</span
              >台
              <div>资产总数</div>
            </div>
            <div class="item-opt">
              <span>{{ objData.maintenanceTotal || 0 }}</span
              >人
              <div>运维人员</div>
            </div>
          </div>
          <div class="item col1-top">
            <div class="item-title">{{ titles.title1 }}</div>
            <div class="echart">
              <div class="echart-item one">
                <Echarts
                  id="pieData"
                  width="100%"
                  height="100%"
                  :fullOptions="pieOption"
                />
              </div>
              <div class="echart-item two">
                <Echarts
                  id="barData"
                  width="100%"
                  height="100%"
                  :fullOptions="barOption"
                />
              </div>
            </div>
          </div>
          <div class="item col1-bottom">
            <div class="item-title">{{ titles.title2 }}</div>
            <div class="battery" @click="objData.icCover = !objData.icCover">
              <span>{{
                objData.icCover
                  ? objData.assetCoverageStatistics.coverage
                  : objData.assetCoverageStatistics.uncovered
              }}</span>
            </div>
            <div class="table" style="height: 9.5vw">
              <div class="table-row opt-title">
                <div class="table-row_name table-row_head">部门名称</div>
                <div class="table-row_name table-row_head">资产总数</div>
                <div class="table-row_name table-row_head">操作</div>
              </div>
              <div
                class="table-row data"
                v-for="item in objData.assetCoverageStatistics
                  .assetCoverageRanking"
                :key="item.deptName"
              >
                <div class="table-row_name">{{ item.deptName }}</div>
                <div class="table-row_name">{{ item.assetsNum }}</div>
                <div class="table-row_name">点击查看</div>
              </div>
            </div>
          </div>
          <div class="item col1-bottom">
            <div class="item-title">{{ titles.title3 }}</div>
            <div style="display: flex">
              <div class="table">
                <div class="subtitle">备件使用排行榜</div>
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">备件名称</div>
                  <div class="table-row_count table-row_head">使用量</div>
                </div>
                <div
                  class="table-row data"
                  v-for="(item, index) in objData.sparepartsUseStatistics"
                  :key="index"
                >
                  <div class="table-row_number">{{ index + 1 }}</div>
                  <div class="table-row_type">{{ item.sparepartsName }}</div>
                  <div class="table-row_count">{{ item.userNum }}</div>
                </div>
              </div>
              <div class="table">
                <div class="subtitle">设备备件消耗排行榜</div>
                <div class="table-row opt-title">
                  <div class="table-row_number table-row_head">序号</div>
                  <div class="table-row_type table-row_head">备件名称</div>
                  <div class="table-row_count table-row_head">使用量</div>
                </div>
                <div
                  class="table-row data"
                  v-for="(
                    item, index
                  ) in objData.sparepartsConsumptionStatistics"
                  :key="index"
                >
                  <div class="table-row_number">{{ index + 1 }}</div>
                  <div class="table-row_type">{{ item.deviceName }}</div>
                  <div class="table-row_count">{{ item.consumptionNum }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col col2">
          <div class="item col2-bottom">
            <div class="item-title">{{ titles.title4 }}</div>
            <div class="col2-bottom_bg">
              <div
                class="item-opt"
                :class="[
                  `opt${index + 1}`,
                  curTab == index ? 'active' : '',
                  index % 2 == 0 ? 'left' : 'right',
                ]"
                v-for="(item, index) in optList5"
                :key="index"
                @click="handleWork(index)"
              >
                <div>{{ item.name }}</div>
                <span>{{ item.number }}</span>
              </div>
            </div>
          </div>
          <div style="display: flex; gap: 0 1vw">
            <div class="item col2-bottom" style="width: 50%; height: 12vw">
              <div class="item-title">{{ titles.title5 }}</div>
              <div class="echart line">
                <Echarts
                  id="lineData"
                  width="100%"
                  height="10vw"
                  :fullOptions="lineOption"
                />
                <div>
                  <div class="item-opt" v-for="(item, index) in optList3" :key="index">
                    {{ item.title }}
                    <div class="item-opt_row">
                      <span>{{
                        objData.workOrderIncreaseStatistics[0]
                          .processingWorkOrderYear || 0
                      }}</span
                      >个
                      <div
                        :class="
                          objData.workOrderIncreaseStatistics[0].ratioYear > 0
                            ? 'up'
                            : objData.workOrderIncreaseStatistics[0].ratioYear <
                              0
                            ? 'low'
                            : 'none'
                        "
                      >
                        {{
                          objData.workOrderIncreaseStatistics[0]
                            .processingWorkOrderYearRise
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
            <div class="item col2-bottom" style="width: 50%; height: 12vw">
              <div class="item-title">{{ titles.title6 }}</div>
              <div class="echart line">
                <Echarts
                  id="lineData2"
                  width="100%"
                  height="10vw"
                  :fullOptions="lineOption2"
                />
                <div>
                  <div class="item-opt" v-for="(item, index) in optList3" :key="index">
                    {{ item.title }}
                    <div class="item-opt_row">
                      <span>{{
                        objData.knowledgeBaseIncreaseStatistics[0]
                          .knowledgeBaseYear || 0
                      }}</span
                      >个
                      <div
                        :class="
                          objData.knowledgeBaseIncreaseStatistics[0].ratioYear >
                          0
                            ? 'up'
                            : objData.knowledgeBaseIncreaseStatistics[0]
                                .ratioYear < 0
                            ? 'low'
                            : 'none'
                        "
                      >
                        {{
                          objData.knowledgeBaseIncreaseStatistics[0]
                            .knowledgeBaseYearRise
                        }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="col col3">
          <div class="item col3-top">
            <div class="item-title">{{ titles.title7 }}</div>
            <div style="display: flex; width: 100%; height: 6vw">
              <div style="width: 50%">
                <div class="subtitle">使用年限占比</div>
                <Echarts
                  id="pieData2"
                  width="100%"
                  height="80%"
                  :fullOptions="pieOption2"
                />
              </div>
              <div style="width: 50%">
                <div class="subtitle">故障时长占比</div>
                <Echarts
                  id="pieData3"
                  width="100%"
                  height="80%"
                  :fullOptions="pieOption3"
                />
              </div>
            </div>
          </div>
          <div class="item col3-center">
            <div class="item-title">{{ titles.title8 }}</div>
            <div style="display: flex; width: 100%; height: 6vw">
              <div style="width: 50%">
                <div class="subtitle">软件资产状况</div>
                <Echarts
                  id="pieData4"
                  width="100%"
                  height="80%"
                  :fullOptions="pieOption4"
                />
              </div>
              <div style="width: 50%">
                <div class="subtitle">软件到期时间</div>
                <Echarts
                  id="pieData5"
                  width="100%"
                  height="80%"
                  :fullOptions="pieOption5"
                />
              </div>
            </div>
            <div style="display: flex; width: 100%; height: 6vw">
              <div style="width: 50%">
                <div class="subtitle">软件使用状况</div>
                <Echarts
                  id="pieData6"
                  width="100%"
                  height="80%"
                  :fullOptions="pieOption6"
                />
              </div>
              <div style="width: 50%">
                <div class="subtitle">应用使用时长Top5</div>
                <Echarts
                  id="barData2"
                  width="100%"
                  height="80%"
                  :fullOptions="barOption2"
                />
              </div>
            </div>
          </div>
          <div class="item col3-bottom2">
            <div class="item-title">{{ titles.title9 }}</div>
            <div class="echart line" style="display: flex; height: 6vw">
              <Echarts
                id="pieData7"
                width="50%"
                height="100%"
                :fullOptions="pieOption7"
              />
              <div
                class="subitem"
                style="
                  width: 50%;
                  padding-top: 2.5vw;
                  line-height: 1.5vw;
                  font-size: 0.8vw;
                  text-align: center;
                "
              >
                共入库网络地址个数<br />
                <span style="font-size: 1.5vw">36564</span>
              </div>
            </div>
          </div>
          <div class="item col3-bottom">
            <div class="item-title">{{ titles.title10 }}</div>
            <div class="echart line" style="display: flex; gap: 0 1vw">
              <Echarts
                id="lineData3"
                width="50%"
                height="10vw"
                :fullOptions="lineOption3"
              />
              <div>
                <div class="item-opt" v-for="(item, index) in optList3" :key="index">
                  {{ item.title }}
                  <div class="item-opt_row">
                    <span>42</span>个
                    <div
                      :class="
                        objData.knowledgeBaseIncreaseStatistics[0].ratioYear > 0
                          ? 'up'
                          : objData.knowledgeBaseIncreaseStatistics[0]
                              .ratioYear < 0
                          ? 'low'
                          : 'none'
                      "
                    >
                      {{
                        objData.knowledgeBaseIncreaseStatistics[0]
                          .knowledgeBaseYearRise
                      }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup name="unifyIndex">
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getSchoolStatistics,
  getWorkOrderStatistics,
  getAssetsStatistics,
} from "@/api/unify";

const { proxy } = getCurrentInstance();
const router = useRouter();

const titles = ref({
  title1: "资产类别占比",
  title2: "资产覆盖率",
  title3: "排行榜",
  title4: "快捷入口主视图",
  title5: "工单统计",
  title6: "月度数据信息资产总数",
  title7: "硬件资产状况",
  title8: "软件资产状况",
  title9: "网络资产状况",
  title10: "知识产权资产状况",
});

const optList3 = ref([
  { title: "设备总数量", money: 1359, up: 0.8 },
  { title: "在线设备", money: 1286, up: 0.5 },
  // { title: "故障设备", money: 73, up: -0.4 },
  // { title: "设备利用率", money: 94.6, up: 0.3 },
]);

const optList5 = ref([
  { name: "数据信息资产", number: "01" },
  { name: "工作台", number: "02" },
  { name: "网络资产", number: "03" },
  { name: "硬件资产", number: "04" },
  { name: "知识产权资产", number: "05" },
  { name: "软件资产", number: "06" },
]);

const barXData = ["服务器", "网络设备", "安全设备", "存储设备", "监控设备"];
const barSData = [386, 275, 198, 167, 142];

const state = reactive({
  timer: null,
  curBtn: 0,
  curTab: 1,
  curTime: new Date().getTime(),
});

const { curBtn, curTab, curTime, timer } = toRefs(state);

// 设备状态列表
const deviceStatusList = ref([
  { value: 92, unit: "%", title: "设备在线率" },
  { value: 85, unit: "%", title: "设备完好率" },
  { value: 78, unit: "%", title: "设备利用率" },
  { value: 95, unit: "%", title: "维护覆盖率" },
]);

//
const deviceCountList = ref([
  { value: 0, unit: "台", title: "资产总数" },
  { value: 0, unit: "人", title: "运维人员" },
]);

// 设备维护情况列表
const maintenanceList = ref([
  {
    percentage: 67,
    title: "设备利用率",
    color: "#1a57fa",
  },
  {
    percentage: 85,
    title: "维护完成率",
    color: "#47a7fa",
  },
  {
    percentage: 92,
    title: "巡检覆盖率",
    color: "#00b8f9",
  },
]);

// 头部按钮数据
const headerBtns = ref([
  { name: "资产总览", type: "overview" },
  { name: "设备集控", type: "monitor" },
  // { name: "告警分析", type: "alarm" },
]);

function handleWork(val) {
  if (val == 1) {
    // router.push("/work");
  }
}

// 按钮点击处理函数
const handleBtnClick = (index) => {
  // curBtn.value = index; // 更新当前选中按钮的索引

  // 根据不同按钮类型更新数据
  switch (headerBtns.value[index].type) {
    // case "overview": // 设备概况视图
    //   // 更新设备状态数据 - 显示设备整体运行状态
    //   deviceStatusList.value = [
    //     { value: 92, unit: "%", title: "设备在线率" },
    //     { value: 85, unit: "%", title: "设备完好率" },
    //     { value: 78, unit: "%", title: "设备利用率" },
    //     { value: 95, unit: "%", title: "维护覆盖率" },
    //   ];

    //   // 更新设备数量统计 - 显示不同状态的设备数量
    //   deviceCountList.value = [
    //     { value: 3653, unit: "台", title: "在线设备" },
    //     { value: 286, unit: "台", title: "故障设备" },
    //     { value: 1247, unit: "台", title: "运行设备" },
    //     { value: 892, unit: "台", title: "待机设备" },
    //   ];

    //   // 更新维护情况 - 显示设备维护相关的进度
    //   maintenanceList.value = [
    //     { percentage: 67, title: "设备利用率", color: "#1a57fa" },
    //     { percentage: 85, title: "维护完成率", color: "#47a7fa" },
    //     { percentage: 92, title: "巡检覆盖率", color: "#00b8f9" },
    //   ];

    //   // 更新各区域标题
    //   titles.value = {
    //     ...titles.value,
    //     title1: "设备类型分布",
    //     title2: "设备运行状态",
    //     title3: "部门设备概况",
    //   };
    //   break;

    case "monitor": // 运行监控视图
      // router.push("/deviceControl/monitor");
      // 更新状态列表 - 显示系统资源使用情况
      // deviceStatusList.value = [
      //   { value: 88, unit: "%", title: "CPU使用率" },
      //   { value: 76, unit: "%", title: "内存使用率" },
      //   { value: 82, unit: "%", title: "存储使用率" },
      //   { value: 93, unit: "%", title: "网络使用率" },
      // ];

      // // 更新设备数量 - 显示性能相关的设备状态
      // deviceCountList.value = [
      //   { value: 2865, unit: "台", title: "正常运行" },
      //   { value: 468, unit: "台", title: "性能告警" },
      //   { value: 986, unit: "台", title: "高负载" },
      //   { value: 756, unit: "台", title: "待优化" },
      // ];

      // // 更新维护情况 - 显示资源利用率
      // maintenanceList.value = [
      //   { percentage: 78, title: "CPU利用率", color: "#1a57fa" },
      //   { percentage: 65, title: "内存利用率", color: "#47a7fa" },
      //   { percentage: 88, title: "网络利用率", color: "#00b8f9" },
      // ];

      // // 更新标题为监控相关
      // titles.value = {
      //   ...titles.value,
      //   title1: "资源使用分布",
      //   title2: "性能监控状态",
      //   title3: "系统负载情况",
      // };
      break;

    case "alarm": // 告警分析视图
      // 更新状态列表 - 显示告警处理相关指标
      deviceStatusList.value = [
        { value: 95, unit: "%", title: "告警处理率" },
        { value: 89, unit: "%", title: "故障解决率" },
        { value: 72, unit: "%", title: "预警准确率" },
        { value: 86, unit: "%", title: "系统可用率" },
      ];

      // 更新设备数量 - 显示不同级别的告警数量
      deviceCountList.value = [
        { value: 156, unit: "个", title: "严重告警" },
        { value: 486, unit: "个", title: "重要告警" },
        { value: 892, unit: "个", title: "次要告警" },
        { value: 1247, unit: "个", title: "提示信息" },
      ];

      // 更新维护情况 - 显示告警处理进度
      maintenanceList.value = [
        { percentage: 92, title: "告警处理率", color: "#1a57fa" },
        { percentage: 76, title: "故障修复率", color: "#47a7fa" },
        { percentage: 85, title: "问题解决率", color: "#00b8f9" },
      ];

      // 更新标题为告警相关
      titles.value = {
        ...titles.value,
        title1: "告警等级分布",
        title2: "告警处理状态",
        title3: "故障分析概况",
      };
      break;
  }

  // 更新图表数据
  updateChartData(headerBtns.value[index].type);
};

// 更新所有图表的数据
const updateChartData = (type) => {
  switch (type) {
    case "overview":
      // 设备概况下的图表数据
      barOption.options.series[0].data = [386, 275, 198, 167, 142]; // 柱状图：各类设备数量
      pieOption.options.series[0].data = [
        // 饼图：设备类型分布
        { value: 386, name: "服务器" },
        { value: 275, name: "网络设备" },
        { value: 198, name: "安全设备" },
        { value: 167, name: "存储设备" },
      ];
      lineOption.options.series[0].data = [42, 38, 45, 32, 29, 35, 28]; // 折线图：设备趋势
      break;

    case "monitor":
      // 运行监控下的图表数据
      barOption.options.series[0].data = [425, 312, 256, 189, 134]; // 柱状图：资源使用情况
      pieOption.options.series[0].data = [
        // 饼图：资源分布
        { value: 425, name: "CPU" },
        { value: 312, name: "内存" },
        { value: 256, name: "存储" },
        { value: 189, name: "网络" },
      ];
      lineOption.options.series[0].data = [56, 48, 52, 45, 39, 42, 35]; // 折线图：性能趋势
      break;

    case "alarm":
      // 告警分析下的图表数据
      barOption.options.series[0].data = [156, 486, 892, 1247, 235]; // 柱状图：告警数量
      pieOption.options.series[0].data = [
        // 饼图：告警等级分布
        { value: 156, name: "严重告警" },
        { value: 486, name: "重要告警" },
        { value: 892, name: "次要告警" },
        { value: 235, name: "提示信息" },
      ];
      lineOption.options.series[0].data = [35, 42, 38, 45, 32, 28, 36]; // 折线图：告警趋势
      break;
  }
};

const objData = ref({
  icCover: true,
  assetCoverageStatistics: {},
  assetsTypeStatistics: [],
  knowledgeBaseIncreaseStatistics: [
    {
      knowledgeBaseMonth: "",
      knowledgeBaseMonthRise: "",
      knowledgeBaseYear: "",
      knowledgeBaseYearRise: "",
    },
  ],
  workOrderIncreaseStatistics: [
    {
      processingWorkOrderMonth: "",
      processingWorkOrderMonthRise: "",
      processingWorkOrderYear: "",
      processingWorkOrderYearRise: "",
    },
  ],
}); // 存放数据
const orderParams = ref({
  range: 3,
}); // 工单统计年月日筛选 range: 1日 2月 3年

//资产类别占比柱状图
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "15%",
      right: "3%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//应用使用时长柱状图
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "15%",
      top: "15%",
      right: "3%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "10%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

//资产类别占比饼图
const pieOption = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [{ value: 386, name: "服务器" }],
      },
    ],
  },
});

// 硬件使用年限占比
const pieOption2 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 硬件故障时长占比
const pieOption3 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 软件资产状况（假数据）
const pieOption4 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "操作系统", value: 512 },
          { name: "教学软件", value: 231 },
          { name: "其他软件", value: 96 },
        ],
      },
    ],
  },
});

// 软件到期时间
const pieOption5 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "5年以上", value: 1347 },
          { name: "3-5年", value: 563 },
          { name: "1-3年", value: 872 },
          { name: "1年以内", value: 468 },
        ],
      },
    ],
  },
});

// 软件使用状况
const pieOption6 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [],
      },
    ],
  },
});

// 网络资产状况（假数据）
const pieOption7 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "50%",
              lineHeight: "25",
            },
            b: {
              color: "#6889de",
              fontSize: "50%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 5,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//工单统计
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(73, 119, 196, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
        },
        symbol: "circle",
        symbolSize: 5,
      },
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(0, 155, 171, .2)",
        },
        itemStyle: {
          color: "#009bab",
        },
        lineStyle: {
          color: "#009bab",
        },
        symbol: "circle",
        symbolSize: 5,
      },
    ],
  },
});

//月度数据资产总数
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
        },
        symbol: "circle",
        symbolSize: 5,
      },
    ],
  },
});

//知识产权资产状况(假数据)
const lineOption3 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      axisLabel: {
        color: "#8495b6",
        fontSize: "50%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "15%",
      left: "10%",
      height: "60%",
      right: "5%",
    },
    series: [
      {
        data: [42],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#6a99fc",
        },
        lineStyle: {
          color: "#6a99fc",
        },
        symbol: "circle",
        symbolSize: 5,
      },
    ],
  },
});

//获取数据
function getData() {
  proxy.$modal.loading();
  getSchoolStatistics()
    .then((res) => {
      proxy.$modal.closeLoading();
      console.log(res, "大屏数据");
      objData.value = {
        ...objData.value,
        deviceTotal: res.data.deviceTotal, // 资产总数
        maintenanceTotal: res.data.maintenanceTotal, // 运维人员
        sparepartsUseStatistics: res.data.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
        sparepartsConsumptionStatistics: res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}

        knowledgeBaseIncreaseStatistics: res.data.knowledgeBaseIncreaseStatistics, // 月度数据信息资产总数 本月度知识库-本年度知识库  返回是数组 数据如下
        //knowledgeBaseIncreaseStatistics： [{
        //   "knowledgeBaseMonth": 8, // 本月
        //   "knowledgeBaseMonthRise": "0%", // 本月增长率
        //   "knowledgeBaseYear": 8, // 本年
        //   "knowledgeBaseYearRise": "0%" // 本年增长率
        // }]

        knowledgeBaseStatistics: res.data.knowledgeBaseStatistics, // 月度数据信息资产总数 折线图数据  返回是数组 数据如下
        //knowledgeBaseStatistics： [
        //   {
        //     "statisticsTime": "2025-02",
        //     "num": 8
        //   }
        // ]

        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
        //    {
        //   "coverage": "22.73%",
        //   "uncovered": "77.27%",
        //   "assetCoverageRanking": [ // 表格
        //     {
        //       "deptName": "606",
        //       "assetsNum": 5
        //     },
        //     {
        //       "deptName": "开发部",
        //       "assetsNum": 4
        //     },
        //     {
        //       "deptName": "云天数据",
        //       "assetsNum": 3
        //     },
        //     {
        //       "deptName": "广州外国语学院",
        //       "assetsNum": 3
        //     },
        //     {
        //       "deptName": "A栋",
        //       "assetsNum": 1
        //     }
        //   ]
        // },
        assetsTypeStatistics: res.data.assetsTypeStatistics, // 资产类别占比
        // [
        //   {
        //     "assetsTypeName": "初始信息资产类别",
        //     "num": 2,
        //     "proportion": "66.67%"
        //   },
        //   {
        //     "assetsTypeName": "数据文件",
        //     "num": 1,
        //     "proportion": "33.33%"
        //   }
        // ]
        appUseTime: res.data.deviceAppUserStatistics.appUseTime, // 软件应用使用时长
        appUseType: res.data.deviceAppUserStatistics.appUseType, // 软件使用状况
      };

      // 软件使用时长柱状图赋值
      barOption2.value.options.xAxis.data = objData.value.appUseTime.map(
        (item) => item.month
      );
      barOption2.value.options.series[0].data = objData.value.appUseTime.map(
        (item) => item.timeTotal
      );

      // 软件使用占比饼图赋值
      pieOption6.value.options.series[0].data = objData.value.appUseType.map(
        (item) => {
          return {
            name: item.appType,
            value: item.num,
          };
        }
      );

      // 资产类别占比柱状图赋值
      barOption.value.options.xAxis.data =
        objData.value.assetsTypeStatistics.map((item) => item.assetsTypeName);
      barOption.value.options.series[0].data =
        objData.value.assetsTypeStatistics.map((item) => item.num);

      // 资产类别占比饼图赋值
      pieOption.value.options.series[0].data =
        objData.value.assetsTypeStatistics.map((item) => {
          return {
            name: item.assetsTypeName,
            value: item.num,
          };
        });

      lineOption2.value.options.xAxis.data =
        objData.value.knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
      lineOption2.value.options.series[0].data =
        objData.value.knowledgeBaseStatistics.map((item) => item.num);

      console.log(objData.value, "objData.value");
    })
    .catch(() => {
      proxy.$modal.closeLoading();
    });
}

function getAssetsData() {
  getAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    objData.value.deviceTroubleProportion = res.data.deviceTroubleProportion; // 折线图
    objData.value.hardwareAssetsUseProportion =
      res.data.hardwareAssetsUseProportion;

    pieOption2.value.options.series[0].data =
      objData.value.deviceTroubleProportion.map((item) => {
        return {
          name: item.timeRange,
          value: item.num,
        };
      });

    pieOption3.value.options.series[0].data =
      objData.value.hardwareAssetsUseProportion.map((item) => {
        return {
          name: item.timeRange,
          value: item.num,
        };
      });
  });
}

function getOrderData() {
  getWorkOrderStatistics(orderParams.value).then((res) => {
    console.log(res, "工单统计");
    objData.value.workOrderStatistics = res.data.workOrderStatistics; // 折线图
    objData.value.workOrderIncreaseStatistics = res.data.workOrderIncreaseStatistics; //月度年度处理单数
    //   "workOrderStatistics": [
    //   {
    //     "statisticsTime": "2025",
    //     "workOrderTotal": 115,
    //     "processingWorkOrder": 70
    //   }
    // ],
    // "workOrderIncreaseStatistics": [
    //   {
    //     "processingWorkOrderMonth": 70,
    //     "processingWorkOrderMonthRise": "0%",
    //     "processingWorkOrderYear": 70,
    //     "processingWorkOrderYearRise": "0%"
    //   }
    // ]

    lineOption.value.options.xAxis.data = objData.value.workOrderStatistics.map(
      (item) => item.statisticsTime
    );
    lineOption.value.options.series[0].data =
      objData.value.workOrderStatistics.map((item) => item.workOrderTotal);
    lineOption.value.options.series[1].data =
      objData.value.workOrderStatistics.map((item) => item.processingWorkOrder);
  });
}

onMounted(() => {
  getData();
  getOrderData();
  getAssetsData();
  // state.timer = setInterval(() => {
  //     state.curTime += 1000
  // }, 1000)
});

onBeforeUnmount(() => clearInterval(state.timer));
</script>

<style lang="scss" scoped>
.unify {
  width: 100vw;
  min-height: 100vh;
  background-color: #020d20;
  color: #c8d7fa;

  .opt-title {
    color: #6889de !important;
  }

  .subtitle {
    color: #c8d7fa;
    padding: 0.2vw 0.5vw 0;
    font-size: 0.7vw;
  }

  &-main {
    width: 100%;
    // height: 90vh;
    background: url("@/assets/screen/bg.png");
    background-size: 100% 100%;

    &_header {
      position: relative;
      width: 100%;
      height: 4vw;
      background: url("@/assets/screen/top_nav.png");
      background-size: 100% 100%;
      display: flex;
      align-items: center;
      padding: 0 1vw;

      &-title {
        // border: 1px solid red;
        font-size: 2vw;
        letter-spacing: 0.4vw;
      }

      &-btns {
        display: flex;
        margin-left: 4vw;
        padding-top: 0.5vw;

        &_btn {
          // border: 1px solid red;
          color: #6889de;
          cursor: pointer;
          text-align: center;
          width: 10.3vw;
          height: 2vw;
          line-height: 2vw;
          background: url("@/assets/screen/top_btn.png");
          background-size: 100% 100%;
          transition: all 0.3s ease;
          position: relative;
          overflow: hidden;

          &::before {
            content: "";
            position: absolute;
            top: 50%;
            left: 50%;
            width: 0;
            height: 0;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            transform: translate(-50%, -50%);
            transition: width 0.5s ease, height 0.5s ease;
          }

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);

            &::before {
              width: 300%;
              height: 300%;
            }
          }

          &.active {
            color: #c8d7fa;
            background: url("@/assets/screen/top_btn_sel.png");
            background-size: 100% 100%;
            transform: translateY(0);

            &::before {
              width: 0;
              height: 0;
            }
          }
        }
      }

      &-date {
        position: absolute;
        right: 1vw;
        top: 1.5vw;
      }
    }

    &_charts {
      display: flex;
      // border: 1px solid red;
      width: 100%;
      height: 47vw;
      padding: 1vw;

      .col {
        .item {
          // width: 100%;
          position: relative;
          margin-bottom: 0.5vw;

          &::before {
            //border: 1px solid red;
            content: "";
            width: 100%;
            height: 2.5vw;
            left: 0;
            bottom: 0;
            position: absolute;
            background: url("@/assets/screen/frame_bottom.png");
            background-size: 100% 100%;
          }

          &-title {
            // width: 100%;
            height: 2.5vw;
            line-height: 2.5vw;
            background: url("@/assets/screen/title.png");
            background-size: 100% 100%;
          }
        }

        .flex1 {
          display: flex;
          flex-wrap: wrap;
          gap: 1vw;
        }
      }

      .col1 {
        // border: 1px solid red;
        // background-color: green;
        width: 25vw;
        height: 100%;
        &-top2 {
          // border: 1px solid red;
          display: flex;
          padding: 0 1vw 0.5vw;
          gap: 0 1vw;

          .item-opt {
            padding: 0vw 0 0vw 1.5vw;
            width: 12vw;
            height: 3.5vw;
            // border: 1px solid red;
            background: url("@/assets/screen/top_data_frame.png");
            background-size: 100% 100%;
            letter-spacing: 0.1vw;
            color: #6c95ff;

            span {
              color: #c8d7fa;
              display: inline-block;
              font-size: 1.5vw;
              margin-bottom: 0.4vw;
            }
          }
        }

        &-top {
          height: 9vw;

          .echart {
            display: flex;
            justify-content: space-between;
            height: 5vw;
            gap: 0 .5vw;
            margin-top: 1vw;

            &-item {
              flex: 1;

              // border: 1px solid #fff;
              &.one {
                background: url("@/assets/screen/left_data_frame_top.png");
                background-size: 100% 100%;
                text-align: center;
                align-content: center;

                :deep(.el-progress-circle) {
                  width: 5vw !important;
                  height: 5vw !important;
                }

                :deep(.el-progress path:first-child) {
                  stroke: #042043;
                }

                :deep(.el-progress__text) {
                  color: #c8d7fa;
                  font-size: 1.5vw !important;
                  min-width: 0 !important;
                }
              }

              &.two {
                //border: 1px solid yellow;
                flex: 1;
              }
            }
          }
        }

        &-bottom {
          .item-main {
            // padding: 1vw 0;
          }
          .battery {
            cursor: pointer;
            background: url("@/assets/screen/cover.png");
            background-size: 100% 100%;
            height: 6vw;
            text-align: center;
            line-height: 5vw;
            font-size: 1.5vw;
          }

          .item-opt {
            // border: 1px solid red;
            width: 9.2vw;
            background: url("@/assets/screen/left_data_frame_middle.png");
            background-size: 100% 100%;
            height: 3vw;
            padding: 0.2vw 0.5vw 0 3.5vw;
            font-size: 1.2vw;

            div {
              font-size: 0.8vw;
            }
          }
        }

        .table {
          height: 10.5vw;
          font-size: 0.7vw;

          // padding-top: 1vw;
          &-row {
            display: flex;
            padding: 0.2vw 0;
            text-align: center;
            margin-top: 0.2vw;

            &.data:hover {
              background-color: #061d47;
            }

            &_name {
              width: 9vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &_number {
              width: 3vw;
            }

            &_type {
              width: 5vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &_count {
              width: 5vw;
              overflow: hidden;
              text-overflow: ellipsis;
              white-space: nowrap;
            }

            &_percent {
              width: 4vw;
              padding-left: 1vw;
              position: relative;

              &.up::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 0.7vw;
                height: 0.8vw;
                background: url("@/assets/screen/arrow_up.png");
                background-size: 100% 100%;
              }

              &.low::before {
                content: "";
                position: absolute;
                top: 0;
                left: 0;
                width: 0.7vw;
                height: 0.8vw;
                background: url("@/assets/screen/arrow_low.png");
                background-size: 100% 100%;
              }

              &.table-row_head {
                padding-left: 0;
              }
            }
          }
        }

        .table2 {
          height: 14vw;
        }
      }

      .col2 {
        // border: 1px solid red;
        // background-color: yellow;
        width: 43vw;
        height: 100%;
        margin: 0 1vw;
        .line {
          display: flex;
        }

        &-bottom {
          height: 33vw;

          .item-opt {
            width: 8.7vw;
            // border: 1px solid red;
            background: url("@/assets/screen/right_data_frame.png");
            background-size: 100% 100%;
            padding: 0.5vw 0.5vw 1vw;
            font-size: 0.8vw;
            color: #8495b6;

            &_row {
              display: flex;
              align-items: center;
              margin-top: 0.5vw;

              span {
                display: inline-block;
                font-size: 1.1vw;
                padding-right: 0.2vw;
                color: #c7d9f9;
              }

              div {
                padding-left: 1.2vw;
              }

              .up {
                color: #2dcd5e;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0.3vw;
                  width: 0.7vw;
                  height: 0.8vw;
                  background: url("@/assets/screen/arrow_up.png");
                  background-size: 100% 100%;
                }
              }

              .low {
                color: #fe005d;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0.3vw;
                  width: 0.7vw;
                  height: 0.8vw;
                  background: url("@/assets/screen/arrow_low.png");
                  background-size: 100% 100%;
                }
              }
            }
          }
          // border: 1px solid red;
          &_bg {
            position: relative;
            // border: 1px solid red;
            width: 30vw;
            height: 26vw;
            margin: 2vw auto;
            background: url("@/assets/screen/middle_banner.png");
            background-size: 100% 100%;

            .item-opt {
              width: 10vw;
              height: 2vw;
              position: absolute;
              cursor: pointer;

              div {
                position: absolute;
                // border: 1px solid red;
                width: 8vw;
                height: 2vw;
                text-align: center;
                line-height: 1vw;
              }

              span {
                position: absolute;
                right: 0.4vw;
                top: 0.5vw;
              }

              &.right {
                background: url("@/assets/screen/middle_frame_right.png");
                background-size: 100% 100%;

                div {
                  right: 0;
                }

                span {
                  left: 0.4vw;
                }
              }

              &.left {
                background: url("@/assets/screen/middle_frame_left.png");
                background-size: 100% 100%;
              }

              &.opt1 {
                top: -0.8vw;
                left: -5.5vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_left_top_line.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 3vw;
                  right: -3.9vw;
                  top: -0.4vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_left_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    right: -4.6vw;
                    top: -1.1vw;
                    width: 5vw;
                    height: 4vw;
                    background: url("@/assets/screen/middle_left_top_line_sel.png");
                    background-size: 100% 100%;
                  }
                }
              }

              &.opt2 {
                right: -2.5vw;
                top: 0.5vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_right_top_line.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 3vw;
                  left: -3.9vw;
                  top: -0.6vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_right_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    background: url("@/assets/screen/middle_right_top_line_sel.png");
                    background-size: 100% 100%;
                    width: 5vw;
                    height: 4vw;
                    left: -4.7vw;
                    top: -1vw;
                  }
                }
              }

              &.opt3 {
                top: 5.8vw;
                left: -5vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_left_middle_line.png");
                  background-size: 100% 100%;
                  width: 3vw;
                  height: 4vw;
                  right: -0.7vw;
                  top: 1.9vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_left_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_left_middle_line_sel.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 5vw;
                    right: -1.1vw;
                    top: 1.6vw;
                  }
                }
              }

              &.opt4 {
                right: -5vw;
                bottom: 10.5vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_right_middle_line.png");
                  background-size: 100% 100%;
                  width: 3vw;
                  height: 4vw;
                  left: 3.5vw;
                  top: -4vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_right_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_right_middle_line_sel.png");
                    background-size: 100% 100%;
                    width: 4vw;
                    height: 5vw;
                    left: 3vw;
                    top: -4.6vw;
                  }
                }
              }

              &.opt5 {
                bottom: -0.5vw;
                left: -6vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_left_bottom_line.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 3vw;
                  right: -4vw;
                  top: -0.5vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_left_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_left_bottom_line_sel.png");
                    background-size: 100% 100%;
                    width: 5vw;
                    height: 4vw;
                    right: -4.6vw;
                    top: -0.9vw;
                  }
                }
              }

              &.opt6 {
                right: -3.5vw;
                bottom: 0.5vw;

                &::before {
                  position: absolute;
                  content: "";
                  background: url("@/assets/screen/middle_right_bottom_line.png");
                  background-size: 100% 100%;
                  width: 4vw;
                  height: 1.8vw;
                  left: -3.9vw;
                  top: -1.5vw;
                }

                &.active {
                  background: url("@/assets/screen/middle_frame_right_sel.png");
                  background-size: 100% 100%;

                  &::before {
                    position: absolute;
                    content: "";
                    background: url("@/assets/screen/middle_right_bottom_line_sel.png");
                    background-size: 100% 100%;
                    width: 5.5vw;
                    height: 4vw;
                    left: -5.5vw;
                    top: -3.4vw;
                  }
                }
              }
            }
          }
        }
      }

      .col3 {
        // border: 1px solid red;
        width: 28vw;
        height: 100%;

        &-top {
          .item-main {
            padding: 1vw 0;
            display: flex;
            flex-wrap: wrap;
            gap: 0.5vw;
          }
        }

        &-center {
          height: 15vw;

          .echart {
            height: 10vw;
          }
        }

        &-bottom {
          height: 12vw;

          .echart {
            height: 15vw;
          }

          .item-opt {
            width: 13vw;
            // border: 1px solid red;
            background: url("@/assets/screen/right_data_frame.png");
            background-size: 100% 100%;
            padding: 0.5vw 0.5vw 1vw;
            font-size: 0.8vw;
            color: #8495b6;

            &_row {
              display: flex;
              align-items: center;
              margin-top: 0.5vw;

              span {
                display: inline-block;
                font-size: 1.1vw;
                padding-right: 0.2vw;
                color: #c7d9f9;
              }

              div {
                padding-left: 1.2vw;
              }

              .up {
                color: #2dcd5e;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0.3vw;
                  width: 0.7vw;
                  height: 0.8vw;
                  background: url("@/assets/screen/arrow_up.png");
                  background-size: 100% 100%;
                }
              }

              .low {
                color: #fe005d;
                position: relative;

                &::before {
                  content: "";
                  position: absolute;
                  top: 0;
                  left: 0.3vw;
                  width: 0.7vw;
                  height: 0.8vw;
                  background: url("@/assets/screen/arrow_low.png");
                  background-size: 100% 100%;
                }
              }
            }
          }
        }
      }
    }
  }
}
</style>
