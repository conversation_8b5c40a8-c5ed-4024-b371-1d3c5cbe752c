<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <Detail
        :troubleId="route.query.id"
        :isHandled="true"
        @changeTroubleStatus="changeTroubleStatus"
        @changeEditStatus="changeEditStatus"
        @handleBack="handleBack"
        @getRecord="getRecord"
      />

      <!-- 按钮区域，根据readonly状态显示不同按钮 -->
      <div class="taskInfo-btns">
        <div style="display: flex" v-if="canEdit && !(isKnowledge || isSpare)">
          <el-button type="danger" @click="handleRollBack" v-throttle
            >回退</el-button
          >
          <el-button type="success" @click="handleToKonwledge" v-throttle
            >转入知识库</el-button
          >
          <el-button type="warning" @click="handleToFile" v-throttle
            >生成文档</el-button
          >
        </div>

        <el-button @click="handleBack">返回</el-button>
      </div>

      <div v-if="recordList.length > 0">
        <div class="taskInfo-tit">变更记录</div>
        <div
          class="taskInfo-record"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          {{
            `${item.name} ${item.role || ""} 于${item.operationTime} ${
              item.remark
            }`
          }}
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import { toRefs } from "vue";
import { addKnowledgeBase, troubleUpdate } from "@/api/mediaTeach/trouble";
import useUserStore from "@/store/modules/user";
import Detail from "@/views/taskCenter/components/index.vue";
import { addPointObj } from "@/utils/addPoint";
import { sendPointRequest } from "@/utils";

const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const isKnowledge = ref(route.query.knowledge == 1);
const isSpare = ref(!!route.query.spareId);
const isDevice = ref(!!route.query.deviceId);
const isDaily = ref(!!route.query.isDaily);

const state = reactive({
  troubleStatus: 2,
  canEdit: false,
  total: 0,
  recordList: [],
  title: "请选择扭转对象",
  dialogVisible: false,
  ledgerList: [],
  queryParams: {
    current: 1,
    size: 5,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: "",
    deviceStatus: "",
    putTime: "",
    status: "",
  },
  processStartTime: null, // 添加开始处理时间字段
  rules: {
    maintainName: [
      { required: true, message: "维修人不能为空", trigger: "blur" },
    ],
    results: [
      { required: true, message: "维修结果不能为空", trigger: "blur" },
      { max: 200, message: "维修结果最多输入200个字符", trigger: "blur" },
    ],
    maintainTime: [
      {
        required: true,
        message: "维修时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
    repairImg: [
      {
        required: true,
        message: "维修图片不能为空",
        trigger: ["blur", "change"],
      },
    ],
    /* repairFile: [
      {
        required: true,
        message: "维修附件不能为空",
        trigger: ["blur", "change"],
      },
    ], */
  },
});

const {
  troubleStatus,
  recordList,
  canEdit,
  total,
  title,
  dialogVisible,
  ledgerList,
  queryParams,
  commentForm,
  rules,
} = toRefs(state);

const changeTroubleStatus = (troubleStatus) => {
  state.troubleStatus = troubleStatus;
};

function handleToFile() {
  router.push({
    path: "/taskManage/taskCenter/addFileInfo",
    query: { id: route.query.id },
  });
}

const getRecord = (recordList) => {
  state.recordList = recordList;
};

const changeEditStatus = (editStatus) => {
  state.canEdit = editStatus;
};

const handleToKonwledge = () => {
  proxy.$modal.confirm(`确定将此工单转入知识库？`).then(() => {
    addKnowledgeBase({
      troubleId: route.query.id,
    }).then((resp) => {
      if (resp.code == 200) {
        sendPointRequest({
          event: "Click",
          eventDescribe: "点击转入知识库",
          content:
            troubleStatus.value > -1
              ? addPointObj[troubleStatus.value].status
              : "",
          num: 1,
        });
        proxy.$modal.msgSuccess("转入成功");
      }
    });
  });
};

// 取消/返回处理
function handleBack() {
  if (isKnowledge.value) {
    proxy.$tab.closeOpenPage("/knowledgeManage/knowledge");
  } else if (isSpare.value) {
    proxy.$tab.closeOpenPage(
      `/spareManage/spareInfo?id=${route.query.spareId}`
    );
  } else if (isDevice.value) {
    proxy.$tab.closeOpenPage(
      `/deviceLedger/deviceInfo?id=${route.query.deviceId}&type=0&form=${route.query.form}`
    );
  } else if (isDaily.value) {
    proxy.$tab.closeOpenPage(
      `/taskManage/daily?pageNum=${route.query.pageNum}&tab=${route.query.tab}`
    );
  } else {
    router.go(-1);
    proxy.$tab.closeOpenPage();
  }
}

const handleRollBack = () => {
  proxy.$modal
    .confirm(`确定回退此工单？`)
    .then(() => {
      troubleUpdate({
        troubleId: route.query.id,
        status: 5,
      }).then((resp) => {
        if (resp.code == 200) {
          proxy.$modal.msgSuccess("操作成功");
          handleBack();
        }
      });
    })
    .catch();
};
</script>

<style scoped lang="scss">
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }
  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.taskInfo {
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }

  &-record {
    font-size: 14px;
    margin-bottom: 5px;
  }
  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }
  &-btns {
    padding: 20px;
    text-align: center;
    display: flex;
    justify-content: center;
    // margin-top: 40px;
    .el-button {
      margin: 0 10px;
    }
  }
}

.upload-area {
  display: flex;
  align-items: center;
  margin-top: 10px;

  .upload-container {
    display: flex;
    align-items: center;
    gap: 20px;
  }

  .file-info {
    margin-left: 20px;
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      display: inline-block;
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    max-width: 200px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
