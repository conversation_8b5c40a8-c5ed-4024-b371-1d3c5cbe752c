<template>
    <div class="aprr-setting">
      <flowComponents @getApprovalData="getApprovalData" status="4" :objData="objData" />
    </div>
  </template>
  
  <script setup>
  import flowComponents from "./components/flowComponents.vue";
  import { approvalList } from "@/api/approval";
  
  const { proxy } = getCurrentInstance();
  const objData = ref({});
  
  // 获取审批列表
  function getApprovalData() {
    proxy.$modal.loading();
    approvalList()
      .then((res) => {
        console.log(res.data, "配置");
        // <!-- 1:挂起 2:回退 3:扭转 4:设备报废 -->
        objData.value = res.data.find((item) => item.operationApproval == 4) || {
          approvalPeopleList: [],
        };
  
        proxy.$modal.closeLoading();
      })
      .catch(() => {
        proxy.$modal.closeLoading();
      });
  }
  getApprovalData();
  </script>
  
  <style scoped>
  .aprr-setting {
    padding: 20px;
    background-color: #f5f5f5;
    border-radius: 5px;
    margin-top: 20px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
  }
  </style>
  