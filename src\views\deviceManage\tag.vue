<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
          <el-button v-if="route.query.type" @click="handleBack"
            >返回工作台</el-button
          >
        </div>
      </template>

      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="tag">
          <el-input
            v-model="queryParams.tag"
            placeholder="请输入标签名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb12">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd"
            >新增</el-button
          >
        </el-col>
      </el-row>

      <el-table
        v-loading="loading"
        :data="tagList"
        border
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          label="标签名称"
          align="center"
          minWidth="120px"
          prop="tag"
        />
        <el-table-column label="标签描述" align="center" minWidth="120px">
          <template #default="scope">
            {{ scope.row.remark || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="创建时间"
          align="center"
          minWidth="120px"
          prop="createTime"
        >
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime, "{y}-{m}-{d}") }}</span>
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="150"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              icon="Edit"
              @click="handleUpdate(scope.row)"
              >修改</el-button
            >
            <el-button
              link
              type="danger"
              icon="Delete"
              @click="handleDelete(scope.row)"
              >删除</el-button
            >
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        @pagination="getList"
      />
    </el-card>
    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="tagRef"
        :model="form"
        :rules="rules"
        label-width="100px"
        @submit.native.prevent
      >
        <el-form-item label="标签名称" prop="tag">
          <el-input
            v-model="form.tag"
            :readonly="readonly"
            :maxlength="15"
            placeholder="请输入标签名称"
          />
        </el-form-item>
        <el-form-item label="标签描述" prop="remark">
          <el-input
            v-model="form.remark"
            type="textarea"
            :readonly="readonly"
            :maxlength="100"
            placeholder="请输入标签描述"
          />
        </el-form-item>
        <!-- <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status">
                        <el-radio v-for="item in statusList" :key="item.value" :label="item.value">{{ item.label
                        }}</el-radio>
                    </el-radio-group>
                </el-form-item> -->
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitForm" v-throttle
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="typeManage">
import { useRoute } from "vue-router";
import {
  deviceTagAdd,
  checkDeviceTag,
  deviceTagDel,
  deviceTagEdit,
  deviceTagPage,
} from "@/api/mediaTeach/tag";
import { onMounted, onBeforeUnmount } from "vue";
import { sendPointRequest } from "@/utils";

const route = useRoute();
const { proxy } = getCurrentInstance();

const tagList = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const readonly = ref(false);
const ids = ref([]);
const statusList = ref([
  { label: "启用", value: 0, type: "success" },
  { label: "停用", value: 1, type: "danger" },
]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
  addTimer: null,
  addSecond: 0,
  form: {},
  queryParams: {
    current: 1,
    size: 10,
    tag: "",
  },
  rules: {
    tag: [
      { required: true, message: "标签名称不能为空", trigger: "blur" },
      { max: 20, message: "标签名称最多输入20个字符", trigger: "blur" },
    ],
    remark: [
      { max: 200, message: "标签描述最多输入200个字符", trigger: "blur" },
    ],
  },
});

const { queryParams, form, rules, addTimer, addSecond } = toRefs(data);

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}

/** 查询标签列表 */
function getList() {
  loading.value = true;
  deviceTagPage(queryParams.value).then((response) => {
    tagList.value = response.data.records;
    total.value = response.data.total;
    loading.value = false;
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}
/** 表单重置 */
function reset() {
  form.value = {
    tagId: "",
    tag: "",
    remark: "",
    // status: 0,
    createTime: "",
  };
  proxy.resetForm("tagRef");
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  queryParams.value.size = 10;
  handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map((item) => item.postId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
  reset();
  open.value = true;
  title.value = "添加标签";
}
/** 修改按钮操作 */
function handleUpdate(row) {
  reset();
  title.value = "修改标签";
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}
/** 查看按钮操作 */
function handleCheck(row) {
  reset();
  title.value = "查看标签";
  readonly.value = true;
  form.value = JSON.parse(JSON.stringify(row));
  open.value = true;
}
/** 提交按钮 */
function submitForm() {
  proxy.$refs["tagRef"].validate((valid) => {
    if (valid) {
      if (!!form.value.tagId) {
        deviceTagEdit(form.value).then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          getList();
          open.value = false;
        });
      } else {
        deviceTagAdd(form.value).then((response) => {
          proxy.$modal.msgSuccess("新增成功");
          getList();
          open.value = false;
        });
      }
    }
  });
}
/** 删除按钮操作 */
function handleDelete(row) {
  checkDeviceTag(row.tagId).then((res) => {
    console.log("检查", res);
    if (!!res.data) {
      proxy.$modal
        .confirm(`${row.tag}标签已存在设备，确认删除?`)
        .then(function () {
          return deviceTagDel({ id: row.tagId });
        })
        .then(() => {
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    } else {
      proxy.$modal
        .confirm('是否确认删除标签名称为"' + row.tag + '"的数据项？')
        .then(async function () {
          await deviceTagDel({ id: row.tagId });
        })
        .then(() => {
          getList();
          proxy.$modal.msgSuccess("删除成功");
        })
        .catch(() => {});
    }
  });
}

onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  getList();
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览设备页面基础数据管理",
    content: '标签管理',
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
});
</script>
