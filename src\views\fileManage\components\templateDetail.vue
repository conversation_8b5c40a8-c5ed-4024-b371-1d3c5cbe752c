<template>
  <div class="templateInfo" v-loading="loading">
    <div class="templateInfo-header">
      <div
        class="templateInfo-header_item"
        v-for="(item, index) in activeList"
        :key="index"
        :class="[active == index ? 'active' : '', `item${index + 1}`]"
      >
        {{ item }}
      </div>
    </div>
    <div class="templateInfo-main" v-show="active == 0">
      <el-form :model="form" ref="formRef" :disabled="isPreview" :rules="rules">
        <el-descriptions title="" border :column="isPreview ? 3 : 2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width-other"
            label="模板编号"
            v-if="isPreview"
          >
            <span>{{ form.templateNo || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="模板名称"
          >
            <div class="column_item">
              <el-form-item label="" prop="templateName" style="width: 100%">
                <el-input
                  placeholder="请输入"
                  v-model.trim="form.templateName"
                  :disabled="isPreview"
                  :maxlength="10"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="模板类别"
          >
            <div class="column_item">
              <el-form-item prop="templateTypeId">
                <el-select
                  v-model="form.templateTypeId"
                  placeholder="请选择类别"
                >
                  <el-option
                    v-for="item in typeList"
                    :key="item.value"
                    :label="item.label"
                    :value="item.value"
                  />
                </el-select>
              </el-form-item>
            </div>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width-other"
            label="模板描述"
          >
            <div class="column_item">
              <el-form-item prop="templateDescribe">
                <el-input
                  placeholder="请输入"
                  v-model.trim="form.templateDescribe"
                  type="textarea"
                  :maxlength="200"
                  show-word-limit
                />
              </el-form-item>
            </div>
          </el-descriptions-item>
        </el-descriptions>

        <el-form-item
          label-position="top"
          label="请选择模板配置的信息"
          prop="config"
          style="margin-top: 20px"
        >
          <el-checkbox-group v-model="form.config">
            <el-checkbox label="项目信息" value="0" @change="changeConfig" />
            <el-checkbox
              label="干系人信息"
              value="1"
              v-if="form.config.includes('0')"
            />
            <el-checkbox label="资产信息" value="2" />
            <el-checkbox label="运维工单信息" value="3" />
            <!-- <el-checkbox label="统计分析信息" value="4" /> -->
          </el-checkbox-group>
        </el-form-item>
      </el-form>
    </div>
    <div v-show="active == 1">
      <CanvasEditor
        ref="canvasEditor"
        :parentContent="parentContent"
        :view="view"
        :type="isPreview ? '1' : '0'"
        @save-content="handleSaveCanvasEditorContent"
      />
    </div>

    <div class="templateInfo-footer">
      <template v-if="!isPreview">
        <el-button v-if="active == 0" type="primary" @click="handleNext"
          >下一步</el-button
        >
        <el-button v-if="active == 1" type="primary" @click="active = 0"
          >上一步</el-button
        >
        <el-button v-if="active == 1" type="success" @click="handleSave" v-throttle
          >完成创建</el-button
        >
      </template>
      <template v-else>
        <el-button v-if="active == 0" type="primary" @click="active = 1"
          >查看模板内容</el-button
        >
        <el-button v-if="active == 1" type="primary" @click="active = 0"
          >返回基本信息</el-button
        >
      </template>
      <el-button @click="handleBack">返回</el-button>
    </div>
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  getCurrentInstance,
  reactive,
  toRefs,
  watch,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import useUserStore from "@/store/modules/user";
import { timeFormat } from "@/utils";
import CanvasEditor from "@/views/canvas-editor/index.vue";
import { nextTick } from "process";
import { getSchoolTemplateTypeList } from "@/api/fileMamage/templateCategory";
import {
  addSchoolTemplate,
  getSchoolTemplateDetail,
  getSchoolTemplateList,
} from "@/api/fileMamage/template";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();
const { nickName, roles } = useUserStore();
const props = defineProps({
  id: {
    type: String,
    default: "",
  },
  isAdd: {
    type: Boolean,
    default: false,
  },
});
const state = reactive({
  formRef: null,
  loading: false,
  active: 0,
  activeList: ["基本信息", props.isAdd ? "编辑模板" : "模板内容"],
  templateNameList_all: [],
  typeList: [],
  isNum: 0,
  form: {
    templateNumber: "",
    templateName: "",
    templateTypeId: "",
    templateDescribe: "",
    templateContent: "",
    config: [],
    projectStatus: 0,
    relationStatus: 0,
    assetStatus: 0,
    workOrderStatus: 0,
    statisticsStatus: 0,
  },
  rules: {
    templateName: [
      {
        required: true,
        validator: validName,
        trigger: "blur",
      },
    ],
    templateTypeId: [
      {
        required: true,
        message: "模板类型不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  formRef,
  loading,
  active,
  activeList,
  form,
  typeList,
  templateNameList_all,
  rules,
} = toRefs(state);

// 添加必要的响应式变量
const view = ref("parent");
const parentContent = ref(undefined);
const canvasEditor = ref(null);
const isPreview = ref(false);

function validName(rules, value, callback) {
  if (value === "") {
    callback(new Error("模板名称不能为空"));
  } else if (state.templateNameList_all.indexOf(value) != -1) {
    callback(new Error("模板名称已存在"));
  } else {
    callback();
  }
}

// 初始化数据
onMounted(async () => {
  // 判断是否是预览模式
  isPreview.value = !props.isAdd;
  if (props.isAdd) {
    await getTemplateList();
  }
  // 如果是预览模式且有ID，获取模板详情
  if (isPreview.value && props.id) {
    // 注意：这里使用 templateNo 作为参数
    state.loading = true;
    await getSchoolTemplateDetail(props.id)
      .then((res) => {
        if (res.code === 200) {
          console.log(res, "模板详情");

          // 填充表单数据
          Object.assign(form.value, res.data);

          // 设置配置项
          form.value.config = [];
          if (res.data.projectStatus === 1) form.value.config.push("0");
          if (res.data.relationStatus === 1) form.value.config.push("1");
          if (res.data.assetStatus === 1) form.value.config.push("2");
          if (res.data.workOrderStatus === 1) form.value.config.push("3");
          if (res.data.statisticsStatus === 1) form.value.config.push("4");

          // 初始化编辑器内容
          if (res.data.templateContent) {
            try {
              parentContent.value = JSON.parse(res.data.templateContent);
              console.log(parentContent.value, "模板内容");
            } catch (e) {
              console.error("解析模板内容失败:", e);
              parentContent.value = { main: [] };
            }
          }
        } else {
          proxy.$modal.msgError(res.msg || "获取模板详情失败");
        }
        state.loading = false;
      })
      .catch((error) => {
        console.error("获取模板详情失败:", error);
        state.loading = false;
        proxy.$modal.msgError("获取模板详情失败");
      });
  } else {
    getEditorContent();
  }
  getTemplateType();
});

function getTemplateList() {
  getSchoolTemplateList({ pageNum: 1, pageSize: 99999 }).then((res) => {
    if (res.data) {
      state.templateNameList_all = res.data.rows?.map(
        (item) => item.templateName
      );
    }
    console.log("模板列表", res, state.templateNameList_all);
  });
}

function changeConfig(val) {
  if (!val) {
    state.form.config = state.form.config.filter((_) => _ != "1");
    console.log(val, state.form.config);
  }
}

function getTemplateType() {
  // 获取模板类型列表
  getSchoolTemplateTypeList({
    pageNum: 1,
    pageSize: 9999,
  })
    .then((res) => {
      if (res.code === 200) {
        let arr = res.data.rows;
        if (isPreview.value) {
          typeList.value = arr.map((item) => ({
            label: item.templateTypeName,
            value: item.templateTypeId,
          }));
        } else {
          typeList.value = arr
            .filter((_) => _.templateTypeStatus == 0)
            .map((item) => ({
              label: item.templateTypeName,
              value: item.templateTypeId,
            }));
        }
      }
    })
    .catch((error) => {
      console.error("获取模板类型列表失败:", error);
      proxy.$modal.msgError("获取模板类型列表失败");
    });
}

function handleNext() {
  if (!props.isAdd) {
    state.active = 1;
    return;
  }

  formRef.value.validate((valid) => {
    if (valid) {
      state.active = 1;
    }
  });
}

function handleBack() {
  proxy.$tab.closeOpenPage("/fileManage/templateManage/fileTemplate");
}

// 监听 config 数组变化，更新对应的状态
watch(
  () => form.value.config,
  (newVal) => {
    // 重置所有状态为0
    form.value.projectStatus = 0;
    form.value.relationStatus = 0;
    form.value.assetStatus = 0;
    form.value.workOrderStatus = 0;
    form.value.statisticsStatus = 0;

    // 根据选中的配置更新状态
    newVal.forEach((item) => {
      switch (item) {
        case "0":
          form.value.projectStatus = 1;
          break;
        case "1":
          form.value.relationStatus = 1;
          break;
        case "2":
          form.value.assetStatus = 1;
          break;
        case "3":
          form.value.workOrderStatus = 1;
          break;
        case "4":
          form.value.statisticsStatus = 1;
          break;
      }
    });
  }
);

// 修改保存方法为传统的 Promise 方式
function handleSave() {
  canvasEditor.value.saveContent();

  if (!form.value.templateContent) {
    proxy.$modal.msgError("请编辑模板内容");
    return;
  }

  // 构造API所需的参数对象，并去除空格
  const params = {
    templateName: form.value.templateName?.trim(),
    templateTypeId: form.value.templateTypeId,
    templateDescribe: form.value.templateDescribe?.trim(),
    templateContent: form.value.templateContent,
    projectStatus: form.value.projectStatus,
    relationStatus: form.value.relationStatus,
    assetStatus: form.value.assetStatus,
    workOrderStatus: form.value.workOrderStatus,
    statisticsStatus: form.value.statisticsStatus,
  };
  console.log(params);
  // return;
  // return;
  // 新增调用接口
  addSchoolTemplate(params)
    .then((res) => {
      proxy.$modal.msgSuccess("创建成功");
      handleBack();
    })
    .catch((error) => {
      // console.error("创建模板失败:", error);
      // proxy.$modal.msgError("创建模板失败");
    });
}

// 确保 getEditorContent 方法正确初始化编辑器内容
const getEditorContent = () => {
  let obj = {
    main: [],
  };

  parentContent.value = JSON.parse(JSON.stringify(obj));
};

// 处理子组件传递的数据
const handleSaveCanvasEditorContent = (data) => {
  console.log("从子组件接收到的数据:", data);
  form.value.templateContent = JSON.stringify(data.data);
  // form.value.templateContent = JSON.stringify(data.data);
};
</script>

<style lang="scss" scoped>
.templateInfo {
  width: 100%;
  background-color: #fff;
  padding: 0 20px;
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
      position: relative;
      &::after {
        content: "*";
        left: -60px;
        top: 30px;
        position: absolute;
        color: red;
      }
    }
    ::deep(.value-width-other) {
      width: 270px;
      min-height: 60px;
    }
  }
  &-header {
    background-color: #fff;
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px 2vw;
    gap: 0 10vw;
    font-size: 14px;
    &_item {
      display: flex;
      align-items: center;
      gap: 0 10px;
      &::before {
        content: "1";
        width: 2.5vw;
        height: 2.5vw;
        display: inline-block;
        text-align: center;
        line-height: 2.5vw;
        background-color: #909399;
        color: #fff;
        border-radius: 50%;
      }
      &.item2::before {
        content: "2";
      }
      &.active {
        color: #4095e5;
        &::before {
          background-color: #4095e5;
        }
      }
    }
  }
  &-main {
    min-height: 550px;
  }

  &-footer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    gap: 0 50px;
    background-color: #fff;
    margin-top: 20px;
  }

  // 添加预览模式的样式
  :deep(.el-input.is-disabled .el-input__wrapper) {
    background-color: #f5f7fa;
  }

  :deep(.el-textarea.is-disabled .el-textarea__inner) {
    background-color: #f5f7fa;
  }
}

.column_item {
  position: relative;
  margin-top: 20px;
  width: 100%;
}
</style>
