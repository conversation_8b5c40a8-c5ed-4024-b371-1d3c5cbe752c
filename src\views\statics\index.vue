<template>
  <div class="dashboard-container">
    <el-row :gutter="20" class="h-full flex">
      <!-- 左侧区域 -->
      <el-col :span="17" class="flex flex-col">
        <!-- 数据概览 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">数据概览</h3>
            <p class="update-time">
              更新时间：<span>{{ formattedDate }}</span>
            </p>
          </div>
          <el-row :gutter="20" class="mt-3">
            <el-col
              :span="6"
              v-for="(item, index) in statisticCards"
              :key="index"
            >
              <div class="statistic-card">
                <div class="card-title">{{ item.title }}</div>
                <div class="card-value">
                  {{ item.value }}
                  <span class="trend" :class="item.trend > 0 ? 'up' : 'down'">
                    {{ item.trend > 0 ? "+" : "" }}{{ item.trend }}%
                    <el-icon>
                      <CaretTop v-if="item.trend > 0" />
                      <CaretBottom v-else />
                    </el-icon>
                  </span>
                </div>
                <div class="card-desc">{{ item.desc }}</div>
              </div>
            </el-col>
          </el-row>
        </section>

        <!-- 工单分析 -->
        <section class="section-block">
          <div class="section-header">
            <div class="section-header-left">
              <h3 class="title">工单分析</h3>
              <div class="view-switcher ml-4">
                <el-radio-group v-model="viewType" size="small">
                  <el-radio-button label="department">按单位</el-radio-button>
                  <el-radio-button label="device">按设备类型</el-radio-button>
                </el-radio-group>
              </div>
            </div>
            <div class="section-header-right">
              <el-select
                v-model="timeRange"
                style="width: 120px"
                size="default"
              >
                <el-option
                  v-for="item in timeRangeOptions"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                />
              </el-select>
            </div>
          </div>

          <!-- 图例选择器 -->
          <!-- <div class="chart-legend mt-3">
    <el-checkbox-group v-model="selectedItems" @change="handleLegendChange">
      <el-checkbox 
        v-for="item in viewType === 'department' ? departments : deviceTypes"
        :key="item"
        :label="item"
      >
        {{ item }}
      </el-checkbox>
    </el-checkbox-group>
  </div> -->

          <!-- 图表 -->
          <Echarts
            id="workOrderChart"
            :full-options="workOrderOptions"
            height="380px"
            class="mt-3"
          />

          <!-- 数据统计卡片 -->
          <!-- <el-row :gutter="20" class="data-statistics mt-4">
    <el-col :span="8">
      <div class="stat-card">
        <div class="stat-title">{{ viewType === 'department' ? '最活跃单位' : '最常见设备类型' }}</div>
        <div class="stat-value">{{ viewType === 'department' ? '技术部' : '服务器' }}</div>
        <div class="stat-desc">占总工单量 35%</div>
      </div>
    </el-col>
    <el-col :span="8">
      <div class="stat-card">
        <div class="stat-title">平均处理时长</div>
        <div class="stat-value">4.5h</div>
        <div class="stat-desc">较上周提升 12%</div>
      </div>
    </el-col>
    <el-col :span="8">
      <div class="stat-card">
        <div class="stat-title">工单完成率</div>
        <div class="stat-value">92%</div>
        <div class="stat-desc">较上周提升 5%</div>
      </div>
    </el-col>
  </el-row> -->
        </section>

        <!-- 报障统计 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">报障统计</h3>
            <el-link type="primary" @click="showMoreTrouble">查看更多</el-link>
          </div>
          <el-table
            :data="troubleData"
            class="mt-3 custom-table"
            :row-style="{ height: '72px' }"
          >
            <el-table-column align="center" min-width="50">
              <template #header>
                <span class="table-header">排序</span>
              </template>
              <template #default="scope">
                <div
                  class="circle-index"
                  :class="scope.$index < 3 ? 'blue' : 'gray'"
                >
                  {{ scope.$index + 1 }}
                </div>
              </template>
            </el-table-column>
            <el-table-column
              v-for="col in troubleColumns"
              :key="col.prop"
              v-bind="col"
              align="center"
            >
              <template #header>
                <span class="table-header">{{ col.label }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" min-width="50" align="center">
              <template #header>
                <span class="table-header">操作</span>
              </template>
              <template #default="scope">
                <span class="detail-btn" @click="showTroubleDetail(scope.row)"
                  >详情</span
                >
              </template>
            </el-table-column>
          </el-table>
        </section>
      </el-col>

      <!-- 右侧区域 -->
      <el-col :span="7" class="flex flex-col">
        <!-- 常用功能 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">常用功能</h3>
          </div>
          <div class="function-buttons mt-3">
            <el-button
              v-for="func in commonFunctions"
              :key="func.name"
              size="large"
              color="#cccccc"
              style="color: #525252"
              @click="handleFunctionClick(func)"
            >
              <el-icon>
                <component :is="func.icon" />
              </el-icon>
              {{ func.name }}
            </el-button>
          </div>
        </section>

        <!-- 服务情况统计分析 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">服务情况统计分析</h3>
            <el-link type="primary" @click="showServiceDetails"
              >查看详情</el-link
            >
          </div>
          <div class="progress-list mt-3">
            <div
              class="progress-item"
              v-for="item in progressData"
              :key="item.label"
            >
              <div class="label label-text">{{ item.label }}</div>
              <el-progress
                :stroke-width="10"
                :percentage="item.percentage"
                :color="item.color"
              />
            </div>
          </div>
        </section>

        <!-- 故障类型统计分析 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">故障类型统计分析</h3>
            <el-select placeholder="本周" style="width: 80px">
              <el-option label="本周" value="week" />
            </el-select>
          </div>
          <Echarts
            id="faultTypeChart"
            :full-options="faultTypeOptions"
            height="300px"
          />
        </section>

        <!-- 操作日志 -->
        <section class="section-block">
          <div class="section-header">
            <h3 class="title">操作日志</h3>
            <el-link type="primary" @click="showMoreLogs">查看详情</el-link>
          </div>
          <el-table
            :data="operationLogs"
            class="mt-8 custom-table"
            :row-style="{ height: '40px' }"
          >
            <el-table-column
              v-for="col in operationColumns"
              :key="col.prop"
              v-bind="col"
              align="center"
            >
              <template #header>
                <span class="table-header">{{ col.label }}</span>
              </template>
            </el-table-column>
          </el-table>
        </section>
      </el-col>
    </el-row>

    <!-- 服务情况详情对话框 -->
    <el-dialog
      v-model="serviceDialogVisible"
      title="服务情况详细统计"
      width="800px"
    >
      <div class="service-statistics">
        <div class="statistics-header">
          <div class="header-title">服务情况统计</div>
          <div class="header-time">统计周期: {{ formattedDate }}</div>
          <el-select
            v-model="selectedSchool"
            placeholder="请选择学校"
            class="school-select"
            filterable
          >
            <el-option
              v-for="school in schoolList"
              :key="school"
              :label="school"
              :value="school"
            />
          </el-select>
        </div>

        <div class="statistics-overview">
          <div class="overview-item normal">
            <div class="item-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-value">{{ serviceDetails.total.normal }}</div>
              <div class="item-label">正常运行</div>
              <div class="item-stats">
                占比 {{ serviceDetails.total.normalPercent }}%
                <span class="trend up"
                  >+{{ serviceDetails.total.normalTrend }}%</span
                >
              </div>
            </div>
          </div>
          <div class="overview-item abnormal">
            <div class="item-icon">
              <el-icon><Warning /></el-icon>
            </div>
            <div class="item-content">
              <div class="item-value">{{ serviceDetails.total.abnormal }}</div>
              <div class="item-label">运行异常</div>
              <div class="item-stats">
                占比 {{ serviceDetails.total.abnormalPercent }}%
                <span class="trend down"
                  >{{ serviceDetails.total.abnormalTrend }}%</span
                >
              </div>
            </div>
          </div>
        </div>

        <el-table :data="serviceDetails.details" class="mt-4">
          <el-table-column prop="type" label="设备类型" />
          <el-table-column prop="normal" label="正常运行">
            <template #default="{ row }">
              <span class="text-success">{{ row.normal }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="abnormal" label="异常">
            <template #default="{ row }">
              <span class="text-danger">{{ row.abnormal }}</span>
            </template>
          </el-table-column>
        </el-table>
      </div>
    </el-dialog>

    <!-- 报障统计更多对话框 -->
    <el-dialog
      v-model="troubleDialogVisible"
      title="报障统计详情"
      width="800px"
    >
      <el-table :data="paginatedTroubleData">
        <el-table-column
          v-for="col in troubleColumns"
          :key="col.prop"
          v-bind="col"
        />
      </el-table>
      <template #footer>
        <div class="dialog-footer">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :total="moreTroubleData.length"
            :page-sizes="[5, 10, 20]"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </template>
    </el-dialog>

    <!-- 报障详情对话框 -->
    <el-dialog v-model="troubleDetailDialog" title="报障详细信息" width="600px">
      <template v-if="currentTroubleDetail">
        <el-descriptions :column="1" border>
          <el-descriptions-item label="报障名称" min-width="100px">
            {{ currentTroubleDetail.name }}
          </el-descriptions-item>
          <el-descriptions-item label="报障单位">
            {{ currentTroubleDetail.unit }}
          </el-descriptions-item>
          <el-descriptions-item label="报障人">
            {{ currentTroubleDetail.person }}
          </el-descriptions-item>
          <el-descriptions-item label="提交时间">
            {{ currentTroubleDetail.submittedTime }}
          </el-descriptions-item>
          <el-descriptions-item label="优先级">
            {{ currentTroubleDetail.priority }}
          </el-descriptions-item>
          <el-descriptions-item label="处理状态">
            {{ currentTroubleDetail.status }}
          </el-descriptions-item>
          <el-descriptions-item label="处理人">
            {{ currentTroubleDetail.assignee }}
          </el-descriptions-item>
          <el-descriptions-item label="预计耗时">
            {{ currentTroubleDetail.estimatedTime }}
          </el-descriptions-item>
          <el-descriptions-item label="故障描述">
            {{ currentTroubleDetail.description }}
          </el-descriptions-item>
          <el-descriptions-item label="解决方案">
            {{ currentTroubleDetail.solution }}
          </el-descriptions-item>
        </el-descriptions>
      </template>
    </el-dialog>

    <!-- 操作日志更多对话框 -->
    <el-dialog v-model="logDialogVisible" title="操作日志详情" width="700px">
      <el-table :data="operationLogs">
        <el-table-column
          v-for="col in operationColumns"
          :key="col.prop"
          v-bind="col"
        />
      </el-table>
    </el-dialog>
  </div>
</template>

<script setup>
import { ref } from "vue";
import { ElMessage } from "element-plus";
import {
  Document,
  Setting,
  Warning,
  DataLine,
  Tools,
  User,
  CaretTop,
  CaretBottom,
  Check,
} from "@element-plus/icons-vue";
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";

const router = useRouter();

const date = new Date();
const formattedDate = ref(
  date
    .toLocaleString("zh-CN", {
      year: "numeric",
      month: "2-digit",
      day: "2-digit",
      hour: "2-digit",
      minute: "2-digit",
      second: "2-digit",
      hour12: false,
    })
    .replace(/\//g, "-")
);

// 常用能数据
const commonFunctions = [
  { name: "工作台", icon: "Document", route: "/work" },
  { name: "备件管理", icon: "Setting", route: "/spareManage/spare" },
  {
    name: "工单中心",
    icon: "Warning",
    route: "/taskManage/taskCenter/todo?type=1",
  },
  { name: "运维日报", icon: "DataLine", route: "/taskManage/daily?type=1" },
  { name: "巡检管理", icon: "Tools", route: "/checkManage/plan" },
  { name: "投诉处理", icon: "User", route: "/repair/repairIndex" },
];

// 数据概览卡片数据
const statisticCards = ref([
  {
    title: "单位工单量",
    value: 596,
    trend: 19.2,
    desc: "与昨日相比差距95个",
  },
  {
    title: "设备工单量",
    value: 970,
    trend: 7.8,
    desc: "与昨日相比多出70个",
  },
  {
    title: "报障次数",
    value: 20,
    trend: -33.3,
    desc: "与昨日相比少了10次",
  },
  {
    title: "报障率",
    value: "30%",
    trend: -10,
    desc: "与昨日相比少了10%",
  },
]);

// 表格列配置
const troubleColumns = [
  { prop: "name", label: "报障名称", minWidth: 120 },
  { prop: "time", label: "最近报时间", minWidth: 160 },
  { prop: "unit", label: "报障单位", minWidth: 120 },
  { prop: "person", label: "报障人", minWidth: 100 },
  { prop: "todayService", label: "今日报障次数", minWidth: 120 },
  { prop: "totalService", label: "总次数", minWidth: 100 },
];

const operationColumns = [
  { prop: "operator", label: "操作人员", minWidth: 80 },
  { prop: "time", label: "操作时间", minWidth: 140 },
  { prop: "content", label: "操作内容", minWidth: 80 },
];

/*  */

// 时间范围
const timeRange = ref("7");

// 进度条数据
const progressData = [
  { label: "状况良好", percentage: 88, color: "#4095e5" },
  { label: "状况异常", percentage: 12, color: "#ff5c00" },
];

// 报障统计数据
const troubleData = ref([
  {
    name: "网络连接故障",
    time: "2024-11-18 16:00:09",
    unit: "广州市增城区派潭镇中心小学",
    person: "张宇明",
    todayService: 5,
    totalService: 28,
  },
  {
    name: "服务器异常",
    time: "2024-11-18 15:30:22",
    unit: "广州市花都区炭步第二小学",
    person: "李明亮",
    todayService: 3,
    totalService: 15,
  },
  {
    name: "设备离线",
    time: "2024-11-18 14:45:33",
    unit: "广州市花都区新雅街新雅小学",
    person: "王风",
    todayService: 4,
    totalService: 22,
  },
  {
    name: "系统响应缓慢",
    time: "2024-11-18 14:20:15",
    unit: "广州市增城区中新镇中心小学",
    person: "赵汝亮",
    todayService: 2,
    totalService: 18,
  },
  {
    name: "数据同步失败",
    time: "2024-11-18 13:55:40",
    unit: "广州市花都区花东镇七星小学",
    person: "刘星",
    todayService: 3,
    totalService: 12,
  },
]);

// 操作日志数据
const operationLogs = ref([
  {
    operator: "李华",
    time: "2024-11-18 15:20",
    content: "处理网络",
  },
  {
    operator: "王伟",
    time: "2024-11-18 15:15",
    content: "更新设备",
  },
  {
    operator: "张娜",
    time: "2024-11-18 15:10",
    content: "新建任务",
  },
  {
    operator: "刘洋",
    time: "2024-11-18 15:05",
    content: "解决异常",
  },
  {
    operator: "陈静",
    time: "2024-11-18 15:00",
    content: "提交报告",
  },
  {
    operator: "林婷",
    time: "2024-11-18 16:00",
    content: "维护设备",
  },
]);

// 对话框相关
const serviceDialogVisible = ref(false);
const troubleDialogVisible = ref(false);
const logDialogVisible = ref(false);
const troubleDetailDialog = ref(false);
const currentTroubleDetail = ref(null);

// 添加学校列表数据
const schoolList = [
  "广州市从化希贤小学",
  "广州增城外国语实验中学",
  "广州市增城中学高中部",
  "广州市从化区流溪小学",
  "广州市从化区温泉镇第二中心小学",
  "广州市从化区城郊中学",
  "广州市增城区新塘镇甘泉小学",
  "广东外语外贸大学从化实验中学",
  "广州市从化区太平镇第二中心小学",
  "广州市花都区秀全中学",
  "广州市花都区花东镇七星小学",
  "广州市增城区仙村镇中心小学",
  "广州市增城区永和中学",
  "广州市花都区新华中学",
  "广州市从化区鳌头镇第二中心小学",
  "广州市增城区石滩镇三江中学",
  "广州市从化区良口镇善施学校",
  "广州市增城区中新镇中心小学",
  "广州市花都区新雅街新雅小学",
  "广州市花都区秀全街乐泉小学",
  "广州市从化区雅居乐小学",
  "广州市花都区新雅街清㘵初级中学",
  "广州市花都区炭步第二小学",
  "广州市花都区骏威小学",
  "广州市从化区鳌头中学",
  "广州市增城区派潭镇中心小学",
  "广州市花都区空港学校",
  "广州市增城区石滩镇中心小学",
  "广州市增城区小楼镇中心小学",
  "广州市花都区育才学校",
];

// 首先声明生成随机数据的辅助函数
const generateRandomData = (base, variance) => {
  return Math.floor(base + (Math.random() - 0.5) * variance);
};

// 2. 然后声明生成学校数据的函数
const generateSchoolData = (schoolName) => {
  // 使用学校名称的哈希值作为随机种子,确保同一学校每次生成的数据相对稳定
  const seed = schoolName
    .split("")
    .reduce((acc, char) => acc + char.charCodeAt(0), 0);
  const random = () => (seed * Math.random()) % 1;

  // 调整基础数值，确保正常运行率在95%以上
  const normalBase = 180 + Math.floor(random() * 20); // 基础值提高到180-200之间
  const abnormalBase = 5 + Math.floor(random() * 4); // 异常值降低到5-8之间

  const total = normalBase + abnormalBase;
  const normalPercent = Math.floor((normalBase / total) * 100);
  const abnormalPercent = 100 - normalPercent;

  return {
    total: {
      normal: normalBase,
      abnormal: abnormalBase,
      normalPercent: normalPercent,
      abnormalPercent: abnormalPercent,
      normalTrend: Math.floor(random() * 3), // 降低波动范围到0-2
      abnormalTrend: -Math.floor(random() * 2), // 降低波动范围到0-1
    },
    details: [
      {
        type: "防火墙",
        normal: Math.floor(normalBase * 0.4),
        abnormal: Math.floor(abnormalBase * 0.3),
      },
      {
        type: "交换机",
        normal: Math.floor(normalBase * 0.35),
        abnormal: Math.floor(abnormalBase * 0.4),
      },
      {
        type: "智慧大屏",
        normal: Math.floor(normalBase * 0.25),
        abnormal: Math.floor(abnormalBase * 0.3),
      },
    ],
  };
};

// 3. 声明数据缓存
const schoolDataCache = new Map();

// 4. 声明获取学校数据的函数
const getSchoolData = (schoolName) => {
  if (schoolDataCache.has(schoolName)) {
    return schoolDataCache.get(schoolName);
  }

  const newData = generateSchoolData(schoolName);
  schoolDataCache.set(schoolName, newData);
  return newData;
};

// 5. 最后初始化响应式数据
const selectedSchool = ref(schoolList[0]);
const serviceDetails = ref(getSchoolData(schoolList[0]));

// 6. 监听学校选择变化
watch(selectedSchool, (newSchool) => {
  serviceDetails.value = getSchoolData(newSchool);
});

// 更多报障统计数据
const moreTroubleData = ref([
  ...troubleData.value,
  {
    name: "应用程序崩溃",
    time: "2024-11-18 13:30:18",
    unit: "广东外语外贸大学从化实验中学",
    person: "陈明",
    todayService: 2,
    totalService: 9,
  },
  {
    name: "硬件故障",
    time: "2024-11-18 13:15:45",
    unit: "广州市从化区流溪小学",
    person: "吴强",
    todayService: 1,
    totalService: 7,
  },
  {
    name: "网络中断",
    time: "2024-11-18 14:00:00",
    unit: "广州市花都区花东镇七星小学",
    person: "李华",
    todayService: 3,
    totalService: 12,
  },
  {
    name: "数据库异常",
    time: "2024-11-18 14:30:00",
    unit: "广州市增城区派潭镇中心小学",
    person: "张伟",
    todayService: 1,
    totalService: 8,
  },
  {
    name: "服务器宕机",
    time: "2024-11-18 15:00:00",
    unit: "广州市从化区雅居乐小学",
    person: "王刚",
    todayService: 2,
    totalService: 10,
  },
  {
    name: "系统更新失败",
    time: "2024-11-18 15:30:00",
    unit: "广东外语外贸大学从化实验中学",
    person: "刘洋",
    todayService: 1,
    totalService: 6,
  },
  {
    name: "权限配置错误",
    time: "2024-11-18 16:00:00",
    unit: "广州增城外国语实验中学",
    person: "赵丽",
    todayService: 2,
    totalService: 5,
  },
  {
    name: "API调用超时",
    time: "2024-11-18 16:30:00",
    unit: "广州市增城区小楼镇中心小学",
    person: "钱涛",
    todayService: 3,
    totalService: 11,
  },
  {
    name: "磁盘空间不足",
    time: "2024-11-18 17:00:00",
    unit: "广州市花都区育才学校",
    person: "孙磊",
    todayService: 1,
    totalService: 4,
  },
]);

// 固定的工单数据
const fixedWorkOrderData = {
  // 按区域的固定数据
  department: {
    花都区: {
      7: [55, 42, 58, 40, 56, 45, 52],
      15: [55, 42, 58, 40, 56, 45, 52, 38, 54, 60, 43, 57, 41, 55, 48],
      30: [
        55, 42, 58, 40, 56, 45, 52, 38, 54, 60, 43, 57, 41, 55, 48, 39, 58, 44,
        53, 37, 55, 61, 42, 56, 40, 54, 47, 59, 43, 52,
      ],
    },
    从化区: {
      7: [38, 52, 42, 57, 40, 54, 45],
      15: [38, 52, 42, 57, 40, 54, 45, 58, 39, 51, 44, 56, 41, 53, 47],
      30: [
        38, 52, 42, 57, 40, 54, 45, 58, 39, 51, 44, 56, 41, 53, 47, 59, 43, 55,
        38, 50, 46, 58, 40, 52, 45, 57, 41, 54, 48, 56,
      ],
    },
    增城区: {
      7: [52, 38, 48, 35, 51, 42, 55],
      15: [52, 38, 48, 35, 51, 42, 55, 37, 50, 44, 56, 39, 53, 45, 57],
      30: [
        52, 38, 48, 35, 51, 42, 55, 37, 50, 44, 56, 39, 53, 45, 57, 36, 49, 58,
        41, 54, 38, 52, 46, 57, 40, 53, 47, 58, 42, 55,
      ],
    },
  },

  // 按设备类型的定数据
  device: {
    防火墙: {
      7: [45, 58, 40, 52, 35, 48, 55],
      15: [45, 58, 40, 52, 35, 48, 55, 38, 50, 57, 42, 54, 36, 49, 56],
      30: [
        45, 58, 40, 52, 35, 48, 55, 38, 50, 57, 42, 54, 36, 49, 56, 39, 51, 58,
        43, 55, 37, 50, 57, 41, 53, 35, 48, 56, 42, 54,
      ],
    },
    交换机: {
      7: [52, 35, 47, 58, 42, 54, 38],
      15: [52, 35, 47, 58, 42, 54, 38, 50, 56, 40, 53, 37, 49, 55, 41],
      30: [
        52, 35, 47, 58, 42, 54, 38, 50, 56, 40, 53, 37, 49, 55, 41, 57, 43, 51,
        36, 48, 54, 39, 52, 57, 41, 53, 38, 50, 56, 42,
      ],
    },
    智慧大屏: {
      7: [48, 35, 51, 42, 55, 38, 52],
      15: [48, 35, 51, 42, 55, 38, 52, 37, 50, 57, 41, 53, 36, 49, 54],
      30: [
        48, 35, 51, 42, 55, 38, 52, 37, 50, 57, 41, 53, 36, 49, 54, 40, 52, 35,
        48, 56, 39, 51, 58, 43, 54, 37, 50, 55, 42, 53,
      ],
    },
  },
};

// 工单分析的时间范围选项
const timeRangeOptions = [
  { label: "最近7天", value: "7" },
  { label: "最近15天", value: "15" },
  { label: "最近30天", value: "30" },
];

// 部门列表
const departments = ["花都区", "从化区", "增城区"];

// 设备类型列表
const deviceTypes = ["防火墙", "交换机", "智慧大屏"];

// 生成模拟数据的��数
const generateMockData = (days, baseValue, fluctuation) => {
  return Array.from({ length: days }, () => {
    return Math.floor(baseValue + (Math.random() - 0.5) * fluctuation * 2);
  });
};

// 生成日期数组
const generateDateArray = (days) => {
  const dates = [];
  const today = new Date();
  for (let i = days - 1; i >= 0; i--) {
    const date = new Date(today);
    date.setDate(date.getDate() - i);
    dates.push(
      date.toLocaleDateString("zh-CN", { month: "2-digit", day: "2-digit" })
    );
  }
  return dates;
};

// 图表数据计算函数
/* const calculateChartData = (selectedDays) => {
  const days = parseInt(selectedDays);
  const dates = generateDateArray(days);

  // 生成各单位的工单数据
  const departmentData = departments.map(dept => ({
    name: dept,
    data: generateMockData(days, 50, 20)
  }));

  // 生成各类设备的工单数据
  const deviceData = deviceTypes.map(type => ({
    name: type,
    data: generateMockData(days, 40, 15)
  }));

  return {
    dates,
    departmentData,
    deviceData
  };
}; */

// 修改后的图表数据计算函数
const calculateChartData = (selectedDays) => {
  const days = parseInt(selectedDays);
  const dates = generateDateArray(days);

  // 获取对应类型和天数的固定数据
  const getDayData = (type, name, days) => {
    return fixedWorkOrderData[type][name][days];
  };

  // 生成各单位工单数据
  const departmentData = departments.map((dept) => ({
    name: dept,
    data: getDayData("department", dept, days),
  }));

  // 生成各类设备的工单数据
  const deviceData = deviceTypes.map((type) => ({
    name: type,
    data: getDayData("device", type, days),
  }));

  return {
    dates,
    departmentData,
    deviceData,
  };
};

// 工单分析图表配置
const workOrderOptions = ref({
  options: {
    tooltip: {
      trigger: "axis",
      backgroundColor: "#fff",
      borderRadius: 4,
      padding: [8, 12],
      textStyle: {
        color: "#666",
        fontSize: 12,
      },
      extraCssText:
        "box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);",
    },
    legend: {
      type: "scroll",
      bottom: 0,
      icon: "roundRect",
      itemWidth: 30,
      itemHeight: 4,
      itemGap: 20,
      itemStyle: {
        borderRadius: 10,
      },
      textStyle: {
        color: "#666",
        fontSize: 12,
        padding: [0, 0, 0, 8],
      },
    },
    grid: {
      top: 30,
      bottom: 60,
      left: "3%",
      right: "4%",
      containLabel: true,
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: generateDateArray(15),
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      splitLine: {
        lineStyle: {
          color: "#E2E8F0",
          type: "solid",
        },
      },
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
    },
    series: [],
  },
});

// 视图类型（单位/设备）
const viewType = ref("department"); // 'department' or 'device'

// 当前选中的图表项
const selectedItems = ref([]);

// 监听时间范围变化
watch(timeRange, (newValue) => {
  updateChartData(newValue);
});

// 监听视图类型变化
watch(viewType, () => {
  selectedItems.value = [];
  updateChartData(timeRange.value);
});

// 更新图表数据
const updateChartData = (days) => {
  const { dates, departmentData, deviceData } = calculateChartData(days);
  const currentData =
    viewType.value === "department" ? departmentData : deviceData;

  // 更新图表配置
  workOrderOptions.value.options.xAxis.data = dates;
  workOrderOptions.value.options.legend.data = currentData.map(
    (item) => item.name
  );

  // 生成系列数据
  const colors = [
    "#1976D2",
    "#2196F3",
    "#64B5F6",
    "#90CAF9",
    "#4CAF50",
    "#81C784",
    "#A5D6A7",
    "#C8E6C9",
  ];

  workOrderOptions.value.options.series = currentData.map((item, index) => ({
    name: item.name,
    type: "line",
    smooth: true,
    symbol: "circle",
    symbolSize: 4,
    showSymbol: false,
    data: item.data,
    itemStyle: {
      color: colors[index],
      borderColor: "#fff",
      borderWidth: 1,
    },
    lineStyle: {
      width: 2,
    },
    areaStyle: {
      color: {
        type: "linear",
        x: 0,
        y: 0,
        x2: 0,
        y2: 1,
        colorStops: [
          { offset: 0, color: `${colors[index]}20` },
          { offset: 1, color: `${colors[index]}05` },
        ],
      },
    },
  }));
};

// 初始化数据
onMounted(() => {
  updateChartData(timeRange.value);
});

// 故障类型计配置
const faultTypeOptions = ref({
  options: {
    tooltip: {
      trigger: "item",
      backgroundColor: "#fff",
      borderRadius: 4,
      padding: [8, 12],
      textStyle: {
        color: "#666",
        fontSize: 12,
      },
      extraCssText:
        "box-shadow: 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08);",
    },
    legend: {
      bottom: 0,
      icon: "roundRect",
      itemWidth: 30,
      itemHeight: 4,
      itemGap: 30,
      itemStyle: {
        borderRadius: 2,
      },
      textStyle: {
        color: "#666",
        fontSize: 10,
        padding: [0, 0, 0, 8],
      },
      data: ["备掉线", "系统故障", "数据错乱"],
    },
    series: [
      {
        type: "pie",
        radius: ["60%", "75%"],
        center: ["50%", "45%"],
        avoidLabelOverlap: false,
        label: { show: false },
        labelLine: { show: false },
        emphasis: {
          scale: false,
        },
        data: [
          {
            value: 75,
            name: "设备掉线",
            itemStyle: { color: "#1976D2" },
          },
          {
            value: 15,
            name: "系统故障",
            itemStyle: { color: "#2196F3" },
          },
          {
            value: 10,
            name: "数据错乱",
            itemStyle: { color: "#64B5F6" },
          },
        ],
      },
      {
        name: "中心文字",
        type: "pie",
        radius: ["0%", "55%"],
        center: ["50%", "45%"],
        label: {
          show: true,
          position: "center",
          formatter: ["{value|75%}", "{name|设备掉线故障占比}"].join("\n"),
          rich: {
            value: {
              fontSize: 24,
              fontWeight: "normal",
              color: "#333",
              lineHeight: 30,
            },
            name: {
              fontSize: 14,
              fontWeight: "normal",
              color: "#999",
              lineHeight: 20,
            },
          },
        },
        data: [{ value: 75, name: "设备掉线故障占比" }],
        itemStyle: {
          color: "transparent",
        },
      },
    ],
  },
});

// 功能按钮点击处理
const handleFunctionClick = (func) => {
  // ElMessage.success(`导航到${func.name}页面`);
  // 实际项目中可以使用路由导航
  router.push(func.route);
};

// 查看服务情况详情
const showServiceDetails = () => {
  serviceDialogVisible.value = true;
};

// 查看更多报障统计
const showMoreTrouble = () => {
  troubleDialogVisible.value = true;
};

// 查看报障详情
const showTroubleDetail = (row) => {
  currentTroubleDetail.value = {
    ...row,
    description:
      "详细故障描述：系统出现异常，导致部分功能无法正常使用。用户反馈页面加载缓慢，数据展示异常。",
    solution:
      "解决方案：1. 检查服务器负载情况\n2. 优化数据库查询\n3. 清理缓存\n4. 重启应用服务",
    status: "处理中",
    priority: "高",
    assignee: "技术支持组",
    submittedTime: row.time,
    estimatedTime: "2小时",
  };
  troubleDetailDialog.value = true;
};

// 查看更多操作日志
const showMoreLogs = () => {
  logDialogVisible.value = true;
};

// 分页相关
const currentPage = ref(1);
const pageSize = ref(5);

// 计算分页后的数据
const paginatedTroubleData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return moreTroubleData.value.slice(start, end);
});

// 处理每页显示数量变化
const handleSizeChange = (val) => {
  pageSize.value = val;
  currentPage.value = 1; // 重置到第一页
};

// 处理页码变化
const handleCurrentChange = (val) => {
  currentPage.value = val;
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f7fa;
  min-height: 100vh;
}

.section-block {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  .update-time {
    color: #808080;
  }

  .title {
    font-size: 16px;
    font-weight: bold;
    color: #303133;
    margin: 0;
  }
}

.statistic-card {
  background-color: #409eff;
  color: #fff;
  padding: 15px;
  border-radius: 4px;
  height: 120px;

  .card-title {
    font-size: 14px;
    opacity: 0.8;
  }

  .card-value {
    font-size: 24px;
    font-weight: bold;
    margin: 10px 0;
  }

  .card-desc {
    font-size: 12px;
    opacity: 0.8;
  }

  .trend {
    font-size: 14px;
    margin-left: 8px;

    &.up {
      color: #67c23a;
    }
    &.down {
      color: #f56c6c;
    }
  }
}

.function-buttons {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 10px;

  .el-button {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;

    &:active {
      background-color: #4095e5;
      color: #fff !important;
    }
  }
}

.progress-list {
  margin-top: 20px;
  .progress-item {
    margin-bottom: 15px;
    .label-text {
      margin-bottom: 10px;
      font-size: 14px;
      font-weight: 540;
      color: #333333;
    }
  }
}

.circle-index {
  width: 24px;
  height: 24px;
  line-height: 24px;
  border-radius: 50%;
  text-align: center;
  color: #fff;
  margin: 0 auto;

  &.blue {
    background-color: #409eff;
  }
  &.gray {
    background-color: #909399;
  }
}

:deep(.custom-table) {
  --el-table-border: none;
  --el-table-border-color: transparent;
  width: 100% !important;

  .el-scrollbar__wrap {
    max-width: 100%;
  }

  .el-table__header {
    background-color: transparent !important;
  }

  .el-table__header-wrapper {
    background-color: transparent !important;
  }

  th.el-table__cell {
    background-color: transparent !important;
    border-bottom: none;
    padding: 8px 0;
  }

  .el-table__row {
    height: 50px;

    td.el-table__cell {
      border: none;
    }

    &:not(:last-child) td.el-table__cell {
      border-bottom: 1px solid var(--el-border-color-lighter);
    }

    &:hover > td.el-table__cell {
      background-color: transparent !important;
    }
  }

  .table-header {
    font-size: 13px;
    color: #909399;
    font-weight: normal;
  }

  .detail-btn {
    padding: 0;
    margin: 0 -18px;
    display: block;
    text-align: center;
    cursor: pointer;
    color: #4095e5;
    &:hover {
      text-decoration: underline;
      color: #67aff2;
    }
  }
}

.mt-3 {
  margin-top: 12px;
}

.el-button + .el-button {
  margin-left: 0;
}

.el-row {
  display: flex;
  flex: 1;

  .el-col {
    display: flex;
    flex-direction: column;
  }
}

.section-block {
  background-color: #fff;
  border-radius: 4px;
  padding: 20px;
  margin-bottom: 20px;

  // 最后一个section-block占据剩余空间
  &:last-child {
    flex: 1;
    margin-bottom: 0;
  }
}

// 对话框样式
.el-dialog {
  .el-descriptions {
    margin: 20px 0;
  }

  .mx-1 {
    margin: 0 4px;
  }
}

// Echarts 相关
.echarts-container {
  width: 100%;
  height: 100%;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  &-left {
    display: flex;
    align-items: center;
  }

  .view-switcher {
    margin-left: 16px;
  }
}

.chart-legend {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  padding: 8px 0;

  :deep(.el-checkbox) {
    margin-right: 16px;
    margin-bottom: 8px;
  }
}

.data-statistics {
  .stat-card {
    background-color: #f8f9fa;
    border-radius: 8px;
    padding: 16px;

    .stat-title {
      color: #666;
      font-size: 14px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: bold;
      color: #333;
      margin: 8px 0;
    }

    .stat-desc {
      color: #999;
      font-size: 12px;
    }
  }
}
.mt-3 {
  margin-top: 12px;
}

.mt-4 {
  margin-top: 16px;
}

.ml-4 {
  margin-left: 16px;
}

.service-statistics {
  .statistics-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 20px;

    .header-title {
      font-size: 16px;
      font-weight: bold;
    }

    .header-time {
      color: #909399;
      font-size: 14px;
    }

    .school-select {
      width: 220px; // 增加选择框宽度以适应学校名称
    }
  }

  .statistics-overview {
    display: flex;
    gap: 20px;
    margin-bottom: 20px;

    .overview-item {
      flex: 1;
      display: flex;
      align-items: center;
      padding: 20px;
      border-radius: 8px;

      &.normal {
        background-color: #f0f9eb;
        .item-icon {
          background-color: #67c23a;
        }
      }

      &.abnormal {
        background-color: #fef0f0;
        .item-icon {
          background-color: #f56c6c;
        }
      }

      .item-icon {
        width: 48px;
        height: 48px;
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        margin-right: 16px;

        .el-icon {
          font-size: 24px;
          color: #fff;
        }
      }

      .item-content {
        .item-value {
          font-size: 24px;
          font-weight: bold;
          line-height: 1;
          margin-bottom: 8px;
        }

        .item-label {
          color: #606266;
          margin-bottom: 4px;
        }

        .item-stats {
          font-size: 12px;
          color: #909399;

          .trend {
            margin-left: 8px;
            &.up {
              color: #67c23a;
            }
            &.down {
              color: #f56c6c;
            }
          }
        }
      }
    }
  }

  .text-success {
    color: #67c23a;
  }

  .text-danger {
    color: #f56c6c;
  }
}
</style>
