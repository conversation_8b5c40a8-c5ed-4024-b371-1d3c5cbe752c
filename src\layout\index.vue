<template>
  <div :class="classObj" class="app-wrapper" :style="{ '--current-color': theme }">
    <div
      v-if="device === 'mobile' && sidebar.opened"
      class="drawer-bg"
      @click="handleClickOutside"
    />
    <div
      :class="{ hasTagsView: needTagsView, sidebarHide: sidebar.hide }"
      class="main-container"
    >
      <div :class="{ 'fixed-header': fixedHeader }">
        <navbar @setLayout="setLayout" @setStudent="setStudent" />
      </div>
      <div style="display: flex">
        <sidebar v-if="!sidebar.hide" class="sidebar-container" />
        <div class="flexMain">
          <tags-view v-if="needTagsView" />
          <app-main />
        </div>
      </div>

      <settings ref="settingRef" />
      <StudentNum ref="studentRef" />

      <div class="copy-footer" v-if="ifFooter">
        <div>
          <span>主办单位:广州市电化教育馆</span>
          <span>单位地址:广州市越秀区宝汉直街4号</span>
          <!-- <span>客户邮箱:<EMAIL></span>
          <span>技术支持:广州云天数据技术有限公司</span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useWindowSize } from "@vueuse/core";
import Sidebar from "./components/Sidebar/index.vue";
import { AppMain, Navbar, Settings, TagsView, StudentNum } from "./components";
import defaultSettings from "@/settings";

import useAppStore from "@/store/modules/app";
import useSettingsStore from "@/store/modules/settings";

const settingsStore = useSettingsStore();
const theme = computed(() => settingsStore.theme);
const sideTheme = computed(() => settingsStore.sideTheme);
const sidebar = computed(() => useAppStore().sidebar);
const device = computed(() => useAppStore().device);
const needTagsView = computed(() => settingsStore.tagsView);
const fixedHeader = computed(() => settingsStore.fixedHeader);
const ifFooter = computed(() => settingsStore.ifFooter);

const classObj = computed(() => ({
  hideSidebar: !sidebar.value.opened,
  openSidebar: sidebar.value.opened,
  withoutAnimation: sidebar.value.withoutAnimation,
  mobile: device.value === "mobile",
}));

const { width, height } = useWindowSize();
const WIDTH = 992; // refer to Bootstrap's responsive design

watchEffect(() => {
  // if (device.value === "mobile" && sidebar.value.opened) {
  //   useAppStore().closeSideBar({ withoutAnimation: false });
  // }
  // if (width.value - 1 < WIDTH) {
  //   useAppStore().toggleDevice("mobile");
  //   useAppStore().closeSideBar({ withoutAnimation: true });
  // } else {
  //   useAppStore().toggleDevice("desktop");
  // }
});

function handleClickOutside() {
  useAppStore().closeSideBar({ withoutAnimation: false });
}

const settingRef = ref(null);
const studentRef = ref(null);
function setLayout() {
  settingRef.value.openSetting();
}
function setStudent() {
  studentRef.value.openSetting();
}
</script>

<style lang="scss" scoped>
@import "@/assets/styles/mixin.scss";
@import "@/assets/styles/variables.module.scss";
.flexMain {
  /* 50= navbar  50  */
  min-height: calc(100vh - 50px);
  width: 100%;
  position: relative;
  overflow: hidden;
}

.navbar {
  // overflow: hidden;
  // border-bottom-left-radius: 10px;
  // border-bottom-right-radius: 10px;
}

.app-wrapper {
  @include clearfix;
  position: relative;
  height: 100%;
  width: 100%;

  &.mobile.openSidebar {
    position: fixed;
    top: 0;
  }
}

.drawer-bg {
  background: #000;
  opacity: 0.3;
  width: 100%;
  top: 0;
  height: 100%;
  position: absolute;
  z-index: 999;
}

.fixed-header {
  position: fixed;
  top: 0;
  right: 0;
  z-index: 9;
  width: calc(100% - #{$base-sidebar-width});
  transition: width 0.28s;
}

.hideSidebar .fixed-header {
  width: calc(100% - 54px);
}

.sidebarHide .fixed-header {
  width: 100%;
}

.mobile .fixed-header {
  width: 100%;
}

.copy-footer {
  background-color: #4095e5;
  color: white;
  font-size: 12px;
  display: flex;
  justify-content: center;
  padding: 9.5px;
  div {
    display: flex;
    gap: 30px;
  }
}
</style>
