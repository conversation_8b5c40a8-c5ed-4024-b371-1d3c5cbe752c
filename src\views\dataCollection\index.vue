<template>
    <div class="districtmain">
        <div class="item item1">
            <div class="item-opt">
                <div class="tit">校园卫生与健康</div>
                <div class="row"><span>杀毒消菌</span> 3次</div>
                <div class="row"><span>体温异常</span> 0人</div>
                <div class="row"><span>食堂卫生</span> 已排查</div>
            </div>
            <div class="item-opt">
                <div class="tit">预防传染病主题教育</div>
                <div class="row2"><span>3</span>次<br />本学期开展</div>
            </div>
        </div>
        <div class="item item2">
            <Echarts @setFontSize="setFontSize" id="bjlgjk" width="100%" height="100%" style="float: left;"
                :fullOptions="bjlgjkOption" :loading="false" />
        </div>
        <div class="item item3">
            <div class="tit">今日值班医生</div>
            <div class="row2">
                <div><span>王敏玲</span></div>
                <div>联系方式<br /><span style="margin-left: 0;">18865039342</span></div>
            </div>
            <!-- <img src="@/assets/images/doctor.jpg" class="doctor" /> -->
        </div>
        <div class="item">
            <Echarts @setFontSize="setFontSize" id="bylgjk" width="100%" height="100%" :fullOptions="bylgjkOption"
                :loading="false" />
        </div>
        <div class="item">
            <Echarts @setFontSize="setFontSize" id="ywslfrs" width="100%" height="100%" :fullOptions="ywslfrsOption"
                :loading="false" />
        </div>
        <div class="item">
            <Echarts @setFontSize="setFontSize" id="bygfcrb" width="100%" height="100%" style="float: left;"
                :fullOptions="bygfcrbOption" :loading="false" />

        </div>
        <div class="item item7">
            <Echarts @setFontSize="setFontSize" id="sbgzztzb" width="100%" height="100%" :fullOptions="sbgzztzbOption"
                :loading="false" />
        </div>
    </div>
</template>

<script setup name="Index">
import Echarts from '@/components/Echarts/index.vue';
import { onMounted, reactive, ref, getCurrentInstance } from 'vue';
import { transformSize } from '@/utils'

const { proxy } = getCurrentInstance()

/** 传染病监控 */
let bylgjkXData = ['1号', '2号', '3号', '4号', '5号', '6号', '7号', '8号', '9号', '10号']
let bylgjkSData = [0, 0, 0, 2, 0, 1, 0, 0, 1, 0, 0]

/** 医务室来访人数 */
let ywslfrsXData = ['1月', '2月', '3月', '4月', '5月', '6月', '7月', '8月', '9月', '10月']
let ywslfrsSData = [10, 14, 23, 10, 13, 15, 10, 20, 14, 10]

/** 校园传染病监控 */
let sbgzztzbXData = ['甲流', '新冠', '水痘', '诺如', '其他传染病']
let sbgzztzbSData = [3, 0, 0, 0, 2]

/** 本月高发传染病 */
let bygfcrbSData = [
    { name: '甲流', value: 3 },
    { name: '新冠', value: 0 },
    { name: '其他', value: 2 },
    { name: '诺如', value: 0 },
    { name: '水痘', value: 0 },
]

/** 年级传染病监控 */
let bjlgjkYData = ['初一', '初二', '初三', '高一', '高二', '高三']
let bjlgjkSData = [1, 0, 1, 2, 0, 1]

/** 注入 */
function injectOption() {
    proxy.bylgjk = bylgjkOption
    proxy.ywslfrs = ywslfrsOption
    proxy.sbgzztzb = sbgzztzbOption
    proxy.bygfcrb = bygfcrbOption
    proxy.bjlgjk = bjlgjkOption
}

/** 当月传染病监控 */
const bylgjkOption = reactive({
    options: {
        title: {
            text: '本月传染病监控',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '15%',
            left: '8%',
            right: '3%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'line',
                itemStyle: {
                    color: '#00ffff'
                },
                barMaxWidth: '30%'
            }
        ]
    },
    init: false
})
/** 医务室来访人数 */
const ywslfrsOption = reactive({
    options: {
        title: {
            text: '医务室来访人数',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '15%',
            left: '8%',
            right: '3%'
        },
        xAxis: {
            type: 'category',
            data: [],
            axisLabel: {
                color: '#fff'
            }
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [
            {
                data: [],
                type: 'line',
                itemStyle: {
                    color: '#ff69b4'
                },
                barMaxWidth: '30%'
            }
        ]
    },
    init: false
})
/** 校园传染病监控 */
const sbgzztzbOption = reactive({
    options: {
        title: {
            text: '校园传染病监控',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            right: '5%',
            textStyle: {
                color: '#fff'
            }
        },
        grid: {
            height: '55%',
            bottom: '15%',
            left: '5%',
            right: '3%'
        },
        xAxis: {
            type: 'category',
            axisLabel: {
                color: '#fff'
            },
            data: []
        },
        yAxis: {
            type: 'value',
            axisLabel: {
                color: '#fff'
            }
        },
        series: [{
            data: [],
            type: 'bar',
            itemStyle: {
                color: '#bd3124'
            },
            barMaxWidth: '30%'
        }]
    },
    init: false
})
/** 本月高发传染病 */
const bygfcrbOption = reactive({
    options: {
        title: {
            text: '本月高发传染病',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(25)
            },
            top: '3%',
            left: '1%'
        },
        tooltip: {
            trigger: 'item'
        },
        legend: {
            left: 'center',
            bottom: '5%',
            textStyle: {
                color: '#fff'
            }
        },
        series: [
            {
                name: '人数',
                type: 'pie',
                radius: '50%',
                color: ['#75bedc', '#ef6567', '#91cd77', '#f9c956', '#5470c6'],
                data: [],
                emphasis: {
                    itemStyle: {
                        shadowBlur: 10,
                        shadowOffsetX: 0,
                        shadowColor: 'rgba(0, 0, 0, 0.5)'
                    }
                }
            }
        ]
    },
    init: false
})
/** 年级传染病监控 */
const bjlgjkOption = reactive({
    options: {
        title: {
            text: '年级传染病监控',
            textStyle: {
                color: '#fff',
                fontSize: transformSize(22)
            },
            top: '2%',
            left: '1%'
        },
        tooltip: {
            trigger: 'axis',
            axisPointer: {
                type: 'shadow'
            }
        },
        grid: {
            left: '5%',
            right: '10%',
            bottom: '5%',
            height: '75%',
            containLabel: true
        },
        xAxis: {
            type: 'value',
            boundaryGap: [0, 0.01],
            axisLabel: {
                color: '#fff'
            },
            splitLine: { show: false },
        },
        yAxis: {
            type: 'category',
            axisLabel: {
                color: '#fff'
            },
            data: []
        },
        series: [
            {
                name: '发病人数',
                type: 'bar',
                barMaxWidth: '40%',
                data: [],
                itemStyle: {
                    color: '#fac858'
                }
            },
        ]
    },
    init: false
})

let opt1 = bylgjkOption.options
let opt2 = ywslfrsOption.options
let opt4 = sbgzztzbOption.options
let opt5 = bygfcrbOption.options
let opt6 = bjlgjkOption.options

injectOption()

function setDatas() {
    // 当月传染病监控
    opt1.series[0].data = bylgjkSData
    opt1.xAxis.data = bylgjkXData

    // 当月医务室来访人数
    opt2.series[0].data = ywslfrsSData
    opt2.xAxis.data = ywslfrsXData

    //校园传染病监控
    opt4.xAxis.data = sbgzztzbXData
    opt4.series[0].data = sbgzztzbSData

    //本月高发传染病
    opt5.series[0].data = bygfcrbSData

    //年级传染病监控
    opt6.yAxis.data = bjlgjkYData
    opt6.series[0].data = bjlgjkSData

}

const setFontSize = (id) => {
    proxy[id].options.title.textStyle.fontSize = transformSize(22)
    proxy[id].options.graphic ? proxy[id].options.graphic.style.fontSize = transformSize(25) : ''
}

onMounted(() => {
    setDatas()
})

</script>

<style lang="scss" scoped>
.districtmain {
    background-color: #000a7b;
    color: #fff;
    padding: 2.5vh 1vw 1.5vh;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    grid-template-rows: 25vh 26vh 33vh;
    grid-gap: 1.6vh 1vw;

    .item {
        border: 1px solid #fff;
    }

    .item1 {
        display: flex;

        .item-opt {
            // border: 1px solid red;
            flex: 1;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 1.8vh;
            font-weight: bold;

            .row {
                margin-left: 2vw;

                span {
                    display: inline-block;
                    margin-right: 1.5vw;
                    color: #eff81d;
                }
            }

            .row2 {
                margin-left: 5.2vw;

                span {
                    display: inline-block;
                    font-size: 2vw;
                    color: #eff81d;
                    margin-bottom: 1vh;
                }
            }
        }
    }

    .item3 {
        position: relative;
        font-weight: bold;
        display: flex;
        width: 100%;
        padding: 2vh 1vw;

        .row2 {
            width: 73%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            gap: 3vh;
            margin-top: 3vh;

            span {
                display: inline-block;
                font-size: 2vw;
                margin-left: 7vw;
                color: #eff81d;
            }
        }

        .doctor {
            width: 25%;
            height: 100%;
            flex: 1;
            border: 1px solid #fff;
        }
    }

    .item1,
    .item3 {
        .tit {
            position: absolute;
            font-size: 1.05vw;
            top: 4%;
            left: 3%;
        }
    }

    .item2 {
        grid-row-start: span 2;
    }

    .item7 {
        grid-column-start: 2;
        grid-column-end: 4;
    }
}
</style>
