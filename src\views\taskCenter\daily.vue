<template>
  <div class="taskCenter app-container">
    <div class="taskCenter-title">运维日报</div>

    <div class="taskCenter-header">
      <div class="taskCenter-header_left">
        待处理工单<span>{{ workOrderInfo.waitProcessTotal }}</span>
        <div
          class="handle"
          @click="
            goPath(
              `/taskManage/taskCenter/todo?status=1`,
              '点击待处理工单去处理'
            )
          "
        >
          去处理
        </div>
      </div>
      <div class="taskCenter-header_right">
        自系统上线以来，工单流水共{{ workOrderInfo.total }}件，已处理工单{{
          workOrderInfo.processingTotal
        }}件
        <span>{{ curDate }}</span>
      </div>
    </div>

    <div class="taskCenter-main">
      <div class="taskCenter-main_left">
        <div class="itemList">
          <div
            class="taskCenter-main_left-item"
            v-for="(item, index) in infoList"
            :key="item.name"
            @click="index == 0 ? '' : goPath(item.path)"
            :style="{ cursor: index == 0 ? '' : 'pointer' }"
          >
            <div class="top">
              <div class="top-left">
                {{ item.name }}
                <span>{{ item.count }}</span>
              </div>
              <img :class="`item-${index + 1}`" :src="item.icon" />
            </div>
            <div class="bottom" :class="[`${index == 0 ? 'noArrow' : ''}`]">
              <div
                class="range"
                :class="item.range > 0 ? 'up' : item.range < 0 ? 'low' : 'none'"
              >
                {{ Math.abs(item.range) }}%
              </div>
              <!-- {{ item.time }} -->
            </div>
          </div>
        </div>

        <div class="taskCenter-main_left-item echart line">
          <div class="taskCenter-subTitle bzsblb bzpdtj">
            <div class="tit">报障频度统计</div>
            <div class="bzpdtj-right">
              <el-radio-group
                v-model="curTab"
                size="small"
                @change="getWorkOrderFq"
              >
                <el-radio-button label="日（近7天）" :value="1" />
                <el-radio-button label="月（近半年）" :value="2" />
              </el-radio-group>
              <el-button
                type="warning"
                icon="upload"
                size="small"
                @click="handleExport"
                >导出报表</el-button
              >
            </div>
          </div>
          <div class="echart-container">
            <Echarts
              id="deviceFq"
              :fullOptions="lineOption"
              :loading="false"
              width="100%"
              height="100%"
            />
          </div>
        </div>

        <div class="taskCenter-main_left-item echart">
          <div class="taskCenter-subTitle bzsblb">
            <div class="tit">报障设备类别</div>
          </div>
          <div class="echart-container">
            <Echarts
              id="deviceType"
              :fullOptions="deviceTypeOption"
              :loading="false"
              width="100%"
              height="100%"
            />
          </div>
        </div>

        <div class="taskCenter-main_left-item echart">
          <div class="taskCenter-subTitle bzsbsynx">
            <div class="tit">报障设备使用年限</div>
          </div>
          <div class="echart-container">
            <Echarts
              id="deviceAge"
              :fullOptions="deviceAgeOption"
              :loading="false"
              width="100%"
              height="100%"
            />
          </div>
        </div>
      </div>
      <div class="taskCenter-main_right" v-loading="loadingTask">
        <div class="taskCenter-subTitle gdjl">
          <div class="tit">工单记录</div>

          <el-radio-group
            v-model="queryParams.days"
            size="small"
            @change="handleRadioChange"
          >
            <el-radio-button label="近7天" :value="7" />
            <el-radio-button label="近15天" :value="15" />
            <el-radio-button label="近30天" :value="30" />
          </el-radio-group>
        </div>
        <div class="taskCenter-main_right-list" v-if="taskList.length > 0">
          <div
            class="taskCenter-main_right-list_item"
            v-for="(item, index) in taskList"
            :key="index"
            @click="
              goPath(
                item.troubleStatus == 1
                  ? `taskCenter/todoTaskInfo?id=${item.troubleId}&isDaily=true&pageNum=${queryParams.pageNum}&tab=${queryParams.days}`
                  : `taskCenter/handleTaskInfo?id=${item.troubleId}&isDaily=true&pageNum=${queryParams.pageNum}&tab=${queryParams.days}`,
                '点击工单记录单个工单',
                item.troubleStatus
              )
            "
          >
            <div
              class="top"
              :class="item.troubleStatus == 1 ? 'unhandle' : 'handled'"
            >
              {{ item.workOrderCode }}（工单编号）
              <span>{{ item.troubleStatus == 1 ? "未处理" : "已处理" }}</span>
            </div>
            <div class="bottom">
              <el-tooltip
                class="box-item"
                effect="dark"
                :content="item.repairName"
                placement="bottom"
              >
                <div class="bottom-name">{{ item.repairName }}</div>
              </el-tooltip>
              <div class="bottom-time">{{ item.submittedTime }}</div>
            </div>
          </div>
          <pagination
            class="page"
            v-show="total > 0"
            :autoScroll="false"
            :total="total"
            :pageSizes="[5, 10, 20, 30, 50]"
            layout="total,prev,pager,next"
            v-model:page="queryParams.pageNum"
            v-model:limit="queryParams.pageSize"
            @pagination="handlePageChange"
          />
        </div>
        <div class="taskCenter-main_right-empty" v-else>
          <el-empty description="暂无记录" :image-size="100" />
        </div>
      </div>
    </div>

    <el-dialog
      class="custom-dialog"
      title="导出报表"
      v-model="dialogVisible"
      width="500"
      align-center
    >
      <el-form ref="exportRef" :model="exportInfo" :rules="rules">
        <el-form-item label="导出类型" prop="type">
          <el-radio-group
            v-model="exportInfo.range"
            @change="exportInfo.time = ''"
          >
            <el-radio label="日报表" :value="1" />
            <el-radio label="月报表" :value="2" />
          </el-radio-group>
        </el-form-item>
        <el-form-item label="导出时间范围" prop="time">
          <el-date-picker
            v-if="exportInfo.range == 1"
            type="daterange"
            v-model="exportInfo.time"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            :disabled-date="disabledDate"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            clearable
          />
          <el-date-picker
            v-if="exportInfo.range == 2"
            type="monthrange"
            v-model="exportInfo.time"
            :disabled-date="disabledDate"
            value-format="YYYY-MM"
            format="YYYY-MM"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            clearable
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click.stop="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click.stop="exportTable" v-throttle
          >导出</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="daily">
import {
  ref,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  reactive,
  toRefs,
  onActivated,
} from "vue";
import { useRoute, useRouter } from "vue-router";
import Echarts from "@/components/Echarts/index.vue";
import { downloadBlob, timeFormat, sendPointRequest } from "@/utils";
import {
  deviceMaintenanceList,
  troubleDaily,
  troubleFrequency,
  exportFrequency,
} from "@/api/mediaTeach/trouble";
import { addPointObj } from "@/utils/addPoint";
import { nextTick } from "process";

const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();

const state = reactive({
  addTimer: null,
  addSecond: 0,
  loadingTask: false,
  exportRef: null,
  curTab: 1,
  curDate: "",
  total: 8,
  exportInfo: {
    range: 1,
    time: "",
    startDate: "",
    endDate: "",
  },
  dialogVisible: false,
  queryParams: {
    pageNum: 1,
    pageSize: 8,
    troubleStatusList: [1, 2, 3],
    type: 2,
    days: 7,
    deviceCode: "",
    workOrderCode: "",
    nameAndPhone: "",
    channel: "",
  },
  workOrderInfo: {
    processingTotal: 0,
    waitProcessTotal: 0,
    total: 0,
  },
  infoList: [
    {
      name: "今日报障数",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-info_icon1.png",
      count: 0,
      range: 0,
      time: "自前一天以来",
      path: "taskCenter/claim",
    },
    {
      name: "今日处理数",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-info_icon2.png",
      count: 0,
      range: 0,
      time: "自前一天以来",
      path: `/taskManage/taskCenter/handled?time=${timeFormat(
        new Date().getTime(),
        "yyyy-mm-dd"
      )}`,
    },
    {
      name: "今日待处理",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-info_icon3.png",
      count: 0,
      range: 0,
      time: "自前一天以来",
      path: `/taskManage/taskCenter/todo?status=1&time=${timeFormat(
        new Date().getTime(),
        "yyyy-mm-dd"
      )}`,
    },
    {
      name: "待处理总数",
      icon: "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-info_icon4.png",
      count: 0,
      range: 0,
      time: "更新至 2024-12-10",
      path: `/taskManage/taskCenter/todo?status=1`,
    },
  ],
  taskList: [],
  rules: {
    time: [
      {
        required: true,
        type: "array",
        min: 1,
        message: "请选择导出时间范围",
        trigger: ["blur", "change"],
      },
    ],
  },
});

const {
  addTimer,
  addSecond,
  loadingTask,
  exportRef,
  rules,
  exportInfo,
  dialogVisible,
  curTab,
  total,
  queryParams,
  curDate,
  workOrderInfo,
  infoList,
  taskList,
} = toRefs(state);

const goPath = (path, text = "", status = undefined) => {
  if (!!text) {
    sendPointRequest({
      event: "Click",
      eventDescribe: text,
      content: status > -1 ? addPointObj[status].status : "",
      num: 1,
    });
  }
  router.push(path);
};

//报障频度统计折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: ["2025-02"],
      axisLabel: {
        color: "#8495b6",
        fontSize: "90%",
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      axisLabel: {
        color: "#8495b6",
        fontSize: "90%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "10%",
      left: "8%",
      height: "75%",
      right: "8%",
    },
    series: [
      {
        data: [5],
        type: "line",
        areaStyle: {
          color: "rgba(81, 131, 255, .2)",
        },
        itemStyle: {
          color: "#4095e5",
        },
        lineStyle: {
          color: "#4095e5",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
    ],
  },
});

// 左侧饼图配置
const deviceTypeOption = ref({
  options: {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}",
    },
    legend: {
      // orient: 'vertical',
      show: true,
      itemWidth: 15,
      itemHeight: 10,
    },
    series: [
      {
        name: "设备类别分布",
        type: "pie",
        radius: "55%",
        center: ["50%", "65%"],
        color: ["#4095e5", "#7ab5ed", "#b3d5f5", "#a3c9ed"],
        label: {
          show: true,
          position: "outside",
          formatter: "{c}台",
          fontSize: 13,
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 20,
        },
        data: [],
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetY: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  },
});

// 右侧饼图配置
const deviceAgeOption = ref({
  options: {
    tooltip: {
      trigger: "item",
      formatter: "{b}: {c}",
    },
    legend: { show: true },
    series: [
      {
        name: "设备类别分布",
        type: "pie",
        radius: ["45%", "60%"],
        center: ["50%", "60%"],
        color: ["#4095e5", "#7ab5ed", "#b2d4f4", "#e4ebf3"],
        label: {
          show: true,
          position: "outside",
          formatter: "{d}%",
          fontSize: 13,
          lineHeight: 20,
        },
        labelLine: {
          show: true,
          length: 10,
          length2: 20,
        },
        data: [],
        emphasis: {
          scale: false,
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetY: 0,
            shadowColor: "rgba(0, 0, 0, 0.5)",
          },
        },
      },
    ],
  },
});

const handlePageChange = () => {
  router.replace({
    query: {
      ...route.query,
      tab: queryParams.value.days,
      pageNum: queryParams.value.pageNum,
    },
  });
  getList();
};

const handleRadioChange = () => {
  queryParams.value.pageNum = 1;
  router.replace({
    query: {
      ...route.query,
      tab: queryParams.value.days,
      pageNum: queryParams.value.pageNum,
    },
  });
  getList();
};

const disabledDate = (date) => {
  return new Date() < date;
};

// 添加时间显示相关代码
const currentDate = ref("");
let timer = null;

const updateTime = () => {
  const now = new Date().getTime();
  curDate.value = timeFormat(now, "yyyy-mm-dd hh:MM:ss");
};

const handleExport = () => {
  state.exportInfo = {
    range: 1,
    time: "",
    startDate: "",
    endDate: "",
  };
  state.dialogVisible = true;
  nextTick(() => state.exportRef.resetFields());
};

// 导出功能实现
const exportTable = () => {
  state.exportRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      const { range, time } = state.exportInfo;
      let curMonth = new Date().getMonth(),
        endArr = time[1].split("-");
      let d = {
        range,
        startDate: range == 1 ? time[0] : `${time[0]}-01`,
        endDate: time[1],
      };
      if (range == 2) {
        d.endDate =
          curMonth + 1 == endArr[1] * 1
            ? `${timeFormat(new Date().getTime(), "yyyy-mm-dd")}`
            : `${timeFormat(
                new Date(endArr[0] * 1, endArr[1] * 1, 0).getTime(),
                "yyyy-mm-dd"
              )}`;
      }
      console.log("导出传参", d);
      exportFrequency(range, d.startDate, d.endDate)
        .then((res) => {
          downloadBlob(
            res,
            "application/vnd.ms-excel",
            `报障频度${state.exportInfo.range == 1 ? "日报表" : "月报表"}`
          );
          sendPointRequest({
            event: "Click",
            eventDescribe: "点击导出报表",
            content: "",
            num: 1,
          });
          state.dialogVisible = false;
        })
        .finally(() => proxy.$modal.closeLoading());
    }
  });
};

const getWorkOrderFq = () => {
  troubleFrequency({ range: state.curTab }).then((res) => {
    console.log("报障频度", res);
    if (res.data) {
      lineOption.value.options.xAxis.data = res.data.map(
        (item) => item.statisticsTime
      );
      lineOption.value.options.series[0].data = res.data.map(
        (item) => item.workOrderTotal
      );
    }
  });
};

// 获取工单统计数据
const getWorkOrderStats = () => {
  troubleDaily().then((resp) => {
    console.log("统计数据", resp);
    if (resp.data) {
      const {
        deviceAssetsTypeStatistics,
        deviceUseStatistics,
        workOrderStatistics,
        workOrderProcessingTotal,
        workOrderWaitProcessingTotal,
        workOrderTotal,
      } = resp.data;
      state.workOrderInfo.processingTotal = workOrderProcessingTotal || 0;
      state.workOrderInfo.waitProcessTotal = workOrderWaitProcessingTotal || 0;
      state.workOrderInfo.total = workOrderTotal || 0;

      if (deviceAssetsTypeStatistics) {
        deviceTypeOption.value.options.series[0].data =
          deviceAssetsTypeStatistics.map((item) => {
            return {
              name: item.assetsTypeName,
              value: item.num,
            };
          });
      }

      if (deviceUseStatistics) {
        deviceAgeOption.value.options.series[0].data = deviceUseStatistics.map(
          (item) => {
            return {
              name: item.time,
              value: item.num,
            };
          }
        );
      }

      if (workOrderStatistics) {
        let {
          processingIncrease,
          repairIncrease,
          waitProcessingIncrease,
          waitProcessingTotalIncrease,
          todayProcessing,
          todayRepair,
          todayWaitProcessing,
          todayWaitProcessingTotal,
        } = workOrderStatistics;
        let arr1 = [
            todayRepair,
            todayProcessing,
            todayWaitProcessing,
            todayWaitProcessingTotal,
          ],
          arr2 = [
            repairIncrease,
            processingIncrease,
            waitProcessingIncrease,
            waitProcessingTotalIncrease,
          ];
        state.infoList = state.infoList.map((item, index) => {
          item.count = arr1[index] || 0;
          item.range = arr2[index]?.replace("%", "") * 1 || 0;
          // console.log(arr1[index], arr2[index])
          return item;
        });
      }
    }
  });
};

const getList = () => {
  // 获取待处理工单数据
  state.loadingTask = true;
  console.log("工单传参", state.queryParams);
  deviceMaintenanceList(state.queryParams)
    .then((resp) => {
      console.log("工单列表", resp);
      state.taskList = [];
      if (resp.data) {
        const { maintenanceWebListVOList = [], total = 0 } = resp.data;
        state.taskList = maintenanceWebListVOList || [];
        state.total = total;
      }
    })
    .finally(() => (state.loadingTask = false));
};

// 初始化
onMounted(() => {
  addTimer.value = setInterval(() => {
    addSecond.value++;
  }, 1000);
  updateTime();
  timer = setInterval(updateTime, 1000);
  queryParams.value.days = route.query.tab * 1 || 7;
  queryParams.value.pageNum = route.query.pageNum * 1 || 1;
  console.log(
    "当前tab",
    route.query.tab,
    "当前页码",
    route.query.pageNum,
    "当前queryParams",
    queryParams.value
  );
  getWorkOrderFq();
  getWorkOrderStats(); // 获取工单统计数据
  getList();
});

onBeforeUnmount(() => {
  sendPointRequest({
    event: "PageView",
    eventDescribe: "浏览运维日报页面",
    content: "",
    num: addSecond.value,
  });
  clearInterval(addTimer.value);
  if (timer) {
    clearInterval(timer);
  }
});

function handleBack() {
  proxy.$tab.closeOpenPage("/work");
}
</script>

<style lang="scss" scoped>
.page {
  padding: 0 !important;
  margin: 0 0 5px;
}
.taskCenter {
  background-color: #f3f3f3;
  &-title {
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    font-size: 24px;
    color: #4095e5;
    font-weight: bold;
    gap: 0 10px;
    &::before {
      content: "";
      display: inline-block;
      width: 40px;
      height: 40px;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-title.png");
      background-size: 100% 100%;
    }
  }
  &-subTitle {
    display: flex;
    align-items: center;
    gap: 0 10px;
    font-size: 1vw;
    font-weight: bold;
    margin-bottom: 10px;
    justify-content: space-between;
    position: relative;
    .tit {
      display: flex;
      align-items: center;
      gap: 0 0.5vw;
      &::before {
        width: 2vw;
        height: 2vw;
        content: "";
        display: inline-block;
      }
    }
    &.gdjl .tit::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-title1.png");
      background-size: 100% 100%;
    }
    &.bzsblb .tit::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-title2.png");
      background-size: 100% 100%;
    }
    &.bzsbsynx .tit::before {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-title3.png");
      background-size: 100% 100%;
    }
    :deep(.el-radio-button--small) {
      .el-radio-button__inner {
        font-size: 0.9vw;
      }
    }
  }
  &-header {
    display: flex;
    justify-content: space-between;
    background-color: #fff;
    padding: 10px 12px;
    border-radius: 5px;
    &_left {
      display: flex;
      align-items: center;
      .handle {
        color: #4095e5;
        display: inline-block;
        padding: 3px 12px;
        border: 1px solid #4095e5;
        font-size: 12px;
        border-radius: 5px;
        margin-left: 10px;
        cursor: pointer;
      }
      span {
        display: inline-block;
        color: #ff3b30;
        padding-left: 5px;
        font-size: 18px;
        font-weight: bold;
      }
    }
    &_right {
      display: flex;
      align-items: center;
      span {
        display: inline-block;
        padding-left: 20px;
        font-size: 14px;
        color: #666666;
      }
    }
  }
  &-main {
    margin-top: 15px;
    display: flex;
    gap: 0 12px;
    &_left {
      flex: 3;
      display: grid;
      grid-template-columns: 24.5% 36% 36%;
      column-gap: 15px;
      row-gap: 15px;
      // flex-wrap: wrap;
      // gap: 15px;
      .itemList {
        grid-row-start: 1;
        grid-row-end: 3;
        display: flex;
        flex-direction: column;
        gap: 15px 0;
      }
      &-item {
        // width: 49%;
        background-color: #fff;
        border-radius: 5px;
        padding: 15px;
        // cursor: pointer;
        &.line {
          grid-column-start: 2;
          grid-column-end: 4;
        }
        .bzpdtj {
          &-right {
            display: flex;
            align-items: center;
            gap: 0 10px;
          }
        }
        .top {
          font-size: 13px;
          display: flex;
          align-items: center;
          justify-content: space-between;
          span {
            display: block;
            font-size: 24px;
            font-weight: bold;
            margin-top: 10px;
          }
          .item-1,
          .item-2 {
            width: 75px;
            height: 50px;
          }
          .item-3,
          .item-4 {
            width: 50px;
            height: 50px;
          }
        }
        .bottom {
          color: #666;
          display: flex;
          align-items: center;
          gap: 0 10px;
          margin-top: 48px;
          position: relative;
          &::after {
            position: absolute;
            content: "";
            right: 0;
            width: 20px;
            height: 20px;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-arrow_right.png");
            background-size: 100% 100%;
          }
          &.noArrow::after {
            background: none;
          }
          .range {
            font-size: 13px;
            padding: 2px 8px 2px 20px;
            color: #d54941;
            background-color: #fff0ed;
            position: relative;
            &::before {
              position: absolute;
              left: 5px;
              content: "";
              display: inline-block;
              width: 13px;
              height: 14px;
              background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-up.png");
              background-size: 100% 100%;
            }
            &.low {
              background-color: #e3f9e9;
              color: #2ba471;
              &::before {
                background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-down.png");
                background-size: 100% 100%;
              }
            }

            &.none {
              background-color: #eee;
              color: gray;
              &::before {
                left: 10px;
                content: "-";
                background: none;
              }
            }
          }
        }
      }
    }
    &_right {
      flex: 1;
      background-color: #fff;
      padding: 10px;
      border-radius: 5px;
      &-list {
        display: flex;
        flex-direction: column;
        gap: 10px 0;
        margin-top: 15px;
        &_item {
          border: 1px solid #4095e5;
          padding: 6px 10px;
          border-radius: 5px;
          cursor: pointer;
          .top {
            display: flex;
            justify-content: space-between;
            span {
              color: #ff3b30;
              &::before {
                content: "";
                display: inline-block;
                margin-right: 3px;
                border-radius: 50%;
                width: 10px;
                height: 10px;
                background-color: #ff3b30;
              }
            }
            &.handled {
              span {
                color: #34c759;
                &::before {
                  background-color: #34c759;
                }
              }
            }
          }
          .bottom {
            display: flex;
            gap: 0 0.5vw;
            margin-top: 1vw;
            &-name {
              position: relative;
              gap: 0 0.5vw;
              width: 7vw;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              padding-left: 1.5vw;
              line-height: 1;
              &::before {
                position: absolute;
                display: inline-block;
                width: 1vw;
                height: 1vw;
                content: "";
                left: 0;
                background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-name.png");
                background-size: 100% 100%;
              }
            }
            &-time {
              // display: flex;
              // align-items: center;
              position: relative;
              gap: 0 0.5vw;
              font-size: 1vw;
              width: 11vw;
              text-overflow: ellipsis;
              overflow: hidden;
              white-space: nowrap;
              padding-left: 1.5vw;
              // line-height: 1;
              &::before {
                position: absolute;
                left: 0;
                display: inline-block;
                width: 1vw;
                height: 1vw;
                content: "";
                background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/icons/daily-date.png");
                background-size: 100% 100%;
              }
            }
          }
        }
      }

      &-empty {
        height: 500px;
        display: flex;
        justify-content: center;
        align-items: center;
      }
    }
  }
}

/* 图标容器 */
.echart-container {
  height: 250px;
  padding-top: 20px;
  margin-top: 15px;
  border-top: 1px solid #eee;
}
</style>