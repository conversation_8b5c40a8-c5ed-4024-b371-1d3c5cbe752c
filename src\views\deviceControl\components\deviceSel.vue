<template>
  <div class="deviceSel">
    <span v-if="!isEdit">{{
      props.deviceCodes?.length > 0 && deviceTotal == props.deviceCodes?.length
        ? "全选"
        : `${props.deviceCodes?.length}台`
    }}</span>
    <el-popover placement="bottom-start" :width="700" trigger="click">
      <template #reference>
        <el-button
          v-if="!isEdit"
          size="small"
          type="primary"
          :disabled="props.btnDisabled"
          >查看已选设备</el-button
        >
        <el-input
          v-else
          class="deviceSel-input"
          v-model="selectNames"
          style="width: 490px"
          placeholder="请选择生效设备"
          readonly
          :disabled="props.disabled"
          suffix-icon="ArrowDown"
        />
      </template>
      <div class="deviceSel-main">
        <div class="deviceSel-item">
          <el-form
            class="search-list"
            :model="queryParams"
            ref="queryRef"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="name">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入位置名称搜索"
                clearable
                size="small"
                style="width: 150px"
                @keyup.enter="handleQuery()"
                @change="handleQuery()"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                icon="Search"
                @click="handleQuery()"
                >搜索</el-button
              >
              <el-button icon="Refresh" size="small" @click="resetQuery()"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <el-scrollbar
            class="scrollbar"
            :style="{ height: '290px' }"
            v-loading="loading"
          >
            <el-tree
              style="width: 100%"
              :data="positionTreeList"
              :show-checkbox="isEdit"
              node-key="id"
              expand-on-click-node
              :default-expanded-keys="defaultExpandedKeys"
              :indent="10"
              :props="positionProps"
              ref="treeRef"
              @check="handleNodeCheck"
            />
          </el-scrollbar>
        </div>
        <div class="deviceSel-item" style="border-left: none">
          <el-form
            class="search-list"
            :model="queryParams2"
            ref="queryRef2"
            :inline="true"
            @submit.native.prevent
          >
            <el-form-item label="" prop="name">
              <el-input
                v-model="queryParams2.name"
                placeholder="请输入关键词搜索"
                clearable
                size="small"
                style="width: 150px"
                @keyup.enter="handleQuery(1)"
                @change="handleQuery(1)"
              />
            </el-form-item>
            <el-form-item>
              <el-button
                type="primary"
                size="small"
                icon="Search"
                @click="handleQuery(1)"
                >搜索</el-button
              >
              <el-button icon="Refresh" size="small" @click="resetQuery(1)"
                >重置</el-button
              >
            </el-form-item>
          </el-form>
          <div class="deviceSel-total">
            已选<span style="color: #4095e5; margin: 0 5px">{{
              selectList_all?.length
            }}</span
            >台设备
          </div>
          <el-scrollbar
            class="scrollbar"
            :style="{ height: '250px' }"
            v-loading="loading2"
          >
            <div
              class="deviceSel-item_opt"
              v-for="item in selectList"
              :key="item.id"
            >
              <span>{{ item.installAddressName }}</span>
              <el-icon v-if="isEdit" :size="14" @click="handleDel(item)">
                <CircleClose />
              </el-icon>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup>
import { getPositionTree } from "@/api/mediaTeach/position";
import { treeToArray, extractPropertyFromTree, treeFindPath } from "@/utils";
import { getCurrentInstance, onMounted } from "vue";
import { devicePage } from "@/api/mediaTeach/ledger";

const emits = defineEmits(["change", "valid"]);
const props = defineProps({
  maxHeight: {
    type: String,
    default: "300px",
  },
  isEdit: {
    type: Boolean,
    default: true,
  },
  btnDisabled: {
    type: Boolean,
    default: false,
  },
  disabled: {
    type: Boolean,
    default: false,
  },
  deviceCodes: {
    type: Array,
    default: [],
  },
  selectedAllCodes: {
    type: Array,
    default: [],
  },
});

const { proxy } = getCurrentInstance();

const state = reactive({
  deviceTotal: 0,
  defaultExpandedKeys: [],
  disabledCodes: [],
  selectedKeys: [],
  originKeys: [],
  treeRef: null,
  loading: false,
  loading2: false,
  selectList: [],
  selectList_all: [],
  positionList: [],
  positionTreeList: [],
  positionNodeList: [],
  positionTreeList_all: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  queryRef: null,
  queryRef2: null,
  queryParams: {
    name: "",
    isDevice: 1,
    // isDeviceTree: 1,
  },
  queryParams2: {
    name: "",
  },
});

const {
  deviceTotal,
  defaultExpandedKeys,
  disabledCodes,
  selectList_all,
  selectedKeys,
  positionList,
  positionNodeList,
  positionTreeList_all,
  originKeys,
  treeRef,
  loading,
  loading2,
  selectList,
  positionTreeList,
  positionProps,
  queryRef,
  queryRef2,
  queryParams,
  queryParams2,
} = toRefs(state);

const selectNames = computed(() => {
  const arr = selectList_all.value.map((item) => item.installAddressName);
  props.isEdit ? emits("change", selectedKeys.value) : "";
  return arr?.length > 0 && arr?.length == state.deviceTotal
    ? "全选"
    : arr.join("、");
});

watch(
  () => [props.isEdit, selectNames],
  ([newEdit, newNames], [oldEdit, oldNames]) => {
    // console.log(newEdit, newNames, oldEdit, oldNames);
    if (newEdit != oldEdit) handleShow();
    if (newEdit == oldEdit) props.isEdit ? emits("valid") : "";
  }
);

onMounted(() => {
  getDeviceTotal();
  handleShow();
});

const getDeviceTotal = async () => {
  try {
    const deviceRes = await devicePage({
      abnormalInterruptionChannel: 0,
      tenantId: "",
      deviceName: "",
      model: "",
      deviceStatus: "",
      positionIds: [],
      putTime: "",
      status: [],
      current: 1,
      size: 9999999,
      typeId: 1,
      smartScreen: 1,
    });
    console.log("所有台账数据", deviceRes);
    state.deviceTotal = deviceRes.data?.page?.total || 0;
  } catch (e) {
    console.log(e);
  }
};

const handleShow = async () => {
  try {
    selectedKeys.value = JSON.parse(JSON.stringify(props.deviceCodes)) || [];
    await getPositionList();
    getSelectedList();
  } catch (e) {
    console.log(e);
  }
};

const handleDel = async (item) => {
  try {
    selectList_all.value = selectList_all.value.filter((i) => i !== item);
    selectedKeys.value = selectList_all.value.map((i) => i.id);
    console.log(item, selectList_all.value, selectedKeys.value);
    await getPositionList();
    getSelectedList();
  } catch (e) {
    console.log(e);
  }
};

const getSelectedList = () => {
  if (queryParams2.value.name) {
    state.selectList = state.selectList_all.filter((item) => {
      return item.installAddressName.includes(queryParams2.value.name);
    });
  } else {
    state.selectList = [...state.selectList_all];
  }
  loading2.value = false;
};

const getPositionList = async () => {
  loading.value = true;
  if (props.deviceCodes?.length > 0 && !props.isEdit) {
    queryParams.value.isDeviceTree = 1;
    queryParams.value.allChildren = 1;
    queryParams.value.deviceCodes = JSON.parse(
      JSON.stringify(props.deviceCodes)
    );
    !!queryParams.value.name
      ? (queryParams.value.children = 1)
      : delete queryParams.value.children;
  } else {
    delete queryParams.value.deviceCodes;
    !!queryParams.value.name
      ? ((queryParams.value.children = 1),
        (queryParams.value.isDeviceTree = 1),
        (queryParams.value.allChildren = 1))
      : (delete queryParams.value.children,
        delete queryParams.value.isDeviceTree,
        delete queryParams.value.allChildren);
  }

  try {
    console.log("搜索位置树传参", queryParams.value);
    await getPositionTree(queryParams.value).then((response) => {
      console.log("搜索的位置树结果", response);
      if (queryParams.value.name === "") {
        positionTreeList_all.value = JSON.parse(JSON.stringify(response.data));
        positionNodeList.value = [];
      }

      state.disabledCodes = props.selectedAllCodes.filter(
        (_) => !props.deviceCodes?.includes(_)
      );
      // console.log("禁用的设备", state.disabledCodes);
      positionList.value = treeToArray(response.data);
      state.defaultExpandedKeys = [];
      positionTreeList.value = transTree2(
        JSON.parse(JSON.stringify(response.data))
      );
      treeRef.value?.setCheckedKeys(selectedKeys.value);
      console.log("组合后的位置树", positionTreeList.value);
      selectList_all.value = [];
      selectedKeys.value.map((item) => {
        let obj = positionNodeList.value.find((_) => _.id == item);
        if (obj) {
          selectList_all.value?.push({
            installAddressName:
              (obj?.installAddress ? obj?.installAddress + "-" : "") + obj?.id,
            positionIds: obj.positionIds,
            id: obj.id,
            name: obj.name,
          });
        }
      });
      nextTick(() => {
        originKeys.value = treeRef.value
          ?.getCheckedKeys()
          .filter((_) => String(_).includes("ZHDP"));
        // console.log("originKeys", originKeys.value, selectedKeys.value);
      });
    });
  } catch (e) {
    console.log(e);
  }
  loading.value = false;
};

const transTree2 = (data) => {
  for (let i = 0; i < data.length; i++) {
    if (data[i].children && data[i].children?.length > 0) {
      data[i].children = transTree2(data[i].children);
    }
    if (data[i].deviceCodes && data[i].deviceCodes?.length > 0) {
      let ids = treeFindPath(
        positionTreeList_all.value,
        (d) => d.id == data[i].id
      );
      if (!queryParams.value.name && !props.isEdit) {
        let ids2 = [...ids];
        state.defaultExpandedKeys = state.defaultExpandedKeys.concat(ids2);
        state.defaultExpandedKeys = [...new Set(state.defaultExpandedKeys)];
        console.log(state.defaultExpandedKeys);
      }
      const arr = ids;
      let names = [];
      for (let j = 0; j < arr?.length; j++) {
        let name =
          positionList.value.find((node) => node.id == arr[j])?.name || "";
        name ? (names[j] = name) : "";
      }
      let arr2 = data[i].deviceCodes?.map((item) => {
        let obj = {
          positionIds: ids?.join(",") || "",
          installAddress: names?.join("-") || "",
          parentId: data[i].id,
          id: item,
          name: item,
          disabled: state.disabledCodes?.includes(item),
        };
        positionNodeList.value.push(obj);
        return obj;
      });
      data[i].children = arr2?.concat(data[i].children || []);
    }
    data[i].disabled =
      data[i].status == 1 || data[i].children?.every((_) => !!_.disabled);
  }
  return data;
};

// 处理节点勾选事件
const handleNodeCheck = (node, checkedStatus) => {
  console.log(node, checkedStatus);
  const { checkedNodes } = checkedStatus;
  let checkedKeys = checkedNodes
    .filter((_) => String(_.id).includes("ZHDP"))
    .map((item) => item.id);
  // console.log(checkedKeys, originKeys.value, "checkedKeys");

  const addArr = checkedKeys.filter((_) => !originKeys.value.includes(_));
  const delArr = originKeys.value.filter((_) => !checkedKeys.includes(_));

  let newSelected = selectedKeys.value.filter((_) => !delArr.includes(_));
  newSelected = newSelected.concat(addArr);

  selectedKeys.value = newSelected;
  loading2.value = true;
  selectList_all.value = [];
  selectedKeys.value.map((item) => {
    let obj = positionNodeList.value.find((_) => _.id == item);
    if (obj) {
      selectList_all.value?.push({
        installAddressName:
          (obj?.installAddress ? obj?.installAddress + "-" : "") + obj?.id,
        positionIds: obj.positionIds,
        id: obj.id,
        name: obj.name,
      });
    }
  });
  nextTick(() => {
    originKeys.value = treeRef.value
      ?.getCheckedKeys()
      .filter((_) => String(_).includes("ZHDP"));
    getSelectedList();
  });

  // console.log(addArr, delArr, newSelected, "新增集合、删除集合、合成集合");
};

/** 搜索按钮操作 */
const handleQuery = (type = 0) => {
  !!type ? getSelectedList() : getPositionList();
};

/** 重置按钮操作 */
const resetQuery = (type = 0) => {
  proxy.resetForm(!!type ? "queryRef2" : "queryRef");
  if (!!type) {
    state.queryParams2 = {
      name: "",
    };
  } else {
    state.queryParams = {
      name: "",
      isDevice: 1,
      // isDeviceTree: 1,
    };
  }

  handleQuery(type);
};
</script>

<style lang="scss" scoped>
.deviceSel {
  font-size: 12px;
  display: flex;
  align-items: center;
  gap: 0 10px;

  &-main {
    display: flex;
  }

  &-input {
    :deep(.el-input__wrapper),
    :deep(.el-input__inner) {
      cursor: pointer !important;
    }
  }

  &-total {
    padding-bottom: 10px;
    border-bottom: 1px solid #ccc;
    margin-bottom: 10px;
  }

  &-item {
    flex: 1;
    border: 1px solid #ccc;
    padding: 10px;
    .search-list {
      // width: 350px;
    }
    .scrollbar {
      padding-right: 20px;
    }
    &_opt {
      display: flex;
      align-items: center;
      justify-content: space-between;
      margin-bottom: 5px;
      &:hover {
        color: #4095e5;
      }
      .el-icon {
        cursor: pointer;
      }
    }
  }
}
</style>