import request from "@/utils/request";

// 更改集控按钮设置
export function setControlBtn(params) {
  const { configKey, configValue } = params;
  return request({
    url: `/system/config/updateConfigValue?configKey=${configKey}&configValue=${configValue}`,
    method: "put",
  });
}

// 获取集控按钮设置
export function getControlBtn(params) {
  return request({
    url: "/system/config/configKey/button_control",
    method: "get",
    params,
  });
}

// 获取正在穿透安装/下发设备
export function penetrateDevice(params) {
  return request({
    url: "/system/schoolSoftware/find/penetrate/device",
    method: "get",
    params,
  });
}

// 批量锁屏规则设置
export function lockScreenRuleBatch(data) {
  return request({
    url: "/system/device/lockScreenRule/batch",
    method: "post",
    data,
  });
}

// 设置今日不提醒
export function skipToday(data) {
  return request({
    url: "/system/device/skip/today",
    method: "post",
    data,
  });
}

// 查询是否需要提醒
export function checkRemind() {
  return request({
    url: "/system/device/should/remind",
    method: "get",
  });
}

// 查询视频流情况
export function checkDeviceOnline(params) {
  return request({
    url: "/system/edgeCtl/checkDeviceOnline",
    method: "get",
    params,
    noMsg: true,
  });
}

// 查询机构点位信息详情
export function getPointInfoDetail(data) {
  return request({
    url: "/system/deviceMachine/getPointInfoDetail",
    method: "post",
    data,
  });
}

// 查询机构点位最近到期时间
export function getLatestExpirationInfo(data) {
  return request({
    url: "/system/deviceMachine/getLatestExpirationInfo",
    method: "post",
    data,
  });
}

// 分页查询设备机器码
export function deleteMachineCode(data) {
  return request({
    url: "/system/deviceMachine/deleteMachineCode",
    method: "post",
    data,
  });
}

// 激活机器码设备
export function deviceMachineActivate(data) {
  return request({
    url: "/system/deviceMachine/activate",
    method: "post",
    data,
  });
}

// 分页查询设备机器码
export function deviceMachineList(data) {
  return request({
    url: "/system/deviceMachine/list",
    method: "post",
    data,
  });
}

// 查询机构点位信息
export function getPointInfo(data) {
  return request({
    url: "/system/deviceMachine/getPointInfo",
    method: "post",
    data,
  });
}

// mqtt静默安装
export function installationSoftwareMqtt(data) {
  return request({
    url: "/system/schoolSoftware/mqttOrHttp/installationSoftware",
    method: "post",
    data,
  });
}

// 发布消息mqtt
export function sendMessageMqtt(data) {
  return request({
    url: "/system/device/sendMessageMqtt",
    method: "post",
    data,
  });
}

// mqtt截屏
export function addByMqtt(data) {
  return request({
    url: "/system/schoolScreenshot/addByMqtt",
    method: "post",
    data,
  });
}

// 锁屏屏保设置默认（删除屏保）
export function deleteScreenImg() {
  return request({
    url: "/system/schoolSoftware/deleteScreenImg",
    method: "delete",
  });
}

// 锁屏屏保回显
export function downScreenImg() {
  return request({
    url: "/system/schoolSoftware/downScreenImg",
    method: "get",
    responseType: "blob",
  });
}

// 锁屏规则回显
export function getLockScreenRule(params) {
  return request({
    url: "/system/device/getLockScreenRule",
    method: "get",
    params,
  });
}

// 锁屏规则设置
export function lockScreenRule(data) {
  return request({
    url: "/system/device/lockScreenRule",
    method: "post",
    data,
  });
}

// 批量远程操作
export function deviceCtlMqtt(data) {
  return request({
    url: "/system/edgeCtl/deviceCtlMqtt",
    method: "post",
    timeout: 10000,
    data,
  });
}

// 查询阿里云上传参数
export function getStsToken() {
  return request({
    url: "/system/schoolSoftware/getPostToken",
    method: "get",
  });
}

// 软件上传
export function uploadSoftware(data) {
  return request({
    url: "/system/schoolSoftware/software/upload",
    method: "post",
    data,
  });
}

// 查询阿里云上传参数
export function getStsToken2() {
  return request({
    url: "/system/schoolSoftware/getStsToken",
    method: "get",
  });
}

// 发布消息
export function sendMessage(data) {
  return request({
    url: "/system/device/sendMessage",
    method: "post",
    data,
  });
}

// 远程操作
export function deviceCtl(data) {
  return request({
    url: "/system/edgeCtl/deviceCtl",
    method: "post",
    timeout: 10000,
    data,
  });
}

// 查询日志列表 /system/schoolDeviceLog
export function getSchoolDeviceLogList(params) {
  return request({
    url: "/system/schoolDeviceLog",
    method: "get",
    params,
  });
}

// 添加日志记录 /system/schoolDeviceLog
export function addSchoolDeviceLog(data) {
  return request({
    url: "/system/schoolDeviceLog",
    method: "post",
    data,
  });
}

// 查询截图列表 /system/schoolScreenshot
export function getSchoolScreenshotList(params) {
  return request({
    url: "/system/schoolScreenshot",
    method: "get",
    params,
  });
}

// 截屏 /system/schoolScreenshot
export function addSchoolScreenshot(data) {
  return request({
    url: "/system/schoolScreenshot",
    method: "post",
    data,
  });
}

// 远程开机 /system/edgeCtl/deviceStartByWOL
export function deviceStartByWOL(data) {
  return request({
    url: "/system/edgeCtl/deviceStartByWOL",
    method: "post",
    data,
  });
}

// 冰冻解冻 /system/device/winDDK
export function postWinDDK(data) {
  return request({
    url: "/system/device/winDDK",
    method: "post",
    data,
    noMsg: true,
  });
}

// 校验导出设备编码 /system/device/checkDeviceCode
export function checkDeviceCode(data) {
  return request({
    url: "/system/device/checkDeviceCode",
    method: "post",
    data,
  });
}

// 软件安装 /system/schoolSoftware/list
export function listSchoolSoftware(params) {
  return request({
    url: "/system/schoolSoftware/list",
    method: "get",
    params,
  });
}

// 上传软件 /system/schoolSoftware/uploadSoftware
export function uploadSoftware2(data) {
  return request({
    url: "/system/schoolSoftware/uploadSoftware",
    method: "post",
    data,
  });
}

// 静默安装 /system/schoolSoftware/installationSoftware
export function installationSoftware(data) {
  return request({
    url: "/system/schoolSoftware/installationSoftware",
    method: "post",
    data,
  });
}

// 查询设备数量 /system/schoolSoftware/checkDeviceNum
export function checkDeviceNum(data) {
  return request({
    url: "/system/device/checkDeviceNum",
    method: "post",
    data,
  });
}

// 修改巡检人员信息 /system/user/edit/inspection
export function editInspectionUser(data) {
  return request({
    url: "/system/user/edit/inspection",
    method: "put",
    data,
  });
}

// 消息列表 /system/timePlan/list
export function listTimePlan(params) {
  return request({
    url: "/system/timePlan/list",
    method: "get",
    params,
  });
}

// 倒计时功能 /system/edgeCtl/countdown
export function countdown(data) {
  return request({
    url: "/system/edgeCtl/countdown",
    method: "post",
    data,
  });
}

// 消息详情 /system/timePlan/1914606321262985217
export function getTimePlan(id) {
  return request({
    url: `/system/timePlan/${id}`,
    method: "get",
  });
}

// httpOrMqtt发送消息 /system/device/sendMessageMqttOrHttp
export function sendMessageMqttOrHttp(data) {
  return request({
    url: "/system/device/sendMessageMqttOrHttp",
    method: "post",
    data,
  });
}

// 音量控制接口 /system/edgeCtl/volumeSize
export function volumeSize(data) {
  return request({
    url: "/system/edgeCtl/volumeSize",
    method: "post",
    data,
  });
}

// 开关机规则列表 /system/timePlan/switchMachine/list
export function listSwitchMachine(params) {
  return request({
    url: "/system/timePlan/switchMachine/list",
    method: "get",
    params,
  });
}

// 开关机详情 /system/timePlan/switchMachine
export function getSwitchMachine(params) {
  return request({
    url: "/system/timePlan/switchMachine",
    method: "get",
    params,
  });
}

// 开关机规则 /system/timePlan/add/switchMachine
export function addSwitchMachine(data) {
  return request({
    url: "/system/timePlan/add/switchMachine",
    method: "post",
    data,
  });
}

// 删除定时计划 /system/timePlan/delete/4154
export function deleteTimePlan(data) {
  return request({
    url: "/system/timePlan/delete",
    method: "post",
    data,
  });
}
