<template>
  <div class="taskInfo" v-loading="loadingPage">
    <!-- 工单详情表格 -->
    <el-descriptions title="报障信息" border>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="工单编号"
      >
        {{ repairForm.code || "-" }}
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="工单来源"
      >
        <span>{{ repairForm.resource || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障人"
      >
        <span>{{ repairForm.repairName || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障人联系方式"
      >
        <span>{{ repairForm.repairPhone || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障时间"
      >
        <span>{{ repairForm.submittedTime || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障来源"
      >
        <span>{{ repairForm.channel || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="设备能否正常使用"
      >
        <span>{{
          repairForm.isNormal == 1 ? "不能正常使用" : "能正常使用"
        }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障单位"
        :span="2"
      >
        <span>{{ repairForm.corpName || "-" }}</span>
      </el-descriptions-item>
      <el-descriptions-item
        :span="3"
        label-class-name="label-width"
        class-name="value-width"
        label="报障描述"
      >
        <span>{{ repairForm.remark || "-" }}</span>
      </el-descriptions-item>

      <el-descriptions-item
        :span="3"
        label-class-name="label-width"
        class-name="value-width"
        label="报障图片"
      >
        <span v-if="!repairForm.troubleImg">暂无图片</span>
        <el-image
          v-else
          v-for="(item, index) in repairForm.troubleImg"
          :key="index"
          style="width: 100px; height: 100px; margin-right: 10px"
          :src="item"
          :zoom-rate="1.2"
          :max-scale="7"
          :min-scale="0.2"
          :preview-src-list="repairForm.troubleImg"
          :initial-index="index"
          fit="contain"
        />
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="报障附件"
        :span="3"
      >
        <span
          @click="downloadHttp(repairForm.annexUrl)"
          :class="repairForm.annexName ? 'uploadlink' : ''"
          >{{ repairForm.annexName || "-" }}</span
        >
      </el-descriptions-item>
      <el-descriptions-item
        label-class-name="label-width"
        class-name="value-width"
        label="电话录音"
        v-if="repairForm.channel == '电话报障'"
      >
        <span
          @click="downloadHttp(repairForm.fileUrl)"
          :class="repairForm.fileName ? 'uploadlink' : ''"
          >{{ repairForm.fileName || "-" }}</span
        >
      </el-descriptions-item>
    </el-descriptions>

    <!-- 设备详情表格 -->
    <el-form
      ref="formRef"
      :model="form"
      :rules="rules"
      :validate-on-rule-change="false"
      style="margin-top: 20px"
      @submit.native.prevent
    >
      <el-descriptions title="设备详情" border :column="2">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="设备编码"
        >
          {{ form.deviceCode || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width required"
          class-name="value-width"
          label="设备名称"
        >
          <span :style="{ color: form.isDelete ? 'red' : '#000' }">{{
            form.deviceName
          }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="设备图片"
        >
          <span v-if="!form.deviceImg">-</span>
          <el-image
            v-else-if="props.isHandled || isDelete"
            style="width: 100px; height: 50px"
            :src="form.deviceImg"
            :zoom-rate="1.2"
            :max-scale="7"
            :min-scale="0.2"
            :preview-src-list="[form.deviceImg]"
            :initial-index="0"
            fit="contain"
          />
          <el-form-item v-else label="" prop="deviceImg">
            <imgUpload
              @update:modelValue="
                (url) => {
                  form.deviceImg = url;
                }
              "
              :limit="1"
              :modelValue="form.deviceImg"
              :disabled="props.readonly"
              :isShowTip="!props.readonly"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width required"
          class-name="value-width"
          label="设备类型"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.deviceType || "-"
          }}</span>
          <el-form-item v-else label="" prop="typeId">
            <el-select
              v-model="form.typeId"
              placeholder="请选择设备类型"
              style="width: 100%"
              :disabled="props.readonly || form.deviceType == '智慧大屏'"
              @change="handleChangeType"
            >
              <el-option
                v-for="item in typeList_disabled"
                :key="item.id"
                :label="item.typeName"
                :value="item.id"
                :disabled="item.disabled || !!item.status"
              />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="设备标签"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.deviceTag || "-"
          }}</span>
          <el-form-item v-else label="" prop="tagIdList">
            <el-select
              v-model="form.tagIdList"
              placeholder="请选择标签"
              :disabled="props.readonly"
              filterable
              multiple
              :multiple-limit="5"
              style="width: 100%"
            >
              <el-option
                v-for="item in tagList"
                :key="item.tagId"
                :label="item.tag"
                :value="item.tagId"
              />
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width required"
          class-name="value-width"
          label="安装位置"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.installAddress || "-"
          }}</span>
          <el-form-item v-else label="" prop="installAddressId">
            <el-input
              v-if="props.readonly"
              placeholder="-"
              v-model="form.installAddress"
              disabled
              style="width: 100%"
              :title="form.installAddress"
            />
            <el-tree-select
              v-else
              v-model="form.installAddressId"
              :props="positionProps"
              :data="positionTreeList_disabled"
              placeholder="请选择安装位置"
              :render-after-expand="false"
              check-strictly
              clearable
              style="width: 100%"
              @change="handlePosition"
            >
            </el-tree-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="信息资产类别"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.assetsTypeName || "-"
          }}</span>
          <el-form-item v-else label="" prop="infoTypeId">
            <el-tree-select
              v-model="form.infoTypeId"
              :props="assetsProps"
              :data="assetsTreeList_disabled"
              placeholder="请选择信息资产类别"
              :render-after-expand="false"
              check-strictly
              clearable
              :disabled="props.readonly"
              style="width: 100%"
              @change="handleAssets"
            ></el-tree-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="信息资产等级"
        >
          {{ form.level || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="资产端口类别"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.portTypeName || "-"
          }}</span>
          <el-form-item v-else label="" prop="portTypeId">
            <el-select
              v-model="form.portTypeId"
              placeholder="请选择资产端口类别"
              style="width: 100%"
              :disabled="props.readonly"
              clearable
            >
              <el-option
                v-for="item in portTypeList"
                :key="item.id"
                :label="item.name"
                :value="item.id"
              >
              </el-option>
            </el-select>
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="规格型号"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.model || "-"
          }}</span>
          <el-form-item v-else label="" prop="model">
            <el-input
              v-model="form.model"
              :disabled="props.readonly"
              placeholder="请输入规格型号"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="物理地址"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.macAddress || "-"
          }}</span>
          <el-form-item v-else label="" prop="macAddress">
            <el-input
              v-model="form.macAddress"
              :disabled="props.readonly"
              placeholder="请输入物理地址"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="逻辑地址"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.logicAddress || "-"
          }}</span>
          <el-form-item v-else label="" prop="logicAddress">
            <el-input
              v-model="form.logicAddress"
              :disabled="props.readonly"
              placeholder="请输入逻辑地址"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="IP地址"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.ipAddress || "-"
          }}</span>
          <el-form-item v-else label="" prop="ipAddress">
            <el-input
              v-model="form.ipAddress"
              :disabled="props.readonly"
              placeholder="请输入IP地址"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="操作系统版本号"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.osVersion || "-"
          }}</span>
          <el-form-item v-else label="" prop="osVersion">
            <el-input
              v-model="form.osVersion"
              :disabled="props.readonly"
              placeholder="请输入操作系统版本号"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="状态"
        >
          <el-tag
            v-if="statusObj[form.deviceStatus]"
            effect="dark"
            :type="statusObj[form.deviceStatus].type"
            >{{ statusObj[form.deviceStatus].label }}</el-tag
          >
          <span v-else>-</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="CPU"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.cpu || "-"
          }}</span>
          <el-form-item v-else label="" prop="cpu">
            <el-input
              v-model="form.cpu"
              :disabled="props.readonly"
              placeholder="请输入CPU"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="品牌"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.brand || "-"
          }}</span>
          <el-form-item v-else label="" prop="brand">
            <el-input
              v-model="form.brand"
              :disabled="props.readonly"
              placeholder="请输入品牌"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="内存"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.internalStorage || "-"
          }}</span>
          <div v-else class="unitFlex">
            <el-input-number
              :min="0"
              style="width: 180px"
              v-model="form.internalStorageNumber"
              :disabled="props.readonly"
              :precision="0"
              :step="1"
            />
            <el-select
              v-model="form.internalStorageUnit"
              style="width: 120px"
              :disabled="props.readonly"
            >
              <el-option v-for="item in sizeList" :label="item" :value="item" />
            </el-select>
          </div>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="硬盘"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.disk || "-"
          }}</span>
          <div v-else class="unitFlex">
            <el-input-number
              :min="0"
              style="width: 180px"
              v-model="form.diskNumber"
              :disabled="props.readonly"
              :precision="0"
              :step="1"
            />
            <el-select
              v-model="form.diskUnit"
              style="width: 120px"
              :disabled="props.readonly"
            >
              <el-option v-for="item in sizeList" :label="item" :value="item" />
            </el-select>
          </div>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="端口"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.port || "-"
          }}</span>
          <el-form-item v-else label="" prop="port">
            <el-input
              v-model="form.port"
              :disabled="props.readonly"
              placeholder="请输入端口"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="线缆"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.cable || "-"
          }}</span>
          <el-form-item v-else label="" prop="cable">
            <el-input
              v-model="form.cable"
              :disabled="props.readonly"
              placeholder="请输入线缆"
              style="width: 100%"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width required"
          class-name="value-width"
          label="入库时间"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.putTime || "-"
          }}</span>
          <el-form-item label="" prop="putTime" v-else>
            <el-date-picker
              v-model="form.putTime"
              format="YYYY-MM-DD"
              :disabled-date="disabledDateFn"
              value-format="YYYY-MM-DD"
              style="width: 100%"
              type="date"
              :disabled="props.readonly"
              placeholder="请选择入库时间"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="关联设备"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.associatedDevice?.replace(/,/g, "、") || "-"
          }}</span>
          <el-form-item v-else label="" prop="associatedDevice">
            <el-input
              v-model="form.associatedDevice"
              style="width: 100%"
              readonly
              type="textarea"
              :rows="1"
              :disabled="props.readonly"
              placeholder="点击选择关联设备"
              @click="handleDialog(0)"
            />
          </el-form-item>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="关联人员"
        >
          <span v-if="props.isHandled || isDelete || !form.deviceCode">{{
            form.associatedPeople?.replace(/,/g, "、") || "-"
          }}</span>
          <el-form-item v-else label="" prop="associatedPeople">
            <el-input
              v-model="form.associatedPeople"
              style="width: 100%"
              readonly
              type="textarea"
              :rows="1"
              :disabled="props.readonly"
              placeholder="点击选择关联人员"
              @click="handleDialog(1)"
            />
          </el-form-item>
        </el-descriptions-item>
      </el-descriptions>
    </el-form>

    <div
      class="taskInfo-tit"
      v-if="historyMaintenanceList.length > 0 || !props.readonly"
    >
      维修信息
    </div>

    <!-- 历史扭转信息 -->
    <div v-for="(item, index) in historyMaintenanceList" :key="index">
      <el-descriptions title="" border style="margin-top: 20px" :column="3">
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="维修人"
        >
          {{ item.maintainName || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="维修时间"
        >
          <span>{{ item.maintainTime || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="处理耗时"
        >
          {{ item.troubleTime ? `${item.troubleTime} 小时` : "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="维修结果"
          :span="3"
        >
          <span>{{ item.results || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="维修图片"
        >
          <view>
            <span v-if="!item.repairImg">暂无图片</span>
            <el-image
              v-else
              v-for="(item2, index2) in item.repairImg"
              :key="index2"
              style="width: 50px; height: 30px"
              :src="item2"
              :zoom-rate="1.2"
              :max-scale="7"
              :min-scale="0.2"
              :preview-src-list="item.repairImg"
              :initial-index="index2"
              fit="contain"
            />
          </view>
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <div v-if="!props.readonly">
      <!-- 维修详情 -->
      <el-form
        ref="maintainFormRef"
        :model="maintainForm"
        :rules="maintainRules"
        :validate-on-rule-change="false"
        @submit.native.prevent
      >
        <el-descriptions title="" border style="margin-top: 20px" :column="3">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="维修人"
          >
            {{ maintainForm.maintainName || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="维修时间"
          >
            <span v-if="props.isHandled">{{
              maintainForm.maintainTime || "-"
            }}</span>
            <el-form-item label="" prop="maintainTime" v-else>
              <el-date-picker
                v-model="maintainForm.maintainTime"
                format="YYYY-MM-DD HH:mm:ss"
                :disabled-date="disabledDateFn"
                value-format="YYYY-MM-DD HH:mm:ss"
                style="width: 100%"
                type="datetime"
                placeholder="请选择维修时间"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="处理耗时"
          >
            {{
              maintainForm.troubleTime
                ? `${maintainForm.troubleTime} 小时`
                : "-"
            }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="维修结果"
            :span="3"
          >
            <span v-if="props.isHandled">{{
              maintainForm.results || "-"
            }}</span>
            <el-form-item label="" prop="results" v-else>
              <el-input
                v-model="maintainForm.results"
                type="textarea"
                placeholder="请输入维修结果"
                style="width: 100%"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="维修图片"
          >
            <view v-if="props.isHandled">
              <span v-if="!maintainForm.repairImg">暂无图片</span>
              <el-image
                v-else
                v-for="(item, index) in maintainForm.repairImg"
                :key="index"
                style="width: 50px; height: 30px"
                :src="item"
                :zoom-rate="1.2"
                :max-scale="7"
                :min-scale="0.2"
                :preview-src-list="maintainForm.repairImg"
                :initial-index="index"
                fit="contain"
              />
            </view>
            <el-form-item label="" prop="repairImg" v-if="!props.isHandled">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    maintainForm.repairImg = url;
                  }
                "
                :limit="3"
                :uploadType="4"
                :modelValue="maintainForm.repairImg"
                :isShowTip="true"
              />
            </el-form-item>
          </el-descriptions-item>
        </el-descriptions>
      </el-form>
    </div>

    <!-- 备件表格 -->
    <div class="taskInfo-tit">
      备件信息
      <el-button
        v-if="!(props.isHandled || props.readonly)"
        plain
        type="primary"
        icon="Plus"
        size="small"
        @click="handleDialog2(0)"
        >关联备件信息</el-button
      >
    </div>
    <el-table :data="spareList" border max-height="300px">
      <el-table-column
        label="备件编号"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
      >
        <template #default="scope">
          {{ scope.row.sparePartsCode || scope.row.code || "-" }}
        </template>
      </el-table-column>
      <el-table-column
        label="备件名称"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="name"
      />
      <el-table-column
        label="备件类型"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="typeName"
      />
      <el-table-column
        label="规格型号"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="specifications"
      />
      <el-table-column
        label="备件品牌"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="brand"
      />
      <el-table-column
        label="颜色分类"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="color"
      />
      <el-table-column
        label="现有库存"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
        prop="stock"
      />
      <el-table-column
        label="消耗量"
        align="center"
        show-overflow-tooltip
        minWidth="150px"
      >
        <template #default="scope">
          <span v-if="props.isHandled || props.readonly">{{
            scope.row.num
          }}</span>
          <el-input-number
            v-else
            v-model="scope.row.num"
            :max="scope.row.stock"
            :min="0"
            size="small"
          />
        </template>
      </el-table-column>
      <el-table-column
        v-if="!(props.isHandled || props.readonly)"
        label="操作"
        align="center"
        show-overflow-tooltip
        minWidth="120px"
      >
        <template #default="scope">
          <el-button type="danger" link @click="handleDelete(scope)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>
    <!-- <pagination
      class="page"
      v-show="totalSpare > 0"
      :total="totalSpare"
      v-model:page="queryParamsSpare.pageNum"
      v-model:limit="queryParamsSpare.pageSize"
      @pagination="getSpareList"
      :background="false"
      layout="total, prev, pager, next"
    /> -->

    <div v-if="props.isHandled">
      <!-- 评价详情 -->
      <div v-if="!commentForm.id">
        <div class="taskInfo-tit">评价信息</div>
        <div style="font-size: 16px">暂无评价</div>
      </div>

      <el-descriptions
        v-if="!!commentForm.id"
        title="评价信息"
        border
        style="margin-top: 20px"
        :column="2"
      >
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="问题是否解决"
        >
          {{ commentForm.resolved || "未解决" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="服务态度"
        >
          <el-rate
            v-model="commentForm.serviceAttitude"
            disabled
            size="large"
          />
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="技术能力"
        >
          <el-rate
            v-model="commentForm.technicalAbility"
            disabled
            size="large"
          />
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="处理速度"
        >
          <el-rate
            v-model="commentForm.processingSpeed"
            disabled
            size="large"
          />
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="意见反馈"
        >
          <span>{{ commentForm.feedback || "-" }}</span>
        </el-descriptions-item>
      </el-descriptions>

      <!-- 关联投诉单 -->
      <div v-if="!complaintsForm.id">
        <div class="taskInfo-tit">关联投诉单</div>
        <div style="font-size: 16px">暂无投诉</div>
      </div>
      <el-descriptions
        v-if="!!complaintsForm.id"
        title="关联投诉单"
        border
        style="margin-top: 20px"
        :column="2"
      >
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉单编号"
        >
          {{ complaintsForm.code || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉人"
        >
          {{ complaintsForm.name || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="联系方式"
        >
          {{ complaintsForm.phone || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉类别"
        >
          {{ complaintsForm.type || "-" }}
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="投诉内容"
          :span="2"
        >
          <span>{{ complaintsForm.complaintsContent || "-" }}</span>
        </el-descriptions-item>
        <el-descriptions-item
          label-class-name="label-width"
          class-name="value-width"
          label="状态"
        >
          <el-tag
            size="large"
            :type="complaintsForm.status === 1 ? 'success' : 'danger'"
            >{{ complaintsForm.status === 1 ? "已回复" : "未回复" }}</el-tag
          >
        </el-descriptions-item>
        <el-descriptions-item
          v-if="props.showComplaintsBtn"
          label-class-name="label-width"
          class-name="value-width"
          label="操作"
        >
          <el-button type="primary" link @click="handleDetail()"
            >查看详情</el-button
          >
        </el-descriptions-item>
      </el-descriptions>
    </div>

    <!-- 选择设备/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="800"
      @close="handleCancel"
      top="3vh"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
        @submit.native.prevent
      >
        <div v-if="title == '选择关联设备'" style="display: inline-block">
          <el-form-item prop="deviceCode">
            <el-input
              v-model="queryParams.deviceCode"
              placeholder="请输入设备编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item prop="deviceName">
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="addressStr">
            <el-tree-select
              v-model="queryParams.addressStr"
              :props="positionProps"
              :data="positionTreeList"
              placeholder="请选择安装位置"
              :render-after-expand="false"
              check-strictly
              style="width: 200px"
              clearable
              @change="handleQueryPosition"
            />
          </el-form-item>
        </div>
        <div v-if="title == '选择关联人员'" style="display: inline-block">
          <el-form-item prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入人员名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <template v-if="title == '选择关联设备'">
          <el-table-column
            type="selection"
            align="center"
            width="70"
            fixed="left"
          />
          <el-table-column
            label="设备编号"
            prop="deviceCode"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="设备名称"
            prop="deviceName"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            label="设备类别"
            show-overflow-tooltip
            min-width="100"
          >
            <template #default="{ row }">
              {{ row.deviceType || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="安装位置"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="installAddress"
          >
            <template #default="scope">
              <span> {{ scope.row.installAddress || "-" }}</span>
            </template>
          </el-table-column>
          <el-table-column
            label="设备状态"
            align="center"
            show-overflow-tooltip
            minWidth="80px"
          >
            <template #default="scope">
              <el-tag
                v-if="statusObj[scope.row.status]"
                effect="dark"
                :type="statusObj[scope.row.status].type"
                >{{ statusObj[scope.row.status].label }}</el-tag
              >
            </template>
          </el-table-column>
        </template>
        <template v-if="title == '选择关联人员'">
          <el-table-column
            type="selection"
            align="center"
            width="70"
            fixed="left"
          />
          <el-table-column
            label="人员编号"
            prop="userName"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="人员姓名"
            prop="nickName"
            show-overflow-tooltip
            min-width="120"
          />
        </template>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :background="false"
        :autoScroll="false"
        layout="total, prev, pager, next"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="submitForm" v-throttle
            >选择</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 选择备件/扭转对象弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title2"
      v-model="dialogVisible2"
      width="800"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams2"
        ref="queryRef2"
        @submit.native.prevent
      >
        <el-form-item v-if="title2 == '选择关联备件'" prop="codeAndName">
          <el-input
            v-model="queryParams2.codeAndName"
            placeholder="请输入备件编号/名称搜索"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery2"
          />
        </el-form-item>
        <el-form-item v-if="title2 == '选择关联备件'" prop="typeId">
          <el-select
            v-model="queryParams2.typeId"
            placeholder="请选择备件类别"
            @change="handleQuery2"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in spareTypeList"
              :key="item.id"
              :label="item.name"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item v-if="title2 == '选择扭转对象'" prop="nickName">
          <el-input
            v-model="queryParams2.nickName"
            placeholder="请输入人员名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery2"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery2"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery2">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        ref="tableRef2"
        v-if="title2 == '选择关联备件'"
        :data="tableList2"
        border
        highlight-current-row
        @row-click="rowClick2"
        @selection-change="selectionChange2"
        @select="onTableSelect2"
        @select-all="selectSingleTableAll2"
      >
        <el-table-column type="selection" align="center" width="70" />
        <el-table-column
          label="备件编号"
          prop="code"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="备件名称"
          prop="name"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="备件类别"
          prop="typeName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="规格型号"
          prop="specifications"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column
          label="备件品牌"
          prop="brand"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column
          label="现库存"
          prop="stock"
          show-overflow-tooltip
          min-width="100"
        />
      </el-table>

      <el-table
        v-loading="loading"
        ref="tableRef2"
        v-if="title2 == '选择扭转对象'"
        :data="tableList2"
        border
        @row-click="rowSingleClick"
      >
        <el-table-column align="center" width="70">
          <template #default="scope">
            <el-checkbox
              v-model="scope.row.checked"
              @change="(val) => handleSingleChange(val, scope.$index)"
              @click.native.stop=""
            />
          </template>
        </el-table-column>
        <el-table-column label="工号" show-overflow-tooltip min-width="100">
          <template #default="{ row }">
            {{ row.workId || "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="姓名"
          prop="nickName"
          show-overflow-tooltip
          min-width="120"
        />
        <el-table-column label="性别" show-overflow-tooltip min-width="90">
          <template #default="{ row }">
            {{ row.sex === 0 ? "男" : row.sex === 1 ? "女" : "-" }}
          </template>
        </el-table-column>
        <el-table-column
          label="所属部门"
          prop="deptName"
          show-overflow-tooltip
          min-width="100"
        />
        <el-table-column label="岗位" show-overflow-tooltip min-width="100">
          <template #default="{ row }">
            {{ row.postName || "-" }}
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total2 > 0"
        :total="total2"
        v-model:page="queryParams2.pageNum"
        v-model:limit="queryParams2.pageSize"
        @pagination="getList2"
        :background="false"
        layout="total, prev, pager, next"
        :autoScroll="false"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel2">返回</el-button>
          <el-button type="primary" @click="submitForm2" v-throttle>{{
            title2 == "选择关联备件" ? "选择" : "扭转"
          }}</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup>
import { useRoute, useRouter } from "vue-router";
import {
  devicePage,
  deviceRelatePage,
  deviceEdit,
  deviceSparePage,
} from "@/api/mediaTeach/ledger";
import {
  repairInfo,
  deviceMaintenance,
  addSparePartsTrouble,
  spareUse,
  troubleUpdate,
} from "@/api/mediaTeach/trouble";
import {
  sparePartsPage,
  addSparePartsDetails,
  sparePartsCategoryList,
} from "@/api/spare";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import {
  schoolAssetsTypeTree,
  schoolAssetsPortTypeList,
} from "@/api/mediaTeach/assets";
import { getPositionTree } from "@/api/mediaTeach/position";
import { toRefs } from "vue";
import {
  treeToArray,
  treeFindPath,
  findIndexInObejctArr,
  extractNumbers,
  extractWords,
  downloadHttp,
  scrollToErrorField,
} from "@/utils";
import imgUpload from "@/components/ImageUpload";
import useUserStore from "@/store/modules/user";
import { getUserByRole } from "@/api/system/user";
import { nextTick } from "process";

const emits = defineEmits([
  "changeTroubleStatus",
  "changeDeviceStatus",
  "changeEditStatus",
  "getRecord",
  "handleBack",
]);
const userStore = useUserStore();
const { proxy } = getCurrentInstance();
const route = useRoute();
const router = useRouter();
const props = defineProps({
  // 查看/待认领状态
  readonly: {
    type: Boolean,
    default: false,
  },
  troubleId: {
    type: String,
    default: "",
  },
  // 是否已处理
  isHandled: {
    type: Boolean,
    default: true,
  },
  showComplaintsBtn: {
    type: Boolean,
    default: true,
  },
});

const state = reactive({
  historyMaintenanceList: [],
  sizeList: ["KB", "MB", "GB", "TB"],
  complaintsForm: {},
  curUser: {},
  loadingPage: false,
  complainList: [],
  isDelete: false,
  totalSpare: 0,
  totalComplain: 0,
  tableRef: null,
  tableRef2: null,
  queryRef2: null,
  queryRef: null,
  maintainFormRef: null,
  formRef: null,
  associatedDeviceRow: [],
  associatedPeopleRow: [],
  associatedSpareRow: [],
  allDeviceList: [],
  allPeopleList: [],
  form: {
    deviceImg: "",
    installAddressId: "",
    internalStorageNumber: 0,
    internalStorageUnit: "GB",
    diskNumber: 0,
    diskUnit: "GB",
    deviceStatus: 0,
    associatedDevice: "",
    associatedPeople: "",
    associatedDeviceList: [],
    associatedDeviceIds: [],
    associatedPeopleList: [],
    associatedPeopleIds: [],
    associatedUserIds: [],
  },
  loading: false,
  dialogVisible: false,
  dialogVisible2: false,
  spareTypeList: [],
  deviceList: [],
  personList: [],
  title: "",
  title2: "",
  total2: 0,
  total: 0,
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    deviceId: "",
    deviceCode: "",
    deviceName: "",
    addressStr: "",
    nickName: "",
  },
  queryParams2: {
    pageNum: 1,
    pageSize: 5,
    codeAndName: "",
    nickNam: "",
  },
  queryParamsSpare: {
    pageNum: 1,
    pageSize: 99999,
    troubleId: props.troubleId,
  },
  queryParamsComplain: {
    pageNum: 1,
    pageSize: 5,
    troubleId: props.troubleId,
  },
  spareList: [],
  tagList: [],
  typeList: [],
  tableList: [],
  tableList2: [],
  tableList_all: [],
  tableList_all2: [],
  tableRadio: [],
  tableRadio2: [],
  tableAllSelectedId: [],
  tableAllSelectedId2: [],
  tableAllSelectedRow: [],
  tableAllSelectedRow2: [],
  portTypeList: [],
  typeList_disabled: [],
  positionTreeList: [],
  positionTreeList_disabled: [],
  assetsTreeList: [],
  assetsTreeList_disabled: [],
  assetsList: [],
  positionList: [],
  queryPositionResult: {
    names: [],
    ids: [],
  },
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  assetsProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  positionResult: {
    names: [],
    ids: [],
  },
  assetsResult: {
    names: [],
    ids: [],
  },
  repairForm: {
    deviceCode: "",
    remark: "",
    repairName: "",
    submittedTime: "",
    repairPhone: "",
    repairUnit: "",
    troubleImg: [],
  },
  maintainForm: {
    troubleIds: [],
    maintainTime: "",
    maintainName: userStore.nickName,
    results: "",
    repairImg: "",
    repairFile: null,
    processTime: "", // 新增处理耗时字段
  },
  commentForm: {
    id: "",
  },
  statusObj: {
    0: { label: "正常", type: "success" },
    1: { label: "维修中", type: "danger" },
    2: { label: "报障中", type: "warning" },
    3: { label: "待审核", type: "primary" },
    4: { label: "已报废", type: "info" },
  },
  rules: {},
  maintainRules: {
    maintainName: [
      { required: true, message: "维修人不能为空", trigger: "blur" },
    ],
    results: [
      { required: true, message: "维修结果不能为空", trigger: "blur" },
      { max: 200, message: "维修结果最多输入200个字符", trigger: "blur" },
    ],
    maintainTime: [
      {
        required: true,
        validator: validateMaintainTime,
        trigger: ["blur", "change"],
      },
    ],
    repairImg: [
      {
        required: true,
        message: "维修图片不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
  baseRules: {
    deviceName: [
      { required: true, message: "设备名称不能为空", trigger: "blur" },
      { max: 50, message: "设备名称最多输入50个字符", trigger: "blur" },
    ],
    typeId: [{ required: true, message: "设备类型不能为空", trigger: "blur" }],
    deviceCode: [
      { required: true, message: "设备编码不能为空", trigger: "blur" },
    ],
    deviceStatus: [
      {
        required: true,
        message: "设备状态不能为空",
        trigger: ["blur", "change"],
      },
    ],
    // deviceImg: [
    //   {
    //     required: true,
    //     message: "设备照片不能为空",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    installAddressId: [
      {
        required: true,
        message: "安装位置不能为空",
        trigger: ["blur", "change"],
      },
    ],
    // model: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
    putTime: [
      {
        required: true,
        message: "入库时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
  smartRules: {
    // tagIdList: [
    //   {
    //     required: true,
    //     message: "设备标签不能为空",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    macAddress: [
      { required: true, message: "物理地址不能为空", trigger: "blur" },
    ],
    logicAddress: [
      { required: true, message: "逻辑地址不能为空", trigger: "blur" },
    ],
    osVersion: [
      { required: true, message: "操作系统版本号不能为空", trigger: "blur" },
    ],
    cpu: [{ required: true, message: "CPU类型不能为空", trigger: "blur" }],
    // internalStorage: [
    //   { required: true, message: "设备内存不能为空", trigger: "blur" },
    // ],
    ipAddress: [{ required: true, message: "IP地址不能为空", trigger: "blur" }],
    // ralayHost: [
    //   { required: true, message: "边缘服务器ip不能为空", trigger: "blur" },
    // ],
    // brand: [{ required: true, message: "设备品牌不能为空", trigger: "blur" }],
    // disk: [{ required: true, message: "设备硬盘不能为空", trigger: "blur" }],
  },
});
const {
  historyMaintenanceList,
  sizeList,
  complaintsForm,
  allDeviceList,
  allPeopleList,
  curUser,
  queryParamsComplain,
  totalComplain,
  loadingPage,
  complainList,
  commentForm,
  maintainFormRef,
  isDelete,
  spareTypeList,
  totalSpare,
  queryParamsSpare,
  tableRef2,
  queryRef2,
  statusObj,
  spareList,
  associatedSpareRow,
  associatedDeviceRow,
  associatedPeopleRow,
  queryRef,
  tableRef,
  loading,
  formRef,
  queryPositionResult,
  tableList,
  tableList2,
  tableList_all,
  tableList_all2,
  tableAllSelectedId,
  tableAllSelectedId2,
  tableAllSelectedRow,
  tableAllSelectedRow2,
  tableRadio,
  tableRadio2,
  deviceList,
  personList,
  positionResult,
  assetsProps,
  positionList,
  portTypeList,
  typeList,
  tagList,
  typeList_disabled,
  positionTreeList,
  positionTreeList_disabled,
  assetsTreeList,
  assetsResult,
  assetsList,
  assetsTreeList_disabled,
  positionProps,
  total,
  total2,
  title2,
  title,
  dialogVisible2,
  dialogVisible,
  queryParams,
  queryParams2,
  repairForm,
  maintainForm,
  maintainRules,
  baseRules,
  smartRules,
  form,
  rules,
} = toRefs(state);
let checkName = computed(() =>
  state.title == "选择关联设备" ? "deviceId" : "userId"
);
let checkName2 = computed(() =>
  state.title2 == "选择关联备件" ? "id" : "userId"
);

function validateMaintainTime(rule, value, callback) {
  if (!value) {
    callback(new Error("请选择维修时间"));
  } else if (new Date(value).getTime() > new Date().getTime()) {
    callback(new Error("维修时间不能选择未来时间"));
  } else {
    callback();
  }
}

const handleDetail = () => {
  router.push({
    path: `/repair/repairInfo`,
    query: {
      id: state.complaintsForm.id,
      taskId: props.troubleId,
      status: state.repairForm.status,
    },
  });
};

const handleDelete = ({ row, $index }) => {
  state.spareList.splice($index, 1);
};

function checkSpareStock() {
  if (state.spareList.length > 0) {
    state.spareList.map((item) => {
      if (item.num == 0) {
        proxy.$modal.msgError(`备件“${item.name}”的库存为0，请删除`);
        return true;
      }
    });
  }
}

const handleSave = () => {
  state.maintainFormRef.validate((valid, error) => {
    if (valid) {
      const obj = {
        ...state.maintainForm,
        troubleIds: [props.troubleId],
        repairImg: state.maintainForm.repairImg.split(","),
        detailsAdd:
          state.spareList.length > 0
            ? [
                {
                  associatedId: props.troubleId,
                  associatedCode: state.repairForm.code,
                  sparePartsDetails: state.spareList.map((item) => {
                    let temp = {
                      sparePartsId: item.id,
                      type: 0,
                      resourceType: 1,
                      resource: "关联工单",
                      num: item.num,
                    };
                    return temp;
                  }),
                },
              ]
            : [],
      };
      console.log("维修传参", obj);
      proxy.$modal.loading();
      deviceMaintenance(obj)
        .then((resp) => {
          proxy.$modal.msgSuccess("操作成功");
          proxy.$modal.closeLoading();
          emits("handleBack");
        })
        .catch(() => proxy.$modal.closeLoading());
    } else {
      state.formRef.resetFields();
      nextTick(() => scrollToErrorField());
    }
  });
};

const handleSaveAndUpdate = async () => {
  let flag1 = false,
    flag2 = false;
  await state.formRef.validate((valid2) => {
    if (valid2) {
      flag1 = true;
    }
  });
  await state.maintainFormRef.validate((valid) => {
    if (valid) {
      flag2 = true;
    }
  });
  console.log(flag1, flag2);
  if (flag1 && flag2) {
    proxy.$modal.loading();
    state.form.installAddress =
      state.positionResult.names.join("-") || state.form.installAddress;
    state.form.positionId =
      state.positionResult.ids.join(",") || state.form.positionId;
    state.form.assetsTypeName =
      state.assetsResult.names?.join("-") || state.form.assetsTypeName;
    state.form.assetsTypeId =
      state.assetsResult.ids?.join(",") || state.form.assetsTypeId;
    state.form.tagId = !!state.form.tagIdList
      ? state.form.tagIdList.join(",")
      : "";
    state.form.portTypeName = state.form.portTypeId
      ? state.portTypeList.find((_) => _.id == state.form.portTypeId).name
      : "";
    state.form.internalStorage =
      state.form.internalStorageNumber.toFixed(0) +
      state.form.internalStorageUnit;
    state.form.disk = state.form.diskNumber.toFixed(0) + state.form.diskUnit;
    state.form.troubleId = props.troubleId;
    state.form.troubleCode = state.repairForm.code;
    console.log("修改的设备参数", state.form);
    deviceEdit(state.form)
      .then((resp2) => {
        let d = {
          ...state.maintainForm,
          troubleIds: [props.troubleId],
          repairImg: state.maintainForm.repairImg.split(","),
          detailsAdd:
            state.spareList.length > 0
              ? [
                  {
                    associatedId: props.troubleId,
                    associatedCode: state.repairForm.code,
                    sparePartsDetails: state.spareList.map((item) => {
                      let temp = {
                        sparePartsId: item.id,
                        type: 0,
                        resourceType: 1,
                        resource: "关联工单",
                        num: item.num,
                      };
                      return temp;
                    }),
                  },
                ]
              : [],
        };
        console.log("提交的维修参数", d);
        deviceMaintenance(d)
          .then((resp) => {
            proxy.$modal.msgSuccess("操作成功");
            proxy.$modal.closeLoading();
            emits("handleBack");
          })
          .catch(() => proxy.$modal.closeLoading());
      })
      .catch(() => proxy.$modal.closeLoading());
  } else {
    scrollToErrorField();
  }
};

const handleDialog = (type) => {
  state.title = !!type ? "选择关联人员" : "选择关联设备";
  state.dialogVisible = true;
  nextTick(() => resetQuery());
};

const handleDialog2 = (type) => {
  if (!!type) {
    state.maintainFormRef.validate((valid, error) => {
      if (valid) {
        state.title2 = "选择扭转对象";
        state.dialogVisible2 = true;
        nextTick(() => resetQuery2());
      }
    });
  } else {
    state.title2 = "选择关联备件";
    state.dialogVisible2 = true;
    nextTick(() => resetQuery2());
  }
};

// 关闭弹窗
const handleCancel = () => {
  resetQuery();
  state.tableAllSelectedId = [];
  state.tableAllSelectedRow = [];
  state.tableRadio = [];
  state.dialogVisible = false;
  console.log("form", state.form);
};

// 关闭弹窗
const handleCancel2 = () => {
  resetQuery2();
  state.tableAllSelectedId2 = [];
  state.tableAllSelectedRow2 = [];
  state.tableRadio2 = [];
  state.dialogVisible2 = false;
};

// 提交弹窗表单
function submitForm() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请至少选择一项");
    return;
  }

  if (state.title == "选择关联人员") {
    state.form.associatedUserIds = state.tableAllSelectedId;
    state.associatedPeopleRow = state.tableAllSelectedRow;
    state.form.associatedPeopleList = state.tableAllSelectedRow.reduce(
      (res, cur) => {
        res.push({
          userId: cur.userId,
          nickName: cur.nickName,
        });
        return res;
      },
      []
    );
    state.form.associatedPeople = state.form.associatedPeopleList
      .map((item) => item.nickName)
      .join("、");
  }
  if (state.title == "选择关联设备") {
    state.form.associatedDeviceIds = state.tableAllSelectedId;
    state.associatedDeviceRow = state.tableAllSelectedRow;
    state.form.associatedDeviceList = state.tableAllSelectedRow.reduce(
      (res, cur) => {
        res.push({
          deviceId: cur.deviceId,
          deviceName: cur.deviceName,
        });
        return res;
      },
      []
    );
    state.form.associatedDevice = state.form.associatedDeviceList
      .map((item) => item.deviceName)
      .join("、");
  }
  handleCancel();
}

// 提交弹窗表单
function submitForm2() {
  if (state.title2 == "选择关联备件") {
    if (state.tableAllSelectedId2.length < 1) {
      proxy.$modal.msgWarning("请至少选择一项");
      return;
    }
    state.associatedSpareRow = state.tableAllSelectedRow2;
    state.spareList = state.spareList.concat(
      JSON.parse(JSON.stringify(state.associatedSpareRow)).map((item) => {
        item.num = 1;
        return item;
      })
    );
    handleCancel2();
  }
  if (state.title2 == "选择扭转对象") {
    if (!state.curUser.userId) {
      proxy.$modal.msgWarning("请选择扭转对象");
      return;
    }
    proxy.$modal
      .confirm("确认扭转？")
      .then(() => {
        proxy.$modal.loading();
        const { repairImg, results } = state.maintainForm;
        let obj = {
          troubleId: props.troubleId,
          status: 7,
          userId: state.curUser.userId,
          repairImg: repairImg.split(","),
          results,
        };
        console.log(obj, "扭转传参");
        troubleUpdate(obj)
          .then((resp) => {
            proxy.$modal.msgSuccess("操作成功");
            emits("handleBack");
            proxy.$modal.closeLoading();
          })
          .catch(() => proxy.$modal.closeLoading());
      })
      .catch();
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.pageNum = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQueryPosition();
}

/** 搜索按钮操作 */
function handleQuery2() {
  state.queryParams2.pageNum = 1;
  state.tableAllSelectedId2 = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow2 = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList2();
}

/** 重置按钮操作 */
function resetQuery2() {
  proxy.resetForm("queryRef2");
  handleQuery2();
}

function getList() {
  state.loading = true;
  if (state.title == "选择关联设备") {
    if (state.form.associatedDeviceIds.length > 0) {
      state.tableAllSelectedId = state.form.associatedDeviceIds;
      state.tableAllSelectedRow = state.associatedDeviceRow;
    }
    deviceRelatePage({
      pageNum: 1,
      pageSize: 9999999,
      deviceId: state.form.deviceId,
    }).then((res) => {
      // console.log(res)
      state.tableList_all = res.data?.records || [];
    });
    deviceRelatePage({
      ...state.queryParams,
      deviceId: state.form.deviceId,
      // positionIds: state.queryParams.positionId ? [state.queryParams.positionId] : null,
    }).then((response) => {
      console.log("设备列表", response.data);
      state.total = response.data?.total || 0;
      state.tableList = response.data?.records || [];
      nextTick(() => {
        state.tableList.forEach((item) => {
          if (state.tableAllSelectedId.indexOf(item[checkName.value]) > -1) {
            state.tableRef.toggleRowSelection(item, true);
          } else {
            state.tableRef.toggleRowSelection(item, false);
          }
        });
        state.loading = false;
      });
    });
  }
  if (state.title == "选择关联人员") {
    if (state.form.associatedPeopleIds.length > 0) {
      state.tableAllSelectedId = state.form.associatedPeopleIds;
      state.tableAllSelectedRow = state.associatedPeopleRow;
    }
    getUserByRole({
      pageNum: 1,
      pageSize: 999999,
      roleKey: ["maintain", "maintainManage"],
    }).then((resp) => {
      console.log("全部运维人员", resp);
      if (resp.data) {
        state.tableList_all = resp.data.records || [];
      }
    });
    const { pageNum, pageSize, nickName } = state.queryParams;
    console.log("运维人员列表传参", { pageNum, pageSize, nickName });
    getUserByRole({
      pageNum,
      pageSize,
      nickName,
      roleKey: ["maintain", "maintainManage"],
    })
      .then((resp) => {
        console.log("运维人员列表", resp.data);
        if (resp.data) {
          const { records = [], total = 0 } = resp.data;
          state.tableList = records;
          state.total = total;
          nextTick(() => {
            state.tableList.forEach((item) => {
              if (
                state.tableAllSelectedId.indexOf(item[checkName.value]) > -1
              ) {
                state.tableRef.toggleRowSelection(item, true);
              } else {
                state.tableRef.toggleRowSelection(item, false);
              }
            });
          });
          state.loading = false;
        }
      })
      .catch(() => (state.loading = false));
  }
}

function getList2() {
  state.loading = true;
  if (state.title2 == "选择关联备件") {
    sparePartsPage({
      ...state.queryParams2,
      pageNum: 1,
      pageSize: 9999999,
      exclusionIds: state.spareList.map((item) => item.id),
    }).then((res) => {
      // console.log(res)
      state.tableList_all2 = res.data?.records || [];
    });
    sparePartsPage({
      ...state.queryParams2,
      exclusionIds: state.spareList.map((item) => item.id),
    }).then((response) => {
      console.log("备件列表", response.data);
      state.total2 = response.data?.total || 0;
      state.tableList2 = response.data?.records || [];
      nextTick(() => {
        state.tableList2.forEach((item) => {
          if (state.tableAllSelectedId2.indexOf(item[checkName2.value]) > -1) {
            state.tableRef2.toggleRowSelection(item, true);
          } else {
            state.tableRef2.toggleRowSelection(item, false);
          }
        });
        state.loading = false;
      });
    });
  }
  if (state.title2 == "选择扭转对象") {
    getUserByRole({
      ...state.queryParams2,
      roleKey: ["maintain", "maintainManage"],
    })
      .then((resp) => {
        console.log("运维人员", resp);
        if (resp.data) {
          const { records = [], total = 0 } = resp.data;
          state.tableList2 = records.map((item) => {
            item.checked = state.curUser.userId == item.userId;
            return item;
          });
          state.total2 = total;
        }
        state.loading = false;
      })
      .catch(() => (state.loading = false));
  }
}

function handleQueryPosition(val) {
  state.queryPositionResult = {
    ids: [],
    names: [],
  };
  state.queryPositionResult.ids = treeFindPath(
    state.positionTreeList,
    (d) => d.id == val
  );
  const arr = state.queryPositionResult.ids;
  for (let i = 0; i < arr.length; i++) {
    state.queryPositionResult.names[i] = state.positionList.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", state.queryPositionResult);
  state.queryParams.address = state.queryPositionResult.names.join("-");
  handleQuery();
}

// 根据类型动态修改校验规则
function handleChangeType(val) {
  let name = "";
  state.typeList.forEach((item) => {
    if (item.id == val) name = item.typeName;
  });
  state.rules = {};
  if (name == "智慧大屏") {
    if (state.baseRules.deviceCode[1]) delete state.baseRules.deviceCode[1];
    Object.assign(state.rules, state.baseRules, state.smartRules);
  } else {
    state.baseRules.deviceCode[1] = {
      pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
      message: "设备编码不能以“ZHDP”开头",
      trigger: "blur",
    };
    Object.assign(state.rules, state.baseRules);
  }
  state.formRef.clearValidate();
}

function handlePosition(val) {
  state.positionResult = {
    ids: [],
    names: [],
  };
  state.positionResult.ids = treeFindPath(
    state.positionTreeList,
    (d) => d.id == val
  );
  const arr = state.positionResult.ids;
  for (let i = 0; i < arr.length; i++) {
    state.positionResult.names[i] = state.positionList.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("positionResult ==> ", state.positionResult);
}

function handleAssets(val) {
  state.assetsResult = {
    ids: [],
    names: [],
  };
  state.assetsResult.ids = treeFindPath(
    state.assetsTreeList,
    (d) => d.id == val
  );
  const arr = state.assetsResult.ids;
  for (let i = 0; i < arr.length; i++) {
    state.assetsResult.names[i] = state.assetsList.find(
      (node) => node.id == arr[i]
    ).name;
  }

  if (state.assetsResult.ids.length > 0) {
    let len = state.assetsResult.ids.length;
    state.form.level = state.assetsList.find(
      (_) => _.id == state.assetsResult.ids[len - 1]
    ).level;
  } else {
    state.form.level = "";
  }
  console.log("assetsResult ==> ", state.assetsResult);
}

// 获取端口类别
async function getPortTypeList() {
  await schoolAssetsPortTypeList({ pageNum: 1, pageSize: 999999 }).then(
    (response) => {
      state.portTypeList = response.data?.records || [];
    }
  );
}

// 获取标签
async function getTagList() {
  await getDeviceTag().then((response) => {
    state.tagList = response.data;
  });
}

async function getPositionTreeList() {
  await getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    state.positionTreeList = response.data;
    state.positionTreeList_disabled = addAttr(tree);
    state.positionList = treeToArray(response.data);
  });
}

async function getAssetsTreeList() {
  await schoolAssetsTypeTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data?.tree || []));
    console.log("信息类别", response.data.tree);
    state.assetsTreeList = response.data?.tree || [];
    state.assetsTreeList_disabled = addAttr(tree);
    state.assetsList = treeToArray(state.assetsTreeList);
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

async function getTypeList() {
  await getDeviceType().then((response) => {
    state.typeList = response.data;
    state.typeList_disabled = response.data.reduce((res, cur) => {
      res.push({
        ...cur,
        disabled: cur.typeName == "智慧大屏",
      });
      return res;
    }, []);
  });
}

// 限制日期
const disabledDateFn = (date) => {
  if (date.getTime() > new Date().getTime()) {
    return true;
  }
  return false;
};

// 限制小时
const disabledHours = () => {
  const a = [];
  for (let i = 0; i < 24; i++) {
    if (new Date().getHours() < i) {
      a.push(i);
    }
  }
  return a;
};

// 限制分钟
const disabledMinutes = () => {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getMinutes() < i) {
      a.push(i);
    }
  }
  return a;
};

// 限制秒数
const disabledSeconds = () => {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getSeconds() < i) {
      a.push(i);
    }
  }
  return a;
};

function clearCheck() {
  state.tableList2 = state.tableList2.map((item) => {
    item.checked = false;
    return item;
  });
}

function handleSingleChange(val, index) {
  console.log(val, index, state.tableList2);
  if (val) {
    clearCheck();
    state.tableList2[index].checked = true;
    state.curUser = JSON.parse(JSON.stringify(state.tableList2[index]));
    console.log("check", val);
  } else {
    state.curUser = {};
  }
}

function rowSingleClick(row) {
  const idx = state.tableList2.findIndex((_) => _.userId == row.userId);
  if (row.checked) {
    state.tableList2[idx].checked = false;
    state.curUser = {};
  } else {
    clearCheck();
    state.tableList2[idx].checked = true;
    state.curUser = JSON.parse(JSON.stringify(row));
  }
}

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      checkName.value
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
}

/** 单击某行 */
function rowClick2(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow2)),
      row,
      checkName2.value
    ) > -1
  ) {
    if (state.tableRadio2 === row) {
      state.tableRadio2 = [];
      state.tableRef2.setCurrentRow(null);
      state.tableRef2.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId2.indexOf(row[checkName2.value]);
      state.tableAllSelectedId2.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow2.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio2 = row;
      state.tableRef2.setCurrentRow(row);
    }
  } else {
    state.tableRadio2 = row;
    state.tableRef2.setCurrentRow(row);
    state.tableRef2.toggleRowSelection(row, true);
  }
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
      state.tableAllSelectedId.push(item[checkName.value]);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 多选事件
function selectionChange2(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId2.indexOf(item[checkName2.value]) === -1) {
      state.tableAllSelectedId2.push(item[checkName2.value]);
      state.tableAllSelectedRow2.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}

// 点击表格勾选触发的事件
function onTableSelect2(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId2.indexOf(row[checkName2.value]);
    state.tableAllSelectedId2.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow2.splice(index, 1); // 取消勾选，则删除数据
  }
}

// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item[checkName.value] === a[0][checkName.value]) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
        state.tableAllSelectedId.push(item[checkName.value]); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
  }
}

// 表格全选触发的事件
function selectSingleTableAll2(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList2;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item[checkName2.value] === a[0][checkName2.value]) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all2.forEach((item) => {
      if (state.tableAllSelectedId2.indexOf(item[checkName2.value]) === -1) {
        state.tableAllSelectedId2.push(item[checkName2.value]); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow2.push(item); // 则保存表格全部的数据
      }
    });
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId2 = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow2 = []; // // 如果取消全选，则清空保存的数据
  }
}

async function getSpareList() {
  await spareUse(state.queryParamsSpare).then((resp) => {
    console.log("备件使用情况", resp.data);
    if (resp.data) {
      const { records = [], total = 0 } = resp.data;
      state.spareList = records.map((item) => {
        item.id = item.sparePartsId;
        return item;
      });
      state.totalSpare = total;
    }
  });
}

async function getComplainList() {
  // await spareUse(state.queryParamsSpare).then((resp) => {
  //   console.log("备件使用情况", resp.data);
  //   if (resp.data) {
  //     const { records = [], total = 0 } = resp.data;
  //     state.spareList = records;
  //     state.totalSpare = total;
  //   }
  // });
}

async function getSpareTypeList() {
  await sparePartsCategoryList().then((response) => {
    if (response.code == 200) {
      console.log(response);
      state.spareTypeList = response.data || [];
    }
  });
}

async function getAllUserList() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["maintain", "maintainManage"],
  }).then((resp) => {
    console.log("全部运维人员", resp);
    if (resp.data) {
      state.allPeopleList = resp.data.records || [];
    }
  });
}

async function getAllDeviceList() {
  await deviceRelatePage({
    pageNum: 1,
    pageSize: 9999999,
    deviceId: route.query.id,
  }).then((res) => {
    console.log(res);
    state.allDeviceList = res.data?.records || [];
  });
}

// 修改 getData 方法
async function getData() {
  state.loadingPage = true;
  try {
    Object.assign(state.rules, state.baseRules);
    // await getAllDeviceList()
    // await getAllUserList()
    getTagList();
    getTypeList();
    getPortTypeList();
    await getPositionTreeList();
    getAssetsTreeList();
    // 获取备件使用情况
    getSpareTypeList();
    getSpareList();
    // 获取工单详情
    repairInfo({
      type: 1,
      troubleId: props.troubleId,
    })
      .then((res) => {
        const {
          complaints,
          deviceVo,
          maintenanceVO,
          repairVO,
          historyMaintenanceList,
          sparePartsInfoVOList,
          operationLogList,
          evaluation,
        } = res.data;
        console.log("工单详情", res.data);
        state.historyMaintenanceList = historyMaintenanceList || [];
        console.log("历史扭转列表", state.historyMaintenanceList);
        if (deviceVo) {
          emits(
            "changeDeviceStatus",
            deviceVo.isDelete || !deviceVo.deviceCode
          );
          state.isDelete = !!deviceVo.isDelete;
          const {
            positionId,
            deviceType,
            assetsTypeId,
            associatedDeviceIds,
            associatedUserIds,
            associatedDevice,
            associatedPeople,
            deviceId,
            disk,
            internalStorage,
          } = deviceVo;
          state.rules = {};
          if (deviceType == "智慧大屏") {
            if (state.baseRules.deviceCode[1])
              delete state.baseRules.deviceCode[1];
            Object.assign(state.rules, state.baseRules, state.smartRules);
          } else {
            state.baseRules.deviceCode[1] = {
              pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
              message: "设备编码不能以“ZHDP”开头",
              trigger: "blur",
            };
            Object.assign(state.rules, state.baseRules);
          }
          const arr = !!positionId ? positionId.split(",") : [];
          const arr2 = !!assetsTypeId ? assetsTypeId.split(",") : [];
          state.positionResult.ids = JSON.parse(JSON.stringify(arr));
          state.assetsResult.ids = JSON.parse(JSON.stringify(arr2));
          let arrDevice = JSON.parse(JSON.stringify(associatedDevice)),
            arrPeople = JSON.parse(JSON.stringify(associatedPeople));
          state.associatedDeviceRow = JSON.parse(
            JSON.stringify(associatedDevice)
          );
          state.associatedPeopleRow = JSON.parse(
            JSON.stringify(associatedPeople)
          );
          Object.assign(state.form, {
            ...deviceVo,
            internalStorageNumber: internalStorage
              ? extractNumbers(internalStorage)
              : 0,
            internalStorageUnit: internalStorage
              ? extractWords(internalStorage)
              : "GB",
            diskNumber: disk ? extractNumbers(disk) : 0,
            diskUnit: disk ? extractWords(disk) : "GB",
            associatedDeviceList: JSON.parse(JSON.stringify(associatedDevice)),
            associatedDevice:
              arrDevice?.map((item) => item.deviceName).join("、") || "",
            associatedPeopleList: JSON.parse(JSON.stringify(associatedPeople)),
            associatedPeople:
              arrPeople?.map((item) => item.nickName).join("、") || "",
            associatedDeviceIds: arrDevice?.map((item) => item.deviceId) || [],
            associatedUserIds: arrPeople?.map((item) => item.userId) || [],
            associatedPeopleIds: arrPeople?.map((item) => item.userId) || [],
          });

          state.form.installAddressId =
            arr.length > 0 ? arr[arr.length - 1] * 1 : "";
          state.form.infoTypeId = arr2.length > 0 ? arr2[arr2.length - 1] : "";
          for (let i = 0; i < arr.length; i++) {
            state.positionResult.names[i] = state.positionList.find(
              (node) => node.id == arr[i]
            )?.name;
          }
          for (let i = 0; i < arr2.length; i++) {
            state.assetsResult.names[i] = state.assetsList.find(
              (node) => node.id == arr2[i]
            )?.name;
          }
          console.log("设备详情", state.form, state.positionResult);
        }
        if (repairVO) {
          Object.assign(state.repairForm, {
            ...repairVO,
            troubleImg: repairVO.troubleImg
              ? JSON.parse(repairVO.troubleImg)
              : [],
          });
          emits("changeTroubleStatus", repairVO.status);
        }
        if (maintenanceVO) {
          emits(
            "changeEditStatus",
            maintenanceVO.maintainUserId == userStore.userId
          );
          Object.assign(state.maintainForm, {
            ...maintenanceVO,
            repairImg: maintenanceVO.repairImg
              ? JSON.parse(maintenanceVO.repairImg)
              : "",
          });
        }
        if (evaluation) {
          Object.assign(state.commentForm, {
            ...evaluation,
          });
        }
        if (complaints) {
          Object.assign(state.complaintsForm, {
            ...complaints,
          });
        }
        console.log("评价详情", state.commentForm);
        if (sparePartsInfoVOList) {
          state.spareList = sparePartsInfoVOList;
        }
        if (operationLogList) {
          emits("getRecord", operationLogList);
        }
        state.loadingPage = false;
      })
      .catch(() => (state.loadingPage = false));
  } catch {
    state.loadingPage = false;
  }
}

onMounted(() => getData());

defineExpose({
  state,
  handleDialog2,
  handleSave,
  handleSaveAndUpdate,
});
</script>

<style scoped lang="scss">
.unitFlex {
  display: flex;
  gap: 0 10px;
}
.uploadlink {
  color: #4095e5;
  text-decoration: underline;
  cursor: pointer;
}
.page {
  margin: 0 !important;
  padding: 0 !important;
}
.taskInfo {
  .avatar {
    width: 100px;
    height: 50px;
  }

  .avatar-uploader {
    width: 100px;
    height: 50px;
  }

  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
      &.required {
        position: relative;
        &::before {
          font-size: 14px;
          content: "*";
          color: red;
          padding-right: 5px;
        }
      }
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }

  &-tit {
    font-size: 16px;
    display: flex;
    align-items: center;
    // justify-content: space-between;
    gap: 0 10px;
    font-weight: bold;
    margin: 20px 0;
  }

  &-form {
    width: 80%;
    font-size: 14px;
    &_item {
      display: flex;
      margin-bottom: 20px;
    }
    &_label {
      margin-right: 10px;
      text-align: left;
      font-weight: bold;
      color: #606266;
    }
    &_value {
      &-pics {
        display: flex;
        gap: 0 10px;
      }
    }
  }

  .upload-area {
    display: flex;
    align-items: center;
    margin-top: 10px;

    .upload-container {
      display: flex;
      align-items: center;
      gap: 20px;
    }

    .file-info {
      margin-left: 20px;
      display: flex;
      align-items: center;
      gap: 10px;

      span {
        display: inline-block;
        max-width: 200px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
    }
  }

  .repair-file {
    display: flex;
    align-items: center;
    gap: 10px;

    span {
      max-width: 200px;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: nowrap;
    }
  }

  &-btns {
    padding: 20px;
    text-align: center;

    .el-button {
      margin: 0 10px;
    }
  }
}

.info-table {
  width: 100%;
  border-collapse: collapse;
  margin-bottom: 30px;

  td {
    border: 1px solid #ebeef5;
    padding: 12px;
    height: 45px;
    box-sizing: border-box;

    &.label {
      background-color: #f5f7fa;
      width: 120px;
      color: #606266;
      font-weight: bold;
    }

    &.content {
      width: calc((100% - 360px) / 3);
      text-align: left;
      padding-left: 20px;
    }
  }
}

.repair-images {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.repair-file {
  display: flex;
  align-items: center;
  gap: 10px;

  span {
    font-size: 14px;
    color: #606266;
  }
}
</style>
