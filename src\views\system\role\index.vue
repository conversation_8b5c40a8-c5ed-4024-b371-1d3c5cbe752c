<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <el-form class="search-list" :model="queryParams" ref="queryRef" v-show="showSearch" :inline="true" label-width="68px">
        <el-form-item label="" prop="roleName">
          <el-input v-model.trim="queryParams.roleName" placeholder="请输入角色名称" clearable style="width: 200px" @clear="resetQuery"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="" prop="roleKey">
          <el-input v-model.trim="queryParams.roleKey" placeholder="请输入权限字符" clearable style="width: 200px" @clear="resetQuery"
            @keyup.enter="handleQuery" />
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select v-model="queryParams.status" @change="handleQuery" placeholder="请选择角色状态" clearable
            style="width: 200px">
            <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="createTime">
          <el-date-picker
            v-model="dateRange"
            type="daterange"
            range-separator="-"
            start-placeholder="开始日期"
            end-placeholder="结束日期"
            value-format="YYYY-MM-DD"
            format="YYYY-MM-DD"
            style="width: 240px"
            @change="handleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>
      <el-row :gutter="10" class="mb12" v-if="!isExternalPark">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="Plus" @click="handleAdd" v-hasPermi="['system:role:add']">新增</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="danger" plain icon="Delete" :disabled="multiple" @click="handleDelete"
            v-hasPermi="['system:role:remove']">删除</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="success" plain icon="Download" :disabled="multiple" @click="handleBatchExport">导出</el-button>
        </el-col>
        <!-- <right-toolbar v-model:showSearch="showSearch" @queryTable="getList"></right-toolbar> -->
      </el-row>

      <!-- 表格数据 -->
      <el-table v-loading="loading" :data="roleList" @selection-change="handleSelectionChange" border>
        <el-table-column type="selection" width="55" align="center" :selectable="row => !isExternalPark" />
        <el-table-column label="角色编号" prop="roleId" width="120" />
        <el-table-column label="角色名称" prop="roleName" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="权限字符" prop="roleKey" :show-overflow-tooltip="true" width="150" />
        <el-table-column label="显示顺序" prop="roleSort" width="100" />
        <el-table-column label="状态" align="center" width="100">
          <template #default="scope">
            <el-switch v-model="scope.row.status" active-value="0" inactive-value="1"
              @change="handleStatusChange(scope.row)" :disabled="isExternalPark"></el-switch>
          </template>
        </el-table-column>
        <el-table-column label="创建时间" align="center" prop="createTime">
          <template #default="scope">
            <span>{{ parseTime(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template #default="scope">
            <template v-if="!isExternalPark">
              <el-tooltip content="修改" placement="top" v-if="scope.row.roleId !== 1">
                <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)"
                  v-hasPermi="['system:role:edit']"></el-button>
              </el-tooltip>
              <el-tooltip content="删除" placement="top" v-if="scope.row.roleId !== 1">
                <el-button link type="primary" icon="Delete" @click="handleDelete(scope.row)"
                  v-hasPermi="['system:role:remove']"></el-button>
              </el-tooltip>
              <el-tooltip content="分配用户" placement="top" v-if="scope.row.roleId !== 1">
                <el-button link type="primary" icon="User" @click="handleAuthUser(scope.row)"
                  v-hasPermi="['system:role:edit']"></el-button>
              </el-tooltip>
            </template>
            <template v-else>
              <span>-</span>
            </template>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum" v-model:limit="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <!-- 添加或修改角色配置对话框 -->
    <el-dialog class="custom-dialog" :title="title" v-model="open" width="500px" append-to-body :close-on-click-modal="false">
      <el-form ref="roleRef" :model="form" :rules="rules" label-width="100px">
        <el-form-item label="角色名称" prop="roleName">
          <el-input v-model.trim="form.roleName" placeholder="请输入角色名称" maxlength="30" show-word-limit/>
        </el-form-item>
        <el-form-item prop="roleKey">
          <template #label>
            <span>
              <el-tooltip content="控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasRole('admin')`)" placement="top">
                <el-icon><question-filled /></el-icon>
              </el-tooltip>
              权限字符
            </span>
          </template>
          <el-input v-model.trim="form.roleKey" placeholder="请输入权限字符" />
        </el-form-item>
        <el-form-item label="角色顺序" prop="roleSort">
          <el-input-number v-model="form.roleSort" controls-position="right" :min="0" />
        </el-form-item>
        <el-form-item label="状态">
          <el-radio-group v-model="form.status">
            <el-radio v-for="item in statusList" :key="item.value" :value="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="菜单权限">
          <el-checkbox v-model="menuExpand" @change="handleCheckedTreeExpand($event, 'menu')">展开/折叠</el-checkbox>
          <el-checkbox v-model="menuNodeAll" @change="handleCheckedTreeNodeAll($event, 'menu')">全选/全不选</el-checkbox>
          <el-checkbox v-model="form.menuCheckStrictly"
            @change="handleCheckedTreeConnect($event, 'menu')">父子联动</el-checkbox>
          <el-tree class="tree-border" :data="menuOptions" show-checkbox ref="menuRef" node-key="id"
            :check-strictly="!form.menuCheckStrictly" empty-text="加载中，请稍候"
            :props="{ label: 'label', children: 'children' }" style="height: 200px; overflow-y: auto"></el-tree>
        </el-form-item>
        <el-form-item label="备注">
          <el-input v-model.trim="form.remark" type="textarea" placeholder="请输入内容"></el-input>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button 
            type="primary" 
            @click="submitForm"
          >确 定</el-button>
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 添加导出确认对话框 -->
    <el-dialog
      class="custom-dialog"
      v-model="exportDialogVisible"
      title="提示"
      width="300px"
      align-center
    >
      <span>确定批量导出?</span>
      <template #footer>
        <span class="dialog-footer">
          <el-button @click="cancelExport">返回</el-button>
          <el-button type="primary" @click="confirmExport">确定</el-button>
        </span>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="Role">
import { addRole, changeRoleStatus, delRole, getRole, listRole, updateRole, exportRole } from "@/api/system/role";
import { roleMenuTreeselect, treeselect as menuTreeselect } from "@/api/system/menu";
import { useRoute, useRouter } from 'vue-router'
import { debounce } from "@/utils/debounce";

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();

const statusList = ref([
  { label: '正常', value: '0' },
  { label: '停用', value: '1' }
])
const roleList = ref([]);
const open = ref(false);
const loading = ref(true);
const showSearch = ref(true);
const ids = ref([]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");
const dateRange = ref([]);
const menuOptions = ref([]);
const menuExpand = ref(false);
const menuNodeAll = ref(false);
const menuRef = ref(null);
const isExternalPark = ref(false);
const exportDialogVisible = ref(false)

/** 数据范围选项*/
const dataScopeOptions = ref([
  { value: "1", label: "全部数据权限" },
  { value: "2", label: "自定数据权限" },
  { value: "3", label: "本部门数据权限" },
  { value: "4", label: "本部门及以下数据权限" },
  { value: "5", label: "仅本人数据权限" }
]);

const queryParams = ref({
  pageNum: 1,
  pageSize: 10,
  roleName: '',
  roleKey: '',
  status: '',
  beginTime: '',
  endTime: ''
});

const data = reactive({
  form: {},
  rules: {
    roleName: [{ required: true, message: "角色名称不能为空", trigger: "blur" }],
    roleKey: [{ required: true, message: "权限字符不能为空", trigger: "blur" }],
    roleSort: [{ required: true, message: "角色顺序不能为空", trigger: "blur" }]
  },
});

const { form, rules } = toRefs(data);

/** 查询角色列表 */
function getList() {
  loading.value = true;
  let params = proxy.addDateRange(queryParams.value, dateRange.value);
  
  listRole(params).then(response => {
    roleList.value = response.data.rows;
    total.value = response.data.total;
    loading.value = false;
  });
}

// 查询按钮操作
const handleQuery = debounce(() => {
  queryParams.value.pageNum = 1;
  getList();
}, 200);

// 重置按钮操作
const resetQuery = debounce(() => {
  dateRange.value = [];
  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    roleName: '',
    roleKey: '',
    status: '',
    beginTime: '',
    endTime: ''
  };
  getList();
}, 200);

/** 删除按钮操作 */
function handleDelete(row) {
  const roleIds = row.roleId || ids.value;
  proxy.$modal.confirm('是否确认删除角色编号为"' + roleIds + '"的数据项?').then(function () {
    return delRole(roleIds);
  }).then(() => {
    getList();
    proxy.$modal.msgSuccess("删除成功");
  }).catch(() => { });
}
/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/role/export", {
    ...queryParams.value,
  }, `role_${new Date().getTime()}.xls`);
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.roleId);
  single.value = selection.length != 1;
  multiple.value = !selection.length;
}
/** 角色状态修改 */
function handleStatusChange(row) {
  let text = row.status === "0" ? "启用" : "停用";
  proxy.$modal.confirm('确认要' + text + '"' + row.roleName + '"角色吗?').then(function () {
    return changeRoleStatus(row.roleId, row.status);
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功");
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0";
  });
}
/** 分配用户 */
function handleAuthUser(row) {
  router.push("/system/role-auth/user/" + row.roleId);
}
/** 查询菜单树结构 */
function getMenuTreeselect() {
  menuTreeselect().then(response => {
    menuOptions.value = response.data;
  });
}
/** 重置新增的表单以及其他数据  */
function reset() {
  if (menuRef.value != undefined) {
    menuRef.value.setCheckedKeys([]);
  }
  menuExpand.value = false;
  menuNodeAll.value = false;
  form.value = {
    roleId: undefined,
    roleName: undefined,
    roleKey: undefined,
    roleSort: 0,
    status: "0",
    menuIds: [],
    menuCheckStrictly: true,
    remark: undefined
  };
  proxy.resetForm("roleRef");
}
/** 添加角色 */
function handleAdd() {
  reset();
  getMenuTreeselect();
  open.value = true;
  title.value = "添加角色";
}
/** 修改角色 */
function handleUpdate(row) {
  reset();
  const roleId = row.roleId || ids.value;
  const roleMenu = getRoleMenuTreeselect(roleId);
  getRole(roleId).then(response => {
    form.value = response.data;
    form.value.roleSort = Number(form.value.roleSort);
    open.value = true;
    nextTick(() => {
      roleMenu.then((res) => {
        // console.log('roleMenu => ', res)
        let checkedKeys = res.data.checkedKeys;
        // console.log('checkedKeys => ', res.data.checkedKeys)
        checkedKeys.forEach((v) => {
          nextTick(() => {
            menuRef.value.setChecked(v, true, false);
          });
        });
      });
    });
    title.value = "修改角色";
  });
}
/** 根据角色ID查询菜单树结构 */
function getRoleMenuTreeselect(roleId) {
  return roleMenuTreeselect(roleId).then(response => {
    menuOptions.value = response.data.menus;
    return response;
  });
}
/** 树权限（展开/折叠）*/
function handleCheckedTreeExpand(value, type) {
  let treeList = menuOptions.value;
  for (let i = 0; i < treeList.length; i++) {
    menuRef.value.store.nodesMap[treeList[i].id].expanded = value;
  }

}
/** 树权限（全选/全不选） */
function handleCheckedTreeNodeAll(value, type) {
  menuRef.value.setCheckedNodes(value ? menuOptions.value : []);

}
/** 树权限（父子联动） */
function handleCheckedTreeConnect(value, type) {
  form.value.menuCheckStrictly = value ? true : false;

}
/** 所有菜单节点数据 */
function getMenuAllCheckedKeys() {
  // 目前被选中的菜单节点
  let checkedKeys = menuRef.value.getCheckedKeys();
  // 半选中的菜单节点
  let halfCheckedKeys = menuRef.value.getHalfCheckedKeys();
  checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
  return checkedKeys;
}

// 添加节流函数
const throttle = (fn, delay = 1000) => {
  let timer = null;
  let isExecuting = false;  // 添加执行标志位

  return async function (...args) {
    if (timer || isExecuting) return;  // 如果正在执行或等待中，直接返回
    
    isExecuting = true;  // 设置执行标志
    timer = setTimeout(() => {
      timer = null;
    }, delay);

    try {
      await fn.apply(this, args);
    } finally {
      isExecuting = false;  // 确保执行完成后重置标志位
    }
  };
};

/** 提交按钮 */
const submitForm = throttle(async () => {
  try {
    const valid = await proxy.$refs["roleRef"].validate();
    if (valid) {
      if (form.value.roleId != undefined) {
        form.value.menuIds = getMenuAllCheckedKeys();
        await updateRole(form.value);
        proxy.$modal.msgSuccess("修改成功");
        open.value = false;
        await getList();
      } else {
        form.value.menuIds = getMenuAllCheckedKeys();
        await addRole(form.value);
        proxy.$modal.msgSuccess("新增成功");
        open.value = false;
        await getList();
      }
    }
  } catch (error) {
    // console.error('提交出错:', error);
  }
}, 2500);  // 设置1.5秒的节流时间

/** 取消按钮 */
function cancel() {
  open.value = false;
  reset();
}

// 修改监听函数
function handleParkChange(event) {
  if (route.path === '/system/role') {
    if (event.detail.type === 'select') {
      isExternalPark.value = true;
    } else if (event.detail.type === 'clear') {
      isExternalPark.value = false;
    }
    getList();
  }
}

// 初始化时检查状态
function initParkState() {
  const selectedParkData = localStorage.getItem('selectedParkData');
  isExternalPark.value = !!selectedParkData;
}

// 添加导出相关函数
function handleBatchExport() {
  if (ids.value.length === 0) {
    proxy.$modal.msgWarning('请至少选择一条数据')
    return
  }
  proxy.$modal.confirm('确定批量导出？').then(() => {
    const roleIds = ids.value
    exportRole(roleIds).then(res => {
      if (res instanceof Blob) {
        const blob = res
        const filename = `角色信息.xlsx`
        
        // 创建下载链接并触发下载
        const link = document.createElement('a')
        link.href = window.URL.createObjectURL(blob)
        link.download = filename
        link.click()
        window.URL.revokeObjectURL(link.href)
        
        proxy.$modal.msgSuccess('导出成功')
      } else {
        proxy.$modal.msgError('导出失败：返回格式错误')
      }
    }).catch(error => {
      proxy.$modal.msgError('导出失败')
    })
  })
}

function confirmExport() {
  exportDialogVisible.value = false
}

function cancelExport() {
  exportDialogVisible.value = false
}

onMounted(() => {
  initParkState();
  getList();
  window.addEventListener('parkChange', handleParkChange);
});

onUnmounted(() => {
  window.removeEventListener('parkChange', handleParkChange);
});
</script>

