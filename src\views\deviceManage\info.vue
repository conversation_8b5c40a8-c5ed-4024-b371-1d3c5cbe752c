<template>
  <div class="ledgerInfo app-container">
    <div
      id="codeImg"
      style="position: absolute; bottom: 0; left: -99999999999px"
    >
      <div id="qrcode"></div>
    </div>
    <el-card shadow="never" class="page-card" v-loading="loadingPage">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        :validate-on-rule-change="false"
      >
        <el-descriptions title="" border :column="2">
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="设备名称"
          >
            <el-form-item label="" prop="deviceName">
              <el-input
                :placeholder="isEdit ? '请输入设备名称' : '-'"
                v-model="form.deviceName"
                style="width: 100%"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="设备编码"
          >
            <el-form-item label="" prop="deviceCode">
              <el-input
                placeholder="-"
                v-model="form.deviceCode"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="设备类型"
          >
            <el-form-item label="" prop="typeId">
              <el-select
                v-model="form.typeId"
                :placeholder="isEdit ? '请选择设备类型' : '-'"
                filterable
                clearable
                style="width: 100%"
                :disabled="
                  (form.deviceType == '智慧大屏' && !!form.deviceId) || !isEdit
                "
                @change="handleChangeType"
              >
                <el-option
                  v-for="item in typeList_disabled"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                  :disabled="item.disabled || !!item.status"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备标签"
          >
            <el-form-item label="" prop="tagIdList">
              <el-select
                v-model="form.tagIdList"
                :placeholder="isEdit ? '请选择设备标签' : '-'"
                style="width: 100%"
                multiple
                clearable
                :disabled="!isEdit"
                :multiple-limit="5"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tag"
                  :value="item.tagId"
                />
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="安装位置"
          >
            <el-form-item label="" prop="installAddressId">
              <el-input
                v-if="!isEdit"
                placeholder="-"
                v-model="form.installAddress"
                disabled
                style="width: 100%"
                :title="form.installAddress"
              />
              <el-tree-select
                v-else
                v-model="form.installAddressId"
                :props="positionProps"
                :data="positionTreeList_disabled"
                placeholder="请选择安装位置"
                :render-after-expand="false"
                clearable
                check-strictly
                :expand-on-click-node="false"
                style="width: 100%"
                @change="handlePosition"
              >
              </el-tree-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="资产端口类别"
          >
            <el-form-item label="" prop="portTypeId">
              <el-select
                v-model="form.portTypeId"
                :placeholder="isEdit ? '请选择资产端口类别' : '-'"
                style="width: 100%"
                clearable
                :disabled="!isEdit"
              >
                <el-option
                  v-for="item in portTypeList"
                  :key="item.id"
                  :label="item.name"
                  :value="item.id"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产类别"
          >
            <el-form-item label="" prop="infoTypeId">
              <el-tree-select
                v-model="form.infoTypeId"
                :props="assetsProps"
                :data="assetsTreeList_disabled"
                :placeholder="isEdit ? '请选择信息资产类别' : '-'"
                :render-after-expand="false"
                check-strictly
                clearable
                :disabled="!isEdit"
                style="width: 100%"
                @change="handleAssets"
              ></el-tree-select>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="信息资产等级"
          >
            <el-form-item label="" prop="level">
              <el-input
                :placeholder="isEdit ? '请先选择信息资产类别' : '-'"
                v-model="form.level"
                disabled
                style="width: 100%"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="品牌"
          >
            <el-form-item label="" prop="brand">
              <el-input
                v-model="form.brand"
                :placeholder="isEdit ? '请输入设备品牌名称' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="规格型号"
          >
            <el-form-item label="" prop="model">
              <el-input
                v-model="form.model"
                :placeholder="isEdit ? '请输入规格型号' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="物理地址"
          >
            <el-form-item label="" prop="macAddress">
              <el-input
                v-model="form.macAddress"
                :placeholder="isEdit ? '请输入物理地址' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="逻辑地址"
          >
            <el-form-item label="" prop="logicAddress">
              <el-input
                v-model="form.logicAddress"
                :placeholder="isEdit ? '请输入逻辑地址' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="IP地址"
          >
            <el-form-item label="" prop="ipAddress">
              <el-input
                v-model="form.ipAddress"
                :placeholder="isEdit ? '请输入IP地址' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="内存"
          >
            <el-form-item label="" prop="internalStorageNumber">
              <div class="unitFlex">
                <el-input-number
                  :min="0"
                  :max="999999999"
                  style="width: 180px"
                  :disabled="!isEdit"
                  v-model="form.internalStorageNumber"
                  :precision="0"
                  :step="1"
                  @keydown="SendEventOne"
                />
                <el-select
                  v-model="form.internalStorageUnit"
                  style="width: 120px"
                  :disabled="!isEdit"
                >
                  <el-option
                    v-for="item in sizeList"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </div>
              <!-- <el-input
                v-model="form.internalStorage"
                :placeholder="isEdit ? '请输入设备内存' : '-'"
                
                maxlength="50"
              /> -->
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="CPU"
          >
            <el-form-item label="" prop="cpu">
              <el-input
                v-model="form.cpu"
                :placeholder="isEdit ? '请输入CPU型号' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="操作系统"
          >
            <el-form-item label="" prop="osVersion">
              <el-input
                v-model="form.osVersion"
                :placeholder="isEdit ? '请输入操作系统版本号' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="硬盘"
          >
            <el-form-item label="" prop="diskNumber">
              <div class="unitFlex">
                <el-input-number
                  :min="0"
                  :max="999999999"
                  style="width: 180px"
                  :disabled="!isEdit"
                  v-model="form.diskNumber"
                  :precision="0"
                  :step="1"
                  @keydown="SendEventOne"
                />
                <el-select
                  v-model="form.diskUnit"
                  style="width: 120px"
                  :disabled="!isEdit"
                >
                  <el-option
                    v-for="item in sizeList"
                    :label="item"
                    :value="item"
                  />
                </el-select>
              </div>
              <!-- <el-input
                v-model="form.disk"
                :placeholder="isEdit ? '请输入设备硬盘' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              /> -->
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width required"
            class-name="value-width"
            label="入库时间"
          >
            <el-form-item label="" prop="putTime">
              <el-date-picker
                v-model="form.putTime"
                format="YYYY-MM-DD"
                :disabled-date="disabledDateFn"
                value-format="YYYY-MM-DD"
                style="width: 100%"
                type="date"
                :placeholder="isEdit ? '请选择入库时间' : '-'"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="端口"
          >
            <el-form-item label="" prop="port">
              <el-input
                v-model="form.port"
                :placeholder="isEdit ? '请输入端口' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="线缆"
          >
            <el-form-item label="" prop="cable">
              <el-input
                v-model="form.cable"
                :placeholder="isEdit ? '请输入线缆信息' : '-'"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="边缘服务器"
          >
            <el-form-item label="" prop="ralayHost">
              <el-input
                v-model="form.ralayHost"
                :placeholder="isEdit ? '请输入边缘服务器的ip地址' : '-'"
                style="width: 100%"
                :disabled="!isEdit"
                maxlength="50"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备状态"
          >
            <el-tag :type="statusObj[form.deviceStatus].type" size="large">{{
              statusObj[form.deviceStatus].label
            }}</el-tag>
          </el-descriptions-item>
          <el-descriptions-item
            v-if="form.deviceStatus < 2"
            label-class-name="label-width"
            class-name="value-width"
            label="设备二维码"
          >
            <el-form-item label="" prop="deviceQrCode">
              <div class="flex items-center">
                <el-button type="primary" @click="handleViewQrCode">
                  查看二维码
                </el-button>
                <a
                  class="download-link ml-4"
                  @click="handleDownloadQrCode"
                  style="color: red"
                >
                  下载
                </a>
              </div>
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备图片"
          >
            <el-form-item label="" prop="deviceImg">
              <imgUpload
                @update:modelValue="
                  (url) => {
                    form.deviceImg = url;
                  }
                "
                :limit="1"
                :modelValue="form.deviceImg"
                :isShowTip="isEdit"
                :disabled="!isEdit"
              />
            </el-form-item>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备能否正常使用"
          >
            <span>{{
              form.isNormal == 1 ? "不能正常使用" : "能正常使用"
            }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="设备机器码"
            v-if="useUserStore().isCommon"
          >
            <span>{{ form.machineCode || "-" }}</span>
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="激活日期"
            v-if="useUserStore().isCommon"
          >
            <span>{{
              form.activeTime ? form.activeTime.split(" ")[0] : "-"
            }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <div
          v-show="isEdit && form.deviceStatus < 3"
          style="margin: 20px 0 0; text-align: right"
        >
          <el-button type="danger" @click="handleDestroy">报废设备</el-button>
        </div>

        <div class="ledgerInfo-tit" style="">关联配置</div>
        <el-form-item label="关联设备">
          <el-input
            v-model="form.associatedDevice"
            style="width: 100%"
            readonly
            type="textarea"
            :rows="1"
            :disabled="!isEdit"
            :placeholder="isEdit ? '点击选择关联设备' : '-'"
            @click="handleDialog(0)"
          />
        </el-form-item>
        <el-form-item label="关联人员">
          <el-input
            v-model="form.associatedPeople"
            style="width: 100%"
            readonly
            type="textarea"
            :rows="1"
            :disabled="!isEdit"
            :placeholder="isEdit ? '点击选择关联人员' : '-'"
            @click="handleDialog(1)"
          />
        </el-form-item>
      </el-form>

      <!-- 备件表格 -->
      <div class="ledgerInfo-tit">备件使用情况</div>
      <el-table :data="spareList" border>
        <el-table-column
          label="备件编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="sparePartsCode"
        />
        <el-table-column
          label="备件名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="name"
        />
        <el-table-column
          label="单据编号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="detailsCode"
        />
        <el-table-column
          label="单据来源"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="resource"
        />
        <el-table-column
          label="消耗量"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="num"
        />
        <el-table-column
          label="操作"
          align="center"
          show-overflow-tooltip
          minWidth="100px"
        >
          <template #default="scope">
            <el-button
              link
              type="primary"
              @click="handleSpareDetail(scope.row.sparePartsId)"
              >查看详情</el-button
            >
          </template>
        </el-table-column>
      </el-table>
      <pagination
        v-show="spareTotal > 0"
        :total="spareTotal"
        v-model:page="queryParamsSpare.pageNum"
        v-model:limit="queryParamsSpare.pageSize"
        @pagination="getSpareList"
        :background="false"
        layout="total,prev,pager,next"
        :autoScroll="false"
      />

      <!-- 添加报废情况描述列表 -->
      <template v-if="form.deviceStatus === 4">
        <div class="ledgerInfo-tit">报废情况</div>
        <el-descriptions border :column="2">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报废原因"
          >
            <span>{{ form.scrapReason || "-" }}</span>
          </el-descriptions-item>
        </el-descriptions>
        <el-descriptions border :column="4">
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报废时间"
          >
            {{ form.scrapTime || "-" }}
          </el-descriptions-item>
          <el-descriptions-item
            label-class-name="label-width"
            class-name="value-width"
            label="报废提交人"
          >
            {{ form.scrapUser || "-" }}
          </el-descriptions-item>
        </el-descriptions>
      </template>

      <div class="ledgerInfo-btns">
        <el-button
          type="primary"
          v-if="form.deviceStatus < 3 && route.query.from !== '4'"
          @click="handleSubmit"
          v-throttle
          >{{ isEdit ? "点击提交" : "编辑" }}</el-button
        >
        <el-button @click="handleBack">返回</el-button>
      </div>

      <div v-if="recordList.length > 0">
        <div class="ledgerInfo-tit" style="margin-bottom: 10px">变更信息</div>
        <div
          class="ledgerInfo-record"
          v-for="(item, index) in recordList.slice(0, 5)"
          :key="index"
        >
          <span v-if="item.troubleId">
            {{ `${item.name} ${item.role} 于${item.operationTime} 因 工单` }}
            <span
              style="
                color: #4095e5;
                text-decoration: underline;
                cursor: pointer;
              "
              @click="goTaskDetail(item.troubleId)"
              >{{ `${item.remark}` }}</span
            >
            {{ ` 更新了设备信息` }}
          </span>
          <span v-if="!item.troubleId">
            {{
              `${item.name} ${item.role || ""} 于${item.operationTime} 因 ${
                item.remark.indexOf("报废") != -1 ? "" : "编辑"
              }${item.remark} 更新了设备信息`
            }}
          </span>
        </div>
      </div>
    </el-card>

    <!-- 选择设备/人员弹窗 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="dialogVisible"
      width="800"
      @close="handleCancel"
    >
      <el-form
        class="search-list"
        :inline="true"
        :model="queryParams"
        ref="queryRef"
        @submit.native.prevent
      >
        <div v-if="title == '选择关联设备'" style="display: inline-block">
          <el-form-item prop="deviceCode">
            <el-input
              v-model="queryParams.deviceCode"
              placeholder="请输入设备编号"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item prop="deviceName">
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
          <el-form-item label="" prop="positionId">
            <el-tree-select
              v-model="queryParams.positionId"
              :props="positionProps"
              :data="positionTreeList"
              placeholder="请选择安装位置"
              :render-after-expand="false"
              style="width: 200px"
              clearable
              check-strictly
              @change="handleQueryPosition"
            />
          </el-form-item>
        </div>
        <div v-if="title == '选择关联人员'" style="display: inline-block">
          <el-form-item prop="nickName">
            <el-input
              v-model="queryParams.nickName"
              placeholder="请输入人员名称"
              clearable
              style="width: 200px"
              @keyup.enter="handleQuery"
            />
          </el-form-item>
        </div>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="loading"
        ref="tableRef"
        :data="tableList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <template v-if="title == '选择关联设备'">
          <el-table-column
            type="selection"
            align="center"
            width="70"
            fixed="left"
          />
          <el-table-column
            label="设备编号"
            prop="deviceCode"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="设备名称"
            prop="deviceName"
            show-overflow-tooltip
            min-width="120"
          />
          <el-table-column
            label="设备类别"
            prop="deviceType"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="安装位置"
            align="center"
            show-overflow-tooltip
            minWidth="120px"
            prop="installAddress"
          />
          <el-table-column
            label="设备状态"
            align="center"
            show-overflow-tooltip
            minWidth="80px"
          >
            <template #default="scope">
              <el-tag
                v-if="statusObj[scope.row.status]"
                effect="dark"
                :type="statusObj[scope.row.status].type"
                >{{ statusObj[scope.row.status].label }}</el-tag
              >
            </template>
          </el-table-column>
        </template>
        <template v-if="title == '选择关联人员'">
          <el-table-column
            type="selection"
            align="center"
            width="70"
            fixed="left"
          />
          <el-table-column
            label="人员编号"
            prop="userName"
            show-overflow-tooltip
            min-width="100"
          />
          <el-table-column
            label="人员姓名"
            prop="nickName"
            show-overflow-tooltip
            min-width="120"
          />
        </template>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.pageNum"
        v-model:limit="queryParams.pageSize"
        @pagination="getList"
        :background="false"
        layout="total, prev, pager, next"
        :autoScroll="false"
      />
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取消</el-button>
          <el-button type="primary" @click="submitForm">选择</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 查看二维码弹窗-->
    <el-dialog
      title="设备二维码"
      v-model="qrCodeDialogVisible"
      width="360px"
      append-to-body
      :align-center="true"
    >
      <div style="display: flex; justify-content: center; padding: 20px">
        <img :src="qrCodeImg" alt="设备二维码" style="max-width: 250px" />
      </div>
    </el-dialog>

    <!-- 添加报废对话框 -->
    <el-dialog
      class="custom-dialog"
      title="报废设备"
      v-model="scrapDialogVisible"
      width="500px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form
        ref="scrapFormRef"
        :model="scrapForm"
        :rules="scrapRules"
        label-width="100px"
      >
        <el-form-item label="报废原因" prop="scrapReason">
          <el-input
            v-model="scrapForm.scrapReason"
            type="textarea"
            placeholder="请输入报废原因"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
        <el-form-item label="报废时间" prop="scrapTime">
          <el-date-picker
            v-model="scrapForm.scrapTime"
            type="datetime"
            placeholder="请选择报废时间"
            format="YYYY-MM-DD HH:mm:ss"
            value-format="YYYY-MM-DD HH:mm:ss"
            style="width: 100%"
            :disabled-date="disabledScrapDateFn"
          />
        </el-form-item>
        <el-form-item label="报废提交人" prop="scrapSubmitterId">
          <el-select
            v-model="scrapForm.scrapSubmitterId"
            placeholder="请选择报废提交人"
            style="width: 100%"
            filterable
            clearable
            disabled
            @change="scrapUserChange"
          >
            <el-option
              v-for="item in userList"
              :key="item.userId"
              :label="item.nickName"
              :value="item.userId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitScrapForm">确 定</el-button>
          <el-button @click="scrapDialogVisible = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>
      
  <script setup>
import { ref, onMounted, reactive, getCurrentInstance, nextTick } from "vue";
import imgUpload from "@/components/ImageUpload";
import { generateQRCode, autoPicture } from "@/utils/uploadCode2";
import QRCode from "qrcode";
import { wrapText } from "@/utils/uploadCode";
import { useRouter, useRoute } from "vue-router";
import {
  treeToArray,
  treeFindPath,
  findIndexInObejctArr,
  timeFormat,
  extractNumbers,
  extractWords,
  scrollToErrorField,
} from "@/utils";
import {
  scrapDevice,
  deviceSparePage,
  deviceInfo,
  deviceEdit,
  devicePage,
  deviceRelatePage,
} from "@/api/mediaTeach/ledger";
import { getDeviceType } from "@/api/mediaTeach/type";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import {
  schoolAssetsTypeTree,
  schoolAssetsPortTypeList,
} from "@/api/mediaTeach/assets";
import { getPositionTree } from "@/api/mediaTeach/position";
import { getUserByRole } from "@/api/system/user";
import useUserStore from "@/store/modules/user";

const { proxy } = getCurrentInstance();
const router = useRouter();
const route = useRoute();

const state = reactive({
  sizeList: ["KB", "MB", "GB", "TB"],
  loadingPage: false,
  qrCodeImg: "",
  qrCodeDialogVisible: false,
  isEdit: false,
  recordList: [],
  queryRef: null,
  tableRef: null,
  formRef: null,
  associatedDeviceRow: [],
  associatedPeopleRow: [],
  form: {
    installAddressId: "",
    deviceStatus: 4,
    internalStorageNumber: 0,
    internalStorageUnit: "GB",
    diskNumber: 0,
    diskUnit: "GB",
    associatedDevice: "",
    associatedPeople: "",
    associatedDeviceList: [],
    associatedDeviceIds: [],
    associatedPeopleList: [],
    associatedPeopleIds: [],
  },
  loading: false,
  dialogVisible: false,
  deviceList: [],
  personList: [],
  title: "",
  total: 0,
  spareTotal: 0,
  spareList: [],
  queryParamsSpare: {
    pageNum: 1,
    pageSize: 5,
  },
  queryParams: {
    pageNum: 1,
    pageSize: 5,
    deviceId: route.query.id,
    deviceCode: "",
    deviceName: "",
    positionId: "",
    nickName: "",
  },
  allDeviceList: [],
  allPeopleList: [],
  tagList: [],
  typeList: [],
  tableList: [],
  tableList_all: [],
  tableRadio: [],
  tableAllSelectedId: [],
  tableAllSelectedRow: [],
  portTypeList: [],
  typeList_disabled: [],
  positionTreeList: [],
  positionTreeList_disabled: [],
  assetsTreeList: [],
  assetsTreeList_disabled: [],
  assetsList: [],
  positionList: [],
  queryPositionResult: {
    names: [],
    ids: [],
  },
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  assetsProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  positionResult: {
    names: [],
    ids: [],
  },
  rules: {},
  assetsResult: {
    names: [],
    ids: [],
  },
  statusObj: {
    0: { label: "正常", type: "success" },
    1: { label: "维修中", type: "danger" },
    2: { label: "报障中", type: "warning" },
    3: { label: "待审核", type: "primary" },
    4: { label: "已报废", type: "info" },
  },
  baseRules: {
    deviceName: [
      { required: true, message: "设备名称不能为空", trigger: "blur" },
      { max: 50, message: "设备名称最多输入50个字符", trigger: "blur" },
    ],
    typeId: [{ required: true, message: "设备类型不能为空", trigger: "blur" }],
    deviceCode: [
      { required: true, message: "设备编码不能为空", trigger: "blur" },
    ],
    deviceStatus: [
      {
        required: true,
        message: "设备状态不能为空",
        trigger: ["blur", "change"],
      },
    ],
    // deviceImg: [
    //   {
    //     required: true,
    //     message: "设备照片不能为空",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    installAddressId: [
      {
        required: true,
        message: "安装位置不能为空",
        trigger: ["blur", "change"],
      },
    ],
    // model: [{ required: true, message: "规格型号不能为空", trigger: "blur" }],
    putTime: [
      {
        required: true,
        message: "入库时间不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
  smartRules: {
    // tagIdList: [
    //   {
    //     required: true,
    //     message: "设备标签不能为空",
    //     trigger: ["blur", "change"],
    //   },
    // ],
    macAddress: [
      { required: true, message: "物理地址不能为空", trigger: "blur" },
    ],
    logicAddress: [
      { required: true, message: "逻辑地址不能为空", trigger: "blur" },
    ],
    osVersion: [
      { required: true, message: "操作系统版本号不能为空", trigger: "blur" },
    ],
    cpu: [{ required: true, message: "CPU类型不能为空", trigger: "blur" }],
    // internalStorage: [
    //   { required: true, message: "设备内存不能为空", trigger: "blur" },
    // ],
    ipAddress: [{ required: true, message: "IP地址不能为空", trigger: "blur" }],
    // ralayHost: [
    //   { required: true, message: "边缘服务器ip不能为空", trigger: "blur" },
    // ],
    // brand: [{ required: true, message: "设备品牌不能为空", trigger: "blur" }],
    // disk: [{ required: true, message: "设备硬盘不能为空", trigger: "blur" }],
  },
  scrapDialogVisible: false,
  userList: [],
  scrapForm: {
    scrapReason: "",
    scrapTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    scrapSubmitterId: undefined,
    scrapSubmitter: "",
  },
  scrapRules: {
    scrapReason: [
      { required: true, message: "报废原因不能为空", trigger: "blur" },
      { max: 200, message: "报废原因最多输入200个字符", trigger: "blur" },
    ],
    scrapTime: [
      {
        required: true,
        validator: validateTime,
        trigger: ["blur", "change"],
      },
    ],
    scrapSubmitterId: [
      {
        required: true,
        message: "报废提交人不能为空",
        trigger: ["blur", "change"],
      },
    ],
  },
});
const {
  sizeList,
  loadingPage,
  allPeopleList,
  allDeviceList,
  qrCodeImg,
  qrCodeDialogVisible,
  spareList,
  spareTotal,
  queryParamsSpare,
  isEdit,
  recordList,
  associatedDeviceRow,
  associatedPeopleRow,
  queryRef,
  rules,
  tableRef,
  loading,
  baseRules,
  smartRules,
  formRef,
  form,
  dialogVisible,
  queryPositionResult,
  tableList,
  tableList_all,
  tableAllSelectedId,
  tableAllSelectedRow,
  tableRadio,
  deviceList,
  personList,
  positionResult,
  positionProps,
  assetsProps,
  positionList,
  portTypeList,
  typeList,
  tagList,
  typeList_disabled,
  positionTreeList,
  positionTreeList_disabled,
  assetsTreeList,
  assetsResult,
  assetsList,
  assetsTreeList_disabled,
  title,
  total,
  queryParams,
  statusObj,
  scrapDialogVisible,
  scrapForm,
  scrapRules,
  userList,
} = toRefs(state);

let checkName = computed(() =>
  state.title == "选择关联设备" ? "deviceId" : "userId"
);

function goTaskDetail(id) {
  router.push({
    path: "/taskManage/taskCenter/handleTaskInfo",
    query: { id, deviceId: route.query.id, form: route.query.form },
  });
}

function SendEventOne(e) {
  if (e.key === "." || e.key === "," || e.key === "-" || e.key === "+") {
    e.preventDefault();
  }
}

function validateTime(rule, value, callback) {
  if (!value) {
    callback(new Error("报废时间不能为空"));
  } else if (new Date(value).getTime() > new Date().getTime()) {
    callback(new Error("报废时间不能选择未来时间"));
  } else {
    callback();
  }
}

const handleSpareDetail = (id) => {
  router.push({
    path: "/spareManage/spareInfo",
    query: {
      id,
      deviceId: route.query.id,
      // type: route.query.type,
      // from: route.query.from
    },
  });
};

const handleDownloadQrCode2 = () => {
  clearTimeout(timer);
  timer = setTimeout(() => {
    proxy.$modal.loading("正在下载...");

    generateQRCode(`${state.form.deviceCode}`, "设备", true);
    setTimeout(() => {
      proxy.$modal.closeLoading();
    }, 150);
  }, 150);
};
// 下载二维码方法
let timer = null;
function handleDownloadQrCode() {
  clearTimeout(timer);
  timer = setTimeout(() => {
    proxy.$modal.loading("正在下载...");
    const deviceData = [
      {
        deviceCode: form.value.deviceCode,
        deviceName: form.value.deviceName,
        installAddress: form.value.installAddress,
      },
    ];

    generateQRCode(
      deviceData,
      false,
      `${form.value.deviceName}-设备二维码.png`
    );
    setTimeout(() => {
      proxy.$modal.closeLoading();
    }, 150);
  }, 150);
}

const handleViewQrCode2 = () => {
  state.qrCodeImg = generateQRCode(`${state.form.deviceCode}`, "设备", false);
  state.qrCodeDialogVisible = true;
  console.log("base64", state.qrCodeImg);
};

// 添加生成单个二维码的方法
async function generateSingleQRCode(deviceData) {
  const { deviceCode, deviceName, installAddress } = deviceData;

  try {
    // 显著减小二维码尺寸
    const qrUrl = await QRCode.toDataURL(deviceCode, {
      width: 128, // 调整为更小的尺寸
      margin: 1, // 减小边距
      scale: 4, // 保持清晰度
    });

    const canvas = document.createElement("canvas");
    const ctx = canvas.getContext("2d");
    const img = new Image();

    return new Promise((resolve) => {
      img.onload = () => {
        const scale = 2;
        canvas.width = (img.width + 60) * scale; // 继续减小边距
        canvas.height = (img.height + 20 + 15 * 3) * scale;

        ctx.scale(scale, scale);
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = "high";

        ctx.fillStyle = "white";
        ctx.fillRect(0, 0, canvas.width / scale, canvas.height / scale);

        const qrX = (canvas.width / scale - img.width) / 2;
        const qrY = 10;

        ctx.drawImage(img, qrX, qrY);

        const prompt = `设备名称：${deviceName}\n设备编码：${
          deviceCode || "-"
        }\n安装位置：${installAddress || "-"}`;
        ctx.fillStyle = "black";
        ctx.font = "bold 10px Arial"; // 稍微调小字体

        const promptLines = wrapText(ctx, prompt, 150); // 减小文本宽度
        promptLines.forEach((line, index) => {
          const textY = qrY + img.height + 15 + 13 * index; // 调整行间距
          const textWidth = ctx.measureText(line).width;
          const xPosition = (canvas.width / scale - textWidth) / 2;
          ctx.fillText(line, xPosition, textY);
        });

        resolve(canvas.toDataURL("image/png", 1.0));
      };
      img.src = qrUrl;
    });
  } catch (error) {
    console.error("Error generating QR code:", error);
    return null;
  }
}

// 查看二维码方法
async function handleViewQrCode() {
  try {
    const deviceData = {
      deviceCode: state.form.deviceCode,
      deviceName: state.form.deviceName,
      installAddress: state.form.installAddress,
    };

    const qrCodeUrl = await generateSingleQRCode(deviceData);
    if (qrCodeUrl) {
      state.qrCodeImg = qrCodeUrl;
      state.qrCodeDialogVisible = true;
    }
  } catch (error) {
    proxy.$modal.msgError("生成二维码失败");
    console.error(error);
  }
}

const handleDestroy = () => {
  state.scrapDialogVisible = true;
  // 重置表单
  state.scrapForm = {
    scrapReason: "",
    scrapTime: timeFormat(new Date().getTime(), "yyyy-mm-dd hh:MM:ss"),
    scrapSubmitterId: useUserStore().userId,
    scrapSubmitter: useUserStore().nickName,
  };
};

const getSpareList = () => {
  deviceSparePage({
    ...state.queryParamsSpare,
    deviceId: route.query.id,
  }).then((response) => {
    if (response.data) {
      console.log("备件情况", response.data);
      const { records, total } = response.data || [];
      state.spareList = records || [];
      state.spareTotal = total || 0;
    }
  });
};

const handleDialog = (type) => {
  state.title = !!type ? "选择关联人员" : "选择关联设备";
  state.dialogVisible = true;
  nextTick(() => resetQuery());
};

// 关闭弹窗
const handleCancel = () => {
  resetQuery();
  state.tableAllSelectedId = [];
  state.tableAllSelectedRow = [];
  state.tableRadio = [];
  state.dialogVisible = false;
  console.log("form", state.form);
};

// 提交页面表单
const handleSubmit = () => {
  if (!state.isEdit) {
    state.isEdit = true;
    return;
  }
  state.formRef.validate((valid) => {
    if (valid) {
      proxy.$modal.loading();
      state.form.installAddress =
        state.positionResult.names.join("-") || state.form.installAddress;
      state.form.positionId =
        state.positionResult.ids.join(",") || state.form.positionId;
      state.form.assetsTypeName =
        state.assetsResult.names?.join("-") || state.form.assetsTypeName;
      state.form.assetsTypeId =
        state.assetsResult.ids?.join(",") || state.form.assetsTypeId;
      state.form.tagId = !!state.form.tagIdList
        ? state.form.tagIdList.join(",")
        : "";
      state.form.portTypeName = state.form.portTypeId
        ? state.portTypeList.find((_) => _.id == state.form.portTypeId).name
        : "";
      state.form.associatedUserIds = state.form.associatedPeopleIds;
      state.form.internalStorage =
        state.form.internalStorageNumber.toFixed(0) +
        state.form.internalStorageUnit;
      state.form.disk = state.form.diskNumber.toFixed(0) + state.form.diskUnit;
      console.log("提交的页面表单", state.form);
      deviceEdit(state.form)
        .then((response) => {
          proxy.$modal.msgSuccess("修改成功");
          state.isEdit = false;
          getData();
          proxy.$modal.closeLoading();
        })
        .catch(() => proxy.$modal.closeLoading());
    } else {
      scrollToErrorField();
    }
  });
};

// 提交弹窗表单
function submitForm() {
  if (state.tableAllSelectedId.length < 1) {
    proxy.$modal.msgWarning("请至少选择一项");
    return;
  }

  if (state.title == "选择关联人员") {
    state.form.associatedPeopleIds = state.tableAllSelectedId;
    state.associatedPeopleRow = state.tableAllSelectedRow;
    state.form.associatedPeopleList = state.tableAllSelectedRow.reduce(
      (res, cur) => {
        res.push({
          userId: cur.userId,
          nickName: cur.nickName,
        });
        return res;
      },
      []
    );
    state.form.associatedPeople = state.form.associatedPeopleList
      .map((item) => item.nickName)
      .join("、");
  }
  if (state.title == "选择关联设备") {
    state.form.associatedDeviceIds = state.tableAllSelectedId;
    state.associatedDeviceRow = state.tableAllSelectedRow;
    state.form.associatedDeviceList = state.tableAllSelectedRow.reduce(
      (res, cur) => {
        res.push({
          deviceId: cur.deviceId,
          deviceName: cur.deviceName,
        });
        return res;
      },
      []
    );
    state.form.associatedDevice = state.form.associatedDeviceList
      .map((item) => item.deviceName)
      .join("、");
  }
  handleCancel();
}

/** 搜索按钮操作 */
function handleQuery() {
  state.queryParams.current = 1;
  state.queryParams.pageNum = 1;
  state.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  state.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  getList();
}

/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQueryPosition();
}

function getList() {
  state.loading = true;
  if (state.title == "选择关联设备") {
    if (state.form.associatedDeviceIds.length > 0) {
      state.tableAllSelectedId = state.form.associatedDeviceIds;
      state.tableAllSelectedRow = state.associatedDeviceRow;
    }
    const { positionId } = state.queryParams;
    deviceRelatePage({
      ...state.queryParams,
      positionIds: positionId ? [positionId] : null,
      pageNum: 1,
      pageSize: 9999999,
      deviceId: route.query.id,
    }).then((res) => {
      // console.log(res)
      state.tableList_all = res.data?.records || [];
    });
    console.log("关联设备列表传参", {
      ...state.queryParams,
      positionIds: positionId ? [positionId] : null,
    });
    deviceRelatePage({
      ...state.queryParams,
      positionIds: positionId ? [positionId] : null,
    })
      .then((response) => {
        console.log("设备列表", response.data);
        state.total = response.data?.total || 0;
        state.tableList = response.data?.records || [];
        nextTick(() => {
          state.tableList.forEach((item) => {
            if (state.tableAllSelectedId.indexOf(item[checkName.value]) > -1) {
              state.tableRef.toggleRowSelection(item, true);
            } else {
              state.tableRef.toggleRowSelection(item, false);
            }
          });
          state.loading = false;
        });
      })
      .catch(() => (state.loading = false));
  }
  if (state.title == "选择关联人员") {
    if (state.form.associatedPeopleIds.length > 0) {
      state.tableAllSelectedId = state.form.associatedPeopleIds;
      state.tableAllSelectedRow = state.associatedPeopleRow;
    }
    const { pageNum, pageSize, nickName } = state.queryParams;
    getUserByRole({
      nickName,
      pageNum: 1,
      pageSize: 999999,
      roleKey: ["maintain", "maintainManage"],
    }).then((resp) => {
      console.log("全部运维人员", resp);
      if (resp.data) {
        state.tableList_all = resp.data.records || [];
      }
    });
    console.log("运维人员列表传参", { pageNum, pageSize, nickName });
    getUserByRole({
      pageNum,
      pageSize,
      nickName,
      roleKey: ["maintain", "maintainManage"],
    })
      .then((resp) => {
        console.log("运维人员列表", resp.data);
        if (resp.data) {
          const { records = [], total = 0 } = resp.data;
          state.tableList = records;
          state.total = total;
          nextTick(() => {
            state.tableList.forEach((item) => {
              if (
                state.tableAllSelectedId.indexOf(item[checkName.value]) > -1
              ) {
                state.tableRef.toggleRowSelection(item, true);
              } else {
                state.tableRef.toggleRowSelection(item, false);
              }
            });
          });
          state.loading = false;
        }
      })
      .catch(() => (state.loading = false));
  }
}

async function getAllUserList() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: ["maintain", "maintainManage"],
  }).then((resp) => {
    console.log("全部运维人员", resp);
    if (resp.data) {
      state.allPeopleList = resp.data.records || [];
    }
  });
}

async function getAllDeviceList() {
  await deviceRelatePage({
    pageNum: 1,
    pageSize: 9999999,
    deviceId: route.query.id,
  }).then((res) => {
    console.log(res);
    state.allDeviceList = res.data?.records || [];
  });
}

function handleQueryPosition(val) {
  // state.queryPositionResult = {
  //   ids: [],
  //   names: [],
  // };
  // state.queryPositionResult.ids = treeFindPath(
  //   state.positionTreeList,
  //   (d) => d.id == val
  // );
  // const arr = state.queryPositionResult.ids;
  // for (let i = 0; i < arr.length; i++) {
  //   state.queryPositionResult.names[i] = state.positionList.find(
  //     (node) => node.id == arr[i]
  //   ).name;
  // }
  // console.log("queryPositionResult ==> ", state.queryPositionResult);
  // state.queryParams.address = state.queryPositionResult.names.join("-");
  handleQuery();
}

// 根据类型动态修改校验规则
function handleChangeType(val) {
  let name = "";
  state.typeList.forEach((item) => {
    if (item.id == val) name = item.typeName;
  });
  state.rules = {};
  if (name == "智慧大屏") {
    if (state.baseRules.deviceCode[1]) delete state.baseRules.deviceCode[1];
    Object.assign(state.rules, state.baseRules, state.smartRules);
  } else {
    state.baseRules.deviceCode[1] = {
      pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
      message: "设备编码不能以“ZHDP”开头",
      trigger: "blur",
    };
    Object.assign(state.rules, state.baseRules);
  }
  state.formRef.clearValidate();
}

function handlePosition(val) {
  state.positionResult = {
    ids: [],
    names: [],
  };
  state.positionResult.ids = treeFindPath(
    state.positionTreeList,
    (d) => d.id == val
  );
  const arr = state.positionResult.ids;
  for (let i = 0; i < arr.length; i++) {
    state.positionResult.names[i] = state.positionList.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("positionResult ==> ", state.positionResult);
}

function handleAssets(val) {
  state.assetsResult = {
    ids: [],
    names: [],
  };
  state.assetsResult.ids = treeFindPath(
    state.assetsTreeList,
    (d) => d.id == val
  );
  const arr = state.assetsResult.ids;
  for (let i = 0; i < arr.length; i++) {
    state.assetsResult.names[i] = state.assetsList.find(
      (node) => node.id == arr[i]
    ).name;
  }

  if (state.assetsResult.ids.length > 0) {
    let len = state.assetsResult.ids.length;
    state.form.level = state.assetsList.find(
      (_) => _.id == state.assetsResult.ids[len - 1]
    ).level;
  } else {
    state.form.level = "";
  }
  console.log("assetsResult ==> ", state.assetsResult);
}

// 获取端口类别
async function getPortTypeList() {
  await schoolAssetsPortTypeList({ pageNum: 1, pageSize: 999999 }).then(
    (response) => {
      console.log("端口类别", response.data);
      state.portTypeList = response.data?.records || [];
    }
  );
}

// 获取标签
async function getTagList() {
  await getDeviceTag().then((response) => {
    state.tagList = response.data;
  });
}

async function getPositionTreeList() {
  await getPositionTree({}).then((response) => {
    let tree = JSON.parse(JSON.stringify(response.data));
    state.positionTreeList = response.data;
    state.positionTreeList_disabled = addAttr(tree);
    state.positionList = treeToArray(response.data);
  });
}

async function getAssetsTreeList() {
  await schoolAssetsTypeTree({ pageNum: 1, pageSize: 999999 }).then(
    (response) => {
      console.log("信息类别", response.data?.tree);
      state.assetsTreeList = response.data?.tree || [];
      state.assetsList = treeToArray(state.assetsTreeList);
    }
  );
  await schoolAssetsTypeTree({
    pageNum: 1,
    pageSize: 999999,
    status: 0,
    typeId: form.value.infoTypeId,
  }).then((response) => {
    console.log("无禁用信息类别", response.data);
    let tree = JSON.parse(JSON.stringify(response.data || []));
    state.assetsTreeList_disabled = addAttr(tree);
  });
}

function addAttr(data) {
  for (var j = 0; j < data.length; j++) {
    data[j].disabled = data[j].status == 1; //添加title属性
    if (data[j].children.length > 0) {
      addAttr(data[j].children);
    }
  }
  return data;
}

async function getTypeList() {
  await getDeviceType().then((response) => {
    state.typeList = response.data;
    console.log("设备类型", response.data);
    state.typeList_disabled = response.data
      .reduce((res, cur) => {
        res.push({
          ...cur,
          disabled: cur.typeName == "智慧大屏",
        });
        return res;
      }, [])
      .filter((_) => _.status == 0 || _.id == form.value.typeId);
  });
}

// 返回页面
const handleBack = () => {
  let url = [
    "/deviceLedger/ledger",
    "/deviceControl/monitor",
    "/audit",
    "/system/changeRecord",
    "/deviceLedger/recycle",
    `/deviceControl/desktop?deviceId=${route.query.id}&isMqtt=${route.query.isMqtt}`,
  ][route.query.from || 0];
  console.log("url", url);
  proxy.$tab.closeOpenPage(url);
};

// 限制日期
const disabledDateFn = (date) => {
  const today = timeFormat(new Date().getTime(), "yyyy-mm-dd").split("-");
  let curYear = today[0];
  const curMonth = today[1];
  const curDate = today[2];

  if (
    date.getTime() <
      new Date(`${curYear * 1 - 100}-${curMonth}-${curDate}`).getTime() ||
    date.getTime() >
      new Date(`${curYear * 1 + 100}-${curMonth}-${curDate}`).getTime()
  ) {
    return true;
  }
  return false;
};

// 限制报废日期
const disabledScrapDateFn = (date) => {
  const today = timeFormat(new Date().getTime(), "yyyy-mm-dd").split("-");
  let curYear = today[0];
  const curMonth = today[1];
  const curDate = today[2];

  if (
    date.getTime() <
      new Date(`${curYear * 1 - 100}-${curMonth}-${curDate}`).getTime() ||
    date.getTime() > new Date().getTime()
  ) {
    return true;
  }
  return false;
};

/** 单击某行 */
function rowClick(row) {
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(state.tableAllSelectedRow)),
      row,
      checkName.value
    ) > -1
  ) {
    if (state.tableRadio === row) {
      state.tableRadio = [];
      state.tableRef.setCurrentRow(null);
      state.tableRef.toggleRowSelection(row, false);
      const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
      state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      state.tableRadio = row;
      state.tableRef.setCurrentRow(row);
    }
  } else {
    state.tableRadio = row;
    state.tableRef.setCurrentRow(row);
    state.tableRef.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
      state.tableAllSelectedId.push(item[checkName.value]);
      state.tableAllSelectedRow.push(item);
    }
  });
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = state.tableAllSelectedId.indexOf(row[checkName.value]);
    state.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    state.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = state.tableList;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item[checkName.value] === a[0][checkName.value]) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    state.tableList_all.forEach((item) => {
      if (state.tableAllSelectedId.indexOf(item[checkName.value]) === -1) {
        state.tableAllSelectedId.push(item[checkName.value]); // 如果点击全选就保存全部的id
        state.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', state.tableList_all)
  } else {
    // 切换成了非全选状态
    state.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    state.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

async function getData() {
  state.loadingPage = true;
  await getAllUserList();
  await getAllDeviceList();
  await getPositionTreeList();
  await getUsers();
  getTagList();
  getPortTypeList();
  getSpareList();

  deviceInfo({ id: route.query.id })
    .then((response) => {
      if (response.data) {
        const {
          positionId,
          deviceType,
          assetsTypeId,
          deviceStatus,
          operationLogList,
          disk,
          internalStorage,
          associatedDevice,
          associatedPeople,
        } = response.data;
        if (route.query.type == 1) {
          state.isEdit = deviceStatus < 3;
        }
        state.rules = {};
        if (deviceType == "智慧大屏") {
          if (state.baseRules.deviceCode[1])
            delete state.baseRules.deviceCode[1];
          Object.assign(state.rules, state.baseRules, state.smartRules);
        } else {
          state.baseRules.deviceCode[1] = {
            pattern: /^(?!((Z|z)(H|h)(D|d)(P|p))).*/,
            message: "设备编码不能以“ZHDP”开头",
            trigger: "blur",
          };
          Object.assign(state.rules, state.baseRules);
        }
        const arr = !!positionId ? positionId.split(",") : [];
        const arr2 = !!assetsTypeId ? assetsTypeId.split(",") : [];
        state.positionResult.ids = JSON.parse(JSON.stringify(arr));
        state.assetsResult.ids = JSON.parse(JSON.stringify(arr2));
        Object.assign(state.form, response.data);
        state.form.installAddressId =
          arr.length > 0 ? arr[arr.length - 1] * 1 : "";
        state.form.infoTypeId = arr2.length > 0 ? arr2[arr2.length - 1] : "";

        let arrDevice = state.form.associatedDevice?.split(",") || [],
          arrPeople = state.form.associatedPeople?.split(",") || [];
        state.form.associatedDevice = arrDevice.join("、");
        state.form.associatedPeople = arrPeople.join("、");

        state.form.internalStorageNumber = extractNumbers(internalStorage) || 0;
        state.form.internalStorageUnit = extractWords(internalStorage) || "GB";
        state.form.diskNumber = extractNumbers(disk) || 0;
        state.form.diskUnit = extractWords(disk) || "GB";

        state.form.associatedPeopleIds = [];
        state.form.associatedPeopleList = [];
        state.associatedPeopleRow = [];
        state.form.associatedDeviceIds = [];
        state.form.associatedDeviceList = [];
        state.associatedDeviceRow = [];

        arrDevice.map((item) => {
          let obj = state.allDeviceList.find((_) => _.deviceName == item) || "";
          if (obj) {
            state.form.associatedDeviceIds.push(obj.deviceId);
            state.form.associatedDeviceList.push(obj);
            state.associatedDeviceRow.push(obj);
          }
        });

        arrPeople.map((item) => {
          let obj = state.allPeopleList.find((_) => _.nickName == item) || "";
          if (obj) {
            state.form.associatedPeopleIds.push(obj.userId);
            state.form.associatedPeopleList.push(obj);
            state.associatedPeopleRow.push(obj);
          }
        });

        console.log("设备id集合", arrDevice, state.allDeviceList, state.form);

        for (let i = 0; i < arr.length; i++) {
          state.positionResult.names[i] = state.positionList.find(
            (node) => node.id == arr[i]
          )?.name;
        }
        for (let i = 0; i < arr2.length; i++) {
          state.assetsResult.names[i] = state.assetsList.find(
            (node) => node.id == arr2[i]
          )?.name;
        }

        if (operationLogList) {
          state.recordList = operationLogList;
        }

        getTypeList();
        getAssetsTreeList();

        console.log("设备详情", state.form, response.data);
      }
      state.loadingPage = false;
    })
    .catch(() => (state.loadingPage = false));
}

// 添加用户选择变更处理方法
const scrapUserChange = (val) => {
  const idx = state.userList.findIndex((_) => _.userId == val);
  state.scrapForm.scrapSubmitter = state.userList[idx].nickName || "";
};

// 添加提交报废表单方法
const submitScrapForm = () => {
  proxy.$refs.scrapFormRef.validate((valid) => {
    if (valid) {
      proxy.$modal.confirm("确认报废设备？").then(() => {
        scrapDevice({
          deviceId: route.query.id,
          ...state.scrapForm,
        }).then((response) => {
          if (response.code == 200) {
            proxy.$modal.msgSuccess("操作成功");
            state.scrapDialogVisible = false;
            getData();
          }
        });
      });
    }
  });
};

/** 查询用户列表 */
async function getUsers() {
  await getUserByRole({
    pageNum: 1,
    pageSize: 999999,
    roleKey: [
      "admin",
      "common",
      "cityCabin",
      "cityManage",
      "districtCabin",
      "districtManage",
      "schoolCabin",
      "schoolManage",
      "maintain",
      "maintainManage",
      "teacherStaff",
    ],
  }).then((resp) => {
    if (resp.data) {
      state.userList = resp.data.records;
    }
  });
}

// 初始化
onMounted(() => {
  // 如果是编辑模式，获取路由参数中的数据
  if (route.query.id) {
    state.isEdit = route.query.type == 1;
    getData();
  }
});
</script>
  
  <style lang="scss" scoped>
.unitFlex {
  display: flex;
  gap: 0 10px;
}
.ledgerInfo {
  .el-descriptions {
    :deep(.label-width) {
      width: 130px;
      height: 60px;
      &.required {
        position: relative;
        &::before {
          font-size: 14px;
          content: "*";
          color: red;
          padding-right: 5px;
        }
      }
    }
    :deep(.value-width) {
      width: 270px;
      min-height: 60px;
    }
  }
  .pagination-container {
    padding: 0 !important;
    margin-top: 15px;
    :deep(.el-pagination) {
      justify-content: center;
    }
  }
  &-tit {
    font-size: 16px;
    font-weight: bold;
    margin: 20px 0;
    display: flex;
    align-items: center;
    gap: 0 20px;
  }
  &-record {
    font-size: 14px;
    margin-bottom: 5px;
  }
  &-btns {
    margin-top: 50px;
    display: flex;
    justify-content: center;
    gap: 0 40px;
  }
  &-record {
    .title {
      padding: 2vw 0 0.3vw;
      font-weight: bold;
    }
    &-item {
      margin-top: 0.5vw;
    }
  }
}
</style>