<!-- views/emergency/workOrderDetail.vue -->
<template>
  <div class="taskInfo">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <!-- 工单基础信息 -->
      <order-base-info :work-order-data="workOrderData" :show-report="true">
        <!-- 添加自定义处理报告插槽 -->
        <template #report>
          <div class="report-container">
            <div class="report-list">
              <template
                v-if="workOrderData.reports && workOrderData.reports.length > 0"
              >
                <div
                  v-for="(report, index) in workOrderData.reports"
                  :key="index"
                  class="report-item"
                >
                  <el-button
                    type="primary"
                    link
                    @click="downloadFile(report.fileUrl, report.fileName)"
                  >
                    {{ report.fileName }}
                  </el-button>
                </div>
              </template>
              <span v-else>-</span>
            </div>
          </div>
        </template>
      </order-base-info>

      <!-- 关联备件表格 -->
      <related-parts-table :parts="relatedParts" />

      <!-- 关联人员表格 -->
      <related-personnel-table
        :personnel="relatedPersonnel"
        :readonly="true"
        :show-notify-button="false"
      />

      <!-- 工单时间线 -->
      <!-- <order-timeline :work-order-data="workOrderData" /> -->

      <!-- 底部按钮区域 -->
      <div class="taskInfo-btns">
        <el-button @click="handleBack">返回</el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import { useRoute, useRouter } from "vue-router";
import { ElMessage } from "element-plus";
import { schoolPlanWorkOrderInfo, schoolPlanSpareInfo } from "@/api/emergency";
// 导入公共组件
import OrderBaseInfo from "@/views/emergencyManage/components/OrderBaseInfo.vue";
import RelatedPartsTable from "@/views/emergencyManage/components/RelatedPartsTable.vue";
import RelatedPersonnelTable from "@/views/emergencyManage/components/RelatedPersonnelTable.vue";
import OrderTimeline from "@/views/emergencyManage/components/OrderTimeline.vue";

const router = useRouter();
const route = useRoute();
const { proxy } = getCurrentInstance();
const isSpare = ref(!!route.query.spareId);
// 数据
const workOrderData = ref({});
const relatedParts = ref([]);
const relatedPersonnel = ref([]);

// 获取关联备件数据
const getRelatedParts = (planWorkOrderId) => {
  const params = {
    planWorkOrderId,
    pageNum: 1,
    pageSize: 10,
  };
  console.log("获取关联备件数据参数:", params);

  schoolPlanSpareInfo(params)
    .then((res) => {
      if (res.code === 200) {
        console.log("关联备件数据:", res.data);

        relatedParts.value = res.data.records.map((item) => ({
          partId: item.sparePartsCode,
          partName: item.name,
          quantity: item.num,
          useDate: item.createTime,
          stock: item.stock || 0,
        }));
      } else {
        ElMessage.warning(res.msg || "获取关联备件数据失败");
      }
    })
    .catch((error) => {
      console.error("获取关联备件数据失败:", error);
      ElMessage.warning("获取关联备件数据失败");
    });
};

// 获取工单数据
const getWorkOrderData = () => {
  proxy.$modal.loading();
  try {
    const orderId = route.query.id;
    if (!orderId) {
      ElMessage.error("工单ID不能为空");
      router.back();
      return;
    }

    schoolPlanWorkOrderInfo(orderId)
      .then((res) => {
        if (res.code === 200) {
          console.log("工单详情数据:", res.data);
          const { planWorkOrderInfo, planWorkOrderPeopleInfo, planInfo } =
            res.data;

          // 处理报告和上传人的显示逻辑
          let reports = [];
          let uploaderName = "-";

          // 如果创建者上传了报告，只显示创建者的报告
          if (
            planWorkOrderInfo.reportFileName &&
            planWorkOrderInfo.reportFileUrl
          ) {
            reports.push({
              fileName: planWorkOrderInfo.reportFileName,
              fileUrl: planWorkOrderInfo.reportFileUrl,
            });
            uploaderName = planWorkOrderInfo.uploadUser;
          } else {
            // 创建者未上传报告时，显示关联人的报告（如果有）
            const relatedPersonsWithReport =
              planWorkOrderPeopleInfo?.filter(
                (person) => person.reportFileName && person.reportFileUrl
              ) || [];

            if (relatedPersonsWithReport.length > 0) {
              relatedPersonsWithReport.forEach((person) => {
                reports.push({
                  fileName: person.reportFileName,
                  fileUrl: person.reportFileUrl,
                });
              });
              // 多个关联人上传报告时，用顿号分隔姓名
              uploaderName = relatedPersonsWithReport
                .map((person) => person.nickName)
                .join("、");
            }
          }

          // 设置工单基础信息
          workOrderData.value = {
            orderId: planWorkOrderInfo.code,
            orderType: planInfo?.type || "-",
            description: planWorkOrderInfo.remark,
            emergencyPlan: planInfo?.name || "-",
            status:
              planWorkOrderInfo.status == 0
                ? "待上传"
                : planWorkOrderInfo.status == 1
                ? "已归档"
                : "待上传",
            reports: reports.length > 0 ? reports : [], // 如果没有报告则传空数组
            uploaderName: uploaderName, // 使用处理后的上传人名称
            level: planWorkOrderInfo.level || "-",
            createTime: planWorkOrderInfo.createTime,
            handleTime: planWorkOrderInfo.processingTime,
          };

          // 获取关联备件数据
          getRelatedParts(orderId);

          // 设置关联人员数据
          if (planWorkOrderPeopleInfo && planWorkOrderPeopleInfo.length > 0) {
            console.log("关联人员数据:", planWorkOrderPeopleInfo);

            relatedPersonnel.value = planWorkOrderPeopleInfo.map((person) => ({
              workerId: person.userId,
              workId: person.workId || "-",
              name: person.nickName || "-",
              relateTime: person.associatedTime || "-",
              reportFileName: person.reportFileName,
              reportFileUrl: person.reportFileUrl,
              reportStatus: person.reportFileUrl ? "已上传" : "未上传",
            }));
          } else {
            relatedPersonnel.value = [];
          }
          proxy.$modal.closeLoading();
        } else {
          // ElMessage.error(res.msg || '获取工单详情失败')
          proxy.$modal.closeLoading();
          router.back();
        }
      })
      .catch((error) => {
        // console.error('获取工单详情失败:', error)
        // ElMessage.error('获取工单详情失败')
        proxy.$modal.closeLoading();
        router.back();
      });
  } catch (error) {
    proxy.$modal.closeLoading();
    // console.error('获取工单详情失败:', error)
    // ElMessage.error('获取工单详情失败')
    router.back();
  }
};

// 返回按钮处理函数
const handleBack = () => {
  if (isSpare.value) {
    proxy.$tab.closeOpenPage(
      `/spareManage/spareInfo?id=${route.query.spareId}`
    );
  } else {
    router.go(-1);
    proxy.$tab.closeOpenPage();
  }
  /* router.go(-1);
  proxy.$tab.closeOpenPage(); */
};

const downloadFile = (fileUrl, fileName) => {
  if (!fileUrl) {
    ElMessage.warning("文件不存在");
    return;
  }

  try {
    // 确保使用HTTPS链接
    let downloadUrl = fileUrl;
    if (downloadUrl.startsWith("http:")) {
      downloadUrl = downloadUrl.replace("http:", "https:");
    }

    // 创建一个隐藏的a标签直接下载
    const link = document.createElement("a");
    link.style.display = "none";
    link.href = downloadUrl;
    link.setAttribute("download", fileName || "未命名文件");
    link.setAttribute("target", "_blank");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  } catch (error) {
    console.error("下载文件失败:", error);
    ElMessage.error("下载文件失败");
  }
};

// 页面加载时获取数据
onMounted(() => {
  getWorkOrderData();
});
</script>

<style scoped lang="scss">
.taskInfo {
  padding: 20px;
}

.taskInfo-btns {
  padding: 20px;
  text-align: center;
  margin-top: 20px;
}

// 添加报告相关样式
.report-container {
  .report-list {
    flex-grow: 1;
  }
}

.report-item {
  margin: 5px 0;

  &:first-child {
    margin-top: 0;
  }

  &:last-child {
    margin-bottom: 0;
  }
}
</style>