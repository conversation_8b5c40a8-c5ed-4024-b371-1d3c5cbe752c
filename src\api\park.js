import request from '@/utils/request'

// 更新消息状态  msgId不传表示一键已读
export function readMsg(data) {
    return request({
        url: '/system/msg/readMsg',
        method: 'post',
        data
    })
}

// 查询消息
export function getMsg(params) {
    return request({
        url: '/system/msg/getMsg',
        method: 'get',
        params
    })
}

export function getInfo(data) {
    return request({
        url: '/system/tenant/getInfo',
        method: 'post',
        data
    })
}


export function editNum(data) {
    return request({
        url: '/system/tenant/editNum',
        method: 'post',
        data
    })
}

export function tenantTree(params) {
    return request({
        url: '/system/tenant/queTenantTreeNew',
        method: 'get',
        params
    })
}

export function tenantPage(data) {
    return request({
        url: '/system/tenant/quePage',
        method: 'post',
        data
    })
}

export function getTenantList() {
    return request({
        url: '/system/tenant/queList',
        method: 'get'
    })
}

export function tenantAdd(data) {
    return request({
        url: '/system/tenant/add',
        method: 'post',
        data
    })
}

export function tenantDel(data) {
    return request({
        url: '/system/tenant/delById',
        method: 'post',
        data
    })
}

export function tenantEdit(data) {
    return request({
        url: '/system/tenant/edit',
        method: 'post',
        data
    })
}