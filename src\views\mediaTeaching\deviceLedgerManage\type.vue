<template>
    <div class="app-container">
        <el-card shadow="never" class="page-card">
            <template #header>
                <div class="card-header page-header">
                    <span>{{ route.meta.title }}</span>
                    <el-button v-if="route.query.type" @click="handleBack">返回工作台</el-button>
                </div>
            </template>

            <el-form class="search-list" :model="queryParams" ref="queryRef" :inline="true" v-show="showSearch" @submit.native.prevent>
                <el-form-item label="" prop="typeName">
                    <el-input v-model="queryParams.typeName" placeholder="请输入类型名称" clearable style="width: 200px"
                        @keyup.enter="handleQuery" />
                </el-form-item>
                <el-form-item label="" prop="status">
                    <el-select v-model="queryParams.status" placeholder="请选择状态" @change="handleQuery" clearable filterable
                        style="width: 200px">
                        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
                        </el-option>
                    </el-select>
                </el-form-item>
                <el-form-item>
                    <el-button type="primary" icon="Search" @click="handleQuery">搜索</el-button>
                    <el-button icon="Refresh" @click="resetQuery">重置</el-button>
                </el-form-item>
            </el-form>

            <el-row :gutter="10" class="mb12">
                <el-col :span="1.5">
                    <el-button type="primary" plain icon="Plus" @click="handleAdd">新增</el-button>
                </el-col>
            </el-row>

            <el-table v-loading="loading" :data="typeList" border @selection-change="handleSelectionChange">
                <el-table-column type="index" label="序号" width="90" align="center" />
                <el-table-column label="类型名称" align="center" minWidth="120px" prop="typeName" />
                <el-table-column label="类型描述" align="center" minWidth="120px">
                    <template #default="scope">
                        {{ scope.row.remark || '-' }}
                    </template>
                </el-table-column>
                <el-table-column label="类型状态" align="center" minWidth="120px">
                    <template #default="scope">
                        <el-tag v-if="statusList[scope.row.status]" effect="dark"
                            :type="statusList[scope.row.status].type">{{
                                statusList[scope.row.status].label }}</el-tag>
                    </template>
                </el-table-column>
                <el-table-column label="创建时间" align="center" minWidth="120px" prop="createTime">
                    <template #default="scope">
                        <span>{{ parseTime(scope.row.createTime, '{y}-{m}-{d}') }}</span>
                    </template>
                </el-table-column>
                <el-table-column label="操作" min-width="150" align="center" fixed="right"
                    class-name="small-padding fixed-width">
                    <template #default="scope">
                        <el-button link type="primary" icon="Edit" @click="handleUpdate(scope.row)">修改</el-button>
                        <el-button v-if="scope.row.typeName !== '智慧大屏'" link type="danger" icon="Delete" @click="handleDelete(scope.row)">删除</el-button>
                    </template>
                </el-table-column>
            </el-table>

            <pagination v-show="total > 0" :total="total" v-model:page="queryParams.pageNum"
                v-model:limit="queryParams.pageSize" @pagination="getList" />
        </el-card>
        <!-- 添加或修改岗位对话框 -->
        <el-dialog class="custom-dialog" :title="title" v-model="open" width="500px" append-to-body :close-on-click-modal="false">
            <el-form ref="typeRef" :model="form" :rules="rules" label-width="100px">
                <el-form-item label="类型名称" prop="typeName">
                    <el-input v-model="form.typeName" :readonly="readonly || (form.typeName == '智慧大屏' && !!form.id)" placeholder="请输入类型名称" />
                </el-form-item>
                <el-form-item label="类型描述" prop="remark">
                    <el-input v-model="form.remark" type="textarea" :readonly="readonly" placeholder="请输入类型描述" />
                </el-form-item>
                <el-form-item label="状态" prop="status">
                    <el-radio-group v-model="form.status" :disabled="form.typeName == '智慧大屏' && !!form.id">
                        <el-radio v-for="item in statusList" :key="item.value" :label="item.value">{{ item.label
                        }}</el-radio>
                    </el-radio-group>
                </el-form-item>
            </el-form>
            <template #footer>
                <div class="dialog-footer">
                    <el-button type="primary" @click="submitForm">确 定</el-button>
                    <el-button @click="cancel">取 消</el-button>
                </div>
            </template>
        </el-dialog>
    </div>
</template>

<script setup name="typeManage">
import { useRoute } from 'vue-router'
import { deviceTypeAdd, checkDeviceType, deviceTypeDel, deviceTypeEdit, deviceTypePage } from '@/api/mediaTeach/type';

const route = useRoute()
const { proxy } = getCurrentInstance();

const typeList = ref([]);
const open = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const readonly = ref(false);
const ids = ref([]);
const statusList = ref([
    { label: '启用', value: 0, type: 'success' },
    { label: '停用', value: 1, type: 'danger' }
]);
const single = ref(true);
const multiple = ref(true);
const total = ref(0);
const title = ref("");

const data = reactive({
    form: {},
    queryParams: {
        current: 1,
        size: 10,
        typeName: '',
        status: ''
    },
    rules: {
        typeName: [
            { required: true, message: "类型名称不能为空", trigger: "blur" },
            { max: 20, message: "类型名称最多输入20个字符", trigger: "blur" }
        ],
        remark: [
            { max: 200, message: "类型描述最多输入200个字符", trigger: "blur" }
        ]
    }
});

const { queryParams, form, rules } = toRefs(data);

function handleBack() {
    proxy.$tab.closeOpenPage('/work')
}

/** 查询类型列表 */
function getList() {
    loading.value = true;
    deviceTypePage(queryParams.value).then(response => {
        console.log('type', response)
        typeList.value = response.data.deviceTypeList
        total.value = response.data.total
        loading.value = false
    })
}
/** 取消按钮 */
function cancel() {
    open.value = false;
    reset();
}
/** 表单重置 */
function reset() {
    form.value = {
        id: '',
        typeName: '',
        remark: '',
        status: 0,
        createTime: ''
    };
    proxy.resetForm("typeRef");
}
/** 搜索按钮操作 */
function handleQuery() {
    queryParams.value.current = 1;
    getList();
}
/** 重置按钮操作 */
function resetQuery() {
    proxy.resetForm("queryRef");
    handleQuery();
}
/** 多选框选中数据 */
function handleSelectionChange(selection) {
    ids.value = selection.map(item => item.postId);
    single.value = selection.length != 1;
    multiple.value = !selection.length;
}
/** 新增按钮操作 */
function handleAdd() {
    reset();
    open.value = true;
    title.value = "添加类型";
}
/** 修改按钮操作 */
function handleUpdate(row) {
    reset();
    title.value = "修改类型";
    form.value = JSON.parse(JSON.stringify(row))
    open.value = true;
}
/** 查看按钮操作 */
function handleCheck(row) {
    reset();
    title.value = "查看类型";
    readonly.value = true
    form.value = JSON.parse(JSON.stringify(row))
    open.value = true;
}
/** 提交按钮 */
function submitForm() {
    proxy.$refs["typeRef"].validate(valid => {
        if (valid) {
            if (!!form.value.id) {
                deviceTypeEdit(form.value).then(response => {
                    proxy.$modal.msgSuccess("修改成功");
                    getList();
                    open.value = false
                })
            } else {
                deviceTypeAdd(form.value).then(response => {
                    proxy.$modal.msgSuccess("新增成功");
                    getList();
                    open.value = false
                })
            }
        }
    });
}
/** 删除按钮操作 */
function handleDelete(row) {
    checkDeviceType(row.id).then(res => {
      console.log('检查', res)
      if (!!res.data) {
         proxy.$modal.confirm(`${row.typeName}类型已存在设备，确认删除?`).then(function() {
            return deviceTypeDel({ id: row.id });
            }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
         }).catch(() => {});
      } else {
        proxy.$modal.confirm('是否确认删除类型名称为"' + row.typeName + '"的数据项？').then(async function () {
            await deviceTypeDel({ id: row.id })
        }).then(() => {
            getList();
            proxy.$modal.msgSuccess("删除成功");
        }).catch(() => { });
      }
   })
}

getList();
</script>
