<template>
  <div class="recording app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          {{ route.meta.title }}
        </div>
      </template>

      <div class="view-switch">
        <el-radio-group v-model="queryParams.logType" @change="changeType">
          <el-radio-button value="1" label="operation"
            >消息定时计划</el-radio-button
          >
          <el-radio-button value="5" label="message"
            >设备管控计划</el-radio-button
          >
        </el-radio-group>
      </div>
      <!-- 消息定时计划 -->
      <div v-if="queryParams.logType != '5'" class="recording-main">
        <el-table
          v-loading="loading"
          :data="tableList"
          border
          style="width: 100%"
        >
          <el-table-column
            prop="status"
            label="状态"
            align="center"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="effectiveDate"
            label="生效日期"
            align="center"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="duration"
            label="持续时间"
            align="center"
            min-width="120"
            show-overflow-tooltip
          />
          <el-table-column
            prop="messageContent"
            label="消息内容"
            align="center"
            min-width="120"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.messageContent || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            prop="receiveDevice"
            label="接收设备"
            align="center"
            min-width="180"
            show-overflow-tooltip
          >
            <template #default="scope">
              {{ scope.row.receiveDevice || "-" }}
            </template>
          </el-table-column>
          <el-table-column
            label="操作"
            align="center"
            min-width="200"
            show-overflow-tooltip
          >
            <template #default="scope">
              <el-button
                link
                type="primary"
                icon="Edit"
                @click.stop="handleEdit(scope.row)"
                >修改</el-button
              >
              <el-button
                link
                type="danger"
                icon="Delete"
                @click.stop="handleDelete(scope.row)"
                >删除</el-button
              >
            </template>
          </el-table-column>
        </el-table>

        <div class="add-row" @click="handleAdd" v-if="tableList.length < 5">
          <el-icon><Plus /></el-icon>
          <span>添加一项</span>
        </div>
      </div>

      <div v-if="queryParams.logType == '5'" class="recording-main">
        <div class="deviceUseManage-tit">设备开关机定时计划</div>
        <el-table v-loading="loadingOff" :data="switchMachineList" border>
          <el-table-column
            prop="status"
            label="状态"
            width="120"
            align="center"
          />
          <el-table-column
            prop="controlType"
            label="管控类别"
            align="center"
            min-width="120"
          />
          <el-table-column
            prop="frequency"
            label="频率"
            align="center"
            min-width="120"
          />
          <el-table-column
            prop="effectiveTime"
            label="生效时间"
            align="center"
            min-width="180"
          />
          <el-table-column
            prop="devices"
            label="接收设备"
            align="center"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handleEditDeviceControl(scope.row)"
                >更改</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div
          class="add-row"
          @click="handleAddDeviceControl"
          v-if="switchMachineList.length === 0"
        >
          <el-icon><Plus /></el-icon>
          <span>添加一项</span>
        </div>

        <div class="deviceUseManage-tit">设备自动解锁/锁定定时计划</div>
        <el-table v-loading="loadingUsb" :data="lockScreenPlanList" border>
          <el-table-column
            prop="status"
            label="状态"
            width="120"
            align="center"
          />
          <el-table-column
            prop="controlType"
            label="管控类别"
            align="center"
            min-width="120"
          />
          <el-table-column
            prop="effectiveTimeRange2"
            label="生效时间段"
            align="center"
            min-width="180"
          />
          <el-table-column
            prop="lockScreenImage"
            label="锁定屏保图片"
            align="center"
            min-width="120"
          >
            <template #default="scope">
              <div>
                {{ scope.row.lockScreenImage }}
                <el-button
                  v-if="scope.row.imageUrl"
                  type="primary"
                  size="small"
                  @click="handleViewLockScreenImage(scope.row.imageUrl)"
                  >查看</el-button
                >
              </div>
            </template>
          </el-table-column>
          <el-table-column
            prop="devices"
            label="接收设备"
            align="center"
            min-width="200"
            show-overflow-tooltip
          />
          <el-table-column label="操作" align="center" width="120">
            <template #default="scope">
              <el-button
                link
                type="primary"
                @click="handleEditLockRule(scope.row)"
                >更改</el-button
              >
            </template>
          </el-table-column>
        </el-table>
        <div
          class="add-row"
          @click="handleLockRule"
          v-if="lockScreenPlanList.length === 0"
        >
          <el-icon><Plus /></el-icon>
          <span>添加一项</span>
        </div>

        <!-- 添加查看图片的对话框 -->
        <el-dialog
          v-model="lockScreenImageDialogVisible"
          title="锁定屏保图片"
          width="500px"
          align-center
        >
          <img
            :src="currentLockScreenImage"
            style="max-width: 100%; display: block; margin: 0 auto"
            alt="锁定屏保图片"
          />
        </el-dialog>
      </div>
    </el-card>

    <!-- 添加/编辑对话框 -->
    <el-dialog
      v-model="dialogVisible"
      :title="dialogTitle"
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="custom-dialog message-plan-dialog"
      align-center
      @close="handleCancel"
    >
      <el-scrollbar :max-height="500" style="padding-right: 20px">
        <el-form ref="formRef" :model="form" :rules="rules" label-width="80px">
          <el-form-item label="状态" prop="status">
            <el-radio-group v-model="form.status">
              <el-radio :value="0">启用</el-radio>
              <el-radio :value="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="生效日期" prop="effectiveDate">
            <el-date-picker
              v-model="form.effectiveDate"
              type="date"
              placeholder="请选择日期"
              :disabled-date="disabledDate"
              @change="handleDateChange"
            />
          </el-form-item>

          <el-form-item label="生效时间" prop="effectiveTime">
            <el-time-picker
              v-model="form.effectiveTime"
              placeholder="请选择时间"
              format="HH:mm:ss"
            />
          </el-form-item>

          <el-form-item label="持续时间" prop="duration">
            <el-input-number
              v-model="form.duration"
              :min="1"
              :max="60"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
            <el-select
              v-model="form.durationUnit"
              style="margin-left: 10px; width: 80px"
            >
              <el-option label="秒" value="1" />
              <el-option label="分钟" value="2" />
              <el-option label="小时" value="3" />
              <el-option label="天" value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="轮播时间" prop="loopTime">
            <el-input-number
              v-model="form.loopTime"
              :min="1"
              :max="60"
              :precision="0"
              controls-position="right"
              style="width: 200px"
            />
            <el-select
              v-model="form.loopTimeUnit"
              style="margin-left: 10px; width: 80px"
            >
              <el-option label="秒" value="1" />
              <el-option label="分钟" value="2" />
              <el-option label="小时" value="3" />
              <el-option label="天" value="4" />
            </el-select>
          </el-form-item>

          <el-form-item label="消息内容" prop="messageContent">
            <el-input
              v-model="form.messageContent"
              type="textarea"
              placeholder="请输入"
              :rows="4"
              maxlength="50"
              show-word-limit
              style="width: 100%"
            />
          </el-form-item>

          <el-form-item label="接收设备" prop="selectedDevices">
            <div class="select-device-container">
              <template v-if="form.selectedDevices">
                <el-tag
                  v-for="device in form.selectedDevices.split(',')"
                  :key="device"
                  closable
                  class="device-tag"
                  @close="removeDevice(device)"
                >
                  {{ device }}
                </el-tag>
              </template>
              <div v-else class="placeholder-text">请选择设备</div>
            </div>
          </el-form-item>

          <div class="mt-4">
            <div class="mb-3 label">请选择设备</div>
            <div class="tree-container">
              <el-tree
                ref="deviceTreeRef"
                :data="deviceTreeData"
                :props="defaultProps"
                show-checkbox
                node-key="id"
                default-expand-all
                @check="handleDeviceCheck"
              />
            </div>
          </div>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleCancel">取 消</el-button>
          <el-button type="primary" @click="submitForm" v-throttle
            >确 定</el-button
          >
        </div>
      </template>
    </el-dialog>

    <!-- 设备开关机定时计划对话框 -->
    <el-dialog
      v-model="deviceControlDialogVisible"
      :title="deviceControlDialogTitle"
      width="590px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      class="custom-dialog device-control-dialog"
      align-center
    >
      <el-scrollbar :max-height="500" style="padding-right: 20px">
        <el-form
          ref="deviceControlFormRef"
          :model="deviceControlForm"
          :rules="deviceControlRules"
          label-width="120px"
        >
          <el-form-item label="状态">
            <el-radio-group v-model="deviceControlForm.status">
              <el-radio :value="0">启用</el-radio>
              <el-radio :value="1">禁用</el-radio>
            </el-radio-group>
          </el-form-item>

          <!-- 只在状态为启用(0)时显示开机规则设置 -->
          <template v-if="deviceControlForm.status === 0">
            <el-form-item label="开机规则设置" class="rule-title">
              <div class="rule-header">
                <span>开机规则设置</span>
                <el-switch
                  v-model="deviceControlForm.startEnabled"
                  style="--el-switch-on-color: #5a7bf4"
                  @change="handleStartEnabledChange"
                />
              </div>
            </el-form-item>

            <!-- 开机规则相关表单项 -->
            <template v-if="deviceControlForm.startEnabled">
              <el-form-item label="频率" v-if="deviceControlForm.startEnabled">
                <el-select
                  v-model="deviceControlForm.startFrequency"
                  placeholder="请选择"
                  style="width: 150px"
                >
                  <el-option label="每天一次" value="daily-once" />
                  <el-option label="每天两次" value="daily-twice" />
                  <el-option label="每周一次" value="weekly-once" />
                </el-select>
              </el-form-item>

              <el-form-item
                label="每周生效日期"
                v-if="
                  deviceControlForm.startEnabled &&
                  deviceControlForm.startFrequency.startsWith('weekly')
                "
              >
                <el-select
                  v-model="deviceControlForm.startWeekday"
                  placeholder="请选择"
                  style="width: 150px"
                >
                  <el-option label="星期一" value="1" />
                  <el-option label="星期二" value="2" />
                  <el-option label="星期三" value="3" />
                  <el-option label="星期四" value="4" />
                  <el-option label="星期五" value="5" />
                  <el-option label="星期六" value="6" />
                  <el-option label="星期日" value="0" />
                </el-select>
              </el-form-item>
              <div style="display: flex; gap: 10px">
                <el-form-item
                  label="每次生效时间1"
                  v-if="deviceControlForm.startEnabled"
                  prop="startTime1"
                >
                  <el-time-picker
                    style="width: 150px"
                    v-model="deviceControlForm.startTime1"
                    format="HH:mm:ss"
                    placeholder="请选择时间"
                  />
                </el-form-item>

                <el-form-item
                  label="每次生效时间2"
                  v-if="
                    deviceControlForm.startEnabled &&
                    deviceControlForm.startFrequency.endsWith('twice')
                  "
                  prop="startTime2"
                >
                  <el-time-picker
                    style="width: 150px"
                    v-model="deviceControlForm.startTime2"
                    format="HH:mm:ss"
                    placeholder="请选择时间"
                  />
                </el-form-item>
              </div>

              <el-form-item
                label="开机接收设备"
                prop="startReceiveDevices"
                v-if="deviceControlForm.startEnabled"
              >
                <div class="select-device-container">
                  <template v-if="deviceControlForm.selectedStartDevices">
                    <el-tag
                      v-for="device in deviceControlForm.selectedStartDevices.split(
                        ','
                      )"
                      :key="device"
                      closable
                      class="device-tag"
                      @close="removeStartDevice(device)"
                    >
                      {{ device }}
                    </el-tag>
                  </template>
                  <div v-else class="placeholder-text">请选择设备</div>
                </div>
              </el-form-item>

              <div v-if="deviceControlForm.startEnabled" class="mt-3">
                <div class="mb-3 label">请选择设备</div>
                <div class="tree-container">
                  <el-tree
                    :key="deviceControlDialogVisible"
                    ref="startDeviceTreeRef"
                    :data="deviceTreeData"
                    :props="defaultProps"
                    show-checkbox
                    node-key="id"
                    default-expand-all
                    @check="handleStartDeviceCheck"
                  />
                </div>
              </div>
            </template>

            <el-form-item label="关机规则设置" class="rule-title">
              <div class="rule-header">
                <span>关机规则设置</span>
                <el-switch
                  v-model="deviceControlForm.endEnabled"
                  style="--el-switch-on-color: #5a7bf4"
                  @change="handleEndEnabledChange"
                />
              </div>
            </el-form-item>

            <!-- 关机规则相关表单项 -->
            <template v-if="deviceControlForm.endEnabled">
              <el-form-item label="频率" v-if="deviceControlForm.endEnabled">
                <el-select
                  v-model="deviceControlForm.endFrequency"
                  placeholder="请选择"
                  style="width: 150px"
                >
                  <el-option label="每天一次" value="daily-once" />
                  <el-option label="每天两次" value="daily-twice" />
                  <el-option label="每周一次" value="weekly-once" />
                </el-select>
              </el-form-item>

              <el-form-item
                label="每周生效日期"
                v-if="
                  deviceControlForm.endEnabled &&
                  deviceControlForm.endFrequency.startsWith('weekly')
                "
              >
                <el-select
                  v-model="deviceControlForm.endWeekday"
                  placeholder="请选择"
                  style="width: 150px"
                >
                  <el-option label="星期一" value="1" />
                  <el-option label="星期二" value="2" />
                  <el-option label="星期三" value="3" />
                  <el-option label="星期四" value="4" />
                  <el-option label="星期五" value="5" />
                  <el-option label="星期六" value="6" />
                  <el-option label="星期日" value="0" />
                </el-select>
              </el-form-item>
              <div style="display: flex; gap: 10px">
                <el-form-item
                  label="每次生效时间1"
                  v-if="deviceControlForm.endEnabled"
                  prop="endTime1"
                >
                  <el-time-picker
                    v-model="deviceControlForm.endTime1"
                    format="HH:mm:ss"
                    placeholder="请选择时间"
                    style="width: 150px"
                  />
                </el-form-item>

                <el-form-item
                  label="每次生效时间2"
                  v-if="
                    deviceControlForm.endEnabled &&
                    deviceControlForm.endFrequency.endsWith('twice')
                  "
                  prop="endTime2"
                >
                  <el-time-picker
                    v-model="deviceControlForm.endTime2"
                    format="HH:mm:ss"
                    placeholder="请选择时间"
                    style="width: 150px"
                  />
                </el-form-item>
              </div>
              <el-form-item
                label="关机接收设备"
                prop="endReceiveDevices"
                v-if="deviceControlForm.endEnabled"
              >
                <div class="select-device-container">
                  <template v-if="deviceControlForm.selectedEndDevices">
                    <el-tag
                      v-for="device in deviceControlForm.selectedEndDevices.split(
                        ','
                      )"
                      :key="device"
                      closable
                      class="device-tag"
                      @close="removeEndDevice(device)"
                    >
                      {{ device }}
                    </el-tag>
                  </template>
                  <div v-else class="placeholder-text">请选择设备</div>
                </div>
              </el-form-item>

              <div v-if="deviceControlForm.endEnabled" class="mt-3">
                <div class="mb-3 label">请选择设备</div>
                <div class="tree-container">
                  <el-tree
                    :key="deviceControlDialogVisible"
                    ref="endDeviceTreeRef"
                    :data="deviceTreeData"
                    :props="defaultProps"
                    show-checkbox
                    node-key="id"
                    default-expand-all
                    @check="handleEndDeviceCheck"
                  />
                </div>
              </div>
            </template>
          </template>
        </el-form>
      </el-scrollbar>
      <template #footer>
        <div class="dialog-footer">
          <el-button
            type="primary"
            @click="submitDeviceControlForm"
            v-throttle
            >{{ submitButtonText }}</el-button
          >
          <el-button @click="handleCancelDeviceControl">取消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 锁屏规则设置对话框 -->
    <lockRule
      ref="lockRuleDialogRef"
      :open="ruleOpen"
      :deviceTreeData="deviceTreeData"
      :formData="lockRuleForm"
      :curRow="curRow"
      @submit="handleSubmit"
      @cancel="handleCancelSubmit"
    />
    <el-dialog
      class="custom-dialog lock-rule-dialog"
      :v-model="false"
      width="550px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      align-center
    >
      <template #header>
        <div
          style="
            font-size: 18px;
            display: flex;
            align-items: center;
            gap: 0 10px;
          "
        >
          锁屏规则设置
          <div
            style="
              font-size: 14px;
              color: red;
              display: flex;
              align-items: center;
              gap: 0 5px;
            "
          >
            <el-icon><QuestionFilled /></el-icon
            >注意：更新锁屏规则后需设备重启才会应用
          </div>
        </div>
      </template>
      <el-form :model="lockRuleForm" ref="lockRuleRef" label-position="top">
        <el-form-item
          prop="num"
          :rules="{
            required: lockRuleForm.isAutoLock,
            message: '请输入自动锁定的无操作时间',
            trigger: ['change', 'blur'],
          }"
        >
          <template #label>
            <div style="display: inline-block">
              <div style="display: flex; align-items: center; gap: 0 10px">
                自动锁定规则设置
                <el-switch v-model="lockRuleForm.isAutoLock" />
              </div>
            </div>
          </template>
          <div
            v-show="lockRuleForm.isAutoLock"
            style="display: flex; align-items: center; gap: 0 10px"
          >
            检测到设备无操作
            <el-input-number
              v-model="lockRuleForm.num"
              :max="99999"
              :min="1"
              :precision="0"
              :step="1"
              @keydown="SendEventTwo"
            />
            <el-select v-model="lockRuleForm.unit" style="width: 100px">
              <el-option label="秒" :value="1" />
              <el-option label="分钟" :value="2" />
              <el-option label="小时" :value="3" />
              <el-option label="天" :value="4" />
            </el-select>
            后自动锁定
          </div>
        </el-form-item>
        <!-- <el-form-item
          prop="timeList"
          :rules="{
            required: lockRuleForm.isDisable,
            message: '请选择自动解锁时间范围',
            trigger: ['change', 'blur'],
          }"
        > -->
        <el-form-item prop="timeRangeList">
          <template #label>
            <div style="display: inline-block">
              <div style="display: flex; align-items: center; gap: 0 10px">
                自动解锁规则设置
                <el-switch
                  v-model="lockRuleForm.isDisable"
                  @change="changeDisable"
                />
                <div
                  style="
                    font-size: 12px;
                    display: flex;
                    align-items: center;
                    gap: 0 5px;
                  "
                >
                  <el-icon size="20"><QuestionFilled /></el-icon
                  >在设置的时间段内设备自动解锁，最多五个时间段
                </div>
              </div>
            </div>
          </template>

          <div v-show="lockRuleForm.isDisable" style="width: 100%">
            <div
              style="
                display: flex;
                align-items: center;
                gap: 0 10px;
                font-size: 13px;
                justify-content: center;
                margin-bottom: 10px;
              "
            >
              是否允许手动锁定？
              <el-switch v-model="lockRuleForm.isLock" />
              <div
                style="
                  font-size: 12px;
                  display: flex;
                  align-items: center;
                  gap: 0 5px;
                "
              >
                <el-icon size="20"><QuestionFilled /></el-icon
                >关闭后，在指定解锁时间段内无法手动锁定
              </div>
            </div>
            <div
              class="timeRangeRow"
              v-for="(item, index) in lockRuleForm.timeRangeList"
              :key="index"
            >
              <div class="timeIndex">{{ index + 1 }}</div>
              <el-time-picker
                v-model="item.time"
                is-range
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                format="HH:mm:ss"
                value-format="HH:mm:ss"
                style="margin-bottom: 5px; width: 425px; flex-grow: inherit"
                @change="changeTimeList"
              />
              <el-button
                v-if="index != 0"
                type="danger"
                icon="Delete"
                circle
                @click="handleDelTime(item, index)"
              />
            </div>
            <div
              class="timeAdd"
              @click="handleAddTime"
              v-if="lockRuleForm.timeRangeList.length < 5"
            >
              <el-button type="primary" icon="Plus" color="#333" circle />
              新增时间段
            </div>
            <!-- <el-time-picker
              v-model="lockRuleForm.timeList"
              is-range
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              format="HH:mm:ss"
              value-format="HH:mm:ss"
              style="margin-bottom: 5px; width: 100%"
              @change="changeTimeList"
            /> -->
          </div>
        </el-form-item>
        <el-form-item label="锁定屏保图片">
          <div style="width: 100%">
            <el-radio-group
              v-model="lockRuleForm.lockImg"
              @change="changeLockImg"
            >
              <el-radio label="默认" :value="0" />
              <el-radio label="自定义" :value="1" />
            </el-radio-group>
          </div>
          <div
            class="upload-item"
            v-show="lockRuleForm.lockImg == 1"
            style="display: flex; align-items: center; gap: 0 10px"
          >
            <img
              v-if="lockRuleForm.lockUrl && !isImgChange"
              class="el-upload-list__item-thumbnail"
              :src="lockRuleForm.lockUrl"
              alt=""
              style="
                width: 148px;
                height: 148px;
                object-fit: contain;
                border: 1px solid #dcdfe6;
                cursor: pointer;
                border-radius: 6px;
              "
              @click="handlePictureCardPreview()"
            />
            <el-upload
              class="uploadImg"
              ref="uploadImgRef"
              :headers="headers"
              action="#"
              list-type="picture-card"
              :limit="1"
              :auto-upload="false"
              :on-change="handleImgChange"
              :on-exceed="handleImgExceed"
              :http-request="httpRequestImgFn"
            >
              <el-button type="primary"
                >{{ lockRuleForm.lockUrl ? "更换" : "选择" }}图片</el-button
              >
              <template #file="{ file }">
                <div>
                  <img
                    class="el-upload-list__item-thumbnail"
                    :src="file.url || lockRuleForm.lockUrl"
                    alt=""
                  />
                  <span class="el-upload-list__item-actions">
                    <span
                      class="el-upload-list__item-preview"
                      @click="handlePictureCardPreview(file)"
                    >
                      <el-icon><zoom-in /></el-icon>
                    </span>
                  </span>
                </div>
              </template>
              <template #tip>
                <div class="el-upload__tip">
                  建议更换 3840 * 2160 的图片，大小不超过5MB
                </div>
              </template>
            </el-upload>
          </div>
        </el-form-item>
        <!-- 添加接收设备选择 -->
        <el-form-item label="接收设备" prop="selectedDevices">
          <div class="select-device-container">
            <template v-if="lockRuleForm.selectedDevices">
              <el-tag
                v-for="device in lockRuleForm.selectedDevices.split(',')"
                :key="device"
                closable
                class="device-tag"
                @close="removeLockRuleDevice(device)"
              >
                {{ device }}
              </el-tag>
            </template>
            <div v-else class="placeholder-text">请选择设备</div>
          </div>
        </el-form-item>

        <div class="mt-4">
          <div class="mb-3 label">请选择设备</div>
          <div class="tree-container">
            <el-tree
              ref="lockRuleDeviceTreeRef"
              :data="deviceTreeData"
              :props="defaultProps"
              show-checkbox
              node-key="id"
              default-expand-all
              @check="handleLockRuleDeviceCheck"
            />
          </div>
        </div>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="handleRule" v-throttle
            >保存</el-button
          >
          <el-button @click="handleCancelRule">返回</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog v-model="dialogVisibleImg" width="900">
      <img
        :src="dialogImageUrl"
        alt="Preview Image"
        style="
          display: block;
          max-width: 100%;
          margin: 0 auto;
          object-fit: contain;
          height: 600px;
        "
      />
    </el-dialog>
  </div>
</template>

<script setup name="Log">
import lockRule from "./components/lockRule.vue";
import imgUpload from "@/components/ImageUpload";
import { useRoute } from "vue-router";
import {
  ref,
  reactive,
  toRefs,
  getCurrentInstance,
  onActivated,
  onMounted,
  watch,
  nextTick,
  computed,
} from "vue";
import { getSchoolDeviceLogList } from "@/api/deviceControl";
import { queUsbPage, queOffPage } from "@/api/mediaTeach/use";
import { getPositionTree } from "@/api/mediaTeach/position";
import { treeToArray, treeFindPath } from "@/utils";
import { Plus } from "@element-plus/icons-vue";
import { ElMessage, genFileId } from "element-plus";
import {
  addSchoolDeviceLog,
  deviceStartByWOL,
  deviceCtl,
  deviceCtlMqtt,
  checkDeviceCode,
  sendMessage,
  lockScreenRule,
  lockScreenRuleBatch,
  getLockScreenRule,
  downScreenImg,
  deleteScreenImg,
  sendMessageMqtt,
  getPointInfo,
  getLatestExpirationInfo,
  getPointInfoDetail,
  listTimePlan,
  getTimePlan,
  sendMessageMqttOrHttp,
  listSwitchMachine,
  getSwitchMachine,
  addSwitchMachine,
  deleteTimePlan,
} from "@/api/deviceControl";
import { getToken } from "@/utils/auth";
import {
  devicePage,
  deviceInfo,
  exportDeviceCode,
} from "@/api/mediaTeach/ledger";

const route = useRoute();
const { proxy } = getCurrentInstance();
const actionUrlImg = ref(
  import.meta.env.VITE_APP_BASE_API + "/system/schoolSoftware/uploadScreenImg"
);

const data = reactive({
  curRow: {},
  isImgChange: false,
  hasScreenImg: false,
  ruleOpen: false,
  lockRuleForm: {
    num: 10,
    unit: 1,
    timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
    timeList: ["08:00:00", "18:00:00"],
    lockImg: 0,
    lockUrl: "",
    isAutoLock: false,
    isDisable: false,
    isLock: false,
    selectedDevices: "", // 新增字段
    deviceIds: [], // 新增字段
  },
  lockRuleRef: null,
  dialogImageUrl: "",
  dialogVisibleImg: false,
  showStartTreeSelect: false,
  showEndTreeSelect: false,
  startDeviceTreeRef: null,
  endDeviceTreeRef: null,
  deviceTreeData: [],
  defaultProps: {
    children: "children",
    label: "label",
  },
});
const state = reactive({
  lockRuleDialogRef: null,
  dialogVisible: false,
  total: 0,
  totalOff: 0,
  totalUsb: 0,
  loading: false,
  tableList_all: [],
  tableList: [],
  loadingOff: false,
  loadingUsb: false,
  queryParams: {
    current: 1,
    pageSize: 10,
    logType: "1",
    deviceCode: "",
    createTimeQueryList: null,
  },
  queryPositionResult: {
    names: [],
    ids: [],
  },
  queryOffPositionResult: {
    names: [],
    ids: [],
  },
  queryUsbPositionResult: {
    names: [],
    ids: [],
  },
  positionList: [],
  positionTreeList: [],
  positionProps: {
    label: "name",
    children: "children",
    value: "id",
    disabled: "disabled",
  },
  queryOffParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
  queryUsbParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
});

const {
  curRow,
  lockRuleForm,
  hasScreenImg,
  ruleOpen,
  isImgChange,
  lockRuleRef,
  dialogImageUrl,
  dialogVisibleImg,
  showStartTreeSelect,
  showEndTreeSelect,
  startDeviceTreeRef,
  endDeviceTreeRef,
  defaultProps,
  deviceTreeData,
} = toRefs(data);

const {
  lockRuleDialogRef,
  positionList,
  totalUsb,
  totalOff,
  loadingOff,
  loadingUsb,
  positionTreeList,
  queryPositionResult,
  queryOffPositionResult,
  queryUsbPositionResult,
  positionProps,
  queryOffParams,
  queryUsbParams,
  tableList,
  queryParams,
  loading,
  total,
} = toRefs(state);

const handleAddTime = () => {
  if (lockRuleForm.value.timeRangeList.length < 5) {
    lockRuleForm.value.timeRangeList.push({ time: ["08:00:00", "18:00:00"] });
  } else {
    proxy.$modal.msgWarning("最多只能添加五个时间段");
  }
};

const handleDelTime = (item, index) => {
  lockRuleForm.value.timeRangeList.splice(index, 1);
};

const handleDateChange = () => {
  if (form.value.effectiveDate && form.value.effectiveTime) {
    formRef.value.validateField("effectiveTime");
  }
};

const headers = ref({
  Authorization: "Bearer " + getToken(),
  "Content-Type": "multipart/form-data",
});
function SendEventTwo(e) {
  if (e.key === "." || e.key === "," || e.key === "-" || e.key === "+") {
    e.preventDefault();
  }
}

function changeTimeList(val) {
  // console.log(val);
}

function changeDisable(val) {
  if (!val) {
    // 只重置与自动解锁相关的字段
    // lockRuleRef.value.timeList
  }
}

function changeAutoLock(val) {
  if (!val) {
    // 只重置与自动锁定相关的字段
    data.lockRuleForm.num = 10;
    data.lockRuleForm.unit = 1;
  }
}

// 自定义上传屏保
const httpRequestImgFn = (options) => {
  console.log(options, "自定义上传屏保");

  const formData = new FormData();
  formData.append("file", options.file);

  fetch(actionUrlImg.value, {
    method: "POST",
    body: formData,
    headers: { Authorization: "Bearer " + getToken() },
  })
    .then((response) => {
      if (response.ok) {
        return response.json();
      }
    })
    .then((res) => {
      console.log(res);
      if (res && res.data && res.code == 200) {
        submitRule();
      } else {
        proxy.$modal.msgError("操作失败");
        handleCancelRule();
      }
    });
};

// 自定义上传
const httpRequestFn = (options) => {
  console.log(options, "自定义上传");

  uploading.value = true;
  progress.value = 0;
  abortController.value = new AbortController();

  // 生成 requestId
  generateRequestId(options.file, 1024 * 1024, function (requestId) {
    try {
      console.log(`生成的 requestId 是: ${requestId}`);

      const chunkSize = 10 * 1024 * 1024; // 每个分片大小为10MB
      let start = 0;
      console.log("文件大小：" + options.file.size);
      let end = Math.min(chunkSize, options.file.size);

      const uploadChunk = (chunk, chunkNumber, totalChunks) => {
        const formData = new FormData();
        formData.append("file", new Blob([chunk]));
        formData.append("fileDescription", data.fileForm.fileDescription);
        formData.append(
          "deviceCodeList",
          JSON.stringify(
            data.tableAllSelectedRow.map((item) => item.deviceCode)
          )
        );
        formData.append("filePath", data.fileForm.filePath);
        formData.append("type", data.fileForm.type);
        formData.append("chunkNumber", chunkNumber);
        formData.append("totalChunks", totalChunks);
        formData.append(
          "identifier",
          options.file.name + "_" + options.file.size
        ); // 使用文件名和大小作为标识符
        formData.append("requestId", requestId); // 添加生成的 requestId
        formData.append("originalFileName", options.file.name); // 添加原始文件名

        // 如果是最后一个分片，则添加一个标志
        if (chunkNumber === totalChunks) {
          formData.append("isLastChunk", "true");
        }

        fetch(actionUrl.value, {
          // 更新为你提供的URL
          method: "POST",
          body: formData,
          headers: headers2.value,
          signal: abortController.value.signal,
        })
          .then((response) => {
            if (response.ok) {
              // 打印当前时间和分片上传成功信息
              if (chunkNumber === totalChunks) {
                progress.value = 100;
                return response.json();
              } else {
                progress.value = Math.round((chunkNumber / totalChunks) * 100);
              }
              console.log(
                `[${getCurrentTime()}] 分片 ${chunkNumber} 已成功上传`
              );
              processNextChunk(chunkNumber + 1);
            } else {
              console.error("分片上传失败:", response.statusText);
              proxy.$modal.alert(`发送失败`, "提示");
            }
          })
          .then((res) => {
            console.log(res);
            if (res && res.data) {
              if (res.code == 200) {
                let data = sm2Decrypt(res.data);
                console.log(data);
                if (data == "分发成功") {
                  proxy.$modal.alert(`发送成功`, "提示");
                } else {
                  proxy.$modal.alert(`发送失败`, "提示");
                }
              } else {
                proxy.$modal.alert(`发送失败`, "提示");
              }
            }
          })
          .finally(() => {
            if (chunkNumber === totalChunks) {
              handleCancelFile();
            }
          });
      };

      const processNextChunk = (chunkNumber) => {
        if (start < options.file.size) {
          const nextChunk = options.file.slice(start, end);
          uploadChunk(
            nextChunk,
            chunkNumber,
            Math.ceil(options.file.size / chunkSize)
          );
          start = end;
          end = Math.min(options.file.size, start + chunkSize);
        } else {
          console.log("所有分片已上传完毕");
        }
      };

      // 开始处理第一个分片
      processNextChunk(1);
    } catch {
      proxy.$modal.alert(
        `${progress.value < 100 ? "上传" : "发送"}失败`,
        "提示"
      );
      handleCancelFile();
    }
  });
};

const dialogVisible = ref(false);
const formRef = ref();
const form = ref({
  status: 0,
  effectiveDate: "",
  effectiveTime: "",
  duration: 1,
  durationUnit: "1",
  loopTime: 1,
  loopTimeUnit: "1",
  messageContent: "",
  selectedDevices: "",
  deviceIds: [],
});

const rules = {
  effectiveDate: [
    // 先验证是否早于今天
    {
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        if (value) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const selectedDate = new Date(value);
          selectedDate.setHours(0, 0, 0, 0);
          if (selectedDate.getTime() < today.getTime()) {
            callback(new Error("生效日期不能早于今天"));
            return;
          }
        }
        callback();
      },
      trigger: "change",
    },
    // 再验证是否必填
    {
      required: true,
      message: "请选择生效日期",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (form.value.status === 1) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error("请选择生效日期"));
          return;
        }
        callback();
      },
    },
  ],
  effectiveTime: [
    {
      required: true,
      trigger: "change",
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        let date = new Date(form.value.effectiveDate),
          time = new Date(value);
        let year = date.getFullYear();
        let month = date.getMonth() + 1;
        let day = date.getDate();
        let hour = time.getHours();
        let minute = time.getMinutes();
        let second = time.getSeconds();
        let now = new Date();

        if (!value) {
          callback(new Error("请选择生效时间"));
        } else if (
          form.value.effectiveDate &&
          now.getTime() >
            new Date(year, month - 1, day, hour, minute, second).getTime()
        ) {
          callback(new Error("生效时间不能小于当前时间"));
        } else {
          callback();
        }
      },
    },
  ],
  duration: [
    {
      required: true,
      message: "请输入持续时间",
      trigger: "blur",
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        if (!value) {
          callback(new Error("请输入持续时间"));
        } else {
          callback();
        }
      },
    },
  ],
  loopTime: [
    {
      required: true,
      message: "请输入循环时间",
      trigger: "blur",
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        if (!value) {
          callback(new Error("请输入循环时间"));
        } else {
          callback();
        }
      },
    },
  ],
  messageContent: [
    {
      required: true,
      message: "请输入消息内容",
      trigger: "blur",
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        if (!value) {
          callback(new Error("请输入消息内容"));
        } else {
          callback();
        }
      },
    },
  ],
  selectedDevices: [
    {
      required: true,
      message: "请选择接收设备",
      trigger: "change",
      validator: (rule, value, callback) => {
        // 如果状态为禁用，跳过验证
        if (form.value.status === 1) {
          callback();
          return;
        }

        if (!value) {
          callback(new Error("请选择接收设备"));
        } else {
          callback();
        }
      },
    },
  ],
};

function changeType(val) {
  if (val != 5) {
    handleQuery();
  } else {
    handleQueryPosition("", 0);
    handleQueryPosition("", 1);
  }
}

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1;
  getList();
}

/** 搜索按钮操作 */
function handleQueryOff() {
  queryOffParams.value.current = 1;
  // getOffList();
}
function handleQueryUsb() {
  queryUsbParams.value.current = 1;
  // getUsbList();
}

function handleQueryPosition(val, type) {
  if (type == 0) {
    queryOffPositionResult.value = {
      ids: [],
      names: [],
    };
    queryOffPositionResult.value.ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == val
    );
    const arr = queryOffPositionResult.value.ids;
    for (let i = 0; i < arr.length; i++) {
      queryOffPositionResult.value.names[i] = positionList.value.find(
        (node) => node.id == arr[i]
      ).name;
    }
    console.log("queryOffPositionResult ==> ", state.queryOffPositionResult);
    state.queryOffParams.positionIds =
      state.queryOffPositionResult.ids.join(",");
    handleQueryOff();
  }
  if (type == 1) {
    queryUsbPositionResult.value = {
      ids: [],
      names: [],
    };
    queryUsbPositionResult.value.ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == val
    );
    const arr = queryUsbPositionResult.value.ids;
    for (let i = 0; i < arr.length; i++) {
      queryUsbPositionResult.value.names[i] = positionList.value.find(
        (node) => node.id == arr[i]
      ).name;
    }
    console.log("queryUsbPositionResult ==> ", state.queryUsbPositionResult);
    state.queryUsbParams.positionIds =
      state.queryUsbPositionResult.ids.join(",");
    handleQueryUsb();
  }
  if (type == 2) {
    queryPositionResult.value = {
      ids: [],
      names: [],
    };
    queryPositionResult.value.ids = treeFindPath(
      positionTreeList.value,
      (d) => d.id == val
    );
    const arr = queryPositionResult.value.ids;
    for (let i = 0; i < arr.length; i++) {
      queryPositionResult.value.names[i] = positionList.value.find(
        (node) => node.id == arr[i]
      ).name;
    }
    console.log("queryPositionResult ==> ", state.queryPositionResult);
    state.queryParams.positionId = state.queryPositionResult.ids.join(",");
    handleQuery();
  }
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    positionTreeList.value = response.data;
    positionList.value = treeToArray(response.data);
    console.log("positionList ==>", positionList.value);
  });
}

function getList() {
  loading.value = true;
  listTimePlan(queryParams.value)
    .then((res) => {
      if (res.code === 200) {
        console.log(res.data, "res.data");
        tableList.value = res.data.messageTimePlanVOList.map((item) => ({
          id: item.id,
          status: item.status === 1 ? "禁用" : "启用",
          effectiveDate: item.noticeTime,
          duration: item.duration,
          messageContent: item.content,
          receiveDevice: item.deviceCodes.join(","),
        }));
        total.value = res.data.total;
      }
    })
    .finally(() => {
      loading.value = false;
    });
}

onMounted(() => {
  getList();
  getDeptList();
  getSwitchMachineList();
  getLockScreenPlanList();
});

onActivated(() => {
  console.log("组件激活");
  getList();
  getPositionTreeList();
});

// 添加所有展开状态控制的响应式变量
const currentExpandedKeys = ref([]);
const startExpandedKeys = ref([]);
const endExpandedKeys = ref([]);

const defaultExpandedKeys = ref([1, 2]); // 只展开"全选"和"云天数据"

const resetExpandedKeys = () => {
  currentExpandedKeys.value = [...defaultExpandedKeys.value];
  startExpandedKeys.value = [...defaultExpandedKeys.value];
  endExpandedKeys.value = [...defaultExpandedKeys.value];
};

const handleAdd = () => {
  dialogVisible.value = true;
  dialogTitle.value = "添加计划";
  resetForm();
  resetExpandedKeys(); // 重置展开状态
  nextTick(() => {
    const treeContainer = document.querySelector(".tree-container");
    if (treeContainer) {
      treeContainer.scrollTop = 0;
    }
  });
};

const resetForm = () => {
  form.value = {
    id: "",
    status: 0,
    effectiveDate: "",
    effectiveTime: "",
    duration: 1,
    durationUnit: "1",
    loopTime: 1,
    loopTimeUnit: "1",
    messageContent: "",
    selectedDevices: "",
    deviceIds: [],
  };
  if (deviceTreeRef.value) {
    deviceTreeRef.value.setCheckedKeys([]);
  }
};

const submitForm = async () => {
  if (!formRef.value) return;
  await formRef.value.validate((valid) => {
    if (valid) {
      // 格式化日期时间
      const formatDateTime = (date) => {
        if (!date) return null;
        const year = date.getFullYear();
        const month = (date.getMonth() + 1).toString().padStart(2, "0");
        const day = date.getDate().toString().padStart(2, "0");
        const hours = date.getHours().toString().padStart(2, "0");
        const minutes = date.getMinutes().toString().padStart(2, "0");
        const seconds = date.getSeconds().toString().padStart(2, "0");
        return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      };

      // 计算轮播时间的秒数
      let carouselSeconds = form.value.loopTime;
      switch (form.value.loopTimeUnit) {
        case "2": // 分钟
          carouselSeconds = form.value.loopTime * 60;
          break;
        case "3": // 小时
          carouselSeconds = form.value.loopTime * 60 * 60;
          break;
        case "4": // 天
          carouselSeconds = form.value.loopTime * 24 * 60 * 60;
          break;
        default: // 秒
          carouselSeconds = form.value.loopTime;
      }

      // 处理生效时间
      let effectiveDateTime = null;
      if (form.value.effectiveDate && form.value.effectiveTime) {
        const now = new Date();
        const selectedDateTime = new Date(form.value.effectiveDate);
        selectedDateTime.setHours(
          form.value.effectiveTime.getHours(),
          form.value.effectiveTime.getMinutes(),
          form.value.effectiveTime.getSeconds()
        );

        if (selectedDateTime.getTime() <= now.getTime()) {
          const adjustedTime = new Date(now.getTime() + 5000);
          effectiveDateTime = formatDateTime(adjustedTime);
        } else {
          effectiveDateTime = formatDateTime(selectedDateTime);
        }
      }

      let submitData = {
        status: form.value.status,
        deviceCodeList: form.value.selectedDevices
          ? form.value.selectedDevices.split(",").filter(Boolean)
          : [""],
        message: form.value.messageContent || "",
        isTop: false,
        noticeTime: effectiveDateTime,
        lastTimeNum: form.value.duration || 1,
        lastTimeUnit: Number(form.value.durationUnit || 1),
        second: carouselSeconds || 1,
        carouselUnit: Number(form.value.loopTimeUnit || 1),
      };

      if (form.value.id) {
        submitData.id = form.value.id;
      }

      console.log("提交数据:", submitData, JSON.stringify(submitData));

      // 调用接口
      sendMessageMqttOrHttp(submitData)
        .then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("操作成功");
            dialogVisible.value = false;
            getList();
          } else {
            proxy.$modal.msgError(res.msg || "操作失败");
          }
        })
        .catch((err) => {
          proxy.$modal.msgError("操作失败");
        });
    }
  });
};

const handleCancel = () => {
  dialogVisible.value = false;
  resetForm();
  if (formRef.value) {
    formRef.value.resetFields();
  }
  // 清空展开状态
  currentExpandedKeys.value = [];
};

const deviceControlDialogVisible = ref(false);
const deviceControlFormRef = ref(null);
const deviceTreeRef = ref(null);

const deviceControlForm = ref({
  status: 1,
  startEnabled: false,
  endEnabled: false,
  startFrequency: "daily-once",
  startWeekday: "1",
  startTime1: "",
  startTime2: "",
  selectedStartDevices: "",
  selectedEndDevices: "",
  startDeviceIds: [],
  endDeviceIds: [],
  endFrequency: "daily-once",
  endWeekday: "1",
  endTime1: "",
  endTime2: "",
});

const deviceControlRules = {
  startTime1: [
    {
      required: true,
      message: "请选择生效时间",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (!deviceControlForm.value.startEnabled) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error("请选择生效时间"));
          return;
        }
        callback();
      },
    },
  ],
  startTime2: [
    {
      required: true,
      message: "请选择第二个生效时间",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (
          !deviceControlForm.value.startEnabled ||
          deviceControlForm.value.startFrequency !== "daily-twice"
        ) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error("请选择第二个生效时间"));
          return;
        }
        callback();
      },
    },
  ],
  endTime1: [
    {
      required: true,
      message: "请选择生效时间",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (!deviceControlForm.value.endEnabled) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error("请选择生效时间"));
          return;
        }
        callback();
      },
    },
  ],
  endTime2: [
    {
      required: true,
      message: "请选择第二个生效时间",
      trigger: "change",
      validator: (rule, value, callback) => {
        if (
          !deviceControlForm.value.endEnabled ||
          deviceControlForm.value.endFrequency !== "daily-twice"
        ) {
          callback();
          return;
        }
        if (!value) {
          callback(new Error("请选择第二个生效时间"));
          return;
        }
        callback();
      },
    },
  ],
};

const handleAddDeviceControl = () => {
  deviceControlDialogVisible.value = true;
  deviceControlDialogTitle.value = "添加设备开关机定时计划";
  resetDeviceControlForm();

  // 设置默认展开的节点
  startExpandedKeys.value = [1, 2]; // 展开"全选"和第一级节点
  endExpandedKeys.value = [1, 2]; // 展开"全选"和第一级节点

  nextTick(() => {
    // 重置树形结构的滚动位置
    const treeContainers = document.querySelectorAll(".tree-container");
    treeContainers.forEach((container) => {
      if (container) {
        container.scrollTop = 0;
      }
    });
  });
};

const resetDeviceControlForm = () => {
  deviceControlForm.value = {
    status: 0, // 默认启用
    startEnabled: false, // 修改为默认关闭
    endEnabled: false, // 修改为默认关闭
    startFrequency: "daily-once",
    startWeekday: "1",
    startTime1: "",
    startTime2: "",
    selectedStartDevices: "",
    selectedEndDevices: "",
    startDeviceIds: [],
    endDeviceIds: [],
    endFrequency: "daily-once",
    endWeekday: "1",
    endTime1: "",
    endTime2: "",
  };
  if (startDeviceTreeRef.value) {
    startDeviceTreeRef.value.setCheckedKeys([]);
  }
  if (endDeviceTreeRef.value) {
    endDeviceTreeRef.value.setCheckedKeys([]);
  }
};

const submitDeviceControlForm = async () => {
  if (!deviceControlFormRef.value) return;

  await deviceControlFormRef.value.validate((valid) => {
    if (valid) {
      // 构建提交数据
      const submitData = {
        statusTotal: deviceControlForm.value.status,
        info: [],
      };

      if (deviceControlForm.value.id) {
        submitData.id = deviceControlForm.value.id;
      }

      // 开机规则 - 无论是否启用都需要传递基础参数
      const startRule = {
        status: deviceControlForm.value.startEnabled
          ? deviceControlForm.value.status
          : 1,
        type: 1,
        frequency: 1, // 默认值
        oneEffectiveTime: "00:00:00", // 默认时间
        deviceCodes: [], // 空数组
      };

      if (deviceControlForm.value.startEnabled) {
        startRule.frequency = mapOptionToFrequency(
          deviceControlForm.value.startFrequency
        );
        startRule.deviceCodes = deviceControlForm.value.selectedStartDevices
          ? deviceControlForm.value.selectedStartDevices.split(",")
          : [];

        // 添加时间1
        if (deviceControlForm.value.startTime1) {
          startRule.oneEffectiveTime = formatTime(
            deviceControlForm.value.startTime1
          );
        }

        // 如果是每天两次，添加时间2
        if (
          deviceControlForm.value.startFrequency === "daily-twice" &&
          deviceControlForm.value.startTime2
        ) {
          startRule.twoEffectiveTime = formatTime(
            deviceControlForm.value.startTime2
          );
        }

        // 如果是每周一次，添加生效日期
        if (deviceControlForm.value.startFrequency === "weekly-once") {
          startRule.effectiveDate = Number(
            deviceControlForm.value.startWeekday
          );
        }
      }

      submitData.info.push(startRule);

      // 关机规则 - 无论是否启用都需要传递基础参数
      const endRule = {
        status: deviceControlForm.value.endEnabled
          ? deviceControlForm.value.status
          : 1,
        type: 0,
        frequency: 1, // 默认值
        oneEffectiveTime: "00:00:00", // 默认时间
        deviceCodes: [], // 空数组
      };

      if (deviceControlForm.value.endEnabled) {
        endRule.frequency = mapOptionToFrequency(
          deviceControlForm.value.endFrequency
        );
        endRule.deviceCodes = deviceControlForm.value.selectedEndDevices
          ? deviceControlForm.value.selectedEndDevices.split(",")
          : [];

        // 添加时间1
        if (deviceControlForm.value.endTime1) {
          endRule.oneEffectiveTime = formatTime(
            deviceControlForm.value.endTime1
          );
        }

        // 如果是每天两次，添加时间2
        if (
          deviceControlForm.value.endFrequency === "daily-twice" &&
          deviceControlForm.value.endTime2
        ) {
          endRule.twoEffectiveTime = formatTime(
            deviceControlForm.value.endTime2
          );
        }

        // 如果是每周一次，添加生效日期
        if (deviceControlForm.value.endFrequency === "weekly-once") {
          endRule.effectiveDate = Number(deviceControlForm.value.endWeekday);
        }
      }

      submitData.info.push(endRule);
      console.log(submitData, "修改或新增传递的开关机参数");
      // return
      // 调用保存接口
      proxy.$modal.loading();
      addSwitchMachine(submitData).then(async (res) => {
        if (res.code === 200) {
          // 成功后分别添加开机和关机的操作日志
          try {
            // 如果启用了开机计划，记录开机日志
            if (deviceControlForm.value.startEnabled) {
              const startDeviceCodes =
                deviceControlForm.value.selectedStartDevices
                  ?.split(",")
                  .filter(Boolean) || [];
              if (startDeviceCodes.length > 0) {
                const startParams = {
                  logType: 3,
                  deviceCode: startDeviceCodes.join("、"),
                  logContent: "设备定时开机",
                  deviceNum: startDeviceCodes.length,
                };
                console.log(startParams, "开机日志参数");

                await addSchoolDeviceLog(startParams);
              }
            }

            // 如果启用了关机计划，记录关机日志
            if (deviceControlForm.value.endEnabled) {
              const endDeviceCodes =
                deviceControlForm.value.selectedEndDevices
                  ?.split(",")
                  .filter(Boolean) || [];
              if (endDeviceCodes.length > 0) {
                const endParams = {
                  logType: 3,
                  deviceCode: endDeviceCodes.join("、"),
                  logContent: "设备定时关机",
                  deviceNum: endDeviceCodes.length,
                };
                console.log(endParams, "关机日志参数");
                await addSchoolDeviceLog(endParams);
              }
            }
          } catch (error) {
            console.error("添加操作日志失败:", error);
          }

          proxy.$modal.msgSuccess("操作成功");
          proxy.$modal.closeLoading();
          deviceControlDialogVisible.value = false;
          getSwitchMachineList();
        } else {
          proxy.$modal.msgError(res.msg || "操作失败");
        }
      });
    }
  });
};

// 格式化时间的统一函数
const formatTime = (date) => {
  if (!date) return null;
  const hours = date.getHours().toString().padStart(2, "0");
  const minutes = date.getMinutes().toString().padStart(2, "0");
  const seconds = date.getSeconds().toString().padStart(2, "0");
  return `${hours}:${minutes}:${seconds}`;
};

const handleEdit = (row) => {
  dialogVisible.value = true;
  dialogTitle.value = "修改计划";

  getTimePlan(row.id).then((res) => {
    if (res.code === 200) {
      const data = res.data;

      // 计算轮播时间和单位
      let loopTime = data.carouselSecond;
      let loopTimeUnit = String(data.carouselUnit || "1");

      // 根据单位反向计算显示值
      switch (data.carouselUnit) {
        case 2: // 分钟
          loopTime = data.carouselSecond / 60;
          break;
        case 3: // 小时
          loopTime = data.carouselSecond / (60 * 60);
          break;
        case 4: // 天
          loopTime = data.carouselSecond / (24 * 60 * 60);
          break;
        default: // 秒
          loopTime = data.carouselSecond;
      }

      // 正确解析原始时间
      const [datePart, timePart] = data.noticeTime.split(" ");

      // 先设置基本表单数据
      form.value = {
        id: row.id,
        status: data.status,
        effectiveDate: datePart,
        effectiveTime: new Date(`1970-01-01T${timePart}`),
        duration: data.lastTimeNum,
        durationUnit: String(data.lastTimeUnit || "1"),
        loopTime: loopTime,
        loopTimeUnit: loopTimeUnit,
        messageContent: data.content,
        selectedDevices: data.deviceCodes.join(","),
        deviceIds: [],
      };

      // 使用 setTimeout 确保对话框完全渲染后再设置树的选中状态
      setTimeout(() => {
        // 等待 DOM 更新完成
        nextTick(() => {
          if (deviceTreeRef.value) {
            const nodeIds = data.deviceCodes
              .map((code) => findDeviceNodeId(code))
              .filter(Boolean);
            deviceTreeRef.value.setCheckedKeys(nodeIds);
            form.value.deviceIds = nodeIds;

            // 重置树形结构的滚动位置
            const treeContainer = document.querySelector(".tree-container");
            if (treeContainer) {
              treeContainer.scrollTop = 0;
            }

            // 恢复展开状态
            resetExpandedKeys();
          }
        });
      }, 100);
    }
  });
};

const handleEditDeviceControl = async (row) => {
  try {
    // 显示加载中
    proxy.$modal.loading("正在加载数据...");

    // 先重置表单和清空状态
    resetDeviceControlForm();

    // 获取数据
    const res = await getSwitchMachine({ id: row.id });

    if (res.code === 200) {
      const data = res.data;
      console.log(data, "获取的设备开关机定时计划数据");

      // 确保数据存在且格式正确
      if (!data || !data.info || !Array.isArray(data.info)) {
        throw new Error("数据格式错误");
      }

      // 从info数组中获取开机和关机计划
      const startPlan = data.info.find((item) => item.type === 1);
      const endPlan = data.info.find((item) => item.type === 0);

      // 先设置基础数据
      deviceControlForm.value = {
        id: row.id,
        status: data.statusTotal,
        startEnabled: false,
        endEnabled: false,
        startFrequency: "daily-once",
        startWeekday: "1",
        startTime1: null,
        startTime2: null,
        selectedStartDevices: "",
        endFrequency: "daily-once",
        endWeekday: "1",
        endTime1: null,
        endTime2: null,
        selectedEndDevices: "",
        startDeviceIds: [],
        endDeviceIds: [],
      };

      // 等待基础数据设置完成
      await nextTick();

      // 再设置开机规则数据
      if (startPlan) {
        deviceControlForm.value.startEnabled = startPlan.status === 0;
        deviceControlForm.value.startFrequency = mapFrequencyToOption(
          startPlan.frequency
        );
        deviceControlForm.value.startWeekday =
          startPlan.effectiveDate?.toString() || "1";
        deviceControlForm.value.startTime1 = startPlan.oneEffectiveTime
          ? new Date(`1970-01-01T${startPlan.oneEffectiveTime}`)
          : null;
        deviceControlForm.value.startTime2 = startPlan.twoEffectiveTime
          ? new Date(`1970-01-01T${startPlan.twoEffectiveTime}`)
          : null;
        deviceControlForm.value.selectedStartDevices =
          startPlan.deviceCodes?.join(",") || "";
      }

      // 设置关机规则数据
      if (endPlan) {
        deviceControlForm.value.endEnabled = endPlan.status === 0;
        deviceControlForm.value.endFrequency = mapFrequencyToOption(
          endPlan.frequency
        );
        deviceControlForm.value.endWeekday =
          endPlan.effectiveDate?.toString() || "1";
        deviceControlForm.value.endTime1 = endPlan.oneEffectiveTime
          ? new Date(`1970-01-01T${endPlan.oneEffectiveTime}`)
          : null;
        deviceControlForm.value.endTime2 = endPlan.twoEffectiveTime
          ? new Date(`1970-01-01T${endPlan.twoEffectiveTime}`)
          : null;
        deviceControlForm.value.selectedEndDevices =
          endPlan.deviceCodes?.join(",") || "";
      }

      // 等待所有数据设置完成
      await nextTick();

      // 打开对话框
      deviceControlDialogTitle.value = "修改设备开关机定时计划";
      deviceControlDialogVisible.value = true;

      // 设置默认展开的节点
      startExpandedKeys.value = [1, 2];
      endExpandedKeys.value = [1, 2];

      // 再次等待对话框渲染完成
      await nextTick();

      // 设置树形选中状态
      if (startPlan?.deviceCodes) {
        const startNodeIds = startPlan.deviceCodes
          .map((code) => findDeviceNodeId(code))
          .filter(Boolean);
        if (startDeviceTreeRef.value) {
          startDeviceTreeRef.value.setCheckedKeys(startNodeIds);
        }
      }

      if (endPlan?.deviceCodes) {
        const endNodeIds = endPlan.deviceCodes
          .map((code) => findDeviceNodeId(code))
          .filter(Boolean);
        if (endDeviceTreeRef.value) {
          endDeviceTreeRef.value.setCheckedKeys(endNodeIds);
        }
      }
    } else {
      proxy.$modal.msgError(res.msg || "获取数据失败");
    }
  } catch (error) {
    console.error("获取数据失败:", error);
    proxy.$modal.msgError("获取数据失败");
  } finally {
    proxy.$modal.closeLoading();
  }
};

// 修改状态变化处理方法
const handleStatusChange = (val) => {
  if (val === 1) {
    // 禁用状态
    deviceControlForm.value.startEnabled = false;
    deviceControlForm.value.endEnabled = false;
  }
};

// 添加辅助函数：将频率数字映射为选项值
const mapFrequencyToOption = (frequency) => {
  switch (frequency) {
    case 1:
      return "daily-once"; // 每天一次
    case 2:
      return "daily-twice"; // 每天两次
    case 3:
      return "weekly-once"; // 每周一次
    default:
      return "daily-once";
  }
};

// 添加辅助函数：解析时间字符串为 Date 对象
const parseTimeString = (timeStr) => {
  if (!timeStr) return null;
  return new Date(`1970-01-01T${timeStr}`);
};

function changeLockImg() {
  console.log(
    hasScreenImg.value,
    "hasScreenImg",
    data.lockRuleForm.lockImg,
    "lockImg",
    data.lockRuleForm.lockUrl,
    "lockUrl",
    isImgChange.value,
    "isImgChange"
  );
}

const handlePictureCardPreview = (file) => {
  dialogImageUrl.value = file?.url || data.lockRuleForm.lockUrl;
  dialogVisibleImg.value = true;
};

const uploadImgRef = ref(null);
const handleImgChange = (file) => {
  console.log(file);

  let fileName = file.name;
  let index = fileName.lastIndexOf(".");
  let fileType = fileName.substring(index + 1);
  let whiteName = ["jpg", "jpeg", "png"];
  if (file.status == "ready") {
    if (whiteName.indexOf(fileType) == -1) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning(`仅支持${whiteName.join("/")}格式的图片`);
      return;
    }

    if (file.size > 5 * 1024 * 1024) {
      uploadImgRef.value.clearFiles();
      proxy.$modal.msgWarning("图片大小不能超过5MB");
      return;
    }

    isImgChange.value = true;
    data.lockRuleForm.lockUrl = file.url;
  }
};

const handleImgExceed = (files) => {
  uploadImgRef.value.clearFiles();
  console.log(files);

  const file = files[0];
  file.uid = genFileId();
  uploadImgRef.value.handleStart(file);
  data.lockRuleForm.lockUrl = file.name;
};

function findDeviceNodeId(deviceCode) {
  let foundId = null;

  const findId = (nodes) => {
    for (const node of nodes) {
      if (node.children?.length < 1 || !node.children) {
        // 检查节点是否包含分隔符
        const nodeDeviceCode = node.label.includes("-")
          ? node.label.split("-")[1]
          : node.label;

        if (nodeDeviceCode === deviceCode) {
          foundId = node.id;
          break;
        }
      } else if (node.children.length > 0) {
        findId(node.children);
      }
    }
  };

  findId(deviceTreeData.value);
  return foundId;
}

async function handleLockRule(isAdd = false) {
  try {
    if (isAdd) {
      // 添加时显示初始空数据
      ruleOpen.value = true;

      await nextTick();

      // 重置表单数据到初始状态
      data.lockRuleForm = {
        num: 10,
        unit: 1,
        timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
        timeList: ["08:00:00", "18:00:00"],
        lockImg: 0, // 默认选择默认图片
        lockUrl: "",
        isLock: false,
        isAutoLock: false, // 自动锁定规则设置默认关闭
        isDisable: false, // 自动解锁规则设置默认关闭
        selectedDevices: "",
        deviceIds: [],
        deviceCodes: [], // 确保清空设备代码
      };

      // 清空树选择
      if (lockRuleDeviceTreeRef.value) {
        lockRuleDeviceTreeRef.value.setCheckedKeys([]);
      }

      // 重置图片相关状态
      hasScreenImg.value = false;
      isImgChange.value = false;
    } else {
      // 编辑时获取现有数据
      const res = await getLockScreenRule();
      console.log("锁屏规则回显", res);

      if (res.data) {
        const deviceCodes =
          Array.isArray(res.data.deviceCodes) && res.data.deviceCodes.length > 0
            ? res.data.deviceCodes
            : [];

        console.log("处理后的Device codes:", deviceCodes);

        ruleOpen.value = true;

        await nextTick();

        if (lockRuleDeviceTreeRef.value) {
          lockRuleDeviceTreeRef.value.setCheckedKeys([]);
        }

        Object.assign(data.lockRuleForm, {
          ...res.data,
          timeRangeList: res.data.isDisable
            ? res.data.timeRangeList?.map((item) => ({
                ...item,
                time: [item.startTime, item.endTime],
              })) || [{ time: ["08:00:00", "18:00:00"] }]
            : [{ time: ["08:00:00", "18:00:00"] }],
          selectedDevices: deviceCodes.join(","),
          deviceCodes: deviceCodes,
        });

        if (deviceCodes.length > 0) {
          const nodeIds = deviceCodes
            .map((code) => findDeviceNodeId(code))
            .filter(Boolean);

          console.log("要设置的节点IDs:", nodeIds);

          if (lockRuleDeviceTreeRef.value && nodeIds.length > 0) {
            lockRuleDeviceTreeRef.value.setCheckedKeys(nodeIds);
          }
        }

        // 获取锁屏图片
        const imgRes = await downScreenImg();
        console.log("锁屏图片回显", imgRes, imgRes.size);
        if (imgRes && imgRes.size > 76) {
          data.lockRuleForm.lockImg = 1;
          data.lockRuleForm.lockUrl = window.URL.createObjectURL(imgRes);
          hasScreenImg.value = true;
        } else {
          data.lockRuleForm.lockImg = 0;
          hasScreenImg.value = false;
        }
      }
    }

    console.log(
      hasScreenImg.value,
      "hasScreenImg",
      data.lockRuleForm.lockImg,
      "lockImg",
      data.lockRuleForm.lockUrl,
      "lockUrl",
      isImgChange.value,
      "isImgChange"
    );
  } catch (error) {
    console.error("Error in handleLockRule:", error);
  }
}

function handleRule() {
  if (
    lockRuleForm.value.timeRangeList.some(
      (item) => item.time == null || item.length < 1
    )
  ) {
    proxy.$modal.msgWarning("自动解锁时间范围不能为空");
    return;
  }

  if (lockRuleForm.value.selectedDevices.length < 1) {
    proxy.$modal.msgWarning("请至少选择一台接收设备");
    return;
  }
  data.lockRuleRef.validate((valid) => {
    if (valid) {
      console.log(valid);
      if (data.lockRuleForm.lockImg == 0) {
        if (hasScreenImg.value) {
          deleteScreenImg().then((res) => {
            submitRule();
          });
        } else {
          submitRule();
        }
      } else {
        if (!!data.lockRuleForm.lockUrl) {
          if (isImgChange.value) {
            proxy.$refs.uploadImgRef.submit();
          } else {
            submitRule();
          }
        } else {
          proxy.$modal.msgWarning("请选择锁屏屏保图片");
        }
      }
    }
  });
}

// 修改 handleCancelRule 函数，确保完全清空数据
function handleCancelRule() {
  isImgChange.value = false;
  uploadImgRef.value.clearFiles();

  // 重置表单数据到初始状态
  data.lockRuleForm = {
    num: 10,
    unit: 1,
    timeRangeList: [{ time: ["08:00:00", "18:00:00"] }],
    timeList: ["08:00:00", "18:00:00"],
    lockImg: 0,
    lockUrl: "",
    isAutoLock: false,
    isDisable: false,
    isLock: false,
    selectedDevices: "",
    deviceIds: [],
    deviceCodes: [], // 确保清空设备代码
  };

  // 清空树选择
  if (lockRuleDeviceTreeRef.value) {
    lockRuleDeviceTreeRef.value.setCheckedKeys([]);
  }

  lockRuleRef.value.resetFields();
  ruleOpen.value = false;
}

function handleSubmit(d) {
  // 构造提交数据
  const submitData = {
    num: d.num,
    unit: d.unit,
    // timeList: d.timeList,
    timeList: d.isDisable
      ? d.timeRangeList.map((item) => {
          return {
            startTime: item.time[0],
            endTime: item.time[1],
          };
        })
      : [],
    lockImg: d.lockImg,
    isAutoLock: d.isAutoLock,
    isDisable: d.isDisable,
    isLock: d.isLock,
    lockTime: d.lockTime,
    deviceCodes: d.deviceCodes,
    lockUrl: d.lockImg === 1 ? d.lockUrl : "",
  };

  console.log("最终提交的数据:", submitData, JSON.stringify(submitData));

  proxy.$modal.loading();
  lockScreenRuleBatch(submitData)
    .then(async (res) => {
      if (res.code === 200) {
        // 添加操作日志
        try {
          const logPromises = [];

          // 如果启用了自动锁定规则且有选择设备
          if (submitData.isAutoLock && submitData.deviceCodes.length > 0) {
            // 为每个设备创建一个锁屏日志
            submitData.deviceCodes.forEach((deviceCode) => {
              logPromises.push(
                addSchoolDeviceLog({
                  logType: 4,
                  deviceCode: deviceCode,
                  logContent: "设备后台锁屏",
                  deviceNum: 1,
                })
              );
            });
          }

          // 如果启用了自动解锁规则且有选择设备
          if (submitData.isDisable && submitData.deviceCodes.length > 0) {
            // 为每个设备创建一个解锁日志
            submitData.deviceCodes.forEach((deviceCode) => {
              logPromises.push(
                addSchoolDeviceLog({
                  logType: 4,
                  deviceCode: deviceCode,
                  logContent: "设备后台解锁",
                  deviceNum: 1,
                })
              );
            });
          }

          // 等待所有日志添加完成
          await Promise.all(logPromises);
        } catch (error) {
          console.error("添加操作日志失败:", error);
        }

        proxy.$modal.msgSuccess("操作成功");
        // 操作成功后立即重新获取最新数据
        getLockScreenPlanList().then(() => {
          console.log("表格数据已更新");
        });
      } else {
        proxy.$modal.msgError(res.msg || "操作失败");
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading();
      lockRuleDialogRef.value.handleCancelRule();
    });
}

function handleCancelSubmit() {
  ruleOpen.value = false;
}

function submitRule() {
  // 获取树形组件当前选中的所有节点
  const checkedNodes = lockRuleDeviceTreeRef.value.getCheckedNodes();

  // 从选中的节点中提取设备代码，只保留 ZHDP 开头的设备编号
  const deviceCodes = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children) // 只取叶子节点
    .map((node) => {
      const label = node.label;
      // 如果包含 '-'，取后半部分，否则使用整个 label
      return label.includes("-") ? label.split("-")[1] : label;
    })
    .filter(Boolean); // 过滤掉空值

  console.log("选中的设备代码:", deviceCodes);
  // 构造提交数据
  const submitData = {
    num: data.lockRuleForm.num,
    unit: data.lockRuleForm.unit,
    // timeList: data.lockRuleForm.timeList,
    timeList: data.lockRuleForm.timeRangeList.map((item) => {
      return {
        startTime: item.time[0],
        endTime: item.time[1],
      };
    }),
    lockImg: data.lockRuleForm.lockImg,
    isAutoLock: data.lockRuleForm.isAutoLock,
    isDisable: data.lockRuleForm.isDisable,
    isLock: data.lockRuleForm.isLock,
    lockTime: data.lockRuleForm.lockTime,
    deviceCodes: deviceCodes,
    lockUrl: data.lockRuleForm.lockImg === 1 ? data.lockRuleForm.lockUrl : "",
  };

  console.log("最终提交的数据:", submitData, JSON.stringify(submitData));

  proxy.$modal.loading();
  lockScreenRuleBatch(submitData)
    .then(async (res) => {
      if (res.code === 200) {
        // 添加操作日志
        try {
          const logPromises = [];

          // 如果启用了自动锁定规则且有选择设备
          if (submitData.isAutoLock && submitData.deviceCodes.length > 0) {
            // 为每个设备创建一个锁屏日志
            submitData.deviceCodes.forEach((deviceCode) => {
              logPromises.push(
                addSchoolDeviceLog({
                  logType: 4,
                  deviceCode: deviceCode,
                  logContent: "设备后台锁屏",
                  deviceNum: 1,
                })
              );
            });
          }

          // 如果启用了自动解锁规则且有选择设备
          if (submitData.isDisable && submitData.deviceCodes.length > 0) {
            // 为每个设备创建一个解锁日志
            submitData.deviceCodes.forEach((deviceCode) => {
              logPromises.push(
                addSchoolDeviceLog({
                  logType: 4,
                  deviceCode: deviceCode,
                  logContent: "设备后台解锁",
                  deviceNum: 1,
                })
              );
            });
          }

          // 等待所有日志添加完成
          await Promise.all(logPromises);
        } catch (error) {
          console.error("添加操作日志失败:", error);
        }

        proxy.$modal.msgSuccess("操作成功");
        // 操作成功后立即重新获取最新数据
        getLockScreenPlanList().then(() => {
          console.log("表格数据已更新");
        });
      } else {
        proxy.$modal.msgError(res.msg || "操作失败");
      }
    })
    .finally(() => {
      proxy.$modal.closeLoading();
      handleCancelRule();
    });
}

const handleCancelDeviceControl = () => {
  deviceControlDialogVisible.value = false;
  resetDeviceControlForm();
  if (deviceControlFormRef.value) {
    deviceControlFormRef.value.resetFields();
  }
  // 清空展开状态
  startExpandedKeys.value = [];
  endExpandedKeys.value = [];
};

const lockScreenDialogVisible = ref(false);
const lockScreenFormRef = ref(null);
const lockScreenForm = ref({
  lockTime: 1,
  unit: 1,
  time: null,
  lockImg: 0,
  images: "",
});

async function getDeptList() {
  try {
    const params = {
      smartScreen: 1,
      abnormalInterruptionChannel: 0,
      current: 1,
      size: 9999999,
      tenantId: "",
      deviceName: "",
      model: "",
      typeId: 1,
      deviceStatus: "",
      positionIds: [],
      putTime: "",
      status: [],
    };

    const res = await devicePage(params);

    if (res.code === 200) {
      deviceTreeData.value = buildTree(res.data.page.records);

      // const treeData = [
      //   {
      //     id: 1,
      //     label: "全选",
      //     children: [],
      //   },
      // ];

      // const nodeMap = new Map();
      // let currentId = 2;

      // res.data.page.records.forEach((device) => {
      //   // 如果没有安装地址,直接添加设备节点
      //   if (!device.installAddress || device.installAddress.trim() === "") {
      //     treeData[0].children.push({
      //       id: currentId++,
      //       label: device.deviceCode, // 直接使用设备编号,不加前缀
      //     });
      //     return;
      //   }

      //   // 有安装地址的情况保持原有逻辑
      //   const addressParts = device.installAddress.split("-");
      //   let currentLevel = treeData[0].children;
      //   let parentPath = "";

      //   addressParts.forEach((part, index) => {
      //     parentPath = parentPath ? `${parentPath}-${part}` : part;

      //     if (index === addressParts.length - 1) {
      //       currentLevel.push({
      //         id: currentId++,
      //         label: `${part}-${device.deviceCode}`,
      //       });
      //     } else {
      //       let node = nodeMap.get(parentPath);
      //       if (!node) {
      //         node = {
      //           id: currentId++,
      //           label: part,
      //           children: [],
      //         };
      //         nodeMap.set(parentPath, node);
      //         currentLevel.push(node);
      //       }
      //       currentLevel = node.children;
      //     }
      //   });
      // });

      // deviceTreeData.value = treeData;
      console.log(deviceTreeData.value, "树形结构数据");
    }
  } catch (error) {
    console.error("获取设备列表失败:", error);
    proxy.$modal.msgError("获取设备列表失败");
  }
}

// 构建树形结构函数
function buildTree(data) {
  const root = { label: "全选", id: 0, children: [] };
  let nextId = 1;
  const nodeMap = new Map();

  // 按层级深度排序（浅→深）
  data.sort((a, b) => {
    const aDepth = a.installAddress ? a.installAddress.split("-").length : 0;
    const bDepth = b.installAddress ? b.installAddress.split("-").length : 0;
    return aDepth - bDepth;
  });

  // 第一步：创建所有分支节点
  data.forEach((item) => {
    if (!item.installAddress) return;

    const parts = item.installAddress.split("-");
    let currentPath = "";
    let parentNode = root;

    for (let i = 0; i < parts.length; i++) {
      const part = parts[i];
      currentPath = currentPath ? `${currentPath}-${part}` : part;

      if (!nodeMap.has(currentPath)) {
        const newNode = {
          label: part,
          id: nextId++,
          children: [],
        };

        parentNode.children.push(newNode);
        nodeMap.set(currentPath, newNode);
        parentNode = newNode;
      } else {
        parentNode = nodeMap.get(currentPath);
      }
    }
  });

  // 第二步：添加数据节点
  data.forEach((item) => {
    if (!item.installAddress) {
      root.children.push({
        label: item.deviceCode.toString(),
        id: nextId++,
        children: [],
      });
      return;
    }

    const parts = item.installAddress.split("-");
    const fullPath = item.installAddress;
    const parentPath = parts.slice(0, -1).join("-");

    // 查找父节点
    const parentNode =
      nodeMap.get(parentPath) || nodeMap.get(fullPath)
        ? nodeMap.get(fullPath)
        : root;

    // 检查是否已存在相同数据节点
    const exists = parentNode.children.some((child) =>
      child.label.includes(item.deviceCode.toString())
    );

    if (!exists) {
      const isLeaf = !nodeMap.has(fullPath);
      const leafLabel = isLeaf
        ? `${parts[parts.length - 1]}-${item.deviceCode}`
        : item.deviceCode.toString();

      parentNode.children.unshift({
        label: leafLabel,
        id: nextId++,
        children: [],
      });
    }
  });

  return [root];
}

function handleStartDeviceCheck(data, { checkedNodes, checkedKeys }) {
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children) // 过滤出叶子节点
    .map((node) => {
      // 如果节点标签包含'-'，说明是有位置信息的设备
      if (node.label.includes("-")) {
        return node.label.split("-")[1];
      }
      // 否则直接返回设备编号
      return node.label;
    })
    .join(",");

  deviceControlForm.value.selectedStartDevices = devices;
  deviceControlForm.value.startDeviceIds = checkedKeys;
}

function handleEndDeviceCheck(data, { checkedNodes, checkedKeys }) {
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children)
    .map((node) => {
      if (node.label.includes("-")) {
        return node.label.split("-")[1];
      }
      return node.label;
    })
    .join(",");

  deviceControlForm.value.selectedEndDevices = devices;
  deviceControlForm.value.endDeviceIds = checkedKeys;
}

function handleDeviceCheck(data, { checkedNodes, checkedKeys }) {
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children)
    .map((node) => {
      if (node.label.includes("-")) {
        return node.label.split("-")[1];
      }
      return node.label;
    })
    .join(",");

  form.value.selectedDevices = devices;
  form.value.deviceIds = checkedKeys;
}

function removeDevice(deviceToRemove) {
  const devices = form.value.selectedDevices
    .split(",")
    .filter((d) => d !== deviceToRemove);
  form.value.selectedDevices = devices.join(",");

  const treeNodes = deviceTreeRef.value.getCheckedNodes();
  const nodeToUncheck = treeNodes.find(
    (node) =>
      (node.children?.length < 1 || !node.children) &&
      (node.label.includes("-")
        ? node.label.split("-")[1] === deviceToRemove
        : node.label === deviceToRemove)
  );
  if (nodeToUncheck) {
    deviceTreeRef.value.setChecked(nodeToUncheck, false);
    form.value.deviceIds = deviceTreeRef.value.getCheckedKeys();
  }
}

function handleSizeChange(val) {
  queryParams.value.pageSize = val;
  getList();
}

function handleCurrentChange(val) {
  queryParams.value.current = val;
  getList();
}

// 添加dialogTitle的响应式引用
const dialogTitle = ref("添加计划");

// 添加日期限制方法
const disabledDate = (time) => {
  const today = new Date();
  today.setHours(0, 0, 0, 0);
  return time.getTime() < today.getTime();
};

// 添加开关机规则的处理方法
const handleStartEnabledChange = (val) => {
  if (!val) {
    // 如果禁用开机规则，清空相关字段
    deviceControlForm.value.startFrequency = "";
    deviceControlForm.value.startWeekday = "";
    deviceControlForm.value.startTime1 = null;
    deviceControlForm.value.startTime2 = null;
    deviceControlForm.value.selectedStartDevices = "";
    if (startDeviceTreeRef.value) {
      startDeviceTreeRef.value.setCheckedKeys([]);
    }
  } else {
    // 启用时设置默认值
    deviceControlForm.value.startFrequency = "daily-once";
    deviceControlForm.value.startWeekday = "1";
  }
};

const handleEndEnabledChange = (val) => {
  if (!val) {
    // 如果禁用关机规则，清空相关字段
    deviceControlForm.value.endFrequency = "";
    deviceControlForm.value.endWeekday = "";
    deviceControlForm.value.endTime1 = null;
    deviceControlForm.value.endTime2 = null;
    deviceControlForm.value.selectedEndDevices = "";
    if (endDeviceTreeRef.value) {
      endDeviceTreeRef.value.setCheckedKeys([]);
    }
  } else {
    // 启用时设置默认值
    deviceControlForm.value.endFrequency = "daily-once";
    deviceControlForm.value.endWeekday = "1";
  }
};

// 添加响应式引用
const switchMachineList = ref([]);

// 添加获取设备开关机计划的方法
const getSwitchMachineList = () => {
  loadingOff.value = true;
  listSwitchMachine()
    .then((res) => {
      if (res.code === 200) {
        // 处理数据
        console.log(res.data, "获取开关机列表数据");

        const processedData = processSwitchMachineData(res.data);
        switchMachineList.value = processedData;
      }
    })
    .finally(() => {
      loadingOff.value = false;
    });
};

// 处理设备开关机数据的方法
const processSwitchMachineData = (data) => {
  // 添加空值检查
  if (!data || !Array.isArray(data.list) || data.list.length === 0) {
    return [];
  }

  // 按id分组
  const groupedById = data.list.reduce((acc, curr) => {
    if (!acc[curr.id]) {
      acc[curr.id] = {
        plans: [],
        statusTotal: data.statusTotal,
      };
    }
    acc[curr.id].plans.push(curr);
    return acc;
  }, {});

  // 处理每组数据
  return Object.entries(groupedById).map(([id, group]) => {
    const { plans, statusTotal } = group;
    const startPlan = plans.find((item) => item.type === 1);
    const endPlan = plans.find((item) => item.type === 0);
    const isDisabled = statusTotal === 1;

    // 获取频率显示文本
    const getFrequencyText = (plan) => {
      if (!plan || plan.status === 1) return "";
      switch (plan.frequency) {
        case 1:
          return "每天一次";
        case 2:
          return "每天两次";
        case 3:
          return "每周一次";
        default:
          return "";
      }
    };

    // 获取时间文本
    const getEffectiveTimeText = (plan) => {
      if (!plan || plan.status === 1) return "";
      const times = [plan.oneEffectiveTime];
      if (plan.frequency === 2 && plan.twoEffectiveTime) {
        times.push(plan.twoEffectiveTime);
      }
      return times.filter(Boolean).join("、");
    };

    // 获取设备列表
    const getDevices = () => {
      if (isDisabled) return "-";
      const devices = [];
      if (startPlan && startPlan.status === 0) {
        devices.push(...(startPlan.deviceCodes || []));
      }
      if (endPlan && endPlan.status === 0) {
        devices.push(...(endPlan.deviceCodes || []));
      }
      return [...new Set(devices)].join("，") || "-";
    };

    // 获取频率显示
    const getFrequency = () => {
      if (isDisabled) return "-";
      const frequencies = [];
      const startFrequency = getFrequencyText(startPlan);
      const endFrequency = getFrequencyText(endPlan);

      if (startFrequency) {
        frequencies.push(`开机${startFrequency}`);
      }
      if (endFrequency) {
        frequencies.push(`关机${endFrequency}`);
      }
      return frequencies.length > 0 ? frequencies.join("，") : "-";
    };

    // 获取生效时间显示
    const getEffectiveTime = () => {
      if (isDisabled) return "-";
      const times = [];
      const startTime = getEffectiveTimeText(startPlan);
      const endTime = getEffectiveTimeText(endPlan);

      if (startTime) {
        times.push(`开机${startTime}`);
      }
      if (endTime) {
        times.push(`关机${endTime}`);
      }
      return times.length > 0 ? times.join("，") : "-";
    };

    return {
      id: id,
      status: isDisabled ? "禁用" : "启用",
      controlType: "定时开关机",
      frequency: getFrequency(),
      effectiveTime: getEffectiveTime(),
      devices: getDevices(),
    };
  });
};

// 添加对话框标题的响应式引用
const deviceControlDialogTitle = ref("添加设备开关机定时计划");

// 修改选项映射到频率的函数
const mapOptionToFrequency = (option) => {
  switch (option) {
    case "daily-once":
      return 1;
    case "daily-twice":
      return 2;
    case "weekly-once":
      return 3;
    default:
      return 1;
  }
};

// 添加状态变化的watch
watch(
  () => deviceControlForm.value.status,
  (newStatus) => {
    if (newStatus === 1) {
      // 禁用状态
      deviceControlForm.value.startEnabled = false;
      deviceControlForm.value.endEnabled = false;
    } else {
      // 启用状态
      deviceControlForm.value.startEnabled = false; // 修改为默认关闭
      deviceControlForm.value.endEnabled = false; // 修改为默认关闭
    }
  }
);

// 添加新的响应式引用
const lockRuleDeviceTreeRef = ref(null);
const lockRuleExpandedKeys = ref([1, 2]);

// 添加设备选择处理函数
function handleLockRuleDeviceCheck(data, { checkedNodes, checkedKeys }) {
  const devices = checkedNodes
    .filter((node) => node.children?.length < 1 || !node.children)
    .map((node) => {
      if (node.label.includes("-")) {
        return node.label.split("-")[1];
      }
      return node.label;
    })
    .join(",");

  lockRuleForm.value.selectedDevices = devices;
  lockRuleForm.value.deviceIds = checkedKeys;
}

// 添加设备移除处理函数
function removeLockRuleDevice(deviceToRemove) {
  const devices = lockRuleForm.value.selectedDevices
    .split(",")
    .filter((d) => d !== deviceToRemove);
  lockRuleForm.value.selectedDevices = devices.join(",");

  const treeNodes = lockRuleDeviceTreeRef.value.getCheckedNodes();
  const nodeToUncheck = treeNodes.find(
    (node) =>
      (node.children?.length < 1 || !node.children) &&
      (node.label.includes("-")
        ? node.label.split("-")[1] === deviceToRemove
        : node.label === deviceToRemove)
  );
  if (nodeToUncheck) {
    lockRuleDeviceTreeRef.value.setChecked(nodeToUncheck, false);
    lockRuleForm.value.deviceIds = lockRuleDeviceTreeRef.value.getCheckedKeys();
  }
}

// 添加响应式变量
const lockScreenPlanList = ref([]);
const lockScreenImageUrl = ref("");
const lockScreenImageDialogVisible = ref(false);
const currentLockScreenImage = ref("");

// 添加获取锁屏计划列表的方法
const getLockScreenPlanList = async () => {
  try {
    loadingUsb.value = true;
    const [ruleRes, imgRes] = await Promise.all([
      getLockScreenRule(),
      downScreenImg(),
    ]);

    if (ruleRes.code === 200 && ruleRes.data) {
      const data = ruleRes.data;
      const imageUrl =
        imgRes && imgRes.size > 76 ? URL.createObjectURL(imgRes) : "";
      console.log(ruleRes.data, "ruleRes.data");
      lockScreenPlanList.value = [
        {
          status: data.isAutoLock || data.isDisable ? "启用" : "禁用",
          controlType: "自动锁屏/解锁",
          effectiveTimeRange: data.timeList?.length
            ? data.timeList.join(" ~ ")
            : "-",
          effectiveTimeRange2:
            data.isDisable && data.timeRangeList?.length
              ? data.timeRangeList
                  .map((item) => {
                    return `${item.startTime} ~ ${item.endTime}`;
                  })
                  .join("、")
              : "-",
          lockScreenImage: imageUrl ? "自定义" : "默认",
          imageUrl: imageUrl,
          devices: data.deviceCodes?.join("，") || "-",
          // 保存原始数据用于编辑
          rawData: {
            ...data,
            lockImg: imageUrl ? 1 : 0,
            lockUrl: imageUrl,
          },
        },
      ];
    }
  } catch (error) {
    console.error("获取锁屏计划列表失败:", error);
    proxy.$modal.msgError("获取数据失败");
  } finally {
    loadingUsb.value = false;
  }
};

// 添加查看图片的方法
const handleViewLockScreenImage = (imageUrl) => {
  if (imageUrl) {
    currentLockScreenImage.value = imageUrl;
    lockScreenImageDialogVisible.value = true;
  }
};

// 添加编辑方法
const handleEditLockRule = async (row) => {
  console.log(row, "curRow");
  try {
    // 先打开对话框
    // ruleOpen.value = true;

    // await nextTick();

    // 合并数据前清空之前的设备选择
    // if (lockRuleDeviceTreeRef.value) {
    //   lockRuleDeviceTreeRef.value.setCheckedKeys([]);
    // }

    // 合并数据到 data.lockRuleForm，保留设备选择
    curRow.value = row;
    Object.assign(data.lockRuleForm, {
      ...row.rawData,
      selectedDevices: row.rawData.deviceCodes?.join(",") || "",
      deviceCodes: row.rawData.deviceCodes || [],
    });

    data.lockRuleForm.timeRangeList = !data.lockRuleForm.isDisable
      ? [{ time: ["08:00:00", "18:00:00"] }]
      : data.lockRuleForm.timeRangeList?.map((item) => {
          if (!!item) {
            return {
              ...item,
              time: [item.startTime, item.endTime],
            };
          }
        }) || [{ time: ["08:00:00", "18:00:00"] }];

    console.log("合并后的数据:", data.lockRuleForm);

    // 设置树形选择
    // if (lockRuleDeviceTreeRef.value && row.rawData.deviceCodes) {
    //   const nodeIds = row.rawData.deviceCodes
    //     .map((code) => findDeviceNodeId(code))
    //     .filter(Boolean);

    //   if (nodeIds.length > 0) {
    //     lockRuleDeviceTreeRef.value.setCheckedKeys(nodeIds);
    //   }
    // }

    // 设置图片相关状态
    // if (row.imageUrl) {
    //   data.lockRuleForm.lockImg = 1;
    //   data.lockRuleForm.lockUrl = row.imageUrl;
    //   hasScreenImg.value = true;
    // } else {
    //   data.lockRuleForm.lockImg = 0;
    //   data.lockRuleForm.lockUrl = "";
    //   hasScreenImg.value = false;
    // }

    // isImgChange.value = false;

    ruleOpen.value = true;
  } catch (error) {
    console.error("Error in handleEditLockRule:", error);
  }
};

function removeStartDevice(deviceToRemove) {
  const devices = deviceControlForm.value.selectedStartDevices
    .split(",")
    .filter((d) => d !== deviceToRemove);
  deviceControlForm.value.selectedStartDevices = devices.join(",");

  const treeNodes = startDeviceTreeRef.value.getCheckedNodes();
  const nodeToUncheck = treeNodes.find(
    (node) =>
      (node.children?.length < 1 || !node.children) &&
      (node.label.includes("-")
        ? node.label.split("-")[1] === deviceToRemove
        : node.label === deviceToRemove)
  );
  if (nodeToUncheck) {
    startDeviceTreeRef.value.setChecked(nodeToUncheck, false);
    deviceControlForm.value.startDeviceIds =
      startDeviceTreeRef.value.getCheckedKeys();
  }
}

function removeEndDevice(deviceToRemove) {
  const devices = deviceControlForm.value.selectedEndDevices
    .split(",")
    .filter((d) => d !== deviceToRemove);
  deviceControlForm.value.selectedEndDevices = devices.join(",");

  const treeNodes = endDeviceTreeRef.value.getCheckedNodes();
  const nodeToUncheck = treeNodes.find(
    (node) =>
      (node.children?.length < 1 || !node.children) &&
      (node.label.includes("-")
        ? node.label.split("-")[1] === deviceToRemove
        : node.label === deviceToRemove)
  );
  if (nodeToUncheck) {
    endDeviceTreeRef.value.setChecked(nodeToUncheck, false);
    deviceControlForm.value.endDeviceIds =
      endDeviceTreeRef.value.getCheckedKeys();
  }
}

// 添加一个计算属性来判断是否为编辑模式
const isEditMode = computed(() => {
  return !!deviceControlForm.value.id;
});

// 添加一个计算属性来确定按钮文本
const submitButtonText = computed(() => {
  return isEditMode.value ? "保存" : "生成";
});

function handleDelete(row) {
  proxy.$modal
    .confirm("是否确认删除该定时计划?", "警告", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    })
    .then(() => {
      proxy.$modal.loading("正在删除...");
      const params = {
        id: row.id,
      };
      deleteTimePlan(params)
        .then((res) => {
          if (res.code === 200) {
            proxy.$modal.msgSuccess("删除成功");
            getList(); // 重新获取列表数据
          } else {
            // proxy.$modal.msgError(res.msg || '删除失败')
          }
        })
        .catch(() => {
          // proxy.$modal.msgError('删除失败')
        })
        .finally(() => {
          proxy.$modal.closeLoading();
        });
    });
}
</script>

<style lang="scss" scoped>
.timeRangeRow {
  display: flex;
  gap: 0 10px;
  margin-bottom: 10px;
  .timeIndex {
    background-color: #4095e5;
    color: #fff;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
.timeAdd {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  width: 150px;
  margin: 10px auto 0;
}
.uploadImg {
  :deep(.el-upload__tip) {
    margin-top: 0;
  }
  :deep(.el-upload--picture-card) {
    width: 90px !important;
    height: 35px;
    border: none;
    background: none;
  }
  :deep(.el-upload-list--picture-card) {
    align-items: center;
  }
  :deep(.el-upload-list--picture-card .el-upload-list__item) {
    margin-bottom: 0;
    justify-content: center;
  }
}
.deviceUseManage-tit {
  font-size: 18px;
  margin: 20px 0 10px;

  &:first-child {
    margin-top: 10px;
  }
}

.view-switch {
  margin-bottom: 10px;
}

.recording-main {
  border: 1px solid #e4e7ed;
  border-radius: 4px;
  padding: 20px;
  background-color: #fff;
  .table-header {
    margin-bottom: 16px;
  }
  .table-footer {
    margin-top: 16px;
  }
  .add-row {
    width: 100%;
    height: 40px;
    border: 1px solid #dcdfe6;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: #7c8c9c;
    margin-top: -1px;

    .el-icon {
      margin-right: 4px;
      font-size: 12px;
    }
  }
}
:deep(.el-form-item__label) {
  color: #606266;
}

:deep(.el-table) {
  margin-top: 15px;
}

.rule-title {
  :deep(.el-form-item__label) {
    display: none;
  }
  :deep(.el-form-item__content) {
    display: block;
  }
}

.rule-header {
  display: flex;
  align-items: center;
  gap: 10px;
  font-weight: bold;
  color: #000;

  span {
    font-size: 14px;
  }
}

.el-form-item--default {
  margin-bottom: 12px;
}

.select-label {
  color: #606266;
  margin: 12px 0 8px;
}

.select-container {
  background-color: #fff;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 300px;
  overflow-y: auto;
}

.tree-select-container {
  margin-top: 8px;
}

.tree-select-label {
  margin-bottom: 8px;
  font-weight: 700;
  padding-left: 120px;
}

.tree-container {
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;
  max-height: 200px;
  overflow-y: auto;
  margin-bottom: 20px;
}

.mb-3 {
  margin-bottom: 12px;
  font-weight: 700;
}

.mt-3 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 25px;
}
.mt-4 {
  margin-top: -10px;
  margin-bottom: 20px;
  margin-left: 10px;
}

.select-device-container {
  min-height: 32px;
  width: 100%;
  padding: 4px 8px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  margin-bottom: 10px;
  max-height: 100px;
  overflow-y: auto;

  .device-tag {
    margin: 2px 4px;
  }

  .placeholder-text {
    color: #999;
    line-height: 24px;
  }
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

:deep(.custom-dialog) {
  .el-dialog {
    margin: 0 !important;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    max-height: 90vh;
    display: flex;
    flex-direction: column;

    .el-dialog__body {
      flex: 1;
      overflow-y: auto;
      padding: 20px;

      /* 设置滚动条样式 */
      &::-webkit-scrollbar {
        width: 6px;
      }

      &::-webkit-scrollbar-thumb {
        background-color: #dcdfe6;
        border-radius: 3px;
      }

      &::-webkit-scrollbar-track {
        background-color: #f5f7fa;
      }
    }

    .el-dialog__header {
      padding: 20px 20px 10px;
      margin-right: 0;
    }

    .el-dialog__footer {
      padding: 10px 20px 20px;
      border-top: 1px solid #dcdfe6;
    }
  }
}

/* 针对不同对话框的特定高度设置 */
:deep(.message-plan-dialog) {
  .el-dialog {
    height: 80vh;
  }
}

:deep(.device-control-dialog) {
  .el-dialog {
    height: 85vh;
  }
}

:deep(.lock-rule-dialog) {
  .el-dialog {
    height: 85vh;
  }
}

/* 调整树形控件容器的高度 */
.tree-container {
  max-height: 180px;
  overflow-y: auto;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  padding: 10px;

  /* 设置滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}

/* 调整设备标签容器的样式 */
.select-device-container {
  min-height: 32px;
  max-height: 100px;
  overflow-y: auto;

  /* 设置滚动条样式 */
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: #dcdfe6;
    border-radius: 3px;
  }

  &::-webkit-scrollbar-track {
    background-color: #f5f7fa;
  }
}
:deep(.el-form-item--default) {
  margin-bottom: 15px;
}
</style>
