import request from "@/utils/request";

// 查询当前的构信息 /system/schoolAppConfig/corpInfo
export function corpInfo() {
  return request({
    url: "/system/schoolAppConfig/corpInfo",
    method: "get",
  });
}

// 机构信息-刷新机构密钥 /system/schoolAppConfig/refreshSecret
export function refreshSecret() {
  return request({
    url: "/system/schoolAppConfig/refreshSecret",
    method: "get",
  });
}

// 绑定上级机构-校验 /system/schoolAppConfig/bind
export function bind(data) {
  return request({
    url: "/system/schoolAppConfig/bind",
    method: "post",
    data,
  });
}

// 保存上级机构信息 /system/schoolAppConfig/saveBind
export function saveBind(data) {
  return request({
    url: "/system/schoolAppConfig/saveBind",
    method: "post",
    data,
  });
}

// 管理端-查询绑定到他的下级机构列表 /system/schoolAppConfig/listBindCorp
export function listBindCorp(params) {
  return request({
    url: "/system/schoolAppConfig/listBindCorp",
    method: "get",
    params,
  });
}