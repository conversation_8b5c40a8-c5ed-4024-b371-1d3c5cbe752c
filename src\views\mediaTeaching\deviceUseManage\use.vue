<template>
  <div class="app-container deviceUseManage">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>

      <div class="deviceUseManage-tit">设备开关机记录</div>
      <el-form
        class="search-list"
        :model="queryOffParams"
        ref="queryOffRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="deviceCode">
          <el-input
            v-model="queryOffParams.deviceCode"
            placeholder="请输入设备编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQueryOff"
          />
        </el-form-item>
        <el-form-item label="" prop="addressStr">
          <el-tree-select
            v-model="queryOffParams.addressStr"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            style="width: 200px"
            clearable
            @change="handleQueryOff"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQueryOff"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQueryOff">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table v-loading="loadingOff" :data="offList" border>
        <el-table-column type="index" label="序号" width="90" align="center" />
        <el-table-column
          label="设备编号"
          align="center"
          minWidth="120px"
          prop="deviceCodeStr"
          show-overflow-tooltip
        />
        <el-table-column
          label="安装位置"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="addressStr"
        />
        <el-table-column
          label="设备此次开机时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="startTimeStr"
        />
        <el-table-column
          label="设备此次关机时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="endTimeStr"
        />
        <el-table-column
          label="设备此次使用时长（h）"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="runTimeStr"
        />
      </el-table>

      <pagination
        v-show="totalOff > 0"
        :autoScroll="false"
        :total="totalOff"
        :pageSizes="[5, 10, 20, 30, 50]"
        v-model:page="queryOffParams.current"
        v-model:limit="queryOffParams.size"
        @pagination="getOffList"
      />

      <div class="deviceUseManage-tit" style="margin-top: 40px">
        设备USB使用记录
      </div>
      <el-form
        class="search-list"
        :model="queryUsbParams"
        ref="queryUsbRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="deviceCode">
          <el-input
            v-model="queryUsbParams.deviceCode"
            placeholder="请输入设备编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQueryUsb"
          />
        </el-form-item>
        <el-form-item label="" prop="addressStr">
          <el-tree-select
            v-model="queryUsbParams.addressStr"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            style="width: 200px"
            clearable
            @change="handleQueryUsb"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQueryUsb"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQueryUsb">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table v-loading="loadingUsb" :data="usbList" border>
        <el-table-column type="index" label="序号" width="90" align="center" />
        <el-table-column
          label="设备编码"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceCodeStr"
        />
        <el-table-column
          label="安装位置"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="addressStr"
        />
        <el-table-column
          label="USB大小"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="addressStr"
        />
        <el-table-column
          label="USB型号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="addressStr"
        />
        <el-table-column
          label="USB序列号"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="addressStr"
        />
        <el-table-column
          label="USB接入时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="startTimeStr"
        />
        <el-table-column
          label="USB拔出时间"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="endTimeStr"
        />
      </el-table>

      <pagination
        v-show="totalUsb > 0"
        :autoScroll="false"
        :total="totalUsb"
        :pageSizes="[5, 10, 20, 30, 50]"
        v-model:page="queryUsbParams.current"
        v-model:limit="queryUsbParams.size"
        @pagination="getUsbList"
      />
    </el-card>
  </div>
</template>

<script setup name="deviceUseManage">
import { useRoute } from "vue-router";
import { queUsbPage, queOffPage } from "@/api/mediaTeach/use";

const route = useRoute();
const { proxy } = getCurrentInstance();

const offList = ref([]);
const usbList = ref([]);
const loadingOff = ref(false);
const loadingUsb = ref(false);
const showSearch = ref(true);
const totalOff = ref(0);
const totalUsb = ref(0);

const data = reactive({
  queryOffParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
  queryUsbParams: {
    current: 1,
    size: 5,
    deviceCode: "",
  },
});

const { queryOffParams, queryUsbParams } = toRefs(data);

/** 查询类型列表 */
function getOffList() {
  loadingOff.value = true;
  queOffPage(queryOffParams.value).then((response) => {
    console.log(queryOffParams.value, response.data);
    offList.value = response.data.records.reduce((res, cur) => {
      let { address, deviceCode, endTime, model, runTime, startTime } = cur;
      console.log("runtime", runTime);
      res.push({
        ...cur,
        addressStr: address || "-",
        deviceCodeStr: deviceCode || "-",
        endTimeStr: endTime || "-",
        runTimeStr: runTime.replace("-", "") || "-",
        startTimeStr: startTime || "-",
      });
      return res;
    }, []);
    totalOff.value = response.data.total;
    loadingOff.value = false;
  });
}

/** 查询类型列表 */
function getUsbList() {
  loadingUsb.value = true;
  queUsbPage(queryUsbParams.value).then((response) => {
    console.log(queryUsbParams.value, response.data);
    usbList.value = response.data.records.reduce((res, cur) => {
      let { address, deviceCode, endTime, model, startTime } = cur;
      res.push({
        ...cur,
        addressStr: address || "-",
        deviceCodeStr: deviceCode || "-",
        endTimeStr: endTime || "-",
        startTimeStr: startTime || "-",
      });
      return res;
    }, []);
    totalUsb.value = response.data.total;
    loadingUsb.value = false;
  });
}
/** 搜索按钮操作 */
function handleQueryOff() {
  queryOffParams.value.current = 1;
  getOffList();
}
function handleQueryUsb() {
  queryUsbParams.value.current = 1;
  getUsbList();
}
/** 重置按钮操作 */
function resetQueryOff() {
  proxy.resetForm("queryOffRef");
  handleQueryOff();
}
function resetQueryUsb() {
  proxy.resetForm("queryUsbRef");
  handleQueryUsb();
}

getOffList();
getUsbList();
</script>

<style scoped lang="scss">
.deviceUseManage-tit {
  margin: 10px 0;
}
</style>
