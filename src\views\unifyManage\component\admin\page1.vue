<template>
  <div class="unify-main_charts">
    <div class="col col1">
      <div class="col1-block1 flex" style="gap: 0 0.8vw; align-items: center">
        <div class="info-left">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryLeftList"
            :key="index"
          >
            <div class="info-left_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-left_cont">
              <div class="info-left_title">{{ item.name }}</div>
              <div class="info-left_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
        <div class="info-right">
          <div
            class="flex flex2"
            v-for="(item, index) in summaryRightList"
            :key="index"
          >
            <div class="info-right_icon" :class="[`icon${index + 1}`]"></div>
            <div class="info-right_cont">
              <div class="info-right_title">{{ item.name }}</div>
              <div class="info-right_count">
                <span>{{ item.num }}</span
                >{{ item.unit }}
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">
          <div class="block-title_text">
            <div class="titleText">辖区内资产覆盖率</div>
          </div>
        </div>
        <div class="battery">
          <span>{{
            (objData.assetCoverageStatistics &&
              objData.assetCoverageStatistics.coverage) ||
            0
          }}</span>
        </div>
      </div>
      <div class="col1-block4">
        <div class="block-title">
          <div class="block-title_text">
            <div class="titleText">辖区内资产总数</div>
          </div>
        </div>
        <div class="table block">
          <div class="table-row opt-title">
            <div class="table-row_name table-row_head">校区名称</div>
            <div class="table-row_count table-row_head">资产总数</div>
          </div>
          <div
            class="table-data"
            @mouseenter="setAnimate(false, 2)"
            @mouseleave="setAnimate(true, 2)"
          >
            <!-- <div class="table-data"> -->
            <div :class="{ 'table-data_inner': animate2 }">
              <div
                class="table-row data"
                v-for="(item, index) in objData.corpAssets"
                :key="index"
              >
                <div class="table-row_name" :title="item.corpName">
                  {{ item.corpName }}
                </div>
                <div class="table-row_count" :title="item.total">
                  {{ item.total > 99999 ? "99999+" : item.total }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <div class="col1-block3">
        <div class="block-title">
          <div class="titleText">网络信息</div>
        </div>
        <div class="echart-item">
          <Echarts
            id="lineData6"
            width="100%"
            height="9.5vw"
            :fullOptions="lineOption6"
          />
        </div>
      </div>
    </div>
    <div class="col col2">
      <div class="col2-block1">
        <div
          class="col2-block1_item"
          v-for="(item, index) in col2_block1_list"
          :key="index"
        >
          <div>
            <span>{{ item.num }}</span
            >个
          </div>
          <div>{{ item.name }}</div>
        </div>
      </div>
      <div class="col2-block2">
        <div>
          <Echarts
            id="mapData"
            width="100%"
            height="100%"
            :fullOptions="mapOption"
            :mapConfig="{ name: 'GZ', data: getGuangZhouData }"
          />
        </div>
      </div>
      <div class="col2-block3">
        <div class="flex" style="gap: 0 1.5vw; padding: 0vw">
          <div class="block echart-item" style="background: none">
            <div class="subtitle">备件使用排行榜</div>
            <div class="table2">
              <div class="table-row opt-title">
                <div class="table-row_number table-row_head">序号</div>
                <div class="table-row_type table-row_head">备件名称</div>
                <div class="table-row_count table-row_head">使用量</div>
              </div>
              <div
                class="table-data"
                @mouseenter="setAnimate(false, 1)"
                @mouseleave="setAnimate(true, 1)"
              >
                <div :class="{ 'table-data_inner': animate1 }">
                  <div
                    class="table-row data"
                    v-for="(item, index) in objData.sparepartsUseStatistics"
                    :key="index"
                    :class="[item.rank % 2 == 0 ? 'even' : 'odd']"
                  >
                    <div class="table-row_number">{{ item.rank }}</div>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.sparepartsName)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_type">
                        {{ item.sparepartsName }}
                      </div>
                    </el-tooltip>
                    <el-tooltip
                      class="box-item"
                      effect="dark"
                      :content="String(item.userNum)"
                      placement="bottom"
                      :enterable="false"
                      :hide-after="0"
                    >
                      <div class="table-row_count">
                        {{ item.userNum > 99999 ? "99999+" : item.userNum }}
                      </div>
                    </el-tooltip>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div class="block echart-item">
            <div class="subtitle">辖区内应用使用时长排行榜</div>
            <Echarts
              id="barData2"
              ref="barData2Ref"
              width="100%"
              height="9vw"
              :getYMax="true"
              :fullOptions="barOption2"
            />
          </div>
          <div class="block echart-item">
            <div class="subtitle">辖区内信息资产类别Top5</div>
            <Echarts
              id="barData"
              ref="barDataRef"
              width="100%"
              height="9vw"
              :getYMax="true"
              :fullOptions="barOption"
            />
          </div>
        </div>
      </div>
    </div>

    <div class="col col3">
      <div class="col3-block1" :class="curAssetsTab == 0 ? 'left' : 'right'">
        <div class="col3-block1_left" @click="curAssetsTab = 0"></div>
        <div class="col3-block1_right" @click="curAssetsTab = 1"></div>
      </div>
      <div class="col3-block2 bg" v-if="curAssetsTab == 0">
        <div class="flex flex-bg">
          <div class="block">
            <div class="block-left">
              <div class="subtitle">使用年限占比</div>
              <StripeCircularPie
                :option="pieOption6"
                id="pieData6"
                ref="pieRef6"
              />
            </div>
          </div>
          <div class="block">
            <div class="block-left">
              <div class="subtitle">硬盘大小占比</div>
              <RadiusCircularPie
                :option="pieOption10"
                id="pieData10"
                ref="pieRef10"
              />
            </div>
          </div>
        </div>
        <div class="flex flex-bg1">
          <div class="block">
            <div class="subtitle">设备类型占比</div>
            <CircularPie3D
              :option="pieOption8"
              :data="data3D8"
              id="pieData8"
              ref="pieRef8"
              style="min-height: 13vw"
            />
          </div>
          <div class="block">
            <div class="subtitle">端口类型占比</div>
            <CircularPie3D
              :option="pieOption9"
              :data="data3D9"
              id="pieData9"
              ref="pieRef9"
            />
          </div>
        </div>
        <div class="flex flex-bg">
          <div class="block">
            <div class="subtitle">故障时长占比</div>
            <CircularPie :option="pieOption7" id="pieData7" ref="pieRef7" />
          </div>
          <div class="block">
            <div class="subtitle">内存占比</div>
            <RadiusCircularPie
              :option="pieOption11"
              id="pieData11"
              ref="pieRef11"
            />
          </div>
        </div>
        <div class="flex" style="padding-bottom: 0.8vw">
          <div class="block">
            <div class="subtitle">品牌占比</div>
            <CircularPie3D
              :option="pieOption12"
              :data="data3D12"
              id="pieData12"
              ref="pieRef12"
              style="min-height: 13vw"
            />
          </div>
          <div class="block">
            <div class="subtitle">系统版本占比</div>
            <CircularPie3D
              :option="pieOption15"
              :data="data3D15"
              id="pieData15"
              ref="pieRef15"
            />
          </div>
        </div>
      </div>
      <div class="col3-block2" v-if="curAssetsTab == 1">
        <div class="flex flex-bg" style="margin-bottom: 1vw">
          <div class="block">
            <div class="block-left">
              <div class="subtitle">网络资产状况</div>
              <StripeCircularPie
                :option="pieOption5"
                id="pieData5"
                ref="pieRef5"
                :showNum="true"
                :num="col2_block1_list[3].num"
              />
            </div>
          </div>
          <div class="block">
            <div class="block-left">
              <div class="subtitle">软件使用状况</div>
              <RadiusCircularPie
                :option="pieOption4"
                id="pieData4"
                ref="pieRef4"
              />
            </div>
          </div>
        </div>
        <div class="flex" style="margin-bottom: 1.4vw">
          <div class="block">
            <div class="subtitle">软件库分类占比</div>
            <CircularPie3D
              :option="pieOption2"
              :data="data3D2"
              id="pieData2"
              ref="pieRef2"
            />
          </div>
          <div class="block">
            <div class="subtitle">软件许可证到期时间占比</div>
            <CircularPie3D
              :option="pieOption3"
              :data="data3D3"
              id="pieData3"
              ref="pieRef3"
            />
          </div>
        </div>

        <div class="flex" style="margin-bottom: 0">
          <div style="flex: 1">
            <div class="block-title">
              <div class="titleText">数据信息资产状况</div>
              <div class="titleTab">
                <div
                  class="titleTab_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="{ active: curRange1 == item.value }"
                  @click="handleRange(0, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="flex" style="gap: 0; margin-bottom: 0">
              <Echarts
                style="flex: 1"
                id="lineData"
                width="100%"
                height="10.1vw"
                :fullOptions="lineOption"
              />
              <div class="block block-right" style="flex: 0.8">
                <div
                  class="item-opt"
                  v-for="(item, index) in optList1_1"
                  :key="index"
                  style="font-size: 0.8vw"
                >
                  <div style="display: flex; justify-content: space-between">
                    {{ item.title }}
                    <div>
                      <span
                        style="color: #0bf9fe !important; padding-right: 0.2vw"
                        >{{ item.total || 26 }}</span
                      >条数据
                    </div>
                  </div>
                  <div class="item-opt_row">
                    <span style="font-size: 0.65vw !important"
                      >对比上个月同比{{
                        item.num > 0 ? "增长" : item.num < 0 ? "下降" : "相同"
                      }}</span
                    >
                    <div
                      style="font-size: 0.7vw !important"
                      :class="
                        item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      "
                    >
                      {{ item.ratio.replace(/-/g, "") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div class="flex">
          <div style="flex: 1">
            <div class="block-title">
              <div class="titleText">知识产权资产状况</div>
              <div class="titleTab">
                <div
                  class="titleTab_item"
                  v-for="item in rangeList"
                  :key="item.value"
                  :class="{ active: curRange2 == item.value }"
                  @click="handleRange(1, item.value)"
                >
                  {{ item.label }}
                </div>
              </div>
            </div>
            <div class="flex" style="gap: 0">
              <Echarts
                style="flex: 1"
                id="lineData2"
                width="100%"
                height="10.1vw"
                :fullOptions="lineOption2"
              />
              <div class="block block-right" style="flex: 0.8">
                <div
                  class="item-opt"
                  v-for="(item, index) in optList2"
                  :key="index"
                  style="font-size: 0.8vw"
                >
                  <div style="width: 100%; text-align: center">
                    {{ item.title }}
                  </div>
                  <div class="item-opt_row">
                    <span style="font-size: 0.65vw !important"
                      >{{ item.tip
                      }}{{
                        item.num > 0 ? "增长" : item.num < 0 ? "下降" : "相同"
                      }}</span
                    >
                    <div
                      style="font-size: 0.7vw !important"
                      :class="
                        item.num > 0 ? 'up' : item.num < 0 ? 'low' : 'none'
                      "
                    >
                      {{ item.ratio.replace(/-/g, "") }}
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
  
  <script setup name="unifyIndex">
import CircularPie3D from "@/views/unifyManage/component/echarts/circularPie3D.vue";
import StripeCircularPie from "@/views/unifyManage/component/echarts/stripeCircularPie.vue";
import CircularPie from "@/views/unifyManage/component/echarts/circularPie.vue";
import RadiusCircularPie from "@/views/unifyManage/component/echarts/radiusCircularPie.vue";
import Echarts from "@/components/Echarts/index.vue";
import { useRouter } from "vue-router";
import {
  reactive,
  toRefs,
  getCurrentInstance,
  onMounted,
  onBeforeUnmount,
  ref,
} from "vue";
import {
  getAdminSchoolStatistics,
  getAdminWorkOrderStatistics,
  getAdminAssetsStatistics,
  getAdminMaintainCountStatistics,
  getAdminWorkOrderTimeStatistics,
  getAdminKnowledgeStatistics,
  getAdminCorpGps,
} from "@/api/unify";
import { fitChartSize, downloadBlob, timeFormat } from "@/utils";
import getGuangZhouData from "@/api/gz.json";

const { proxy } = getCurrentInstance();
const router = useRouter();

const state = reactive({
  loading: false,
  pieRef2: null,
  pieRef3: null,
  pieRef4: null,
  pieRef5: null,
  pieRef6: null,
  pieRef7: null,
  pieRef8: null,
  pieRef9: null,
  pieRef10: null,
  pieRef11: null,
  pieRef12: null,
  pieRef15: null,
  barDataRef: null,
  barData2Ref: null,
  scrolltimer1: null,
  scrolltimer2: null,
  animate2: false,
  animate1: false,
  curRange1: 2,
  curRange2: 2,
  networkList: {
    avgUp: 0,
    avgDown: 0,
    peakUp: 0,
    peakDown: 0,
  },
  curAssetsTab: 0,
  dialogVisible: false,
  chartsDOM: null,
  timer: null,
  deptInfo: {
    deptName: "",
    peopleNum: 0,
    assetsNum: 0,
    leaderName: "",
  },
  rangeList: [
    { label: "按年", value: 3 },
    { label: "按月", value: 2 },
  ],
});

const {
  loading,
  pieRef2,
  pieRef3,
  pieRef4,
  pieRef5,
  pieRef6,
  pieRef7,
  pieRef8,
  pieRef9,
  pieRef10,
  pieRef11,
  pieRef12,
  pieRef15,
  rangeList,
  barDataRef,
  barData2Ref,
  animate1,
  scrolltimer1,
  scrolltimer2,
  animate2,
  curRange1,
  curRange2,
  networkList,
  curAssetsTab,
  dialogVisible,
  deptInfo,
  chartsDOM,
  timer,
  timer2,
} = toRefs(state);

const col2_block1_list = ref([
  { name: "硬件资产", num: 2157 },
  { name: "软件资产", num: 203 },
  { name: "数据信息资产", num: 407 },
  { name: "网络资产", num: 271 },
  { name: "知识产权", num: 36 },
]);

const summaryLeftList = ref([
  { name: "资产总数", num: "3074", unit: "个" },
  { name: "辖区开机率", num: "35.71", unit: "%" },
  { name: "总报废数", num: "14", unit: "个" },
]);
const summaryRightList = ref([
  { name: "辖区运维人员总人数", num: "165", unit: "人" },
  { name: "今日总执勤", num: "128", unit: "人" },
  { name: "今日总轮休", num: "37", unit: "人" },
]);

const maintainList = ref([
  {
    title: "当前工单量",
    total: 0,
    yesTotal: 0,
    ratio: "0%",
    unit: "个",
    num: 0,
  },
  { title: "报障次数", total: 0, yesTotal: 0, ratio: "0%", unit: "次", num: 0 },
  { title: "报障率", total: 0, yesTotal: 0, ratio: "0%", unit: "%", num: 0 },
]);

const optList1 = ref([
  { title: "知识库", tip: "", total: 0, ratio: "0%", num: 0 },
  { title: "对比上个月", tip: "个数同比", total: 0, ratio: "0", num: 0 },
]);

const optList1_1 = ref([
  {
    title: "知识库",
    tip: "同比",
    total: 0,
    ratio: "-100%",
    num: -100,
  },
]);

const optList2 = ref([
  {
    title: "本月对比上个月",
    tip: "知识产权个数同比",
    total: 0,
    ratio: "-50%",
    num: -50,
  },
  {
    title: "本年度对比上年度",
    tip: "知识产权个数同比",
    total: 5,
    ratio: "300%",
    num: 300,
  },
]);

const optList3 = ref([
  { title: "本月度处理单数", total: 0, ratio: "0%", num: 0 },
  { title: "本年度处理单数", total: 0, ratio: "0%", num: 0 },
]);

const optList4 = ref([
  { title: "今日工单平均处理时间", total: 0, ratio: "0%", num: 0 },
  { title: "合计工单平均处理时间", total: 0, fq: "0天/次" },
]);

const objData = ref({
  icCover: true,
  sparepartsUseStatistics: [
    { rank: 1, sparepartsName: "50W白炽灯管", userNum: 192345 },
    { rank: 2, sparepartsName: "3mm螺丝", userNum: 156232 },
    { rank: 3, sparepartsName: "200W白炽灯管", userNum: 119089 },
    { rank: 4, sparepartsName: "英菲克鼠标无线版", userNum: 235 },
    { rank: 5, sparepartsName: "华硕主板BM460", userNum: 199 },
  ],
  assetCoverageStatistics: {
    coverage: "55.56%",
  },
  assetsTypeStatistics: [],
}); // 存放数据
const orderParams1 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams2 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年
const orderParams3 = ref({
  range: 1,
}); // 工单统计年月日筛选 range: 1周 2月 3年

//广州市地图配置
const mapFontSize = Math.max(10, (window.innerWidth / 1920) * 12);
const mapFontSizeEmphasis = Math.max(10, (window.innerWidth / 1920) * 14);
const mapBgWidth = Math.max(10, (window.innerWidth / 1920) * 90);
const mapBgHeight = Math.max(10, (window.innerWidth / 1920) * 55);
const mapBgPadding = [
  -Math.max(10, (window.innerWidth / 1920) * 10),
  Math.max(10, (window.innerWidth / 1920) * 20),
  0,
  -Math.max(10, (window.innerWidth / 1920) * 65),
];
const mapBgSymbolSize = [
  Math.max(10, (window.innerWidth / 1920) * 90),
  Math.max(10, (window.innerWidth / 1920) * 50),
];
const mapBgOffset = [
  Math.min(10, (window.innerWidth / 1920) * 2),
  -Math.min(10, (window.innerWidth / 1920) * 4),
];

// 获取当前日期
var currentDate = new Date();
// 获取一周前的日期
var oneWeekAgo = new Date(currentDate.getTime() - 5 * 24 * 60 * 60 * 1000);
// 保存日期的数组
var dates = [];
// 循环获取一周内的日期
for (var i = 0; i < 6; i++) {
  var dateTime = oneWeekAgo.getTime() + i * 24 * 60 * 60 * 1000;
  dates.push(timeFormat(dateTime, "mm-dd"));
}
//网络信息折线图
const lineOption6 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal1 = params[0];
        let relVal2 = params[1];
        return `${relVal1.name}<br/>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;margin: 5px 0;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal1?.color?.legendColor
          }"></i>${
          relVal1?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal1 == undefined ? "" : relVal1.value + "MB/s"
        }</b>
        </div>
        <div style="width: 100%;display: flex;align-items: center;justify-content: space-between;">
          <div style="display: flex;align-items: center;gap: 0 5px;"><i style="width: 10px;height:10px;border-radius: 50%;background-color: ${
            relVal2?.color?.legendColor
          }"></i>${
          relVal2?.seriesName || ""
        }</div><b style="margin-left: 20px">${
          relVal2 == undefined ? "" : relVal2.value + "MB/s"
        }</b>
          </div>`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    legend: {
      itemGap: fitChartSize(20),
      itemWidth: fitChartSize(20),
      itemHeight: fitChartSize(2),
      icon: "rect",
      top: "4%",
      left: "8%",
      data: [
        { name: "上行网速", itemStyle: { color: "#0783FA" } },
        { name: "下行网速", itemStyle: { color: "#07D1FA" } },
      ],
      textStyle: {
        color: "#fff",
        fontSize: fitChartSize(9),
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: dates,
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(12),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(30),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "8%",
      height: "65%",
      right: "4%",
    },
    series: [
      {
        name: "上行网速",
        data: [20, 80, 60, 12, 40, 100],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: {
            legendColor: "#0783FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#0783FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        name: "下行网速",
        data: [60, 50, 30, 70, 11, 90],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
        tooltip: {
          show: false,
        },
      },
    ],
  },
});

const mapOption = ref({
  options: {
    tooltip: {
      show: true,
    },
    geo: {
      show: true,
      map: "GZ",
      zoom: 1.2,
      aspectScale: 1,
      tooltip: {
        show: false,
        trigger: "item",
      },
      label: {
        show: true,
        color: "#fff",
        fontSize: "70%",
        formatter: (params) => {
          return `{bg|}{name|${params.name}}`;
        },
        // 定义富文本样式
        rich: {
          bg: {
            // 关键：通过 backgroundColor 的 image 属性设置背景图
            backgroundColor: {
              image:
                "https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/city-bg.png", // 背景图 URL
            },
            width: mapBgWidth, // 背景图宽度
            height: mapBgHeight, // 背景图高度
          },
          name: {
            fontSize: mapFontSize,
            padding: mapBgPadding, // 调整文字位置
          },
        },
      },
      itemStyle: {
        opacity: 0, //图形透明度 0 - 1
        borderColor: "yellow", //图形的描边颜色
        // borderColor: "#00c6e8", //图形的描边颜色
        borderWidth: 1, //描边线宽。为 0 时无描边。
        borderType: "solid", //柱条的描边类型，默认为实线，支持 'solid', 'dashed', 'dotted'。
        areaColor: "transparent",
        // areaColor: "rgba(4, 46, 101, 0)", //图形的颜色 #eee
        // normal: {},
      },
      emphasis: {
        label: {
          color: "#fff",
        },
        itemStyle: {
          opacity: 0,
        },
      },
    },
    series: [
      {
        tooltip: {
          show: true,
          trigger: "item",
          backgroundColor: "transparent",
          borderColor: "transparent",
          formatter: function (params) {
            // console.log(params);
            let { name, detail } = params.data;
            return `
              <div class="tooltip-bg">
                <div class="tooltip-title">${
                  name.length > 11
                    ? name.substring(0, 5) +
                      "..." +
                      name.substring(name.length - 5, name.length)
                    : name
                }</div>
                <div class="tooltip-row row1">
                  <div class="tooltip-label">资产总数：</div>
                  <div class="tooltip-value">${detail.zczs}</div>
                </div>
                <div class="tooltip-row row2">
                  <div class="tooltip-label">在线率：</div>
                  <div class="tooltip-value">${detail.zxl}</div>
                </div>
              </div>
            `;
          }, // 可选：自定义提示内容
        },
        name: "校区标记",
        map: "GZ",
        type: "scatter",
        coordinateSystem: "geo",
        symbolSize: mapBgSymbolSize,
        symbol: `image://https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/school-bg.png`,
        label: {
          show: true,
          color: "#fff",
          fontSize: mapFontSize,
          offset: mapBgOffset,
          formatter: function (params) {
            let { name } = params.data;
            return `${
              name.length > 4
                ? name.substring(0, 2) +
                  "..." +
                  name.substring(name.length - 2, name.length)
                : name
            }`;
          },
        },
        emphasis: {
          label: {
            fontSize: mapFontSizeEmphasis,
          },
        },
        data: [
          {
            name: "云天数据应用端",
            detail: {
              zczs: 1,
              zxl: "0.00%",
            },
            value: [113.31954, 23.18037],
          },
        ],
      },
    ],
  },
});

//信息资产类型柱状图相关配置
// const barData = ref([154, 132, 132, 99, 44]);
const barData = ref([]);
const borderHeight = 3;
const topData = barData.value.map(() => borderHeight);
const barColors = [
  "rgba(73, 195, 132, 1)",
  "rgba(156, 190, 207, 1)",
  "rgba(43, 142, 243, 1)",
  "rgba(221, 210, 70, 1)",
  "rgba(53, 224, 255, 1)",
];
const yMax = Math.max(...barData.value) * 1.2;
// 预处理背景系列数据：每个柱子单独配置 borderColor
const backgroundData = barData.value.map((_, index) => ({
  value: yMax,
  itemStyle: {
    borderColor: barColors[index % barColors.length], // 通过取余确保颜色循环
  },
}));
const barOption = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value + borderHeight}个`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "10%",
      left: "13%",
      right: "5%",
      top: "5%",
    },
    xAxis: {
      type: "category",
      data: ["网络通信设备", "磁盘", "实物资产", "装置", "投影仪"],
      axisLabel: {
        color: "#fff",
        margin: fitChartSize(8),
        fontSize: fitChartSize(9),
        // rotate: 15,
        formatter: function (value, index) {
          return value.length > 4 ? `${value.substring(0, 3)}...` : `${value}`;
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      // name: "h",
      minInterval: 1,
      splitNumber: 4,
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(11),
        // formatter: "{value} h",
        align: "left",
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      // 背景系列：高度固定为 yMax
      {
        type: "bar",
        barGap: "-100%",
        barWidth: "49%",
        data: backgroundData, // 所有背景条数据 = yMax
        itemStyle: {
          color: "transparent",
          borderWidth: 0.1,
        },
        tooltip: { show: false }, // 隐藏提示框
      },
      {
        data: [
          {
            name: "网络通信设备",
            value: barData.value[0] - borderHeight,
            itemStyle: {
              color: "rgba(73, 195, 132, 0.5)",
            },
          },
          {
            name: "磁盘",
            value: barData.value[1] - borderHeight,
            itemStyle: { color: "rgba(156, 190, 207, 0.5)" },
          },
          {
            name: "实物资产",
            value: barData.value[2] - borderHeight,
            itemStyle: { color: "rgba(43, 142, 243, 0.5)" },
          },
          {
            name: "装置",
            value: barData.value[3] - borderHeight,
            itemStyle: { color: "rgba(221, 210, 70, 0.5)" },
          },
          {
            name: "投影仪",
            value: barData.value[4] - borderHeight,
            itemStyle: { color: "rgba(53, 224, 255, 0.5)" },
          },
        ],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        stack: "total", // 与主系列堆叠
        label: {
          show: true,
          position: "top",
          formatter: (params) => {
            return `${params.value + borderHeight}`;
          },
          color: "#fff",
          fontSize: fitChartSize(10),
        },
      },
      // 顶部系列：显示固定高度的边框
      {
        type: "bar",
        data: topData.map((value, index) => ({
          value,
          itemStyle: {
            color: barColors[index], // 自定义颜色逻辑
          },
        })),
        barWidth: "50%",
        stack: "total", // 与主系列堆叠
        tooltip: { show: false }, // 隐藏提示框
      },
    ],
  },
});

//应用使用时长TOP5柱状图相关配置
// const barData2 = ref([13397, 10045, 7454, 2780, 1461]);
const barData2 = ref([]);
const borderHeight2 = 3;
const topData2 = barData2.value.map(() => borderHeight2);
const barColors2 = [
  "rgba(73, 195, 132, 1)",
  "rgba(156, 190, 207, 1)",
  "rgba(43, 142, 243, 1)",
  "rgba(221, 210, 70, 1)",
  "rgba(53, 224, 255, 1)",
];
const yMax2 = ref(Math.max(...barData2.value) * 1.2);
// 预处理背景系列数据：每个柱子单独配置 borderColor
const backgroundData2 = barData2.value.map((_, index) => ({
  value: yMax2.value,
  itemStyle: {
    borderColor: barColors2[index % barColors2.length], // 通过取余确保颜色循环
  },
}));
const barOption2 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value + borderHeight2}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "10%",
      left: "17%",
      right: "5%",
      top: "5%",
    },
    xAxis: {
      type: "category",
      data: ["办公", "浏览器", "社交", "其他", "娱乐"],
      axisLabel: {
        color: "#fff",
        margin: fitChartSize(8),
        fontSize: fitChartSize(10),
        formatter: function (value, index) {
          return value.length > 4 ? `${value.substring(0, 3)}...` : `${value}`;
        },
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      name: "h",
      minInterval: 1,
      splitNumber: 4,
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#fff",
        fontSize: fitChartSize(11),
        formatter: "{value} h",
        align: "left",
        margin: fitChartSize(45),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      // 背景系列：高度固定为 yMax
      {
        type: "bar",
        barGap: "-100%",
        barWidth: "49%",
        data: backgroundData2, // 所有背景条数据 = yMax
        itemStyle: {
          color: "transparent",
          borderWidth: 0.1,
        },
        tooltip: { show: false }, // 隐藏提示框
      },
      {
        data: [
          {
            name: "其它",
            value: barData2.value[0] - borderHeight2,
            itemStyle: {
              color: "rgba(73, 195, 132, 0.5)",
            },
          },
          {
            name: "娱乐类",
            value: barData2.value[1] - borderHeight2,
            itemStyle: { color: "rgba(156, 190, 207, 0.5)" },
          },
          {
            name: "办公类",
            value: barData2.value[2] - borderHeight2,
            itemStyle: { color: "rgba(43, 142, 243, 0.5)" },
          },
          {
            name: "教学类",
            value: barData2.value[3] - borderHeight2,
            itemStyle: { color: "rgba(221, 210, 70, 0.5)" },
          },
          {
            name: "应用类",
            value: barData2.value[4] - borderHeight2,
            itemStyle: { color: "rgba(53, 224, 255, 0.5)" },
          },
        ],
        type: "bar",
        barWidth: "50%",
        showBackground: false,
        stack: "total", // 与主系列堆叠
        label: {
          show: true,
          position: "top",
          formatter: (params) => {
            return `${params.value + borderHeight2}`;
          },
          color: "#fff",
          fontSize: fitChartSize(10),
        },
      },
      // 顶部系列：显示固定高度的边框
      {
        type: "bar",
        data: topData2.map((value, index) => ({
          value,
          itemStyle: {
            color: barColors2[index], // 自定义颜色逻辑
          },
        })),
        barWidth: "50%",
        stack: "total", // 与主系列堆叠
        tooltip: { show: false }, // 隐藏提示框
      },
    ],
  },
});

//标签使用频率饼图
const barOption3 = ref({
  options: {
    tooltip: {
      trigger: "axis",
      formatter: (params) => {
        let relVal = params[0];
        return `${relVal.name}<br/>${relVal.value}小时`;
      },
      axisPointer: {
        type: "shadow",
      },
    },
    grid: {
      bottom: "20%",
      left: "12%",
      right: "5%",
      top: "10%",
    },
    xAxis: {
      type: "category",
      data: [],
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
        margin: 5,
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: false,
      },
    },
    yAxis: {
      type: "value",
      nameTextStyle: {
        color: "#fff",
        nameLocation: "start",
      },
      axisLabel: {
        color: "#8495b6",
        fontSize: "40%",
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          color: "#8495b6",
          opacity: 0.2,
        },
      },
      axisLine: {
        show: false,
      },
    },
    series: [
      {
        data: [],
        type: "bar",
        barWidth: "30%",
        showBackground: false,
        backgroundStyle: {
          color: "rgb(4, 22, 50, 1)",
          borderColor: "#c8d7fa",
        },
        itemStyle: {
          color: "rgba(81, 131, 255, .6)",
        },
      },
    ],
  },
});

// 软件资产状况饼图（假数据）
const data3D2 = ref([
  {
    name: "操作系统",
    value: 160,
    proportion: "35.70%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#0783FAFF",
    },
  },
  {
    name: "教学系统",
    value: 144,
    proportion: "32.15%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#FFD15CFF",
    },
  },
  {
    name: "其他软件",
    value: 144,
    proportion: "32.15%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#20E6A4FF",
    },
  },
]);
const pieOption2 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 软件到期时间饼图（假数据）
const data3D3 = ref([
  {
    name: "1年以内",
    value: 160,
    proportion: "27.00%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#0783FAFF",
    },
  },
  {
    name: "1-3年",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#FFD15CFF",
    },
  },
  {
    name: "3-5年",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#20E6A4FF",
    },
  },
  {
    name: "5年以上",
    value: 144,
    proportion: "24.33%",
    height: 20,
    itemStyle: {
      opacity: 0.6,
      color: "#07D1FAFF",
    },
  },
]);
const pieOption3 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 软件使用状况饼图
const pieOption4 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "办公",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "浏览器",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "社交",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "娱乐",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
          {
            name: "其他",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 网络资产状况饼图（假数据）
const pieOption5 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "办公平台",
            value: 100,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "教育平台",
            value: 200,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "门户网站",
            value: 300,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
        ],
      },
    ],
  },
});

// 硬件使用年限占比饼图
const pieOption6 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["50%", "65%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 3,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "一年以内",
            value: 0,
            itemStyle: { color: "rgba(0,164,255,0.3)", borderColor: "#00a4ff" },
          },
          {
            name: "1-3年",
            value: 0,
            itemStyle: { color: "rgba(0,204,3,0.3)", borderColor: "#00cc03" },
          },
          {
            name: "3-5年",
            value: 0,
            itemStyle: { color: "rgba(255,85,1,0.3)", borderColor: "#ff5501" },
          },
          {
            name: "5年以上",
            value: 0,
            itemStyle: { color: "rgba(255,255,0,0.3)", borderColor: "#ffff00" },
          },
        ],
      },
    ],
  },
});

// 硬件故障时长占比饼图
const pieOption7 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 0,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
            fontSize: 40,
            fontWeight: "bold",
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          {
            name: "1天以内",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "1-5天",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "5-10天",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "10天以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fda331",
                  },
                  {
                    offset: 1,
                    color: "#ff4e02",
                  },
                ],
              },
              bgColor: "#FFBF60",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产设备类型占比饼图
const pieColor8 = ref([
  { color1: "rgba(118,197,236,0.6)", color2: "rgba(118,197,236,1)" },
  { color1: "rgba(71,109,238,0.6)", color2: "rgba(71,109,238,1)" },
  { color1: "rgba(235,194,61,0.6)", color2: "rgba(235,194,61,1)" },
  { color1: "rgba(71,192,127,0.6)", color2: "rgba(71,192,127,1)" },
  { color1: "rgba(225,98,98,0.6)", color2: "rgba(225,98,98,1)" },
  { color1: "rgba(242,147,57,0.6)", color2: "rgba(242,147,57,1)" },
]);
const data3D8 = ref([]);
const pieOption8 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      // light: {
      //   main: {
      //     // 光源类型，可以是 'ambient'（环境光）、'lambert'（兰伯特光照）等
      //     type: "lambert",
      //     intensity: 1, // 光照强度
      //     alpha: 45, // 光源方位角（度）
      //     beta: -45, // 光源俯仰角（度）
      //   },
      // },
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产端口类型占比饼图
const pieColor9 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D9 = ref([]);
const pieOption9 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产硬盘大小占比饼图
const pieOption10 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "128GB以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0170ff",
                  },
                ],
              },
              bgColor: "#0091ff",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
          {
            name: "128GB-256GB",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#0bc699",
                  },
                  {
                    offset: 1,
                    color: "#0ed877",
                  },
                ],
              },
              bgColor: "#0ED877",
              bgColor2: "rgba(14, 216, 119, 0.3)",
            },
          },
          {
            name: "257GB-512GB",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00eaff",
                  },
                  {
                    offset: 1,
                    color: "#00abff",
                  },
                ],
              },
              bgColor: "#67DBFF",
              bgColor2: "rgba(103, 219, 255, 0.3)",
            },
          },
          {
            name: "513GB-1TB",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
              bgColor2: "rgba(255, 191, 96, 0.3)",
            },
          },
          {
            name: "1TB以上",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
              bgColor2: "rgba(0, 145, 255, 0.3)",
            },
          },
        ],
      },
    ],
  },
});

// 资产内存占比饼图
const pieOption11 = ref({
  options: {
    series: [
      {
        type: "pie",
        radius: ["58%", "64%"],
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        padAngle: 5,
        emphasis: {
          scaleSize: 2.5,
          label: {
            show: true,
          },
        },
        label: {
          show: false,
          position: "center",
          formatter: "{d|{d}%}\n{b|{b}}",
          rich: {
            d: {
              color: "#fff",
              fontSize: fitChartSize(14),
              lineHeight: fitChartSize(18),
              fontWeight: "bold",
            },
            b: {
              color: "#D4EDFF",
              fontSize: fitChartSize(12),
            },
          },
        },
        labelLine: {
          show: false,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        itemStyle: {
          borderRadius: 30, // 内外不同圆角
        },
        data: [
          {
            name: "8G以下",
            value: 10,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#00aaff",
                  },
                  {
                    offset: 1,
                    color: "#0252ef",
                  },
                ],
              },
              bgColor: "#00AAFF",
            },
          },
          {
            name: "8G-16G",
            value: 30,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#01e9ff",
                  },
                  {
                    offset: 1,
                    color: "#00aafe",
                  },
                ],
              },
              bgColor: "#00EAFF",
            },
          },
          {
            name: "16G-32G",
            value: 20,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#55cb69",
                  },
                  {
                    offset: 1,
                    color: "#79ed8d",
                  },
                ],
              },
              bgColor: "#00CC03",
            },
          },
          {
            name: "32G-64G",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#fe4c01",
                  },
                  {
                    offset: 1,
                    color: "#ff5b00",
                  },
                ],
              },
              bgColor: "#FF5501",
            },
          },
          {
            name: "64G及以上",
            value: 40,
            itemStyle: {
              color: {
                type: "linear",
                x: 0, // 渐变起始点 x 坐标（0-1）
                y: 0, // 渐变起始点 y 坐标
                x2: 1, // 渐变结束点 x 坐标
                y2: 1, // 渐变结束点 y 坐标
                colorStops: [
                  {
                    offset: 0,
                    color: "#ff9e1e",
                  },
                  {
                    offset: 1,
                    color: "#fef702",
                  },
                ],
              },
              bgColor: "#FFFF00",
            },
          },
        ],
      },
    ],
  },
});

// 故障资产品牌占比饼图
const pieColor12 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D12 = ref([]);
const pieOption12 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

// 资产内故障资产使用年限占比饼图
const pieOption13 = ref({
  options: {
    color: ["#5470c6", "#a6340e", "#05596f", "#185afc"],
    tooltip: {
      trigger: "item",
    },
    series: [
      {
        type: "pie",
        radius: "55%",
        center: ["50%", "50%"],
        avoidLabelOverlap: false,
        label: {
          formatter: "{b|{b}}\n{d|{d}%}",
          rich: {
            d: {
              color: "#c8d7fa",
              fontSize: "40%",
              lineHeight: "10",
            },
            b: {
              color: "#6889de",
              fontSize: "40%",
            },
          },
        },
        labelLine: {
          show: true,
          length: 2,
          length2: 5,
          lineStyle: {
            color: "rgba(81, 131, 255, .2)",
          },
        },
        data: [
          { name: "门户网站", value: 362 },
          { name: "教育平台", value: 768 },
          { name: "门户网站", value: 730 },
        ],
      },
    ],
  },
});

//系统版本占比饼图
const pieColor15 = ref([
  "#76c5ec",
  "#476dee",
  "#ebc23d",
  "#47c07f",
  "#E16262",
  "#F29339",
]);
const data3D15 = ref([]);
const pieOption15 = ref({
  options: {
    legend: {
      type: "scroll",
      show: false,
    },
    animation: true,
    xAxis3D: {
      min: -1,
      max: 1,
    },
    yAxis3D: {
      min: -1,
      max: 1,
    },
    zAxis3D: {
      min: -1,
      max: 1,
    },
    grid3D: {
      show: false,
      boxHeight: 1,
      top: "-5%",
      viewControl: {
        roam: false,
        zoomSpeed: 0,
        distance: 130,
        alpha: 30,
        beta: 0,
        mouseRotate: false,
        mouseZoom: false,
        rotateSensitivity: 0,
        zoomSensitivity: 0,
        autoRotate: false, // 自动旋转
      },
    },
    series: [],
  },
});

//数据信息资产总数折线图
const lineOption = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val = value;
          if (curRange1.value == 2 && value) {
            val = value.split("-")[1] + "月";
          }
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "15%",
      height: "65%",
      right: "4.5%",
    },
    series: [
      {
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: {
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "rgba(19, 141, 255, .5)", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "rgba(19, 141, 255, 0)", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        itemStyle: {
          color: {
            legendColor: "#0783FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#0783FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

//知识产权资产状况
const lineOption2 = ref({
  options: {
    color: ["#092258"],
    tooltip: {
      trigger: "axis",
      axisPointer: {
        type: "shadow",
      },
    },
    xAxis: {
      type: "category",
      boundaryGap: false,
      data: [1, 2, 3, 4, 5, 6],
      axisLabel: {
        color: "#fff",
        rotate: 30,
        formatter: function (value) {
          let val = value;
          if (curRange2.value == 2 && value) {
            val = value.split("-")[1] + "月";
          }
          return val;
        },
        fontSize: fitChartSize(9),
      },
      axisTick: {
        show: false,
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
    },
    yAxis: {
      type: "value",
      minInterval: 1,
      splitNumber: 4,
      axisLabel: {
        color: "#fff",
        align: "left",
        fontSize: fitChartSize(12),
        margin: fitChartSize(35),
      },
      axisTick: {
        show: false,
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: "dotted",
          color: "rgba(143, 171, 191, 0.45)",
        },
      },
      axisLine: {
        show: false,
      },
    },
    grid: {
      top: "20%",
      left: "15%",
      height: "65%",
      right: "4.5%",
    },
    series: [
      {
        data: [],
        type: "line",
        label: {
          show: true,
          position: "top",
          fontSize: fitChartSize(10),
        },
        areaStyle: {
          color: "rgba(2, 106, 130, .2)",
        },
        itemStyle: {
          color: {
            legendColor: "#07D1FA",
            type: "linear",
            x: 0,
            y: 0,
            x2: 0,
            y2: 1,
            colorStops: [
              {
                offset: 0,
                color: "#fff", // 0% 处的颜色
              },
              {
                offset: 1,
                color: "#fff", // 100% 处的颜色
              },
            ],
            global: false, // 缺省为 false
          },
        },
        lineStyle: {
          color: "#07D1FA",
          width: 1,
        },
        symbol: "circle",
        symbolSize: 3,
      },
      {
        data: [120],
        type: "line",
        tooltip: {
          show: false,
        },
        areaStyle: {
          opacity: 0,
        },
        itemStyle: {
          opacity: 0,
        },
        lineStyle: {
          opacity: 0,
        },
      },
    ],
  },
});

function setAnimate(flag, type) {
  let timer = null;
  if (flag) {
    play(type);
  } else {
    window.clearInterval(state[`scrolltimer${type}`]);
    timer = setTimeout(() => {
      state[`animate${type}`] = false;
      clearTimeout(timer);
    }, 1000);
  }
}

function play(type) {
  let timer = null;
  if (
    objData.value[type == 1 ? "sparepartsUseStatistics" : "corpAssets"]
      .length >= 5 &&
    state[`animate${type}`] == false
  ) {
    clearTimeout(state[`scrolltimer${type}`]);
    state[`scrolltimer${type}`] = setTimeout(() => {
      let arr =
        objData.value[type == 1 ? "sparepartsUseStatistics" : "corpAssets"];
      let obj = JSON.parse(JSON.stringify(arr[0]));
      arr.push(obj);
      state[`animate${type}`] = true;
      clearTimeout(timer);
      timer = setTimeout(() => {
        arr.shift();
        state[`animate${type}`] = false;
        play(type);
        clearTimeout(timer);
      }, 1000);
    }, 2000);
  }
}

// function setAnimate(flag) {
//   if (flag) {
//     play();
//   } else {
//     let timer = setTimeout(() => {
//       state.animate = false;
//       clearTimeout(timer);
//     }, 1000);
//     window.clearInterval(state.scrolltimer);
//   }
// }

// function play() {
//   if (objData.value.sparepartsUseStatistics.length >= 5) {
//     state.scrolltimer = setInterval(() => {
//       let obj = JSON.parse(
//         JSON.stringify(objData.value.sparepartsUseStatistics[0])
//       );
//       objData.value.sparepartsUseStatistics.push(obj);

//       state.animate = true;
//       let timer = setTimeout(() => {
//         objData.value.sparepartsUseStatistics.shift();
//         state.animate = false;
//         clearTimeout(timer);
//       }, 1000);
//     }, 2000);
//   }
// }

function handleRange(type, tab) {
  if (type == 0) {
    state.curRange1 = tab;
    getAdminKnowledgeStatistics({ range: state.curRange1 }).then((res) => {
      console.log("知识库统计", res);
      const { knowledgeBaseStatistics } = res.data;
      if (knowledgeBaseStatistics) {
        let data = [];
        lineOption.value.options.xAxis.data = knowledgeBaseStatistics.map(
          (item) => item.statisticsTime
        );
        lineOption.value.options.series[0].data = data =
          knowledgeBaseStatistics.map((item) => item.num);
        const yMax = Math.max(...data) * 1.1;
        lineOption.value.options.series[1].data = [yMax];
      }
    });
  }
  if (type == 1) {
    let data = [];
    state.curRange2 = tab;
    lineOption2.value.options.xAxis.data =
      tab == 2
        ? [
            "2024-09",
            "2024-10",
            "2024-11",
            "2024-12",
            "2025-01",
            "2025-02",
            "2025-03",
          ]
        : ["2019", "2020", "2021", "2022", "2023", "2024", "2025"];
    lineOption2.value.options.series[0].data = data =
      tab == 2 ? [0, 0, 0, 1, 0, 2, 1] : [0, 0, 0, 0, 0, 1, 4];
    const yMax = Math.max(...data) * 1.1;
    lineOption2.value.options.series[1].data = [yMax];
  }
}

objData.value.corpAssets = [
  {
    corpName: "广州市天河区第一实验小学",
    gpsX: 113.348337,
    gpsY: 23.129589,
    total: 159,
    onlineRatio: 0.77,
  },
  {
    corpName: "广州市实验外语学校（小学部）",
    gpsX: 113.26171,
    gpsY: 23.247371,
    total: 117,
    onlineRatio: 0.79,
  },
  {
    corpName: "广东外语外贸大学实验中学",
    gpsX: 113.363154,
    gpsY: 23.301951,
    total: 174,
    onlineRatio: 0.83,
  },
  {
    corpName: "天河区御景小学",
    gpsX: 113.383368,
    gpsY: 23.175122,
    total: 106,
    onlineRatio: 0.85,
  },
  {
    corpName: "海珠小学",
    gpsX: 113.345811,
    gpsY: 23.093671,
    total: 182,
    onlineRatio: 0.73,
  },
  {
    corpName: "广州市海珠区实验小学",
    gpsX: 113.284902,
    gpsY: 23.100963,
    total: 194,
    onlineRatio: 0.71,
  },
  {
    corpName: "广州高新区第一小学",
    gpsX: 113.495927,
    gpsY: 23.193623,
    total: 139,
    onlineRatio: 0.54,
  },
  {
    corpName: "黄埔区萝峰小学",
    gpsX: 113.519948,
    gpsY: 23.184295,
    total: 129,
    onlineRatio: 0.68,
  },
  {
    corpName: "华师附中番禺小学",
    gpsX: 113.369485,
    gpsY: 23.030568,
    total: 141,
    onlineRatio: 0.66,
  },
  {
    corpName: "番禺毓秀小学",
    gpsX: 113.325003,
    gpsY: 22.966873,
    total: 134,
    onlineRatio: 0.64,
  },
  {
    corpName: "广州市荔湾区汇龙小学",
    gpsX: 113.241166,
    gpsY: 23.139255,
    total: 146,
    onlineRatio: 0.62,
  },
  {
    corpName: "芳村小学",
    gpsX: 113.24526,
    gpsY: 23.090921,
    total: 153,
    onlineRatio: 0.51,
  },
  {
    corpName: "南沙小学（珠江湾校区）",
    gpsX: 113.587463,
    gpsY: 22.746424,
    total: 135,
    onlineRatio: 0.61,
  },
  {
    corpName: "南沙高新小学",
    gpsX: 113.423635,
    gpsY: 22.811211,
    total: 185,
    onlineRatio: 0.46,
  },
  {
    corpName: "广州市从化区流溪小学",
    gpsX: 113.5888876,
    gpsY: 23.552665,
    total: 189,
    onlineRatio: 0.47,
  },
  {
    corpName: "广州市从化区西宁小学",
    gpsX: 113.555098,
    gpsY: 23.58149,
    total: 167,
    onlineRatio: 0.53,
  },
  {
    corpName: "广州大学附属中学花都附属小学",
    gpsX: 113.246141,
    gpsY: 23.427947,
    total: 190,
    onlineRatio: 0.36,
  },
  {
    corpName: "广州市花都区新华第七小学",
    gpsX: 113.228086,
    gpsY: 23.394361,
    total: 114,
    onlineRatio: 0.42,
  },
  {
    corpName: "广州市增城区实验小学",
    gpsX: 113.812648,
    gpsY: 23.250421,
    total: 156,
    onlineRatio: 0.25,
  },
  {
    corpName: "广州市增城区挂绿小学",
    gpsX: 113.836811,
    gpsY: 23.314937,
    total: 164,
    onlineRatio: 0.49,
  },
];
// 中间广州市地图
mapOption.value.options.series[0].data = objData.value.corpAssets.map(
  (item) => {
    return {
      name: item.corpName,
      detail: {
        zczs: item.total,
        zxl: item.onlineRatio * 100 + "%",
      },
      value: [item.gpsX, item.gpsY],
    };
  }
);
function getCorpInfo() {
  getAdminCorpGps().then((res) => {
    console.log(res, "机构位置信息");
    if (res.data) {
      mapOption.value.options.series[0].data = res.data.map((item) => {
        let gps = item.gps
          ? JSON.parse(item.gps)
          : { gpsX: 113.298, gpsY: 23.17 };
        return {
          name: item.corpName,
          detail: {
            zczs: item.deviceTotal || 0,
            zxl: item.onlineRatio,
          },
          value: [gps.gpsX, gps.gpsY],
        };
      });
    }
  });
}

function goAnimate(obj) {
  let timer = null;
  window.clearInterval(state.scrolltimer1);
  window.clearInterval(state.scrolltimer2);
  state.animate1 = false;
  state.animate2 = false;
  setTimeout(() => {
    objData.value = {
      ...objData.value,
      ...obj,
    };
    play(1);
    play(2);
    clearTimeout(timer);
  }, 100);
}

//获取数据
async function getData() {
  getCorpInfo();
  handleRange(0, 2);
  handleRange(1, 2);
  await getAdminSchoolStatistics()
    .then((res) => {
      console.log(res, "大屏数据");
      let {
        deviceTotal,
        currentRunTotal,
        maintenanceTotal,
        assetsTotal,
        dutyTotal,
        runTotal,
        restTotal,
        scrapTotal,
        corpAssets,
        assetsTypeStatistics,
        deviceAppUserStatistics,
        knowledgeBaseStatistics,
        knowledgeBaseIncreaseStatistics,
        networkStatistics,
      } = res.data;

      mapOption.value.options.series[0].data[0].detail.yjzc = deviceTotal;

      col2_block1_list.value[0].num = deviceTotal;

      summaryLeftList.value[0].num = deviceTotal;
      summaryLeftList.value[1].num = (
        (currentRunTotal / assetsTotal) *
        100
      ).toFixed(2);
      summaryLeftList.value[2].num = scrapTotal;

      summaryRightList.value[0].num = maintenanceTotal;
      summaryRightList.value[1].num = dutyTotal;
      summaryRightList.value[2].num = restTotal;
      console.log(res.data.assetCoverageStatistics, "objData.value1111");

      objData.value = {
        ...objData.value,
        sparepartsConsumptionStatistics:
          res.data.sparepartsConsumptionStatistics, // 备件消耗排行   {deviceName: '2125', consumptionNum: 214}
        assetCoverageStatistics: res.data.assetCoverageStatistics, // 资产覆盖统计 返回一个对象
      };

      goAnimate({
        corpAssets,
        sparepartsUseStatistics:
          res.data.sparepartsUseStatistics?.map((item, index) => {
            item.rank = index + 1;
            return item;
          }) || objData.value.sparepartsUseStatistics, // 备件使用排行 表格对应字段  [{sparepartsName: '电脑键盘', userNum: 228}]
      });

      if (deviceAppUserStatistics?.appUseTime) {
        pieOption4.value.options.series[0].data.map((item) => {
          let obj = deviceAppUserStatistics?.appUseTime.find(
            (_) => _.month == item.name
          );
          item.value = obj?.timeTotal || 0;
        });
        let arr = [],
          data = [];
        deviceAppUserStatistics?.appUseTime.map((item, index) => {
          arr.push(item.month);
          data.push(item.timeTotal * 1);
        });
        const yMax2 = Math.max(...data) * 1.2;
        const newHeight = yMax2 * borderHeight2 * 0.01;
        barOption2.value.options.xAxis.data = arr;
        barOption2.value.options.series[1].data = data.map((val, index) => ({
          name: arr[index],
          value: val > newHeight ? val - newHeight : val,
          itemStyle: {
            color: barColors2[index % barColors2.length].replace(
              ", 1)",
              ", 0.5)"
            ),
          },
        }));
        barOption2.value.options.series[1].label.formatter = (params) => {
          return `${
            params.value > newHeight ? params.value + newHeight : params.value
          } h`;
        };
        barOption2.value.options.tooltip.formatter = (params) => {
          let relVal = params[0];
          return `${relVal.name}<br/>${
            relVal.value > newHeight ? relVal.value + newHeight : relVal.value
          }小时`;
        };
        barOption2.value.options.series[2].data = data.map((val, index) => ({
          value: val > newHeight ? newHeight : 0,
          itemStyle: {
            color: barColors2[index % barColors2.length],
          },
        }));

        nextTick(() => {
          let newMax = barData2Ref.value.getYAxisMax();
          // 获取 Y 轴最大值
          barOption2.value.options.series[0].data = data.map((_, index) => ({
            value: newMax,
            itemStyle: {
              borderColor: barColors2[index % barColors2.length],
            },
          }));
          console.log(
            newMax,
            barOption2.value,
            barData2Ref.value,
            "barData2Ref"
          );
        });
        console.log("应用使用时长", deviceAppUserStatistics?.appUseTime, yMax2);
      }

      if (assetsTypeStatistics) {
        let arr = [],
          data = [];
        assetsTypeStatistics.map((item, index) => {
          arr.push(item.assetsTypeName);
          data.push(item.num);
        });
        const yMax = Math.max(...data) * 1.2;
        const newHeight = yMax * borderHeight * 0.01;
        barOption.value.options.xAxis.data = arr;
        barOption.value.options.series[1].data = data.map((val, index) => ({
          name: arr[index],
          value: val > newHeight ? val - newHeight : val,
          itemStyle: {
            color: barColors[index % barColors.length].replace(
              ", 1)",
              ", 0.5)"
            ),
          },
        }));
        barOption.value.options.series[1].label.formatter = (params) => {
          return `${
            params.value > newHeight ? params.value + newHeight : params.value
          }`;
        };
        barOption.value.options.tooltip.formatter = (params) => {
          let relVal = params[0];
          return `${relVal.name}<br/>${
            relVal.value > newHeight ? relVal.value + newHeight : relVal.value
          }个`;
        };
        barOption.value.options.series[2].data = data.map((val, index) => ({
          value: val > newHeight ? newHeight : 0,
          itemStyle: {
            color: barColors[index % barColors.length],
          },
        }));
        // 获取 Y 轴最大值
        nextTick(() => {
          let newMax = Math.max(...data) * 1.2;
          console.log(barDataRef.value, "barDataRef");
          newMax = barDataRef.value.getYAxisMax();
          barOption.value.options.series[0].data = data.map((_, index) => ({
            value: newMax,
            itemStyle: {
              borderColor: barColors[index % barColors.length],
            },
          }));
        });
        console.log("信息资产类别", assetsTypeStatistics);
      }

      if (knowledgeBaseIncreaseStatistics) {
        let {
          knowledgeBaseMonth,
          knowledgeBaseMonthRise,
          knowledgeBaseYear,
          knowledgeBaseYearRise,
        } = knowledgeBaseIncreaseStatistics[0];

        mapOption.value.options.series[0].data[0].detail.sjxx =
          knowledgeBaseYear;

        col2_block1_list.value[2].num = knowledgeBaseYear;

        optList1.value[0].total = knowledgeBaseMonth;
        optList1.value[1].ratio = knowledgeBaseMonthRise;
        optList1.value[1].num =
          knowledgeBaseMonthRise?.replace("%", "") * 1 || 0;
      }

      if (networkStatistics) {
        networkList.value = networkStatistics;
      }

      console.log(objData.value, "objData.value");
    })
    .finally(() => {});
}

async function getAssetsData() {
  await getAdminAssetsStatistics().then((res) => {
    console.log(res, "资产统计");
    let {
      hardwareAssetsUseProportion,
      deviceTroubleProportion,
      assetsTypeProportion,
      assetsPortProportion,
      assetsHardDiskProportion,
      assetsMemoryProportion,
      assetsBrandProportion,
      assetsFaultBrandProportion,
      assetsFaultUseYearProportion,
      assetsSystemVersionProportion,
      assetsTagUseProportion,
    } = res.data;

    // 标签使用频率
    if (assetsTagUseProportion) {
      barOption3.value.options.xAxis.data =
        assetsTagUseProportion?.map((item, index) => item.assetsTagName) || [];
      barOption3.value.options.series[0].data =
        assetsTagUseProportion?.map((item) => item.num) || [];
    }

    // 资产系统版本占比TOP5
    if (assetsSystemVersionProportion) {
      let arr = [];
      assetsSystemVersionProportion?.map((item, index) => {
        arr.push({
          name: item.assetsSystemVersionName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor15.value[index % pieColor15.value.length],
          },
        });
      }) || [];
      data3D15.value = arr;

      console.log(pieOption15.value, "pieOption15.value");
    }

    // 故障时长占比
    if (deviceTroubleProportion) {
      pieOption7.value.options.series[0].data.map((item) => {
        let obj = deviceTroubleProportion.find((_) => _.timeRange == item.name);
        item.value = obj?.num || 0;
      });
    }

    // 设备使用年限占比
    if (hardwareAssetsUseProportion) {
      pieOption6.value.options.series[0].data.map((item) => {
        let obj = hardwareAssetsUseProportion.find(
          (_) => _.timeRange == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产设备类型占比
    if (assetsTypeProportion) {
      let arr = [];
      assetsTypeProportion.map((item, index) => {
        arr.push({
          name: item.assetsTypeName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor8.value[index % pieColor8.value.length].color2,
            detail: {
              color: pieColor8.value[index % pieColor8.value.length].color2,
            },
          },
        });
      });
      data3D8.value = arr;
      console.log("设备类型", assetsTypeProportion, data3D8.value);
    }

    // 资产端口类型占比
    if (assetsPortProportion) {
      let arr = [];
      assetsPortProportion.map((item, index) => {
        arr.push({
          name: item.assetsPortName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor9.value[index % pieColor9.value.length],
          },
        });
      });
      data3D9.value = arr;
      console.log("端口类型", assetsPortProportion, data3D9.value);
    }

    // 资产硬盘大小占比
    if (assetsHardDiskProportion) {
      pieOption10.value.options.series[0].data.map((item) => {
        let obj = assetsHardDiskProportion.find(
          (_) => _.assetsHardDiskName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 资产内存占比
    if (assetsMemoryProportion) {
      pieOption11.value.options.series[0].data.map((item) => {
        let obj = assetsMemoryProportion.find(
          (_) => _.assetsMemoryName == item.name
        );
        item.value = obj?.num || 0;
      });
    }

    // 故障资产品牌占比
    if (assetsBrandProportion) {
      let arr = [];
      assetsBrandProportion?.map((item, index) => {
        arr.push({
          name: item.assetsBrandName,
          value: item.num,
          proportion: item.proportion,
          height: 20,
          itemStyle: {
            opacity: 0.6,
            color: pieColor12.value[index % pieColor12.value.length],
          },
        });
      }) || [];

      data3D12.value = arr;
      console.log(data3D12.value, "data3D12.value");
    }

    // 故障资产使用年限占比
    if (assetsFaultUseYearProportion) {
      pieOption13.value.options.series[0].data =
        assetsFaultUseYearProportion?.map((item) => {
          return {
            name: item.timeRange,
            value: item.num,
          };
        }) || [];
    }
  });
}

function getOrderData() {
  getAdminWorkOrderStatistics({ range: 3 }).then((res) => {
    console.log(res, "工单统计");
    let {
      workOrderIncreaseStatistics,
      workOrderProportion,
      deviceFaultIncrease,
      workOrderProcrssingTime,
    } = res.data;

    if (workOrderProportion) {
      let { workOrderTotal, workOrderYesterdayTotal, workOrderTotalIncrease } =
        workOrderProportion;
      maintainList.value[0].total = workOrderTotal;
      maintainList.value[0].ratio = workOrderTotalIncrease;
      maintainList.value[0].num = workOrderTotal - workOrderYesterdayTotal;
      maintainList.value[1].total = workOrderTotal;
      maintainList.value[1].ratio = workOrderTotalIncrease;
      maintainList.value[1].num = workOrderTotal - workOrderYesterdayTotal;
    }

    if (deviceFaultIncrease) {
      let {
        deviceFaultTotal,
        deviceFaultTotalIncrease,
        deviceFaultYesterdayTotal,
        deviceFaultYesterdayTotalIncrease,
      } = deviceFaultIncrease;
      workOrderProportion;

      maintainList.value[2].total = deviceFaultTotalIncrease;
      maintainList.value[2].ratio =
        (
          deviceFaultTotalIncrease?.replace("%", "") * 1 ||
          0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
          0
        ).toFixed(2) + "%";
      maintainList.value[2].num =
        deviceFaultTotalIncrease?.replace("%", "") * 1 ||
        0 - deviceFaultYesterdayTotalIncrease?.replace("%", "") * 1 ||
        0;
    }

    if (workOrderIncreaseStatistics) {
      let {
        processingWorkOrderMonth,
        processingWorkOrderMonthRise,
        processingWorkOrderYear,
        processingWorkOrderYearRise,
      } = workOrderIncreaseStatistics[0];
      optList3.value[0].total = processingWorkOrderMonth;
      optList3.value[0].ratio = processingWorkOrderMonthRise;
      optList3.value[0].num =
        processingWorkOrderMonthRise?.replace("%", "") * 1 || 0;

      optList3.value[1].total = processingWorkOrderYear;
      optList3.value[1].ratio = processingWorkOrderYearRise;
      optList3.value[1].num =
        processingWorkOrderYearRise?.replace("%", "") * 1 || 0;
    }

    if (workOrderProcrssingTime) {
      let {
        todayAverageProcessingTime,
        riseOfProcessingTime,
        averageTime,
        totalAverageProcessingTime,
      } = workOrderProcrssingTime;
      optList4.value[0].total = todayAverageProcessingTime || 0;
      optList4.value[0].ratio = riseOfProcessingTime || "0%";
      optList4.value[0].num = riseOfProcessingTime?.replace("%", "") * 1 || 0;

      optList4.value[1].total =
        Math.floor(totalAverageProcessingTime?.replace("分钟", "") * 1 || 0) +
        "分钟";
      optList4.value[1].fq = averageTime;
    }
  });
}

watch(
  () => state.curAssetsTab,
  () => {
    console.log("切换tab");
    nextTick(() => {
      initChart();
    });
  },
  {
    deep: true,
    // immediate: true,
  }
);

function initChart() {
  if (state.curAssetsTab == 1) {
    pieRef4.value.initPieFunc();
    pieRef5.value.initPieFunc();
    pieRef2.value.initPieFunc();
    pieRef3.value.initPieFunc();
  } else {
    pieRef6.value.initPieFunc();
    pieRef7.value.initPieFunc();
    pieRef11.value.initPieFunc();
    pieRef10.value.initPieFunc();
    pieRef8.value.initPieFunc();
    pieRef9.value.initPieFunc();
    pieRef12.value.initPieFunc();
    pieRef15.value.initPieFunc();
  }
}

onMounted(async () => {
  proxy.$modal.loading();
  getOrderData();
  await getData();
  await getAssetsData();
  proxy.$modal.closeLoading();
  initChart();
  state.timer = setInterval(() => {
    getData();
    getOrderData();
    getAssetsData();
  }, 60000);
});

onBeforeUnmount(() => {
  window.clearInterval(state.scrolltimer1);
  window.clearInterval(state.scrolltimer2);
  clearInterval(state.timer);
});
</script>
  
  <style lang="scss" scoped>
:deep(.tooltip-bg) {
  position: absolute;
  top: -2.5vw;
  left: 0;
  background-color: transparent;
  width: 16vw;
  height: 8vw;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-bg.png");
  background-size: 100% 100%;
}
:deep(.tooltip-title) {
  // border: 1px solid red;
  padding-left: 1vw;
  font-size: 0.8vw;
  font-weight: bold;
  height: 2.2vw;
  line-height: 2.2vw;
  display: flex;
  align-items: center;
  gap: 0 0.5vw;
  color: #fff;
  &::before {
    content: "";
    width: 2vw;
    height: 2vw;
    background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/dialog-icon.png");
    background-size: 100% 100%;
  }
}
:deep(.tooltip-label) {
  // border: 1px solid red;
  width: 4vw;
}
:deep(.tooltip-value) {
  font-weight: bold;
}
:deep(.tooltip-row) {
  // border: 1px solid red;
  font-size: 0.8vw;
  display: flex;
  align-items: center;
  gap: 0 0.3vw;
  padding-left: 1.5vw;
  height: 2.5vw;
  padding-top: 0.8vw;
  color: #fff;
}
.el-descriptions {
  :deep(.label-width) {
    width: 130px;
    height: 60px;
  }

  :deep(.value-width) {
    width: 270px;
    min-height: 60px;
  }
}
.subtitle {
  // color: #c6d6ff;
  // // color: #fff;
  // padding: 0.2vw 0vw 0.2vw 0;
  // font-size: 0.7vw;
  // position: relative;
  // display: flex;
  // align-items: center;
  // justify-content: space-between;
  //  &-subTitle {
  font-size: 0.7vw;
  height: 1.6vw;
  line-height: 1.6vw;
  padding-left: 2.5vw;
  // margin-top: 1vw;
  color: #fff;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
  background-size: 100% 100%;
  text-shadow: 0px 0px 4px #0091ff;
  // }
  &.range {
    .range-tabs {
      cursor: pointer;
      display: flex;
      top: 0.4vw;
      right: 0vw;
      font-size: 0.6vw;
      color: #4095e5;
      &_item {
        border: 1px solid #1658ff4d;
        padding: 0.1vw 0.6vw;
        &.active {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/admin/range_checked.png");
          background-size: 96% 96%;
          background-position: center;
          background-repeat: no-repeat;
          color: #00f0ffff;
        }
      }
    }
  }
}
.unify-main_charts {
  margin-top: -1.5vw;
  display: flex;
  // border: 1px solid yellow;
  width: 100%;
  gap: 0 1vw;
  overflow: hidden;
  background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gz-bg.png");
  background-size: 100% 100%;

  .col {
    .flex {
      display: flex;
      gap: 0 1vw;
    }

    .block {
      flex: 1;
      //   border: 1px solid red;
      position: relative;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-pie_bg.png");
      background-size: 100% 100%;

      &-title {
        font-size: 0.8vw;
        height: 2vw;
        line-height: 2vw;
        padding-left: 1.5vw;
        margin-top: 1vw;
        color: #fff;
        position: relative;
        font-family: "HYYaKuHeiW";
        .titleText {
          transform: scale(1, 0.993905) skew(-6.290719deg, 0deg);
          background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
            #ffffff;
          -webkit-background-clip: text !important;
          -webkit-text-fill-color: transparent;
        }
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg.png");
        background-size: 100% 100%;
        &.long {
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/title-bg_long.png");
          background-size: 100% 100%;
        }
      }
    }

    .item-opt {
      width: 100%;
      // border: 1px solid red;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/line-info_bg2.png");
      background-size: 100% 100%;
      padding: 0.3vw;
      font-size: 0.6vw;
      // color: #8495b6;
      color: #fff;

      &_row {
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-top: 0.5vw;

        span {
          display: inline-block;
          font-size: 0.7vw;
          // line-height: 1.2vw;
          padding-right: 0.2vw;
        }

        div {
          padding-left: 0.8vw;
        }

        .up {
          // color: #fe005d;
          color: #ff2200;
          position: relative;

          &::before {
            content: "";
            position: absolute;
            top: 0.1vw;
            left: -0.2vw;
            width: 1vw;
            height: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/up.png");
            background-size: 100% 100%;
          }
        }

        .low {
          position: relative;
          color: #3be27e;

          &::before {
            content: "";
            position: absolute;
            top: 0.1vw;
            left: -0.2vw;
            width: 1vw;
            height: 0.6vw;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/maintain/low.png");
            background-size: 100% 100%;
          }
        }
      }

      &.center {
        .item-opt_row {
          justify-content: center;
          color: #fff;
          font-size: 0.7vw;
          span {
            font-size: 0.8vw;
            color: #4095e5;
          }
        }
      }
    }
  }

  .col1 {
    // border: 1px solid red;
    width: 24vw;
    height: 100%;

    &-block1 {
      //   border: 1px solid red;
      font-size: 0.8vw;
      padding: 1vw 0 0;

      .flex {
        align-items: center;
      }

      .flex2 {
        gap: 0 0.5vw;
      }

      .info-left {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 3vw;
          height: 3vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon2.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon3.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title1.png");
          background-size: 100% 100%;
        }
      }

      .info-right {
        display: flex;
        flex-direction: column;
        gap: 1vw 0;
        flex: 1;

        &_icon {
          // border: 1px solid red;
          width: 2.5vw;
          height: 2.5vw;
        }

        .icon1 {
          background: url("@/assets/screen/app/col1-block1_icon1.png");
          background-size: 100% 100%;
        }

        .icon2 {
          background: url("@/assets/screen/app/col1-block1_icon4.png");
          background-size: 100% 100%;
        }

        .icon3 {
          background: url("@/assets/screen/app/col1-block1_icon5.png");
          background-size: 100% 100%;
        }

        &_cont {
          width: 100%;
          padding: 0.4vw 0.2vw 0;
          background: url("@/assets/screen/app/col1-block1_bg1.png");
          background-size: 100% 100%;

          div {
            height: 1.3vw;
            line-height: 1.3vw;
            // border: 1px solid red;
            padding: 0 0.5vw;
            display: flex;
            align-items: center;
            gap: 0 0.2vw;

            span {
              color: #4095e5;
              font-size: 0.9vw;
              display: inline-block;
              // font-weight: bold;
            }
          }
        }

        &_title {
          background: url("@/assets/screen/app/col1-block1_title2.png");
          background-size: 100% 100%;
        }
      }
    }

    &-block2 {
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/network-bg.png");
      background-size: 100% 100%;
      .flex {
        gap: 0;
      }
      .network {
        position: relative;
        color: #4f5760;
        font-size: 0.85vw;
        line-height: 1.3vw;
        flex: 1;
        padding: 0 1vw 0 4vw;
        span {
          font-size: 1vw;
          color: #fff;
          display: inline-block;
          padding-right: 0.2vw;
        }
        &::before {
          position: absolute;
          content: "";
          left: 1vw;
          width: 2.5vw;
          height: 2.5vw;
        }
        &.up::before {
          background: url("@/assets/screen/admin/network_up.png");
          background-size: 100% 100%;
        }

        &.low::before {
          background: url("@/assets/screen/admin/network_low.png");
          background-size: 100% 100%;
        }
        .unit {
          color: #4f5760;
        }
      }
    }

    &-block3 {
      .battery {
        margin: 0.5vw 0;
        background: url("@/assets/screen/app/col1-block3_bg.png");
        background-size: 100% 100%;
        height: 6vw;
        width: 100%;
        text-align: center;
        font-family: "ZhengQingKeHuangYouTi";
        line-height: 5vw;
        font-size: 1.7vw;
        color: #fff;
      }
      .block-title_text {
        background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
          #ffffffff;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent;
      }
    }

    &-block4 {
      .block-title_text {
        background: linear-gradient(180deg, #31beff5c 0%, #ffffff5c 59.52%),
          #ffffffff;
        -webkit-background-clip: text !important;
        -webkit-text-fill-color: transparent;
      }
      .table {
        // min-height: 10.4vw;
        font-size: 0.75vw;
        padding: 0.2vw 0vw 0.5vw;
        margin-top: 0.8vw;
        color: #fff;
        background: radial-gradient(
            150.78% 100% at 50% 100%,
            #d6f0ff33 0%,
            #85e2ff1a 20.31%,
            #3b80c00f 41.67%,
            #032f7215 62.5%,
            #0210410c 80.73%,
            #00000000 100%
          ),
          linear-gradient(180deg, #050e2e4d 0%, #0c1a4b4d 100%);
        border-bottom: 1px solid #0091ff80;

        .table-data {
          height: 9.6vw;
          overflow-y: hidden;
          // border: 1px solid red;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -2vw;
          }
        }
        .opt-title {
          margin-bottom: 1vw;
          font-size: 1vw;
        }

        &-row {
          display: flex;
          height: 1.6vw;
          line-height: 1.6vw;
          text-align: center;
          justify-content: space-between;
          margin-bottom: 0.4vw;
          gap: 0 0.5vw;
          &.data:hover {
            background-color: #061d47;
          }

          &_name {
            width: 17vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            padding: 0 1vw;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 5vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 6vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }
      }
    }
  }

  .col2 {
    // border: 1px solid red;
    width: 49vw;
    &-block1 {
      padding-top: 1vw;
      width: 100%;
      // border: 1px solid red;
      display: flex;
      justify-content: center;
      font-size: 0.9vw;
      color: #6c95ff;
      gap: 0 1vw;
      &_item {
        // border: 1px solid red;
        width: 8.2vw;
        padding: 0.6vw 1vw 0.1vw;
        line-height: 1.2vw;
        letter-spacing: 0.1vw;
        background: url("@/assets/screen/top_data_frame.png");
        background-size: 100% 100%;
        span {
          font-size: 1.5vw;
          color: #c6d6ff;
          display: inline-block;
          padding-right: 0.2vw;
        }
      }
    }

    &-block2 {
      margin-top: 0.2vw;
      // border: 1px solid red;
      height: 35vw;
      position: relative;
      width: 49vw;
      div {
        margin: 0 auto;
        width: 30vw;
        height: 35vw;
        // border: 1px solid red;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/admin/gzmap-bg2.png");
        background-size: 100% 100%;
      }
    }

    &-block3 {
      .echart-item {
        // border: 1px solid red;
        height: 11.3vw;
        flex: 1;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
      }
      .subtitle {
        width: 90%;
        height: 1.5vw;
        margin-bottom: 0.5vw;
      }
      .table2 {
        flex: 1;
        height: 9.3vw;
        // border: 1px solid red;
        font-size: 0.6vw;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-block-bg.png");
        background-size: 100% 100%;

        .table-row {
          display: flex;
          height: 1.2vw;
          line-height: 1.2vw;
          text-align: center;
          // border: 1px solid red;
          margin-bottom: 0.3vw;
          &.data:hover {
            cursor: pointer;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-item_hover.png");
            background-size: 100% 100%;
          }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 8vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_count {
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .table-data {
          overflow: hidden;
          // border: 1px solid red;
          height: 7.6vw;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -1.5vw;
          }
          .odd,
          .even {
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-item_bg.png");
            background-size: 100% 100%;
          }
        }

        .opt-title {
          margin-top: 0.1vw;
          height: 1.3vw;
          line-height: 1.3vw;
          background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/rank-table_header.png");
          background-size: 100% 100%;
        }
      }
    }
  }

  .col3 {
    // border: 1px solid red;
    width: 24vw;
    height: 100%;

    .subtitle {
      font-size: 0.65vw;
      height: 1.3vw;
      line-height: 1.3vw;
      padding-left: 2vw;
      // margin-top: 1vw;
      color: #fff;
      background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/subTitle-bg.png");
      background-size: 100% 100%;
      text-shadow: 0px 0px 4px #0091ff;
    }

    &-block1 {
      // margin-top: -0.3vw;
      // border: 1px solid red;
      height: 2vw;
      cursor: pointer;
      display: flex;
      &.left {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/assets-tab1.png");
        background-size: 100% 100%;
      }
      &.right {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/assets-tab3.png");
        background-size: 100% 100%;
      }

      &_left,
      &_right {
        flex: 1;
        height: 2vw;
        // border: 1px solid yellow;
      }
    }

    &-block2 {
      .titleTab {
        position: absolute;
        right: 0.5vw;
        top: 0.3vw;
        display: flex;
        font-size: 0.65vw;
        gap: 0 0.5vw;
        color: #b4c0cc;
        z-index: 11;
        &_item {
          cursor: pointer;
          padding: 0vw 0.5vw;
          background-color: #3a4356;
          height: 1.3vw;
          line-height: 1.3vw;
          &.active {
            color: #1fc6ff;
            background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/timeRange_checked.png");
            background-size: 100% 100%;
          }
        }
      }

      .addr {
        padding: 0.4vw 0;
        text-align: center;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/admin/addr-bg.png");
        background-size: 100% 100%;
        font-size: 0.5vw;
        span {
          color: #0bf9fe;
          font-size: 0.6vw;
          padding: 0 0.1vw;
        }
      }

      &.bg {
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.5/app/assets/col3-bg.png");
        background-size: 100% 100%;
      }
      .flex {
        gap: 0 0.5vw;
      }
      .flex-bg {
        margin-bottom: 0.5vw;
      }
      .flex-bg1 {
        margin-bottom: 0.8vw;
      }
      .block {
        margin-top: 0.5vw;
        background: none;
      }
      .block-right {
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        gap: 1vw 0;
        background: none;
      }
    }

    &-block3 {
      margin-top: 0.6vw;
      .table2 {
        flex: 1;
        height: 11.5vw;
        // border: 1px solid red;
        background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/echart-bar_bg.png");
        background-size: 100% 100%;
        padding: 0.2vw 0;
        font-size: 0.7vw;

        .table-row {
          display: flex;
          // padding: .5vw 0 0;
          height: 1.2vw;
          line-height: 1.2vw;
          text-align: center;
          // border: 1px solid red;
          margin-bottom: 0.4vw;
          font-size: 0.6vw;
          // &.data:hover {
          //   background-color: #061d47;
          // }

          &_name {
            width: 9vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }

          &_number {
            width: 2vw;
          }

          &_type {
            width: 19vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            // border: 1px solid red;
          }

          &_count {
            // border: 1px solid red;
            width: 3vw;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
          }
        }

        .table-data {
          overflow: hidden;
          // border: 1px solid red;
          height: 7.6vw;
          &_inner {
            transition: all 0.5s ease-out;
            margin-top: -1.6vw;
          }
          .odd {
            // background-color: rgba(63, 86, 237, 0.35);
            background: #1658ff4d;
          }
          .even {
            // background-color: rgba(53, 206, 217, 0.35);
            background: #00f0ff4d;
          }
        }

        .opt-title {
          margin-top: 0.1vw;
          height: 1.3vw;
          line-height: 1.3vw;
          // background: url("https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/tableHead-bg.png");
          // background-size: 100% 100%;
          background: radial-gradient(
            99.01% 198.21% at 99.01% 50%,
            #71b2ff26 0%,
            #0014c700 100%
          );
        }
      }
    }
  }
}
</style>