server {
    listen       80;
    listen       18000;
    listen       36000;
    server_name  localhost;
    client_max_body_size 1024m;

    #access_log  /var/log/nginx/host.access.log  main;

    location / {
        root   /usr/share/nginx/html;
        index  index2.html index.htm;
    }

    ##应用端-版本1
    location ^~ /admin {
        alias /usr/share/nginx/html/maintainapp-prod;
        try_files $uri $uri/ /admin/index.html;
        index index.html index.htm;
    }
    
    location ^~ /teacher {
        alias /usr/share/nginx/html/maintainapp-prod;
        try_files $uri $uri/ /admin/index.html;
        index index.html index.htm;
    }

    ##应用端-版本1

    ##应用端-电教馆版本
    location ^~ /djgAdmin {
        alias /usr/share/nginx/html/maintainapp-prod;
        try_files $uri $uri/ /djgAdmin/index.html;
        index index.html index.htm;
    }
    ##应用端-电教馆版本

    ##后台
    location ^~ /prod-api/ {
        proxy_set_header Host $http_host;
        proxy_set_header ytflag 120;
        # proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://school-gateway:13000/;
        # proxy_pass http://********:13000/;# wg VPN
        # proxy_pass http://localhost:11451/api/;# openresty
        ##限流配置begin##
        # zone=ipRateLimit 设置使用哪个配置区域来做限制，与上面 limit_req_zone 里的name对应
        # limit_req zone=ipRateLimit burst=5 nodelay;
        ##限流配置end##

        ## 文件上传大小限制 ##
        client_max_body_size 1200M;
        # 设置代理读取和发送的超时时间为10分钟
        proxy_read_timeout 600s;  # 10分钟，即600秒
        proxy_send_timeout 600s;  # 同上
    }
    ##后台

        ##后台地址-电教馆版本
    location ^~ /djg-prod-api/ {
        proxy_set_header Host $http_host;
        proxy_set_header ytflag 120;
        # proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        # proxy_pass http://school-gateway:13000/;
        # proxy_pass http://********:13000/;# wg VPN
        proxy_pass http://**********:11451/api/;# openresty
        ##限流配置begin##
        # zone=ipRateLimit 设置使用哪个配置区域来做限制，与上面 limit_req_zone 里的name对应
        # limit_req zone=ipRateLimit burst=5 nodelay;
        ##限流配置end##

        ## 文件上传大小限制 ##
        client_max_body_size 20M;
        # 设置代理读取和发送的超时时间为10分钟
        proxy_read_timeout 600s;  # 10分钟，即600秒
        proxy_send_timeout 600s;  # 同上
    }
    ##后台地址-电教馆版本

    ##文件分发下载
    location /bigFileDispatch/ {
        charset utf-8;
        alias /home/<USER>/;
        autoindex on;  # 启用目录浏览
        autoindex_exact_size off;  # 可选：以更友好的格式显示文件大小
        autoindex_localtime on;  # 可选：显示文件的本地时间而非GMT
    }
    ##文件分发下载


    # 静态资源目录
    location /maintainUploadFile/ {
        # alias /home/<USER>/maintainUploadFile/;
        alias /home/<USER>/maintainUploadFile/;
        autoindex on;  # 启用目录浏览
        autoindex_exact_size off;  # 可选：以更友好的格式显示文件大小
        autoindex_localtime on;  # 可选：显示文件的本地时间而非GMT
    }
    # 静态资源目录

    ##文件分发下载
    location /schoolUploadFile/ {
        alias /home/<USER>/schoolUploadFile/;
        autoindex on;  # 启用目录浏览
        autoindex_exact_size off;  # 可选：以更友好的格式显示文件大小
        autoindex_localtime on;  # 可选：显示文件的本地时间而非GMT
    }
    ##文件分发下载

    ##后台
    location ^~ /xyywpt-prod-api/ {
        proxy_set_header Host $http_host;
        proxy_set_header ytflag 120;
        # proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header REMOTE-HOST $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_pass http://school-gateway:13000/;
        ##限流配置begin##
        # zone=ipRateLimit 设置使用哪个配置区域来做限制，与上面 limit_req_zone 里的name对应
        # limit_req zone=ipRateLimit burst=5 nodelay;
        ##限流配置end##
    }
    ##后台

    #websocket
    location ^~ /xyywpt-prod-api/system/tenantWs {
        proxy_pass http://school-gateway:13000/system/tenantWs;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
    
        # For WebSocket upgrade header
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 600s; 

    }
    #websocket

    #websocket-2025-04-01 18:35:37
    location ^~ /wsflow/ServerHub {
        proxy_pass http://school-gateway:13000/system/wsflow;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;

        # For WebSocket upgrade header
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_read_timeout 600s;

    }
    #websocket


    #error_page  404              /404.html;

    # redirect server error pages to the static page /50x.html
    #
    error_page   500 502 503 504  /50x.html;
    location = /50x.html {
        root   /usr/share/nginx/html;
    }

    # proxy the PHP scripts to Apache listening on 127.0.0.1:80
    #
    #location ~ \.php$ {
    #    proxy_pass   http://127.0.0.1;
    #}

    # pass the PHP scripts to FastCGI server listening on 127.0.0.1:9000
    #
    #location ~ \.php$ {
    #    root           html;
    #    fastcgi_pass   127.0.0.1:9000;
    #    fastcgi_index  index.php;
    #    fastcgi_param  SCRIPT_FILENAME  /scripts$fastcgi_script_name;
    #    include        fastcgi_params;
    #}

    # deny access to .htaccess files, if Apache's document root
    # concurs with nginx's one
    #
    #location ~ /\.ht {
    #    deny  all;
    #}
}

