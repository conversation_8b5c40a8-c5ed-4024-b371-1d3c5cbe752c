<template>
  <div class="app-container">
    <el-card shadow="never" class="page-card">
      <template #header>
        <div class="card-header page-header">
          <span>{{ route.meta.title }}</span>
        </div>
      </template>
      <div class="monitor-statics">
        <div class="monitor-statics_item">
          <div>智能终端总数</div>
          {{ ledgerTotal }}台
        </div>
        <div class="monitor-statics_item" @click="handleErrorQuery">
          <div>异常终端总数（点击处理）</div>
          {{ ledgerErrorTotal }}台
        </div>
      </div>
      <el-form
        class="search-list"
        :model="queryParams"
        ref="queryRef"
        :inline="true"
        v-show="showSearch"
        @submit.native.prevent
      >
        <el-form-item label="" prop="deviceCode">
          <el-input
            v-model="queryParams.deviceCode"
            placeholder="请输入设备编号"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="deviceName">
          <el-input
            v-model="queryParams.deviceName"
            placeholder="请输入设备名称"
            clearable
            style="width: 200px"
            @keyup.enter="handleQuery"
          />
        </el-form-item>
        <el-form-item label="" prop="typeId">
          <el-select
            v-model="queryParams.typeId"
            placeholder="请选择设备类型"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in typeList"
              :key="item.id"
              :label="item.typeName"
              :value="item.id"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="deviceStatus">
          <el-select
            v-model="queryParams.deviceStatus"
            placeholder="请选择设备状态"
            @change="handleQuery"
            clearable
            filterable
            style="width: 200px"
          >
            <el-option
              v-for="item in statusList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="status">
          <el-select
            v-model="queryParams.status"
            placeholder="请选择运行状态"
            @change="handleQuery"
            multiple
            clearable
            filterable
            style="width: 220px"
          >
            <el-option
              v-for="item in runList"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            >
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="" prop="address">
          <el-tree-select
            v-model="queryParams.address"
            :props="positionProps"
            :data="positionTreeList"
            placeholder="请选择安装位置"
            :render-after-expand="false"
            style="width: 200px"
            clearable
            @change="handleQueryPosition"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="Search" @click="handleQuery"
            >搜索</el-button
          >
          <el-button icon="Refresh" @click="resetQuery">重置</el-button>
          <!-- <el-switch
            v-model="isRalay"
            size="large"
            style="margin-left: 10px"
            active-text="是否转播"
          /> -->
        </el-form-item>
      </el-form>

      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="Setting"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleRepair"
            >批量报修</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="SwitchButton"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchRemote(1, 0)"
            >批量关机</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="primary"
            plain
            icon="RefreshRight"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchRemote(1, 1)"
            >批量重启</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="warning"
            plain
            icon="AlarmClock"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchFixed"
            >批量定时关机</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="success"
            plain
            icon="SortUp"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchWifi(1, 1)"
            >批量开启WIFI</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="info"
            plain
            icon="SortDown"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchWifi(1, 0)"
            >批量关闭WIFI</el-button
          >
        </el-col>
        <el-col :span="1.5">
          <el-button
            type="danger"
            plain
            icon="Brush"
            :disabled="tableAllSelectedId.length < 1"
            @click="handleBatchClear(1)"
            >批量清除缓存</el-button
          >
        </el-col>
        <!-- <right-toolbar
          v-model:showSearch="showSearch"
          @queryTable="getList"
        ></right-toolbar> -->
      </el-row>

      <el-table
        ref="ledgerTable"
        v-loading="loading"
        :data="ledgerList"
        border
        highlight-current-row
        @row-click="rowClick"
        @selection-change="selectionChange"
        @select="onTableSelect"
        @select-all="selectSingleTableAll"
      >
        <el-table-column
          type="selection"
          width="55"
          align="center"
          fixed="left"
        />
        <el-table-column
          label="运行状态"
          align="center"
          minWidth="120px"
          show-overflow-tooltip
        >
          <template #default="scope">
            <div
              v-if="runList[scope.row.runStatusList[0]]"
              style="
                display: flex;
                flex-wrap: wrap;
                gap: 5px;
                justify-content: center;
              "
            >
              <el-tag
                v-for="(item, index) in scope.row.runStatusList"
                :key="index"
                effect="dark"
                :type="runList[item].type"
                >{{ runList[item].label }}</el-tag
              >
            </div>
          </template>
        </el-table-column>
        <el-table-column
          label="设备编码"
          align="center"
          show-overflow-tooltip
          minWidth="150px"
          prop="deviceCodeStr"
        />
        <el-table-column
          label="设备名称"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="状态"
          align="center"
          show-overflow-tooltip
          minWidth="100px"
        >
          <template #default="scope">
            <el-tag
              v-if="statusList[scope.row.deviceStatus]"
              effect="dark"
              :type="statusList[scope.row.deviceStatus].type"
              >{{ statusList[scope.row.deviceStatus].label }}</el-tag
            >
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          show-overflow-tooltip
          minWidth="120px"
          prop="installAddress"
        />
        <el-table-column
          label="设备图片"
          align="center"
          show-overflow-tooltip
          minWidth="110px"
        >
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="操作"
          min-width="300"
          align="center"
          fixed="right"
          class-name="small-padding fixed-width"
        >
          <template #default="scope">
            <div
              style="
                display: flex;
                flex-wrap: wrap;
                justify-content: center;
                gap: 2px;
              "
            >
              <!-- 第一行按钮组 -->
              <div style="display: flex; gap: 2px">
                <el-tooltip content="详情" placement="top">
                  <el-button
                    link
                    type="success"
                    icon="Search"
                    @click.stop="handleCheck(scope.row)"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="关机" placement="top">
                  <el-button
                    link
                    type="danger"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="SwitchButton"
                    @click.stop="handleBatchRemote(0, 0, scope.row)"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="重启" placement="top">
                  <el-button
                    link
                    type="primary"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="RefreshRight"
                    @click.stop="handleBatchRemote(0, 1, scope.row)"
                  ></el-button>
                </el-tooltip>
              </div>
              <!-- 第二行按钮组 -->
              <div style="display: flex; gap: 2px">
                <el-tooltip content="定时关机" placement="top">
                  <el-button
                    link
                    type="warning"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="AlarmClock"
                    @click.stop="
                      isBatch = false;
                      handleFixed(scope.row);
                    "
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="开启WIFI" placement="top">
                  <el-button
                    link
                    type="success"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="SortUp"
                    @click.stop="handleBatchWifi(0, 1, scope.row)"
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="关闭WIFI" placement="top">
                  <el-button
                    link
                    type="info"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="SortDown"
                    @click.stop="handleBatchWifi(0, 0, scope.row)"
                  ></el-button>
                </el-tooltip>
              </div>
              <!-- 第三行按钮组 -->
              <div style="display: flex; gap: 2px">
                <el-tooltip content="清除缓存" placement="top">
                  <el-button
                    link
                    type="danger"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="Brush"
                    @click.stop="
                      isBatch = false;
                      handleBatchClear(0, scope.row);
                    "
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="实时桌面" placement="top">
                  <el-button
                    link
                    type="primary"
                    :disabled="scope.row.runStatus[0] == 0"
                    icon="Monitor"
                    @click.stop="
                      isRalay
                        ? handleDesktopRalay(scope.row)
                        : handleDesktop(scope.row)
                    "
                  ></el-button>
                </el-tooltip>
                <el-tooltip content="报修" placement="top">
                  <el-button
                    link
                    type="success"
                    icon="Setting"
                    @click.stop="handleRepair(scope.row)"
                  ></el-button>
                </el-tooltip>
              </div>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total > 0"
        :total="total"
        v-model:page="queryParams.current"
        v-model:limit="queryParams.size"
        :pageSizes="[5, 10, 20, 30, 50]"
        @pagination="getList"
      />
    </el-card>

    <!-- 添加或修改岗位对话框 -->
    <el-dialog
      class="custom-dialog"
      :title="title"
      v-model="open"
      width="400px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="fixRef" :model="fixForm" :rules="rules" label-width="100px">
        <el-form-item label="关机时间" prop="time">
          <el-date-picker
            v-model="fixForm.time"
            type="datetime"
            value-format="YYYY-MM-DDTHH:mm:ssZ"
            placeholder="请选择关机时间"
            :disabled-date="disabledDateFn"
            :disabled-hours="disabledHours"
            :disabled-minutes="disabledMinutes"
            :disabled-seconds="disabledSeconds"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button v-show="!readonly2" type="primary" @click="checkFixTime"
            >确 定</el-button
          >
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <!-- 填写报修单 -->
    <el-dialog
      class="custom-dialog"
      title="报修单填写"
      v-model="showRepair"
      top="10vh"
      width="1000px"
      append-to-body
      :close-on-click-modal="false"
      @close="cancel"
    >
      <el-table
        :data="repairList"
        border
        style="margin: 0 auto 40px; width: 96%"
        max-height="300"
      >
        <el-table-column
          label="设备编码"
          align="center"
          minWidth="120px"
          prop="deviceCode"
        />
        <el-table-column
          label="设备名称"
          align="center"
          minWidth="120px"
          prop="deviceNameStr"
        />
        <el-table-column
          label="设备类型"
          align="center"
          minWidth="120px"
          prop="deviceTypeStr"
        />
        <el-table-column
          label="规格型号"
          align="center"
          minWidth="120px"
          prop="modelStr"
        />
        <el-table-column label="设备图片" align="center" minWidth="120px">
          <template #default="scope">
            <el-image
              style="width: 50px; height: 30px; display: block; margin: 0 auto"
              :src="scope.row.deviceImg"
              fit="cover"
            />
          </template>
        </el-table-column>
        <el-table-column
          label="安装位置"
          align="center"
          minWidth="120px"
          prop="installAddress"
        />
        <el-table-column
          label="物理地址"
          align="center"
          minWidth="120px"
          prop="macAddress"
        />
        <el-table-column
          label="逻辑地址"
          align="center"
          minWidth="120px"
          prop="logicAddress"
        />
        <el-table-column
          label="IP地址"
          align="center"
          minWidth="120px"
          prop="ipAddress"
        />
        <el-table-column
          label="操作系统"
          align="center"
          minWidth="120px"
          prop="osVersion"
        />
        <el-table-column
          label="CPU"
          align="center"
          minWidth="120px"
          prop="cpu"
          show-overflow-tooltip
        />
        <el-table-column
          label="品牌"
          align="center"
          minWidth="120px"
          prop="brand"
        />
        <el-table-column
          label="内存"
          align="center"
          minWidth="120px"
          prop="internalStorage"
        />
        <el-table-column
          label="硬盘"
          align="center"
          minWidth="120px"
          prop="disk"
        />
        <el-table-column
          label="入库时间"
          align="center"
          minWidth="120px"
          prop="putTime"
        />
      </el-table>

      <el-form
        ref="repairRef"
        :model="repairForm"
        :rules="repairRules"
        label-width="140px"
      >
        <el-row>
          <el-col :span="12">
            <el-form-item label="报修人" prop="repairName">
              <el-input
                v-model="repairForm.repairName"
                placeholder="请输入报修人"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修人联系方式" prop="repairPhone">
              <el-input
                v-model="repairForm.repairPhone"
                placeholder="请输入报修人联系方式"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="故障描述" prop="remark">
              <el-input
                v-model="repairForm.remark"
                type="textarea"
                placeholder="请输入故障描述"
                style="width: 780px"
              />
            </el-form-item>
          </el-col>
          
          <el-col :span="24">
            <el-form-item label="故障图片" prop="images">
              <imgUpload
                @update:modelValue="setImgUrl"
                :limit="3"
                :modelValue="repairForm.images"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="报修时间" prop="repairTime">
              <el-date-picker
                type="date"
                v-model="repairForm.repairTime"
                placeholder="请选择报修时间"
                style="width: 300px"
              />
            </el-form-item>
          </el-col>
          <!-- <el-col :span="24">
            <el-form-item label="相关附件" prop="attachments">
              <el-upload
                class="upload-demo"
                :action="uploadFileUrl"
                :headers="headers"
                :on-success="handleAttachmentSuccess"
                :on-remove="handleAttachmentRemove"
                :before-upload="beforeAttachmentUpload"
                multiple
              >
                <el-button type="primary">点击上传</el-button>
                <template #tip>
                  <div class="el-upload__tip">
                    可上传任意格式文件,单个文件不超过10MB
                  </div>
                </template>
              </el-upload>
            </el-form-item>
          </el-col> -->
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="submitRepairForm">确 定</el-button>
          <el-button @click="repairCancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>

    <el-dialog
      title="实时桌面"
      v-model="open2"
      width="1000px"
      append-to-body
      @close="handleCloseWs"
      :close-on-click-modal="false"
    >
      <img id="player" style="width: 100%; height: 70vh" />
    </el-dialog>

    <el-dialog
      title="设备详情"
      v-model="open3"
      width="800px"
      append-to-body
      :close-on-click-modal="false"
    >
      <el-form ref="ledgerRef" :model="form" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="deviceName">
              <el-input
                v-model="form.deviceName"
                :readonly="readonly"
                placeholder="请输入设备名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备类型" prop="typeId">
              <el-select
                v-model="form.typeId"
                placeholder="请选择类型"
                :disabled="readonly"
                filterable
                style="width: 100%"
              >
                <el-option
                  v-for="item in typeList"
                  :key="item.id"
                  :label="item.typeName"
                  :value="item.id"
                  :disabled="!!item.status"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备标签" prop="tagIdList">
              <el-select
                v-model="form.tagIdList"
                placeholder="请选择标签"
                :disabled="readonly"
                filterable
                multiple
                style="width: 100%"
              >
                <el-option
                  v-for="item in tagList"
                  :key="item.tagId"
                  :label="item.tag"
                  :value="item.tagId"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备编码" prop="deviceCode">
              <el-input
                v-model="form.deviceCode"
                :readonly="readonly"
                placeholder="请输入设备编码"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="安装位置" prop="installAddress">
              <el-input
                v-model="form.installAddress"
                :readonly="readonly"
                placeholder="-"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="物理地址" prop="macAddress">
              <el-input
                v-model="form.macAddress"
                :readonly="readonly"
                placeholder="请输入物理地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="规格型号" prop="model">
              <el-input
                v-model="form.model"
                :readonly="readonly"
                placeholder="请输入规格型号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="逻辑地址" prop="logicAddress">
              <el-input
                v-model="form.logicAddress"
                :readonly="readonly"
                placeholder="请输入逻辑地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="IP地址" prop="ipAddress">
              <el-input
                v-model="form.ipAddress"
                :readonly="readonly"
                placeholder="请输入IP地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="操作系统" prop="osVersion">
              <el-input
                v-model="form.osVersion"
                :readonly="readonly"
                placeholder="请输入操作系统版本号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="状态" prop="deviceStatus">
              <el-select
                v-model="form.deviceStatus"
                placeholder="请选择状态"
                disabled
                style="width: 100%"
              >
                <el-option
                  v-for="item in statusList"
                  :key="item.value"
                  :label="item.label"
                  :value="item.value"
                >
                </el-option>
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="CPU" prop="cpu">
              <el-input
                v-model="form.cpu"
                :readonly="readonly"
                placeholder="请输入CPU型号"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="品牌" prop="brand">
              <el-input
                v-model="form.brand"
                :readonly="readonly"
                placeholder="请输入设备品牌名称"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内存" prop="internalStorage">
              <el-input
                v-model="form.internalStorage"
                :readonly="readonly"
                placeholder="请输入设备内存"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="硬盘" prop="disk">
              <el-input
                v-model="form.disk"
                :readonly="readonly"
                placeholder="请输入设备硬盘"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="入库时间" prop="putTime">
              <el-date-picker
                v-model="form.putTime"
                format="YYYY-MM-DD"
                style="width: 100%"
                :readonly="readonly"
                type="date"
                placeholder="请选择入库时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="边缘服务器" prop="ralayHost">
              <el-input
                v-model="form.ralayHost"
                :readonly="readonly"
                placeholder="请输入边缘服务器的ip地址"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备图片" prop="deviceImg">
              <imgUpload
                :limit="1"
                :modelValue="form.deviceImg"
                :disabled="readonly"
                :isShowTip="!readonly"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button @click="cancel">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="monitorManage">
import imgUpload from "@/components/ImageUpload";
import { ref, onBeforeUnmount, onMounted, nextTick } from "vue";
import { ElLoading } from "element-plus";
import { useRoute } from "vue-router";
import { devicePage, deviceInfo } from "@/api/mediaTeach/ledger";
import { getDeviceType } from "@/api/mediaTeach/type";
import { tenantTree as getTenantTree } from "@/api/park";
import { getDeviceTag } from "@/api/mediaTeach/tag";
import { getPositionTree } from "@/api/mediaTeach/position";
import { deviceRepair } from "@/api/mediaTeach/trouble";
import { treeToArray, treeFindPath, formatDate } from "@/utils";
import useUserStore from "@/store/modules/user";
import axios from "axios";
import { createWebSocket, closeSock, websocketsend } from "@/utils/socket";
import { webSocketStore } from "@/store/modules/webSocket";
import { getToken } from "@/utils/auth";

axios.defaults.timeout = 15000;
const webSocket = webSocketStore();
const ipUrl = ref({
  ip: "",
  url: "",
});
const global_callback = (msg, err = false) => {
  // console.log("websocket的回调函数收到服务器信息：", msg);
  if (err) {
    proxy.$modal.msgError(msg);
    loadingPage.value.close();
    open2.value = false;
  } else {
    if (typeof msg !== "string") {
      const data = new Blob([msg], { type: "image/png" });
      const img = new Image();
      img.src = window.URL.createObjectURL(data);
      const player = document.getElementById("player");
      img.onload = function () {
        player.src = img.src;
      };
      webSocket.addMsg(msg);
    } else {
      const player = document.getElementById("player");
      player.src = data.pic;
      open2.value = false;
    }
  }
};
async function handleCloseWs() {
  await closeSock();
  if (isRalay.value) {
    let { ip1, ip2, url1, url2 } = getUrlIp(ipUrl.value.ip, ipUrl.value.url);
    axios({
      headers: {
        "Content-Type": "application/json;charset=utf-8",
        "Ralay-Url": url1,
      },
      url: `${ip2}/api/Win/StopSend`,
      method: "get",
    }).then((res) => {
      console.log("stopsend");
    });
  }
  const player = document.getElementById("player");
  player.src = data.pic;
}
function getUrlIp(ip, url = "") {
  let str = "http",
    ws = "ws",
    href = window.location.href.split(":");
  let ip1 = ip,
    url1 = ip,
    ip2 = url,
    url2 = url;
  if (ip1.includes(str)) {
    url1 = url1.replace("s", "").replace("http://", "");
  } else {
    ip1 = `${href[0]}://` + ip1;
  }
  if (ip2.includes(str)) {
    url2 = url2.replace("s", "").replace("http://", "");
  } else {
    ip2 = `${href[0]}://` + ip2;
  }
  ws = href[0] == "https" ? "wss" : "ws";
  // console.log('网站协议', href[0], ws, href[0] == 'https')
  return { ip1, ip2, url1, url2, ws };
}
function handleDesktop(row) {
  loadingPage.value = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0,0,0,0.7)",
  });
  let { url1, ws } = getUrlIp(row.ipAddress);
  let uri = `${ws}://${url1}/ServerHub?token=${getToken()}`;
  createWebSocket(global_callback, uri).then((res) => {
    loadingPage.value.close();
    open2.value = true;
  });
}
async function handleDesktopRalay(row) {
  if (row.ralayHost) {
    loadingPage.value = ElLoading.service({
      lock: true,
      text: "Loading",
      background: "rgba(0,0,0,0.7)",
    });
    ipUrl.value.ip = row.ipAddress;
    ipUrl.value.url = row.ralayHost;
    let { ip1, ip2, url1, url2, ws } = getUrlIp(row.ipAddress, row.ralayHost);

    console.log(ip1, ip2, url1, url2, ws);
    let uri = `${ws}://${url2}/ServerHub?token=${getToken()}`;
    createWebSocket(global_callback, uri).then((res) => {
      console.log(res);
      websocketsend(url1);
      // res.send("1");
      loadingPage.value.close();
      open2.value = true;
    });
    // await axios({
    //     headers: {
    //         'Content-Type': 'application/json;charset=utf-8',
    //         'Ralay-Url': url1
    //     },
    //     url: `${ip2}/api/Win/Open?ip=${url2}`,
    //     method: 'get'
    // }).then(async res => {
    //     console.log(res.data.data)
    //     // let uri = `${ws}://${url2}/Video?sendId=${res.data.data}`
    //     let uri = `${ws}://${url2}/ServerHub?token=${getToken()}`
    //     await createWebSocket(global_callback, uri)
    //     axios({
    //         headers: {
    //             'Content-Type': 'application/json;charset=utf-8',
    //             'Ralay-Url': url1
    //         },
    //         url: `${ip2}/api/Win/Send`,
    //         method: 'get'
    //     }).then(resp => {
    //         loadingPage.value.close()
    //         open2.value = true
    //     }).catch((e) => {
    //         proxy.$modal.msgError(e);
    //         loadingPage.value.close()
    //     })
    // }).catch((e) => {
    //     proxy.$modal.msgError(e);
    //     loadingPage.value.close()
    // })
  } else {
    proxy.$modal.msgWarning("该设备缺少边缘服务器字段！");
  }
}

const route = useRoute();
const { proxy } = getCurrentInstance();
const isAdmin = !useUserStore().tenantId;

const ledgerTable = ref(null);
const isRalay = ref(false);
const ledgerList = ref([]);
const repairList = ref([]);
const loadingPage = ref(null);
const open = ref(false);
const open2 = ref(false);
const open3 = ref(false);
const loading = ref(false);
const showSearch = ref(true);
const showRepair = ref(false);
const readonly2 = ref(false);
const readonly = ref(true);
const isBatch = ref(false);
const ips = ref([]);
const statusList = ref([
  { label: "正常", value: 0, type: "success" },
  { label: "维修中", value: 1, type: "danger" },
]);
const runList = ref([
  { label: "关机", value: 0, type: "info" },
  { label: "运行良好", value: 1, type: "success" },
  { label: "网速过慢", value: 2, type: "warning" },
  { label: "GPU过高", value: 3, type: "danger" },
  { label: "磁盘占用率过高", value: 4, type: "danger" },
  { label: "CPU过高", value: 5, type: "danger" },
  { label: "内存过高", value: 6, type: "danger" },
]);
const tagList = ref([]);
const positionTreeList = ref([]);
const positionList = ref([]);
const positionProps = ref({
  label: "name",
  children: "children",
  value: "id",
});
const queryPositionResult = ref({
  names: [],
  ids: [],
});
const typeList = ref([]);
const tenantTree = ref([]);
const total = ref(0);
const title = ref("");

const data = reactive({
  tableRadio: [],
  tableAllSelectedId: [], // 保存表格勾选的全部id
  tableAllSelectedRow: [], // ��存表格勾选的行数��
  tableData: [], // 当前所在页码的表格数据
  tableData_all: [], // 表格的全部数据
  ledgerTotal: 0,
  ledgerErrorTotal: 0,
  fixForm: {
    deviceCode: "",
    time: "",
  },
  form: {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  },
  queryParams: {
    abnormalInterruptionChannel: 0,
    current: 1,
    size: 5,
    tenantId: "",
    deviceName: "",
    model: "",
    typeId: "",
    deviceStatus: "",
    putTime: "",
    status: [],
  },
  rules: {
    time: [
      {
        required: true,
        message: "请选择关机时间",
        trigger: ["blur", "change"],
      },
    ],
  },
  repairForm: {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  },
  repairRules: {
    repairName: [
      { required: true, message: "报修人不能为空", trigger: "blur" },
    ],
    repairPhone: [
      { required: true, message: "报修人联系方式不能为空", trigger: "blur" },
      {
        pattern: /^1[3-9]\d{9}$/,
        message: "请输入正确的手机号",
        trigger: "blur",
      },
    ],
    remark: [
      { required: true, message: "故障描述不能为空", trigger: "blur" },
      { max: 200, message: "故障描述最多输入200个字符", trigger: "blur" },
    ],
    images: [
      {
        required: true,
        type: "array",
        min: 1,
        message: "故障图片至少要有1张",
        trigger: ["blur", "change"],
      },
    ],
  },
  pic: "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAADcAAAAnCAYAAACrDdDdAAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAAFiUAABYlAUlSJPAAAABBSURBVGhD7c8BDQAwEMSg+Td908GnOODtsHKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKqcqpyqnKm7QNz9WRsCGQFWQAAAABJRU5ErkJggg==",
});

const {
  queryParams,
  form,
  rules,
  fixForm,
  tableAllSelectedId,
  repairForm,
  repairRules,
  ledgerErrorTotal,
  ledgerTotal,
} = toRefs(data);

// 限制日期
const disabledDateFn = (date) => {
  //   const today = formatDate(new Date().getTime())
  if (date.getTime() + 86400000 < new Date().getTime()) {
    // console.log('date', date, date.getTime(), (date.getTime() + 86400000), new Date().getTime())
    return true;
  }
  return false;
};

// 限制小时
const disabledHours = () => {
  const a = [];

  for (let i = 0; i < 24; i++) {
    if (new Date().getHours() > i) {
      a.push(i);
    }
  }
  return a;
};

// 限制分钟
const disabledMinutes = () => {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getMinutes() > i) {
      a.push(i);
    }
  }
  return a;
};

// 限制毫秒
const disabledSeconds = () => {
  const a = [];
  for (let i = 0; i < 60; i++) {
    if (new Date().getSeconds() > i) {
      a.push(i);
    }
  }
  return a;
};

const checkFixTime = () => {
  const time = fixForm.value.time.replace("S", " ").replace("Z", "");
  if (new Date(time).getTime() < new Date().getTime()) {
    proxy.$modal.msgWarning("选择的时间不能小于当前时间");
    return;
  }
  isBatch.value ? handleBatchRemote(1, 3) : handleRemote(fixForm.value, 3);
};

function handleQueryPosition(val) {
  queryPositionResult.value = {
    ids: [],
    names: [],
  };
  queryPositionResult.value.ids = treeFindPath(
    positionTreeList.value,
    (d) => d.id == val
  );
  const arr = queryPositionResult.value.ids;
  for (let i = 0; i < arr.length; i++) {
    queryPositionResult.value.names[i] = positionList.value.find(
      (node) => node.id == arr[i]
    ).name;
  }
  console.log("queryPositionResult ==> ", queryPositionResult.value);
  queryParams.value.address = queryPositionResult.value.names.join("-");
  handleQuery();
}

// 查找对象在对象数组中的位置
function findIndexInObejctArr(arr, obj) {
  for (let i = 0, iLen = arr.length; i < iLen; i++) {
    if (arr[i].deviceId === obj.deviceId) {
      return i;
    }
  }
  return -1;
}

function getTagList() {
  getDeviceTag().then((response) => {
    tagList.value = response.data;
  });
}

async function getOptions() {
  await getDeviceType().then((response) => {
    typeList.value = response.data.reduce((res, cur) => {
      if (cur.typeName == "智慧大屏") queryParams.value.typeId = cur.id;
      res.push({
        ...cur,
      });
      return res;
    }, []);
  });
}

function getTenant() {
  getTenantTree().then((response) => {
    tenantTree.value = response.data;
  });
}

function getPositionTreeList() {
  getPositionTree({}).then((response) => {
    positionTreeList.value = response.data;
    positionList.value = treeToArray(response.data);
    console.log("positionList ==>", positionList.value);
  });
}

function handleRepair(row) {
  if (tableAllSelectedId.length < 1 && !row.deviceId) {
    proxy.$modal.msgWarning("请至少选择一个设备");
    return;
  }
  repairReset();
  repairList.value = row.deviceId ? [row] : data.tableAllSelectedRow;
  // console.log(repairList.value)
  showRepair.value = true;
}

/** 查询设备列表 */
function getList() {
  loading.value = true;
  devicePage(queryParams.value).then((response) => {
    const { page, abnormalTerminal } = response.data;
    ledgerList.value = page.records.reduce((res, cur) => {
      let {
        deviceName,
        deviceType,
        isOff,
        runStatus,
        ralayHost,
        deviceCode,
        ipAddress,
        logicAddress,
        macAddress,
        model,
        osVersion,
        cpu,
        internalStorage,
        disk,
        brand,
        tenantName,
      } = cur;
      res.push({
        ...cur,
        status: !!isOff ? 0 : !!runStatus ? 2 : 1,
        deviceNameStr: deviceName || "-",
        deviceTypeStr: deviceType || "-",
        deviceCodeStr: deviceCode || "-",
        ipAddressStr: ipAddress || "-",
        ralayHostStr: ralayHost || "-",
        logicAddressStr: logicAddress || "-",
        macAddressStr: macAddress || "-",
        tenantNameStr: tenantName || "-",
        modelStr: model || "-",
        osVersionStr: osVersion || "-",
        cpuStr: cpu || "-",
        internalStorageStr: internalStorage || "-",
        diskStr: disk || "-",
        brandStr: brand || "-",
        runStatusList: runStatus,
      });
      return res;
    }, []);
    // console.log(ledgerList.value)
    console.log(
      "筛选条件参数，当前页台账列表数据",
      queryParams.value,
      ledgerList.value
    );
    total.value = page.total;
    // total.value = ledgerTotal.value = page.total
    ledgerErrorTotal.value = abnormalTerminal;
    loading.value = false;
    nextTick(() => {
      ledgerList.value.forEach((item) => {
        if (data.tableAllSelectedId.indexOf(item.deviceId) > -1) {
          // console.log(item, data.tableAllSelectedId)
          ledgerTable.value.toggleRowSelection(item, true);
        } else {
          ledgerTable.value.toggleRowSelection(item, false);
          // console.log('去除勾选:', item)
        }
      });
    });
  });
  devicePage({
    current: 1,
    size: 9999999,
    abnormalInterruptionChannel: queryParams.value.abnormalInterruptionChannel,
  }).then((res) => {
    console.log("所有台账数据", res);
    // total.value = ledgerTotal.value = res.data.page.total

    data.tableData_all = res.data.page.records.filter(
      (_) => _.deviceType == "智慧大屏"
    );
    ledgerTotal.value = data.tableData_all.length;
    console.log(data.tableData_all);
  });
}
/** 取消按钮 */
function cancel() {
  open.value = false;
  open3.value = false;
  reset();
}
/** 取消按钮 */
function repairCancel() {
  showRepair.value = false;
  repairReset();
}
/** 表单重置 */
function reset() {
  fixForm.value = {
    ipAddress: "",
    time: null,
  };
  form.value = {
    deviceId: "",
    deviceImg: "",
    deviceCode: "",
    deviceName: "",
    typeId: "",
    tagIdList: [],
    deviceType: "",
    deviceStatus: 0,
    macAddress: "",
    logicAddress: "",
    model: "",
    ipAddress: "",
    ralayHost: "",
    osVersion: "",
    cpu: "",
    internalStorage: "",
    disk: "",
    putTime: "",
    brand: "",
    installAddress: "",
    createTime: "",
  };
  proxy.resetForm("ledgerRef");
  proxy.resetForm("fixRef");
}
/** 表单重置 */
function repairReset() {
  repairForm.value = {
    type: 1,
    repairName: "",
    repairPhone: "",
    deviceCodeList: [],
    remark: "",
    images: [],
    attachments: [],
  };
  proxy.resetForm("repairRef");
}
function handleCheck(row) {
  reset();
  deviceInfo({ id: row.deviceId }).then((response) => {
    form.value = JSON.parse(JSON.stringify(response.data));
    open3.value = true;
  });
}
/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.current = 1;
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 0;
  getList();
}
/** 异常搜索按钮操作 */
function handleErrorQuery() {
  // resetQuery()
  proxy.resetForm("queryRef");
  queryParams.value.current = 1;
  data.tableAllSelectedId = []; // 点击查询按钮后，保存的勾选的id要清空
  data.tableAllSelectedRow = []; // 点击查询按钮后，保存的勾选的数据要清空
  queryParams.value.abnormalInterruptionChannel = 1;
  // queryParams.value.address = ''
  getList();
}
/** 重置按钮操作 */
function resetQuery() {
  proxy.resetForm("queryRef");
  handleQueryPosition();
}

/** 单击某行 */
function rowClick(row) {
  // console.log(row, 'rowClick')
  // 只有同时高亮并选中的情况下才能取消选中
  if (
    findIndexInObejctArr(
      JSON.parse(JSON.stringify(data.tableAllSelectedRow)),
      row
    ) > -1
  ) {
    if (data.tableRadio === row) {
      data.tableRadio = [];
      ledgerTable.value.setCurrentRow(null);
      ledgerTable.value.toggleRowSelection(row, false);
      const index = data.tableAllSelectedId.indexOf(row.deviceId);
      data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
      data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
    } else {
      data.tableRadio = row;
      ledgerTable.value.setCurrentRow(row);
    }
  } else {
    data.tableRadio = row;
    ledgerTable.value.setCurrentRow(row);
    ledgerTable.value.toggleRowSelection(row, true);
  }
  // console.log(tableAllSelectedId)
}

function submitRepairForm() {
  proxy.$refs["repairRef"].validate((valid) => {
    // console.log(valid)
    if (valid) {
      repairForm.value.deviceCodeList = [];
      repairList.value.map((item) => {
        repairForm.value.deviceCodeList.push(item.deviceCode);
      });

      // 构建提交数据
      const submitData = {
        ...repairForm.value,
        channel: "管理端-管理员主动报修",
        attachments: repairForm.value.attachments.map((item) => item.url), // 只提交文件URL
      };

      deviceRepair(submitData).then((res) => {
        proxy.$modal.msgSuccess("报修成功");
        getList();
        showRepair.value = false;
      });
    }
  });
}

// 多选事件
function selectionChange(val) {
  // 将获取到的id存入tableAllSelectedId数组(点击某行前面的勾选、选中某行的勾选、全选。三种状态都能触发此功能)
  val.forEach((item) => {
    if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
      data.tableAllSelectedId.push(item.deviceId);
      data.tableAllSelectedRow.push(item);
    }
  });
  // console.log(tableAllSelectedId)
  // console.log('selectionChange的事件:', val)
}

// 点击表格勾选触发的事件
function onTableSelect(rows, row) {
  // 判断是点击了表格勾选还是取消勾选
  // true就是选中，0或者false是取消选中
  const selected = rows.length && rows.indexOf(row) !== -1;
  if (!selected) {
    // 如果点击取消勾选
    const index = data.tableAllSelectedId.indexOf(row.deviceId);
    data.tableAllSelectedId.splice(index, 1); // 取消勾选，则删除id
    data.tableAllSelectedRow.splice(index, 1); // 取消勾选，则删除数据
  }
}
// 表格全选触发的事件
function selectSingleTableAll(selection) {
  // 获取当前页码所显示的数据
  const a = ledgerList.value;
  // 获取当前页勾选的数据
  const b = selection;
  let flag_inCurrentPage;
  selection.forEach((item) => {
    if (item.deviceId === a[0].deviceId) {
      flag_inCurrentPage = true;
      return;
    }
  });
  // 后端分页，点击全选框时，当前页的勾选数目等于当前页数据个数，判断为全选
  const flag = a.length === b.length && flag_inCurrentPage;
  if (flag === true) {
    // 切换成了全选状态
    data.tableData_all.forEach((item) => {
      if (data.tableAllSelectedId.indexOf(item.deviceId) === -1) {
        data.tableAllSelectedId.push(item.deviceId); // 如果点击全选就保存全部的id
        data.tableAllSelectedRow.push(item); // 则保存表格全部的数据
      }
    });
    // console.log('切换成了全选状态', data.tableData_all)
  } else {
    // 切换成了非全选状态
    data.tableAllSelectedId = []; // 如果取消全选，则清空保存的id
    data.tableAllSelectedRow = []; // // 如果取消全选，则清空保存的数据
    // console.log('切换成了非全选状态')
  }
  // console.log(tableAllSelectedId)
}

/** 批量操作前的检测 */
const checkBatch = () => {
  let arr = [],
    flag = false;
  ips.value = [];
  data.tableAllSelectedRow.map((item) => {
    if (item.runStatus.indexOf(0) != -1) {
      flag = true;
    } else {
      arr.push(item.deviceName);
      ips.value.push(item.ipAddress);
    }
  });
  return { arr, flag };
};

/** 批量定时关机按钮操作 */
const handleBatchFixed = () => {
  const { flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("无法进行此操作，请查验设备状态");
    return;
  }
  isBatch.value = true;
  title.value = "批量定时关机";
  open.value = true;
};

const handleFixed = (row) => {
  isBatch.value = false;
  title.value = "定时关机";
  fixForm.value.ipAddress = row.ipAddress;
  fixForm.value.ralayHost = row.ralayHost;
  open.value = true;
};

/** 批量重启/关闭/定时关闭按钮操作 */
const handleBatchRemote = (isBatch, type, row) => {
  const { arr, flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("无法进行此操作，请查验设备状态");
    return;
  }
  if (type !== 3) {
    if (!!isBatch) {
      proxy.$modal
        .confirm(
          "是否确认批量" +
            (!!type ? "重启" : "关闭") +
            '设备名称为"' +
            JSON.stringify(arr) +
            '"的设备？'
        )
        .then(function () {
          for (let i = 0; i < ips.value.length; i++) {
            handleRemote(
              {
                ipAddress: ips.value[i],
                time: type === 3 ? fixForm.value.time : null,
              },
              type
            );
          }
        })
        .catch(() => {});
    } else {
      proxy.$modal
        .confirm(
          "是否确认" +
            (!!type ? "重启" : "关闭") +
            '设备名称为"' +
            row.deviceName +
            '"的设备？'
        )
        .then(function () {
          handleRemote(row, type);
        })
        .catch(() => {});
    }
  } else {
    for (let i = 0; i < ips.value.length; i++) {
      handleRemote(
        {
          ipAddress: ips.value[i],
          time: type === 3 ? fixForm.value.time : null,
        },
        type
      );
    }
  }
  cancel();
};

/** 批量清除缓存 */
const handleBatchClear = (isBatch, row) => {
  const { arr, flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("无法进行此操作，请查验设备状态");
    return;
  }
  if (!!isBatch) {
    proxy.$modal
      .confirm(
        '是否确认批量清除设备名称为"' + JSON.stringify(arr) + '"的设备缓存？'
      )
      .then(function () {
        for (let i = 0; i < ips.value.length; i++) {
          handleClear({ ipAddress: ips.value[i] });
        }
      })
      .catch(() => {});
  } else {
    proxy.$modal
      .confirm('是否确认清除设备名称为"' + row.deviceName + '"的设备缓存？')
      .then(function () {
        handleClear(row);
      })
      .catch(() => {});
  }
};
/** 批量开启/关闭 Wifi */
const handleBatchWifi = (isBatch, type, row) => {
  const { arr, flag } = checkBatch();
  if (flag) {
    proxy.$modal.msgWarning("无法进行此操作，请查验设备状态");
    return;
  }
  if (!!isBatch) {
    proxy.$modal
      .confirm(
        "是否确认批量" +
          (!!type ? "开启" : "关闭") +
          '设备名称为"' +
          JSON.stringify(arr) +
          '"的设备wifi？'
      )
      .then(function () {
        for (let i = 0; i < ips.value.length; i++) {
          handleWifi({ ipAddress: ips.value[i] }, type);
        }
      })
      .catch(() => {});
  } else {
    proxy.$modal
      .confirm(
        "是否确认" +
          (!!type ? "开启" : "关闭") +
          '设备名称为"' +
          row.deviceName +
          '"的设备wifi？'
      )
      .then(function () {
        handleWifi(row, type);
      })
      .catch(() => {});
  }
};

const handleClear = async (row) => {
  let { ip1, ip2, url1, url2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let config = {
    headers: {
      "Content-Type": "application/json;charset=utf-8",
      Authorization: "Bearer " + getToken(),
      "Ralay-Url": url1,
    },
    url: (isRalay.value ? ip2 : ip1) + `/api/Cockpit/ClearMemory`,
    // url: 'https://*************:5065/api/Cockpit/ClearMemory',
    method: "get",
  };
  await handleAxios(config);
};

const handleWifi = async (row, type) => {
  let { ip1, ip2, url1, url2 } = getUrlIp(row.ipAddress, row.ralayHost);
  let config = {
    headers: {
      "Content-Type": "application/json;charset=utf-8",
      Authorization: "Bearer " + getToken(),
      "Ralay-Url": url1,
    },
    url:
      (isRalay.value ? ip2 : ip1) +
      `/api/Cockpit/${!!type ? "EnabledNetsh" : "DisabledNetsh"}`,
    method: "get",
    isSuccess: !!type === false,
  };
  await handleAxios(config);
};

const handleRemote = async (row, type) => {
  console.log(row);

  let { ip1, ip2, url1, url2 } = getUrlIp(row.ipAddress, row.ralayHost);
  console.log({ ip1, ip2, url1, url2 });
  let data = { cmd: type === 3 ? 0 : type };
  if (type == 3) {
    data.time = row.time;
  } else {
    delete data.time;
  }
  // time: type === 3 ? row.time : null
  let config = {
    headers: {
      "Content-Type": "application/json;charset=utf-8",
      Authorization: "Bearer " + getToken(),
      "Ralay-Url": url1,
    },
    url: (isRalay.value ? ip2 : ip1) + "/api/Cockpit/Control",
    method: "post",
    data,
  };
  await handleAxios(config);
  if (!isBatch.value) {
    cancel();
  }
};

async function handleAxios(config) {
  loadingPage.value = ElLoading.service({
    lock: true,
    text: "Loading",
    background: "rgba(0,0,0,0.7)",
  });
  await axios({ ...config, timeout: config.isSuccess ? 5000 : 10000 })
    .then((res) => {
      setTimeout(() => {
        handleQuery();
        loadingPage.value.close();
        proxy.$modal.msgSuccess("操作成功");
      }, 500);
    })
    .catch((e) => {
      loadingPage.value.close();
      if (config.isSuccess) {
        proxy.$modal.msgSuccess("操作成功");
      } else {
        proxy.$modal.msgError("操作失败");
      }
    });
}

/** 设置图片url */
function setImgUrl(url) {
  url.split(",").length > 0
    ? (data.repairForm.images = url.split(","))
    : data.repairForm.images.push(url);
}

const handleAttachmentSuccess = (response, file, fileList) => {
  if (response.code === 200) {
    repairForm.value.attachments.push({
      name: file.name,
      url: response.data,
    });
    proxy.$modal.msgSuccess("上传成功");
  } else {
    proxy.$modal.msgError("上传失败");
  }
};

const handleAttachmentRemove = (file, fileList) => {
  const index = repairForm.value.attachments.findIndex(
    (item) => item.name === file.name
  );
  if (index > -1) {
    repairForm.value.attachments.splice(index, 1);
  }
};

const beforeAttachmentUpload = (file) => {
  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    proxy.$modal.msgError("上传文件大小不能超过 10MB!");
    return false;
  }
  return true;
};

onBeforeUnmount(() => {
  window.removeEventListener("beforeunload", (e) => closeSock());
});

onMounted(async () => {
  window.addEventListener("beforeunload", (e) => closeSock());
  await getOptions();
  await getPositionTreeList();
  await getTagList();
  await getTenant();
  getList();
});
</script>

<style lang="scss">
.monitor-statics {
  border: 1px solid #d4d4d4;
  display: flex;
  align-items: center;
  padding: 20px;
  margin-bottom: 20px;
  &_item {
    padding: 40px;
    flex: 1;
    display: flex;
    justify-content: center;
    div {
      margin-right: 20px;
    }
    &:last-child {
      border-left: 1px solid #d4d4d4;
      cursor: pointer;
      div {
        color: #c73528;
      }
    }
  }
}
</style>
