<template>
  <div class="pieData">
    <div class="pieData-chart">
      <img
        class="pieData-bg"
        src="https://xyywpt-oss.oss-cn-guangzhou.aliyuncs.com/system/cabin/0.0.4/3Dpie1-bg.png"
        :style="{ left: bgLeft }"
      />
      <Echarts3D
        :ref="id + 'Ref'"
        :id="id"
        width="100%"
        height="7vw"
        :useTip="true"
        :fullOptions="option"
        :data3D="data"
      />
    </div>
    <div class="pieData-list" :style="{ maxHeight: props.maxListHeight }">
      <div
        class="pieData-item"
        :class="[`${id}-item`]"
        v-for="(item, index) in data"
        :data-index="index"
      >
        <div class="pieData-name">
          <div
            :style="{
              backgroundColor: item.itemStyle.color,
              border: `1px solid ${item.itemStyle.color}`,
            }"
            class="item-dot"
          >
            <i
              :style="{
                backgroundColor: item.itemStyle.color,
              }"
            ></i>
          </div>
          <div class="name" :title="item.name">{{ item.name }}</div>
        </div>
        <div class="pieData-count">
          {{ item.value > 999 ? "999+" : item.value }}
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import Echarts3D from "@/components/Echarts/index3D-2.vue";
import { toRefs, getCurrentInstance, nextTick } from "vue";

const props = defineProps({
  bgLeft: {
    type: String,
    default: "0vw",
  },
  maxListHeight: {
    type: String,
    default: "5.5vw",
  },
  id: {
    type: String,
    default: "pieData",
  },
  data: {
    type: Array,
    default: [],
  },
  option: {
    type: Object,
    default: () => ({
      options: {
        legend: {
          type: "scroll",
          show: false,
        },
        animation: true,
        xAxis3D: {
          min: -1,
          max: 1,
        },
        yAxis3D: {
          min: -1,
          max: 1,
        },
        zAxis3D: {
          min: -1,
          max: 1,
        },
        grid3D: {
          show: false,
          boxHeight: 1,
          top: "-5%",
          viewControl: {
            roam: false,
            zoomSpeed: 0,
            distance: 130,
            alpha: 30,
            beta: 0,
            mouseRotate: false,
            mouseZoom: false,
            rotateSensitivity: 0,
            zoomSensitivity: 0,
            autoRotate: false, // 自动旋转
          },
        },
        series: [],
      },
    }),
    required: true,
  },
});

const state = reactive({
  pieData1Ref: null,
  pieData2Ref: null,
  pieData3Ref: null,
  pieData4Ref: null,
  pieData5Ref: null,
  pieData6Ref: null,
  pieData7Ref: null,
  pieData8Ref: null,
  pieData9Ref: null,
  pieData10Ref: null,
  pieData11Ref: null,
  pieData12Ref: null,
  pieData13Ref: null,
  pieData14Ref: null,
  pieData15Ref: null,
});
const {
  pieData1Ref,
  pieData2Ref,
  pieData3Ref,
  pieData4Ref,
  pieData5Ref,
  pieData6Ref,
  pieData7Ref,
  pieData8Ref,
  pieData9Ref,
  pieData10Ref,
  pieData11Ref,
  pieData12Ref,
  pieData13Ref,
  pieData14Ref,
  pieData15Ref,
} = toRefs(state);

const instance = getCurrentInstance();

const initPieFunc = () => {
  let timer = null;
  function waitForRef() {
    clearTimeout(timer);
    const echarts3DRef = instance?.proxy?.$refs[props.id + "Ref"];
    if (echarts3DRef) {
      document.querySelectorAll(`.${props.id}-item`).forEach((div) => {
        div.addEventListener("mouseover", function (e) {
          const index = parseInt(this.dataset.index); // 获取对应的数据索引
          echarts3DRef.dispatchAction({
            type: "highlight", // 触发高亮动作
            seriesIndex: index, // 系列索引（第一个饼图）
            dataIndex: index, // 数据项索引
          });
        });
        div.addEventListener("mouseout", function () {
          const index = parseInt(this.dataset.index);
          echarts3DRef.dispatchAction({
            type: "downplay", // 取消高亮
            seriesIndex: index,
            dataIndex: index,
          });
        });
      });
    } else {
      timer = setTimeout(waitForRef, 100);
    }
  }
  nextTick(waitForRef);
};

onBeforeUnmount(() => {
  if (state[`${props.id}Ref`]) {
    state[`${props.id}Ref`].unMountFunc();
  }
});

defineExpose({
  initPieFunc,
});
</script>

<style lang="scss" scoped>
.pieData {
  &-chart {
    position: relative;
  }
  &-bg {
    position: absolute;
    top: 0.1vw;
    left: 0;
    width: 11.5vw;
    height: 7vw;
  }
  &-list {
    // border: 1px solid red;
    color: #fff;
    width: 100%;
    max-height: 5.5vw;
    overflow-y: scroll;
    margin-top: 0.5vw;
    font-size: 0.55vw;
    display: flex;
    // align-items: flex-start;
    flex-wrap: wrap;
    gap: 0.5vw 0.8vw;
    padding: 0 0.3vw;
    z-index: 1;
    &::-webkit-scrollbar {
      width: 0;
      display: none;
    }
    scrollbar-width: none;
    -ms-overflow-style: none;
  }
  &-item {
    width: 46%;
    height: 1vw;
    // border: 1px solid red;
    cursor: pointer;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 0 0.5vw;
  }
  &-name {
    display: flex;
    align-items: center;
    gap: 0 0.2vw;
    .item-dot {
      width: 0.3vw;
      height: 0.3vw;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .name {
      color: #ffffffa3;
      width: 3vw;
      /* 旧版弹性盒 */
      display: -webkit-box;
      /* 弹性盒子元素垂直排列 */
      -webkit-box-orient: vertical;
      /* 控制要显示的行数 */
      -webkit-line-clamp: 1;
      overflow: hidden;
      // border: 1px solid red;
    }
  }
  &-count {
    // margin-left: 1vw;
    color: #fff;
  }
}
</style>